<template>
  <view class="container bg-cls">
    <!-- 自定义导航栏 -->
    <view class="custom-navbar">
      <!-- 回退按钮 -->
      <view class="back-button iconfont icon icon-return" @tap="goBack">
      </view>

      <text class="navbar-title">零感数字钥匙</text>
    </view>

    <!-- 页面内容 -->
    <view class="content">
      <view class="padding">
        <view class="flex ">
          <view class="flex-sub text-center">
            <view class="text-xl padding">
              <text class="title-cls text-black text-bold">校准蓝牙</text>
            </view>
            <view v-if="!currentPositionBeingCalibrated.calibrationStatus" class="calibration-tip-cls">{{ '请将手机稳定在' + currentPositionBeingCalibrated.calibrationPrompt + '，点击开始。' }}</view>
            <view v-else-if="currentPositionBeingCalibratedIndex >= calibrationPositionInfos.length - 1" class="calibration-tip-cls">已完成校准流程，请点击完成按钮，回到初始页面</view>
            <view v-else-if="currentPositionBeingCalibrated.calibrationStatus" class="calibration-tip-cls">{{ currentPositionBeingCalibrated.locationName + '校准成功，请点击下一步，开始校准下一个位置' }}</view>
          </view>
          
        </view>
        <view class="flex-sub text-center">
          <image v-if="'位置1' == currentPositionBeingCalibrated.locationName" class="calibration_img" src="~@/static/icons/Illustrations1.png" />
          <image v-else-if="'位置2' == currentPositionBeingCalibrated.locationName" class="calibration_img2" mode="widthFix" src="~@/static/icons/Illustrations2.png" />
        </view>

        <view class="flex-sub text-center" v-if="isCalibrationInProgress || currentPositionBeingCalibrated.calibrationStatus">
          <view class="text-xl padding">
            <text class="position-tip-cls">{{ currentPositionBeingCalibrated.locationName }}
              <text>{{ currentPositionBeingCalibrated.calibrationStatus ? "校准完成：" : "校准中：" }}</text>
              <text class="position-percentage-tip-cls">{{ calibrationProgress }}</text>
            </text>
          </view>
        </view>

        <view class="padding" v-if="isCalibrationInProgress || currentPositionBeingCalibrated.calibrationStatus">
          <view class="cu-progress round striped active">
            <view :class="currentPositionBeingCalibrated.calibrationStatus ? 'bg-green' : 'bg-blue'" :style="[{ width: calibrationProgress}]"></view>
          </view>
        </view>

        <view class="flex" v-if="isCalibrationInProgress">
          <view class="friendly-reminder-cls">数据采集过程中请保持手机稳定</view>
        </view>
      </view>

      <view class="calibration-btn-cls" v-if="!isCalibrationInProgress">
        <view v-if="!currentPositionBeingCalibrated.calibrationStatus" class="margin-tb-sm text-center">
          <button 
            @tap="sendToBle('0209000101', true)" 
            class="cu-btn block lg round"
          >
            开始
          </button>
        </view>
        <view v-else-if="currentPositionBeingCalibratedIndex >= calibrationPositionInfos.length - 1" class="margin-tb-sm text-center">
          <button
            class="cu-btn round bg-olive"
            @tap="goToDigitalKeyPage"
          >
            完成
          </button>
        </view>
        <view v-else-if="currentPositionBeingCalibrated.calibrationStatus" class="margin-tb-sm text-center">
          <button
            class="cu-btn round bg-olive"
            @tap="startTheNextStep"
          >
            下一步
          </button>
        </view>
      </view>
    </view>
  </view>
</template>

<script>
  import Base64 from "base-64";
  import { hexToString, ab2hex } from '@/utils/common'

  export default {
    data() {
      return {
        // 需要校准的位置相关信息
        calibrationPositionInfos: [
          {
            locationName: '位置1',
            calibrationPrompt: '主驾驶门锁处',
            calibrationStatus: false
          },
          {
            locationName: '位置2',
            calibrationPrompt: '主驾驶一侧，3米外位置',
            calibrationStatus: false
          }
        ],
        // 当前正在校准的位置
        currentPositionBeingCalibrated: null,
        // 当前正在校准的位置索引
        currentPositionBeingCalibratedIndex: 0,
        // 是否正在校准中
        isCalibrationInProgress: false,
        // 是否已完成需要返回首页
        isGoToDigitalKeyPage: false,
        // 校准进度
        calibrationProgress: '0%',
        // 校准流程定时器ID
        timerId: null,
        // 校准流程持续时间（单位：秒）
        calibrationDuration: 7,
      };
    },
    onLoad: function() {
      this.currentPositionBeingCalibrated = this.calibrationPositionInfos[0];
      this.openWatchNotify(uni.getStorageSync("mainDeviceId"), uni.getStorageSync("mainServiceId"), uni.getStorageSync("mainCharacteristicId"));
    },

    onShow() {
      console.log('切换到标定页面 :>> ');
      // 开启监听 notify 功能
      this.openWatchNotify(uni.getStorageSync("mainDeviceId"), uni.getStorageSync("mainServiceId"), uni.getStorageSync("mainCharacteristicId"));
    },

    methods: {
      /**
       * @description: 返回上一页
       * @return {*}
       */      
       goBack() {
        wx.navigateBack({
          delta: 1 // 表示回退一层，如果需要回退多层，可以修改这个值
        });
      },

      // 发送信息给蓝牙设备
      async sendToBle(bleSendInfo, isCalibrationInProgress) {

        // this.isCalibrationInProgress = isCalibrationInProgress;
        // let calibrationElapsedTime = 0;
        //         // 开启校准流程定时器
        //         this.timerId = setInterval(() => {
        //           calibrationElapsedTime++;
        //           this.calibrationProgress = `${Math.floor(100 / this.calibrationDuration * calibrationElapsedTime)}%`;
        //           if (calibrationElapsedTime >= this.calibrationDuration) {
        //             // this.sendToBle("0209 0001 03")
        //             if (this.timerId) {
        //               // 清除定时器
        //               clearInterval(this.timerId);
        //               this.timerId = null;
        //               this.isCalibrationInProgress = false
        //               this.currentPositionBeingCalibrated.calibrationStatus = true;
        //             }
        //           }
        //         }, 1000);
        //         return



        this.isCalibrationInProgress = isCalibrationInProgress;

        let that = this;

        uni.writeBLECharacteristicValue({
          // 这里的 deviceId 需要在 getBluetoothDevices 或 onBluetoothDeviceFound 接口中获取
          deviceId: uni.getStorageSync("mainDeviceId"),
          // 这里的 serviceId 需要在 getBLEDeviceServices 接口中获取
          serviceId: uni.getStorageSync("mainServiceId"),
          // 这里的 characteristicId 需要在 getBLEDeviceCharacteristics 接口中获取
          characteristicId: uni.getStorageSync("mainCharacteristicId"),
          value: uni.base64ToArrayBuffer(Base64.encode(bleSendInfo)),
          success(res) {
            uni.hideLoading();
            setTimeout(() => {
              if (that.isCalibrationInProgress) {
                // 校准已用时间
                let calibrationElapsedTime = 0;
                // 开启校准流程定时器
                that.timerId = setInterval(() => {
                  calibrationElapsedTime++;
                  that.calibrationProgress = `${Math.floor(100 / that.calibrationDuration * calibrationElapsedTime)}%`;
                  if (calibrationElapsedTime >= that.calibrationDuration) {
                    that.sendToBle("0209000103")
                    // 清除定时器
                    clearInterval(that.timerId);
                    that.timerId = null;
                    that.isCalibrationInProgress = false
                    that.currentPositionBeingCalibrated.calibrationStatus = true;
                  }
                }, 1000);
              }
            }, 500);
          },
          fail(err) {
            console.log("发送数据失败 err :>> ", err);
            uni.hideLoading();
            setTimeout(() => {
              that.isCalibrationInProgress = false;
              if (that.timerId) {
                // 清除定时器
                clearInterval(that.timerId);
                that.timerId = null;
                that.isCalibrationInProgress = false
              }

              uni.showToast({
                title: "开启流程失败" + JSON.stringify(err),
                duration: 2000,
                icon: "none"
              });
            }, 500);
          },
        });
      },

      /**
       * @description: 开启监听 蓝牙notify 功能
       * @param {*} deviceId
       * @param {*} serviceId
       * @param {*} characteristicId
       * @return {*}
       *
       */
      openWatchNotify(deviceId, serviceId, characteristicId) {
        let that = this;
        uni.notifyBLECharacteristicValueChange({
          state: true, // 启用 notify 功能
          // 这里的 deviceId 需要已经通过 createBLEConnection 与对应设备建立链接
          deviceId,
          // 这里的 serviceId 需要在 getBLEDeviceServices 接口中获取
          serviceId,
          // 这里的 characteristicId 需要在 getBLEDeviceCharacteristics 接口中获取
          characteristicId,
          success(res) {
            console.log("notifyBLECharacteristicValueChange success", res.errMsg);
            uni.onBLECharacteristicValueChange(function (res) {
              console.log(res);
              // 监听帧头帧尾
              var resCode1 = ab2hex(res.value);
              // 收到蓝牙返回的命令（16进制）
              var resCode = resCode1?.toUpperCase();

              console.error("监听蓝牙回复信息内容：" + hexToString(resCode));

              // // TODO 偏移量位置待确认
              // let iv = "";
              // // 使用AES加密算法解密数据。
              // let decryptResCode = this.decryptData(resCode, this.secretKey, iv);

              // console.log("解密后的蓝牙回复信息 :>> ", decryptResCode);

              // 转成UTF-8返回
              let bluetoothNotifyInfo = hexToString(resCode);

              if (that.isCalibrationInProgress) {
                if ("0209000000" == bluetoothNotifyInfo) {
                  uni.showToast({ 
                    title: "已开启校准流程",
                    duration: 2000
                  })
                } else if ("0209010000" == bluetoothNotifyInfo) {
                  uni.showToast({
                    title: "流程异常，请再次尝试",
                    duration: 2000,
                    icon: "none"
                  })
                } else if ("0209000102" == bluetoothNotifyInfo) {
                  uni.showToast({
                    title: "数据异常，请再次尝试",
                    duration: 2000,
                    icon: "none"
                  })
                } else if ("0209000000" == bluetoothNotifyInfo) {
                  uni.showToast({
                    title: "校准成功",
                    duration: 2000
                  })
                  that.currentPositionBeingCalibrated.calibrationStatus = true;
                } else if ("0209010000" == bluetoothNotifyInfo) {
                  uni.showToast({
                    title: "校准失败，请再次尝试",
                    duration: 2000,
                    icon: "none"
                  })
                }
  
                if (!"0209000000" == bluetoothNotifyInfo) {
                  that.isCalibrationInProgress = false;
                  
                  if (that.timerId) {
                    // 清除定时器
                    clearInterval(that.timerId);
                    that.timerId = null;
                  }
                }
              }

              if (that.isGoToDigitalKeyPage) {
                if (bluetoothNotifyInfo?.trim() == "0209000000") {
                  console.log('goToDigitalKeyPage110 :>> ');
                  uni.switchTab({
                    url: '../digitalKey/index'
                  });
                } else if ("0209010000" == bluetoothNotifyInfo?.trim()) {
                  uni.showToast({
                    title: "校准数据应用失败，请再次尝试",
                    duration: 2000,
                    icon: "none"
                  });

                  uni.switchTab({
                    url: '../digitalKey/index'
                  });
                }
              }

            });
          },
          fail(err) {
            console.error("监听蓝牙回复信息失败", err);
            uni.showToast({
              title: "监听蓝牙回复信息失败，" + JSON.stringify(err),
              duration: 2000,
              icon: "none"
            })
            that.isCalibrationInProgress = false;
            if (that.timerId) {
              // 清除定时器
              clearInterval(that.timerId);
              that.timerId = null;
            }

            if (that.isGoToDigitalKeyPage) {
              uni.switchTab({
                url: '../digitalKey/index'
              });
            }
          },
        });
      },

      /**
       * @description: 开始下一个位置的校准流程
       * @return {*}
       */      
      startTheNextStep() {
        this.calibrationProgress = '0%';
        if (this.currentPositionBeingCalibratedIndex >= this.calibrationPositionInfos?.length - 1) {
          this.currentPositionBeingCalibrated = null;
          return;
        }

        this.currentPositionBeingCalibrated = this.calibrationPositionInfos[++this.currentPositionBeingCalibratedIndex]
      },

      /**
       * @description: 标定完成跳转到初始页面
       * @return {*}
       */    
       goToDigitalKeyPage() {
        this.sendToBle("0209000109");
        this.isGoToDigitalKeyPage = true;
      },

      /**
       * @description: 标定完成跳转到初始页面
       * @return {*}
       */    
       goToDigitalKeyPage110() {
        uni.switchTab({
          url: '../digitalKey/index'
        });
      },
    }
  }
</script>

<style lang="scss">
page {
    /* 设置背景固定，不随页面滚动 */
    background-attachment: fixed;

    /* 其他样式，比如设置高度 */
    height: 70vh;
    /* 设置高度为视口高度 */
  }

  .custom-navbar {
    display: flex;
    justify-content: center;
    align-items: center;
    height: 184rpx; /* 导航栏高度 */
    border-radius: 0rpx 0rpx 0rpx 0rpx;
    background-color: rgba(0, 0, 0, 0); /* 自定义背景色 */
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    z-index: 999;
  }

  .navbar-title {
    margin-top: 75rpx;
    color: #121010; /* 标题颜色 */
  }

  .content {
    padding-top: 205rpx;
    width: 100%;
    height: 100%;
  }
  .margin-tb-sm {
  }

  .back-button {
    position: absolute;
    top: 118rpx;
    left: 28rpx; 
  }

  .title-cls {
    color: #060708;
    font-size: 40rpx;
    font-weight: 550;
    line-height: 50rpx;
  }

  .calibration-tip-cls {
    margin-top: 8rpx;
    color: #666666;
    font-size: 28rpx;
    font-weight: 400;
    line-height: 39rpx;
  }

  .calibration_img {
    margin-top: 255rpx;
    width: 750rpx; /* 图标宽度 */
    height: 200rpx; /* 图标高度 */
  }

  .calibration_img2 {
    margin-top: 255rpx;
    width: 100%;
  }

  .calibration-btn-cls {
    margin-top: 120rpx;

    .cu-btn {
      background: #4D7EFC;
      color: #FFFFFF;
      width: 679rpx;
      height: 83rpx;
      margin: 0 auto;
    }
  }

  .position-tip-cls {
    color: #333333;
    font-size: 32rpx;
    font-weight: 400;
    line-height: 38rpx;

    .position-percentage-tip-cls {
      color: #333333;
      font-size: 42rpx;
      font-weight: 500;
      line-height: 49rpx;
    }
  }

  .friendly-reminder-cls {
    color: #999999;
    font-size: 28rpx;
    font-weight: 400;
    line-height: 39rpx;
    margin: 25rpx auto;
  }

</style>

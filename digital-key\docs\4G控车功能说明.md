# 4G控车功能说明

## 概述

4G控车功能是数字钥匙系统的重要组成部分，当蓝牙连接不可用时，用户可以通过4G网络与云平台通信，实现远程车辆控制。

## 功能特性

### 1. 自动切换机制
- **蓝牙优先**: 当蓝牙连接可用时，优先使用蓝牙控制
- **4G备用**: 当蓝牙连接不可用时，自动切换到4G控车
- **状态显示**: 实时显示连接状态和车辆状态

### 2. 支持的控车功能
- ✅ **门锁控制**: 解锁/闭锁车门
- ✅ **状态查询**: 查询车辆当前状态
- ⏳ **启动功能**: 暂不支持（后续版本）
- ⏳ **后备箱**: 暂不支持（后续版本）
- ⏳ **寻车功能**: 暂不支持（后续版本）

### 3. 实时通信
- **双向通信**: 手机APP ↔ 云平台 ↔ TBOX设备
- **状态同步**: TBOX设备状态实时同步到手机APP
- **消息推送**: 控车结果实时反馈

## 技术架构

```
手机APP(UniApp) 
    ↓ WebSocket
云平台(SpringBoot)
    ↓ TCP
TBOX设备(模拟器)
```

### 通信协议
- **WebSocket**: 手机APP与云平台通信
- **TCP**: 云平台与TBOX设备通信
- **消息格式**: JSON格式，包含messageType字段区分消息类型

## 使用方法

### 1. 基本使用流程

1. **打开数字钥匙页面**
   - 进入主页面 `pages/work/digitalKey/index.vue`

2. **检查连接状态**
   - 蓝牙连接状态: 显示在页面顶部
   - 4G控车状态: 显示在蓝牙未连接时

3. **执行控车操作**
   - 点击"门锁"按钮进行解锁/闭锁
   - 系统自动选择最佳连接方式

### 2. 测试页面使用

访问测试页面 `pages/test/4GControlTest.vue` 进行功能测试：

1. **连接测试**
   - 点击"连接4G控车"按钮
   - 观察连接状态变化

2. **控车测试**
   - 点击"解锁车门"/"闭锁车门"按钮
   - 查看消息日志中的反馈信息

3. **状态查询**
   - 点击"查询状态"按钮
   - 查看车辆当前状态

## 配置说明

### 1. 服务器配置

编辑 `config/4GControlConfig.js` 文件：

```javascript
// 开发环境
const development = {
  websocketUrl: 'ws://localhost:9201',
  enabled: true,
  reconnect: {
    maxAttempts: 5,
    interval: 5000
  }
}

// 生产环境
const production = {
  websocketUrl: 'ws://your-production-server:9201',
  enabled: true,
  reconnect: {
    maxAttempts: 3,
    interval: 10000
  }
}
```

### 2. 功能开关

在主页面中可以控制4G控车功能的启用状态：

```javascript
data() {
  return {
    fourGControlEnabled: true, // 是否启用4G控车功能
    // ...
  }
}
```

## 文件结构

```
digital-key/
├── pages/work/digitalKey/index.vue          # 主数字钥匙页面
├── pages/test/4GControlTest.vue             # 4G控车测试页面
├── utils/websocket/4GControlWebSocket.js    # 4G控车WebSocket工具类
├── config/4GControlConfig.js                # 4G控车配置文件
└── docs/4G控车功能说明.md                   # 本文档
```

## 核心代码说明

### 1. WebSocket工具类

`utils/websocket/4GControlWebSocket.js` 提供了完整的WebSocket通信功能：

- **连接管理**: 自动连接、重连、断开
- **消息处理**: 发送控车指令、接收状态更新
- **回调机制**: 连接状态回调、车辆状态回调
- **错误处理**: 连接失败、消息发送失败处理

### 2. 主页面集成

`pages/work/digitalKey/index.vue` 中的关键方法：

- `init4GControl()`: 初始化4G控车功能
- `handleLockCommand()`: 处理门锁按钮点击
- `send4GUnlockCommand()`: 发送4G解锁指令
- `send4GLockCommand()`: 发送4G闭锁指令

### 3. 状态管理

```javascript
data() {
  return {
    fourGConnected: false,    // 4G控车连接状态
    vehicleStatus: null,      // 车辆状态信息
    lastControlCommand: null, // 最后一次控车指令
    // ...
  }
}
```

## 调试和故障排除

### 1. 常见问题

**问题**: 4G控车连接失败
**解决**: 
- 检查服务器地址配置
- 确认云平台服务正常运行
- 检查网络连接

**问题**: 控车指令无响应
**解决**:
- 检查TBOX模拟器是否正常运行
- 查看控制台日志信息
- 确认消息格式正确

### 2. 调试方法

1. **开启控制台日志**
   ```javascript
   console.log('🚗 4G控车调试信息:', data)
   ```

2. **使用测试页面**
   - 访问 `pages/test/4GControlTest.vue`
   - 查看详细的消息日志

3. **检查网络请求**
   - 使用浏览器开发者工具
   - 查看WebSocket连接状态

## 版本更新记录

### v1.0.0 (当前版本)
- ✅ 基础4G控车功能
- ✅ 门锁控制（解锁/闭锁）
- ✅ 状态查询和显示
- ✅ 自动切换机制
- ✅ 测试页面

### v1.1.0 (计划中)
- ⏳ 启动功能支持
- ⏳ 后备箱控制
- ⏳ 寻车功能
- ⏳ 更多车辆状态信息

## 联系支持

如有问题或建议，请联系开发团队。

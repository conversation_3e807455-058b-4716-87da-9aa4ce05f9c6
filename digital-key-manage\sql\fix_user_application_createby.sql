-- 修复用户申请记录的createBy字段
-- 执行时间：2025-01-19

-- 1. 查看当前申请记录的createBy字段状态
SELECT 
    application_id,
    application_no,
    real_name,
    phone,
    create_by,
    create_time
FROM dk_user_application 
ORDER BY create_time DESC;

-- 2. 查看用户表，了解用户名和手机号的对应关系
SELECT 
    user_id,
    user_name,
    nick_name,
    phonenumber,
    create_time
FROM sys_user 
WHERE del_flag = '0'
ORDER BY create_time DESC;

-- 3. 根据手机号匹配，更新申请记录的createBy字段
-- 注意：这个脚本假设手机号是唯一的，且用户表中的phonenumber字段与申请表中的phone字段匹配
UPDATE dk_user_application dua
INNER JOIN sys_user su ON dua.phone = su.phonenumber
SET dua.create_by = su.user_name
WHERE dua.create_by IS NULL OR dua.create_by = '';

-- 4. 验证修复结果
SELECT 
    dua.application_id,
    dua.application_no,
    dua.real_name,
    dua.phone,
    dua.create_by,
    su.user_name,
    su.nick_name
FROM dk_user_application dua
LEFT JOIN sys_user su ON dua.create_by = su.user_name
ORDER BY dua.create_time DESC;

-- 5. 检查是否还有未匹配的记录
SELECT 
    application_id,
    application_no,
    real_name,
    phone,
    create_by,
    create_time
FROM dk_user_application 
WHERE create_by IS NULL OR create_by = ''
ORDER BY create_time DESC;

-- 6. 如果有无法自动匹配的记录，可以手动设置
-- 示例：假设有一个申请记录无法匹配，可以手动设置
-- UPDATE dk_user_application 
-- SET create_by = '具体的用户名'
-- WHERE application_id = 具体的申请ID;

-- 7. 最终验证：确保所有申请记录都有正确的createBy值
SELECT 
    COUNT(*) as total_applications,
    COUNT(create_by) as applications_with_createby,
    COUNT(*) - COUNT(create_by) as applications_without_createby
FROM dk_user_application;

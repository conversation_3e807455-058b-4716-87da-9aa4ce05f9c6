<template>
  <view class="container bg-cls">
    <!-- 自定义导航栏 -->
    <view class="custom-navbar">
      <text class="navbar-title">修改密码</text>
    </view>

    <!-- 页面内容 -->
    <view class="pwd-retrieve-container">
      <uni-forms ref="form" :value="user" labelWidth="80px">

        <view class="input-item flex align-center">
          <view class="content-oldPassword icon"></view>
          <input v-model="user.oldPassword" class="input" type="text" placeholder="请输入旧密码" maxlength="30" />
        </view>

        <view class="input-item flex align-center">
          <view class="content-newPassword icon"></view>
          <input v-model="user.newPassword" class="input" type="text" placeholder="请输入新密码" maxlength="30" />
        </view>

        <view class="input-item flex align-center">
          <view class="content-confirmPassword icon"></view>
          <input v-model="user.confirmPassword" class="input" type="text" placeholder="请确认新密码" maxlength="30" />
        </view>

        <button type="primary" @click="submit">提交</button>
      </uni-forms>
    </view>
  </view>
</template>

<script>
  import { updateUserPwd } from "@/api/system/user"

  export default {
    data() {
      return {
        user: {
          oldPassword: undefined,
          newPassword: undefined,
          confirmPassword: undefined
        },
        rules: {
          oldPassword: {
            rules: [{
              required: true,
              errorMessage: '旧密码不能为空'
            }]
          },
          newPassword: {
            rules: [{
                required: true,
                errorMessage: '新密码不能为空',
              },
              {
                minLength: 6,
                maxLength: 20,
                errorMessage: '长度在 6 到 20 个字符'
              }
            ]
          },
          confirmPassword: {
            rules: [{
                required: true,
                errorMessage: '确认密码不能为空'
              }, {
                validateFunction: (rule, value, data) => data.newPassword === value,
                errorMessage: '两次输入的密码不一致'
              }
            ]
          }
        }
      }
    },
    onReady() {
      this.$refs.form.setRules(this.rules)
    },
    methods: {
      submit() {
        this.$refs.form.validate().then(res => {
          updateUserPwd(this.user.oldPassword, this.user.newPassword).then(response => {
            this.$modal.msgSuccess("修改成功")
          })
        })
      }
    }
  }
</script>

<style lang="scss">
  page {
    /* 设置背景固定，不随页面滚动 */
    background-attachment: fixed;

    /* 其他样式，比如设置高度 */
    height: 70vh;
    /* 设置高度为视口高度 */
  }

  .content-oldPassword::before {
    content: '旧密码';
    color: #606266;
    font-size: 14px;
    min-width: 60px;
    display: block;
  }

  .content-newPassword::before {
    content: '新密码';
    color: #606266;
    font-size: 14px;
    min-width: 60px;
    display: block;
  }

  .content-confirmPassword::before {
    content: '确认密码';
    color: #606266;
    font-size: 14px;
    min-width: 60px;
    display: block;
  }

  .custom-navbar {
    display: flex;
    justify-content: center;
    align-items: center;
    height: 184rpx; /* 导航栏高度 */
    border-radius: 0rpx 0rpx 0rpx 0rpx;
    background-color: rgba(0, 0, 0, 0); /* 自定义背景色 */
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    z-index: 999;
  }

  .navbar-title {
    margin-top: 75rpx;
    color: #121010; /* 标题颜色 */
  }

  .pwd-retrieve-container {
    padding-top: 184rpx;
    width: 100%;
    height: 100%;
  }
</style>

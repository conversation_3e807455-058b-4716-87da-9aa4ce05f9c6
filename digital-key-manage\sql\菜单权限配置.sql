-- =====================================================
-- 数字钥匙运营系统菜单权限配置脚本
-- 用于在系统中添加新的菜单项和权限配置
-- =====================================================

-- 设置安全模式
SET SQL_SAFE_UPDATES = 0;

-- 开始事务
START TRANSACTION;

-- =====================================================
-- 1. 添加数字钥匙运营系统主菜单
-- =====================================================

-- 删除可能存在的旧菜单（避免重复）
DELETE FROM sys_menu WHERE menu_name IN ('数字钥匙运营系统', '运营工作台', '用户申请管理', '车辆信息管理', '数字钥匙管理', '钥匙分配管理');

-- 添加数字钥匙运营系统主菜单
INSERT INTO sys_menu (menu_name, parent_id, order_num, path, component, query, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark) VALUES
('数字钥匙运营系统', 0, 4, 'dk', NULL, '', 1, 0, 'M', '0', '0', '', 'key', 'admin', NOW(), '', NULL, '数字钥匙运营管理系统');

-- 获取主菜单ID
SET @dk_main_menu_id = LAST_INSERT_ID();

-- =====================================================
-- 2. 添加子菜单
-- =====================================================

-- 2.1 运营工作台
INSERT INTO sys_menu (menu_name, parent_id, order_num, path, component, query, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark) VALUES
('运营工作台', @dk_main_menu_id, 1, 'dashboard', 'dk/dashboard/index', '', 1, 0, 'C', '0', '0', 'dk:dashboard:view', 'dashboard', 'admin', NOW(), '', NULL, '数字钥匙运营工作台');

-- 2.2 用户申请管理
INSERT INTO sys_menu (menu_name, parent_id, order_num, path, component, query, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark) VALUES
('用户申请管理', @dk_main_menu_id, 2, 'application', 'dk/userApplication/index', '', 1, 0, 'C', '0', '0', 'dk:application:list', 'peoples', 'admin', NOW(), '', NULL, '用户申请审核管理');

-- 2.3 车辆信息管理
INSERT INTO sys_menu (menu_name, parent_id, order_num, path, component, query, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark) VALUES
('车辆信息管理', @dk_main_menu_id, 3, 'vehicle', 'dk/vehicleInfo/index', '', 1, 0, 'C', '0', '0', 'dk:vehicle:list', 'guide', 'admin', NOW(), '', NULL, '车辆基础信息管理');

-- 2.4 数字钥匙管理（更新原有菜单）
INSERT INTO sys_menu (menu_name, parent_id, order_num, path, component, query, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark) VALUES
('数字钥匙管理', @dk_main_menu_id, 4, 'keys', 'dk/vehicleBluetoothKeys/index', '', 1, 0, 'C', '0', '0', 'dk:vehicleBluetoothKeys:list', 'lock', 'admin', NOW(), '', NULL, '数字钥匙权限管理');

-- 2.5 钥匙分配管理
INSERT INTO sys_menu (menu_name, parent_id, order_num, path, component, query, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark) VALUES
('钥匙分配管理', @dk_main_menu_id, 5, 'assignment', 'dk/keyAssignment/index', '', 1, 0, 'C', '0', '0', 'dk:assignment:list', 'skill', 'admin', NOW(), '', NULL, '数字钥匙分配管理');

-- 2.6 系统测试（开发环境用）
INSERT INTO sys_menu (menu_name, parent_id, order_num, path, component, query, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark) VALUES
('系统测试', @dk_main_menu_id, 6, 'test', 'dk/test/index', '', 1, 0, 'C', '0', '0', 'dk:test:view', 'bug', 'admin', NOW(), '', NULL, '系统功能测试页面');

-- =====================================================
-- 3. 添加详细权限按钮
-- =====================================================

-- 获取各个菜单的ID
SET @dashboard_menu_id = (SELECT menu_id FROM sys_menu WHERE menu_name = '运营工作台' AND parent_id = @dk_main_menu_id);
SET @application_menu_id = (SELECT menu_id FROM sys_menu WHERE menu_name = '用户申请管理' AND parent_id = @dk_main_menu_id);
SET @vehicle_menu_id = (SELECT menu_id FROM sys_menu WHERE menu_name = '车辆信息管理' AND parent_id = @dk_main_menu_id);
SET @keys_menu_id = (SELECT menu_id FROM sys_menu WHERE menu_name = '数字钥匙管理' AND parent_id = @dk_main_menu_id);
SET @assignment_menu_id = (SELECT menu_id FROM sys_menu WHERE menu_name = '钥匙分配管理' AND parent_id = @dk_main_menu_id);

-- 3.1 用户申请管理权限
INSERT INTO sys_menu (menu_name, parent_id, order_num, path, component, query, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark) VALUES
('申请查询', @application_menu_id, 1, '', '', '', 1, 0, 'F', '0', '0', 'dk:application:query', '#', 'admin', NOW(), '', NULL, ''),
('申请新增', @application_menu_id, 2, '', '', '', 1, 0, 'F', '0', '0', 'dk:application:add', '#', 'admin', NOW(), '', NULL, ''),
('申请修改', @application_menu_id, 3, '', '', '', 1, 0, 'F', '0', '0', 'dk:application:edit', '#', 'admin', NOW(), '', NULL, ''),
('申请删除', @application_menu_id, 4, '', '', '', 1, 0, 'F', '0', '0', 'dk:application:remove', '#', 'admin', NOW(), '', NULL, ''),
('申请审核', @application_menu_id, 5, '', '', '', 1, 0, 'F', '0', '0', 'dk:application:audit', '#', 'admin', NOW(), '', NULL, ''),
('申请导出', @application_menu_id, 6, '', '', '', 1, 0, 'F', '0', '0', 'dk:application:export', '#', 'admin', NOW(), '', NULL, '');

-- 3.2 车辆信息管理权限
INSERT INTO sys_menu (menu_name, parent_id, order_num, path, component, query, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark) VALUES
('车辆查询', @vehicle_menu_id, 1, '', '', '', 1, 0, 'F', '0', '0', 'dk:vehicle:query', '#', 'admin', NOW(), '', NULL, ''),
('车辆新增', @vehicle_menu_id, 2, '', '', '', 1, 0, 'F', '0', '0', 'dk:vehicle:add', '#', 'admin', NOW(), '', NULL, ''),
('车辆修改', @vehicle_menu_id, 3, '', '', '', 1, 0, 'F', '0', '0', 'dk:vehicle:edit', '#', 'admin', NOW(), '', NULL, ''),
('车辆删除', @vehicle_menu_id, 4, '', '', '', 1, 0, 'F', '0', '0', 'dk:vehicle:remove', '#', 'admin', NOW(), '', NULL, ''),
('车辆导出', @vehicle_menu_id, 5, '', '', '', 1, 0, 'F', '0', '0', 'dk:vehicle:export', '#', 'admin', NOW(), '', NULL, '');

-- 3.3 数字钥匙管理权限
INSERT INTO sys_menu (menu_name, parent_id, order_num, path, component, query, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark) VALUES
('钥匙查询', @keys_menu_id, 1, '', '', '', 1, 0, 'F', '0', '0', 'dk:vehicleBluetoothKeys:query', '#', 'admin', NOW(), '', NULL, ''),
('钥匙新增', @keys_menu_id, 2, '', '', '', 1, 0, 'F', '0', '0', 'dk:vehicleBluetoothKeys:add', '#', 'admin', NOW(), '', NULL, ''),
('钥匙修改', @keys_menu_id, 3, '', '', '', 1, 0, 'F', '0', '0', 'dk:vehicleBluetoothKeys:edit', '#', 'admin', NOW(), '', NULL, ''),
('钥匙删除', @keys_menu_id, 4, '', '', '', 1, 0, 'F', '0', '0', 'dk:vehicleBluetoothKeys:remove', '#', 'admin', NOW(), '', NULL, ''),
('权限限制', @keys_menu_id, 5, '', '', '', 1, 0, 'F', '0', '0', 'dk:vehicleBluetoothKeys:restrict', '#', 'admin', NOW(), '', NULL, ''),
('权限恢复', @keys_menu_id, 6, '', '', '', 1, 0, 'F', '0', '0', 'dk:vehicleBluetoothKeys:restore', '#', 'admin', NOW(), '', NULL, ''),
('钥匙导出', @keys_menu_id, 7, '', '', '', 1, 0, 'F', '0', '0', 'dk:vehicleBluetoothKeys:export', '#', 'admin', NOW(), '', NULL, '');

-- 3.4 钥匙分配管理权限
INSERT INTO sys_menu (menu_name, parent_id, order_num, path, component, query, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark) VALUES
('分配查询', @assignment_menu_id, 1, '', '', '', 1, 0, 'F', '0', '0', 'dk:assignment:query', '#', 'admin', NOW(), '', NULL, ''),
('钥匙分配', @assignment_menu_id, 2, '', '', '', 1, 0, 'F', '0', '0', 'dk:assignment:assign', '#', 'admin', NOW(), '', NULL, ''),
('钥匙回收', @assignment_menu_id, 3, '', '', '', 1, 0, 'F', '0', '0', 'dk:assignment:revoke', '#', 'admin', NOW(), '', NULL, ''),
('状态更新', @assignment_menu_id, 4, '', '', '', 1, 0, 'F', '0', '0', 'dk:assignment:status', '#', 'admin', NOW(), '', NULL, ''),
('分配导出', @assignment_menu_id, 5, '', '', '', 1, 0, 'F', '0', '0', 'dk:assignment:export', '#', 'admin', NOW(), '', NULL, '');

-- =====================================================
-- 4. 为admin角色分配权限
-- =====================================================

-- 获取admin角色ID
SET @admin_role_id = (SELECT role_id FROM sys_role WHERE role_key = 'admin');

-- 获取所有新增菜单的ID
INSERT INTO sys_role_menu (role_id, menu_id)
SELECT @admin_role_id, menu_id FROM sys_menu 
WHERE menu_name IN (
    '数字钥匙运营系统', '运营工作台', '用户申请管理', '车辆信息管理', '数字钥匙管理', '钥匙分配管理', '系统测试',
    '申请查询', '申请新增', '申请修改', '申请删除', '申请审核', '申请导出',
    '车辆查询', '车辆新增', '车辆修改', '车辆删除', '车辆导出',
    '钥匙查询', '钥匙新增', '钥匙修改', '钥匙删除', '权限限制', '权限恢复', '钥匙导出',
    '分配查询', '钥匙分配', '钥匙回收', '状态更新', '分配导出'
);

-- =====================================================
-- 5. 显示配置结果
-- =====================================================

SELECT '菜单权限配置完成！' as message;

-- 显示新增的菜单结构
SELECT 
    m1.menu_name as '主菜单',
    m2.menu_name as '子菜单',
    m2.path as '路径',
    m2.component as '组件',
    m2.perms as '权限标识'
FROM sys_menu m1
LEFT JOIN sys_menu m2 ON m1.menu_id = m2.parent_id
WHERE m1.menu_name = '数字钥匙运营系统'
ORDER BY m2.order_num;

-- 提交事务
COMMIT;

-- 恢复安全模式
SET SQL_SAFE_UPDATES = 1;

SELECT '
🎉 数字钥匙运营系统菜单配置完成！

📋 新增菜单：
✅ 数字钥匙运营系统（主菜单）
  ├── 运营工作台
  ├── 用户申请管理  
  ├── 车辆信息管理
  ├── 数字钥匙管理
  ├── 钥匙分配管理
  └── 系统测试

🔑 权限配置：
✅ 为admin角色分配了所有权限
✅ 包含查询、新增、修改、删除、导出等操作权限
✅ 包含审核、分配、限制、恢复等业务权限

🚀 使用说明：
1. 重新登录系统查看新菜单
2. 如需为其他角色分配权限，请在角色管理中配置
3. 系统测试菜单用于开发调试，生产环境可隐藏

菜单配置成功！请重新登录查看效果。
' as configuration_summary;

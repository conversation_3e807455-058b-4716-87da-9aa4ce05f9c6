{"name": "dk", "version": "1.2.0", "main": "main.js", "scripts": {"dev:mp-weixin": "cross-env NODE_ENV=development UNI_PLATFORM=mp-weixin vue-cli-service uni-build --watch", "build:mp-weixin": "cross-env NODE_ENV=production UNI_PLATFORM=mp-weixin vue-cli-service uni-build", "dev:h5": "cross-env NODE_ENV=development UNI_PLATFORM=h5 vue-cli-service uni-serve", "build:h5": "cross-env NODE_ENV=production UNI_PLATFORM=h5 vue-cli-service uni-build", "test": "echo \"Error: no test specified\" && exit 1"}, "keywords": [], "author": "", "dependencies": {"@dcloudio/uni-ui": "^1.4.28", "base-64": "^1.0.0", "crypto-js": "^4.2.0", "js-md5": "^0.7.3", "uqrcodejs": "^4.0.7"}, "devDependencies": {"@dcloudio/cli": "^2.0.0", "@dcloudio/vue-cli-plugin-uni": "^2.0.0", "@vue/cli-service": "^4.5.0", "cross-env": "^7.0.3", "vconsole": "^3.15.1"}}
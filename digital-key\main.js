import Vue from "vue";
import App from "./App";
import store from "./store"; // store
import plugins from "./plugins"; // plugins
import "./permission"; // permission
Vue.use(plugins);

import basics from "./pages/coUI/basics/home.vue";
Vue.component("basics", basics);

import components from "./pages/coUI/component/home.vue";
Vue.component("components", components);

import plugin from "./pages/coUI/plugin/home.vue";
Vue.component("plugin", plugin);

import cuCustom from "colorui/components/cu-custom.vue";
Vue.component("cu-custom", cuCustom);

Vue.config.productionTip = false;
Vue.prototype.$store = store;

App.mpType = "app";

import i18n from "./lang/index";
Vue.prototype._i18n = i18n;

const app = new Vue({
  i18n,
  ...App,
});

app.$mount();

<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>数字钥匙三端架构</title>
    <link href="https://fonts.googleapis.com/css2?family=Noto+Sans+SC:wght@400;500;700&display=swap" rel="stylesheet">
    <style>
        body {
            font-family: 'Noto Sans SC', sans-serif;
            margin: 0;
            padding: 0;
            background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
            color: #2c3e50;
            min-height: 100vh;
        }
        .slide {
            width: 90%;
            max-width: 1200px;
            margin: 30px auto;
            background: white;
            padding: 40px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.15);
            border-radius: 12px;
        }
        h1 {
            color: #2c3e50;
            text-align: center;
            font-size: 2.5em;
            margin-bottom: 40px;
            font-weight: 700;
            text-shadow: 1px 1px 2px rgba(0,0,0,0.1);
            position: relative;
        }
        h1:after {
            content: '';
            display: block;
            width: 100px;
            height: 4px;
            background: linear-gradient(90deg, #3498db, #2ecc71);
            margin: 15px auto 0;
            border-radius: 2px;
        }
        h2 {
            color: #3498db;
            padding-bottom: 10px;
            margin-top: 50px;
            font-weight: 600;
            position: relative;
            display: inline-block;
        }
        h2:after {
            content: '';
            display: block;
            width: 100%;
            height: 3px;
            background: linear-gradient(90deg, #3498db, transparent);
            margin-top: 8px;
            border-radius: 2px;
        }
        .architecture-container {
            display: flex;
            justify-content: center;
            margin: 50px 0;
        }
        .architecture-diagram {
            position: relative;
            width: 100%;
            height: 650px;
            background: linear-gradient(to bottom, #f8f9fa, #ffffff);
            border-radius: 15px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.05);
            padding: 20px;
        }
        .component {
            position: absolute;
            width: 320px;
            border-radius: 12px;
            padding: 20px;
            box-shadow: 0 8px 20px rgba(0,0,0,0.1);
            transition: all 0.3s ease;
        }
        .component:hover {
            transform: translateY(-5px);
            box-shadow: 0 12px 25px rgba(0,0,0,0.15);
        }
        .cloud-platform {
            top: 50px;
            left: 50%;
            transform: translateX(-50%);
            background: linear-gradient(135deg, #3498db, #2980b9);
            color: white;
            z-index: 3;
        }
        .cloud-platform:hover {
            transform: translateX(-50%) translateY(-5px);
        }
        .mobile-app {
            bottom: 100px;
            left: 120px;
            background: linear-gradient(135deg, #2ecc71, #27ae60);
            color: white;
            z-index: 2;
        }
        .vehicle-end {
            bottom: 100px;
            right: 120px;
            background: linear-gradient(135deg, #f39c12, #e67e22);
            color: white;
            z-index: 2;
        }
        .connection-line {
            position: absolute;
            z-index: 1;
        }
        .vertical-line {
            width: 4px;
            left: 50%;
            transform: translateX(-50%);
            top: 170px;
            height: 180px;
            background: linear-gradient(to bottom, #3498db, #95a5a6);
        }
        .horizontal-line {
            height: 4px;
            top: 350px;
            left: 280px;
            right: 280px;
            background: linear-gradient(to right, #2ecc71, #f39c12);
        }
        .component-title {
            text-align: center;
            font-weight: bold;
            font-size: 1.4em;
            margin-bottom: 20px;
            padding-bottom: 10px;
            border-bottom: 2px solid rgba(255,255,255,0.3);
            position: relative;
            text-shadow: 1px 1px 2px rgba(0,0,0,0.1);
        }
        .component-list {
            font-size: 0.95em;
            line-height: 1.5;
        }
        .interaction-flow {
            margin-top: 50px;
            background: linear-gradient(to right, #f8f9fa, #ffffff);
            padding: 30px;
            border-radius: 12px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.05);
            position: relative;
        }
        .interaction-flow:before {
            content: '';
            position: absolute;
            left: 0;
            top: 0;
            bottom: 0;
            width: 6px;
            background: linear-gradient(to bottom, #3498db, #2ecc71);
            border-radius: 3px 0 0 3px;
        }
        .flow-step {
            margin-bottom: 25px;
            display: flex;
            align-items: flex-start;
            position: relative;
        }
        .flow-step:last-child {
            margin-bottom: 0;
        }
        .flow-step:after {
            content: '';
            position: absolute;
            left: 15px;
            top: 45px;
            bottom: -15px;
            width: 2px;
            background: rgba(52, 152, 219, 0.3);
        }
        .flow-step:last-child:after {
            display: none;
        }
        .step-number {
            background: linear-gradient(135deg, #3498db, #2980b9);
            color: white;
            width: 30px;
            height: 30px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 50%;
            margin-right: 20px;
            flex-shrink: 0;
            box-shadow: 0 3px 6px rgba(0,0,0,0.1);
            position: relative;
            z-index: 2;
        }
        .arrow-container {
            position: absolute;
            text-align: center;
            font-weight: 500;
            background: rgba(255,255,255,0.9);
            padding: 8px 12px;
            border-radius: 8px;
            box-shadow: 0 3px 10px rgba(0,0,0,0.1);
            z-index: 4;
        }
        .arrow-cloud-mobile {
            left: 360px;
            top: 260px;
        }
        .arrow-cloud-vehicle {
            right: 360px;
            top: 260px;
        }
        .arrow-mobile-vehicle {
            top: 450px;
            left: 50%;
            transform: translateX(-50%);
            width: 250px;
        }
        .legend {
            margin-top: 30px;
            display: flex;
            justify-content: center;
            gap: 30px;
            padding: 15px;
            background: rgba(255,255,255,0.7);
            border-radius: 10px;
            box-shadow: 0 3px 10px rgba(0,0,0,0.05);
        }
        .legend-item {
            display: flex;
            align-items: center;
        }
        .legend-color {
            width: 20px;
            height: 20px;
            margin-right: 10px;
            border-radius: 4px;
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
        }
        /* 添加箭头样式 */
        .arrow {
            position: absolute;
            width: 30px;
            height: 30px;
            z-index: 4;
        }
        .arrow-down-1 {
            top: 200px;
            left: 42%;
            transform: rotate(90deg);
            color: #3498db;
        }
        .arrow-up-1 {
            top: 240px;
            left: 58%;
            transform: rotate(-90deg);
            color: #3498db;
        }
        .arrow-down-2 {
            top: 290px;
            right: 42%;
            transform: rotate(90deg);
            color: #f39c12;
        }
        .arrow-up-2 {
            top: 240px;
            right: 58%;
            transform: rotate(-90deg);
            color: #f39c12;
        }
        .arrow-left {
            top: 405px;
            left: 45%;
            transform: rotate(180deg);
            color: #2ecc71;
        }
        .arrow-right {
            top: 440px;
            right: 45%;
            transform: rotate(0deg);
            color: #e67e22;
        }
        /* 添加图标样式 */
        .icon {
            font-size: 1.5em;
            margin-right: 10px;
            vertical-align: middle;
        }
        
        /* 连接线动画 */
        @keyframes pulse {
            0% { opacity: 0.5; }
            50% { opacity: 1; }
            100% { opacity: 0.5; }
        }
        .connection-line {
            animation: pulse 2s infinite;
        }
        
        /* 添加响应式样式 */
        @media (max-width: 1200px) {
            .component {
                width: 280px;
            }
            .cloud-platform {
                top: 30px;
            }
            .mobile-app {
                left: 80px;
            }
            .vehicle-end {
                right: 80px;
            }
            .horizontal-line {
                left: 240px;
                right: 240px;
            }
            .arrow-cloud-mobile {
                left: 320px;
            }
            .arrow-cloud-vehicle {
                right: 320px;
            }
        }
        
        @media (max-width: 992px) {
            .architecture-diagram {
                height: 800px;
            }
            .component {
                width: 80%;
                left: 10%;
                transform: none;
            }
            .cloud-platform {
                top: 50px;
                transform: none;
            }
            .cloud-platform:hover {
                transform: translateY(-5px);
            }
            .mobile-app {
                top: 350px;
                bottom: auto;
                left: 10%;
            }
            .vehicle-end {
                top: 600px;
                bottom: auto;
                right: 10%;
            }
            .vertical-line, .horizontal-line {
                display: none;
            }
            .arrow-container {
                display: none;
            }
        }
        
        /* 添加专业装饰 */
        .decoration {
            position: absolute;
            width: 100px;
            height: 100px;
            opacity: 0.05;
            z-index: 0;
        }
        .dec-1 {
            top: 20px;
            left: 20px;
            background: radial-gradient(circle, #3498db, transparent);
            border-radius: 50%;
        }
        .dec-2 {
            bottom: 20px;
            right: 20px;
            background: radial-gradient(circle, #2ecc71, transparent);
            border-radius: 50%;
        }
        
        /* 添加专业标题效果 */
        .flow-title {
            font-weight: 600;
            background: linear-gradient(90deg, #3498db, #2ecc71);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            display: inline-block;
        }
    </style>
</head>
<body>
    <div class="slide">
        <h1>数字钥匙系统三端架构</h1>
        
        <!-- 装饰元素 -->
        <div class="decoration dec-1"></div>
        <div class="decoration dec-2"></div>
        
        <div class="architecture-container">
            <div class="architecture-diagram">
                <!-- 连接线 -->
                <div class="connection-line vertical-line"></div>
                <div class="connection-line horizontal-line"></div>
                
                <!-- 云平台组件 -->
                <div class="component cloud-platform">
                    <div class="component-title">
                        <span class="icon">☁️</span>钥匙云平台
                    </div>
                    <div class="component-list">
                        • 钥匙生命周期管理<br>
                        • 车辆关联服务<br>
                        • 安全认证中心<br>
                        • 密钥管理系统<br>
                        • 统一接口服务<br>
                        • 安全通信通道<br>
                        • 异常监控与处理<br>
                        • 时间服务器
                    </div>
                </div>
                
                <!-- 手机端组件 -->
                <div class="component mobile-app">
                    <div class="component-title">
                        <span class="icon">📱</span>手机端
                    </div>
                    <div class="component-list">
                        • 钥匙管理模块<br>
                        • 车辆控制模块<br>
                        • 安全存储模块<br>
                        • 蓝牙通信模块<br>
                        • 时间同步模块<br>
                        • 暗号交换模块<br>
                        • 智能场景管理模块<br>
                        • 异常处理模块<br>
                        • 标定模块
                    </div>
                </div>
                
                <!-- 车端组件 -->
                <div class="component vehicle-end">
                    <div class="component-title">
                        <span class="icon">🚗</span>车端
                    </div>
                    <div class="component-list">
                        • 蓝牙通信模块<br>
                        • 钥匙验证模块<br>
                        • 指令执行模块<br>
                        • 安全存储模块<br>
                        • 远程通信模块<br>
                        • 时间同步模块<br>
                        • 暗号交换模块<br>
                        • 用户行为分析模块<br>
                        • 异常处理模块<br>
                        • 标定配置模块
                    </div>
                </div>
                
                <!-- 箭头和说明 -->
                <div class="arrow-container arrow-cloud-mobile">
                    ↓ 钥匙分发、授权<br>
                    ↑ 状态同步、认证
                </div>
                
                <div class="arrow-container arrow-cloud-vehicle">
                    ↓ 配置下发、更新<br>
                    ↑ 状态上报、日志
                </div>
                
                <div class="arrow-container arrow-mobile-vehicle">
                    ← 控制指令、认证 →<br>
                    ← 状态反馈、蓝牙连接 →
                </div>
                
                <!-- 动态箭头图标 -->
                <div class="arrow arrow-down-1">⬇️</div>
                <div class="arrow arrow-up-1">⬆️</div>
                <div class="arrow arrow-down-2">⬇️</div>
                <div class="arrow arrow-up-2">⬆️</div>
                <div class="arrow arrow-left">⬅️</div>
                <div class="arrow arrow-right">➡️</div>
            </div>
        </div>
        
        <div class="legend">
            <div class="legend-item">
                <div class="legend-color" style="background: linear-gradient(135deg, #3498db, #2980b9);"></div>
                <div>云平台</div>
            </div>
            <div class="legend-item">
                <div class="legend-color" style="background: linear-gradient(135deg, #2ecc71, #27ae60);"></div>
                <div>手机端</div>
            </div>
            <div class="legend-item">
                <div class="legend-color" style="background: linear-gradient(135deg, #f39c12, #e67e22);"></div>
                <div>车端</div>
            </div>
        </div>
        
        <h2>数字钥匙交互流程</h2>
        
        <div class="interaction-flow">
            <div class="flow-step">
                <div class="step-number">1</div>
                <div>
                    <span class="flow-title">钥匙创建与授权:</span> 云平台生成数字钥匙，设定权限与有效期，通过安全通道下发至用户手机端
                </div>
            </div>
            
            <div class="flow-step">
                <div class="step-number">2</div>
                <div>
                    <span class="flow-title">时间同步:</span> 三端与云平台时间服务器保持同步，确保时间一致性，防止重放攻击
                </div>
            </div>
            
            <div class="flow-step">
                <div class="step-number">3</div>
                <div>
                    <span class="flow-title">蓝牙发现与连接:</span> 车端发送加密广播信号，手机端接收并识别车辆，主动发起连接请求
                </div>
            </div>
            
            <div class="flow-step">
                <div class="step-number">4</div>
                <div>
                    <span class="flow-title">双向认证:</span> 手机端与车端交换并验证身份凭证，确保双方身份可信
                </div>
            </div>
            
            <div class="flow-step">
                <div class="step-number">5</div>
                <div>
                    <span class="flow-title">暗号交换:</span> 手机端与车端协商生成临时会话密钥和通信暗号，建立安全通信通道
                </div>
            </div>
            
            <div class="flow-step">
                <div class="step-number">6</div>
                <div>
                    <span class="flow-title">距离感知:</span> 基于RSSI信号强度，车端计算与手机的实际距离，支持无感进入和距离感知操作
                </div>
            </div>
            
            <div class="flow-step">
                <div class="step-number">7</div>
                <div>
                    <span class="flow-title">控制指令执行:</span> 手机端发送控制指令（开锁、启动等），车端验证权限后执行并反馈结果
                </div>
            </div>
            
            <div class="flow-step">
                <div class="step-number">8</div>
                <div>
                    <span class="flow-title">状态同步:</span> 车端将操作状态同步至云平台，手机端可查看最新车辆状态
                </div>
            </div>
            
            <div class="flow-step">
                <div class="step-number">9</div>
                <div>
                    <span class="flow-title">异常处理:</span> 三端各自监控异常行为，执行安全措施，确保系统安全可靠
                </div>
            </div>
        </div>
    </div>
    
    <script>
        // 简单的动画效果
        document.querySelectorAll('.component').forEach(comp => {
            comp.addEventListener('mouseover', function() {
                this.style.transform = this.classList.contains('cloud-platform') ? 
                    'translateX(-50%) translateY(-5px)' : 'translateY(-5px)';
            });
            
            comp.addEventListener('mouseout', function() {
                this.style.transform = this.classList.contains('cloud-platform') ? 
                    'translateX(-50%)' : 'none';
            });
        });
    </script>
</body>
</html>

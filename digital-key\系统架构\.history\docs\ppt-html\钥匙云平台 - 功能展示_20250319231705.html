<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>钥匙云平台</title>
    <!-- 引入html2canvas库 -->
    <script src="https://html2canvas.hertzen.com/dist/html2canvas.min.js"></script>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
            font-family: 'PingFang SC', 'Microsoft YaHei', sans-serif;
        }
        
        body {
            background: #f0f0f0;
            display: flex;
            justify-content: center;
            align-items: center;
            min-height: 100vh;
            color: #333;
            padding: 20px;
        }
        
        /* PPT边框指示器 */
        .ppt-container {
            position: relative;
            width: 1920px; /* 标准PPT宽度 */
            height: 1080px; /* 标准PPT高度 */
            box-shadow: 0 0 30px rgba(0, 0, 0, 0.2);
            border: 2px dashed #ccc;
            background-image: linear-gradient(45deg, #ccc 25%, transparent 25%), 
                              linear-gradient(-45deg, #ccc 25%, transparent 25%), 
                              linear-gradient(45deg, transparent 75%, #ccc 75%), 
                              linear-gradient(-45deg, transparent 75%, #ccc 75%);
            background-size: 20px 20px;
            background-position: 0 0, 0 10px, 10px -10px, -10px 0px;
        }
        
        .ppt-label {
            position: absolute;
            top: -40px;
            left: 0;
            background-color: #555;
            color: white;
            padding: 5px 15px;
            border-radius: 4px;
            font-size: 16px;
        }
        
        /* 复制按钮 */
        .copy-button {
            position: absolute;
            top: 20px;
            right: 20px;
            background: linear-gradient(135deg, #3498db, #2980b9);
            color: white;
            border: none;
            padding: 12px 25px;
            border-radius: 30px;
            font-size: 18px;
            cursor: pointer;
            z-index: 1000;
            box-shadow: 0 4px 10px rgba(0, 0, 0, 0.2);
            display: flex;
            align-items: center;
            gap: 10px;
            transition: all 0.3s ease;
        }
        
        /* 下载按钮 */
        .download-button {
            position: absolute;
            top: 20px;
            right: 230px;
            background: linear-gradient(135deg, #2ecc71, #27ae60);
            color: white;
            border: none;
            padding: 12px 25px;
            border-radius: 30px;
            font-size: 18px;
            cursor: pointer;
            z-index: 1000;
            box-shadow: 0 4px 10px rgba(0, 0, 0, 0.2);
            display: flex;
            align-items: center;
            gap: 10px;
            transition: all 0.3s ease;
        }
        
        .copy-button:hover, .download-button:hover {
            transform: translateY(-3px);
            box-shadow: 0 6px 15px rgba(0, 0, 0, 0.25);
        }
        
        .copy-button:active, .download-button:active {
            transform: translateY(0);
        }
        
        .copy-icon {
            width: 20px;
            height: 20px;
            background-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="white"><path d="M19,21H8V7H19M19,5H8A2,2 0 0,0 6,7V21A2,2 0 0,0 8,23H19A2,2 0 0,0 21,21V7A2,2 0 0,0 19,5M16,1H4A2,2 0 0,0 2,3V17H4V3H16V1Z" /></svg>');
            background-size: contain;
            background-repeat: no-repeat;
        }
        
        .download-icon {
            width: 20px;
            height: 20px;
            background-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="white"><path d="M5,20H19V18H5M19,9H15V3H9V9H5L12,16L19,9Z" /></svg>');
            background-size: contain;
            background-repeat: no-repeat;
        }
        
        /* 清晰度选择器样式 */
        .resolution-selector {
            position: absolute;
            top: 70px;
            right: 230px;
            display: flex;
            align-items: center;
            background: rgba(255, 255, 255, 0.9);
            padding: 8px 15px;
            border-radius: 30px;
            box-shadow: 0 4px 10px rgba(0, 0, 0, 0.1);
            z-index: 1000;
        }
        
        .resolution-label {
            margin-right: 10px;
            font-size: 14px;
            color: #555;
        }
        
        .resolution-btn {
            border: none;
            background: #f0f0f0;
            padding: 5px 10px;
            margin: 0 3px;
            border-radius: 15px;
            font-size: 14px;
            cursor: pointer;
            transition: all 0.2s ease;
            color: #555;
        }
        
        .resolution-btn:hover {
            background: #e0e0e0;
        }
        
        .resolution-btn.active {
            background: linear-gradient(135deg, #3498db, #2980b9);
            color: white;
        }
        
        /* 新设计开始 - 完全透明背景 */
        .slide {
            width: 100%;
            height: 100%;
            background-color: transparent; /* 完全透明背景 */
            padding: 60px;
            display: flex;
            flex-direction: column;
            overflow: hidden;
            position: relative;
        }
        
        /* 顶部标题区域 - 修改了标题样式，删除背景渐变 */
        .header {
            text-align: center;
            margin-bottom: 40px;
            position: relative;
            z-index: 10;
        }
        
        .header h1 {
            font-size: 64px;
            color: #2c3e50; /* 使用纯色而非渐变 */
            margin-bottom: 15px;
            position: relative;
            display: inline-block;
            font-weight: 700;
            background: none; /* 移除背景 */
        }
        
        .header .subtitle {
            font-size: 28px;
            color: #7f8c8d;
            font-weight: 400;
            max-width: 800px;
            margin: 0 auto;
        }
        
        /* 特色功能网格布局 */
        .features-grid {
            display: grid;
            grid-template-columns: repeat(4, 1fr);
            gap: 30px;
            padding: 0 40px;
            margin-top: 20px;
            position: relative;
            z-index: 10;
        }
        
        /* 特色功能卡片 */
        .feature-card {
            background: white;
            border-radius: 16px;
            padding: 30px;
            display: flex;
            flex-direction: column;
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.05);
            height: 100%;
        }
        
        .feature-card:hover {
            transform: translateY(-10px);
            box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
        }
        
        .feature-icon {
            width: 70px;
            height: 70px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-bottom: 20px;
            position: relative;
        }
        
        .feature-icon::before {
            content: '';
            position: absolute;
            width: 70px;
            height: 70px;
            border-radius: 50%;
            background: linear-gradient(135deg, rgba(52, 152, 219, 0.2), rgba(41, 128, 185, 0.2));
            z-index: 1;
        }
        
        .feature-icon svg {
            width: 40px;
            height: 40px;
            z-index: 2;
        }
        
        .feature-title {
            font-size: 24px;
            font-weight: 600;
            color: #2c3e50;
            margin-bottom: 15px;
            position: relative;
        }
        
        .feature-title::after {
            content: '';
            position: absolute;
            bottom: -8px;
            left: 0;
            width: 40px;
            height: 3px;
            background: linear-gradient(90deg, #3498db, transparent);
            border-radius: 3px;
        }
        
        .feature-desc {
            font-size: 16px;
            color: #7f8c8d;
            line-height: 1.6;
            flex-grow: 1;
        }
        
        /* 自定义每个卡片的图标和颜色 */
        .feature-1 .feature-icon::before { background: linear-gradient(135deg, rgba(52, 152, 219, 0.2), rgba(41, 128, 185, 0.2)); }
        .feature-2 .feature-icon::before { background: linear-gradient(135deg, rgba(46, 204, 113, 0.2), rgba(39, 174, 96, 0.2)); }
        .feature-3 .feature-icon::before { background: linear-gradient(135deg, rgba(155, 89, 182, 0.2), rgba(142, 68, 173, 0.2)); }
        .feature-4 .feature-icon::before { background: linear-gradient(135deg, rgba(230, 126, 34, 0.2), rgba(211, 84, 0, 0.2)); }
        .feature-5 .feature-icon::before { background: linear-gradient(135deg, rgba(52, 73, 94, 0.2), rgba(44, 62, 80, 0.2)); }
        .feature-6 .feature-icon::before { background: linear-gradient(135deg, rgba(241, 196, 15, 0.2), rgba(243, 156, 18, 0.2)); }
        .feature-7 .feature-icon::before { background: linear-gradient(135deg, rgba(231, 76, 60, 0.2), rgba(192, 57, 43, 0.2)); }
        .feature-8 .feature-icon::before { background: linear-gradient(135deg, rgba(26, 188, 156, 0.2), rgba(22, 160, 133, 0.2)); }
        
        /* 卡片顶部颜色条 */
        .feature-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 5px;
            background: linear-gradient(90deg, transparent, #3498db, transparent);
        }
        
        .feature-1::before { background: linear-gradient(90deg, transparent, #3498db, transparent); }
        .feature-2::before { background: linear-gradient(90deg, transparent, #2ecc71, transparent); }
        .feature-3::before { background: linear-gradient(90deg, transparent, #9b59b6, transparent); }
        .feature-4::before { background: linear-gradient(90deg, transparent, #e67e22, transparent); }
        .feature-5::before { background: linear-gradient(90deg, transparent, #34495e, transparent); }
        .feature-6::before { background: linear-gradient(90deg, transparent, #f1c40f, transparent); }
        .feature-7::before { background: linear-gradient(90deg, transparent, #e74c3c, transparent); }
        .feature-8::before { background: linear-gradient(90deg, transparent, #1abc9c, transparent); }
        
        /* 底部装饰和版权 */
        .footer {
            position: absolute;
            bottom: 20px;
            width: 100%;
            text-align: center;
            font-size: 14px;
            color: #95a5a6;
            left: 0;
            z-index: 10;
        }
        
        /* 侧边装饰 */
        .side-decoration {
            position: absolute;
            bottom: 40px;
            right: 40px;
            display: flex;
            gap: 15px;
            z-index: 10;
        }
        
        .side-icon {
            width: 50px;
            height: 50px;
            opacity: 0.7;
            transition: all 0.3s ease;
        }
        
        .side-icon:hover {
            transform: scale(1.1);
            opacity: 1;
        }
        
        /* 消息提示 */
        .toast {
            position: fixed;
            top: 50px;
            left: 50%;
            transform: translateX(-50%);
            background-color: rgba(0, 0, 0, 0.8);
            color: white;
            padding: 12px 25px;
            border-radius: 30px;
            font-size: 16px;
            z-index: 2000;
            display: none;
            animation: fadeInOut 2s ease;
        }
        
        @keyframes fadeInOut {
            0% { opacity: 0; }
            20% { opacity: 1; }
            80% { opacity: 1; }
            100% { opacity: 0; }
        }
    </style>
</head>
<body>
    <div class="ppt-container" id="pptContainer">
        <div class="ppt-label">PPT尺寸(1920×1080) - 透明背景</div>
        <button class="copy-button" id="copyButton">
            <span class="copy-icon"></span>复制为PPT图片
        </button>
        <button class="download-button" id="downloadButton">
            <span class="download-icon"></span>下载透明PNG
        </button>
        
        <!-- 添加清晰度选择按钮组 -->
        <div class="resolution-selector" id="resolutionSelector">
            <span class="resolution-label">清晰度:</span>
            <button class="resolution-btn" data-scale="1">标准</button>
            <button class="resolution-btn active" data-scale="2">高清</button>
            <button class="resolution-btn" data-scale="3">超清</button>
        </div>
        
        <div class="slide" id="slide">
            <!-- 顶部标题 -->
            <div class="header">
                <h1>钥匙云平台</h1>
                <div class="subtitle">安全、高效的智能车辆数字钥匙全生命周期管理系统</div>
            </div>
            
            <!-- 功能展示网格 -->
            <div class="features-grid">
                <!-- 功能1: 钥匙生命周期管理 -->
                <div class="feature-card feature-1">
                    <div class="feature-icon">
                        <svg viewBox="0 0 24 24" fill="#3498db">
                            <path d="M7,14A2,2 0 0,1 5,12A2,2 0 0,1 7,10A2,2 0 0,1 9,12A2,2 0 0,1 7,14M12.65,10C11.83,7.67 9.61,6 7,6A6,6 0 0,0 1,12A6,6 0 0,0 7,18C9.61,18 11.83,16.33 12.65,14H17V18H21V14H23V10H12.65Z" />
                        </svg>
                    </div>
                    <div class="feature-title">钥匙生命周期管理</div>
                    <div class="feature-desc">创建、授权、撤销数字钥匙，精确控制钥匙权限与有效期，支持黑名单设定，实现全流程智能管理</div>
                </div>
                
                <!-- 功能2: 车辆关联服务 -->
                <div class="feature-card feature-2">
                    <div class="feature-icon">
                        <svg viewBox="0 0 24 24" fill="#2ecc71">
                            <path d="M18,18.5A1.5,1.5 0 0,1 16.5,17A1.5,1.5 0 0,1 18,15.5A1.5,1.5 0 0,1 19.5,17A1.5,1.5 0 0,1 18,18.5M19.5,9.5L21.46,12H17V9.5M6,18.5A1.5,1.5 0 0,1 4.5,17A1.5,1.5 0 0,1 6,15.5A1.5,1.5 0 0,1 7.5,17A1.5,1.5 0 0,1 6,18.5M20,8L23,12V17H21A3,3 0 0,1 18,20A3,3 0 0,1 15,17H9A3,3 0 0,1 6,20A3,3 0 0,1 3,17H1V6C1,4.89 1.89,4 3,4H17V8H20Z" />
                        </svg>
                    </div>
                    <div class="feature-title">车辆关联服务</div>
                    <div class="feature-desc">验证车辆信息，建立车辆与钥匙的安全绑定关系，确保数字凭证与实体车辆一一对应</div>
                </div>
                
                <!-- 功能3: 安全认证中心 -->
                <div class="feature-card feature-3">
                    <div class="feature-icon">
                        <svg viewBox="0 0 24 24" fill="#9b59b6">
                            <path d="M12,1L3,5V11C3,16.55 6.84,21.74 12,23C17.16,21.74 21,16.55 21,11V5L12,1M12,5A3,3 0 0,1 15,8A3,3 0 0,1 12,11A3,3 0 0,1 9,8A3,3 0 0,1 12,5M17.13,17C15.92,18.85 14.11,20.24 12,20.92C9.89,20.24 8.08,18.85 6.87,17C6.53,16.5 6.24,16 6,15.47C6,13.82 8.71,12.47 12,12.47C15.29,12.47 18,13.79 18,15.47C17.76,16 17.47,16.5 17.13,17Z" />
                        </svg>
                    </div>
                    <div class="feature-title">安全认证中心</div>
                    <div class="feature-desc">确保用户身份可信，执行多层次权限验证，有效防止未授权访问和身份冒用行为</div>
                </div>
                
                <!-- 功能4: 密钥管理系统 -->
                <div class="feature-card feature-4">
                    <div class="feature-icon">
                        <svg viewBox="0 0 24 24" fill="#e67e22">
                            <path d="M22,18V22H18V19H15V16H12L9.74,13.74C9.19,13.91 8.61,14 8,14A6,6 0 0,1 2,8A6,6 0 0,1 8,2A6,6 0 0,1 14,8C14,8.61 13.91,9.19 13.74,9.74L22,18M7,5A2,2 0 0,0 5,7A2,2 0 0,0 7,9A2,2 0 0,0 9,7A2,2 0 0,0 7,5Z" />
                        </svg>
                    </div>
                    <div class="feature-title">密钥管理系统</div>
                    <div class="feature-desc">生成加密密钥，确保密钥安全存储与分发，支持密钥更新与版本管理，构建立体防护</div>
                </div>
                
                <!-- 功能5: 统一接口服务 -->
                <div class="feature-card feature-5">
                    <div class="feature-icon">
                        <svg viewBox="0 0 24 24" fill="#34495e">
                            <path d="M20,19V7H4V19H20M20,3A2,2 0 0,1 22,5V19A2,2 0 0,1 20,21H4A2,2 0 0,1 2,19V5A2,2 0 0,1 4,3H20M13,17V15H18V17H13M9.58,13L5.57,9H8.4L11.7,12.3C12.09,12.69 12.09,13.33 11.7,13.72L8.42,17H5.59L9.58,13Z" />
                        </svg>
                    </div>
                    <div class="feature-title">统一接口服务</div>
                    <div class="feature-desc">提供标准化API，连接外部系统，确保接口安全，支持跨平台无缝对接与扩展</div>
                </div>
                
                <!-- 功能6: 安全通信通道 -->
                <div class="feature-card feature-6">
                    <div class="feature-icon">
                        <svg viewBox="0 0 24 24" fill="#f1c40f">
                            <path d="M12,1L3,5V11C3,16.55 6.84,21.74 12,23C17.16,21.74 21,16.55 21,11V5L12,1M12,5.68C12.5,5.68 12.95,6.11 12.95,6.63V10.11L18,13.26V14.53L12.95,12.95V16.42L14.21,17.37V18.32L12,17.68L9.79,18.32V17.37L11.05,16.42V12.95L6,14.53V13.26L11.05,10.11V6.63C11.05,6.11 11.5,5.68 12,5.68Z" />
                        </svg>
                    </div>
                    <div class="feature-title">安全通信通道</div>
                    <div class="feature-desc">实现端到端加密通信，防止数据被窃听或篡改，保障传输过程中的数据安全</div>
                </div>
                
                <!-- 功能7: 异常监控与处理 -->
                <div class="feature-card feature-7">
                    <div class="feature-icon">
                        <svg viewBox="0 0 24 24" fill="#e74c3c">
                            <path d="M12,9A3,3 0 0,1 15,12A3,3 0 0,1 12,15A3,3 0 0,1 9,12A3,3 0 0,1 12,9M12,4.5C17,4.5 21.27,7.61 23,12C21.27,16.39 17,19.5 12,19.5C7,19.5 2.73,16.39 1,12C2.73,7.61 7,4.5 12,4.5M3.18,12C4.83,15.36 8.24,17.5 12,17.5C15.76,17.5 19.17,15.36 20.82,12C19.17,8.64 15.76,6.5 12,6.5C8.24,6.5 4.83,8.64 3.18,12Z" />
                        </svg>
                    </div>
                    <div class="feature-title">异常监控与处理</div>
                    <div class="feature-desc">智能检测异常行为，执行安全响应，记录安全事件，建立完整事件追溯机制</div>
                </div>
                
                <!-- 功能8: 时间服务器 -->
                <div class="feature-card feature-8">
                    <div class="feature-icon">
                        <svg viewBox="0 0 24 24" fill="#1abc9c">
                            <path d="M12,20A8,8 0 0,0 20,12A8,8 0 0,0 12,4A8,8 0 0,0 4,12A8,8 0 0,0 12,20M12,2A10,10 0 0,1 22,12A10,10 0 0,1 12,22C6.47,22 2,17.5 2,12A10,10 0 0,1 12,2M12.5,7V12.25L17,14.92L16.25,16.15L11,13V7H12.5Z" />
                        </svg>
                    </div>
                    <div class="feature-title">时间服务器</div>
                    <div class="feature-desc">提供精确时间基准，支持NTP/PTP时间同步，有效防止重放攻击，确保系统操作的时序准确性</div>
                </div>
            </div>
            
            
            <!-- 侧边装饰图标 -->
            <div class="side-decoration">
                <svg class="side-icon" viewBox="0 0 24 24" fill="#3498db">
                    <path d="M12,17A2,2 0 0,0 14,15C14,13.89 13.1,13 12,13A2,2 0 0,0 10,15A2,2 0 0,0 12,17M18,8A2,2 0 0,1 20,10V20A2,2 0 0,1 18,22H6A2,2 0 0,1 4,20V10C4,8.89 4.9,8 6,8H7V6A5,5 0 0,1 12,1A5,5 0 0,1 17,6V8H18M12,3A3,3 0 0,0 9,6V8H15V6A3,3 0 0,0 12,3Z" />
                </svg>
                <svg class="side-icon" viewBox="0 0 24 24" fill="#2ecc71">
                    <path d="M21,11C21,16.55 17.16,21.74 12,23C6.84,21.74 3,16.55 3,11V5L12,1L21,5V11M12,21C15.75,20 19,15.54 19,11.22V6.3L12,3.18L5,6.3V11.22C5,15.54 8.25,20 12,21Z" />
                </svg>
                <svg class="side-icon" viewBox="0 0 24 24" fill="#e67e22">
                    <path d="M12,12H19C18.47,16.11 15.72,19.78 12,20.92V12H5V6.3L12,3.19M12,1L3,5V11C3,16.55 6.84,21.73 12,23C17.16,21.73 21,16.55 21,11V5L12,1Z" />
                </svg>
            </div>
        </div>
    </div>
    
    <div class="toast" id="toast">操作成功！</div>
    
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const copyButton = document.getElementById('copyButton');
            const downloadButton = document.getElementById('downloadButton');
            const toast = document.getElementById('toast');
            const slide = document.getElementById('slide');
            const resolutionBtns = document.querySelectorAll('.resolution-btn');
            
            // 默认清晰度为2x (高清)
            let currentScale = 2;
            
            // 清晰度按钮点击事件
            resolutionBtns.forEach(btn => {
                btn.addEventListener('click', function() {
                    // 移除所有按钮的active类
                    resolutionBtns.forEach(b => b.classList.remove('active'));
                    // 给当前按钮添加active类
                    this.classList.add('active');
                    // 更新当前清晰度
                    currentScale = parseFloat(this.getAttribute('data-scale'));
                    
                    // 显示提示
                    showToast(`清晰度已设置为${this.textContent}模式`);
                });
            });
            
            // 显示提示消息
            function showToast(message) {
                toast.textContent = message;
                toast.style.display = 'block';
                setTimeout(function() {
                    toast.style.display = 'none';
                }, 2000);
            }
            
            // 生成透明背景图片
            function generateImage() {
                return html2canvas(slide, {
                    scale: currentScale, // 使用用户选择的清晰度
                    useCORS: true,
                    allowTaint: true,
                    backgroundColor: null // 关键：设置为null以支持透明背景
                });
            }
            
            // 复制按钮功能
            copyButton.addEventListener('click', function() {
                showToast("正在处理图片，请稍候...");
                
                generateImage().then(function(canvas) {
                    try {
                        // 使用现代剪贴板API
                        canvas.toBlob(function(blob) {
                            try {
                                const item = new ClipboardItem({ 'image/png': blob });
                                navigator.clipboard.write([item]).then(function() {
                                    showToast("已复制到剪贴板！现在可以粘贴到PPT中");
                                }).catch(function(err) {
                                    showToast("自动复制失败，请使用下载按钮获取图片");
                                    console.error("剪贴板API错误:", err);
                                });
                            } catch (e) {
                                showToast("您的浏览器不支持复制图片，请使用下载按钮");
                                console.error("ClipboardItem错误:", e);
                            }
                        });
                    } catch (e) {
                        showToast("复制功能不可用，请使用下载图片功能");
                        console.error("toBlob错误:", e);
                    }
                }).catch(function(err) {
                    showToast("生成图片失败，请尝试下载或截图");
                    console.error("html2canvas错误:", err);
                });
            });
            
            // 下载按钮功能
           downloadButton.addEventListener('click', function () {
                showToast(`正在生成${currentScale}x清晰度透明PNG图片，请稍候...`);

                generateImage().then(function (canvas) {
                    // 将Canvas转换为PNG并下载
                    const imageUrl = canvas.toDataURL('image/png');
                    const link = document.createElement('a');

                    // 首先从URL路径获取文件名
                    let fileName = "";
                    const pathParts = window.location.pathname.split('/');
                    const htmlFileName = pathParts[pathParts.length - 1];
                    // 移除.html后缀并进行URL解码以支持中文文件名
                    fileName = decodeURIComponent(htmlFileName.replace('.html', ''));

                    // 如果从URL获取的文件名为空，尝试从标题获取
                    if (!fileName) {
                        const titleElement = document.querySelector('.header h1');
                        if (titleElement && titleElement.textContent) {
                            fileName = titleElement.textContent.trim();
                        }
                    }

                    // 确保文件名不为空
                    if (!fileName) {
                        fileName = "测试";
                    }

                    link.download = `${fileName}-透明-${currentScale}x清晰度.png`;
                    link.href = imageUrl;
                    link.click();

                    showToast(`${currentScale}x清晰度透明背景PNG已下载`);
                }).catch(function (err) {
                    showToast("生成图片失败，请尝试截图");
                    console.error("Error:", err);
                });
            });
        });
    </script>
</body>
</html> 
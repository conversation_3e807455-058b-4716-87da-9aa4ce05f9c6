package com.ruoyi.dk.utils;

import com.ruoyi.common.core.exception.ServiceException;
import com.ruoyi.common.security.utils.SecurityUtils;
import com.ruoyi.dk.domain.DkVehicleBluetoothKeys;
import com.ruoyi.dk.service.IDkVehicleBluetoothKeysService;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Objects;

/**
 * 从车辆识别号码（VIN）生成密钥的功能。
 * 模拟了一个简单的加密过程，通过VIN码生成一个与之相关联的唯一密钥。
 */
@Component
public class VinKeyGenerator {

    @Autowired
    private IDkVehicleBluetoothKeysService dkVehicleBluetoothKeysService;

    // 定义密钥和VIN码的长度常量
    private final int KEY_LENGTH_BYTES = 16; // 128位密钥长度（字节）
    private final int VIN_LENGTH_BYTES = 17; // VIN号最大长度17字节

    /**
     * 根据给定的VIN码生成一个密钥。（临时通讯密钥）
     * 
     * @param vin 车辆识别号码
     * @return 生成的密钥，以字节数组形式返回
     */
    public byte[] generateKeyFromVin(String vin) {
        // TODO 生成密钥逻辑待完善
        // 计算VIN码的ASCII累加值
        //long sum = 0; // 使用long以支持更大的累加值
        //for (int i = 0; i < vin.length(); i++) {
        //    sum += (long) vin.charAt(i); // 将字符转换为其ASCII值并累加
        //}
        //
        //// 获取VIN号末位数字作为加盐值的长度
        //int pos = vin.charAt(VIN_LENGTH_BYTES - 1) - '0';
        //// 提取VIN码末尾的加盐值
        //String salt = vin.substring(VIN_LENGTH_BYTES - pos);
        //
        //// 将VIN码的ASCII累加值和盐值连接起来
        //String input = sum + salt;
        //int len = input.length();
        //
        //// 对连接后的字符串进行简单的转换，生成密钥
        //byte[] key = new byte[KEY_LENGTH_BYTES];
        //for (int i = 0; i < KEY_LENGTH_BYTES; i++) {
        //    int inputChar = input.charAt(i % len); // 循环使用input字符串的字符
        //    int vinChar = vin.charAt(i % VIN_LENGTH_BYTES); // 循环使用vin字符串的字符
        //    // 执行异或运算并确保结果为一个字节
        //    key[i] = (byte) (inputChar ^ (vinChar << (pos % 2)) & 0xFF);
        //}

        Long userId = SecurityUtils.getLoginUser().getUserid();

        if (ObjectUtils.isEmpty(userId)) {
            throw new ServiceException("登录状态异常，请退出后重新登录！");
        }

        DkVehicleBluetoothKeys dkVehicleBluetoothKeyInfo = dkVehicleBluetoothKeysService.selectDkVehicleBluetoothKeysByUserId(userId);

        if (Objects.nonNull(dkVehicleBluetoothKeyInfo)) {
            dkVehicleBluetoothKeyInfo.setVehicleVin(vin);
            dkVehicleBluetoothKeyInfo.setBluetoothTempKey("临时密钥");
            dkVehicleBluetoothKeyInfo.setBluetoothPermKey("正式密钥");

            int res = dkVehicleBluetoothKeysService.updateDkVehicleBluetoothKeys(dkVehicleBluetoothKeyInfo);

            if (res <= 0) {
                throw new ServiceException("绑定失败！");
            }
        } else {
            dkVehicleBluetoothKeyInfo = new DkVehicleBluetoothKeys();
            dkVehicleBluetoothKeyInfo.setUserId(userId);
            dkVehicleBluetoothKeyInfo.setVehicleVin(vin);

            int res = dkVehicleBluetoothKeysService.insertDkVehicleBluetoothKeys(dkVehicleBluetoothKeyInfo);

            if (res <= 0) {
                throw new ServiceException("绑定失败！");
            }
        }

        return new byte[KEY_LENGTH_BYTES];
    }

    /**
     * 主函数演示了如何使用这个类生成密钥。
     * 
     * @param args 命令行参数
     */
    public static void main(String[] args) {
        // 初始化一个VIN码
        String myVin = "LL3AGCJ55NA032335";
        // 循环64次，每次都更新VIN码的最后一位数字，并生成新的密钥
        for (int j = 0; j < 64; j++) {
            myVin = new VinKeyGenerator().incrementVin(myVin); // 更新VIN码
            byte[] myKey = new VinKeyGenerator().generateKeyFromVin(myVin); // 生成密钥
            // 打印VIN码和生成的密钥
            System.out.print("vin:" + myVin + " key:");
            for (byte b : myKey) {
                System.out.printf("0x%02X ", b);
            }
            System.out.println();
        }
    }

    /**
     * 辅助函数，用于递增VIN码的最后四位数字。
     * 
     * @param vin 当前的VIN码
     * @return 递增后的VIN码
     */
    private String incrementVin(String vin) {
        char[] vinChars = vin.toCharArray(); // 将VIN码转换为字符数组
        // 从VIN码的最后一位开始递增，如果超过'9'则进位
        for (int i = VIN_LENGTH_BYTES - 1; i >= VIN_LENGTH_BYTES - 4; i--) {
            if (vinChars[i] < '9') {
                vinChars[i]++; // 如果当前位小于'9'，则递增
                break; // 递增后退出循环
            } else {
                vinChars[i] = '0'; // 如果当前位是'9'，则置为'0'并进位
            }
        }
        return new String(vinChars); // 返回更新后的VIN码
    }
}

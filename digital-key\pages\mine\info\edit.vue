<template>
    <view class="container bg-cls">
    <!-- 自定义导航栏 -->
    <view class="custom-navbar">
      <text class="navbar-title">编辑资料</text>
    </view>

    <!-- 页面内容 -->
    <view class="form-container">
      <view class="example">
        <uni-forms ref="form" :model="user" labelWidth="80px">
          <view class="input-item flex align-center">
            <view class="content-nick"></view>
            <input v-model="user.nickName" class="input" type="text" placeholder="请输入昵称" maxlength="30" />
          </view>
          <view class="input-item flex align-center">
            <view class="content-mail"></view>
            <input v-model="user.email" class="input" type="text" placeholder="请输入邮箱" maxlength="30" />
          </view>
          <!-- <uni-forms-item label="手机号码" name="phonenumber">
            <uni-easyinput v-model="user.phonenumber" placeholder="请输入手机号码" />
          </uni-forms-item> -->
          <uni-forms-item label="性别" name="sex" required>
            <uni-data-checkbox v-model="user.sex" :localdata="sexs" />
          </uni-forms-item>
        </uni-forms>
        <button type="primary" @click="submit">提交</button>
      </view>
    </view>
  </view>
</template>

<script>
  import { getUserProfile } from "@/api/system/user"
  import { updateUserProfile } from "@/api/system/user"

  export default {
    data() {
      return {
        user: {
          nickName: "",
          phonenumber: "",
          email: "",
          sex: ""
        },
        sexs: [{
          text: '男',
          value: "0"
        }, {
          text: '女',
          value: "1"
        }],
        rules: {
          nickName: {
            rules: [{
              required: true,
              errorMessage: '用户昵称不能为空'
            }]
          },
          // phonenumber: {
          //   rules: [{
          //     required: true,
          //     errorMessage: '手机号码不能为空'
          //   }, {
          //     pattern: /^1[3|4|5|6|7|8|9][0-9]\d{8}$/,
          //     errorMessage: '请输入正确的手机号码'
          //   }]
          // },
          email: {
            rules: [{
              required: true,
              errorMessage: '邮箱地址不能为空'
            }, {
              format: 'email',
              errorMessage: '请输入正确的邮箱地址'
            }]
          }
        }
      }
    },
    onLoad() {
      this.getUser()
    },
    onReady() {
      this.$refs.form.setRules(this.rules)
    },
    methods: {
      getUser() {
        getUserProfile().then(response => {
          this.user = response.data
        })
      },
      submit(ref) {
        this.$refs.form.validate().then(res => {
          updateUserProfile(this.user).then(response => {
            this.$modal.msgSuccess("修改成功")
          })
        })
      }
    }
  }
</script>

<style lang="scss" scoped>
  page {
    /* 设置背景固定，不随页面滚动 */
    background-attachment: fixed;

    /* 其他样式，比如设置高度 */
    height: 70vh;
    /* 设置高度为视口高度 */
  }

  .content-nick::before {
    content: '昵称';
    color: #606266;
    font-size: 14px;
    min-width: 50px;
    display: block;
  }
  
  .content-mail::before {
    content: '邮箱';
    color: #606266;
    font-size: 14px;
    min-width: 50px;
    display: block;
  }

  .uni-forms-item {
      display: flex;
      align-items: center;
      width: auto;
  }

  .uni-forms-item label {
      flex: 0 0 auto;
  }

  .uni-forms-item .uni-data-checkbox {
      flex: 1 1 auto;
  }

  .custom-navbar {
    display: flex;
    justify-content: center;
    align-items: center;
    height: 184rpx; /* 导航栏高度 */
    border-radius: 0rpx 0rpx 0rpx 0rpx;
    background-color: rgba(0, 0, 0, 0); /* 自定义背景色 */
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    z-index: 999;
  }

  .navbar-title {
    margin-top: 75rpx;
    color: #121010; /* 标题颜色 */
  }

  .form-container {
    padding-top: 184rpx;
    width: 100%;
    height: 100%;
  }

  .example {
    padding: 15px;
    // background-color: #fff;
  }

  .segmented-control {
    margin-bottom: 15px;
  }

  .button-group {
    margin-top: 15px;
    display: flex;
    justify-content: space-around;
  }

  .form-item {
    display: flex;
    align-items: center;
    flex: 1;
  }

  .button {
    display: flex;
    align-items: center;
    height: 35px;
    line-height: 35px;
    margin-left: 10px;
  }

  // .custom-cls {
  //   border-color: rgb(229, 229, 229);
  //   background-color: rgba(0, 0, 0, 0); /* 自定义背景色 */
  //   border-bottom-style: solid;
  //   border-bottom-width: 1px;
  //   border-bottom-color: #ccc;
  // }

  // uni-easyinput__content is-input-border 
</style>

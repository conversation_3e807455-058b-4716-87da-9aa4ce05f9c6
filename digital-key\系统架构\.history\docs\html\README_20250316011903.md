# 数字钥匙技术架构展示

这是一个基于HTML的上下滚动式展示页面，用于展示数字钥匙的技术架构设计。

## 使用方法

1. 打开 `index.html` 文件，即可查看完整的技术架构展示。
2. 通过滚动页面可以浏览所有内容，或使用顶部导航栏快速跳转到各个部分。
3. 所有架构图和流程图详细内容会直接显示在页面中，无需额外点击操作。
4. 点击"查看详情"按钮可以展开相关流程的详细说明。

## 文件结构

- `index.html` - 主展示页面
- `css/style.css` - 样式文件
- `js/image-loader.js` - 内容加载和显示脚本
- `diagrams/` - 包含详细的架构图和泳道图HTML文件
  - `overall-architecture.html` - 整体架构图
  - `pairing-swimlane.html` - 首次配对流程泳道图
  - `passive-control-swimlane.html` - 无感控车流程泳道图

## 技术架构概述

数字钥匙系统由三大核心模块组成：

1. **手机端**：用户交互界面与核心SDK
2. **车端**：BLE模块与TBOX
3. **钥匙云平台**：密钥管理与安全认证

系统实现了以下核心功能：

- 首次蓝牙配对过程
- 蓝牙通讯过程
- 蓝牙通讯安全设计
- 密钥管理机制
- 时间同步机制
- 无感控车方案
- 故障应急处理机制

## 注意事项

- 本展示页面使用了现代CSS和JavaScript技术，建议使用最新版本的浏览器访问。
- 所有架构图和流程图的详细内容已直接嵌入到页面中，不需要额外点击操作。
- 内容分布在多个文件中只是为了便于维护，在展示上它们会无缝集成为一个整体。 
package com.ruoyi.framework.tcp;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

/**
 * TCP控制服务
 * 处理4G控车协议，生成和解析TBOX指令
 * 
 * <AUTHOR>
 */
@Service
public class TcpControlService {
    
    private static final Logger LOGGER = LoggerFactory.getLogger(TcpControlService.class);
    
    // 4G控车协议常量
    private static final String PROTOCOL_HEADER = "7E000A000A000102030405060708090A00003033303230303031";
    private static final String PROTOCOL_FOOTER = "007E";
    private static final String UNLOCK_CODE = "04"; // 解锁状态码
    private static final String LOCK_CODE = "03";   // 闭锁状态码
    
    /**
     * 生成解锁指令
     * 格式: 7E000A000A000102030405060708090A000030333032303030313034007E
     */
    public String generateUnlockCommand() {
        String command = PROTOCOL_HEADER + UNLOCK_CODE + PROTOCOL_FOOTER;
        LOGGER.info("🔓 生成解锁指令: {}", command);
        return command;
    }
    
    /**
     * 生成闭锁指令
     * 格式: 7E000A000A000102030405060708090A000030333032303030313033007E
     */
    public String generateLockCommand() {
        String command = PROTOCOL_HEADER + LOCK_CODE + PROTOCOL_FOOTER;
        LOGGER.info("🔒 生成闭锁指令: {}", command);
        return command;
    }
    
    /**
     * 解析TBOX消息
     */
    public TboxStatusResult parseTboxMessage(String message) {
        try {
            if (message == null || message.trim().isEmpty()) {
                return new TboxStatusResult(false, "unknown", "消息为空", "消息内容为空");
            }
            
            // 检查消息格式
            if (!message.startsWith("7E") || !message.endsWith("7E")) {
                return new TboxStatusResult(false, "unknown", "格式错误", "消息不以7E开头和结尾");
            }
            
            if (message.length() < 50) {
                return new TboxStatusResult(false, "unknown", "长度错误", "消息长度不足50字符");
            }
            
            // 提取状态码（倒数第五位）
            String statusCode = message.substring(message.length() - 6, message.length() - 5);
            
            String lockStatus;
            String statusDescription;
            
            switch (statusCode) {
                case "4":
                    lockStatus = "unlocked";
                    statusDescription = "车辆已解锁";
                    break;
                case "3":
                    lockStatus = "locked";
                    statusDescription = "车辆已闭锁";
                    break;
                default:
                    lockStatus = "unknown";
                    statusDescription = "未知状态码: " + statusCode;
                    break;
            }
            
            LOGGER.info("📋 解析TBOX消息成功: 状态={}, 描述={}", lockStatus, statusDescription);
            return new TboxStatusResult(true, lockStatus, statusDescription, null);
            
        } catch (Exception e) {
            LOGGER.error("❌ 解析TBOX消息失败: {}", e.getMessage(), e);
            return new TboxStatusResult(false, "unknown", "解析异常", e.getMessage());
        }
    }
    
    /**
     * 发送解锁指令给所有TBOX
     */
    public void sendUnlockCommandToAllTbox() {
        String command = generateUnlockCommand();
        TcpClientHandler.sendCommandToAllTbox(command);
        LOGGER.info("🔓 已发送解锁指令给所有TBOX设备");
    }
    
    /**
     * 发送闭锁指令给所有TBOX
     */
    public void sendLockCommandToAllTbox() {
        String command = generateLockCommand();
        TcpClientHandler.sendCommandToAllTbox(command);
        LOGGER.info("🔒 已发送闭锁指令给所有TBOX设备");
    }
    
    /**
     * 发送指令给指定TBOX
     */
    public void sendCommandToTbox(String tboxId, String action) {
        String command;
        if ("unlock".equalsIgnoreCase(action)) {
            command = generateUnlockCommand();
        } else if ("lock".equalsIgnoreCase(action)) {
            command = generateLockCommand();
        } else {
            LOGGER.warn("⚠️ 未知的控车动作: {}", action);
            return;
        }
        
        TcpClientHandler.sendCommandToTbox(tboxId, command);
        LOGGER.info("📤 已发送{}指令给TBOX: {}", action, tboxId);
    }
    
    /**
     * 获取当前连接的TBOX数量
     */
    public int getConnectedTboxCount() {
        return TcpClientHandler.getTboxClients().size();
    }
    
    /**
     * 验证协议格式
     */
    public boolean isValidProtocolFormat(String message) {
        if (message == null || message.trim().isEmpty()) {
            return false;
        }
        
        return message.startsWith("7E") && 
               message.endsWith("7E") && 
               message.length() >= 50;
    }
    
    /**
     * TBOX状态解析结果
     */
    public static class TboxStatusResult {
        private final boolean valid;
        private final String lockStatus;
        private final String statusDescription;
        private final String errorMessage;
        
        public TboxStatusResult(boolean valid, String lockStatus, String statusDescription, String errorMessage) {
            this.valid = valid;
            this.lockStatus = lockStatus;
            this.statusDescription = statusDescription;
            this.errorMessage = errorMessage;
        }
        
        public boolean isValid() {
            return valid;
        }
        
        public String getLockStatus() {
            return lockStatus;
        }
        
        public String getStatusDescription() {
            return statusDescription;
        }
        
        public String getErrorMessage() {
            return errorMessage;
        }
        
        @Override
        public String toString() {
            return String.format("TboxStatusResult{valid=%s, lockStatus='%s', statusDescription='%s', errorMessage='%s'}", 
                    valid, lockStatus, statusDescription, errorMessage);
        }
    }
}

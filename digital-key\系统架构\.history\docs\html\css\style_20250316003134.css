/* 整体样式 */
body {
    font-family: 'Microsoft YaHei', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    color: #333;
}

.reveal h1, .reveal h2, .reveal h3, .reveal h4 {
    font-family: 'Microsoft YaHei', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    color: #2c3e50;
    margin-bottom: 30px;
}

.reveal h1 {
    font-size: 2.5em;
    color: #1a5276;
}

.reveal h2 {
    font-size: 1.8em;
    color: #2874a6;
}

.reveal h3 {
    font-size: 1.4em;
    color: #3498db;
}

.reveal p {
    margin-bottom: 20px;
    line-height: 1.5;
}

.reveal ul, .reveal ol {
    display: inline-block;
    text-align: left;
    margin: 0 auto;
}

.reveal li {
    margin-bottom: 12px;
    line-height: 1.4;
}

/* 架构图样式 */
.architecture-diagram {
    display: flex;
    justify-content: space-around;
    margin: 30px 0;
    flex-wrap: wrap;
}

.component {
    width: 200px;
    padding: 20px;
    margin: 10px;
    border-radius: 10px;
    text-align: center;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
    transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.component:hover {
    transform: translateY(-5px);
    box-shadow: 0 6px 12px rgba(0, 0, 0, 0.15);
}

.component h3 {
    margin-top: 0;
    margin-bottom: 10px;
}

.component p {
    margin: 0;
    font-size: 0.9em;
}

.mobile {
    background-color: #d4e6f1;
    border: 2px solid #3498db;
}

.car {
    background-color: #d5f5e3;
    border: 2px solid #2ecc71;
}

.cloud {
    background-color: #ebdef0;
    border: 2px solid #9b59b6;
}

/* 组件链接样式 */
.component-links {
    display: flex;
    justify-content: space-around;
    margin: 30px 0;
}

.component-links a {
    display: inline-block;
    padding: 10px 20px;
    background-color: #3498db;
    color: white;
    text-decoration: none;
    border-radius: 5px;
    transition: background-color 0.3s ease;
}

.component-links a:hover {
    background-color: #2874a6;
}

/* 图片样式 */
.reveal img {
    max-width: 80%;
    max-height: 60vh;
    margin: 20px auto;
    border: 1px solid #ddd;
    border-radius: 5px;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

/* 强调文本 */
.reveal strong {
    color: #e74c3c;
    font-weight: bold;
}

/* 注释文本 */
.reveal small {
    color: #7f8c8d;
    font-size: 0.8em;
}

/* 自定义列表样式 */
.reveal ul {
    list-style-type: none;
}

.reveal ul li:before {
    content: "• ";
    color: #3498db;
    font-weight: bold;
    display: inline-block;
    width: 1em;
    margin-left: -1em;
}

.reveal ol {
    counter-reset: li;
    list-style-type: none;
}

.reveal ol li:before {
    content: counter(li) ". ";
    counter-increment: li;
    color: #3498db;
    font-weight: bold;
    display: inline-block;
    width: 1.5em;
    margin-left: -1.5em;
}

/* 响应式调整 */
@media (max-width: 768px) {
    .architecture-diagram {
        flex-direction: column;
        align-items: center;
    }
    
    .component {
        width: 80%;
        margin: 10px 0;
    }
    
    .component-links {
        flex-direction: column;
        align-items: center;
    }
    
    .component-links a {
        margin: 5px 0;
        width: 80%;
        text-align: center;
    }
} 
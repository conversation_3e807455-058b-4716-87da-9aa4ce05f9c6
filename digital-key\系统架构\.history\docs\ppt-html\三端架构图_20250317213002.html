<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>数字钥匙三端架构图</title>
    <script src="https://html2canvas.hertzen.com/dist/html2canvas.min.js"></script>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
            font-family: 'PingFang SC', 'Microsoft YaHei', sans-serif;
        }
        
        body {
            background: #f0f0f0;
            display: flex;
            justify-content: center;
            align-items: center;
            min-height: 100vh;
            padding: 20px;
        }
        
        .ppt-container {
            position: relative;
            width: 1920px;
            height: 1080px;
            box-shadow: 0 0 30px rgba(0, 0, 0, 0.2);
            border: 2px dashed #ccc;
            background-color: white;
        }
        
        .ppt-label {
            position: absolute;
            top: -40px;
            left: 0;
            background-color: #555;
            color: white;
            padding: 5px 15px;
            border-radius: 4px;
            font-size: 16px;
        }
        
        .copy-button {
            position: absolute;
            top: 20px;
            right: 20px;
            background: linear-gradient(135deg, #3498db, #2980b9);
            color: white;
            border: none;
            padding: 12px 25px;
            border-radius: 30px;
            font-size: 18px;
            cursor: pointer;
            z-index: 1000;
            box-shadow: 0 4px 10px rgba(0, 0, 0, 0.2);
            display: flex;
            align-items: center;
            gap: 10px;
            transition: all 0.3s ease;
        }
        
        .download-button {
            position: absolute;
            top: 20px;
            right: 230px;
            background: linear-gradient(135deg, #2ecc71, #27ae60);
            color: white;
            border: none;
            padding: 12px 25px;
            border-radius: 30px;
            font-size: 18px;
            cursor: pointer;
            z-index: 1000;
            box-shadow: 0 4px 10px rgba(0, 0, 0, 0.2);
            display: flex;
            align-items: center;
            gap: 10px;
            transition: all 0.3s ease;
        }
        
        .resolution-selector {
            position: absolute;
            top: 70px;
            right: 230px;
            display: flex;
            align-items: center;
            background: rgba(255, 255, 255, 0.9);
            padding: 8px 15px;
            border-radius: 30px;
            box-shadow: 0 4px 10px rgba(0, 0, 0, 0.1);
            z-index: 1000;
        }
        
        .copy-icon {
            width: 20px;
            height: 20px;
            background-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="white"><path d="M19,21H8V7H19M19,5H8A2,2 0 0,0 6,7V21A2,2 0 0,0 8,23H19A2,2 0 0,0 21,21V7A2,2 0 0,0 19,5M16,1H4A2,2 0 0,0 2,3V17H4V3H16V1Z" /></svg>');
            background-size: contain;
            background-repeat: no-repeat;
        }
        
        .download-icon {
            width: 20px;
            height: 20px;
            background-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="white"><path d="M5,20H19V18H5M19,9H15V3H9V9H5L12,16L19,9Z" /></svg>');
            background-size: contain;
            background-repeat: no-repeat;
        }
        
        .resolution-label {
            margin-right: 10px;
            font-size: 14px;
            color: #555;
        }
        
        .resolution-btn {
            border: none;
            background: #f0f0f0;
            padding: 5px 10px;
            margin: 0 3px;
            border-radius: 15px;
            font-size: 14px;
            cursor: pointer;
            transition: all 0.2s ease;
            color: #555;
        }
        
        .resolution-btn:hover {
            background: #e0e0e0;
        }
        
        .resolution-btn.active {
            background: linear-gradient(135deg, #3498db, #2980b9);
            color: white;
        }
        
        /* 滑动内容区域 */
        .slide {
            width: 100%;
            height: 100%;
            background-color: transparent;
            padding: 60px;
            position: relative;
            overflow: hidden;
        }
        
        /* 标题样式 */
        .header {
            text-align: center;
            margin-bottom: 60px;
        }
        
        .header h1 {
            font-size: 48px;
            color: #2c3e50;
            margin-bottom: 15px;
            font-weight: 700;
        }
        
        .header .subtitle {
            font-size: 24px;
            color: #7f8c8d;
            font-weight: 400;
        }
        
        /* 架构图内容区域 */
        .architecture {
            width: 100%;
            height: 670px;
            position: relative;
        }
        
        /* 平台组件样式 */
        .component {
            position: absolute;
            border-radius: 12px;
            box-shadow: 0 6px 20px rgba(0, 0, 0, 0.1);
            overflow: hidden;
            background-color: white;
            transition: all 0.3s ease;
        }
        
        .component:hover {
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
            transform: translateY(-2px);
        }
        
        /* 云平台 */
        .cloud-platform {
            width: 1400px;
            height: 160px;
            top: 0;
            left: 50%;
            transform: translateX(-50%);
            z-index: 10;
        }
        
        .cloud-header {
            height: 50px;
            background: linear-gradient(135deg, #2a86db, #1c6bbd);
            display: flex;
            align-items: center;
            padding: 0 30px;
        }
        
        .cloud-header .icon {
            width: 26px;
            height: 26px;
            background-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="white"><path d="M19.35,10.03C18.67,6.59 15.64,4 12,4C9.11,4 6.6,5.64 5.35,8.03C2.34,8.36 0,10.9 0,14A6,6 0 0,0 6,20H19A5,5 0 0,0 24,15C24,12.36 21.95,10.22 19.35,10.03Z" /></svg>');
            background-size: contain;
            background-repeat: no-repeat;
            margin-right: 15px;
        }
        
        .cloud-header .title {
            color: white;
            font-size: 22px;
            font-weight: 600;
            flex: 1;
        }
        
        .cloud-content {
            display: flex;
            padding: 20px;
            justify-content: space-between;
        }
        
        .cloud-module {
            padding: 15px;
            text-align: center;
            background-color: #f7f9fc;
            border: 1px solid #e1e8f0;
            border-radius: 6px;
            width: calc(16.66% - 10px);
            color: #2c5282;
            font-size: 15px;
            position: relative;
        }
        
        .cloud-module::before {
            content: '';
            position: absolute;
            top: -5px;
            left: 50%;
            transform: translateX(-50%);
            width: 30px;
            height: 5px;
            background-color: #2a86db;
            border-radius: 2px;
        }
        
        /* 手机端 */
        .mobile-platform {
            width: 500px;
            height: 340px;
            top: 230px;
            left: 50%;
            transform: translateX(-50%);
            z-index: 20;
        }
        
        .mobile-header {
            height: 50px;
            background: linear-gradient(135deg, #34c759, #2ba149);
            display: flex;
            align-items: center;
            padding: 0 30px;
        }
        
        .mobile-header .icon {
            width: 24px;
            height: 24px;
            background-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="white"><path d="M17,19H7V5H17M17,1H7C5.89,1 5,1.89 5,3V21A2,2 0 0,0 7,23H17A2,2 0 0,0 19,21V3C19,1.89 18.1,1 17,1Z" /></svg>');
            background-size: contain;
            background-repeat: no-repeat;
            margin-right: 15px;
        }
        
        .mobile-header .title {
            color: white;
            font-size: 22px;
            font-weight: 600;
            flex: 1;
        }
        
        .mobile-content {
            display: grid;
            grid-template-columns: 1fr 1fr;
            grid-gap: 15px;
            padding: 20px;
        }
        
        .mobile-module {
            padding: 15px;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            text-align: center;
            background-color: #f5faf5;
            border: 1px solid #e1f0e1;
            border-radius: 6px;
            height: 110px;
            color: #2a653a;
            font-size: 15px;
            position: relative;
        }
        
        .mobile-module::before {
            content: '';
            position: absolute;
            top: -5px;
            left: 50%;
            transform: translateX(-50%);
            width: 30px;
            height: 5px;
            background-color: #34c759;
            border-radius: 2px;
        }
        
        /* 车端 */
        .car-platform {
            width: 1400px;
            height: 160px;
            bottom: 0;
            left: 50%;
            transform: translateX(-50%);
            z-index: 10;
        }
        
        .car-header {
            height: 50px;
            background: linear-gradient(135deg, #ff3b30, #e02e24);
            display: flex;
            align-items: center;
            padding: 0 30px;
        }
        
        .car-header .icon {
            width: 26px;
            height: 26px;
            background-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="white"><path d="M18.92,6.01C18.72,5.42 18.16,5 17.5,5h-11c-0.66,0 -1.21,0.42 -1.42,1.01L3,12v8c0,0.55 0.45,1 1,1h1c0.55,0 1,-0.45 1,-1v-1h12v1c0,0.55 0.45,1 1,1h1c0.55,0 1,-0.45 1,-1v-8l-2.08,-5.99zM6.5,16c-0.83,0 -1.5,-0.67 -1.5,-1.5S5.67,13 6.5,13s1.5,0.67 1.5,1.5S7.33,16 6.5,16zM17.5,16c-0.83,0 -1.5,-0.67 -1.5,-1.5s0.67,-1.5 1.5,-1.5 1.5,0.67 1.5,1.5 -0.67,1.5 -1.5,1.5zM5,11l1.5,-4.5h11L19,11L5,11z" /></svg>');
            background-size: contain;
            background-repeat: no-repeat;
            margin-right: 15px;
        }
        
        .car-header .title {
            color: white;
            font-size: 22px;
            font-weight: 600;
            flex: 1;
        }
        
        .car-content {
            display: flex;
            padding: 20px;
            justify-content: space-between;
        }
        
        .car-module {
            padding: 15px;
            text-align: center;
            background-color: #fdf5f5;
            border: 1px solid #f0e1e1;
            border-radius: 6px;
            width: calc(16.66% - 10px);
            color: #823c3c;
            font-size: 15px;
            position: relative;
        }
        
        .car-module::before {
            content: '';
            position: absolute;
            top: -5px;
            left: 50%;
            transform: translateX(-50%);
            width: 30px;
            height: 5px;
            background-color: #ff3b30;
            border-radius: 2px;
        }
        
        /* 连接标签 */
        .connection-label {
            position: absolute;
            padding: 8px 15px;
            background-color: white;
            border-radius: 20px;
            font-size: 14px;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
            z-index: 30;
            pointer-events: none;
            white-space: nowrap;
        }
        
        .cloud-to-mobile {
            top: 195px;
            left: 750px;
            color: #2a86db;
            border: 1px solid rgba(42, 134, 219, 0.3);
        }
        
        .mobile-to-cloud {
            top: 195px;
            left: 1050px;
            color: #34c759;
            border: 1px solid rgba(52, 199, 89, 0.3);
        }
        
        .mobile-to-car {
            top: 585px;
            left: 750px;
            color: #34c759;
            border: 1px solid rgba(52, 199, 89, 0.3);
        }
        
        .car-to-mobile {
            top: 585px;
            left: 1050px;
            color: #ff3b30;
            border: 1px solid rgba(255, 59, 48, 0.3);
        }
        
        .cloud-to-car {
            top: 350px;
            left: 350px;
            color: #2a86db;
            border: 1px solid rgba(42, 134, 219, 0.3);
            transform: rotate(-30deg);
        }
        
        .car-to-cloud {
            top: 350px;
            left: 1450px;
            color: #ff3b30;
            border: 1px solid rgba(255, 59, 48, 0.3);
            transform: rotate(30deg);
        }
        
        /* 距离标签 */
        .distance-tag {
            position: absolute;
            width: 36px;
            height: 36px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-weight: bold;
            font-size: 16px;
            z-index: 40;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
        }
        
        .near-tag {
            background-color: #34c759;
            bottom: 400px;
            left: 560px;
        }
        
        .far-tag {
            background-color: #2a86db;
            bottom: 450px;
            left: 410px;
        }
        
        /* 底部说明 */
        .footer {
            position: absolute;
            bottom: 25px;
            width: 100%;
            text-align: center;
            font-size: 16px;
            color: #95a5a6;
            left: 0;
        }
        
        .toast {
            position: fixed;
            top: 50px;
            left: 50%;
            transform: translateX(-50%);
            background-color: rgba(0, 0, 0, 0.8);
            color: white;
            padding: 12px 25px;
            border-radius: 30px;
            font-size: 16px;
            z-index: 2000;
            display: none;
            animation: fadeInOut 2s ease;
        }
        
        @keyframes fadeInOut {
            0% { opacity: 0; }
            20% { opacity: 1; }
            80% { opacity: 1; }
            100% { opacity: 0; }
        }
    </style>
</head>
<body>
    <div class="ppt-container" id="pptContainer">
        <div class="ppt-label">PPT尺寸(1920×1080) - 透明背景</div>
        <button class="copy-button" id="copyButton">
            <span class="copy-icon"></span>复制为PPT图片
        </button>
        <button class="download-button" id="downloadButton">
            <span class="download-icon"></span>下载透明PNG
        </button>
        
        <div class="resolution-selector" id="resolutionSelector">
            <span class="resolution-label">清晰度:</span>
            <button class="resolution-btn" data-scale="1">标准</button>
            <button class="resolution-btn active" data-scale="2">高清</button>
            <button class="resolution-btn" data-scale="3">超清</button>
        </div>
        
        <div class="slide" id="slide">
            <div class="header">
                <h1>数字钥匙三端架构图</h1>
                <div class="subtitle">车辆数字钥匙系统技术架构与通信流程</div>
            </div>
            
            <div class="architecture" id="architecture">
                <!-- 云平台 -->
                <div class="component cloud-platform">
                    <div class="cloud-header">
                        <div class="icon"></div>
                        <div class="title">钥匙云平台</div>
                    </div>
                    <div class="cloud-content">
                        <div class="cloud-module">钥匙生命周期管理</div>
                        <div class="cloud-module">安全认证中心</div>
                        <div class="cloud-module">密钥管理系统</div>
                        <div class="cloud-module">统一接口服务</div>
                        <div class="cloud-module">安全通信通道</div>
                        <div class="cloud-module">时间同步服务</div>
                    </div>
                </div>
                
                <!-- 手机端 -->
                <div class="component mobile-platform">
                    <div class="mobile-header">
                        <div class="icon"></div>
                        <div class="title">手机端</div>
                    </div>
                    <div class="mobile-content">
                        <div class="mobile-module">钥匙与车辆管理</div>
                        <div class="mobile-module">蓝牙通信模块</div>
                        <div class="mobile-module">安全存储模块</div>
                        <div class="mobile-module">暗号交换模块</div>
                    </div>
                </div>
                
                <!-- 车端 -->
                <div class="component car-platform">
                    <div class="car-header">
                        <div class="icon"></div>
                        <div class="title">车端</div>
                    </div>
                    <div class="car-content">
                        <div class="car-module">钥匙验证模块</div>
                        <div class="car-module">蓝牙通信模块</div>
                        <div class="car-module">指令执行模块</div>
                        <div class="car-module">暗号交换模块</div>
                        <div class="car-module">安全存储模块</div>
                        <div class="car-module">远程通信模块</div>
                    </div>
                </div>
                
                <!-- 连接标签 -->
                <div class="connection-label cloud-to-mobile">钥匙授权/认证</div>
                <div class="connection-label mobile-to-cloud">状态同步/更新</div>
                <div class="connection-label mobile-to-car">蓝牙控制指令</div>
                <div class="connection-label car-to-mobile">状态反馈/验证</div>
                <div class="connection-label cloud-to-car">远程控制/配置更新</div>
                <div class="connection-label car-to-cloud">状态上报/安全校验</div>
                
                <!-- 距离标签 -->
                <div class="distance-tag near-tag">近</div>
                <div class="distance-tag far-tag">远</div>
            </div>
            
            <div class="footer">
                数字钥匙三端架构 - 安全高效的车辆接入控制系统
            </div>
        </div>
    </div>
    
    <div class="toast" id="toast">操作成功！</div>
    
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const copyButton = document.getElementById('copyButton');
            const downloadButton = document.getElementById('downloadButton');
            const toast = document.getElementById('toast');
            const slide = document.getElementById('slide');
            const resolutionBtns = document.querySelectorAll('.resolution-btn');
            const architecture = document.getElementById('architecture');
            
            // 添加SVG连接线
            const svgNS = "http://www.w3.org/2000/svg";
            const svg = document.createElementNS(svgNS, "svg");
            svg.setAttribute("width", "100%");
            svg.setAttribute("height", "100%");
            svg.style.position = "absolute";
            svg.style.top = "0";
            svg.style.left = "0";
            svg.style.zIndex = "5";
            svg.style.pointerEvents = "none";
            
            // 添加箭头定义
            const defs = document.createElementNS(svgNS, "defs");
            
            // 蓝色箭头
            const blueArrow = document.createElementNS(svgNS, "marker");
            blueArrow.setAttribute("id", "blueArrow");
            blueArrow.setAttribute("viewBox", "0 0 10 10");
            blueArrow.setAttribute("refX", "5");
            blueArrow.setAttribute("refY", "5");
            blueArrow.setAttribute("markerWidth", "6");
            blueArrow.setAttribute("markerHeight", "6");
            blueArrow.setAttribute("orient", "auto");
            
            const blueArrowPath = document.createElementNS(svgNS, "path");
            blueArrowPath.setAttribute("d", "M 0 0 L 10 5 L 0 10 z");
            blueArrowPath.setAttribute("fill", "#2a86db");
            blueArrow.appendChild(blueArrowPath);
            defs.appendChild(blueArrow);
            
            // 绿色箭头
            const greenArrow = document.createElementNS(svgNS, "marker");
            greenArrow.setAttribute("id", "greenArrow");
            greenArrow.setAttribute("viewBox", "0 0 10 10");
            greenArrow.setAttribute("refX", "5");
            greenArrow.setAttribute("refY", "5");
            greenArrow.setAttribute("markerWidth", "6");
            greenArrow.setAttribute("markerHeight", "6");
            greenArrow.setAttribute("orient", "auto");
            
            const greenArrowPath = document.createElementNS(svgNS, "path");
            greenArrowPath.setAttribute("d", "M 0 0 L 10 5 L 0 10 z");
            greenArrowPath.setAttribute("fill", "#34c759");
            greenArrow.appendChild(greenArrowPath);
            defs.appendChild(greenArrow);
            
            // 红色箭头
            const redArrow = document.createElementNS(svgNS, "marker");
            redArrow.setAttribute("id", "redArrow");
            redArrow.setAttribute("viewBox", "0 0 10 10");
            redArrow.setAttribute("refX", "5");
            redArrow.setAttribute("refY", "5");
            redArrow.setAttribute("markerWidth", "6");
            redArrow.setAttribute("markerHeight", "6");
            redArrow.setAttribute("orient", "auto");
            
            const redArrowPath = document.createElementNS(svgNS, "path");
            redArrowPath.setAttribute("d", "M 0 0 L 10 5 L 0 10 z");
            redArrowPath.setAttribute("fill", "#ff3b30");
            redArrow.appendChild(redArrowPath);
            defs.appendChild(redArrow);
            
            svg.appendChild(defs);
            
            // 绘制云平台到手机端的连接线
            const cloudToMobile = document.createElementNS(svgNS, "path");
            cloudToMobile.setAttribute("d", "M 800 160 L 800 230");
            cloudToMobile.setAttribute("stroke", "#2a86db");
            cloudToMobile.setAttribute("stroke-width", "3");
            cloudToMobile.setAttribute("fill", "none");
            cloudToMobile.setAttribute("marker-end", "url(#blueArrow)");
            svg.appendChild(cloudToMobile);
            
            // 绘制手机端到云平台的连接线
            const mobileToCloud = document.createElementNS(svgNS, "path");
            mobileToCloud.setAttribute("d", "M 1100 230 L 1100 160");
            mobileToCloud.setAttribute("stroke", "#34c759");
            mobileToCloud.setAttribute("stroke-width", "3");
            mobileToCloud.setAttribute("fill", "none");
            mobileToCloud.setAttribute("marker-end", "url(#greenArrow)");
            svg.appendChild(mobileToCloud);
            
            // 绘制手机端到车端的连接线
            const mobileToCar = document.createElementNS(svgNS, "path");
            mobileToCar.setAttribute("d", "M 800 570 L 800 640");
            mobileToCar.setAttribute("stroke", "#34c759");
            mobileToCar.setAttribute("stroke-width", "3");
            mobileToCar.setAttribute("fill", "none");
            mobileToCar.setAttribute("marker-end", "url(#greenArrow)");
            svg.appendChild(mobileToCar);
            
            // 绘制车端到手机端的连接线
            const carToMobile = document.createElementNS(svgNS, "path");
            carToMobile.setAttribute("d", "M 1100 640 L 1100 570");
            carToMobile.setAttribute("stroke", "#ff3b30");
            carToMobile.setAttribute("stroke-width", "3");
            carToMobile.setAttribute("fill", "none");
            carToMobile.setAttribute("marker-end", "url(#redArrow)");
            svg.appendChild(carToMobile);
            
            // 绘制云平台到车端的连接线（虚线）
            const cloudToCar = document.createElementNS(svgNS, "path");
            cloudToCar.setAttribute("d", "M 550 160 C 400 300, 400 500, 550 640");
            cloudToCar.setAttribute("stroke", "#2a86db");
            cloudToCar.setAttribute("stroke-width", "3");
            cloudToCar.setAttribute("stroke-dasharray", "10, 7");
            cloudToCar.setAttribute("fill", "none");
            cloudToCar.setAttribute("marker-end", "url(#blueArrow)");
            svg.appendChild(cloudToCar);
            
            // 绘制车端到云平台的连接线（虚线）
            const carToCloud = document.createElementNS(svgNS, "path");
            carToCloud.setAttribute("d", "M 1350 640 C 1500 500, 1500 300, 1350 160");
            carToCloud.setAttribute("stroke", "#ff3b30");
            carToCloud.setAttribute("stroke-width", "3");
            carToCloud.setAttribute("stroke-dasharray", "10, 7");
            carToCloud.setAttribute("fill", "none");
            carToCloud.setAttribute("marker-end", "url(#redArrow)");
            svg.appendChild(carToCloud);
            
            architecture.appendChild(svg);
            
            // 默认清晰度为2x (高清)
            let currentScale = 2;
            
            // 清晰度按钮点击事件
            resolutionBtns.forEach(btn => {
                btn.addEventListener('click', function() {
                    // 移除所有按钮的active类
                    resolutionBtns.forEach(b => b.classList.remove('active'));
                    // 给当前按钮添加active类
                    this.classList.add('active');
                    // 更新当前清晰度
                    currentScale = parseFloat(this.getAttribute('data-scale'));
                    
                    // 显示提示
                    showToast(`清晰度已设置为${this.textContent}模式`);
                });
            });
            
            // 显示提示消息
            function showToast(message) {
                toast.textContent = message;
                toast.style.display = 'block';
                setTimeout(function() {
                    toast.style.display = 'none';
                }, 2000);
            }
            
            // 生成透明背景图片
            function generateImage() {
                return html2canvas(slide, {
                    scale: currentScale, // 使用用户选择的清晰度
                    useCORS: true,
                    allowTaint: true,
                    backgroundColor: null // 关键：设置为null以支持透明背景
                });
            }
            
            // 复制按钮功能
            copyButton.addEventListener('click', function() {
                showToast("正在处理图片，请稍候...");
                
                generateImage().then(function(canvas) {
                    try {
                        // 使用现代剪贴板API
                        canvas.toBlob(function(blob) {
                            try {
                                const item = new ClipboardItem({ 'image/png': blob });
                                navigator.clipboard.write([item]).then(function() {
                                    showToast("已复制到剪贴板！现在可以粘贴到PPT中");
                                }).catch(function(err) {
                                    showToast("自动复制失败，请使用下载按钮获取图片");
                                    console.error("剪贴板API错误:", err);
                                });
                            } catch (e) {
                                showToast("您的浏览器不支持复制图片，请使用下载按钮");
                                console.error("ClipboardItem错误:", e);
                            }
                        });
                    } catch (e) {
                        showToast("复制功能不可用，请使用下载图片功能");
                        console.error("toBlob错误:", e);
                    }
                }).catch(function(err) {
                    showToast("生成图片失败，请尝试下载或截图");
                    console.error("html2canvas错误:", err);
                });
            });
            
            // 下载按钮功能
            downloadButton.addEventListener('click', function () {
                showToast(`正在生成${currentScale}x清晰度透明PNG图片，请稍候...`);

                generateImage().then(function (canvas) {
                    // 将Canvas转换为PNG并下载
                    const imageUrl = canvas.toDataURL('image/png');
                    const link = document.createElement('a');

                    // 首先从URL路径获取文件名
                    let fileName = "";
                    const pathParts = window.location.pathname.split('/');
                    const htmlFileName = pathParts[pathParts.length - 1];
                    // 移除.html后缀并进行URL解码以支持中文文件名
                    fileName = decodeURIComponent(htmlFileName.replace('.html', ''));

                    // 如果从URL获取的文件名为空，尝试从标题获取
                    if (!fileName) {
                        const titleElement = document.querySelector('.header h1');
                        if (titleElement && titleElement.textContent) {
                            fileName = titleElement.textContent.trim();
                        }
                    }

                    // 确保文件名不为空
                    if (!fileName) {
                        fileName = "数字钥匙三端架构图";
                    }

                    link.download = `${fileName}-透明-${currentScale}x清晰度.png`;
                    link.href = imageUrl;
                    link.click();

                    showToast(`${currentScale}x清晰度透明背景PNG已下载`);
                }).catch(function (err) {
                    showToast("生成图片失败，请尝试截图");
                    console.error("Error:", err);
                });
            });
        });
    </script>
</body>
</html> 
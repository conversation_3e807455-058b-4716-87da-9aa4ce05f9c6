import request from '@/utils/request'

// 查询车辆数字钥匙分享详情列表
export function listDigitalKeyShareDetail(query) {
  return request({
    url: '/dk/digitalKeyShareDetail/list',
    method: 'get',
    params: query
  })
}

// 查询车辆数字钥匙分享详情详细
export function getDigitalKeyShareDetail(detailId) {
  return request({
    url: '/dk/digitalKeyShareDetail/' + detailId,
    method: 'get'
  })
}

// 新增车辆数字钥匙分享详情
export function addDigitalKeyShareDetail(data) {
  return request({
    url: '/dk/digitalKeyShareDetail',
    method: 'post',
    data: data
  })
}

// 修改车辆数字钥匙分享详情
export function updateDigitalKeyShareDetail(data) {
  return request({
    url: '/dk/digitalKeyShareDetail',
    method: 'put',
    data: data
  })
}

// 删除车辆数字钥匙分享详情
export function delDigitalKeyShareDetail(detailId) {
  return request({
    url: '/dk/digitalKeyShareDetail/' + detailId,
    method: 'delete'
  })
}

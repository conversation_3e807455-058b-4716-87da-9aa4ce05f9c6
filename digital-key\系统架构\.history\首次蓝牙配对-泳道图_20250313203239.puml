@startuml
skinparam monochrome true

participant "APP" as Mobile
participant "车端（BLE & TBOX）" as CarBT
participant "钥匙云平台" as Cloud
participant "TSP/OEM平台" as ExternalSystem

== 蓝牙配对流程 ==

Mobile -> Mobile : 1. 在APP中选择添加数字钥匙
Mobile -> Mobile : 2. 扫描车辆二维码或输入VIN码识别车辆
Mobile -> Cloud : 3. 发送车辆VIN码，请求获取数字钥匙
Cloud -> Cloud : 4. 根据VIN码生成虚拟密钥（VIRKEY）
Cloud -> Mobile : 5. 下发虚拟密钥到手机
Mobile -> Mobile : 6. 保存虚拟密钥到安全存储区


Cloud -> ExternalSystem : 7. 通过统一接口适配层查询VIN码对应的TBOX信息
ExternalSystem -> Cloud : 8. 返回TBOX信息
Cloud -> CarBT : 9. 通过4G连接到车辆TBOX并生成根密钥下发到汽车

CarBT -> CarBT : 10. 存储根密钥
CarBT -> Mobile : 11. 发送蓝牙广播（包含MAC、异化处理后的VIN码等信息）
Mobile -> CarBT : 12. 扫描并连接到汽车蓝牙模块
Mobile -> CarBT : 13. 发送蓝牙配对请求
CarBT -> Mobile : 14. 发起蓝牙安全请求
CarBT -> CarBT : 15. 生成配对码
Mobile -> Mobile : 16. 生成或输入配对码
Mobile -> CarBT : 17. 发送配对码进行校验
CarBT -> Mobile : 18. 返回配对码校验结果
CarBT -> CarBT : 19. 配对成功，绑定设备信息
Mobile -> Mobile : 20. 配对成功，绑定设备信息
Mobile -> CarBT : 21. 发送加密的虚拟密钥和VIN码
CarBT -> CarBT : 22. 验证虚拟密钥和VIN码匹配
CarBT -> Mobile : 23. 返回验证结果
Mobile -> Cloud : 24. 上报配对结果
Cloud -> Cloud : 25. 记录钥匙绑定状态
@enduml
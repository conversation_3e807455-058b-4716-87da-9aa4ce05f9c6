10:05:52.875 [main] ERROR c.r.f.tcp.TcpServer - [start,52] - TCP服务器启动失败: Address already in use: NET_Bind
java.net.BindException: Address already in use: NET_Bind
	at java.base/java.net.PlainSocketImpl.bind0(Native Method)
	at java.base/java.net.PlainSocketImpl.socketBind(PlainSocketImpl.java:132)
	at java.base/java.net.AbstractPlainSocketImpl.bind(AbstractPlainSocketImpl.java:436)
	at java.base/java.net.ServerSocket.bind(ServerSocket.java:381)
	at java.base/java.net.ServerSocket.<init>(ServerSocket.java:243)
	at java.base/java.net.ServerSocket.<init>(ServerSocket.java:135)
	at com.ruoyi.framework.tcp.TcpServer.start(TcpServer.java:42)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:566)
	at org.springframework.beans.factory.annotation.InitDestroyAnnotationBeanPostProcessor$LifecycleElement.invoke(InitDestroyAnnotationBeanPostProcessor.java:389)
	at org.springframework.beans.factory.annotation.InitDestroyAnnotationBeanPostProcessor$LifecycleMetadata.invokeInitMethods(InitDestroyAnnotationBeanPostProcessor.java:333)
	at org.springframework.beans.factory.annotation.InitDestroyAnnotationBeanPostProcessor.postProcessBeforeInitialization(InitDestroyAnnotationBeanPostProcessor.java:157)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.applyBeanPostProcessorsBeforeInitialization(AbstractAutowireCapableBeanFactory.java:440)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.initializeBean(AbstractAutowireCapableBeanFactory.java:1796)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:620)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:542)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:336)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:334)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:209)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.preInstantiateSingletons(DefaultListableBeanFactory.java:955)
	at org.springframework.context.support.AbstractApplicationContext.finishBeanFactoryInitialization(AbstractApplicationContext.java:932)
	at org.springframework.context.support.AbstractApplicationContext.__refresh(AbstractApplicationContext.java:591)
	at org.springframework.context.support.AbstractApplicationContext.jrLockAndRefresh(AbstractApplicationContext.java:41002)
	at org.springframework.context.support.AbstractApplicationContext.refresh(AbstractApplicationContext.java:42008)
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.refresh(ServletWebServerApplicationContext.java:147)
	at org.springframework.boot.SpringApplication.refresh(SpringApplication.java:732)
	at org.springframework.boot.SpringApplication.refreshContext(SpringApplication.java:409)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:308)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1300)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1289)
	at com.ruoyi.system.RuoYiSystemApplication.main(RuoYiSystemApplication.java:33)
10:05:53.878 [main] ERROR o.s.b.d.LoggingFailureAnalysisReporter - [report,40] - 

***************************
APPLICATION FAILED TO START
***************************

Description:

Web server failed to start. Port 9201 was already in use.

Action:

Identify and stop the process that's listening on port 9201 or configure this application to listen on another port.

10:11:13.139 [main] ERROR c.r.f.tcp.TcpServer - [start,52] - TCP服务器启动失败: Address already in use: NET_Bind
java.net.BindException: Address already in use: NET_Bind
	at java.base/java.net.PlainSocketImpl.bind0(Native Method)
	at java.base/java.net.PlainSocketImpl.socketBind(PlainSocketImpl.java:132)
	at java.base/java.net.AbstractPlainSocketImpl.bind(AbstractPlainSocketImpl.java:436)
	at java.base/java.net.ServerSocket.bind(ServerSocket.java:381)
	at java.base/java.net.ServerSocket.<init>(ServerSocket.java:243)
	at java.base/java.net.ServerSocket.<init>(ServerSocket.java:135)
	at com.ruoyi.framework.tcp.TcpServer.start(TcpServer.java:42)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:566)
	at org.springframework.beans.factory.annotation.InitDestroyAnnotationBeanPostProcessor$LifecycleElement.invoke(InitDestroyAnnotationBeanPostProcessor.java:389)
	at org.springframework.beans.factory.annotation.InitDestroyAnnotationBeanPostProcessor$LifecycleMetadata.invokeInitMethods(InitDestroyAnnotationBeanPostProcessor.java:333)
	at org.springframework.beans.factory.annotation.InitDestroyAnnotationBeanPostProcessor.postProcessBeforeInitialization(InitDestroyAnnotationBeanPostProcessor.java:157)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.applyBeanPostProcessorsBeforeInitialization(AbstractAutowireCapableBeanFactory.java:440)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.initializeBean(AbstractAutowireCapableBeanFactory.java:1796)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:620)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:542)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:336)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:334)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:209)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.preInstantiateSingletons(DefaultListableBeanFactory.java:955)
	at org.springframework.context.support.AbstractApplicationContext.finishBeanFactoryInitialization(AbstractApplicationContext.java:932)
	at org.springframework.context.support.AbstractApplicationContext.__refresh(AbstractApplicationContext.java:591)
	at org.springframework.context.support.AbstractApplicationContext.jrLockAndRefresh(AbstractApplicationContext.java:41002)
	at org.springframework.context.support.AbstractApplicationContext.refresh(AbstractApplicationContext.java:42008)
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.refresh(ServletWebServerApplicationContext.java:147)
	at org.springframework.boot.SpringApplication.refresh(SpringApplication.java:732)
	at org.springframework.boot.SpringApplication.refreshContext(SpringApplication.java:409)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:308)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1300)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1289)
	at com.ruoyi.system.RuoYiSystemApplication.main(RuoYiSystemApplication.java:33)
10:11:14.215 [main] ERROR o.s.b.d.LoggingFailureAnalysisReporter - [report,40] - 

***************************
APPLICATION FAILED TO START
***************************

Description:

Web server failed to start. Port 9201 was already in use.

Action:

Identify and stop the process that's listening on port 9201 or configure this application to listen on another port.

10:38:19.492 [rebel-change-detector-thread] ERROR c.r.f.tcp.TcpServer - [start,52] - TCP服务器启动失败: Address already in use: NET_Bind
java.net.BindException: Address already in use: NET_Bind
	at java.base/java.net.PlainSocketImpl.bind0(Native Method)
	at java.base/java.net.PlainSocketImpl.socketBind(PlainSocketImpl.java:132)
	at java.base/java.net.AbstractPlainSocketImpl.bind(AbstractPlainSocketImpl.java:436)
	at java.base/java.net.ServerSocket.bind(ServerSocket.java:381)
	at java.base/java.net.ServerSocket.<init>(ServerSocket.java:243)
	at java.base/java.net.ServerSocket.<init>(ServerSocket.java:135)
	at com.ruoyi.framework.tcp.TcpServer.start(TcpServer.java:42)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:566)
	at org.springframework.beans.factory.annotation.InitDestroyAnnotationBeanPostProcessor$LifecycleElement.invoke(InitDestroyAnnotationBeanPostProcessor.java:389)
	at org.springframework.beans.factory.annotation.InitDestroyAnnotationBeanPostProcessor$LifecycleMetadata.invokeInitMethods(InitDestroyAnnotationBeanPostProcessor.java:333)
	at org.springframework.beans.factory.annotation.InitDestroyAnnotationBeanPostProcessor.postProcessBeforeInitialization(InitDestroyAnnotationBeanPostProcessor.java:157)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.applyBeanPostProcessorsBeforeInitialization(AbstractAutowireCapableBeanFactory.java:440)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.initializeBean(AbstractAutowireCapableBeanFactory.java:1796)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:620)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:542)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:336)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:334)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:209)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.preInstantiateSingletons(DefaultListableBeanFactory.java:955)
	at org.springframework.beans.JrBeanReloader.reinstantiateSingletons(JrBeanReloader.java:1313)
	at org.springframework.beans.JrBeanReloader.reloadBeanDefinitionsInternal(JrBeanReloader.java:1167)
	at org.springframework.beans.JrBeanReloader.reloadBeanDefinitions(JrBeanReloader.java:964)
	at org.springframework.beans.JrBeanReloader.onClassEvent(JrBeanReloader.java:542)
	at org.springframework.beans.JrBeanReloader.access$000(JrBeanReloader.java:97)
	at org.springframework.beans.JrBeanReloader$1.onClassEvent(JrBeanReloader.java:173)
	at org.zeroturnaround.javarebel.ClassEventListener.onClassEvent(SourceFile:67)
	at org.zeroturnaround.javarebel.integration.util.ClassEventListenerUtil$BoundClassEventListener.onClassEvent(SourceFile:83)
	at com.zeroturnaround.javarebel.bb.b(SourceFile:175)
	at com.zeroturnaround.javarebel.bb.a(SourceFile:146)
	at com.zeroturnaround.javarebel.SDKReloaderImpl.a(SourceFile:94)
	at com.zeroturnaround.javarebel.SDKReloaderImpl.c(SourceFile:781)
	at com.zeroturnaround.javarebel.SDKReloaderImpl.c(SourceFile:772)
	at com.zeroturnaround.javarebel.SDKReloaderImpl.fireReloadListeners(SourceFile:817)
	at com.zeroturnaround.reload.ag.a(SourceFile:606)
	at com.zeroturnaround.reload.b.l(SourceFile:439)
	at com.zeroturnaround.reload.b.k(SourceFile:275)
	at com.zeroturnaround.reload.b.j(SourceFile:56)
	at com.zeroturnaround.reload.c.run(SourceFile:109)
10:38:19.583 [rebel-change-detector-thread] ERROR c.r.f.tcp.TcpServer - [start,52] - TCP服务器启动失败: Address already in use: NET_Bind
java.net.BindException: Address already in use: NET_Bind
	at java.base/java.net.PlainSocketImpl.bind0(Native Method)
	at java.base/java.net.PlainSocketImpl.socketBind(PlainSocketImpl.java:132)
	at java.base/java.net.AbstractPlainSocketImpl.bind(AbstractPlainSocketImpl.java:436)
	at java.base/java.net.ServerSocket.bind(ServerSocket.java:381)
	at java.base/java.net.ServerSocket.<init>(ServerSocket.java:243)
	at java.base/java.net.ServerSocket.<init>(ServerSocket.java:135)
	at com.ruoyi.framework.tcp.TcpServer.start(TcpServer.java:42)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:566)
	at org.springframework.beans.factory.annotation.InitDestroyAnnotationBeanPostProcessor$LifecycleElement.invoke(InitDestroyAnnotationBeanPostProcessor.java:389)
	at org.springframework.beans.factory.annotation.InitDestroyAnnotationBeanPostProcessor$LifecycleMetadata.invokeInitMethods(InitDestroyAnnotationBeanPostProcessor.java:333)
	at org.springframework.beans.factory.annotation.InitDestroyAnnotationBeanPostProcessor.postProcessBeforeInitialization(InitDestroyAnnotationBeanPostProcessor.java:157)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.applyBeanPostProcessorsBeforeInitialization(AbstractAutowireCapableBeanFactory.java:440)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.initializeBean(AbstractAutowireCapableBeanFactory.java:1796)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.configureBean(AbstractAutowireCapableBeanFactory.java:365)
	at org.springframework.beans.JrBeanReloader.reprocessBean(JrBeanReloader.java:1875)
	at org.springframework.beans.JrBeanReloader.reprocessBean(JrBeanReloader.java:1850)
	at org.springframework.beans.JrBeanReloader.doReprocessSingletons(JrBeanReloader.java:1829)
	at org.springframework.beans.JrBeanReloader.reprocess(JrBeanReloader.java:1812)
	at org.springframework.beans.JrBeanReloader$5.reprocess(JrBeanReloader.java:1198)
	at org.springframework.beans.JrBeanReloader.processBeanClass(JrBeanReloader.java:1697)
	at org.springframework.beans.JrBeanReloader.processQueue(JrBeanReloader.java:1647)
	at org.springframework.beans.JrBeanReloader.reloadBeanDefinitionsInternal(JrBeanReloader.java:1194)
	at org.springframework.beans.JrBeanReloader.reloadBeanDefinitions(JrBeanReloader.java:964)
	at org.springframework.beans.JrBeanReloader.onClassEvent(JrBeanReloader.java:542)
	at org.springframework.beans.JrBeanReloader.access$000(JrBeanReloader.java:97)
	at org.springframework.beans.JrBeanReloader$1.onClassEvent(JrBeanReloader.java:173)
	at org.zeroturnaround.javarebel.ClassEventListener.onClassEvent(SourceFile:67)
	at org.zeroturnaround.javarebel.integration.util.ClassEventListenerUtil$BoundClassEventListener.onClassEvent(SourceFile:83)
	at com.zeroturnaround.javarebel.bb.b(SourceFile:175)
	at com.zeroturnaround.javarebel.bb.a(SourceFile:146)
	at com.zeroturnaround.javarebel.SDKReloaderImpl.a(SourceFile:94)
	at com.zeroturnaround.javarebel.SDKReloaderImpl.c(SourceFile:781)
	at com.zeroturnaround.javarebel.SDKReloaderImpl.c(SourceFile:772)
	at com.zeroturnaround.javarebel.SDKReloaderImpl.fireReloadListeners(SourceFile:817)
	at com.zeroturnaround.reload.ag.a(SourceFile:606)
	at com.zeroturnaround.reload.b.l(SourceFile:439)
	at com.zeroturnaround.reload.b.k(SourceFile:275)
	at com.zeroturnaround.reload.b.j(SourceFile:56)
	at com.zeroturnaround.reload.c.run(SourceFile:109)
11:12:11.209 [rebel-change-detector-thread] ERROR c.r.f.tcp.TcpServer - [start,52] - TCP服务器启动失败: Address already in use: NET_Bind
java.net.BindException: Address already in use: NET_Bind
	at java.base/java.net.PlainSocketImpl.bind0(Native Method)
	at java.base/java.net.PlainSocketImpl.socketBind(PlainSocketImpl.java:132)
	at java.base/java.net.AbstractPlainSocketImpl.bind(AbstractPlainSocketImpl.java:436)
	at java.base/java.net.ServerSocket.bind(ServerSocket.java:381)
	at java.base/java.net.ServerSocket.<init>(ServerSocket.java:243)
	at java.base/java.net.ServerSocket.<init>(ServerSocket.java:135)
	at com.ruoyi.framework.tcp.TcpServer.start(TcpServer.java:42)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:566)
	at org.springframework.beans.factory.annotation.InitDestroyAnnotationBeanPostProcessor$LifecycleElement.invoke(InitDestroyAnnotationBeanPostProcessor.java:389)
	at org.springframework.beans.factory.annotation.InitDestroyAnnotationBeanPostProcessor$LifecycleMetadata.invokeInitMethods(InitDestroyAnnotationBeanPostProcessor.java:333)
	at org.springframework.beans.factory.annotation.InitDestroyAnnotationBeanPostProcessor.postProcessBeforeInitialization(InitDestroyAnnotationBeanPostProcessor.java:157)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.applyBeanPostProcessorsBeforeInitialization(AbstractAutowireCapableBeanFactory.java:440)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.initializeBean(AbstractAutowireCapableBeanFactory.java:1796)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:620)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:542)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:336)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:334)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:209)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.preInstantiateSingletons(DefaultListableBeanFactory.java:955)
	at org.springframework.beans.JrBeanReloader.reinstantiateSingletons(JrBeanReloader.java:1313)
	at org.springframework.beans.JrBeanReloader.reloadBeanDefinitionsInternal(JrBeanReloader.java:1167)
	at org.springframework.beans.JrBeanReloader.reloadBeanDefinitions(JrBeanReloader.java:964)
	at org.springframework.beans.JrBeanReloader.onClassEvent(JrBeanReloader.java:542)
	at org.springframework.beans.JrBeanReloader.access$000(JrBeanReloader.java:97)
	at org.springframework.beans.JrBeanReloader$1.onClassEvent(JrBeanReloader.java:173)
	at org.zeroturnaround.javarebel.ClassEventListener.onClassEvent(SourceFile:67)
	at org.zeroturnaround.javarebel.integration.util.ClassEventListenerUtil$BoundClassEventListener.onClassEvent(SourceFile:83)
	at com.zeroturnaround.javarebel.bb.b(SourceFile:175)
	at com.zeroturnaround.javarebel.bb.a(SourceFile:146)
	at com.zeroturnaround.javarebel.SDKReloaderImpl.a(SourceFile:94)
	at com.zeroturnaround.javarebel.SDKReloaderImpl.c(SourceFile:781)
	at com.zeroturnaround.javarebel.SDKReloaderImpl.c(SourceFile:772)
	at com.zeroturnaround.javarebel.SDKReloaderImpl.fireReloadListeners(SourceFile:817)
	at com.zeroturnaround.reload.ag.a(SourceFile:606)
	at com.zeroturnaround.reload.b.l(SourceFile:439)
	at com.zeroturnaround.reload.b.k(SourceFile:275)
	at com.zeroturnaround.reload.b.j(SourceFile:56)
	at com.zeroturnaround.reload.c.run(SourceFile:109)
11:12:11.322 [rebel-change-detector-thread] ERROR c.r.f.tcp.TcpServer - [start,52] - TCP服务器启动失败: Address already in use: NET_Bind
java.net.BindException: Address already in use: NET_Bind
	at java.base/java.net.PlainSocketImpl.bind0(Native Method)
	at java.base/java.net.PlainSocketImpl.socketBind(PlainSocketImpl.java:132)
	at java.base/java.net.AbstractPlainSocketImpl.bind(AbstractPlainSocketImpl.java:436)
	at java.base/java.net.ServerSocket.bind(ServerSocket.java:381)
	at java.base/java.net.ServerSocket.<init>(ServerSocket.java:243)
	at java.base/java.net.ServerSocket.<init>(ServerSocket.java:135)
	at com.ruoyi.framework.tcp.TcpServer.start(TcpServer.java:42)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:566)
	at org.springframework.beans.factory.annotation.InitDestroyAnnotationBeanPostProcessor$LifecycleElement.invoke(InitDestroyAnnotationBeanPostProcessor.java:389)
	at org.springframework.beans.factory.annotation.InitDestroyAnnotationBeanPostProcessor$LifecycleMetadata.invokeInitMethods(InitDestroyAnnotationBeanPostProcessor.java:333)
	at org.springframework.beans.factory.annotation.InitDestroyAnnotationBeanPostProcessor.postProcessBeforeInitialization(InitDestroyAnnotationBeanPostProcessor.java:157)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.applyBeanPostProcessorsBeforeInitialization(AbstractAutowireCapableBeanFactory.java:440)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.initializeBean(AbstractAutowireCapableBeanFactory.java:1796)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.configureBean(AbstractAutowireCapableBeanFactory.java:365)
	at org.springframework.beans.JrBeanReloader.reprocessBean(JrBeanReloader.java:1875)
	at org.springframework.beans.JrBeanReloader.reprocessBean(JrBeanReloader.java:1850)
	at org.springframework.beans.JrBeanReloader.doReprocessSingletons(JrBeanReloader.java:1829)
	at org.springframework.beans.JrBeanReloader.reprocess(JrBeanReloader.java:1812)
	at org.springframework.beans.JrBeanReloader$5.reprocess(JrBeanReloader.java:1198)
	at org.springframework.beans.JrBeanReloader.processBeanClass(JrBeanReloader.java:1697)
	at org.springframework.beans.JrBeanReloader.processQueue(JrBeanReloader.java:1647)
	at org.springframework.beans.JrBeanReloader.reloadBeanDefinitionsInternal(JrBeanReloader.java:1194)
	at org.springframework.beans.JrBeanReloader.reloadBeanDefinitions(JrBeanReloader.java:964)
	at org.springframework.beans.JrBeanReloader.onClassEvent(JrBeanReloader.java:542)
	at org.springframework.beans.JrBeanReloader.access$000(JrBeanReloader.java:97)
	at org.springframework.beans.JrBeanReloader$1.onClassEvent(JrBeanReloader.java:173)
	at org.zeroturnaround.javarebel.ClassEventListener.onClassEvent(SourceFile:67)
	at org.zeroturnaround.javarebel.integration.util.ClassEventListenerUtil$BoundClassEventListener.onClassEvent(SourceFile:83)
	at com.zeroturnaround.javarebel.bb.b(SourceFile:175)
	at com.zeroturnaround.javarebel.bb.a(SourceFile:146)
	at com.zeroturnaround.javarebel.SDKReloaderImpl.a(SourceFile:94)
	at com.zeroturnaround.javarebel.SDKReloaderImpl.c(SourceFile:781)
	at com.zeroturnaround.javarebel.SDKReloaderImpl.c(SourceFile:772)
	at com.zeroturnaround.javarebel.SDKReloaderImpl.fireReloadListeners(SourceFile:817)
	at com.zeroturnaround.reload.ag.a(SourceFile:606)
	at com.zeroturnaround.reload.b.l(SourceFile:439)
	at com.zeroturnaround.reload.b.k(SourceFile:275)
	at com.zeroturnaround.reload.b.j(SourceFile:56)
	at com.zeroturnaround.reload.c.run(SourceFile:109)
11:12:45.402 [rebel-change-detector-thread] ERROR c.r.f.tcp.TcpServer - [start,52] - TCP服务器启动失败: Address already in use: NET_Bind
java.net.BindException: Address already in use: NET_Bind
	at java.base/java.net.PlainSocketImpl.bind0(Native Method)
	at java.base/java.net.PlainSocketImpl.socketBind(PlainSocketImpl.java:132)
	at java.base/java.net.AbstractPlainSocketImpl.bind(AbstractPlainSocketImpl.java:436)
	at java.base/java.net.ServerSocket.bind(ServerSocket.java:381)
	at java.base/java.net.ServerSocket.<init>(ServerSocket.java:243)
	at java.base/java.net.ServerSocket.<init>(ServerSocket.java:135)
	at com.ruoyi.framework.tcp.TcpServer.start(TcpServer.java:42)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:566)
	at org.springframework.beans.factory.annotation.InitDestroyAnnotationBeanPostProcessor$LifecycleElement.invoke(InitDestroyAnnotationBeanPostProcessor.java:389)
	at org.springframework.beans.factory.annotation.InitDestroyAnnotationBeanPostProcessor$LifecycleMetadata.invokeInitMethods(InitDestroyAnnotationBeanPostProcessor.java:333)
	at org.springframework.beans.factory.annotation.InitDestroyAnnotationBeanPostProcessor.postProcessBeforeInitialization(InitDestroyAnnotationBeanPostProcessor.java:157)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.applyBeanPostProcessorsBeforeInitialization(AbstractAutowireCapableBeanFactory.java:440)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.initializeBean(AbstractAutowireCapableBeanFactory.java:1796)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:620)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:542)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:336)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:334)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:209)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.preInstantiateSingletons(DefaultListableBeanFactory.java:955)
	at org.springframework.beans.JrBeanReloader.reinstantiateSingletons(JrBeanReloader.java:1313)
	at org.springframework.beans.JrBeanReloader.reloadBeanDefinitionsInternal(JrBeanReloader.java:1167)
	at org.springframework.beans.JrBeanReloader.reloadBeanDefinitions(JrBeanReloader.java:964)
	at org.springframework.beans.JrBeanReloader.onClassEvent(JrBeanReloader.java:542)
	at org.springframework.beans.JrBeanReloader.access$000(JrBeanReloader.java:97)
	at org.springframework.beans.JrBeanReloader$1.onClassEvent(JrBeanReloader.java:173)
	at org.zeroturnaround.javarebel.ClassEventListener.onClassEvent(SourceFile:67)
	at org.zeroturnaround.javarebel.integration.util.ClassEventListenerUtil$BoundClassEventListener.onClassEvent(SourceFile:83)
	at com.zeroturnaround.javarebel.bb.b(SourceFile:175)
	at com.zeroturnaround.javarebel.bb.a(SourceFile:146)
	at com.zeroturnaround.javarebel.SDKReloaderImpl.a(SourceFile:94)
	at com.zeroturnaround.javarebel.SDKReloaderImpl.c(SourceFile:781)
	at com.zeroturnaround.javarebel.SDKReloaderImpl.c(SourceFile:772)
	at com.zeroturnaround.javarebel.SDKReloaderImpl.fireReloadListeners(SourceFile:817)
	at com.zeroturnaround.reload.ag.a(SourceFile:606)
	at com.zeroturnaround.reload.b.l(SourceFile:439)
	at com.zeroturnaround.reload.b.k(SourceFile:275)
	at com.zeroturnaround.reload.b.j(SourceFile:56)
	at com.zeroturnaround.reload.c.run(SourceFile:109)
11:12:45.447 [rebel-change-detector-thread] ERROR c.r.f.tcp.TcpServer - [start,52] - TCP服务器启动失败: Address already in use: NET_Bind
java.net.BindException: Address already in use: NET_Bind
	at java.base/java.net.PlainSocketImpl.bind0(Native Method)
	at java.base/java.net.PlainSocketImpl.socketBind(PlainSocketImpl.java:132)
	at java.base/java.net.AbstractPlainSocketImpl.bind(AbstractPlainSocketImpl.java:436)
	at java.base/java.net.ServerSocket.bind(ServerSocket.java:381)
	at java.base/java.net.ServerSocket.<init>(ServerSocket.java:243)
	at java.base/java.net.ServerSocket.<init>(ServerSocket.java:135)
	at com.ruoyi.framework.tcp.TcpServer.start(TcpServer.java:42)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:566)
	at org.springframework.beans.factory.annotation.InitDestroyAnnotationBeanPostProcessor$LifecycleElement.invoke(InitDestroyAnnotationBeanPostProcessor.java:389)
	at org.springframework.beans.factory.annotation.InitDestroyAnnotationBeanPostProcessor$LifecycleMetadata.invokeInitMethods(InitDestroyAnnotationBeanPostProcessor.java:333)
	at org.springframework.beans.factory.annotation.InitDestroyAnnotationBeanPostProcessor.postProcessBeforeInitialization(InitDestroyAnnotationBeanPostProcessor.java:157)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.applyBeanPostProcessorsBeforeInitialization(AbstractAutowireCapableBeanFactory.java:440)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.initializeBean(AbstractAutowireCapableBeanFactory.java:1796)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.configureBean(AbstractAutowireCapableBeanFactory.java:365)
	at org.springframework.beans.JrBeanReloader.reprocessBean(JrBeanReloader.java:1875)
	at org.springframework.beans.JrBeanReloader.reprocessBean(JrBeanReloader.java:1850)
	at org.springframework.beans.JrBeanReloader.doReprocessSingletons(JrBeanReloader.java:1829)
	at org.springframework.beans.JrBeanReloader.reprocess(JrBeanReloader.java:1812)
	at org.springframework.beans.JrBeanReloader$5.reprocess(JrBeanReloader.java:1198)
	at org.springframework.beans.JrBeanReloader.processBeanClass(JrBeanReloader.java:1697)
	at org.springframework.beans.JrBeanReloader.processQueue(JrBeanReloader.java:1647)
	at org.springframework.beans.JrBeanReloader.reloadBeanDefinitionsInternal(JrBeanReloader.java:1194)
	at org.springframework.beans.JrBeanReloader.reloadBeanDefinitions(JrBeanReloader.java:964)
	at org.springframework.beans.JrBeanReloader.onClassEvent(JrBeanReloader.java:542)
	at org.springframework.beans.JrBeanReloader.access$000(JrBeanReloader.java:97)
	at org.springframework.beans.JrBeanReloader$1.onClassEvent(JrBeanReloader.java:173)
	at org.zeroturnaround.javarebel.ClassEventListener.onClassEvent(SourceFile:67)
	at org.zeroturnaround.javarebel.integration.util.ClassEventListenerUtil$BoundClassEventListener.onClassEvent(SourceFile:83)
	at com.zeroturnaround.javarebel.bb.b(SourceFile:175)
	at com.zeroturnaround.javarebel.bb.a(SourceFile:146)
	at com.zeroturnaround.javarebel.SDKReloaderImpl.a(SourceFile:94)
	at com.zeroturnaround.javarebel.SDKReloaderImpl.c(SourceFile:781)
	at com.zeroturnaround.javarebel.SDKReloaderImpl.c(SourceFile:772)
	at com.zeroturnaround.javarebel.SDKReloaderImpl.fireReloadListeners(SourceFile:817)
	at com.zeroturnaround.reload.ag.a(SourceFile:606)
	at com.zeroturnaround.reload.b.l(SourceFile:439)
	at com.zeroturnaround.reload.b.k(SourceFile:275)
	at com.zeroturnaround.reload.b.j(SourceFile:56)
	at com.zeroturnaround.reload.c.run(SourceFile:109)
11:25:36.679 [rebel-change-detector-thread] ERROR c.r.f.tcp.TcpServer - [start,52] - TCP服务器启动失败: Address already in use: NET_Bind
java.net.BindException: Address already in use: NET_Bind
	at java.base/java.net.PlainSocketImpl.bind0(Native Method)
	at java.base/java.net.PlainSocketImpl.socketBind(PlainSocketImpl.java:132)
	at java.base/java.net.AbstractPlainSocketImpl.bind(AbstractPlainSocketImpl.java:436)
	at java.base/java.net.ServerSocket.bind(ServerSocket.java:381)
	at java.base/java.net.ServerSocket.<init>(ServerSocket.java:243)
	at java.base/java.net.ServerSocket.<init>(ServerSocket.java:135)
	at com.ruoyi.framework.tcp.TcpServer.start(TcpServer.java:42)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:566)
	at org.springframework.beans.factory.annotation.InitDestroyAnnotationBeanPostProcessor$LifecycleElement.invoke(InitDestroyAnnotationBeanPostProcessor.java:389)
	at org.springframework.beans.factory.annotation.InitDestroyAnnotationBeanPostProcessor$LifecycleMetadata.invokeInitMethods(InitDestroyAnnotationBeanPostProcessor.java:333)
	at org.springframework.beans.factory.annotation.InitDestroyAnnotationBeanPostProcessor.postProcessBeforeInitialization(InitDestroyAnnotationBeanPostProcessor.java:157)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.applyBeanPostProcessorsBeforeInitialization(AbstractAutowireCapableBeanFactory.java:440)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.initializeBean(AbstractAutowireCapableBeanFactory.java:1796)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:620)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:542)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:336)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:334)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:209)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.preInstantiateSingletons(DefaultListableBeanFactory.java:955)
	at org.springframework.beans.JrBeanReloader.reinstantiateSingletons(JrBeanReloader.java:1313)
	at org.springframework.beans.JrBeanReloader.reloadBeanDefinitionsInternal(JrBeanReloader.java:1167)
	at org.springframework.beans.JrBeanReloader.reloadBeanDefinitions(JrBeanReloader.java:964)
	at org.springframework.beans.JrBeanReloader.onClassEvent(JrBeanReloader.java:542)
	at org.springframework.beans.JrBeanReloader.access$000(JrBeanReloader.java:97)
	at org.springframework.beans.JrBeanReloader$1.onClassEvent(JrBeanReloader.java:173)
	at org.zeroturnaround.javarebel.ClassEventListener.onClassEvent(SourceFile:67)
	at org.zeroturnaround.javarebel.integration.util.ClassEventListenerUtil$BoundClassEventListener.onClassEvent(SourceFile:83)
	at com.zeroturnaround.javarebel.bb.b(SourceFile:175)
	at com.zeroturnaround.javarebel.bb.a(SourceFile:146)
	at com.zeroturnaround.javarebel.SDKReloaderImpl.a(SourceFile:94)
	at com.zeroturnaround.javarebel.SDKReloaderImpl.c(SourceFile:781)
	at com.zeroturnaround.javarebel.SDKReloaderImpl.c(SourceFile:772)
	at com.zeroturnaround.javarebel.SDKReloaderImpl.fireReloadListeners(SourceFile:817)
	at com.zeroturnaround.reload.ag.a(SourceFile:606)
	at com.zeroturnaround.reload.b.l(SourceFile:439)
	at com.zeroturnaround.reload.b.k(SourceFile:275)
	at com.zeroturnaround.reload.b.j(SourceFile:56)
	at com.zeroturnaround.reload.c.run(SourceFile:109)
11:25:36.745 [rebel-change-detector-thread] ERROR c.r.f.tcp.TcpServer - [start,52] - TCP服务器启动失败: Address already in use: NET_Bind
java.net.BindException: Address already in use: NET_Bind
	at java.base/java.net.PlainSocketImpl.bind0(Native Method)
	at java.base/java.net.PlainSocketImpl.socketBind(PlainSocketImpl.java:132)
	at java.base/java.net.AbstractPlainSocketImpl.bind(AbstractPlainSocketImpl.java:436)
	at java.base/java.net.ServerSocket.bind(ServerSocket.java:381)
	at java.base/java.net.ServerSocket.<init>(ServerSocket.java:243)
	at java.base/java.net.ServerSocket.<init>(ServerSocket.java:135)
	at com.ruoyi.framework.tcp.TcpServer.start(TcpServer.java:42)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:566)
	at org.springframework.beans.factory.annotation.InitDestroyAnnotationBeanPostProcessor$LifecycleElement.invoke(InitDestroyAnnotationBeanPostProcessor.java:389)
	at org.springframework.beans.factory.annotation.InitDestroyAnnotationBeanPostProcessor$LifecycleMetadata.invokeInitMethods(InitDestroyAnnotationBeanPostProcessor.java:333)
	at org.springframework.beans.factory.annotation.InitDestroyAnnotationBeanPostProcessor.postProcessBeforeInitialization(InitDestroyAnnotationBeanPostProcessor.java:157)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.applyBeanPostProcessorsBeforeInitialization(AbstractAutowireCapableBeanFactory.java:440)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.initializeBean(AbstractAutowireCapableBeanFactory.java:1796)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.configureBean(AbstractAutowireCapableBeanFactory.java:365)
	at org.springframework.beans.JrBeanReloader.reprocessBean(JrBeanReloader.java:1875)
	at org.springframework.beans.JrBeanReloader.reprocessBean(JrBeanReloader.java:1850)
	at org.springframework.beans.JrBeanReloader.doReprocessSingletons(JrBeanReloader.java:1829)
	at org.springframework.beans.JrBeanReloader.reprocess(JrBeanReloader.java:1812)
	at org.springframework.beans.JrBeanReloader$5.reprocess(JrBeanReloader.java:1198)
	at org.springframework.beans.JrBeanReloader.processBeanClass(JrBeanReloader.java:1697)
	at org.springframework.beans.JrBeanReloader.processQueue(JrBeanReloader.java:1647)
	at org.springframework.beans.JrBeanReloader.reloadBeanDefinitionsInternal(JrBeanReloader.java:1194)
	at org.springframework.beans.JrBeanReloader.reloadBeanDefinitions(JrBeanReloader.java:964)
	at org.springframework.beans.JrBeanReloader.onClassEvent(JrBeanReloader.java:542)
	at org.springframework.beans.JrBeanReloader.access$000(JrBeanReloader.java:97)
	at org.springframework.beans.JrBeanReloader$1.onClassEvent(JrBeanReloader.java:173)
	at org.zeroturnaround.javarebel.ClassEventListener.onClassEvent(SourceFile:67)
	at org.zeroturnaround.javarebel.integration.util.ClassEventListenerUtil$BoundClassEventListener.onClassEvent(SourceFile:83)
	at com.zeroturnaround.javarebel.bb.b(SourceFile:175)
	at com.zeroturnaround.javarebel.bb.a(SourceFile:146)
	at com.zeroturnaround.javarebel.SDKReloaderImpl.a(SourceFile:94)
	at com.zeroturnaround.javarebel.SDKReloaderImpl.c(SourceFile:781)
	at com.zeroturnaround.javarebel.SDKReloaderImpl.c(SourceFile:772)
	at com.zeroturnaround.javarebel.SDKReloaderImpl.fireReloadListeners(SourceFile:817)
	at com.zeroturnaround.reload.ag.a(SourceFile:606)
	at com.zeroturnaround.reload.b.l(SourceFile:439)
	at com.zeroturnaround.reload.b.k(SourceFile:275)
	at com.zeroturnaround.reload.b.j(SourceFile:56)
	at com.zeroturnaround.reload.c.run(SourceFile:109)
11:28:41.214 [main] ERROR c.r.f.tcp.TcpServer - [start,52] - TCP服务器启动失败: Address already in use: NET_Bind
java.net.BindException: Address already in use: NET_Bind
	at java.base/java.net.PlainSocketImpl.bind0(Native Method)
	at java.base/java.net.PlainSocketImpl.socketBind(PlainSocketImpl.java:132)
	at java.base/java.net.AbstractPlainSocketImpl.bind(AbstractPlainSocketImpl.java:436)
	at java.base/java.net.ServerSocket.bind(ServerSocket.java:381)
	at java.base/java.net.ServerSocket.<init>(ServerSocket.java:243)
	at java.base/java.net.ServerSocket.<init>(ServerSocket.java:135)
	at com.ruoyi.framework.tcp.TcpServer.start(TcpServer.java:42)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:566)
	at org.springframework.beans.factory.annotation.InitDestroyAnnotationBeanPostProcessor$LifecycleElement.invoke(InitDestroyAnnotationBeanPostProcessor.java:389)
	at org.springframework.beans.factory.annotation.InitDestroyAnnotationBeanPostProcessor$LifecycleMetadata.invokeInitMethods(InitDestroyAnnotationBeanPostProcessor.java:333)
	at org.springframework.beans.factory.annotation.InitDestroyAnnotationBeanPostProcessor.postProcessBeforeInitialization(InitDestroyAnnotationBeanPostProcessor.java:157)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.applyBeanPostProcessorsBeforeInitialization(AbstractAutowireCapableBeanFactory.java:440)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.initializeBean(AbstractAutowireCapableBeanFactory.java:1796)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:620)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:542)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:336)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:334)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:209)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.preInstantiateSingletons(DefaultListableBeanFactory.java:955)
	at org.springframework.context.support.AbstractApplicationContext.finishBeanFactoryInitialization(AbstractApplicationContext.java:932)
	at org.springframework.context.support.AbstractApplicationContext.__refresh(AbstractApplicationContext.java:591)
	at org.springframework.context.support.AbstractApplicationContext.jrLockAndRefresh(AbstractApplicationContext.java:41002)
	at org.springframework.context.support.AbstractApplicationContext.refresh(AbstractApplicationContext.java:42008)
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.refresh(ServletWebServerApplicationContext.java:147)
	at org.springframework.boot.SpringApplication.refresh(SpringApplication.java:732)
	at org.springframework.boot.SpringApplication.refreshContext(SpringApplication.java:409)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:308)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1300)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1289)
	at com.ruoyi.system.RuoYiSystemApplication.main(RuoYiSystemApplication.java:38)
11:28:43.558 [main] ERROR o.s.b.d.LoggingFailureAnalysisReporter - [report,40] - 

***************************
APPLICATION FAILED TO START
***************************

Description:

Web server failed to start. Port 9201 was already in use.

Action:

Identify and stop the process that's listening on port 9201 or configure this application to listen on another port.

12:16:00.921 [Thread-39] ERROR c.r.f.t.WebSocketToTcpProxy - [listenForMessages,238] - ❌ TBOX模拟器TCP消息监听异常: Socket closed
java.net.SocketException: Socket closed
	at java.base/java.net.SocketInputStream.socketRead0(SocketInputStream.java)
	at java.base/java.net.SocketInputStream.socketRead(SocketInputStream.java:115)
	at java.base/java.net.SocketInputStream.read(SocketInputStream.java:168)
	at java.base/java.net.SocketInputStream.read(SocketInputStream.java:140)
	at java.base/sun.nio.cs.StreamDecoder.readBytes(StreamDecoder.java:284)
	at java.base/sun.nio.cs.StreamDecoder.implRead(StreamDecoder.java:326)
	at java.base/sun.nio.cs.StreamDecoder.read(StreamDecoder.java:178)
	at java.base/java.io.InputStreamReader.read(InputStreamReader.java:185)
	at java.base/java.io.BufferedReader.fill(BufferedReader.java:161)
	at java.base/java.io.BufferedReader.readLine(BufferedReader.java:326)
	at java.base/java.io.BufferedReader.readLine(BufferedReader.java:392)
	at com.ruoyi.framework.tcp.WebSocketToTcpProxy$TcpConnection.listenForMessages(WebSocketToTcpProxy.java:229)
	at java.base/java.lang.Thread.run(Thread.java:834)
12:16:20.035 [Thread-42] ERROR c.r.f.t.WebSocketToTcpProxy - [listenForMessages,238] - ❌ TBOX模拟器TCP消息监听异常: Socket closed
java.net.SocketException: Socket closed
	at java.base/java.net.SocketInputStream.socketRead0(SocketInputStream.java)
	at java.base/java.net.SocketInputStream.socketRead(SocketInputStream.java:115)
	at java.base/java.net.SocketInputStream.read(SocketInputStream.java:168)
	at java.base/java.net.SocketInputStream.read(SocketInputStream.java:140)
	at java.base/sun.nio.cs.StreamDecoder.readBytes(StreamDecoder.java:284)
	at java.base/sun.nio.cs.StreamDecoder.implRead(StreamDecoder.java:326)
	at java.base/sun.nio.cs.StreamDecoder.read(StreamDecoder.java:178)
	at java.base/java.io.InputStreamReader.read(InputStreamReader.java:185)
	at java.base/java.io.BufferedReader.fill(BufferedReader.java:161)
	at java.base/java.io.BufferedReader.readLine(BufferedReader.java:326)
	at java.base/java.io.BufferedReader.readLine(BufferedReader.java:392)
	at com.ruoyi.framework.tcp.WebSocketToTcpProxy$TcpConnection.listenForMessages(WebSocketToTcpProxy.java:229)
	at java.base/java.lang.Thread.run(Thread.java:834)
13:58:47.987 [Thread-67] ERROR c.r.f.t.WebSocketToTcpProxy - [listenForMessages,238] - ❌ TBOX模拟器TCP消息监听异常: Socket closed
java.net.SocketException: Socket closed
	at java.base/java.net.SocketInputStream.socketRead0(SocketInputStream.java)
	at java.base/java.net.SocketInputStream.socketRead(SocketInputStream.java:115)
	at java.base/java.net.SocketInputStream.read(SocketInputStream.java:168)
	at java.base/java.net.SocketInputStream.read(SocketInputStream.java:140)
	at java.base/sun.nio.cs.StreamDecoder.readBytes(StreamDecoder.java:284)
	at java.base/sun.nio.cs.StreamDecoder.implRead(StreamDecoder.java:326)
	at java.base/sun.nio.cs.StreamDecoder.read(StreamDecoder.java:178)
	at java.base/java.io.InputStreamReader.read(InputStreamReader.java:185)
	at java.base/java.io.BufferedReader.fill(BufferedReader.java:161)
	at java.base/java.io.BufferedReader.readLine(BufferedReader.java:326)
	at java.base/java.io.BufferedReader.readLine(BufferedReader.java:392)
	at com.ruoyi.framework.tcp.WebSocketToTcpProxy$TcpConnection.listenForMessages(WebSocketToTcpProxy.java:229)
	at java.base/java.lang.Thread.run(Thread.java:834)
14:12:41.563 [Thread-69] ERROR c.r.f.t.WebSocketToTcpProxy - [listenForMessages,238] - ❌ TBOX模拟器TCP消息监听异常: Socket closed
java.net.SocketException: Socket closed
	at java.base/java.net.SocketInputStream.socketRead0(SocketInputStream.java)
	at java.base/java.net.SocketInputStream.socketRead(SocketInputStream.java:115)
	at java.base/java.net.SocketInputStream.read(SocketInputStream.java:168)
	at java.base/java.net.SocketInputStream.read(SocketInputStream.java:140)
	at java.base/sun.nio.cs.StreamDecoder.readBytes(StreamDecoder.java:284)
	at java.base/sun.nio.cs.StreamDecoder.implRead(StreamDecoder.java:326)
	at java.base/sun.nio.cs.StreamDecoder.read(StreamDecoder.java:178)
	at java.base/java.io.InputStreamReader.read(InputStreamReader.java:185)
	at java.base/java.io.BufferedReader.fill(BufferedReader.java:161)
	at java.base/java.io.BufferedReader.readLine(BufferedReader.java:326)
	at java.base/java.io.BufferedReader.readLine(BufferedReader.java:392)
	at com.ruoyi.framework.tcp.WebSocketToTcpProxy$TcpConnection.listenForMessages(WebSocketToTcpProxy.java:229)
	at java.base/java.lang.Thread.run(Thread.java:834)
14:35:40.092 [Thread-70] ERROR c.r.f.t.WebSocketToTcpProxy - [listenForMessages,238] - ❌ TBOX模拟器TCP消息监听异常: Socket closed
java.net.SocketException: Socket closed
	at java.base/java.net.SocketInputStream.socketRead0(SocketInputStream.java)
	at java.base/java.net.SocketInputStream.socketRead(SocketInputStream.java:115)
	at java.base/java.net.SocketInputStream.read(SocketInputStream.java:168)
	at java.base/java.net.SocketInputStream.read(SocketInputStream.java:140)
	at java.base/sun.nio.cs.StreamDecoder.readBytes(StreamDecoder.java:284)
	at java.base/sun.nio.cs.StreamDecoder.implRead(StreamDecoder.java:326)
	at java.base/sun.nio.cs.StreamDecoder.read(StreamDecoder.java:178)
	at java.base/java.io.InputStreamReader.read(InputStreamReader.java:185)
	at java.base/java.io.BufferedReader.fill(BufferedReader.java:161)
	at java.base/java.io.BufferedReader.readLine(BufferedReader.java:326)
	at java.base/java.io.BufferedReader.readLine(BufferedReader.java:392)
	at com.ruoyi.framework.tcp.WebSocketToTcpProxy$TcpConnection.listenForMessages(Unknown Source)
	at java.base/java.lang.Thread.run(Thread.java:834)
16:30:40.710 [Thread-61] ERROR c.r.f.t.WebSocketToTcpProxy - [listenForMessages,238] - ❌ TBOX模拟器TCP消息监听异常: Socket closed
java.net.SocketException: Socket closed
	at java.base/java.net.SocketInputStream.socketRead0(SocketInputStream.java)
	at java.base/java.net.SocketInputStream.socketRead(SocketInputStream.java:115)
	at java.base/java.net.SocketInputStream.read(SocketInputStream.java:168)
	at java.base/java.net.SocketInputStream.read(SocketInputStream.java:140)
	at java.base/sun.nio.cs.StreamDecoder.readBytes(StreamDecoder.java:284)
	at java.base/sun.nio.cs.StreamDecoder.implRead(StreamDecoder.java:326)
	at java.base/sun.nio.cs.StreamDecoder.read(StreamDecoder.java:178)
	at java.base/java.io.InputStreamReader.read(InputStreamReader.java:185)
	at java.base/java.io.BufferedReader.fill(BufferedReader.java:161)
	at java.base/java.io.BufferedReader.readLine(BufferedReader.java:326)
	at java.base/java.io.BufferedReader.readLine(BufferedReader.java:392)
	at com.ruoyi.framework.tcp.WebSocketToTcpProxy$TcpConnection.listenForMessages(WebSocketToTcpProxy.java:229)
	at java.base/java.lang.Thread.run(Thread.java:834)
16:30:41.112 [SpringApplicationShutdownHook] ERROR c.a.c.n.r.NacosServiceRegistry - [deregister,111] - ERR_NACOS_DEREGISTER, de-register failed...NacosRegistration{nacosDiscoveryProperties=NacosDiscoveryProperties{serverAddr='localhost:8848', username='', password='', endpoint='', namespace='', watchDelay=30000, logName='', service='ruoyi-system', weight=1.0, clusterName='DEFAULT', group='DEFAULT_GROUP', namingLoadCacheAtStart='false', metadata={IPv6=null, preserved.register.source=SPRING_CLOUD}, registerEnabled=true, ip='*************', networkInterface='', port=9201, secure=false, accessKey='', secretKey='', heartBeatInterval=null, heartBeatTimeout=null, ipDeleteTimeout=null, instanceEnabled=true, ephemeral=true, failureToleranceEnabled=false}, ipDeleteTimeout=null, failFast=true}},
com.alibaba.nacos.api.exception.NacosException: Client not connected, current status:UNHEALTHY
	at com.alibaba.nacos.common.remote.client.RpcClient.request(RpcClient.java:639)
	at com.alibaba.nacos.common.remote.client.RpcClient.request(RpcClient.java:619)
	at com.alibaba.nacos.client.naming.remote.gprc.NamingGrpcClientProxy.requestToServer(NamingGrpcClientProxy.java:356)
	at com.alibaba.nacos.client.naming.remote.gprc.NamingGrpcClientProxy.doDeregisterService(NamingGrpcClientProxy.java:233)
	at com.alibaba.nacos.client.naming.remote.gprc.NamingGrpcClientProxy.deregisterService(NamingGrpcClientProxy.java:219)
	at com.alibaba.nacos.client.naming.remote.NamingClientProxyDelegate.deregisterService(NamingClientProxyDelegate.java:125)
	at com.alibaba.nacos.client.naming.NacosNamingService.deregisterInstance(NacosNamingService.java:201)
	at com.alibaba.nacos.client.naming.NacosNamingService.deregisterInstance(NacosNamingService.java:191)
	at com.alibaba.cloud.nacos.registry.NacosServiceRegistry.deregister(NacosServiceRegistry.java:107)
	at org.springframework.cloud.client.serviceregistry.AbstractAutoServiceRegistration.deregister(AbstractAutoServiceRegistration.java:249)
	at org.springframework.cloud.client.serviceregistry.AbstractAutoServiceRegistration.stop(AbstractAutoServiceRegistration.java:264)
	at org.springframework.cloud.client.serviceregistry.AbstractAutoServiceRegistration.destroy(AbstractAutoServiceRegistration.java:201)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:566)
	at org.springframework.beans.factory.annotation.InitDestroyAnnotationBeanPostProcessor$LifecycleElement.invoke(InitDestroyAnnotationBeanPostProcessor.java:389)
	at org.springframework.beans.factory.annotation.InitDestroyAnnotationBeanPostProcessor$LifecycleMetadata.invokeDestroyMethods(InitDestroyAnnotationBeanPostProcessor.java:347)
	at org.springframework.beans.factory.annotation.InitDestroyAnnotationBeanPostProcessor.postProcessBeforeDestruction(InitDestroyAnnotationBeanPostProcessor.java:177)
	at org.springframework.beans.factory.support.DisposableBeanAdapter.destroy(DisposableBeanAdapter.java:197)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.destroyBean(DefaultSingletonBeanRegistry.java:587)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.destroySingleton(DefaultSingletonBeanRegistry.java:559)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.destroySingleton(DefaultListableBeanFactory.java:1163)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.destroySingletons(DefaultSingletonBeanRegistry.java:520)
	at org.springframework.beans.factory.support.AbstractBeanFactory.destroySingletons(AbstractBeanFactory.java:44004)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.destroySingletons(DefaultListableBeanFactory.java:1156)
	at org.springframework.context.support.AbstractApplicationContext.destroyBeans(AbstractApplicationContext.java:1123)
	at org.springframework.context.support.AbstractApplicationContext.doClose(AbstractApplicationContext.java:1089)
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.doClose(ServletWebServerApplicationContext.java:174)
	at org.springframework.context.support.AbstractApplicationContext.close(AbstractApplicationContext.java:1035)
	at org.springframework.boot.SpringApplicationShutdownHook.closeAndWait(SpringApplicationShutdownHook.java:145)
	at java.base/java.lang.Iterable.forEach(Iterable.java:75)
	at org.springframework.boot.SpringApplicationShutdownHook.run(SpringApplicationShutdownHook.java:114)
	at java.base/java.lang.Thread.run(Thread.java:834)

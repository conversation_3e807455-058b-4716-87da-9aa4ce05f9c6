Mobile -> Mobile : 16b. 生成控车指令并签名
note right of Mobile
  指令包含:
  - 操作类型（如解锁、上锁等）
  - 时间戳（防重放攻击）
  - 随机数（增加安全性）
  - 数字签名（验证指令完整性）
end note

Mobile -> Mobile : 16c. 使用会话密钥加密指令
Mobile -> CarBT : 17. 发送加密的控车指令

CarBT -> CarBT : 18a. 使用会话密钥解密指令
CarBT -> CarBT : 18b. 验证数字签名
CarBT -> CarBT : 18c. 检查用户权限
note right of CarBT
  权限检查包括:
  - 验证用户身份（车主/临时用户）
  - 检查操作权限级别
  - 验证操作授权状态
  - 检查安全限制条件
end note
CarBT -> CarBT : 18d. 检查指令时效性（防重放）
note right of CarBT
  时效性检查:
  - 验证时间戳是否在有效期内
  - 检查是否重复执行
  - 验证随机数是否有效
end note

alt 指令验证通过
    CarBT -> CarBT : 19a. 执行控车操作
    CarBT -> Mobile : 19b. 返回操作执行结果
    CarBT -> Cloud : 19c. 上报操作记录（可选，根据网络状态）
    Mobile -> Mobile : 19d. 生成系统通知提醒用户操作已执行
else 指令验证失败
    CarBT -> Mobile : 20a. 返回验证失败信息
    note right of CarBT
      失败原因可能是:
      - 解密失败
      - 签名验证失败
      - 权限不足
      - 指令过期
      - 重复执行
    end note
    Mobile -> Mobile : 20b. 记录失败原因，不打扰用户
end 
// 图片加载脚本
document.addEventListener('DOMContentLoaded', function() {
    // 查找所有需要替换的图片
    const images = document.querySelectorAll('img[data-html-src]');
    
    // 替换图片源
    images.forEach(function(img) {
        const htmlSrc = img.getAttribute('data-html-src');
        if (htmlSrc) {
            // 设置图片的src为HTML文件路径
            img.setAttribute('src', htmlSrc);
            
            // 添加点击事件，点击图片时在新窗口打开HTML文件
            img.style.cursor = 'pointer';
            img.addEventListener('click', function() {
                window.open(htmlSrc, '_blank');
            });
            
            // 添加提示
            img.setAttribute('title', '点击查看详细内容');
        }
    });
}); 
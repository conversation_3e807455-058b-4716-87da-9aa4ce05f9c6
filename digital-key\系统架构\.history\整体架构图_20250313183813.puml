@startuml
skinparam componentStyle rectangle

package "数字钥匙" {
    package "手机端" {
        component "钥匙管理模块" as KeyManage
        component "车辆控制模块" as VehicleCtrl
        component "蓝牙通信模块" as BLEComm
        component "安全存储模块" as SecStorage
        component "时间同步模块" as TimeSync
    }
    
    package "车端" {
        component "BLE通信模块" as CarBLE
        component "钥匙验证模块" as KeyVerify
        component "指令执行模块" as CmdExec
        component "安全存储模块" as CarSecStorage
        component "T-Box通信模块" as TBoxComm
        component "时间同步模块" as CarTimeSync
    }
    
    package "钥匙云平台" {
        component "钥匙生命周期管理" as KeyLifecycle
        component "VIN码关联服务" as VINService
        component "安全认证中心" as AuthCenter
        component "密钥管理系统" as KeyMgmt
        component "时间服务器" as TimeServer
        component "统一接口服务" as UnifiedAPI
    }
    
    ' 简化外部平台设计
    component "外部平台(TSP/OEM)" as ExternalPlatforms
    
    database "数据存储接口" {
        database "钥匙数据" as KeysDB
        database "密钥材料" as KeyMaterialsDB
    }
    
    database "安全基础设施" {
        database "密码机/HSM" as HSM
        database "PKI/CA系统" as PKICA
    }
}

' 手机端内部关系
KeyManage --> VehicleCtrl
VehicleCtrl --> BLEComm
BLEComm --> SecStorage
SecStorage --> TimeSync

' 车端内部关系
CarBLE --> KeyVerify
KeyVerify --> CmdExec
CmdExec --> CarSecStorage
CarSecStorage --> TBoxComm
TBoxComm --> CarTimeSync

' 钥匙服务内部关系
KeyLifecycle --> VINService
VINService --> AuthCenter
AuthCenter --> KeyMgmt
KeyMgmt --> TimeServer
TimeServer --> UnifiedAPI

' 跨系统关系
BLEComm <--> CarBLE : 蓝牙通信
UnifiedAPI <--> TBoxComm : 远程通信
UnifiedAPI <--> KeyManage : HTTPS通信
TimeServer --> TimeSync : 时间同步
TimeServer --> CarTimeSync : 时间同步

' 数据库关系
KeyLifecycle --> KeysDB
KeyMgmt --> KeyMaterialsDB
AuthCenter --> PKICA
KeyMgmt --> HSM

' 外部平台与钥匙云平台的关系 - 简化连接
UnifiedAPI <--> ExternalPlatforms : 数据交互/API调用

note right of UnifiedAPI
  统一接口服务负责对内与车端TBOX通信，
  下发证书、密钥和控制指令；
  对外与TSP/OEM平台交互，
  获取车辆信息、用户授权等数据
end note

@enduml
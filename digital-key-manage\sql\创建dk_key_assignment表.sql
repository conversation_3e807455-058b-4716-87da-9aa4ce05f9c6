-- 创建 dk_key_assignment 表
-- 用于匹配现有的 DkKeyAssignment 实体类

-- 删除表（如果存在）
DROP TABLE IF EXISTS dk_key_assignment;

-- 创建钥匙分配表
CREATE TABLE dk_key_assignment (
    assignment_id BIGINT(20) NOT NULL AUTO_INCREMENT COMMENT '分配ID',
    user_id BIGINT(20) NOT NULL COMMENT '用户ID',
    user_name VARCHAR(50) COMMENT '用户名',
    vehicle_id BIGINT(20) NOT NULL COMMENT '车辆ID',
    vin_code VARCHAR(17) COMMENT '车辆VIN码',
    vehicle_brand VARCHAR(50) COMMENT '车辆品牌',
    vehicle_model VARCHAR(50) COMMENT '车辆型号',
    status CHAR(1) DEFAULT '0' COMMENT '分配状态（0待分配 1已分配 2已撤销）',
    assignment_time DATETIME COMMENT '分配时间',
    revoke_time DATETIME COMMENT '撤销时间',
    operator_id BIGINT(20) COMMENT '操作员ID',
    operator_name VARCHAR(50) COMMENT '操作员姓名',
    remark VARCHAR(500) DEFAULT '' COMMENT '备注',
    valid_start_time DATETIME COMMENT '有效期开始时间',
    valid_end_time DATETIME COMMENT '有效期结束时间',
    permission_type CHAR(1) DEFAULT '1' COMMENT '权限类型（1临时权限 2长期权限）',
    create_by VARCHAR(64) DEFAULT '' COMMENT '创建者',
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    update_by VARCHAR(64) DEFAULT '' COMMENT '更新者',
    update_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (assignment_id),
    KEY idx_user_id (user_id),
    KEY idx_vehicle_id (vehicle_id),
    KEY idx_status (status),
    KEY idx_assignment_time (assignment_time)
) ENGINE=InnoDB AUTO_INCREMENT=1 COMMENT = '钥匙分配表';

-- 插入测试数据
INSERT INTO dk_key_assignment (assignment_id, user_id, user_name, vehicle_id, vin_code, vehicle_brand, vehicle_model, status, assignment_time, revoke_time, operator_id, operator_name, remark, valid_start_time, valid_end_time, permission_type, create_time, update_time) VALUES
(1, 1001, '张三', 1, 'LSGKB54U8EA123456', '比亚迪', '秦PLUS DM-i', '1', '2024-01-15 09:00:00', NULL, 1, '管理员', '商务出行', '2024-01-15 09:00:00', '2024-01-15 18:00:00', '1', NOW(), NOW()),
(2, 1002, '李四', 2, 'LFV2A21K8E4123456', '特斯拉', 'Model 3', '2', '2024-01-14 14:00:00', '2024-01-14 19:30:00', 1, '管理员', '日常通勤', '2024-01-14 14:00:00', '2024-01-14 20:00:00', '1', NOW(), NOW()),
(3, 1003, '王五', 3, 'LHGCV1658EA123456', '本田', '雅阁', '2', '2024-01-13 10:00:00', '2024-01-13 18:30:00', 1, '管理员', '周末出游', '2024-01-13 10:00:00', '2024-01-13 18:00:00', '1', NOW(), NOW()),
(4, 1006, '孙八', 4, 'LHGCV1658EA789012', '本田', '思域', '1', '2024-01-15 08:30:00', NULL, 1, '管理员', '客户接待', '2024-01-15 08:30:00', '2024-01-15 17:30:00', '1', NOW(), NOW()),
(5, 1005, '钱七', 5, 'LSGKB54U8EA789012', '比亚迪', '汉EV', '2', '2024-01-12 09:15:00', '2024-01-12 18:00:00', 1, '管理员', '临时用车', '2024-01-12 09:15:00', '2024-01-12 18:15:00', '1', NOW(), NOW()),
(6, 1007, '周九', 6, 'LFV2A21K8E4789012', '特斯拉', 'Model Y', '1', '2024-01-15 07:45:00', NULL, 1, '管理员', '出差用车', '2024-01-15 07:45:00', '2024-01-15 19:45:00', '1', NOW(), NOW()),
(7, 1008, '吴十', 7, 'LHGCV1658EA456789', '本田', 'CR-V', '0', '2024-01-11 11:20:00', NULL, 1, '管理员', '紧急用车', '2024-01-11 11:20:00', '2024-01-11 16:20:00', '1', NOW(), NOW()),
(8, 1004, '赵六', 8, 'LSGKB54U8EA456789', '比亚迪', '宋PLUS DM-i', '2', '2024-01-10 13:30:00', '2024-01-10 20:15:00', 1, '管理员', '周末出游', '2024-01-10 13:30:00', '2024-01-10 20:30:00', '1', NOW(), NOW());

-- 验证数据插入
SELECT '钥匙分配表创建完成' as message, COUNT(*) as record_count FROM dk_key_assignment;

-- 显示表结构
DESC dk_key_assignment;

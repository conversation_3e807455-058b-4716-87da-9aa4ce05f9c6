-- 修复申请单号字段问题
-- 执行时间：2025-01-19

-- 1. 修改application_no字段允许为空
ALTER TABLE dk_user_application MODIFY COLUMN application_no VARCHAR(32) NULL COMMENT '申请单号';

-- 2. 为现有的空记录生成申请单号（如果有的话）
UPDATE dk_user_application 
SET application_no = CONCAT('DK', DATE_FORMAT(create_time, '%Y%m%d'), LPAD(FLOOR(RAND() * 900000) + 100000, 6, '0'))
WHERE application_no IS NULL OR application_no = '';

-- 3. 验证修改结果
SELECT 
    application_id,
    application_no,
    real_name,
    phone,
    status,
    create_time
FROM dk_user_application 
ORDER BY create_time DESC 
LIMIT 10;

-- 4. 检查字段定义
DESCRIBE dk_user_application;

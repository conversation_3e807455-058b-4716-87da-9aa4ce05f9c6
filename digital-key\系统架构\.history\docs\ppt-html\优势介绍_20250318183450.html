<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>车载智能钥匙系统 - 优势介绍</title>
    <script src="https://html2canvas.hertzen.com/dist/html2canvas.min.js"></script>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
            font-family: 'PingFang SC', 'Microsoft YaHei', sans-serif;
        }
        
        body {
            background: #f0f0f0;
            display: flex;
            justify-content: center;
            align-items: center;
            min-height: 100vh;
            color: #333;
            padding: 20px;
        }
        
        .ppt-container {
            position: relative;
            width: 1920px;
            height: 1080px;
            box-shadow: 0 0 30px rgba(0, 0, 0, 0.2);
            border: 2px dashed #ccc;
            background-image: linear-gradient(45deg, #ccc 25%, transparent 25%), 
                            linear-gradient(-45deg, #ccc 25%, transparent 25%), 
                            linear-gradient(45deg, transparent 75%, #ccc 75%), 
                            linear-gradient(-45deg, transparent 75%, #ccc 75%);
            background-size: 20px 20px;
            background-position: 0 0, 0 10px, 10px -10px, -10px 0px;
        }
        
        .ppt-label {
            position: absolute;
            top: -40px;
            left: 0;
            background-color: #555;
            color: white;
            padding: 5px 15px;
            border-radius: 4px;
            font-size: 16px;
        }
        
        .copy-button, .download-button {
            position: absolute;
            top: 20px;
            right: 20px;
            background: linear-gradient(135deg, #3498db, #2980b9);
            color: white;
            border: none;
            padding: 12px 25px;
            border-radius: 30px;
            font-size: 18px;
            cursor: pointer;
            z-index: 1000;
            box-shadow: 0 4px 10px rgba(0, 0, 0, 0.2);
            display: flex;
            align-items: center;
            gap: 10px;
            transition: all 0.3s ease;
        }
        
        .download-button {
            right: 230px;
            background: linear-gradient(135deg, #2ecc71, #27ae60);
        }
        
        .slide {
            width: 100%;
            height: 100%;
            background-color: transparent;
            padding: 40px;
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            overflow: hidden;
            position: relative;
        }
        
        .header {
            text-align: center;
            margin-bottom: 40px;
        }
        
        .header h1 {
            font-size: 52px;
            margin-bottom: 20px;
            color: #2c3e50;
            font-weight: 700;
        }
        
        .header .subtitle {
            font-size: 28px;
            color: #7f8c8d;
            font-weight: 300;
        }
        
        .advantages-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 40px;
            padding: 0 80px;
            width: 100%;
            max-width: 1600px;
        }
        
        .advantage-card {
            background: white;
            border-radius: 20px;
            padding: 30px;
            display: flex;
            align-items: flex-start;
            gap: 25px;
            box-shadow: 0 8px 20px rgba(0, 0, 0, 0.07);
            transition: all 0.3s ease;
        }
        
        .advantage-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 12px 25px rgba(0, 0, 0, 0.1);
        }
        
        .advantage-icon {
            width: 80px;
            height: 80px;
            border-radius: 18px;
            display: flex;
            align-items: center;
            justify-content: center;
            flex-shrink: 0;
        }
        
        .advantage-icon.security {
            background: linear-gradient(135deg, #3498db, #2980b9);
        }
        
        .advantage-icon.convenience {
            background: linear-gradient(135deg, #2ecc71, #27ae60);
        }
        
        .advantage-icon.reliability {
            background: linear-gradient(135deg, #9b59b6, #8e44ad);
        }
        
        .advantage-icon.extensibility {
            background: linear-gradient(135deg, #e67e22, #d35400);
        }
        
        .advantage-icon.perception {
            background: linear-gradient(135deg, #1abc9c, #16a085);
        }
        
        .advantage-icon.sharing {
            background: linear-gradient(135deg, #f39c12, #f1c40f);
        }
        
        .advantage-icon img {
            width: 45px;
            height: 45px;
            filter: brightness(0) invert(1);
        }
        
        .advantage-icon svg {
            width: 50px;
            height: 50px;
        }
        
        .advantage-content {
            flex: 1;
        }
        
        .advantage-title {
            font-size: 26px;
            font-weight: 600;
            color: #2c3e50;
            margin-bottom: 15px;
        }
        
        .advantage-desc {
            font-size: 18px;
            color: #7f8c8d;
            line-height: 1.6;
        }
        
        .advantage-features {
            margin-top: 20px;
            list-style: none;
        }
        
        .advantage-features li {
            font-size: 17px;
            color: #555;
            margin-bottom: 10px;
            padding-left: 22px;
            position: relative;
        }
        
        .advantage-features li:before {
            content: "•";
            color: #3498db;
            position: absolute;
            left: 0;
            font-size: 20px;
        }
        
        .toast {
            position: fixed;
            top: 50px;
            left: 50%;
            transform: translateX(-50%);
            background-color: rgba(0, 0, 0, 0.8);
            color: white;
            padding: 12px 25px;
            border-radius: 30px;
            font-size: 16px;
            z-index: 2000;
            display: none;
            animation: fadeInOut 2s ease;
        }
        
        @keyframes fadeInOut {
            0% { opacity: 0; }
            20% { opacity: 1; }
            80% { opacity: 1; }
            100% { opacity: 0; }
        }
    </style>
</head>
<body>
    <div class="ppt-container" id="pptContainer">
        <div class="ppt-label">PPT尺寸(1920×1080) - 透明背景</div>
        <button class="copy-button" id="copyButton">
            <span class="copy-icon"></span>复制为PPT图片
        </button>
        <button class="download-button" id="downloadButton">
            <span class="download-icon"></span>下载透明PNG
        </button>
        
        <div class="slide" id="slide">
            <div class="header">
                <h1>车载智能钥匙系统优势</h1>
                <div class="subtitle">安全、高效、精准的数字钥匙解决方案</div>
            </div>
            
            <div class="advantages-grid">
                <!-- 安全性优势 -->
                <div class="advantage-card">
                    <div class="advantage-icon security">
                        <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" width="50" height="50">
                            <path fill="white" d="M12,1C7.04,1 3,5.04 3,10c0,4.96 4.04,9 9,9s9-4.04 9-9C21,5.04 16.96,1 12,1z M12,19C4.28,19 4,13.72 4,12c0-1.72.28-7 8-7s8,5.28 8,7C19,13.72 18.72,19 12,19z"/>
                        </svg>
                    </div>
                    <div class="advantage-content">
                        <div class="advantage-title">密钥级安全保障</div>
                        <div class="advantage-desc">基于密码学和安全通信的多重防护</div>
                        <ul class="advantage-features">
                            <li>端到端加密通信，防止数据泄露</li>
                            <li>挑战-响应认证机制，避免密钥复制</li>
                            <li>随机挑战数防重放攻击，一次一密</li>
                            <li>安全存储模块，关键信息隔离保护</li>
                        </ul>
                    </div>
                </div>
                
                <!-- 便捷性优势 -->
                <div class="advantage-card">
                    <div class="advantage-icon convenience">
                        <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" width="50" height="50">
                            <path fill="white" d="M12,2C6.48,2 2,6.48 2,12s4.48,10 10,10 10-4.48 10-10S17.52,2 12,2z M12,20c-4.41,0-8-3.59-8-8s3.59-8 8-8 8,3.59 8,8S16.41,20 12,20z"/>
                        </svg>
                    </div>
                    <div class="advantage-content">
                        <div class="advantage-title">无感智能控车</div>
                        <div class="advantage-desc">简化用户操作流程提供无缝体验</div>
                        <ul class="advantage-features">
                            <li>自动蓝牙连接，无需手动操作</li>
                            <li>高精度距离检测，靠近即解锁</li>
                            <li>远程控制，随时随地管理车辆</li>
                            <li>离线使用，无需依赖网络环境</li>
                        </ul>
                    </div>
                </div>
                
                <!-- 智能感知优势 - 更新自省电策略 -->
                <div class="advantage-card">
                    <div class="advantage-icon perception">
                        <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" width="50" height="50">
                            <path fill="white" d="M10,16h.91c.61,0 1.13-.26 1.5-.68l1.95-2.27s.03-.03.06-.03l3.22,4.02c.23.29.58.45.95.45,1,0 1.37-.14 1.37-1.55V8.01c0-1.36-.55-1.4-1.3-1.4v-.01c-.58,0-.91.2s-5.02-1.25-7.59,1.83l-1.11,1.43c-.55.69-.43,1.61-.43,2.49s-.49,1.77-1.04,2.39C6.67,14.52 6.85,16 8,16h2m4-10H8c.58-.67,1.06-1.55,2-2h4v2z"/>
                        </svg>
                    </div>
                    <div class="advantage-content">
                        <div class="advantage-title">智能用户感知</div>
                        <div class="advantage-desc">自适应的智能功耗与连接控制</div>
                        <ul class="advantage-features">
                            <li>监测用户运动状态，智能降低触发频率</li>
                            <li>动态RSSI分析，精确计算距离信息</li>
                            <li>上下文感知唤醒，优化电量使用</li>
                            <li>机器学习算法过滤环境干扰</li>
                        </ul>
                    </div>
                </div>
                
                <!-- 可靠性优势 -->
                <div class="advantage-card">
                    <div class="advantage-icon reliability">
                        <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" width="50" height="50">
                            <path fill="white" d="M12,2C6.48,2 2,6.48 2,12s4.48,10 10,10 10-4.48 10-10S17.52,2 12,2z M10,17l-5-5l1.41-1.41L10,14.17l7.59-7.59L19,8l-9,9z"/>
                        </svg>
                    </div>
                    <div class="advantage-content">
                        <div class="advantage-title">全方位容错设计</div>
                        <div class="advantage-desc">多重保障机制确保系统稳定运行</div>
                        <ul class="advantage-features">
                            <li>双通道通信确保信号稳定可靠</li>
                            <li>异常自动处理与恢复机制</li>
                            <li>指数退避重连，智能应对干扰</li>
                            <li>断网后本地安全能力持续工作</li>
                        </ul>
                    </div>
                </div>
                
                <!-- 扩展性优势 -->
                <div class="advantage-card">
                    <div class="advantage-icon extensibility">
                        <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" width="50" height="50">
                            <path fill="white" d="M19,13h-6V7h6v6zm-8,6v-6H5v6h6zm8-10v6h6V7c0-1.1-.9-2-2-2H19zm-8,0v6H5V7h6zM19,5H5c-1.1,0-2,0.9-2,2v6h6V7h4v2h-4v4h6v4h4v-4h-4v-2h10V13z"/>
                        </svg>
                    </div>
                    <div class="advantage-content">
                        <div class="advantage-title">模块化架构设计</div>
                        <div class="advantage-desc">灵活系统架构支持功能持续扩展</div>
                        <ul class="advantage-features">
                            <li>三端架构分层设计，责任边界清晰</li>
                            <li>标准化接口支持与外部系统集成</li>
                            <li>OTA升级持续优化用户体验</li>
                            <li>多车型快速适配部署能力</li>
                        </ul>
                    </div>
                </div>
                
                <!-- 钥匙分享优势 -->
                <div class="advantage-card">
                    <div class="advantage-icon sharing">
                        <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" width="50" height="50">
                            <path fill="white" d="M18,16C20.01,16 21.71,17.09 21.95,19.65L22,20H2C2,17.99 3.29,16.29 5.85,16.05L6,16H18ZM18,14V12C5.58,12 4,15.79 4,19.4C1.64,17.78 0,15.42 0,12C0,12 0,10 2,10C3.41,13.6 6.23,14 8.34,14C13.5,14 18.57,8.17 19.74,5.25C20.71,6.45 21.83,7 23,7V9C20.78,9 19,11.79 19,14H12C12,11.79 10.22,10 8,10V8C8,6.35 9.35,5 11,5H13C13,6.74 14.57,8.39 16.43,9.73C16.81,7.79 13.6,6 12,6Z"/>
                        </svg>
                    </div>
                    <div class="advantage-content">
                        <div class="advantage-title">灵活钥匙分享</div>
                        <div class="advantage-desc">完整的钥匙授权与权限控制系统</div>
                        <ul class="advantage-features">
                            <li>多级权限设计，精确控制授权范围</li>
                            <li>多种时效模式，支持临时/周期性授权</li>
                            <li>车主全程可监控与撤销钥匙使用</li>
                            <li>钥匙生命周期完整管理能力</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="toast" id="toast">操作成功！</div>
    
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const copyButton = document.getElementById('copyButton');
            const downloadButton = document.getElementById('downloadButton');
            const toast = document.getElementById('toast');
            const slide = document.getElementById('slide');
            
            function showToast(message) {
                toast.textContent = message;
                toast.style.display = 'block';
                setTimeout(function() {
                    toast.style.display = 'none';
                }, 2000);
            }
            
            function generateImage() {
                return html2canvas(slide, {
                    scale: 2,
                    useCORS: true,
                    allowTaint: true,
                    backgroundColor: null
                });
            }
            
            copyButton.addEventListener('click', function() {
                showToast("正在处理图片，请稍候...");
                
                generateImage().then(function(canvas) {
                    try {
                        canvas.toBlob(function(blob) {
                            try {
                                const item = new ClipboardItem({ 'image/png': blob });
                                navigator.clipboard.write([item]).then(function() {
                                    showToast("已复制到剪贴板！现在可以粘贴到PPT中");
                                }).catch(function(err) {
                                    showToast("自动复制失败，请使用下载按钮获取图片");
                                    console.error("剪贴板API错误:", err);
                                });
                            } catch (e) {
                                showToast("您的浏览器不支持复制图片，请使用下载按钮");
                                console.error("ClipboardItem错误:", e);
                            }
                        });
                    } catch (e) {
                        showToast("复制功能不可用，请使用下载图片功能");
                        console.error("toBlob错误:", e);
                    }
                }).catch(function(err) {
                    showToast("生成图片失败，请尝试下载或截图");
                    console.error("html2canvas错误:", err);
                });
            });
            
            downloadButton.addEventListener('click', function() {
                showToast("正在生成高清透明PNG图片，请稍候...");
                
                generateImage().then(function(canvas) {
                    const imageUrl = canvas.toDataURL('image/png');
                    const link = document.createElement('a');
                    link.download = "车载智能钥匙系统优势-透明背景.png";
                    link.href = imageUrl;
                    link.click();
                    
                    showToast("高清透明背景PNG已下载");
                }).catch(function(err) {
                    showToast("生成图片失败，请尝试截图");
                    console.error("Error:", err);
                });
            });
        });
    </script>
</body>
</html> 
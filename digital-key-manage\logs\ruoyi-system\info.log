10:05:48.006 [main] INFO  c.a.n.c.e.SearchableProperties - [sortPropertySourceDefaultOrder,197] - properties search order:PROPERTIES->JVM->ENV->DEFAULT_SETTING
10:05:48.048 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
10:05:48.766 [main] INFO  c.r.s.RuoYiSystemApplication - [logStartupProfileInfo,638] - The following 1 profile is active: "dev"
10:05:50.671 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9201"]
10:05:50.673 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
10:05:50.673 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.83]
10:05:50.808 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
10:05:51.139 [main] INFO  c.a.d.p.DruidDataSource - [init,1009] - {dataSource-1,master} inited
10:05:51.141 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,160] - dynamic-datasource - add a datasource named [master] success
10:05:51.143 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [afterPropertiesSet,243] - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
10:05:52.575 [main] INFO  c.a.c.s.SentinelWebMvcConfigurer - [addInterceptors,52] - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
10:05:53.815 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9201"]
10:05:53.836 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,217] - dynamic-datasource start closing ....
10:05:53.839 [main] INFO  c.a.d.p.DruidDataSource - [close,2174] - {dataSource-1} closing ...
10:05:53.840 [main] INFO  c.a.d.p.DruidDataSource - [close,2247] - {dataSource-1} closed
10:05:53.840 [main] INFO  c.b.d.d.d.DefaultDataSourceDestroyer - [destroy,98] - dynamic-datasource close the datasource named [master] success,
10:05:53.840 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,221] - dynamic-datasource all closed success,bye
10:05:53.842 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Pausing ProtocolHandler ["http-nio-9201"]
10:05:53.842 [main] INFO  o.a.c.c.StandardService - [log,173] - Stopping service [Tomcat]
10:05:53.851 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Stopping ProtocolHandler ["http-nio-9201"]
10:05:53.853 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Destroying ProtocolHandler ["http-nio-9201"]
10:11:08.541 [main] INFO  c.a.n.c.e.SearchableProperties - [sortPropertySourceDefaultOrder,197] - properties search order:PROPERTIES->JVM->ENV->DEFAULT_SETTING
10:11:08.583 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
10:11:09.278 [main] INFO  c.r.s.RuoYiSystemApplication - [logStartupProfileInfo,638] - The following 1 profile is active: "dev"
10:11:11.069 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9201"]
10:11:11.070 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
10:11:11.070 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.83]
10:11:11.198 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
10:11:11.483 [main] INFO  c.a.d.p.DruidDataSource - [init,1009] - {dataSource-1,master} inited
10:11:11.484 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,160] - dynamic-datasource - add a datasource named [master] success
10:11:11.485 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [afterPropertiesSet,243] - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
10:11:12.845 [main] INFO  c.a.c.s.SentinelWebMvcConfigurer - [addInterceptors,52] - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
10:11:14.153 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9201"]
10:11:14.172 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,217] - dynamic-datasource start closing ....
10:11:14.175 [main] INFO  c.a.d.p.DruidDataSource - [close,2174] - {dataSource-1} closing ...
10:11:14.176 [main] INFO  c.a.d.p.DruidDataSource - [close,2247] - {dataSource-1} closed
10:11:14.176 [main] INFO  c.b.d.d.d.DefaultDataSourceDestroyer - [destroy,98] - dynamic-datasource close the datasource named [master] success,
10:11:14.176 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,221] - dynamic-datasource all closed success,bye
10:11:14.177 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Pausing ProtocolHandler ["http-nio-9201"]
10:11:14.178 [main] INFO  o.a.c.c.StandardService - [log,173] - Stopping service [Tomcat]
10:11:14.188 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Stopping ProtocolHandler ["http-nio-9201"]
10:11:14.190 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Destroying ProtocolHandler ["http-nio-9201"]
10:29:49.896 [main] INFO  c.a.n.c.e.SearchableProperties - [sortPropertySourceDefaultOrder,197] - properties search order:PROPERTIES->JVM->ENV->DEFAULT_SETTING
10:29:49.939 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
10:29:50.662 [main] INFO  c.r.s.RuoYiSystemApplication - [logStartupProfileInfo,638] - The following 1 profile is active: "dev"
10:29:52.503 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9202"]
10:29:52.505 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
10:29:52.505 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.83]
10:29:52.620 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
10:29:52.914 [main] INFO  c.a.d.p.DruidDataSource - [init,1009] - {dataSource-1,master} inited
10:29:52.915 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,160] - dynamic-datasource - add a datasource named [master] success
10:29:52.916 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [afterPropertiesSet,243] - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
10:29:54.464 [main] INFO  c.a.c.s.SentinelWebMvcConfigurer - [addInterceptors,52] - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
10:29:54.736 [main] INFO  c.r.f.tcp.TcpServer - [start,37] - TCP服务器已禁用
10:29:55.823 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9202"]
10:29:56.225 [main] INFO  c.r.s.RuoYiSystemApplication - [logStarted,61] - Started RuoYiSystemApplication in 7.69 seconds (JVM running for 9.362)
10:29:56.948 [RMI TCP Connection(1)-192.168.43.78] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
10:29:56.950 [RMI TCP Connection(1)-192.168.43.78] INFO  o.s.w.s.DispatcherServlet - [initServletBean,525] - Initializing Servlet 'dispatcherServlet'
10:29:56.953 [RMI TCP Connection(1)-192.168.43.78] INFO  o.s.w.s.DispatcherServlet - [initServletBean,547] - Completed initialization in 3 ms
11:28:30.790 [main] INFO  c.a.n.c.e.SearchableProperties - [sortPropertySourceDefaultOrder,197] - properties search order:PROPERTIES->JVM->ENV->DEFAULT_SETTING
11:28:30.842 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
11:28:31.185 [main] INFO  c.a.n.p.a.s.c.ClientAuthPluginManager - [init,56] - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.impl.NacosClientAuthServiceImpl success.
11:28:31.186 [main] INFO  c.a.n.p.a.s.c.ClientAuthPluginManager - [init,56] - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.ram.RamClientAuthServiceImpl success.
11:28:33.062 [main] INFO  c.r.s.RuoYiSystemApplication - [logStartupProfileInfo,638] - The following 1 profile is active: "dev"
11:28:34.966 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9201"]
11:28:34.969 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
11:28:34.969 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.83]
11:28:35.164 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
11:28:36.232 [main] INFO  c.a.d.p.DruidDataSource - [init,1009] - {dataSource-1,master} inited
11:28:36.234 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,160] - dynamic-datasource - add a datasource named [master] success
11:28:36.235 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [afterPropertiesSet,243] - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
11:28:39.029 [main] INFO  c.a.c.s.SentinelWebMvcConfigurer - [addInterceptors,52] - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
11:28:43.428 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9201"]
11:28:43.459 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,217] - dynamic-datasource start closing ....
11:28:43.464 [main] INFO  c.a.d.p.DruidDataSource - [close,2174] - {dataSource-1} closing ...
11:28:43.470 [main] INFO  c.a.d.p.DruidDataSource - [close,2247] - {dataSource-1} closed
11:28:43.471 [main] INFO  c.b.d.d.d.DefaultDataSourceDestroyer - [destroy,98] - dynamic-datasource close the datasource named [master] success,
11:28:43.471 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,221] - dynamic-datasource all closed success,bye
11:28:43.474 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Pausing ProtocolHandler ["http-nio-9201"]
11:28:43.475 [main] INFO  o.a.c.c.StandardService - [log,173] - Stopping service [Tomcat]
11:28:43.491 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Stopping ProtocolHandler ["http-nio-9201"]
11:28:43.495 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Destroying ProtocolHandler ["http-nio-9201"]
11:32:48.495 [main] INFO  c.a.n.c.e.SearchableProperties - [sortPropertySourceDefaultOrder,197] - properties search order:PROPERTIES->JVM->ENV->DEFAULT_SETTING
11:32:48.534 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
11:32:48.833 [main] INFO  c.a.n.p.a.s.c.ClientAuthPluginManager - [init,56] - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.impl.NacosClientAuthServiceImpl success.
11:32:48.833 [main] INFO  c.a.n.p.a.s.c.ClientAuthPluginManager - [init,56] - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.ram.RamClientAuthServiceImpl success.
11:32:50.603 [main] INFO  c.r.s.RuoYiSystemApplication - [logStartupProfileInfo,638] - The following 1 profile is active: "dev"
11:32:53.077 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9201"]
11:32:53.079 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
11:32:53.079 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.83]
11:32:53.301 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
11:32:54.426 [main] INFO  c.a.d.p.DruidDataSource - [init,1009] - {dataSource-1,master} inited
11:32:54.427 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,160] - dynamic-datasource - add a datasource named [master] success
11:32:54.428 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [afterPropertiesSet,243] - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
11:32:58.763 [main] INFO  c.a.c.s.SentinelWebMvcConfigurer - [addInterceptors,52] - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
11:33:02.935 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9201"]
11:33:02.974 [main] INFO  c.a.n.p.a.s.c.ClientAuthPluginManager - [init,56] - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.impl.NacosClientAuthServiceImpl success.
11:33:02.974 [main] INFO  c.a.n.p.a.s.c.ClientAuthPluginManager - [init,56] - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.ram.RamClientAuthServiceImpl success.
11:33:03.159 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,76] - nacos registry, DEFAULT_GROUP ruoyi-system 192.168.43.78:9201 register finished
11:33:04.151 [main] INFO  c.r.s.RuoYiSystemApplication - [logStarted,61] - Started RuoYiSystemApplication in 17.127 seconds (JVM running for 18.959)
11:33:04.179 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=ruoyi-system, group=DEFAULT_GROUP
11:33:04.180 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=ruoyi-system.yml, group=DEFAULT_GROUP
11:33:04.182 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=ruoyi-system-dev.yml, group=DEFAULT_GROUP
11:33:04.644 [RMI TCP Connection(1)-192.168.43.78] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
11:57:55.378 [main] INFO  c.a.n.c.e.SearchableProperties - [sortPropertySourceDefaultOrder,197] - properties search order:PROPERTIES->JVM->ENV->DEFAULT_SETTING
11:57:55.433 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
11:57:55.808 [main] INFO  c.a.n.p.a.s.c.ClientAuthPluginManager - [init,56] - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.impl.NacosClientAuthServiceImpl success.
11:57:55.808 [main] INFO  c.a.n.p.a.s.c.ClientAuthPluginManager - [init,56] - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.ram.RamClientAuthServiceImpl success.
11:57:57.786 [main] INFO  c.r.s.RuoYiSystemApplication - [logStartupProfileInfo,638] - The following 1 profile is active: "dev"
11:58:02.855 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9201"]
11:58:02.858 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
11:58:02.858 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.83]
11:58:03.102 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
11:58:04.287 [main] INFO  c.a.d.p.DruidDataSource - [init,1009] - {dataSource-1,master} inited
11:58:04.289 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,160] - dynamic-datasource - add a datasource named [master] success
11:58:04.289 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [afterPropertiesSet,243] - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
11:58:07.894 [main] INFO  c.r.f.tcp.TcpServer - [start,49] - 🚀 TCP服务器启动成功，监听端口: 9999
11:58:09.838 [main] INFO  c.a.c.s.SentinelWebMvcConfigurer - [addInterceptors,52] - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
11:58:14.409 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9201"]
11:58:14.457 [main] INFO  c.a.n.p.a.s.c.ClientAuthPluginManager - [init,56] - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.impl.NacosClientAuthServiceImpl success.
11:58:14.457 [main] INFO  c.a.n.p.a.s.c.ClientAuthPluginManager - [init,56] - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.ram.RamClientAuthServiceImpl success.
11:58:14.609 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,76] - nacos registry, DEFAULT_GROUP ruoyi-system 192.168.43.78:9201 register finished
11:58:16.024 [main] INFO  c.r.s.RuoYiSystemApplication - [logStarted,61] - Started RuoYiSystemApplication in 22.349 seconds (JVM running for 25.936)
11:58:16.055 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=ruoyi-system, group=DEFAULT_GROUP
11:58:16.056 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=ruoyi-system.yml, group=DEFAULT_GROUP
11:58:16.058 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=ruoyi-system-dev.yml, group=DEFAULT_GROUP
11:58:17.358 [RMI TCP Connection(5)-192.168.43.78] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
12:15:22.918 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,95] - De-registering from Nacos Server now...
12:15:22.932 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,115] - De-registration finished.
12:15:22.987 [SpringApplicationShutdownHook] INFO  c.r.f.tcp.TcpServer - [stop,83] - 🛑 TCP服务器已关闭
12:15:22.988 [SpringApplicationShutdownHook] INFO  c.r.f.tcp.TcpServer - [stop,88] - 🛑 TCP线程池已关闭
12:15:23.154 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,217] - dynamic-datasource start closing ....
12:15:23.164 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2174] - {dataSource-1} closing ...
12:15:23.180 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2247] - {dataSource-1} closed
12:15:23.181 [SpringApplicationShutdownHook] INFO  c.b.d.d.d.DefaultDataSourceDestroyer - [destroy,98] - dynamic-datasource close the datasource named [master] success,
12:15:23.181 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,221] - dynamic-datasource all closed success,bye
12:15:30.079 [main] INFO  c.a.n.c.e.SearchableProperties - [sortPropertySourceDefaultOrder,197] - properties search order:PROPERTIES->JVM->ENV->DEFAULT_SETTING
12:15:30.112 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
12:15:30.418 [main] INFO  c.a.n.p.a.s.c.ClientAuthPluginManager - [init,56] - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.impl.NacosClientAuthServiceImpl success.
12:15:30.419 [main] INFO  c.a.n.p.a.s.c.ClientAuthPluginManager - [init,56] - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.ram.RamClientAuthServiceImpl success.
12:15:32.281 [main] INFO  c.r.s.RuoYiSystemApplication - [logStartupProfileInfo,638] - The following 1 profile is active: "dev"
12:15:34.085 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9201"]
12:15:34.086 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
12:15:34.087 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.83]
12:15:34.199 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
12:15:34.797 [main] INFO  c.a.d.p.DruidDataSource - [init,1009] - {dataSource-1,master} inited
12:15:34.798 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,160] - dynamic-datasource - add a datasource named [master] success
12:15:34.798 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [afterPropertiesSet,243] - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
12:15:36.304 [main] INFO  c.r.f.tcp.TcpServer - [start,49] - 🚀 TCP服务器启动成功，监听端口: 9999
12:15:37.271 [main] INFO  c.a.c.s.SentinelWebMvcConfigurer - [addInterceptors,52] - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
12:15:39.179 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9201"]
12:15:39.202 [main] INFO  c.a.n.p.a.s.c.ClientAuthPluginManager - [init,56] - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.impl.NacosClientAuthServiceImpl success.
12:15:39.203 [main] INFO  c.a.n.p.a.s.c.ClientAuthPluginManager - [init,56] - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.ram.RamClientAuthServiceImpl success.
12:15:39.341 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,76] - nacos registry, DEFAULT_GROUP ruoyi-system 192.168.43.78:9201 register finished
12:15:39.351 [http-nio-9201-exec-1] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
12:15:39.429 [http-nio-9201-exec-1] INFO  c.r.f.t.WebSocketToTcpProxy - [onOpen,34] - 📡 TBOX模拟器WebSocket连接建立: 0
12:15:40.024 [main] INFO  c.r.s.RuoYiSystemApplication - [logStarted,61] - Started RuoYiSystemApplication in 11.504 seconds (JVM running for 13.31)
12:15:40.041 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=ruoyi-system, group=DEFAULT_GROUP
12:15:40.041 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=ruoyi-system.yml, group=DEFAULT_GROUP
12:15:40.042 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=ruoyi-system-dev.yml, group=DEFAULT_GROUP
12:15:42.634 [http-nio-9201-exec-3] INFO  c.r.f.t.WebSocketToTcpProxy - [onClose,50] - 📡 TBOX模拟器WebSocket连接关闭: 0
12:15:43.058 [http-nio-9201-exec-5] INFO  c.r.f.t.WebSocketToTcpProxy - [onOpen,34] - 📡 TBOX模拟器WebSocket连接建立: 1
12:15:48.147 [http-nio-9201-exec-8] INFO  c.r.f.t.WebSocketToTcpProxy - [onClose,50] - 📡 TBOX模拟器WebSocket连接关闭: 1
12:15:48.359 [http-nio-9201-exec-9] INFO  c.r.f.t.WebSocketToTcpProxy - [onOpen,34] - 📡 TBOX模拟器WebSocket连接建立: 2
12:15:53.907 [http-nio-9201-exec-1] INFO  c.r.f.t.WebSocketToTcpProxy - [onClose,50] - 📡 TBOX模拟器WebSocket连接关闭: 2
12:15:54.118 [http-nio-9201-exec-3] INFO  c.r.f.t.WebSocketToTcpProxy - [onOpen,34] - 📡 TBOX模拟器WebSocket连接建立: 3
12:15:57.267 [http-nio-9201-exec-6] INFO  c.r.f.t.WebSocketToTcpProxy - [onMessage,79] - 📡 收到TBOX模拟器指令: {"action":"connect","host":"localhost","port":9999}
12:15:57.317 [http-nio-9201-exec-6] INFO  c.r.f.t.WebSocketToTcpProxy - [handleConnect,116] - 🔌 TBOX模拟器尝试连接TCP服务器: localhost:9999
12:15:57.319 [pool-6-thread-1] INFO  c.r.f.tcp.TcpServer - [acceptConnections,63] - 📱 新的TBOX客户端连接: /127.0.0.1:53490
12:15:57.324 [pool-6-thread-2] INFO  c.r.f.t.TcpClientHandler - [run,49] - 🔄 开始处理TBOX客户端: /127.0.0.1:53490
12:15:57.325 [http-nio-9201-exec-6] INFO  c.r.f.t.WebSocketToTcpProxy - [handleConnect,127] - ✅ TBOX模拟器TCP连接建立成功
12:16:00.907 [http-nio-9201-exec-4] INFO  c.r.f.t.WebSocketToTcpProxy - [onClose,50] - 📡 TBOX模拟器WebSocket连接关闭: 3
12:16:00.908 [pool-6-thread-2] INFO  c.r.f.t.TcpClientHandler - [closeConnection,153] - 🔌 TBOX客户端连接已关闭: /127.0.0.1:53490
12:16:01.123 [http-nio-9201-exec-7] INFO  c.r.f.t.WebSocketToTcpProxy - [onOpen,34] - 📡 TBOX模拟器WebSocket连接建立: 4
12:16:02.639 [http-nio-9201-exec-9] INFO  c.r.f.t.WebSocketToTcpProxy - [onMessage,79] - 📡 收到TBOX模拟器指令: {"action":"connect","host":"localhost","port":9999}
12:16:02.639 [http-nio-9201-exec-9] INFO  c.r.f.t.WebSocketToTcpProxy - [handleConnect,116] - 🔌 TBOX模拟器尝试连接TCP服务器: localhost:9999
12:16:02.640 [pool-6-thread-1] INFO  c.r.f.tcp.TcpServer - [acceptConnections,63] - 📱 新的TBOX客户端连接: /127.0.0.1:53504
12:16:02.642 [pool-6-thread-2] INFO  c.r.f.t.TcpClientHandler - [run,49] - 🔄 开始处理TBOX客户端: /127.0.0.1:53504
12:16:02.642 [http-nio-9201-exec-9] INFO  c.r.f.t.WebSocketToTcpProxy - [handleConnect,127] - ✅ TBOX模拟器TCP连接建立成功
12:16:09.708 [http-nio-9201-exec-2] INFO  c.r.f.t.WebSocketToTcpProxy - [onMessage,79] - 📡 收到TBOX模拟器指令: {"action":"heartbeat"}
12:16:09.709 [http-nio-9201-exec-2] INFO  c.r.f.t.WebSocketToTcpProxy - [sendMessage,223] - 📤 TBOX模拟器发送TCP消息: 7E000A000A000102030405060708090A000030333032303030313030007E
12:16:09.710 [pool-6-thread-2] INFO  c.r.f.t.TcpClientHandler - [run,54] - 📨 收到TBOX消息: 7E000A000A000102030405060708090A000030333032303030313030007E from /127.0.0.1:53504
12:16:09.710 [pool-6-thread-2] INFO  c.r.f.t.TcpClientHandler - [handleTboxMessage,89] - 🚗 车辆状态更新: locked (车辆已闭锁)
12:16:09.711 [http-nio-9201-exec-2] INFO  c.r.f.t.WebSocketToTcpProxy - [handleHeartbeat,150] - 💓 TBOX模拟器发送心跳包
12:16:09.724 [pool-6-thread-2] INFO  c.r.f.t.TcpClientHandler - [notifyMobileApp,115] - 📤 已通知手机端车辆状态: 车辆已闭锁
12:16:12.001 [http-nio-9201-exec-1] INFO  c.r.f.t.WebSocketToTcpProxy - [onMessage,79] - 📡 收到TBOX模拟器指令: {"action":"heartbeat"}
12:16:12.002 [pool-6-thread-2] INFO  c.r.f.t.TcpClientHandler - [run,54] - 📨 收到TBOX消息: 7E000A000A000102030405060708090A000030333032303030313030007E from /127.0.0.1:53504
12:16:12.002 [http-nio-9201-exec-1] INFO  c.r.f.t.WebSocketToTcpProxy - [sendMessage,223] - 📤 TBOX模拟器发送TCP消息: 7E000A000A000102030405060708090A000030333032303030313030007E
12:16:12.003 [pool-6-thread-2] INFO  c.r.f.t.TcpClientHandler - [handleTboxMessage,89] - 🚗 车辆状态更新: locked (车辆已闭锁)
12:16:12.003 [pool-6-thread-2] INFO  c.r.f.t.TcpClientHandler - [notifyMobileApp,115] - 📤 已通知手机端车辆状态: 车辆已闭锁
12:16:12.003 [http-nio-9201-exec-1] INFO  c.r.f.t.WebSocketToTcpProxy - [handleHeartbeat,150] - 💓 TBOX模拟器发送心跳包
12:16:13.348 [http-nio-9201-exec-10] INFO  c.r.f.t.WebSocketToTcpProxy - [onMessage,79] - 📡 收到TBOX模拟器指令: {"action":"status"}
12:16:13.349 [pool-6-thread-2] INFO  c.r.f.t.TcpClientHandler - [run,54] - 📨 收到TBOX消息: 7E000A000A000102030405060708090A000030333032303030313034007E from /127.0.0.1:53504
12:16:13.349 [http-nio-9201-exec-10] INFO  c.r.f.t.WebSocketToTcpProxy - [sendMessage,223] - 📤 TBOX模拟器发送TCP消息: 7E000A000A000102030405060708090A000030333032303030313034007E
12:16:13.349 [pool-6-thread-2] INFO  c.r.f.t.TcpClientHandler - [handleTboxMessage,89] - 🚗 车辆状态更新: locked (车辆已闭锁)
12:16:13.349 [pool-6-thread-2] INFO  c.r.f.t.TcpClientHandler - [notifyMobileApp,115] - 📤 已通知手机端车辆状态: 车辆已闭锁
12:16:13.349 [http-nio-9201-exec-10] INFO  c.r.f.t.WebSocketToTcpProxy - [handleStatusReport,168] - 📊 TBOX模拟器发送状态报告
12:16:16.296 [http-nio-9201-exec-3] INFO  c.r.f.t.WebSocketToTcpProxy - [onMessage,79] - 📡 收到TBOX模拟器指令: {"action":"status"}
12:16:16.297 [http-nio-9201-exec-3] INFO  c.r.f.t.WebSocketToTcpProxy - [sendMessage,223] - 📤 TBOX模拟器发送TCP消息: 7E000A000A000102030405060708090A000030333032303030313034007E
12:16:16.297 [pool-6-thread-2] INFO  c.r.f.t.TcpClientHandler - [run,54] - 📨 收到TBOX消息: 7E000A000A000102030405060708090A000030333032303030313034007E from /127.0.0.1:53504
12:16:16.297 [pool-6-thread-2] INFO  c.r.f.t.TcpClientHandler - [handleTboxMessage,89] - 🚗 车辆状态更新: locked (车辆已闭锁)
12:16:16.298 [pool-6-thread-2] INFO  c.r.f.t.TcpClientHandler - [notifyMobileApp,115] - 📤 已通知手机端车辆状态: 车辆已闭锁
12:16:16.298 [http-nio-9201-exec-3] INFO  c.r.f.t.WebSocketToTcpProxy - [handleStatusReport,168] - 📊 TBOX模拟器发送状态报告
12:16:17.933 [http-nio-9201-exec-6] INFO  c.r.f.t.WebSocketToTcpProxy - [onMessage,79] - 📡 收到TBOX模拟器指令: {"action":"status"}
12:16:17.934 [http-nio-9201-exec-6] INFO  c.r.f.t.WebSocketToTcpProxy - [sendMessage,223] - 📤 TBOX模拟器发送TCP消息: 7E000A000A000102030405060708090A000030333032303030313034007E
12:16:17.934 [pool-6-thread-2] INFO  c.r.f.t.TcpClientHandler - [run,54] - 📨 收到TBOX消息: 7E000A000A000102030405060708090A000030333032303030313034007E from /127.0.0.1:53504
12:16:17.934 [pool-6-thread-2] INFO  c.r.f.t.TcpClientHandler - [handleTboxMessage,89] - 🚗 车辆状态更新: locked (车辆已闭锁)
12:16:17.934 [pool-6-thread-2] INFO  c.r.f.t.TcpClientHandler - [notifyMobileApp,115] - 📤 已通知手机端车辆状态: 车辆已闭锁
12:16:17.934 [http-nio-9201-exec-6] INFO  c.r.f.t.WebSocketToTcpProxy - [handleStatusReport,168] - 📊 TBOX模拟器发送状态报告
12:16:20.033 [http-nio-9201-exec-5] INFO  c.r.f.t.WebSocketToTcpProxy - [onMessage,79] - 📡 收到TBOX模拟器指令: {"action":"disconnect"}
12:16:20.035 [pool-6-thread-2] INFO  c.r.f.t.TcpClientHandler - [closeConnection,153] - 🔌 TBOX客户端连接已关闭: /127.0.0.1:53504
12:16:20.035 [http-nio-9201-exec-5] INFO  c.r.f.t.WebSocketToTcpProxy - [handleDisconnect,179] - 🔌 TBOX模拟器TCP连接已断开
12:16:21.499 [http-nio-9201-exec-8] INFO  c.r.f.t.WebSocketToTcpProxy - [onClose,50] - 📡 TBOX模拟器WebSocket连接关闭: 4
12:16:21.715 [http-nio-9201-exec-9] INFO  c.r.f.t.WebSocketToTcpProxy - [onOpen,34] - 📡 TBOX模拟器WebSocket连接建立: 5
13:09:35.000 [http-nio-9201-exec-10] INFO  c.r.f.t.WebSocketToTcpProxy - [onMessage,79] - 📡 收到TBOX模拟器指令: {"action":"connect","host":"localhost","port":9999}
13:09:35.000 [http-nio-9201-exec-10] INFO  c.r.f.t.WebSocketToTcpProxy - [handleConnect,116] - 🔌 TBOX模拟器尝试连接TCP服务器: localhost:9999
13:09:35.002 [pool-6-thread-1] INFO  c.r.f.tcp.TcpServer - [acceptConnections,63] - 📱 新的TBOX客户端连接: /127.0.0.1:62100
13:09:35.004 [http-nio-9201-exec-10] INFO  c.r.f.t.WebSocketToTcpProxy - [handleConnect,127] - ✅ TBOX模拟器TCP连接建立成功
13:09:35.004 [pool-6-thread-3] INFO  c.r.f.t.TcpClientHandler - [run,49] - 🔄 开始处理TBOX客户端: /127.0.0.1:62100
13:57:42.739 [http-nio-9201-exec-3] INFO  c.r.f.w.WebSocketServer - [onOpen,100] - 
 建立连接 - org.apache.tomcat.websocket.WsSession@1ff4b86c
13:57:42.752 [http-nio-9201-exec-3] INFO  c.r.f.w.WebSocketServer - [onOpen,101] - 
 当前人数 - 1
13:57:42.909 [http-nio-9201-exec-3] INFO  c.r.f.w.WebSocketServer - [onClose,113] - 
 关闭连接 - org.apache.tomcat.websocket.WsSession@1ff4b86c
13:57:42.912 [http-nio-9201-exec-3] INFO  c.r.f.w.WebSocketUsers - [remove,117] - 
 正在移出用户 - 6
13:57:42.913 [http-nio-9201-exec-3] INFO  c.r.f.w.WebSocketUsers - [remove,122] - 
 移出结果 - 成功
13:57:42.915 [http-nio-9201-exec-3] INFO  c.r.f.w.WebSocketServer - [onError,132] - 
 连接异常 - 6
13:57:42.924 [http-nio-9201-exec-3] INFO  c.r.f.w.WebSocketServer - [onError,133] - 
 异常信息 - {}
java.lang.NullPointerException: null
	at java.base/java.util.concurrent.ConcurrentHashMap.get(ConcurrentHashMap.java:936)
	at com.ruoyi.framework.websocket.WebSocketUsers.getSessionByUserId(WebSocketUsers.java:148)
	at com.ruoyi.framework.websocket.WebSocketServer.onMessage(WebSocketServer.java:175)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:566)
	at org.apache.tomcat.websocket.pojo.PojoMessageHandlerWholeBase.onMessage(PojoMessageHandlerWholeBase.java:101)
	at org.apache.tomcat.websocket.WsFrameBase.sendMessageText(WsFrameBase.java:390)
	at org.apache.tomcat.websocket.server.WsFrameServer.sendMessageText(WsFrameServer.java:130)
	at org.apache.tomcat.websocket.WsFrameBase.processDataText(WsFrameBase.java:484)
	at org.apache.tomcat.websocket.WsFrameBase.processData(WsFrameBase.java:284)
	at org.apache.tomcat.websocket.WsFrameBase.processInputBuffer(WsFrameBase.java:130)
	at org.apache.tomcat.websocket.server.WsFrameServer.onDataAvailable(WsFrameServer.java:85)
	at org.apache.tomcat.websocket.server.WsFrameServer.doOnDataAvailable(WsFrameServer.java:184)
	at org.apache.tomcat.websocket.server.WsFrameServer.notifyDataAvailable(WsFrameServer.java:164)
	at org.apache.tomcat.websocket.server.WsHttpUpgradeHandler.upgradeDispatch(WsHttpUpgradeHandler.java:152)
	at org.apache.coyote.http11.upgrade.UpgradeProcessorInternal.dispatch(UpgradeProcessorInternal.java:60)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:57)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:928)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1794)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:52)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1191)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.base/java.lang.Thread.run(Thread.java:834)
13:57:42.925 [http-nio-9201-exec-3] INFO  c.r.f.w.WebSocketUsers - [remove,117] - 
 正在移出用户 - 6
13:57:42.925 [http-nio-9201-exec-3] INFO  c.r.f.w.WebSocketUsers - [removeUserSessions,96] - 
 正在移出用户Session映射 - 100
13:57:47.972 [http-nio-9201-exec-6] INFO  c.r.f.w.WebSocketServer - [onOpen,100] - 
 建立连接 - org.apache.tomcat.websocket.WsSession@4e85c37c
13:57:47.972 [http-nio-9201-exec-6] INFO  c.r.f.w.WebSocketServer - [onOpen,101] - 
 当前人数 - 1
13:57:47.978 [http-nio-9201-exec-5] INFO  c.r.f.w.WebSocketServer - [onClose,113] - 
 关闭连接 - org.apache.tomcat.websocket.WsSession@4e85c37c
13:57:47.978 [http-nio-9201-exec-5] INFO  c.r.f.w.WebSocketUsers - [remove,117] - 
 正在移出用户 - 7
13:57:47.978 [http-nio-9201-exec-5] INFO  c.r.f.w.WebSocketUsers - [remove,122] - 
 移出结果 - 成功
13:57:47.979 [http-nio-9201-exec-5] INFO  c.r.f.w.WebSocketServer - [onError,132] - 
 连接异常 - 7
13:57:47.979 [http-nio-9201-exec-5] INFO  c.r.f.w.WebSocketServer - [onError,133] - 
 异常信息 - {}
java.lang.NullPointerException: null
	at java.base/java.util.concurrent.ConcurrentHashMap.get(ConcurrentHashMap.java:936)
	at com.ruoyi.framework.websocket.WebSocketUsers.getSessionByUserId(WebSocketUsers.java:148)
	at com.ruoyi.framework.websocket.WebSocketServer.onMessage(WebSocketServer.java:175)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:566)
	at org.apache.tomcat.websocket.pojo.PojoMessageHandlerWholeBase.onMessage(PojoMessageHandlerWholeBase.java:101)
	at org.apache.tomcat.websocket.WsFrameBase.sendMessageText(WsFrameBase.java:390)
	at org.apache.tomcat.websocket.server.WsFrameServer.sendMessageText(WsFrameServer.java:130)
	at org.apache.tomcat.websocket.WsFrameBase.processDataText(WsFrameBase.java:484)
	at org.apache.tomcat.websocket.WsFrameBase.processData(WsFrameBase.java:284)
	at org.apache.tomcat.websocket.WsFrameBase.processInputBuffer(WsFrameBase.java:130)
	at org.apache.tomcat.websocket.server.WsFrameServer.onDataAvailable(WsFrameServer.java:85)
	at org.apache.tomcat.websocket.server.WsFrameServer.doOnDataAvailable(WsFrameServer.java:184)
	at org.apache.tomcat.websocket.server.WsFrameServer.notifyDataAvailable(WsFrameServer.java:164)
	at org.apache.tomcat.websocket.server.WsHttpUpgradeHandler.upgradeDispatch(WsHttpUpgradeHandler.java:152)
	at org.apache.coyote.http11.upgrade.UpgradeProcessorInternal.dispatch(UpgradeProcessorInternal.java:60)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:57)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:928)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1794)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:52)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1191)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.base/java.lang.Thread.run(Thread.java:834)
13:57:47.981 [http-nio-9201-exec-5] INFO  c.r.f.w.WebSocketUsers - [remove,117] - 
 正在移出用户 - 7
13:57:47.981 [http-nio-9201-exec-5] INFO  c.r.f.w.WebSocketUsers - [removeUserSessions,96] - 
 正在移出用户Session映射 - 100
13:57:53.010 [http-nio-9201-exec-4] INFO  c.r.f.w.WebSocketServer - [onOpen,100] - 
 建立连接 - org.apache.tomcat.websocket.WsSession@9523cf
13:57:53.011 [http-nio-9201-exec-4] INFO  c.r.f.w.WebSocketServer - [onOpen,101] - 
 当前人数 - 1
13:57:53.016 [http-nio-9201-exec-8] INFO  c.r.f.w.WebSocketServer - [onClose,113] - 
 关闭连接 - org.apache.tomcat.websocket.WsSession@9523cf
13:57:53.016 [http-nio-9201-exec-8] INFO  c.r.f.w.WebSocketUsers - [remove,117] - 
 正在移出用户 - 8
13:57:53.016 [http-nio-9201-exec-8] INFO  c.r.f.w.WebSocketUsers - [remove,122] - 
 移出结果 - 成功
13:57:53.016 [http-nio-9201-exec-8] INFO  c.r.f.w.WebSocketServer - [onError,132] - 
 连接异常 - 8
13:57:53.017 [http-nio-9201-exec-8] INFO  c.r.f.w.WebSocketServer - [onError,133] - 
 异常信息 - {}
java.lang.NullPointerException: null
	at java.base/java.util.concurrent.ConcurrentHashMap.get(ConcurrentHashMap.java:936)
	at com.ruoyi.framework.websocket.WebSocketUsers.getSessionByUserId(WebSocketUsers.java:148)
	at com.ruoyi.framework.websocket.WebSocketServer.onMessage(WebSocketServer.java:175)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:566)
	at org.apache.tomcat.websocket.pojo.PojoMessageHandlerWholeBase.onMessage(PojoMessageHandlerWholeBase.java:101)
	at org.apache.tomcat.websocket.WsFrameBase.sendMessageText(WsFrameBase.java:390)
	at org.apache.tomcat.websocket.server.WsFrameServer.sendMessageText(WsFrameServer.java:130)
	at org.apache.tomcat.websocket.WsFrameBase.processDataText(WsFrameBase.java:484)
	at org.apache.tomcat.websocket.WsFrameBase.processData(WsFrameBase.java:284)
	at org.apache.tomcat.websocket.WsFrameBase.processInputBuffer(WsFrameBase.java:130)
	at org.apache.tomcat.websocket.server.WsFrameServer.onDataAvailable(WsFrameServer.java:85)
	at org.apache.tomcat.websocket.server.WsFrameServer.doOnDataAvailable(WsFrameServer.java:184)
	at org.apache.tomcat.websocket.server.WsFrameServer.notifyDataAvailable(WsFrameServer.java:164)
	at org.apache.tomcat.websocket.server.WsHttpUpgradeHandler.upgradeDispatch(WsHttpUpgradeHandler.java:152)
	at org.apache.coyote.http11.upgrade.UpgradeProcessorInternal.dispatch(UpgradeProcessorInternal.java:60)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:57)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:928)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1794)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:52)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1191)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.base/java.lang.Thread.run(Thread.java:834)
13:57:53.018 [http-nio-9201-exec-8] INFO  c.r.f.w.WebSocketUsers - [remove,117] - 
 正在移出用户 - 8
13:57:53.018 [http-nio-9201-exec-8] INFO  c.r.f.w.WebSocketUsers - [removeUserSessions,96] - 
 正在移出用户Session映射 - 100
13:57:58.050 [http-nio-9201-exec-7] INFO  c.r.f.w.WebSocketServer - [onOpen,100] - 
 建立连接 - org.apache.tomcat.websocket.WsSession@674af22c
13:57:58.051 [http-nio-9201-exec-7] INFO  c.r.f.w.WebSocketServer - [onOpen,101] - 
 当前人数 - 1
13:57:58.053 [http-nio-9201-exec-7] INFO  c.r.f.w.WebSocketServer - [onClose,113] - 
 关闭连接 - org.apache.tomcat.websocket.WsSession@674af22c
13:57:58.054 [http-nio-9201-exec-7] INFO  c.r.f.w.WebSocketUsers - [remove,117] - 
 正在移出用户 - 9
13:57:58.054 [http-nio-9201-exec-7] INFO  c.r.f.w.WebSocketUsers - [remove,122] - 
 移出结果 - 成功
13:57:58.054 [http-nio-9201-exec-7] INFO  c.r.f.w.WebSocketServer - [onError,132] - 
 连接异常 - 9
13:57:58.054 [http-nio-9201-exec-7] INFO  c.r.f.w.WebSocketServer - [onError,133] - 
 异常信息 - {}
java.lang.NullPointerException: null
	at java.base/java.util.concurrent.ConcurrentHashMap.get(ConcurrentHashMap.java:936)
	at com.ruoyi.framework.websocket.WebSocketUsers.getSessionByUserId(WebSocketUsers.java:148)
	at com.ruoyi.framework.websocket.WebSocketServer.onMessage(WebSocketServer.java:175)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:566)
	at org.apache.tomcat.websocket.pojo.PojoMessageHandlerWholeBase.onMessage(PojoMessageHandlerWholeBase.java:101)
	at org.apache.tomcat.websocket.WsFrameBase.sendMessageText(WsFrameBase.java:390)
	at org.apache.tomcat.websocket.server.WsFrameServer.sendMessageText(WsFrameServer.java:130)
	at org.apache.tomcat.websocket.WsFrameBase.processDataText(WsFrameBase.java:484)
	at org.apache.tomcat.websocket.WsFrameBase.processData(WsFrameBase.java:284)
	at org.apache.tomcat.websocket.WsFrameBase.processInputBuffer(WsFrameBase.java:130)
	at org.apache.tomcat.websocket.server.WsFrameServer.onDataAvailable(WsFrameServer.java:85)
	at org.apache.tomcat.websocket.server.WsFrameServer.doOnDataAvailable(WsFrameServer.java:184)
	at org.apache.tomcat.websocket.server.WsFrameServer.notifyDataAvailable(WsFrameServer.java:164)
	at org.apache.tomcat.websocket.server.WsHttpUpgradeHandler.upgradeDispatch(WsHttpUpgradeHandler.java:152)
	at org.apache.coyote.http11.upgrade.UpgradeProcessorInternal.dispatch(UpgradeProcessorInternal.java:60)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:57)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:928)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1794)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:52)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1191)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.base/java.lang.Thread.run(Thread.java:834)
13:57:58.055 [http-nio-9201-exec-7] INFO  c.r.f.w.WebSocketUsers - [remove,117] - 
 正在移出用户 - 9
13:57:58.055 [http-nio-9201-exec-7] INFO  c.r.f.w.WebSocketUsers - [removeUserSessions,96] - 
 正在移出用户Session映射 - 100
13:58:03.084 [http-nio-9201-exec-9] INFO  c.r.f.w.WebSocketServer - [onOpen,100] - 
 建立连接 - org.apache.tomcat.websocket.WsSession@14997385
13:58:03.085 [http-nio-9201-exec-9] INFO  c.r.f.w.WebSocketServer - [onOpen,101] - 
 当前人数 - 1
13:58:03.088 [http-nio-9201-exec-2] INFO  c.r.f.w.WebSocketServer - [onClose,113] - 
 关闭连接 - org.apache.tomcat.websocket.WsSession@14997385
13:58:03.088 [http-nio-9201-exec-2] INFO  c.r.f.w.WebSocketUsers - [remove,117] - 
 正在移出用户 - a
13:58:03.088 [http-nio-9201-exec-2] INFO  c.r.f.w.WebSocketUsers - [remove,122] - 
 移出结果 - 成功
13:58:03.088 [http-nio-9201-exec-2] INFO  c.r.f.w.WebSocketServer - [onError,132] - 
 连接异常 - a
13:58:03.089 [http-nio-9201-exec-2] INFO  c.r.f.w.WebSocketServer - [onError,133] - 
 异常信息 - {}
java.lang.NullPointerException: null
	at java.base/java.util.concurrent.ConcurrentHashMap.get(ConcurrentHashMap.java:936)
	at com.ruoyi.framework.websocket.WebSocketUsers.getSessionByUserId(WebSocketUsers.java:148)
	at com.ruoyi.framework.websocket.WebSocketServer.onMessage(WebSocketServer.java:175)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:566)
	at org.apache.tomcat.websocket.pojo.PojoMessageHandlerWholeBase.onMessage(PojoMessageHandlerWholeBase.java:101)
	at org.apache.tomcat.websocket.WsFrameBase.sendMessageText(WsFrameBase.java:390)
	at org.apache.tomcat.websocket.server.WsFrameServer.sendMessageText(WsFrameServer.java:130)
	at org.apache.tomcat.websocket.WsFrameBase.processDataText(WsFrameBase.java:484)
	at org.apache.tomcat.websocket.WsFrameBase.processData(WsFrameBase.java:284)
	at org.apache.tomcat.websocket.WsFrameBase.processInputBuffer(WsFrameBase.java:130)
	at org.apache.tomcat.websocket.server.WsFrameServer.onDataAvailable(WsFrameServer.java:85)
	at org.apache.tomcat.websocket.server.WsFrameServer.doOnDataAvailable(WsFrameServer.java:184)
	at org.apache.tomcat.websocket.server.WsFrameServer.notifyDataAvailable(WsFrameServer.java:164)
	at org.apache.tomcat.websocket.server.WsHttpUpgradeHandler.upgradeDispatch(WsHttpUpgradeHandler.java:152)
	at org.apache.coyote.http11.upgrade.UpgradeProcessorInternal.dispatch(UpgradeProcessorInternal.java:60)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:57)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:928)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1794)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:52)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1191)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.base/java.lang.Thread.run(Thread.java:834)
13:58:03.090 [http-nio-9201-exec-2] INFO  c.r.f.w.WebSocketUsers - [remove,117] - 
 正在移出用户 - a
13:58:03.090 [http-nio-9201-exec-2] INFO  c.r.f.w.WebSocketUsers - [removeUserSessions,96] - 
 正在移出用户Session映射 - 100
13:58:08.122 [http-nio-9201-exec-1] INFO  c.r.f.w.WebSocketServer - [onOpen,100] - 
 建立连接 - org.apache.tomcat.websocket.WsSession@268c7878
13:58:08.122 [http-nio-9201-exec-1] INFO  c.r.f.w.WebSocketServer - [onOpen,101] - 
 当前人数 - 1
13:58:08.126 [http-nio-9201-exec-10] INFO  c.r.f.w.WebSocketServer - [onClose,113] - 
 关闭连接 - org.apache.tomcat.websocket.WsSession@268c7878
13:58:08.127 [http-nio-9201-exec-10] INFO  c.r.f.w.WebSocketUsers - [remove,117] - 
 正在移出用户 - b
13:58:08.127 [http-nio-9201-exec-10] INFO  c.r.f.w.WebSocketUsers - [remove,122] - 
 移出结果 - 成功
13:58:08.127 [http-nio-9201-exec-10] INFO  c.r.f.w.WebSocketServer - [onError,132] - 
 连接异常 - b
13:58:08.128 [http-nio-9201-exec-10] INFO  c.r.f.w.WebSocketServer - [onError,133] - 
 异常信息 - {}
java.lang.NullPointerException: null
	at java.base/java.util.concurrent.ConcurrentHashMap.get(ConcurrentHashMap.java:936)
	at com.ruoyi.framework.websocket.WebSocketUsers.getSessionByUserId(WebSocketUsers.java:148)
	at com.ruoyi.framework.websocket.WebSocketServer.onMessage(WebSocketServer.java:175)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:566)
	at org.apache.tomcat.websocket.pojo.PojoMessageHandlerWholeBase.onMessage(PojoMessageHandlerWholeBase.java:101)
	at org.apache.tomcat.websocket.WsFrameBase.sendMessageText(WsFrameBase.java:390)
	at org.apache.tomcat.websocket.server.WsFrameServer.sendMessageText(WsFrameServer.java:130)
	at org.apache.tomcat.websocket.WsFrameBase.processDataText(WsFrameBase.java:484)
	at org.apache.tomcat.websocket.WsFrameBase.processData(WsFrameBase.java:284)
	at org.apache.tomcat.websocket.WsFrameBase.processInputBuffer(WsFrameBase.java:130)
	at org.apache.tomcat.websocket.server.WsFrameServer.onDataAvailable(WsFrameServer.java:85)
	at org.apache.tomcat.websocket.server.WsFrameServer.doOnDataAvailable(WsFrameServer.java:184)
	at org.apache.tomcat.websocket.server.WsFrameServer.notifyDataAvailable(WsFrameServer.java:164)
	at org.apache.tomcat.websocket.server.WsHttpUpgradeHandler.upgradeDispatch(WsHttpUpgradeHandler.java:152)
	at org.apache.coyote.http11.upgrade.UpgradeProcessorInternal.dispatch(UpgradeProcessorInternal.java:60)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:57)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:928)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1794)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:52)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1191)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.base/java.lang.Thread.run(Thread.java:834)
13:58:08.128 [http-nio-9201-exec-10] INFO  c.r.f.w.WebSocketUsers - [remove,117] - 
 正在移出用户 - b
13:58:08.129 [http-nio-9201-exec-10] INFO  c.r.f.w.WebSocketUsers - [removeUserSessions,96] - 
 正在移出用户Session映射 - 100
13:58:14.048 [http-nio-9201-exec-3] INFO  c.r.f.w.WebSocketServer - [onOpen,100] - 
 建立连接 - org.apache.tomcat.websocket.WsSession@29f96e5a
13:58:14.048 [http-nio-9201-exec-3] INFO  c.r.f.w.WebSocketServer - [onOpen,101] - 
 当前人数 - 1
13:58:14.053 [http-nio-9201-exec-6] INFO  c.r.f.w.WebSocketServer - [onClose,113] - 
 关闭连接 - org.apache.tomcat.websocket.WsSession@29f96e5a
13:58:14.053 [http-nio-9201-exec-6] INFO  c.r.f.w.WebSocketUsers - [remove,117] - 
 正在移出用户 - c
13:58:14.053 [http-nio-9201-exec-6] INFO  c.r.f.w.WebSocketUsers - [remove,122] - 
 移出结果 - 成功
13:58:14.053 [http-nio-9201-exec-6] INFO  c.r.f.w.WebSocketServer - [onError,132] - 
 连接异常 - c
13:58:14.054 [http-nio-9201-exec-6] INFO  c.r.f.w.WebSocketServer - [onError,133] - 
 异常信息 - {}
java.lang.NullPointerException: null
	at java.base/java.util.concurrent.ConcurrentHashMap.get(ConcurrentHashMap.java:936)
	at com.ruoyi.framework.websocket.WebSocketUsers.getSessionByUserId(WebSocketUsers.java:148)
	at com.ruoyi.framework.websocket.WebSocketServer.onMessage(WebSocketServer.java:175)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:566)
	at org.apache.tomcat.websocket.pojo.PojoMessageHandlerWholeBase.onMessage(PojoMessageHandlerWholeBase.java:101)
	at org.apache.tomcat.websocket.WsFrameBase.sendMessageText(WsFrameBase.java:390)
	at org.apache.tomcat.websocket.server.WsFrameServer.sendMessageText(WsFrameServer.java:130)
	at org.apache.tomcat.websocket.WsFrameBase.processDataText(WsFrameBase.java:484)
	at org.apache.tomcat.websocket.WsFrameBase.processData(WsFrameBase.java:284)
	at org.apache.tomcat.websocket.WsFrameBase.processInputBuffer(WsFrameBase.java:130)
	at org.apache.tomcat.websocket.server.WsFrameServer.onDataAvailable(WsFrameServer.java:85)
	at org.apache.tomcat.websocket.server.WsFrameServer.doOnDataAvailable(WsFrameServer.java:184)
	at org.apache.tomcat.websocket.server.WsFrameServer.notifyDataAvailable(WsFrameServer.java:164)
	at org.apache.tomcat.websocket.server.WsHttpUpgradeHandler.upgradeDispatch(WsHttpUpgradeHandler.java:152)
	at org.apache.coyote.http11.upgrade.UpgradeProcessorInternal.dispatch(UpgradeProcessorInternal.java:60)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:57)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:928)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1794)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:52)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1191)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.base/java.lang.Thread.run(Thread.java:834)
13:58:14.055 [http-nio-9201-exec-6] INFO  c.r.f.w.WebSocketUsers - [remove,117] - 
 正在移出用户 - c
13:58:14.055 [http-nio-9201-exec-6] INFO  c.r.f.w.WebSocketUsers - [removeUserSessions,96] - 
 正在移出用户Session映射 - 100
13:58:19.093 [http-nio-9201-exec-5] INFO  c.r.f.w.WebSocketServer - [onOpen,100] - 
 建立连接 - org.apache.tomcat.websocket.WsSession@41f691bd
13:58:19.093 [http-nio-9201-exec-5] INFO  c.r.f.w.WebSocketServer - [onOpen,101] - 
 当前人数 - 1
13:58:19.102 [http-nio-9201-exec-4] INFO  c.r.f.w.WebSocketServer - [onClose,113] - 
 关闭连接 - org.apache.tomcat.websocket.WsSession@41f691bd
13:58:19.102 [http-nio-9201-exec-4] INFO  c.r.f.w.WebSocketUsers - [remove,117] - 
 正在移出用户 - d
13:58:19.103 [http-nio-9201-exec-4] INFO  c.r.f.w.WebSocketUsers - [remove,122] - 
 移出结果 - 成功
13:58:19.103 [http-nio-9201-exec-4] INFO  c.r.f.w.WebSocketServer - [onError,132] - 
 连接异常 - d
13:58:19.104 [http-nio-9201-exec-4] INFO  c.r.f.w.WebSocketServer - [onError,133] - 
 异常信息 - {}
java.lang.NullPointerException: null
	at java.base/java.util.concurrent.ConcurrentHashMap.get(ConcurrentHashMap.java:936)
	at com.ruoyi.framework.websocket.WebSocketUsers.getSessionByUserId(WebSocketUsers.java:148)
	at com.ruoyi.framework.websocket.WebSocketServer.onMessage(WebSocketServer.java:175)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:566)
	at org.apache.tomcat.websocket.pojo.PojoMessageHandlerWholeBase.onMessage(PojoMessageHandlerWholeBase.java:101)
	at org.apache.tomcat.websocket.WsFrameBase.sendMessageText(WsFrameBase.java:390)
	at org.apache.tomcat.websocket.server.WsFrameServer.sendMessageText(WsFrameServer.java:130)
	at org.apache.tomcat.websocket.WsFrameBase.processDataText(WsFrameBase.java:484)
	at org.apache.tomcat.websocket.WsFrameBase.processData(WsFrameBase.java:284)
	at org.apache.tomcat.websocket.WsFrameBase.processInputBuffer(WsFrameBase.java:130)
	at org.apache.tomcat.websocket.server.WsFrameServer.onDataAvailable(WsFrameServer.java:85)
	at org.apache.tomcat.websocket.server.WsFrameServer.doOnDataAvailable(WsFrameServer.java:184)
	at org.apache.tomcat.websocket.server.WsFrameServer.notifyDataAvailable(WsFrameServer.java:164)
	at org.apache.tomcat.websocket.server.WsHttpUpgradeHandler.upgradeDispatch(WsHttpUpgradeHandler.java:152)
	at org.apache.coyote.http11.upgrade.UpgradeProcessorInternal.dispatch(UpgradeProcessorInternal.java:60)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:57)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:928)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1794)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:52)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1191)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.base/java.lang.Thread.run(Thread.java:834)
13:58:19.105 [http-nio-9201-exec-4] INFO  c.r.f.w.WebSocketUsers - [remove,117] - 
 正在移出用户 - d
13:58:19.105 [http-nio-9201-exec-4] INFO  c.r.f.w.WebSocketUsers - [removeUserSessions,96] - 
 正在移出用户Session映射 - 100
13:58:24.150 [http-nio-9201-exec-8] INFO  c.r.f.w.WebSocketServer - [onOpen,100] - 
 建立连接 - org.apache.tomcat.websocket.WsSession@79024c85
13:58:24.150 [http-nio-9201-exec-8] INFO  c.r.f.w.WebSocketServer - [onOpen,101] - 
 当前人数 - 1
13:58:24.155 [http-nio-9201-exec-7] INFO  c.r.f.w.WebSocketServer - [onClose,113] - 
 关闭连接 - org.apache.tomcat.websocket.WsSession@79024c85
13:58:24.156 [http-nio-9201-exec-7] INFO  c.r.f.w.WebSocketUsers - [remove,117] - 
 正在移出用户 - e
13:58:24.156 [http-nio-9201-exec-7] INFO  c.r.f.w.WebSocketUsers - [remove,122] - 
 移出结果 - 成功
13:58:24.156 [http-nio-9201-exec-7] INFO  c.r.f.w.WebSocketServer - [onError,132] - 
 连接异常 - e
13:58:24.157 [http-nio-9201-exec-7] INFO  c.r.f.w.WebSocketServer - [onError,133] - 
 异常信息 - {}
java.lang.NullPointerException: null
	at java.base/java.util.concurrent.ConcurrentHashMap.get(ConcurrentHashMap.java:936)
	at com.ruoyi.framework.websocket.WebSocketUsers.getSessionByUserId(WebSocketUsers.java:148)
	at com.ruoyi.framework.websocket.WebSocketServer.onMessage(WebSocketServer.java:175)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:566)
	at org.apache.tomcat.websocket.pojo.PojoMessageHandlerWholeBase.onMessage(PojoMessageHandlerWholeBase.java:101)
	at org.apache.tomcat.websocket.WsFrameBase.sendMessageText(WsFrameBase.java:390)
	at org.apache.tomcat.websocket.server.WsFrameServer.sendMessageText(WsFrameServer.java:130)
	at org.apache.tomcat.websocket.WsFrameBase.processDataText(WsFrameBase.java:484)
	at org.apache.tomcat.websocket.WsFrameBase.processData(WsFrameBase.java:284)
	at org.apache.tomcat.websocket.WsFrameBase.processInputBuffer(WsFrameBase.java:130)
	at org.apache.tomcat.websocket.server.WsFrameServer.onDataAvailable(WsFrameServer.java:85)
	at org.apache.tomcat.websocket.server.WsFrameServer.doOnDataAvailable(WsFrameServer.java:184)
	at org.apache.tomcat.websocket.server.WsFrameServer.notifyDataAvailable(WsFrameServer.java:164)
	at org.apache.tomcat.websocket.server.WsHttpUpgradeHandler.upgradeDispatch(WsHttpUpgradeHandler.java:152)
	at org.apache.coyote.http11.upgrade.UpgradeProcessorInternal.dispatch(UpgradeProcessorInternal.java:60)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:57)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:928)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1794)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:52)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1191)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.base/java.lang.Thread.run(Thread.java:834)
13:58:24.157 [http-nio-9201-exec-7] INFO  c.r.f.w.WebSocketUsers - [remove,117] - 
 正在移出用户 - e
13:58:24.157 [http-nio-9201-exec-7] INFO  c.r.f.w.WebSocketUsers - [removeUserSessions,96] - 
 正在移出用户Session映射 - 100
13:58:29.199 [http-nio-9201-exec-9] INFO  c.r.f.w.WebSocketServer - [onOpen,100] - 
 建立连接 - org.apache.tomcat.websocket.WsSession@4a9a5ee
13:58:29.199 [http-nio-9201-exec-9] INFO  c.r.f.w.WebSocketServer - [onOpen,101] - 
 当前人数 - 1
13:58:29.204 [http-nio-9201-exec-2] INFO  c.r.f.w.WebSocketServer - [onClose,113] - 
 关闭连接 - org.apache.tomcat.websocket.WsSession@4a9a5ee
13:58:29.204 [http-nio-9201-exec-2] INFO  c.r.f.w.WebSocketUsers - [remove,117] - 
 正在移出用户 - f
13:58:29.205 [http-nio-9201-exec-2] INFO  c.r.f.w.WebSocketUsers - [remove,122] - 
 移出结果 - 成功
13:58:29.205 [http-nio-9201-exec-2] INFO  c.r.f.w.WebSocketServer - [onError,132] - 
 连接异常 - f
13:58:29.206 [http-nio-9201-exec-2] INFO  c.r.f.w.WebSocketServer - [onError,133] - 
 异常信息 - {}
java.lang.NullPointerException: null
	at java.base/java.util.concurrent.ConcurrentHashMap.get(ConcurrentHashMap.java:936)
	at com.ruoyi.framework.websocket.WebSocketUsers.getSessionByUserId(WebSocketUsers.java:148)
	at com.ruoyi.framework.websocket.WebSocketServer.onMessage(WebSocketServer.java:175)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:566)
	at org.apache.tomcat.websocket.pojo.PojoMessageHandlerWholeBase.onMessage(PojoMessageHandlerWholeBase.java:101)
	at org.apache.tomcat.websocket.WsFrameBase.sendMessageText(WsFrameBase.java:390)
	at org.apache.tomcat.websocket.server.WsFrameServer.sendMessageText(WsFrameServer.java:130)
	at org.apache.tomcat.websocket.WsFrameBase.processDataText(WsFrameBase.java:484)
	at org.apache.tomcat.websocket.WsFrameBase.processData(WsFrameBase.java:284)
	at org.apache.tomcat.websocket.WsFrameBase.processInputBuffer(WsFrameBase.java:130)
	at org.apache.tomcat.websocket.server.WsFrameServer.onDataAvailable(WsFrameServer.java:85)
	at org.apache.tomcat.websocket.server.WsFrameServer.doOnDataAvailable(WsFrameServer.java:184)
	at org.apache.tomcat.websocket.server.WsFrameServer.notifyDataAvailable(WsFrameServer.java:164)
	at org.apache.tomcat.websocket.server.WsHttpUpgradeHandler.upgradeDispatch(WsHttpUpgradeHandler.java:152)
	at org.apache.coyote.http11.upgrade.UpgradeProcessorInternal.dispatch(UpgradeProcessorInternal.java:60)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:57)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:928)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1794)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:52)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1191)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.base/java.lang.Thread.run(Thread.java:834)
13:58:29.207 [http-nio-9201-exec-2] INFO  c.r.f.w.WebSocketUsers - [remove,117] - 
 正在移出用户 - f
13:58:29.207 [http-nio-9201-exec-2] INFO  c.r.f.w.WebSocketUsers - [removeUserSessions,96] - 
 正在移出用户Session映射 - 100
13:58:34.243 [http-nio-9201-exec-1] INFO  c.r.f.w.WebSocketServer - [onOpen,100] - 
 建立连接 - org.apache.tomcat.websocket.WsSession@545655cb
13:58:34.243 [http-nio-9201-exec-1] INFO  c.r.f.w.WebSocketServer - [onOpen,101] - 
 当前人数 - 1
13:58:34.247 [http-nio-9201-exec-10] INFO  c.r.f.w.WebSocketServer - [onClose,113] - 
 关闭连接 - org.apache.tomcat.websocket.WsSession@545655cb
13:58:34.247 [http-nio-9201-exec-10] INFO  c.r.f.w.WebSocketUsers - [remove,117] - 
 正在移出用户 - 10
13:58:34.247 [http-nio-9201-exec-10] INFO  c.r.f.w.WebSocketUsers - [remove,122] - 
 移出结果 - 成功
13:58:34.248 [http-nio-9201-exec-10] INFO  c.r.f.w.WebSocketServer - [onError,132] - 
 连接异常 - 10
13:58:34.248 [http-nio-9201-exec-10] INFO  c.r.f.w.WebSocketServer - [onError,133] - 
 异常信息 - {}
java.lang.NullPointerException: null
	at java.base/java.util.concurrent.ConcurrentHashMap.get(ConcurrentHashMap.java:936)
	at com.ruoyi.framework.websocket.WebSocketUsers.getSessionByUserId(WebSocketUsers.java:148)
	at com.ruoyi.framework.websocket.WebSocketServer.onMessage(WebSocketServer.java:175)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:566)
	at org.apache.tomcat.websocket.pojo.PojoMessageHandlerWholeBase.onMessage(PojoMessageHandlerWholeBase.java:101)
	at org.apache.tomcat.websocket.WsFrameBase.sendMessageText(WsFrameBase.java:390)
	at org.apache.tomcat.websocket.server.WsFrameServer.sendMessageText(WsFrameServer.java:130)
	at org.apache.tomcat.websocket.WsFrameBase.processDataText(WsFrameBase.java:484)
	at org.apache.tomcat.websocket.WsFrameBase.processData(WsFrameBase.java:284)
	at org.apache.tomcat.websocket.WsFrameBase.processInputBuffer(WsFrameBase.java:130)
	at org.apache.tomcat.websocket.server.WsFrameServer.onDataAvailable(WsFrameServer.java:85)
	at org.apache.tomcat.websocket.server.WsFrameServer.doOnDataAvailable(WsFrameServer.java:184)
	at org.apache.tomcat.websocket.server.WsFrameServer.notifyDataAvailable(WsFrameServer.java:164)
	at org.apache.tomcat.websocket.server.WsHttpUpgradeHandler.upgradeDispatch(WsHttpUpgradeHandler.java:152)
	at org.apache.coyote.http11.upgrade.UpgradeProcessorInternal.dispatch(UpgradeProcessorInternal.java:60)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:57)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:928)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1794)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:52)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1191)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.base/java.lang.Thread.run(Thread.java:834)
13:58:34.248 [http-nio-9201-exec-10] INFO  c.r.f.w.WebSocketUsers - [remove,117] - 
 正在移出用户 - 10
13:58:34.248 [http-nio-9201-exec-10] INFO  c.r.f.w.WebSocketUsers - [removeUserSessions,96] - 
 正在移出用户Session映射 - 100
13:58:39.274 [http-nio-9201-exec-3] INFO  c.r.f.w.WebSocketServer - [onOpen,100] - 
 建立连接 - org.apache.tomcat.websocket.WsSession@3a067870
13:58:39.274 [http-nio-9201-exec-3] INFO  c.r.f.w.WebSocketServer - [onOpen,101] - 
 当前人数 - 1
13:58:39.279 [http-nio-9201-exec-6] INFO  c.r.f.w.WebSocketServer - [onClose,113] - 
 关闭连接 - org.apache.tomcat.websocket.WsSession@3a067870
13:58:39.280 [http-nio-9201-exec-6] INFO  c.r.f.w.WebSocketUsers - [remove,117] - 
 正在移出用户 - 11
13:58:39.280 [http-nio-9201-exec-6] INFO  c.r.f.w.WebSocketUsers - [remove,122] - 
 移出结果 - 成功
13:58:39.280 [http-nio-9201-exec-6] INFO  c.r.f.w.WebSocketServer - [onError,132] - 
 连接异常 - 11
13:58:39.280 [http-nio-9201-exec-6] INFO  c.r.f.w.WebSocketServer - [onError,133] - 
 异常信息 - {}
java.lang.NullPointerException: null
	at java.base/java.util.concurrent.ConcurrentHashMap.get(ConcurrentHashMap.java:936)
	at com.ruoyi.framework.websocket.WebSocketUsers.getSessionByUserId(WebSocketUsers.java:148)
	at com.ruoyi.framework.websocket.WebSocketServer.onMessage(WebSocketServer.java:175)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:566)
	at org.apache.tomcat.websocket.pojo.PojoMessageHandlerWholeBase.onMessage(PojoMessageHandlerWholeBase.java:101)
	at org.apache.tomcat.websocket.WsFrameBase.sendMessageText(WsFrameBase.java:390)
	at org.apache.tomcat.websocket.server.WsFrameServer.sendMessageText(WsFrameServer.java:130)
	at org.apache.tomcat.websocket.WsFrameBase.processDataText(WsFrameBase.java:484)
	at org.apache.tomcat.websocket.WsFrameBase.processData(WsFrameBase.java:284)
	at org.apache.tomcat.websocket.WsFrameBase.processInputBuffer(WsFrameBase.java:130)
	at org.apache.tomcat.websocket.server.WsFrameServer.onDataAvailable(WsFrameServer.java:85)
	at org.apache.tomcat.websocket.server.WsFrameServer.doOnDataAvailable(WsFrameServer.java:184)
	at org.apache.tomcat.websocket.server.WsFrameServer.notifyDataAvailable(WsFrameServer.java:164)
	at org.apache.tomcat.websocket.server.WsHttpUpgradeHandler.upgradeDispatch(WsHttpUpgradeHandler.java:152)
	at org.apache.coyote.http11.upgrade.UpgradeProcessorInternal.dispatch(UpgradeProcessorInternal.java:60)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:57)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:928)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1794)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:52)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1191)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.base/java.lang.Thread.run(Thread.java:834)
13:58:39.281 [http-nio-9201-exec-6] INFO  c.r.f.w.WebSocketUsers - [remove,117] - 
 正在移出用户 - 11
13:58:39.281 [http-nio-9201-exec-6] INFO  c.r.f.w.WebSocketUsers - [removeUserSessions,96] - 
 正在移出用户Session映射 - 100
13:58:44.311 [http-nio-9201-exec-5] INFO  c.r.f.w.WebSocketServer - [onOpen,100] - 
 建立连接 - org.apache.tomcat.websocket.WsSession@361b55e7
13:58:44.311 [http-nio-9201-exec-5] INFO  c.r.f.w.WebSocketServer - [onOpen,101] - 
 当前人数 - 1
13:58:44.315 [http-nio-9201-exec-4] INFO  c.r.f.w.WebSocketServer - [onClose,113] - 
 关闭连接 - org.apache.tomcat.websocket.WsSession@361b55e7
13:58:44.316 [http-nio-9201-exec-4] INFO  c.r.f.w.WebSocketUsers - [remove,117] - 
 正在移出用户 - 12
13:58:44.316 [http-nio-9201-exec-4] INFO  c.r.f.w.WebSocketUsers - [remove,122] - 
 移出结果 - 成功
13:58:44.316 [http-nio-9201-exec-4] INFO  c.r.f.w.WebSocketServer - [onError,132] - 
 连接异常 - 12
13:58:44.316 [http-nio-9201-exec-4] INFO  c.r.f.w.WebSocketServer - [onError,133] - 
 异常信息 - {}
java.lang.NullPointerException: null
	at java.base/java.util.concurrent.ConcurrentHashMap.get(ConcurrentHashMap.java:936)
	at com.ruoyi.framework.websocket.WebSocketUsers.getSessionByUserId(WebSocketUsers.java:148)
	at com.ruoyi.framework.websocket.WebSocketServer.onMessage(WebSocketServer.java:175)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:566)
	at org.apache.tomcat.websocket.pojo.PojoMessageHandlerWholeBase.onMessage(PojoMessageHandlerWholeBase.java:101)
	at org.apache.tomcat.websocket.WsFrameBase.sendMessageText(WsFrameBase.java:390)
	at org.apache.tomcat.websocket.server.WsFrameServer.sendMessageText(WsFrameServer.java:130)
	at org.apache.tomcat.websocket.WsFrameBase.processDataText(WsFrameBase.java:484)
	at org.apache.tomcat.websocket.WsFrameBase.processData(WsFrameBase.java:284)
	at org.apache.tomcat.websocket.WsFrameBase.processInputBuffer(WsFrameBase.java:130)
	at org.apache.tomcat.websocket.server.WsFrameServer.onDataAvailable(WsFrameServer.java:85)
	at org.apache.tomcat.websocket.server.WsFrameServer.doOnDataAvailable(WsFrameServer.java:184)
	at org.apache.tomcat.websocket.server.WsFrameServer.notifyDataAvailable(WsFrameServer.java:164)
	at org.apache.tomcat.websocket.server.WsHttpUpgradeHandler.upgradeDispatch(WsHttpUpgradeHandler.java:152)
	at org.apache.coyote.http11.upgrade.UpgradeProcessorInternal.dispatch(UpgradeProcessorInternal.java:60)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:57)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:928)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1794)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:52)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1191)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.base/java.lang.Thread.run(Thread.java:834)
13:58:44.317 [http-nio-9201-exec-4] INFO  c.r.f.w.WebSocketUsers - [remove,117] - 
 正在移出用户 - 12
13:58:44.318 [http-nio-9201-exec-4] INFO  c.r.f.w.WebSocketUsers - [removeUserSessions,96] - 
 正在移出用户Session映射 - 100
13:58:47.980 [http-nio-9201-exec-7] INFO  c.r.f.t.WebSocketToTcpProxy - [onClose,50] - 📡 TBOX模拟器WebSocket连接关闭: 5
13:58:47.984 [pool-6-thread-3] INFO  c.r.f.t.TcpClientHandler - [closeConnection,153] - 🔌 TBOX客户端连接已关闭: /127.0.0.1:62100
13:58:48.232 [http-nio-9201-exec-2] INFO  c.r.f.t.WebSocketToTcpProxy - [onOpen,34] - 📡 TBOX模拟器WebSocket连接建立: 13
13:58:49.350 [http-nio-9201-exec-1] INFO  c.r.f.w.WebSocketServer - [onOpen,100] - 
 建立连接 - org.apache.tomcat.websocket.WsSession@6ac53b5f
13:58:49.350 [http-nio-9201-exec-1] INFO  c.r.f.w.WebSocketServer - [onOpen,101] - 
 当前人数 - 1
13:58:49.355 [http-nio-9201-exec-10] INFO  c.r.f.w.WebSocketServer - [onClose,113] - 
 关闭连接 - org.apache.tomcat.websocket.WsSession@6ac53b5f
13:58:49.355 [http-nio-9201-exec-10] INFO  c.r.f.w.WebSocketUsers - [remove,117] - 
 正在移出用户 - 14
13:58:49.356 [http-nio-9201-exec-10] INFO  c.r.f.w.WebSocketUsers - [remove,122] - 
 移出结果 - 成功
13:58:49.356 [http-nio-9201-exec-10] INFO  c.r.f.w.WebSocketServer - [onError,132] - 
 连接异常 - 14
13:58:49.357 [http-nio-9201-exec-10] INFO  c.r.f.w.WebSocketServer - [onError,133] - 
 异常信息 - {}
java.lang.NullPointerException: null
	at java.base/java.util.concurrent.ConcurrentHashMap.get(ConcurrentHashMap.java:936)
	at com.ruoyi.framework.websocket.WebSocketUsers.getSessionByUserId(WebSocketUsers.java:148)
	at com.ruoyi.framework.websocket.WebSocketServer.onMessage(WebSocketServer.java:175)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:566)
	at org.apache.tomcat.websocket.pojo.PojoMessageHandlerWholeBase.onMessage(PojoMessageHandlerWholeBase.java:101)
	at org.apache.tomcat.websocket.WsFrameBase.sendMessageText(WsFrameBase.java:390)
	at org.apache.tomcat.websocket.server.WsFrameServer.sendMessageText(WsFrameServer.java:130)
	at org.apache.tomcat.websocket.WsFrameBase.processDataText(WsFrameBase.java:484)
	at org.apache.tomcat.websocket.WsFrameBase.processData(WsFrameBase.java:284)
	at org.apache.tomcat.websocket.WsFrameBase.processInputBuffer(WsFrameBase.java:130)
	at org.apache.tomcat.websocket.server.WsFrameServer.onDataAvailable(WsFrameServer.java:85)
	at org.apache.tomcat.websocket.server.WsFrameServer.doOnDataAvailable(WsFrameServer.java:184)
	at org.apache.tomcat.websocket.server.WsFrameServer.notifyDataAvailable(WsFrameServer.java:164)
	at org.apache.tomcat.websocket.server.WsHttpUpgradeHandler.upgradeDispatch(WsHttpUpgradeHandler.java:152)
	at org.apache.coyote.http11.upgrade.UpgradeProcessorInternal.dispatch(UpgradeProcessorInternal.java:60)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:57)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:928)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1794)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:52)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1191)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.base/java.lang.Thread.run(Thread.java:834)
13:58:49.358 [http-nio-9201-exec-10] INFO  c.r.f.w.WebSocketUsers - [remove,117] - 
 正在移出用户 - 14
13:58:49.358 [http-nio-9201-exec-10] INFO  c.r.f.w.WebSocketUsers - [removeUserSessions,96] - 
 正在移出用户Session映射 - 100
13:58:49.494 [http-nio-9201-exec-3] INFO  c.r.f.t.WebSocketToTcpProxy - [onMessage,79] - 📡 收到TBOX模拟器指令: {"action":"connect","host":"localhost","port":9999}
13:58:49.495 [http-nio-9201-exec-3] INFO  c.r.f.t.WebSocketToTcpProxy - [handleConnect,116] - 🔌 TBOX模拟器尝试连接TCP服务器: localhost:9999
13:58:49.500 [http-nio-9201-exec-3] INFO  c.r.f.t.WebSocketToTcpProxy - [handleConnect,127] - ✅ TBOX模拟器TCP连接建立成功
13:58:49.500 [pool-6-thread-1] INFO  c.r.f.tcp.TcpServer - [acceptConnections,63] - 📱 新的TBOX客户端连接: /127.0.0.1:53987
13:58:49.504 [pool-6-thread-3] INFO  c.r.f.t.TcpClientHandler - [run,49] - 🔄 开始处理TBOX客户端: /127.0.0.1:53987
13:58:54.395 [http-nio-9201-exec-6] INFO  c.r.f.w.WebSocketServer - [onOpen,100] - 
 建立连接 - org.apache.tomcat.websocket.WsSession@31040ab0
13:58:54.395 [http-nio-9201-exec-6] INFO  c.r.f.w.WebSocketServer - [onOpen,101] - 
 当前人数 - 1
13:58:54.400 [http-nio-9201-exec-5] INFO  c.r.f.w.WebSocketServer - [onClose,113] - 
 关闭连接 - org.apache.tomcat.websocket.WsSession@31040ab0
13:58:54.400 [http-nio-9201-exec-5] INFO  c.r.f.w.WebSocketUsers - [remove,117] - 
 正在移出用户 - 15
13:58:54.400 [http-nio-9201-exec-5] INFO  c.r.f.w.WebSocketUsers - [remove,122] - 
 移出结果 - 成功
13:58:54.401 [http-nio-9201-exec-5] INFO  c.r.f.w.WebSocketServer - [onError,132] - 
 连接异常 - 15
13:58:54.401 [http-nio-9201-exec-5] INFO  c.r.f.w.WebSocketServer - [onError,133] - 
 异常信息 - {}
java.lang.NullPointerException: null
	at java.base/java.util.concurrent.ConcurrentHashMap.get(ConcurrentHashMap.java:936)
	at com.ruoyi.framework.websocket.WebSocketUsers.getSessionByUserId(WebSocketUsers.java:148)
	at com.ruoyi.framework.websocket.WebSocketServer.onMessage(WebSocketServer.java:175)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:566)
	at org.apache.tomcat.websocket.pojo.PojoMessageHandlerWholeBase.onMessage(PojoMessageHandlerWholeBase.java:101)
	at org.apache.tomcat.websocket.WsFrameBase.sendMessageText(WsFrameBase.java:390)
	at org.apache.tomcat.websocket.server.WsFrameServer.sendMessageText(WsFrameServer.java:130)
	at org.apache.tomcat.websocket.WsFrameBase.processDataText(WsFrameBase.java:484)
	at org.apache.tomcat.websocket.WsFrameBase.processData(WsFrameBase.java:284)
	at org.apache.tomcat.websocket.WsFrameBase.processInputBuffer(WsFrameBase.java:130)
	at org.apache.tomcat.websocket.server.WsFrameServer.onDataAvailable(WsFrameServer.java:85)
	at org.apache.tomcat.websocket.server.WsFrameServer.doOnDataAvailable(WsFrameServer.java:184)
	at org.apache.tomcat.websocket.server.WsFrameServer.notifyDataAvailable(WsFrameServer.java:164)
	at org.apache.tomcat.websocket.server.WsHttpUpgradeHandler.upgradeDispatch(WsHttpUpgradeHandler.java:152)
	at org.apache.coyote.http11.upgrade.UpgradeProcessorInternal.dispatch(UpgradeProcessorInternal.java:60)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:57)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:928)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1794)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:52)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1191)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.base/java.lang.Thread.run(Thread.java:834)
13:58:54.402 [http-nio-9201-exec-5] INFO  c.r.f.w.WebSocketUsers - [remove,117] - 
 正在移出用户 - 15
13:58:54.402 [http-nio-9201-exec-5] INFO  c.r.f.w.WebSocketUsers - [removeUserSessions,96] - 
 正在移出用户Session映射 - 100
13:58:59.451 [http-nio-9201-exec-4] INFO  c.r.f.w.WebSocketServer - [onOpen,100] - 
 建立连接 - org.apache.tomcat.websocket.WsSession@127b361e
13:58:59.453 [http-nio-9201-exec-4] INFO  c.r.f.w.WebSocketServer - [onOpen,101] - 
 当前人数 - 1
13:58:59.458 [http-nio-9201-exec-4] INFO  c.r.f.w.WebSocketServer - [onClose,113] - 
 关闭连接 - org.apache.tomcat.websocket.WsSession@127b361e
13:58:59.458 [http-nio-9201-exec-4] INFO  c.r.f.w.WebSocketUsers - [remove,117] - 
 正在移出用户 - 16
13:58:59.459 [http-nio-9201-exec-4] INFO  c.r.f.w.WebSocketUsers - [remove,122] - 
 移出结果 - 成功
13:58:59.459 [http-nio-9201-exec-4] INFO  c.r.f.w.WebSocketServer - [onError,132] - 
 连接异常 - 16
13:58:59.460 [http-nio-9201-exec-4] INFO  c.r.f.w.WebSocketServer - [onError,133] - 
 异常信息 - {}
java.lang.NullPointerException: null
	at java.base/java.util.concurrent.ConcurrentHashMap.get(ConcurrentHashMap.java:936)
	at com.ruoyi.framework.websocket.WebSocketUsers.getSessionByUserId(WebSocketUsers.java:148)
	at com.ruoyi.framework.websocket.WebSocketServer.onMessage(WebSocketServer.java:175)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:566)
	at org.apache.tomcat.websocket.pojo.PojoMessageHandlerWholeBase.onMessage(PojoMessageHandlerWholeBase.java:101)
	at org.apache.tomcat.websocket.WsFrameBase.sendMessageText(WsFrameBase.java:390)
	at org.apache.tomcat.websocket.server.WsFrameServer.sendMessageText(WsFrameServer.java:130)
	at org.apache.tomcat.websocket.WsFrameBase.processDataText(WsFrameBase.java:484)
	at org.apache.tomcat.websocket.WsFrameBase.processData(WsFrameBase.java:284)
	at org.apache.tomcat.websocket.WsFrameBase.processInputBuffer(WsFrameBase.java:130)
	at org.apache.tomcat.websocket.server.WsFrameServer.onDataAvailable(WsFrameServer.java:85)
	at org.apache.tomcat.websocket.server.WsFrameServer.doOnDataAvailable(WsFrameServer.java:184)
	at org.apache.tomcat.websocket.server.WsFrameServer.notifyDataAvailable(WsFrameServer.java:164)
	at org.apache.tomcat.websocket.server.WsHttpUpgradeHandler.upgradeDispatch(WsHttpUpgradeHandler.java:152)
	at org.apache.coyote.http11.upgrade.UpgradeProcessorInternal.dispatch(UpgradeProcessorInternal.java:60)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:57)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:928)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1794)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:52)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1191)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.base/java.lang.Thread.run(Thread.java:834)
13:58:59.461 [http-nio-9201-exec-4] INFO  c.r.f.w.WebSocketUsers - [remove,117] - 
 正在移出用户 - 16
13:58:59.461 [http-nio-9201-exec-4] INFO  c.r.f.w.WebSocketUsers - [removeUserSessions,96] - 
 正在移出用户Session映射 - 100
13:59:04.493 [http-nio-9201-exec-8] INFO  c.r.f.w.WebSocketServer - [onOpen,100] - 
 建立连接 - org.apache.tomcat.websocket.WsSession@6288326
13:59:04.493 [http-nio-9201-exec-8] INFO  c.r.f.w.WebSocketServer - [onOpen,101] - 
 当前人数 - 1
13:59:04.497 [http-nio-9201-exec-7] INFO  c.r.f.w.WebSocketServer - [onClose,113] - 
 关闭连接 - org.apache.tomcat.websocket.WsSession@6288326
13:59:04.499 [http-nio-9201-exec-7] INFO  c.r.f.w.WebSocketUsers - [remove,117] - 
 正在移出用户 - 17
13:59:04.499 [http-nio-9201-exec-7] INFO  c.r.f.w.WebSocketUsers - [remove,122] - 
 移出结果 - 成功
13:59:04.499 [http-nio-9201-exec-7] INFO  c.r.f.w.WebSocketServer - [onError,132] - 
 连接异常 - 17
13:59:04.504 [http-nio-9201-exec-7] INFO  c.r.f.w.WebSocketServer - [onError,133] - 
 异常信息 - {}
java.lang.NullPointerException: null
	at java.base/java.util.concurrent.ConcurrentHashMap.get(ConcurrentHashMap.java:936)
	at com.ruoyi.framework.websocket.WebSocketUsers.getSessionByUserId(WebSocketUsers.java:148)
	at com.ruoyi.framework.websocket.WebSocketServer.onMessage(WebSocketServer.java:175)
	at jdk.internal.reflect.GeneratedMethodAccessor114.invoke(Unknown Source)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:566)
	at org.apache.tomcat.websocket.pojo.PojoMessageHandlerWholeBase.onMessage(PojoMessageHandlerWholeBase.java:101)
	at org.apache.tomcat.websocket.WsFrameBase.sendMessageText(WsFrameBase.java:390)
	at org.apache.tomcat.websocket.server.WsFrameServer.sendMessageText(WsFrameServer.java:130)
	at org.apache.tomcat.websocket.WsFrameBase.processDataText(WsFrameBase.java:484)
	at org.apache.tomcat.websocket.WsFrameBase.processData(WsFrameBase.java:284)
	at org.apache.tomcat.websocket.WsFrameBase.processInputBuffer(WsFrameBase.java:130)
	at org.apache.tomcat.websocket.server.WsFrameServer.onDataAvailable(WsFrameServer.java:85)
	at org.apache.tomcat.websocket.server.WsFrameServer.doOnDataAvailable(WsFrameServer.java:184)
	at org.apache.tomcat.websocket.server.WsFrameServer.notifyDataAvailable(WsFrameServer.java:164)
	at org.apache.tomcat.websocket.server.WsHttpUpgradeHandler.upgradeDispatch(WsHttpUpgradeHandler.java:152)
	at org.apache.coyote.http11.upgrade.UpgradeProcessorInternal.dispatch(UpgradeProcessorInternal.java:60)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:57)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:928)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1794)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:52)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1191)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.base/java.lang.Thread.run(Thread.java:834)
13:59:04.505 [http-nio-9201-exec-7] INFO  c.r.f.w.WebSocketUsers - [remove,117] - 
 正在移出用户 - 17
13:59:04.505 [http-nio-9201-exec-7] INFO  c.r.f.w.WebSocketUsers - [removeUserSessions,96] - 
 正在移出用户Session映射 - 100
13:59:09.539 [http-nio-9201-exec-9] INFO  c.r.f.w.WebSocketServer - [onOpen,100] - 
 建立连接 - org.apache.tomcat.websocket.WsSession@4dcc5597
13:59:09.539 [http-nio-9201-exec-9] INFO  c.r.f.w.WebSocketServer - [onOpen,101] - 
 当前人数 - 1
13:59:09.544 [http-nio-9201-exec-2] INFO  c.r.f.w.WebSocketServer - [onClose,113] - 
 关闭连接 - org.apache.tomcat.websocket.WsSession@4dcc5597
13:59:09.545 [http-nio-9201-exec-2] INFO  c.r.f.w.WebSocketUsers - [remove,117] - 
 正在移出用户 - 18
13:59:09.546 [http-nio-9201-exec-2] INFO  c.r.f.w.WebSocketUsers - [remove,122] - 
 移出结果 - 成功
13:59:09.546 [http-nio-9201-exec-2] INFO  c.r.f.w.WebSocketServer - [onError,132] - 
 连接异常 - 18
13:59:09.547 [http-nio-9201-exec-2] INFO  c.r.f.w.WebSocketServer - [onError,133] - 
 异常信息 - {}
java.lang.NullPointerException: null
	at java.base/java.util.concurrent.ConcurrentHashMap.get(ConcurrentHashMap.java:936)
	at com.ruoyi.framework.websocket.WebSocketUsers.getSessionByUserId(WebSocketUsers.java:148)
	at com.ruoyi.framework.websocket.WebSocketServer.onMessage(WebSocketServer.java:175)
	at jdk.internal.reflect.GeneratedMethodAccessor114.invoke(Unknown Source)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:566)
	at org.apache.tomcat.websocket.pojo.PojoMessageHandlerWholeBase.onMessage(PojoMessageHandlerWholeBase.java:101)
	at org.apache.tomcat.websocket.WsFrameBase.sendMessageText(WsFrameBase.java:390)
	at org.apache.tomcat.websocket.server.WsFrameServer.sendMessageText(WsFrameServer.java:130)
	at org.apache.tomcat.websocket.WsFrameBase.processDataText(WsFrameBase.java:484)
	at org.apache.tomcat.websocket.WsFrameBase.processData(WsFrameBase.java:284)
	at org.apache.tomcat.websocket.WsFrameBase.processInputBuffer(WsFrameBase.java:130)
	at org.apache.tomcat.websocket.server.WsFrameServer.onDataAvailable(WsFrameServer.java:85)
	at org.apache.tomcat.websocket.server.WsFrameServer.doOnDataAvailable(WsFrameServer.java:184)
	at org.apache.tomcat.websocket.server.WsFrameServer.notifyDataAvailable(WsFrameServer.java:164)
	at org.apache.tomcat.websocket.server.WsHttpUpgradeHandler.upgradeDispatch(WsHttpUpgradeHandler.java:152)
	at org.apache.coyote.http11.upgrade.UpgradeProcessorInternal.dispatch(UpgradeProcessorInternal.java:60)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:57)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:928)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1794)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:52)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1191)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.base/java.lang.Thread.run(Thread.java:834)
13:59:09.548 [http-nio-9201-exec-2] INFO  c.r.f.w.WebSocketUsers - [remove,117] - 
 正在移出用户 - 18
13:59:09.549 [http-nio-9201-exec-2] INFO  c.r.f.w.WebSocketUsers - [removeUserSessions,96] - 
 正在移出用户Session映射 - 100
13:59:14.587 [http-nio-9201-exec-1] INFO  c.r.f.w.WebSocketServer - [onOpen,100] - 
 建立连接 - org.apache.tomcat.websocket.WsSession@576d878f
13:59:14.588 [http-nio-9201-exec-1] INFO  c.r.f.w.WebSocketServer - [onOpen,101] - 
 当前人数 - 1
13:59:14.605 [http-nio-9201-exec-10] INFO  c.r.f.w.WebSocketServer - [onClose,113] - 
 关闭连接 - org.apache.tomcat.websocket.WsSession@576d878f
13:59:14.605 [http-nio-9201-exec-10] INFO  c.r.f.w.WebSocketUsers - [remove,117] - 
 正在移出用户 - 19
13:59:14.606 [http-nio-9201-exec-10] INFO  c.r.f.w.WebSocketUsers - [remove,122] - 
 移出结果 - 成功
13:59:14.606 [http-nio-9201-exec-10] INFO  c.r.f.w.WebSocketServer - [onError,132] - 
 连接异常 - 19
13:59:14.606 [http-nio-9201-exec-10] INFO  c.r.f.w.WebSocketServer - [onError,133] - 
 异常信息 - {}
java.lang.NullPointerException: null
	at java.base/java.util.concurrent.ConcurrentHashMap.get(ConcurrentHashMap.java:936)
	at com.ruoyi.framework.websocket.WebSocketUsers.getSessionByUserId(WebSocketUsers.java:148)
	at com.ruoyi.framework.websocket.WebSocketServer.onMessage(WebSocketServer.java:175)
	at jdk.internal.reflect.GeneratedMethodAccessor114.invoke(Unknown Source)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:566)
	at org.apache.tomcat.websocket.pojo.PojoMessageHandlerWholeBase.onMessage(PojoMessageHandlerWholeBase.java:101)
	at org.apache.tomcat.websocket.WsFrameBase.sendMessageText(WsFrameBase.java:390)
	at org.apache.tomcat.websocket.server.WsFrameServer.sendMessageText(WsFrameServer.java:130)
	at org.apache.tomcat.websocket.WsFrameBase.processDataText(WsFrameBase.java:484)
	at org.apache.tomcat.websocket.WsFrameBase.processData(WsFrameBase.java:284)
	at org.apache.tomcat.websocket.WsFrameBase.processInputBuffer(WsFrameBase.java:130)
	at org.apache.tomcat.websocket.server.WsFrameServer.onDataAvailable(WsFrameServer.java:85)
	at org.apache.tomcat.websocket.server.WsFrameServer.doOnDataAvailable(WsFrameServer.java:184)
	at org.apache.tomcat.websocket.server.WsFrameServer.notifyDataAvailable(WsFrameServer.java:164)
	at org.apache.tomcat.websocket.server.WsHttpUpgradeHandler.upgradeDispatch(WsHttpUpgradeHandler.java:152)
	at org.apache.coyote.http11.upgrade.UpgradeProcessorInternal.dispatch(UpgradeProcessorInternal.java:60)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:57)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:928)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1794)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:52)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1191)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.base/java.lang.Thread.run(Thread.java:834)
13:59:14.607 [http-nio-9201-exec-10] INFO  c.r.f.w.WebSocketUsers - [remove,117] - 
 正在移出用户 - 19
13:59:14.607 [http-nio-9201-exec-10] INFO  c.r.f.w.WebSocketUsers - [removeUserSessions,96] - 
 正在移出用户Session映射 - 100
13:59:19.631 [http-nio-9201-exec-3] INFO  c.r.f.w.WebSocketServer - [onOpen,100] - 
 建立连接 - org.apache.tomcat.websocket.WsSession@32e0de71
13:59:19.631 [http-nio-9201-exec-3] INFO  c.r.f.w.WebSocketServer - [onOpen,101] - 
 当前人数 - 1
13:59:19.636 [http-nio-9201-exec-6] INFO  c.r.f.w.WebSocketServer - [onClose,113] - 
 关闭连接 - org.apache.tomcat.websocket.WsSession@32e0de71
13:59:19.636 [http-nio-9201-exec-6] INFO  c.r.f.w.WebSocketUsers - [remove,117] - 
 正在移出用户 - 1a
13:59:19.638 [http-nio-9201-exec-6] INFO  c.r.f.w.WebSocketUsers - [remove,122] - 
 移出结果 - 成功
13:59:19.638 [http-nio-9201-exec-6] INFO  c.r.f.w.WebSocketServer - [onError,132] - 
 连接异常 - 1a
13:59:19.641 [http-nio-9201-exec-6] INFO  c.r.f.w.WebSocketServer - [onError,133] - 
 异常信息 - {}
java.lang.NullPointerException: null
	at java.base/java.util.concurrent.ConcurrentHashMap.get(ConcurrentHashMap.java:936)
	at com.ruoyi.framework.websocket.WebSocketUsers.getSessionByUserId(WebSocketUsers.java:148)
	at com.ruoyi.framework.websocket.WebSocketServer.onMessage(WebSocketServer.java:175)
	at jdk.internal.reflect.GeneratedMethodAccessor114.invoke(Unknown Source)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:566)
	at org.apache.tomcat.websocket.pojo.PojoMessageHandlerWholeBase.onMessage(PojoMessageHandlerWholeBase.java:101)
	at org.apache.tomcat.websocket.WsFrameBase.sendMessageText(WsFrameBase.java:390)
	at org.apache.tomcat.websocket.server.WsFrameServer.sendMessageText(WsFrameServer.java:130)
	at org.apache.tomcat.websocket.WsFrameBase.processDataText(WsFrameBase.java:484)
	at org.apache.tomcat.websocket.WsFrameBase.processData(WsFrameBase.java:284)
	at org.apache.tomcat.websocket.WsFrameBase.processInputBuffer(WsFrameBase.java:130)
	at org.apache.tomcat.websocket.server.WsFrameServer.onDataAvailable(WsFrameServer.java:85)
	at org.apache.tomcat.websocket.server.WsFrameServer.doOnDataAvailable(WsFrameServer.java:184)
	at org.apache.tomcat.websocket.server.WsFrameServer.notifyDataAvailable(WsFrameServer.java:164)
	at org.apache.tomcat.websocket.server.WsHttpUpgradeHandler.upgradeDispatch(WsHttpUpgradeHandler.java:152)
	at org.apache.coyote.http11.upgrade.UpgradeProcessorInternal.dispatch(UpgradeProcessorInternal.java:60)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:57)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:928)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1794)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:52)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1191)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.base/java.lang.Thread.run(Thread.java:834)
13:59:19.642 [http-nio-9201-exec-6] INFO  c.r.f.w.WebSocketUsers - [remove,117] - 
 正在移出用户 - 1a
13:59:19.642 [http-nio-9201-exec-6] INFO  c.r.f.w.WebSocketUsers - [removeUserSessions,96] - 
 正在移出用户Session映射 - 100
13:59:24.683 [http-nio-9201-exec-5] INFO  c.r.f.w.WebSocketServer - [onOpen,100] - 
 建立连接 - org.apache.tomcat.websocket.WsSession@692eea0b
13:59:24.683 [http-nio-9201-exec-5] INFO  c.r.f.w.WebSocketServer - [onOpen,101] - 
 当前人数 - 1
13:59:24.687 [http-nio-9201-exec-4] INFO  c.r.f.w.WebSocketServer - [onClose,113] - 
 关闭连接 - org.apache.tomcat.websocket.WsSession@692eea0b
13:59:24.687 [http-nio-9201-exec-4] INFO  c.r.f.w.WebSocketUsers - [remove,117] - 
 正在移出用户 - 1b
13:59:24.687 [http-nio-9201-exec-4] INFO  c.r.f.w.WebSocketUsers - [remove,122] - 
 移出结果 - 成功
13:59:24.688 [http-nio-9201-exec-4] INFO  c.r.f.w.WebSocketServer - [onError,132] - 
 连接异常 - 1b
13:59:24.689 [http-nio-9201-exec-4] INFO  c.r.f.w.WebSocketServer - [onError,133] - 
 异常信息 - {}
java.lang.NullPointerException: null
	at java.base/java.util.concurrent.ConcurrentHashMap.get(ConcurrentHashMap.java:936)
	at com.ruoyi.framework.websocket.WebSocketUsers.getSessionByUserId(WebSocketUsers.java:148)
	at com.ruoyi.framework.websocket.WebSocketServer.onMessage(WebSocketServer.java:175)
	at jdk.internal.reflect.GeneratedMethodAccessor114.invoke(Unknown Source)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:566)
	at org.apache.tomcat.websocket.pojo.PojoMessageHandlerWholeBase.onMessage(PojoMessageHandlerWholeBase.java:101)
	at org.apache.tomcat.websocket.WsFrameBase.sendMessageText(WsFrameBase.java:390)
	at org.apache.tomcat.websocket.server.WsFrameServer.sendMessageText(WsFrameServer.java:130)
	at org.apache.tomcat.websocket.WsFrameBase.processDataText(WsFrameBase.java:484)
	at org.apache.tomcat.websocket.WsFrameBase.processData(WsFrameBase.java:284)
	at org.apache.tomcat.websocket.WsFrameBase.processInputBuffer(WsFrameBase.java:130)
	at org.apache.tomcat.websocket.server.WsFrameServer.onDataAvailable(WsFrameServer.java:85)
	at org.apache.tomcat.websocket.server.WsFrameServer.doOnDataAvailable(WsFrameServer.java:184)
	at org.apache.tomcat.websocket.server.WsFrameServer.notifyDataAvailable(WsFrameServer.java:164)
	at org.apache.tomcat.websocket.server.WsHttpUpgradeHandler.upgradeDispatch(WsHttpUpgradeHandler.java:152)
	at org.apache.coyote.http11.upgrade.UpgradeProcessorInternal.dispatch(UpgradeProcessorInternal.java:60)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:57)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:928)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1794)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:52)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1191)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.base/java.lang.Thread.run(Thread.java:834)
13:59:24.690 [http-nio-9201-exec-4] INFO  c.r.f.w.WebSocketUsers - [remove,117] - 
 正在移出用户 - 1b
13:59:24.690 [http-nio-9201-exec-4] INFO  c.r.f.w.WebSocketUsers - [removeUserSessions,96] - 
 正在移出用户Session映射 - 100
13:59:29.723 [http-nio-9201-exec-8] INFO  c.r.f.w.WebSocketServer - [onOpen,100] - 
 建立连接 - org.apache.tomcat.websocket.WsSession@715ebf81
13:59:29.723 [http-nio-9201-exec-8] INFO  c.r.f.w.WebSocketServer - [onOpen,101] - 
 当前人数 - 1
13:59:29.728 [http-nio-9201-exec-7] INFO  c.r.f.w.WebSocketServer - [onClose,113] - 
 关闭连接 - org.apache.tomcat.websocket.WsSession@715ebf81
13:59:29.728 [http-nio-9201-exec-7] INFO  c.r.f.w.WebSocketUsers - [remove,117] - 
 正在移出用户 - 1c
13:59:29.728 [http-nio-9201-exec-7] INFO  c.r.f.w.WebSocketUsers - [remove,122] - 
 移出结果 - 成功
13:59:29.729 [http-nio-9201-exec-7] INFO  c.r.f.w.WebSocketServer - [onError,132] - 
 连接异常 - 1c
13:59:29.730 [http-nio-9201-exec-7] INFO  c.r.f.w.WebSocketServer - [onError,133] - 
 异常信息 - {}
java.lang.NullPointerException: null
	at java.base/java.util.concurrent.ConcurrentHashMap.get(ConcurrentHashMap.java:936)
	at com.ruoyi.framework.websocket.WebSocketUsers.getSessionByUserId(WebSocketUsers.java:148)
	at com.ruoyi.framework.websocket.WebSocketServer.onMessage(WebSocketServer.java:175)
	at jdk.internal.reflect.GeneratedMethodAccessor114.invoke(Unknown Source)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:566)
	at org.apache.tomcat.websocket.pojo.PojoMessageHandlerWholeBase.onMessage(PojoMessageHandlerWholeBase.java:101)
	at org.apache.tomcat.websocket.WsFrameBase.sendMessageText(WsFrameBase.java:390)
	at org.apache.tomcat.websocket.server.WsFrameServer.sendMessageText(WsFrameServer.java:130)
	at org.apache.tomcat.websocket.WsFrameBase.processDataText(WsFrameBase.java:484)
	at org.apache.tomcat.websocket.WsFrameBase.processData(WsFrameBase.java:284)
	at org.apache.tomcat.websocket.WsFrameBase.processInputBuffer(WsFrameBase.java:130)
	at org.apache.tomcat.websocket.server.WsFrameServer.onDataAvailable(WsFrameServer.java:85)
	at org.apache.tomcat.websocket.server.WsFrameServer.doOnDataAvailable(WsFrameServer.java:184)
	at org.apache.tomcat.websocket.server.WsFrameServer.notifyDataAvailable(WsFrameServer.java:164)
	at org.apache.tomcat.websocket.server.WsHttpUpgradeHandler.upgradeDispatch(WsHttpUpgradeHandler.java:152)
	at org.apache.coyote.http11.upgrade.UpgradeProcessorInternal.dispatch(UpgradeProcessorInternal.java:60)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:57)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:928)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1794)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:52)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1191)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.base/java.lang.Thread.run(Thread.java:834)
13:59:29.731 [http-nio-9201-exec-7] INFO  c.r.f.w.WebSocketUsers - [remove,117] - 
 正在移出用户 - 1c
13:59:29.731 [http-nio-9201-exec-7] INFO  c.r.f.w.WebSocketUsers - [removeUserSessions,96] - 
 正在移出用户Session映射 - 100
13:59:34.761 [http-nio-9201-exec-9] INFO  c.r.f.w.WebSocketServer - [onOpen,100] - 
 建立连接 - org.apache.tomcat.websocket.WsSession@28f6d372
13:59:34.761 [http-nio-9201-exec-9] INFO  c.r.f.w.WebSocketServer - [onOpen,101] - 
 当前人数 - 1
13:59:34.765 [http-nio-9201-exec-2] INFO  c.r.f.w.WebSocketServer - [onClose,113] - 
 关闭连接 - org.apache.tomcat.websocket.WsSession@28f6d372
13:59:34.766 [http-nio-9201-exec-2] INFO  c.r.f.w.WebSocketUsers - [remove,117] - 
 正在移出用户 - 1d
13:59:34.766 [http-nio-9201-exec-2] INFO  c.r.f.w.WebSocketUsers - [remove,122] - 
 移出结果 - 成功
13:59:34.766 [http-nio-9201-exec-2] INFO  c.r.f.w.WebSocketServer - [onError,132] - 
 连接异常 - 1d
13:59:34.767 [http-nio-9201-exec-2] INFO  c.r.f.w.WebSocketServer - [onError,133] - 
 异常信息 - {}
java.lang.NullPointerException: null
	at java.base/java.util.concurrent.ConcurrentHashMap.get(ConcurrentHashMap.java:936)
	at com.ruoyi.framework.websocket.WebSocketUsers.getSessionByUserId(WebSocketUsers.java:148)
	at com.ruoyi.framework.websocket.WebSocketServer.onMessage(WebSocketServer.java:175)
	at jdk.internal.reflect.GeneratedMethodAccessor114.invoke(Unknown Source)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:566)
	at org.apache.tomcat.websocket.pojo.PojoMessageHandlerWholeBase.onMessage(PojoMessageHandlerWholeBase.java:101)
	at org.apache.tomcat.websocket.WsFrameBase.sendMessageText(WsFrameBase.java:390)
	at org.apache.tomcat.websocket.server.WsFrameServer.sendMessageText(WsFrameServer.java:130)
	at org.apache.tomcat.websocket.WsFrameBase.processDataText(WsFrameBase.java:484)
	at org.apache.tomcat.websocket.WsFrameBase.processData(WsFrameBase.java:284)
	at org.apache.tomcat.websocket.WsFrameBase.processInputBuffer(WsFrameBase.java:130)
	at org.apache.tomcat.websocket.server.WsFrameServer.onDataAvailable(WsFrameServer.java:85)
	at org.apache.tomcat.websocket.server.WsFrameServer.doOnDataAvailable(WsFrameServer.java:184)
	at org.apache.tomcat.websocket.server.WsFrameServer.notifyDataAvailable(WsFrameServer.java:164)
	at org.apache.tomcat.websocket.server.WsHttpUpgradeHandler.upgradeDispatch(WsHttpUpgradeHandler.java:152)
	at org.apache.coyote.http11.upgrade.UpgradeProcessorInternal.dispatch(UpgradeProcessorInternal.java:60)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:57)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:928)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1794)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:52)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1191)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.base/java.lang.Thread.run(Thread.java:834)
13:59:34.768 [http-nio-9201-exec-2] INFO  c.r.f.w.WebSocketUsers - [remove,117] - 
 正在移出用户 - 1d
13:59:34.768 [http-nio-9201-exec-2] INFO  c.r.f.w.WebSocketUsers - [removeUserSessions,96] - 
 正在移出用户Session映射 - 100
13:59:39.797 [http-nio-9201-exec-1] INFO  c.r.f.w.WebSocketServer - [onOpen,100] - 
 建立连接 - org.apache.tomcat.websocket.WsSession@6eca8c0b
13:59:39.798 [http-nio-9201-exec-1] INFO  c.r.f.w.WebSocketServer - [onOpen,101] - 
 当前人数 - 1
13:59:39.802 [http-nio-9201-exec-10] INFO  c.r.f.w.WebSocketServer - [onClose,113] - 
 关闭连接 - org.apache.tomcat.websocket.WsSession@6eca8c0b
13:59:39.803 [http-nio-9201-exec-10] INFO  c.r.f.w.WebSocketUsers - [remove,117] - 
 正在移出用户 - 1e
13:59:39.803 [http-nio-9201-exec-10] INFO  c.r.f.w.WebSocketUsers - [remove,122] - 
 移出结果 - 成功
13:59:39.803 [http-nio-9201-exec-10] INFO  c.r.f.w.WebSocketServer - [onError,132] - 
 连接异常 - 1e
13:59:39.804 [http-nio-9201-exec-10] INFO  c.r.f.w.WebSocketServer - [onError,133] - 
 异常信息 - {}
java.lang.NullPointerException: null
	at java.base/java.util.concurrent.ConcurrentHashMap.get(ConcurrentHashMap.java:936)
	at com.ruoyi.framework.websocket.WebSocketUsers.getSessionByUserId(WebSocketUsers.java:148)
	at com.ruoyi.framework.websocket.WebSocketServer.onMessage(WebSocketServer.java:175)
	at jdk.internal.reflect.GeneratedMethodAccessor114.invoke(Unknown Source)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:566)
	at org.apache.tomcat.websocket.pojo.PojoMessageHandlerWholeBase.onMessage(PojoMessageHandlerWholeBase.java:101)
	at org.apache.tomcat.websocket.WsFrameBase.sendMessageText(WsFrameBase.java:390)
	at org.apache.tomcat.websocket.server.WsFrameServer.sendMessageText(WsFrameServer.java:130)
	at org.apache.tomcat.websocket.WsFrameBase.processDataText(WsFrameBase.java:484)
	at org.apache.tomcat.websocket.WsFrameBase.processData(WsFrameBase.java:284)
	at org.apache.tomcat.websocket.WsFrameBase.processInputBuffer(WsFrameBase.java:130)
	at org.apache.tomcat.websocket.server.WsFrameServer.onDataAvailable(WsFrameServer.java:85)
	at org.apache.tomcat.websocket.server.WsFrameServer.doOnDataAvailable(WsFrameServer.java:184)
	at org.apache.tomcat.websocket.server.WsFrameServer.notifyDataAvailable(WsFrameServer.java:164)
	at org.apache.tomcat.websocket.server.WsHttpUpgradeHandler.upgradeDispatch(WsHttpUpgradeHandler.java:152)
	at org.apache.coyote.http11.upgrade.UpgradeProcessorInternal.dispatch(UpgradeProcessorInternal.java:60)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:57)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:928)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1794)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:52)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1191)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.base/java.lang.Thread.run(Thread.java:834)
13:59:39.805 [http-nio-9201-exec-10] INFO  c.r.f.w.WebSocketUsers - [remove,117] - 
 正在移出用户 - 1e
13:59:39.805 [http-nio-9201-exec-10] INFO  c.r.f.w.WebSocketUsers - [removeUserSessions,96] - 
 正在移出用户Session映射 - 100
13:59:44.846 [http-nio-9201-exec-3] INFO  c.r.f.w.WebSocketServer - [onOpen,100] - 
 建立连接 - org.apache.tomcat.websocket.WsSession@2f2d48af
13:59:44.846 [http-nio-9201-exec-3] INFO  c.r.f.w.WebSocketServer - [onOpen,101] - 
 当前人数 - 1
13:59:44.851 [http-nio-9201-exec-6] INFO  c.r.f.w.WebSocketServer - [onClose,113] - 
 关闭连接 - org.apache.tomcat.websocket.WsSession@2f2d48af
13:59:44.851 [http-nio-9201-exec-6] INFO  c.r.f.w.WebSocketUsers - [remove,117] - 
 正在移出用户 - 1f
13:59:44.851 [http-nio-9201-exec-6] INFO  c.r.f.w.WebSocketUsers - [remove,122] - 
 移出结果 - 成功
13:59:44.851 [http-nio-9201-exec-6] INFO  c.r.f.w.WebSocketServer - [onError,132] - 
 连接异常 - 1f
13:59:44.852 [http-nio-9201-exec-6] INFO  c.r.f.w.WebSocketServer - [onError,133] - 
 异常信息 - {}
java.lang.NullPointerException: null
	at java.base/java.util.concurrent.ConcurrentHashMap.get(ConcurrentHashMap.java:936)
	at com.ruoyi.framework.websocket.WebSocketUsers.getSessionByUserId(WebSocketUsers.java:148)
	at com.ruoyi.framework.websocket.WebSocketServer.onMessage(WebSocketServer.java:175)
	at jdk.internal.reflect.GeneratedMethodAccessor114.invoke(Unknown Source)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:566)
	at org.apache.tomcat.websocket.pojo.PojoMessageHandlerWholeBase.onMessage(PojoMessageHandlerWholeBase.java:101)
	at org.apache.tomcat.websocket.WsFrameBase.sendMessageText(WsFrameBase.java:390)
	at org.apache.tomcat.websocket.server.WsFrameServer.sendMessageText(WsFrameServer.java:130)
	at org.apache.tomcat.websocket.WsFrameBase.processDataText(WsFrameBase.java:484)
	at org.apache.tomcat.websocket.WsFrameBase.processData(WsFrameBase.java:284)
	at org.apache.tomcat.websocket.WsFrameBase.processInputBuffer(WsFrameBase.java:130)
	at org.apache.tomcat.websocket.server.WsFrameServer.onDataAvailable(WsFrameServer.java:85)
	at org.apache.tomcat.websocket.server.WsFrameServer.doOnDataAvailable(WsFrameServer.java:184)
	at org.apache.tomcat.websocket.server.WsFrameServer.notifyDataAvailable(WsFrameServer.java:164)
	at org.apache.tomcat.websocket.server.WsHttpUpgradeHandler.upgradeDispatch(WsHttpUpgradeHandler.java:152)
	at org.apache.coyote.http11.upgrade.UpgradeProcessorInternal.dispatch(UpgradeProcessorInternal.java:60)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:57)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:928)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1794)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:52)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1191)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.base/java.lang.Thread.run(Thread.java:834)
13:59:44.853 [http-nio-9201-exec-6] INFO  c.r.f.w.WebSocketUsers - [remove,117] - 
 正在移出用户 - 1f
13:59:44.853 [http-nio-9201-exec-6] INFO  c.r.f.w.WebSocketUsers - [removeUserSessions,96] - 
 正在移出用户Session映射 - 100
13:59:49.887 [http-nio-9201-exec-8] INFO  c.r.f.w.WebSocketServer - [onOpen,100] - 
 建立连接 - org.apache.tomcat.websocket.WsSession@70373684
13:59:49.887 [http-nio-9201-exec-8] INFO  c.r.f.w.WebSocketServer - [onOpen,101] - 
 当前人数 - 1
13:59:49.892 [http-nio-9201-exec-7] INFO  c.r.f.w.WebSocketServer - [onClose,113] - 
 关闭连接 - org.apache.tomcat.websocket.WsSession@70373684
13:59:49.893 [http-nio-9201-exec-7] INFO  c.r.f.w.WebSocketUsers - [remove,117] - 
 正在移出用户 - 20
13:59:49.893 [http-nio-9201-exec-7] INFO  c.r.f.w.WebSocketUsers - [remove,122] - 
 移出结果 - 成功
13:59:49.893 [http-nio-9201-exec-7] INFO  c.r.f.w.WebSocketServer - [onError,132] - 
 连接异常 - 20
13:59:49.894 [http-nio-9201-exec-7] INFO  c.r.f.w.WebSocketServer - [onError,133] - 
 异常信息 - {}
java.lang.NullPointerException: null
	at java.base/java.util.concurrent.ConcurrentHashMap.get(ConcurrentHashMap.java:936)
	at com.ruoyi.framework.websocket.WebSocketUsers.getSessionByUserId(WebSocketUsers.java:148)
	at com.ruoyi.framework.websocket.WebSocketServer.onMessage(WebSocketServer.java:175)
	at jdk.internal.reflect.GeneratedMethodAccessor114.invoke(Unknown Source)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:566)
	at org.apache.tomcat.websocket.pojo.PojoMessageHandlerWholeBase.onMessage(PojoMessageHandlerWholeBase.java:101)
	at org.apache.tomcat.websocket.WsFrameBase.sendMessageText(WsFrameBase.java:390)
	at org.apache.tomcat.websocket.server.WsFrameServer.sendMessageText(WsFrameServer.java:130)
	at org.apache.tomcat.websocket.WsFrameBase.processDataText(WsFrameBase.java:484)
	at org.apache.tomcat.websocket.WsFrameBase.processData(WsFrameBase.java:284)
	at org.apache.tomcat.websocket.WsFrameBase.processInputBuffer(WsFrameBase.java:130)
	at org.apache.tomcat.websocket.server.WsFrameServer.onDataAvailable(WsFrameServer.java:85)
	at org.apache.tomcat.websocket.server.WsFrameServer.doOnDataAvailable(WsFrameServer.java:184)
	at org.apache.tomcat.websocket.server.WsFrameServer.notifyDataAvailable(WsFrameServer.java:164)
	at org.apache.tomcat.websocket.server.WsHttpUpgradeHandler.upgradeDispatch(WsHttpUpgradeHandler.java:152)
	at org.apache.coyote.http11.upgrade.UpgradeProcessorInternal.dispatch(UpgradeProcessorInternal.java:60)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:57)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:928)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1794)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:52)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1191)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.base/java.lang.Thread.run(Thread.java:834)
13:59:49.895 [http-nio-9201-exec-7] INFO  c.r.f.w.WebSocketUsers - [remove,117] - 
 正在移出用户 - 20
13:59:49.895 [http-nio-9201-exec-7] INFO  c.r.f.w.WebSocketUsers - [removeUserSessions,96] - 
 正在移出用户Session映射 - 100
13:59:54.932 [http-nio-9201-exec-9] INFO  c.r.f.w.WebSocketServer - [onOpen,100] - 
 建立连接 - org.apache.tomcat.websocket.WsSession@67b35558
13:59:54.932 [http-nio-9201-exec-9] INFO  c.r.f.w.WebSocketServer - [onOpen,101] - 
 当前人数 - 1
13:59:54.936 [http-nio-9201-exec-2] INFO  c.r.f.w.WebSocketServer - [onClose,113] - 
 关闭连接 - org.apache.tomcat.websocket.WsSession@67b35558
13:59:54.937 [http-nio-9201-exec-2] INFO  c.r.f.w.WebSocketUsers - [remove,117] - 
 正在移出用户 - 21
13:59:54.937 [http-nio-9201-exec-2] INFO  c.r.f.w.WebSocketUsers - [remove,122] - 
 移出结果 - 成功
13:59:54.937 [http-nio-9201-exec-2] INFO  c.r.f.w.WebSocketServer - [onError,132] - 
 连接异常 - 21
13:59:54.938 [http-nio-9201-exec-2] INFO  c.r.f.w.WebSocketServer - [onError,133] - 
 异常信息 - {}
java.lang.NullPointerException: null
	at java.base/java.util.concurrent.ConcurrentHashMap.get(ConcurrentHashMap.java:936)
	at com.ruoyi.framework.websocket.WebSocketUsers.getSessionByUserId(WebSocketUsers.java:148)
	at com.ruoyi.framework.websocket.WebSocketServer.onMessage(WebSocketServer.java:175)
	at jdk.internal.reflect.GeneratedMethodAccessor114.invoke(Unknown Source)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:566)
	at org.apache.tomcat.websocket.pojo.PojoMessageHandlerWholeBase.onMessage(PojoMessageHandlerWholeBase.java:101)
	at org.apache.tomcat.websocket.WsFrameBase.sendMessageText(WsFrameBase.java:390)
	at org.apache.tomcat.websocket.server.WsFrameServer.sendMessageText(WsFrameServer.java:130)
	at org.apache.tomcat.websocket.WsFrameBase.processDataText(WsFrameBase.java:484)
	at org.apache.tomcat.websocket.WsFrameBase.processData(WsFrameBase.java:284)
	at org.apache.tomcat.websocket.WsFrameBase.processInputBuffer(WsFrameBase.java:130)
	at org.apache.tomcat.websocket.server.WsFrameServer.onDataAvailable(WsFrameServer.java:85)
	at org.apache.tomcat.websocket.server.WsFrameServer.doOnDataAvailable(WsFrameServer.java:184)
	at org.apache.tomcat.websocket.server.WsFrameServer.notifyDataAvailable(WsFrameServer.java:164)
	at org.apache.tomcat.websocket.server.WsHttpUpgradeHandler.upgradeDispatch(WsHttpUpgradeHandler.java:152)
	at org.apache.coyote.http11.upgrade.UpgradeProcessorInternal.dispatch(UpgradeProcessorInternal.java:60)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:57)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:928)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1794)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:52)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1191)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.base/java.lang.Thread.run(Thread.java:834)
13:59:54.938 [http-nio-9201-exec-2] INFO  c.r.f.w.WebSocketUsers - [remove,117] - 
 正在移出用户 - 21
13:59:54.938 [http-nio-9201-exec-2] INFO  c.r.f.w.WebSocketUsers - [removeUserSessions,96] - 
 正在移出用户Session映射 - 100
13:59:59.979 [http-nio-9201-exec-1] INFO  c.r.f.w.WebSocketServer - [onOpen,100] - 
 建立连接 - org.apache.tomcat.websocket.WsSession@26a73f80
13:59:59.979 [http-nio-9201-exec-1] INFO  c.r.f.w.WebSocketServer - [onOpen,101] - 
 当前人数 - 1
13:59:59.983 [http-nio-9201-exec-10] INFO  c.r.f.w.WebSocketServer - [onClose,113] - 
 关闭连接 - org.apache.tomcat.websocket.WsSession@26a73f80
13:59:59.985 [http-nio-9201-exec-10] INFO  c.r.f.w.WebSocketUsers - [remove,117] - 
 正在移出用户 - 22
13:59:59.985 [http-nio-9201-exec-10] INFO  c.r.f.w.WebSocketUsers - [remove,122] - 
 移出结果 - 成功
13:59:59.985 [http-nio-9201-exec-10] INFO  c.r.f.w.WebSocketServer - [onError,132] - 
 连接异常 - 22
13:59:59.986 [http-nio-9201-exec-10] INFO  c.r.f.w.WebSocketServer - [onError,133] - 
 异常信息 - {}
java.lang.NullPointerException: null
	at java.base/java.util.concurrent.ConcurrentHashMap.get(ConcurrentHashMap.java:936)
	at com.ruoyi.framework.websocket.WebSocketUsers.getSessionByUserId(WebSocketUsers.java:148)
	at com.ruoyi.framework.websocket.WebSocketServer.onMessage(WebSocketServer.java:175)
	at jdk.internal.reflect.GeneratedMethodAccessor114.invoke(Unknown Source)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:566)
	at org.apache.tomcat.websocket.pojo.PojoMessageHandlerWholeBase.onMessage(PojoMessageHandlerWholeBase.java:101)
	at org.apache.tomcat.websocket.WsFrameBase.sendMessageText(WsFrameBase.java:390)
	at org.apache.tomcat.websocket.server.WsFrameServer.sendMessageText(WsFrameServer.java:130)
	at org.apache.tomcat.websocket.WsFrameBase.processDataText(WsFrameBase.java:484)
	at org.apache.tomcat.websocket.WsFrameBase.processData(WsFrameBase.java:284)
	at org.apache.tomcat.websocket.WsFrameBase.processInputBuffer(WsFrameBase.java:130)
	at org.apache.tomcat.websocket.server.WsFrameServer.onDataAvailable(WsFrameServer.java:85)
	at org.apache.tomcat.websocket.server.WsFrameServer.doOnDataAvailable(WsFrameServer.java:184)
	at org.apache.tomcat.websocket.server.WsFrameServer.notifyDataAvailable(WsFrameServer.java:164)
	at org.apache.tomcat.websocket.server.WsHttpUpgradeHandler.upgradeDispatch(WsHttpUpgradeHandler.java:152)
	at org.apache.coyote.http11.upgrade.UpgradeProcessorInternal.dispatch(UpgradeProcessorInternal.java:60)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:57)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:928)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1794)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:52)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1191)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.base/java.lang.Thread.run(Thread.java:834)
13:59:59.987 [http-nio-9201-exec-10] INFO  c.r.f.w.WebSocketUsers - [remove,117] - 
 正在移出用户 - 22
13:59:59.987 [http-nio-9201-exec-10] INFO  c.r.f.w.WebSocketUsers - [removeUserSessions,96] - 
 正在移出用户Session映射 - 100
14:00:05.022 [http-nio-9201-exec-3] INFO  c.r.f.w.WebSocketServer - [onOpen,100] - 
 建立连接 - org.apache.tomcat.websocket.WsSession@2de7c673
14:00:05.022 [http-nio-9201-exec-3] INFO  c.r.f.w.WebSocketServer - [onOpen,101] - 
 当前人数 - 1
14:00:05.027 [http-nio-9201-exec-6] INFO  c.r.f.w.WebSocketServer - [onClose,113] - 
 关闭连接 - org.apache.tomcat.websocket.WsSession@2de7c673
14:00:05.027 [http-nio-9201-exec-6] INFO  c.r.f.w.WebSocketUsers - [remove,117] - 
 正在移出用户 - 23
14:00:05.027 [http-nio-9201-exec-6] INFO  c.r.f.w.WebSocketUsers - [remove,122] - 
 移出结果 - 成功
14:00:05.027 [http-nio-9201-exec-6] INFO  c.r.f.w.WebSocketServer - [onError,132] - 
 连接异常 - 23
14:00:05.029 [http-nio-9201-exec-6] INFO  c.r.f.w.WebSocketServer - [onError,133] - 
 异常信息 - {}
java.lang.NullPointerException: null
	at java.base/java.util.concurrent.ConcurrentHashMap.get(ConcurrentHashMap.java:936)
	at com.ruoyi.framework.websocket.WebSocketUsers.getSessionByUserId(WebSocketUsers.java:148)
	at com.ruoyi.framework.websocket.WebSocketServer.onMessage(WebSocketServer.java:175)
	at jdk.internal.reflect.GeneratedMethodAccessor114.invoke(Unknown Source)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:566)
	at org.apache.tomcat.websocket.pojo.PojoMessageHandlerWholeBase.onMessage(PojoMessageHandlerWholeBase.java:101)
	at org.apache.tomcat.websocket.WsFrameBase.sendMessageText(WsFrameBase.java:390)
	at org.apache.tomcat.websocket.server.WsFrameServer.sendMessageText(WsFrameServer.java:130)
	at org.apache.tomcat.websocket.WsFrameBase.processDataText(WsFrameBase.java:484)
	at org.apache.tomcat.websocket.WsFrameBase.processData(WsFrameBase.java:284)
	at org.apache.tomcat.websocket.WsFrameBase.processInputBuffer(WsFrameBase.java:130)
	at org.apache.tomcat.websocket.server.WsFrameServer.onDataAvailable(WsFrameServer.java:85)
	at org.apache.tomcat.websocket.server.WsFrameServer.doOnDataAvailable(WsFrameServer.java:184)
	at org.apache.tomcat.websocket.server.WsFrameServer.notifyDataAvailable(WsFrameServer.java:164)
	at org.apache.tomcat.websocket.server.WsHttpUpgradeHandler.upgradeDispatch(WsHttpUpgradeHandler.java:152)
	at org.apache.coyote.http11.upgrade.UpgradeProcessorInternal.dispatch(UpgradeProcessorInternal.java:60)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:57)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:928)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1794)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:52)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1191)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.base/java.lang.Thread.run(Thread.java:834)
14:00:05.029 [http-nio-9201-exec-6] INFO  c.r.f.w.WebSocketUsers - [remove,117] - 
 正在移出用户 - 23
14:00:05.029 [http-nio-9201-exec-6] INFO  c.r.f.w.WebSocketUsers - [removeUserSessions,96] - 
 正在移出用户Session映射 - 100
14:00:10.071 [http-nio-9201-exec-4] INFO  c.r.f.w.WebSocketServer - [onOpen,100] - 
 建立连接 - org.apache.tomcat.websocket.WsSession@791eeff0
14:00:10.071 [http-nio-9201-exec-4] INFO  c.r.f.w.WebSocketServer - [onOpen,101] - 
 当前人数 - 1
14:00:10.075 [http-nio-9201-exec-5] INFO  c.r.f.w.WebSocketServer - [onClose,113] - 
 关闭连接 - org.apache.tomcat.websocket.WsSession@791eeff0
14:00:10.076 [http-nio-9201-exec-5] INFO  c.r.f.w.WebSocketUsers - [remove,117] - 
 正在移出用户 - 24
14:00:10.076 [http-nio-9201-exec-5] INFO  c.r.f.w.WebSocketUsers - [remove,122] - 
 移出结果 - 成功
14:00:10.076 [http-nio-9201-exec-5] INFO  c.r.f.w.WebSocketServer - [onError,132] - 
 连接异常 - 24
14:00:10.078 [http-nio-9201-exec-5] INFO  c.r.f.w.WebSocketServer - [onError,133] - 
 异常信息 - {}
java.lang.NullPointerException: null
	at java.base/java.util.concurrent.ConcurrentHashMap.get(ConcurrentHashMap.java:936)
	at com.ruoyi.framework.websocket.WebSocketUsers.getSessionByUserId(WebSocketUsers.java:148)
	at com.ruoyi.framework.websocket.WebSocketServer.onMessage(WebSocketServer.java:175)
	at jdk.internal.reflect.GeneratedMethodAccessor114.invoke(Unknown Source)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:566)
	at org.apache.tomcat.websocket.pojo.PojoMessageHandlerWholeBase.onMessage(PojoMessageHandlerWholeBase.java:101)
	at org.apache.tomcat.websocket.WsFrameBase.sendMessageText(WsFrameBase.java:390)
	at org.apache.tomcat.websocket.server.WsFrameServer.sendMessageText(WsFrameServer.java:130)
	at org.apache.tomcat.websocket.WsFrameBase.processDataText(WsFrameBase.java:484)
	at org.apache.tomcat.websocket.WsFrameBase.processData(WsFrameBase.java:284)
	at org.apache.tomcat.websocket.WsFrameBase.processInputBuffer(WsFrameBase.java:130)
	at org.apache.tomcat.websocket.server.WsFrameServer.onDataAvailable(WsFrameServer.java:85)
	at org.apache.tomcat.websocket.server.WsFrameServer.doOnDataAvailable(WsFrameServer.java:184)
	at org.apache.tomcat.websocket.server.WsFrameServer.notifyDataAvailable(WsFrameServer.java:164)
	at org.apache.tomcat.websocket.server.WsHttpUpgradeHandler.upgradeDispatch(WsHttpUpgradeHandler.java:152)
	at org.apache.coyote.http11.upgrade.UpgradeProcessorInternal.dispatch(UpgradeProcessorInternal.java:60)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:57)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:928)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1794)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:52)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1191)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.base/java.lang.Thread.run(Thread.java:834)
14:00:10.078 [http-nio-9201-exec-5] INFO  c.r.f.w.WebSocketUsers - [remove,117] - 
 正在移出用户 - 24
14:00:10.079 [http-nio-9201-exec-5] INFO  c.r.f.w.WebSocketUsers - [removeUserSessions,96] - 
 正在移出用户Session映射 - 100
14:00:15.103 [http-nio-9201-exec-8] INFO  c.r.f.w.WebSocketServer - [onOpen,100] - 
 建立连接 - org.apache.tomcat.websocket.WsSession@43573e64
14:00:15.103 [http-nio-9201-exec-8] INFO  c.r.f.w.WebSocketServer - [onOpen,101] - 
 当前人数 - 1
14:00:15.107 [http-nio-9201-exec-7] INFO  c.r.f.w.WebSocketServer - [onClose,113] - 
 关闭连接 - org.apache.tomcat.websocket.WsSession@43573e64
14:00:15.107 [http-nio-9201-exec-7] INFO  c.r.f.w.WebSocketUsers - [remove,117] - 
 正在移出用户 - 25
14:00:15.107 [http-nio-9201-exec-7] INFO  c.r.f.w.WebSocketUsers - [remove,122] - 
 移出结果 - 成功
14:00:15.109 [http-nio-9201-exec-7] INFO  c.r.f.w.WebSocketServer - [onError,132] - 
 连接异常 - 25
14:00:15.109 [http-nio-9201-exec-7] INFO  c.r.f.w.WebSocketServer - [onError,133] - 
 异常信息 - {}
java.lang.NullPointerException: null
	at java.base/java.util.concurrent.ConcurrentHashMap.get(ConcurrentHashMap.java:936)
	at com.ruoyi.framework.websocket.WebSocketUsers.getSessionByUserId(WebSocketUsers.java:148)
	at com.ruoyi.framework.websocket.WebSocketServer.onMessage(WebSocketServer.java:175)
	at jdk.internal.reflect.GeneratedMethodAccessor114.invoke(Unknown Source)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:566)
	at org.apache.tomcat.websocket.pojo.PojoMessageHandlerWholeBase.onMessage(PojoMessageHandlerWholeBase.java:101)
	at org.apache.tomcat.websocket.WsFrameBase.sendMessageText(WsFrameBase.java:390)
	at org.apache.tomcat.websocket.server.WsFrameServer.sendMessageText(WsFrameServer.java:130)
	at org.apache.tomcat.websocket.WsFrameBase.processDataText(WsFrameBase.java:484)
	at org.apache.tomcat.websocket.WsFrameBase.processData(WsFrameBase.java:284)
	at org.apache.tomcat.websocket.WsFrameBase.processInputBuffer(WsFrameBase.java:130)
	at org.apache.tomcat.websocket.server.WsFrameServer.onDataAvailable(WsFrameServer.java:85)
	at org.apache.tomcat.websocket.server.WsFrameServer.doOnDataAvailable(WsFrameServer.java:184)
	at org.apache.tomcat.websocket.server.WsFrameServer.notifyDataAvailable(WsFrameServer.java:164)
	at org.apache.tomcat.websocket.server.WsHttpUpgradeHandler.upgradeDispatch(WsHttpUpgradeHandler.java:152)
	at org.apache.coyote.http11.upgrade.UpgradeProcessorInternal.dispatch(UpgradeProcessorInternal.java:60)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:57)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:928)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1794)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:52)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1191)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.base/java.lang.Thread.run(Thread.java:834)
14:00:15.110 [http-nio-9201-exec-7] INFO  c.r.f.w.WebSocketUsers - [remove,117] - 
 正在移出用户 - 25
14:00:15.110 [http-nio-9201-exec-7] INFO  c.r.f.w.WebSocketUsers - [removeUserSessions,96] - 
 正在移出用户Session映射 - 100
14:00:20.136 [http-nio-9201-exec-9] INFO  c.r.f.w.WebSocketServer - [onOpen,100] - 
 建立连接 - org.apache.tomcat.websocket.WsSession@7ef9edc4
14:00:20.136 [http-nio-9201-exec-9] INFO  c.r.f.w.WebSocketServer - [onOpen,101] - 
 当前人数 - 1
14:00:20.139 [http-nio-9201-exec-2] INFO  c.r.f.w.WebSocketServer - [onClose,113] - 
 关闭连接 - org.apache.tomcat.websocket.WsSession@7ef9edc4
14:00:20.139 [http-nio-9201-exec-2] INFO  c.r.f.w.WebSocketUsers - [remove,117] - 
 正在移出用户 - 26
14:00:20.140 [http-nio-9201-exec-2] INFO  c.r.f.w.WebSocketUsers - [remove,122] - 
 移出结果 - 成功
14:00:20.140 [http-nio-9201-exec-2] INFO  c.r.f.w.WebSocketServer - [onError,132] - 
 连接异常 - 26
14:00:20.141 [http-nio-9201-exec-2] INFO  c.r.f.w.WebSocketServer - [onError,133] - 
 异常信息 - {}
java.lang.NullPointerException: null
	at java.base/java.util.concurrent.ConcurrentHashMap.get(ConcurrentHashMap.java:936)
	at com.ruoyi.framework.websocket.WebSocketUsers.getSessionByUserId(WebSocketUsers.java:148)
	at com.ruoyi.framework.websocket.WebSocketServer.onMessage(WebSocketServer.java:175)
	at jdk.internal.reflect.GeneratedMethodAccessor114.invoke(Unknown Source)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:566)
	at org.apache.tomcat.websocket.pojo.PojoMessageHandlerWholeBase.onMessage(PojoMessageHandlerWholeBase.java:101)
	at org.apache.tomcat.websocket.WsFrameBase.sendMessageText(WsFrameBase.java:390)
	at org.apache.tomcat.websocket.server.WsFrameServer.sendMessageText(WsFrameServer.java:130)
	at org.apache.tomcat.websocket.WsFrameBase.processDataText(WsFrameBase.java:484)
	at org.apache.tomcat.websocket.WsFrameBase.processData(WsFrameBase.java:284)
	at org.apache.tomcat.websocket.WsFrameBase.processInputBuffer(WsFrameBase.java:130)
	at org.apache.tomcat.websocket.server.WsFrameServer.onDataAvailable(WsFrameServer.java:85)
	at org.apache.tomcat.websocket.server.WsFrameServer.doOnDataAvailable(WsFrameServer.java:184)
	at org.apache.tomcat.websocket.server.WsFrameServer.notifyDataAvailable(WsFrameServer.java:164)
	at org.apache.tomcat.websocket.server.WsHttpUpgradeHandler.upgradeDispatch(WsHttpUpgradeHandler.java:152)
	at org.apache.coyote.http11.upgrade.UpgradeProcessorInternal.dispatch(UpgradeProcessorInternal.java:60)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:57)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:928)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1794)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:52)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1191)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.base/java.lang.Thread.run(Thread.java:834)
14:00:20.142 [http-nio-9201-exec-2] INFO  c.r.f.w.WebSocketUsers - [remove,117] - 
 正在移出用户 - 26
14:00:20.142 [http-nio-9201-exec-2] INFO  c.r.f.w.WebSocketUsers - [removeUserSessions,96] - 
 正在移出用户Session映射 - 100
14:00:25.177 [http-nio-9201-exec-1] INFO  c.r.f.w.WebSocketServer - [onOpen,100] - 
 建立连接 - org.apache.tomcat.websocket.WsSession@4d3dad
14:00:25.177 [http-nio-9201-exec-1] INFO  c.r.f.w.WebSocketServer - [onOpen,101] - 
 当前人数 - 1
14:00:25.182 [http-nio-9201-exec-10] INFO  c.r.f.w.WebSocketServer - [onClose,113] - 
 关闭连接 - org.apache.tomcat.websocket.WsSession@4d3dad
14:00:25.183 [http-nio-9201-exec-10] INFO  c.r.f.w.WebSocketUsers - [remove,117] - 
 正在移出用户 - 27
14:00:25.183 [http-nio-9201-exec-10] INFO  c.r.f.w.WebSocketUsers - [remove,122] - 
 移出结果 - 成功
14:00:25.183 [http-nio-9201-exec-10] INFO  c.r.f.w.WebSocketServer - [onError,132] - 
 连接异常 - 27
14:00:25.184 [http-nio-9201-exec-10] INFO  c.r.f.w.WebSocketServer - [onError,133] - 
 异常信息 - {}
java.lang.NullPointerException: null
	at java.base/java.util.concurrent.ConcurrentHashMap.get(ConcurrentHashMap.java:936)
	at com.ruoyi.framework.websocket.WebSocketUsers.getSessionByUserId(WebSocketUsers.java:148)
	at com.ruoyi.framework.websocket.WebSocketServer.onMessage(WebSocketServer.java:175)
	at jdk.internal.reflect.GeneratedMethodAccessor114.invoke(Unknown Source)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:566)
	at org.apache.tomcat.websocket.pojo.PojoMessageHandlerWholeBase.onMessage(PojoMessageHandlerWholeBase.java:101)
	at org.apache.tomcat.websocket.WsFrameBase.sendMessageText(WsFrameBase.java:390)
	at org.apache.tomcat.websocket.server.WsFrameServer.sendMessageText(WsFrameServer.java:130)
	at org.apache.tomcat.websocket.WsFrameBase.processDataText(WsFrameBase.java:484)
	at org.apache.tomcat.websocket.WsFrameBase.processData(WsFrameBase.java:284)
	at org.apache.tomcat.websocket.WsFrameBase.processInputBuffer(WsFrameBase.java:130)
	at org.apache.tomcat.websocket.server.WsFrameServer.onDataAvailable(WsFrameServer.java:85)
	at org.apache.tomcat.websocket.server.WsFrameServer.doOnDataAvailable(WsFrameServer.java:184)
	at org.apache.tomcat.websocket.server.WsFrameServer.notifyDataAvailable(WsFrameServer.java:164)
	at org.apache.tomcat.websocket.server.WsHttpUpgradeHandler.upgradeDispatch(WsHttpUpgradeHandler.java:152)
	at org.apache.coyote.http11.upgrade.UpgradeProcessorInternal.dispatch(UpgradeProcessorInternal.java:60)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:57)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:928)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1794)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:52)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1191)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.base/java.lang.Thread.run(Thread.java:834)
14:00:25.186 [http-nio-9201-exec-10] INFO  c.r.f.w.WebSocketUsers - [remove,117] - 
 正在移出用户 - 27
14:00:25.187 [http-nio-9201-exec-10] INFO  c.r.f.w.WebSocketUsers - [removeUserSessions,96] - 
 正在移出用户Session映射 - 100
14:00:30.224 [http-nio-9201-exec-3] INFO  c.r.f.w.WebSocketServer - [onOpen,100] - 
 建立连接 - org.apache.tomcat.websocket.WsSession@19ae3161
14:00:30.224 [http-nio-9201-exec-3] INFO  c.r.f.w.WebSocketServer - [onOpen,101] - 
 当前人数 - 1
14:00:30.228 [http-nio-9201-exec-6] INFO  c.r.f.w.WebSocketServer - [onClose,113] - 
 关闭连接 - org.apache.tomcat.websocket.WsSession@19ae3161
14:00:30.228 [http-nio-9201-exec-6] INFO  c.r.f.w.WebSocketUsers - [remove,117] - 
 正在移出用户 - 28
14:00:30.228 [http-nio-9201-exec-6] INFO  c.r.f.w.WebSocketUsers - [remove,122] - 
 移出结果 - 成功
14:00:30.229 [http-nio-9201-exec-6] INFO  c.r.f.w.WebSocketServer - [onError,132] - 
 连接异常 - 28
14:00:30.229 [http-nio-9201-exec-6] INFO  c.r.f.w.WebSocketServer - [onError,133] - 
 异常信息 - {}
java.lang.NullPointerException: null
	at java.base/java.util.concurrent.ConcurrentHashMap.get(ConcurrentHashMap.java:936)
	at com.ruoyi.framework.websocket.WebSocketUsers.getSessionByUserId(WebSocketUsers.java:148)
	at com.ruoyi.framework.websocket.WebSocketServer.onMessage(WebSocketServer.java:175)
	at jdk.internal.reflect.GeneratedMethodAccessor114.invoke(Unknown Source)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:566)
	at org.apache.tomcat.websocket.pojo.PojoMessageHandlerWholeBase.onMessage(PojoMessageHandlerWholeBase.java:101)
	at org.apache.tomcat.websocket.WsFrameBase.sendMessageText(WsFrameBase.java:390)
	at org.apache.tomcat.websocket.server.WsFrameServer.sendMessageText(WsFrameServer.java:130)
	at org.apache.tomcat.websocket.WsFrameBase.processDataText(WsFrameBase.java:484)
	at org.apache.tomcat.websocket.WsFrameBase.processData(WsFrameBase.java:284)
	at org.apache.tomcat.websocket.WsFrameBase.processInputBuffer(WsFrameBase.java:130)
	at org.apache.tomcat.websocket.server.WsFrameServer.onDataAvailable(WsFrameServer.java:85)
	at org.apache.tomcat.websocket.server.WsFrameServer.doOnDataAvailable(WsFrameServer.java:184)
	at org.apache.tomcat.websocket.server.WsFrameServer.notifyDataAvailable(WsFrameServer.java:164)
	at org.apache.tomcat.websocket.server.WsHttpUpgradeHandler.upgradeDispatch(WsHttpUpgradeHandler.java:152)
	at org.apache.coyote.http11.upgrade.UpgradeProcessorInternal.dispatch(UpgradeProcessorInternal.java:60)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:57)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:928)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1794)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:52)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1191)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.base/java.lang.Thread.run(Thread.java:834)
14:00:30.230 [http-nio-9201-exec-6] INFO  c.r.f.w.WebSocketUsers - [remove,117] - 
 正在移出用户 - 28
14:00:30.230 [http-nio-9201-exec-6] INFO  c.r.f.w.WebSocketUsers - [removeUserSessions,96] - 
 正在移出用户Session映射 - 100
14:00:35.262 [http-nio-9201-exec-4] INFO  c.r.f.w.WebSocketServer - [onOpen,100] - 
 建立连接 - org.apache.tomcat.websocket.WsSession@3c0550fa
14:00:35.262 [http-nio-9201-exec-4] INFO  c.r.f.w.WebSocketServer - [onOpen,101] - 
 当前人数 - 1
14:00:35.267 [http-nio-9201-exec-5] INFO  c.r.f.w.WebSocketServer - [onClose,113] - 
 关闭连接 - org.apache.tomcat.websocket.WsSession@3c0550fa
14:00:35.267 [http-nio-9201-exec-5] INFO  c.r.f.w.WebSocketUsers - [remove,117] - 
 正在移出用户 - 29
14:00:35.267 [http-nio-9201-exec-5] INFO  c.r.f.w.WebSocketUsers - [remove,122] - 
 移出结果 - 成功
14:00:35.268 [http-nio-9201-exec-5] INFO  c.r.f.w.WebSocketServer - [onError,132] - 
 连接异常 - 29
14:00:35.268 [http-nio-9201-exec-5] INFO  c.r.f.w.WebSocketServer - [onError,133] - 
 异常信息 - {}
java.lang.NullPointerException: null
	at java.base/java.util.concurrent.ConcurrentHashMap.get(ConcurrentHashMap.java:936)
	at com.ruoyi.framework.websocket.WebSocketUsers.getSessionByUserId(WebSocketUsers.java:148)
	at com.ruoyi.framework.websocket.WebSocketServer.onMessage(WebSocketServer.java:175)
	at jdk.internal.reflect.GeneratedMethodAccessor114.invoke(Unknown Source)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:566)
	at org.apache.tomcat.websocket.pojo.PojoMessageHandlerWholeBase.onMessage(PojoMessageHandlerWholeBase.java:101)
	at org.apache.tomcat.websocket.WsFrameBase.sendMessageText(WsFrameBase.java:390)
	at org.apache.tomcat.websocket.server.WsFrameServer.sendMessageText(WsFrameServer.java:130)
	at org.apache.tomcat.websocket.WsFrameBase.processDataText(WsFrameBase.java:484)
	at org.apache.tomcat.websocket.WsFrameBase.processData(WsFrameBase.java:284)
	at org.apache.tomcat.websocket.WsFrameBase.processInputBuffer(WsFrameBase.java:130)
	at org.apache.tomcat.websocket.server.WsFrameServer.onDataAvailable(WsFrameServer.java:85)
	at org.apache.tomcat.websocket.server.WsFrameServer.doOnDataAvailable(WsFrameServer.java:184)
	at org.apache.tomcat.websocket.server.WsFrameServer.notifyDataAvailable(WsFrameServer.java:164)
	at org.apache.tomcat.websocket.server.WsHttpUpgradeHandler.upgradeDispatch(WsHttpUpgradeHandler.java:152)
	at org.apache.coyote.http11.upgrade.UpgradeProcessorInternal.dispatch(UpgradeProcessorInternal.java:60)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:57)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:928)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1794)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:52)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1191)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.base/java.lang.Thread.run(Thread.java:834)
14:00:35.269 [http-nio-9201-exec-5] INFO  c.r.f.w.WebSocketUsers - [remove,117] - 
 正在移出用户 - 29
14:00:35.269 [http-nio-9201-exec-5] INFO  c.r.f.w.WebSocketUsers - [removeUserSessions,96] - 
 正在移出用户Session映射 - 100
14:00:40.303 [http-nio-9201-exec-8] INFO  c.r.f.w.WebSocketServer - [onOpen,100] - 
 建立连接 - org.apache.tomcat.websocket.WsSession@10c025b4
14:00:40.303 [http-nio-9201-exec-8] INFO  c.r.f.w.WebSocketServer - [onOpen,101] - 
 当前人数 - 1
14:00:40.306 [http-nio-9201-exec-7] INFO  c.r.f.w.WebSocketServer - [onClose,113] - 
 关闭连接 - org.apache.tomcat.websocket.WsSession@10c025b4
14:00:40.307 [http-nio-9201-exec-7] INFO  c.r.f.w.WebSocketUsers - [remove,117] - 
 正在移出用户 - 2a
14:00:40.307 [http-nio-9201-exec-7] INFO  c.r.f.w.WebSocketUsers - [remove,122] - 
 移出结果 - 成功
14:00:40.307 [http-nio-9201-exec-7] INFO  c.r.f.w.WebSocketServer - [onError,132] - 
 连接异常 - 2a
14:00:40.307 [http-nio-9201-exec-7] INFO  c.r.f.w.WebSocketServer - [onError,133] - 
 异常信息 - {}
java.lang.NullPointerException: null
	at java.base/java.util.concurrent.ConcurrentHashMap.get(ConcurrentHashMap.java:936)
	at com.ruoyi.framework.websocket.WebSocketUsers.getSessionByUserId(WebSocketUsers.java:148)
	at com.ruoyi.framework.websocket.WebSocketServer.onMessage(WebSocketServer.java:175)
	at jdk.internal.reflect.GeneratedMethodAccessor114.invoke(Unknown Source)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:566)
	at org.apache.tomcat.websocket.pojo.PojoMessageHandlerWholeBase.onMessage(PojoMessageHandlerWholeBase.java:101)
	at org.apache.tomcat.websocket.WsFrameBase.sendMessageText(WsFrameBase.java:390)
	at org.apache.tomcat.websocket.server.WsFrameServer.sendMessageText(WsFrameServer.java:130)
	at org.apache.tomcat.websocket.WsFrameBase.processDataText(WsFrameBase.java:484)
	at org.apache.tomcat.websocket.WsFrameBase.processData(WsFrameBase.java:284)
	at org.apache.tomcat.websocket.WsFrameBase.processInputBuffer(WsFrameBase.java:130)
	at org.apache.tomcat.websocket.server.WsFrameServer.onDataAvailable(WsFrameServer.java:85)
	at org.apache.tomcat.websocket.server.WsFrameServer.doOnDataAvailable(WsFrameServer.java:184)
	at org.apache.tomcat.websocket.server.WsFrameServer.notifyDataAvailable(WsFrameServer.java:164)
	at org.apache.tomcat.websocket.server.WsHttpUpgradeHandler.upgradeDispatch(WsHttpUpgradeHandler.java:152)
	at org.apache.coyote.http11.upgrade.UpgradeProcessorInternal.dispatch(UpgradeProcessorInternal.java:60)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:57)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:928)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1794)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:52)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1191)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.base/java.lang.Thread.run(Thread.java:834)
14:00:40.308 [http-nio-9201-exec-7] INFO  c.r.f.w.WebSocketUsers - [remove,117] - 
 正在移出用户 - 2a
14:00:40.308 [http-nio-9201-exec-7] INFO  c.r.f.w.WebSocketUsers - [removeUserSessions,96] - 
 正在移出用户Session映射 - 100
14:00:45.334 [http-nio-9201-exec-9] INFO  c.r.f.w.WebSocketServer - [onOpen,100] - 
 建立连接 - org.apache.tomcat.websocket.WsSession@106a113f
14:00:45.335 [http-nio-9201-exec-9] INFO  c.r.f.w.WebSocketServer - [onOpen,101] - 
 当前人数 - 1
14:00:45.338 [http-nio-9201-exec-2] INFO  c.r.f.w.WebSocketServer - [onClose,113] - 
 关闭连接 - org.apache.tomcat.websocket.WsSession@106a113f
14:00:45.339 [http-nio-9201-exec-2] INFO  c.r.f.w.WebSocketUsers - [remove,117] - 
 正在移出用户 - 2b
14:00:45.339 [http-nio-9201-exec-2] INFO  c.r.f.w.WebSocketUsers - [remove,122] - 
 移出结果 - 成功
14:00:45.339 [http-nio-9201-exec-2] INFO  c.r.f.w.WebSocketServer - [onError,132] - 
 连接异常 - 2b
14:00:45.340 [http-nio-9201-exec-2] INFO  c.r.f.w.WebSocketServer - [onError,133] - 
 异常信息 - {}
java.lang.NullPointerException: null
	at java.base/java.util.concurrent.ConcurrentHashMap.get(ConcurrentHashMap.java:936)
	at com.ruoyi.framework.websocket.WebSocketUsers.getSessionByUserId(WebSocketUsers.java:148)
	at com.ruoyi.framework.websocket.WebSocketServer.onMessage(WebSocketServer.java:175)
	at jdk.internal.reflect.GeneratedMethodAccessor114.invoke(Unknown Source)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:566)
	at org.apache.tomcat.websocket.pojo.PojoMessageHandlerWholeBase.onMessage(PojoMessageHandlerWholeBase.java:101)
	at org.apache.tomcat.websocket.WsFrameBase.sendMessageText(WsFrameBase.java:390)
	at org.apache.tomcat.websocket.server.WsFrameServer.sendMessageText(WsFrameServer.java:130)
	at org.apache.tomcat.websocket.WsFrameBase.processDataText(WsFrameBase.java:484)
	at org.apache.tomcat.websocket.WsFrameBase.processData(WsFrameBase.java:284)
	at org.apache.tomcat.websocket.WsFrameBase.processInputBuffer(WsFrameBase.java:130)
	at org.apache.tomcat.websocket.server.WsFrameServer.onDataAvailable(WsFrameServer.java:85)
	at org.apache.tomcat.websocket.server.WsFrameServer.doOnDataAvailable(WsFrameServer.java:184)
	at org.apache.tomcat.websocket.server.WsFrameServer.notifyDataAvailable(WsFrameServer.java:164)
	at org.apache.tomcat.websocket.server.WsHttpUpgradeHandler.upgradeDispatch(WsHttpUpgradeHandler.java:152)
	at org.apache.coyote.http11.upgrade.UpgradeProcessorInternal.dispatch(UpgradeProcessorInternal.java:60)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:57)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:928)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1794)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:52)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1191)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.base/java.lang.Thread.run(Thread.java:834)
14:00:45.341 [http-nio-9201-exec-2] INFO  c.r.f.w.WebSocketUsers - [remove,117] - 
 正在移出用户 - 2b
14:00:45.342 [http-nio-9201-exec-2] INFO  c.r.f.w.WebSocketUsers - [removeUserSessions,96] - 
 正在移出用户Session映射 - 100
14:00:50.379 [http-nio-9201-exec-1] INFO  c.r.f.w.WebSocketServer - [onOpen,100] - 
 建立连接 - org.apache.tomcat.websocket.WsSession@65065253
14:00:50.379 [http-nio-9201-exec-1] INFO  c.r.f.w.WebSocketServer - [onOpen,101] - 
 当前人数 - 1
14:00:50.383 [http-nio-9201-exec-10] INFO  c.r.f.w.WebSocketServer - [onClose,113] - 
 关闭连接 - org.apache.tomcat.websocket.WsSession@65065253
14:00:50.384 [http-nio-9201-exec-10] INFO  c.r.f.w.WebSocketUsers - [remove,117] - 
 正在移出用户 - 2c
14:00:50.384 [http-nio-9201-exec-10] INFO  c.r.f.w.WebSocketUsers - [remove,122] - 
 移出结果 - 成功
14:00:50.384 [http-nio-9201-exec-10] INFO  c.r.f.w.WebSocketServer - [onError,132] - 
 连接异常 - 2c
14:00:50.385 [http-nio-9201-exec-10] INFO  c.r.f.w.WebSocketServer - [onError,133] - 
 异常信息 - {}
java.lang.NullPointerException: null
	at java.base/java.util.concurrent.ConcurrentHashMap.get(ConcurrentHashMap.java:936)
	at com.ruoyi.framework.websocket.WebSocketUsers.getSessionByUserId(WebSocketUsers.java:148)
	at com.ruoyi.framework.websocket.WebSocketServer.onMessage(WebSocketServer.java:175)
	at jdk.internal.reflect.GeneratedMethodAccessor114.invoke(Unknown Source)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:566)
	at org.apache.tomcat.websocket.pojo.PojoMessageHandlerWholeBase.onMessage(PojoMessageHandlerWholeBase.java:101)
	at org.apache.tomcat.websocket.WsFrameBase.sendMessageText(WsFrameBase.java:390)
	at org.apache.tomcat.websocket.server.WsFrameServer.sendMessageText(WsFrameServer.java:130)
	at org.apache.tomcat.websocket.WsFrameBase.processDataText(WsFrameBase.java:484)
	at org.apache.tomcat.websocket.WsFrameBase.processData(WsFrameBase.java:284)
	at org.apache.tomcat.websocket.WsFrameBase.processInputBuffer(WsFrameBase.java:130)
	at org.apache.tomcat.websocket.server.WsFrameServer.onDataAvailable(WsFrameServer.java:85)
	at org.apache.tomcat.websocket.server.WsFrameServer.doOnDataAvailable(WsFrameServer.java:184)
	at org.apache.tomcat.websocket.server.WsFrameServer.notifyDataAvailable(WsFrameServer.java:164)
	at org.apache.tomcat.websocket.server.WsHttpUpgradeHandler.upgradeDispatch(WsHttpUpgradeHandler.java:152)
	at org.apache.coyote.http11.upgrade.UpgradeProcessorInternal.dispatch(UpgradeProcessorInternal.java:60)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:57)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:928)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1794)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:52)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1191)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.base/java.lang.Thread.run(Thread.java:834)
14:00:50.385 [http-nio-9201-exec-10] INFO  c.r.f.w.WebSocketUsers - [remove,117] - 
 正在移出用户 - 2c
14:00:50.385 [http-nio-9201-exec-10] INFO  c.r.f.w.WebSocketUsers - [removeUserSessions,96] - 
 正在移出用户Session映射 - 100
14:00:55.414 [http-nio-9201-exec-3] INFO  c.r.f.w.WebSocketServer - [onOpen,100] - 
 建立连接 - org.apache.tomcat.websocket.WsSession@57246f22
14:00:55.414 [http-nio-9201-exec-3] INFO  c.r.f.w.WebSocketServer - [onOpen,101] - 
 当前人数 - 1
14:00:55.420 [http-nio-9201-exec-6] INFO  c.r.f.w.WebSocketServer - [onClose,113] - 
 关闭连接 - org.apache.tomcat.websocket.WsSession@57246f22
14:00:55.420 [http-nio-9201-exec-6] INFO  c.r.f.w.WebSocketUsers - [remove,117] - 
 正在移出用户 - 2d
14:00:55.420 [http-nio-9201-exec-6] INFO  c.r.f.w.WebSocketUsers - [remove,122] - 
 移出结果 - 成功
14:00:55.420 [http-nio-9201-exec-6] INFO  c.r.f.w.WebSocketServer - [onError,132] - 
 连接异常 - 2d
14:00:55.421 [http-nio-9201-exec-6] INFO  c.r.f.w.WebSocketServer - [onError,133] - 
 异常信息 - {}
java.lang.NullPointerException: null
	at java.base/java.util.concurrent.ConcurrentHashMap.get(ConcurrentHashMap.java:936)
	at com.ruoyi.framework.websocket.WebSocketUsers.getSessionByUserId(WebSocketUsers.java:148)
	at com.ruoyi.framework.websocket.WebSocketServer.onMessage(WebSocketServer.java:175)
	at jdk.internal.reflect.GeneratedMethodAccessor114.invoke(Unknown Source)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:566)
	at org.apache.tomcat.websocket.pojo.PojoMessageHandlerWholeBase.onMessage(PojoMessageHandlerWholeBase.java:101)
	at org.apache.tomcat.websocket.WsFrameBase.sendMessageText(WsFrameBase.java:390)
	at org.apache.tomcat.websocket.server.WsFrameServer.sendMessageText(WsFrameServer.java:130)
	at org.apache.tomcat.websocket.WsFrameBase.processDataText(WsFrameBase.java:484)
	at org.apache.tomcat.websocket.WsFrameBase.processData(WsFrameBase.java:284)
	at org.apache.tomcat.websocket.WsFrameBase.processInputBuffer(WsFrameBase.java:130)
	at org.apache.tomcat.websocket.server.WsFrameServer.onDataAvailable(WsFrameServer.java:85)
	at org.apache.tomcat.websocket.server.WsFrameServer.doOnDataAvailable(WsFrameServer.java:184)
	at org.apache.tomcat.websocket.server.WsFrameServer.notifyDataAvailable(WsFrameServer.java:164)
	at org.apache.tomcat.websocket.server.WsHttpUpgradeHandler.upgradeDispatch(WsHttpUpgradeHandler.java:152)
	at org.apache.coyote.http11.upgrade.UpgradeProcessorInternal.dispatch(UpgradeProcessorInternal.java:60)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:57)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:928)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1794)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:52)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1191)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.base/java.lang.Thread.run(Thread.java:834)
14:00:55.422 [http-nio-9201-exec-6] INFO  c.r.f.w.WebSocketUsers - [remove,117] - 
 正在移出用户 - 2d
14:00:55.422 [http-nio-9201-exec-6] INFO  c.r.f.w.WebSocketUsers - [removeUserSessions,96] - 
 正在移出用户Session映射 - 100
14:01:00.455 [http-nio-9201-exec-4] INFO  c.r.f.w.WebSocketServer - [onOpen,100] - 
 建立连接 - org.apache.tomcat.websocket.WsSession@3e148223
14:01:00.456 [http-nio-9201-exec-4] INFO  c.r.f.w.WebSocketServer - [onOpen,101] - 
 当前人数 - 1
14:01:00.459 [http-nio-9201-exec-5] INFO  c.r.f.w.WebSocketServer - [onClose,113] - 
 关闭连接 - org.apache.tomcat.websocket.WsSession@3e148223
14:01:00.459 [http-nio-9201-exec-5] INFO  c.r.f.w.WebSocketUsers - [remove,117] - 
 正在移出用户 - 2e
14:01:00.459 [http-nio-9201-exec-5] INFO  c.r.f.w.WebSocketUsers - [remove,122] - 
 移出结果 - 成功
14:01:00.459 [http-nio-9201-exec-5] INFO  c.r.f.w.WebSocketServer - [onError,132] - 
 连接异常 - 2e
14:01:00.461 [http-nio-9201-exec-5] INFO  c.r.f.w.WebSocketServer - [onError,133] - 
 异常信息 - {}
java.lang.NullPointerException: null
	at java.base/java.util.concurrent.ConcurrentHashMap.get(ConcurrentHashMap.java:936)
	at com.ruoyi.framework.websocket.WebSocketUsers.getSessionByUserId(WebSocketUsers.java:148)
	at com.ruoyi.framework.websocket.WebSocketServer.onMessage(WebSocketServer.java:175)
	at jdk.internal.reflect.GeneratedMethodAccessor114.invoke(Unknown Source)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:566)
	at org.apache.tomcat.websocket.pojo.PojoMessageHandlerWholeBase.onMessage(PojoMessageHandlerWholeBase.java:101)
	at org.apache.tomcat.websocket.WsFrameBase.sendMessageText(WsFrameBase.java:390)
	at org.apache.tomcat.websocket.server.WsFrameServer.sendMessageText(WsFrameServer.java:130)
	at org.apache.tomcat.websocket.WsFrameBase.processDataText(WsFrameBase.java:484)
	at org.apache.tomcat.websocket.WsFrameBase.processData(WsFrameBase.java:284)
	at org.apache.tomcat.websocket.WsFrameBase.processInputBuffer(WsFrameBase.java:130)
	at org.apache.tomcat.websocket.server.WsFrameServer.onDataAvailable(WsFrameServer.java:85)
	at org.apache.tomcat.websocket.server.WsFrameServer.doOnDataAvailable(WsFrameServer.java:184)
	at org.apache.tomcat.websocket.server.WsFrameServer.notifyDataAvailable(WsFrameServer.java:164)
	at org.apache.tomcat.websocket.server.WsHttpUpgradeHandler.upgradeDispatch(WsHttpUpgradeHandler.java:152)
	at org.apache.coyote.http11.upgrade.UpgradeProcessorInternal.dispatch(UpgradeProcessorInternal.java:60)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:57)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:928)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1794)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:52)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1191)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.base/java.lang.Thread.run(Thread.java:834)
14:01:00.461 [http-nio-9201-exec-5] INFO  c.r.f.w.WebSocketUsers - [remove,117] - 
 正在移出用户 - 2e
14:01:00.462 [http-nio-9201-exec-5] INFO  c.r.f.w.WebSocketUsers - [removeUserSessions,96] - 
 正在移出用户Session映射 - 100
14:01:05.491 [http-nio-9201-exec-8] INFO  c.r.f.w.WebSocketServer - [onOpen,100] - 
 建立连接 - org.apache.tomcat.websocket.WsSession@3a65c781
14:01:05.492 [http-nio-9201-exec-8] INFO  c.r.f.w.WebSocketServer - [onOpen,101] - 
 当前人数 - 1
14:01:05.496 [http-nio-9201-exec-7] INFO  c.r.f.w.WebSocketServer - [onClose,113] - 
 关闭连接 - org.apache.tomcat.websocket.WsSession@3a65c781
14:01:05.496 [http-nio-9201-exec-7] INFO  c.r.f.w.WebSocketUsers - [remove,117] - 
 正在移出用户 - 2f
14:01:05.496 [http-nio-9201-exec-7] INFO  c.r.f.w.WebSocketUsers - [remove,122] - 
 移出结果 - 成功
14:01:05.496 [http-nio-9201-exec-7] INFO  c.r.f.w.WebSocketServer - [onError,132] - 
 连接异常 - 2f
14:01:05.497 [http-nio-9201-exec-7] INFO  c.r.f.w.WebSocketServer - [onError,133] - 
 异常信息 - {}
java.lang.NullPointerException: null
	at java.base/java.util.concurrent.ConcurrentHashMap.get(ConcurrentHashMap.java:936)
	at com.ruoyi.framework.websocket.WebSocketUsers.getSessionByUserId(WebSocketUsers.java:148)
	at com.ruoyi.framework.websocket.WebSocketServer.onMessage(WebSocketServer.java:175)
	at jdk.internal.reflect.GeneratedMethodAccessor114.invoke(Unknown Source)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:566)
	at org.apache.tomcat.websocket.pojo.PojoMessageHandlerWholeBase.onMessage(PojoMessageHandlerWholeBase.java:101)
	at org.apache.tomcat.websocket.WsFrameBase.sendMessageText(WsFrameBase.java:390)
	at org.apache.tomcat.websocket.server.WsFrameServer.sendMessageText(WsFrameServer.java:130)
	at org.apache.tomcat.websocket.WsFrameBase.processDataText(WsFrameBase.java:484)
	at org.apache.tomcat.websocket.WsFrameBase.processData(WsFrameBase.java:284)
	at org.apache.tomcat.websocket.WsFrameBase.processInputBuffer(WsFrameBase.java:130)
	at org.apache.tomcat.websocket.server.WsFrameServer.onDataAvailable(WsFrameServer.java:85)
	at org.apache.tomcat.websocket.server.WsFrameServer.doOnDataAvailable(WsFrameServer.java:184)
	at org.apache.tomcat.websocket.server.WsFrameServer.notifyDataAvailable(WsFrameServer.java:164)
	at org.apache.tomcat.websocket.server.WsHttpUpgradeHandler.upgradeDispatch(WsHttpUpgradeHandler.java:152)
	at org.apache.coyote.http11.upgrade.UpgradeProcessorInternal.dispatch(UpgradeProcessorInternal.java:60)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:57)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:928)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1794)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:52)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1191)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.base/java.lang.Thread.run(Thread.java:834)
14:01:05.497 [http-nio-9201-exec-7] INFO  c.r.f.w.WebSocketUsers - [remove,117] - 
 正在移出用户 - 2f
14:01:05.498 [http-nio-9201-exec-7] INFO  c.r.f.w.WebSocketUsers - [removeUserSessions,96] - 
 正在移出用户Session映射 - 100
14:01:10.532 [http-nio-9201-exec-9] INFO  c.r.f.w.WebSocketServer - [onOpen,100] - 
 建立连接 - org.apache.tomcat.websocket.WsSession@5b428806
14:01:10.532 [http-nio-9201-exec-9] INFO  c.r.f.w.WebSocketServer - [onOpen,101] - 
 当前人数 - 1
14:01:10.536 [http-nio-9201-exec-2] INFO  c.r.f.w.WebSocketServer - [onClose,113] - 
 关闭连接 - org.apache.tomcat.websocket.WsSession@5b428806
14:01:10.537 [http-nio-9201-exec-2] INFO  c.r.f.w.WebSocketUsers - [remove,117] - 
 正在移出用户 - 30
14:01:10.537 [http-nio-9201-exec-2] INFO  c.r.f.w.WebSocketUsers - [remove,122] - 
 移出结果 - 成功
14:01:10.537 [http-nio-9201-exec-2] INFO  c.r.f.w.WebSocketServer - [onError,132] - 
 连接异常 - 30
14:01:10.538 [http-nio-9201-exec-2] INFO  c.r.f.w.WebSocketServer - [onError,133] - 
 异常信息 - {}
java.lang.NullPointerException: null
	at java.base/java.util.concurrent.ConcurrentHashMap.get(ConcurrentHashMap.java:936)
	at com.ruoyi.framework.websocket.WebSocketUsers.getSessionByUserId(WebSocketUsers.java:148)
	at com.ruoyi.framework.websocket.WebSocketServer.onMessage(WebSocketServer.java:175)
	at jdk.internal.reflect.GeneratedMethodAccessor114.invoke(Unknown Source)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:566)
	at org.apache.tomcat.websocket.pojo.PojoMessageHandlerWholeBase.onMessage(PojoMessageHandlerWholeBase.java:101)
	at org.apache.tomcat.websocket.WsFrameBase.sendMessageText(WsFrameBase.java:390)
	at org.apache.tomcat.websocket.server.WsFrameServer.sendMessageText(WsFrameServer.java:130)
	at org.apache.tomcat.websocket.WsFrameBase.processDataText(WsFrameBase.java:484)
	at org.apache.tomcat.websocket.WsFrameBase.processData(WsFrameBase.java:284)
	at org.apache.tomcat.websocket.WsFrameBase.processInputBuffer(WsFrameBase.java:130)
	at org.apache.tomcat.websocket.server.WsFrameServer.onDataAvailable(WsFrameServer.java:85)
	at org.apache.tomcat.websocket.server.WsFrameServer.doOnDataAvailable(WsFrameServer.java:184)
	at org.apache.tomcat.websocket.server.WsFrameServer.notifyDataAvailable(WsFrameServer.java:164)
	at org.apache.tomcat.websocket.server.WsHttpUpgradeHandler.upgradeDispatch(WsHttpUpgradeHandler.java:152)
	at org.apache.coyote.http11.upgrade.UpgradeProcessorInternal.dispatch(UpgradeProcessorInternal.java:60)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:57)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:928)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1794)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:52)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1191)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.base/java.lang.Thread.run(Thread.java:834)
14:01:10.539 [http-nio-9201-exec-2] INFO  c.r.f.w.WebSocketUsers - [remove,117] - 
 正在移出用户 - 30
14:01:10.539 [http-nio-9201-exec-2] INFO  c.r.f.w.WebSocketUsers - [removeUserSessions,96] - 
 正在移出用户Session映射 - 100
14:01:15.579 [http-nio-9201-exec-1] INFO  c.r.f.w.WebSocketServer - [onOpen,100] - 
 建立连接 - org.apache.tomcat.websocket.WsSession@645627d0
14:01:15.579 [http-nio-9201-exec-1] INFO  c.r.f.w.WebSocketServer - [onOpen,101] - 
 当前人数 - 1
14:01:15.584 [http-nio-9201-exec-10] INFO  c.r.f.w.WebSocketServer - [onClose,113] - 
 关闭连接 - org.apache.tomcat.websocket.WsSession@645627d0
14:01:15.585 [http-nio-9201-exec-10] INFO  c.r.f.w.WebSocketUsers - [remove,117] - 
 正在移出用户 - 31
14:01:15.585 [http-nio-9201-exec-10] INFO  c.r.f.w.WebSocketUsers - [remove,122] - 
 移出结果 - 成功
14:01:15.586 [http-nio-9201-exec-10] INFO  c.r.f.w.WebSocketServer - [onError,132] - 
 连接异常 - 31
14:01:15.586 [http-nio-9201-exec-10] INFO  c.r.f.w.WebSocketServer - [onError,133] - 
 异常信息 - {}
java.lang.NullPointerException: null
	at java.base/java.util.concurrent.ConcurrentHashMap.get(ConcurrentHashMap.java:936)
	at com.ruoyi.framework.websocket.WebSocketUsers.getSessionByUserId(WebSocketUsers.java:148)
	at com.ruoyi.framework.websocket.WebSocketServer.onMessage(WebSocketServer.java:175)
	at jdk.internal.reflect.GeneratedMethodAccessor114.invoke(Unknown Source)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:566)
	at org.apache.tomcat.websocket.pojo.PojoMessageHandlerWholeBase.onMessage(PojoMessageHandlerWholeBase.java:101)
	at org.apache.tomcat.websocket.WsFrameBase.sendMessageText(WsFrameBase.java:390)
	at org.apache.tomcat.websocket.server.WsFrameServer.sendMessageText(WsFrameServer.java:130)
	at org.apache.tomcat.websocket.WsFrameBase.processDataText(WsFrameBase.java:484)
	at org.apache.tomcat.websocket.WsFrameBase.processData(WsFrameBase.java:284)
	at org.apache.tomcat.websocket.WsFrameBase.processInputBuffer(WsFrameBase.java:130)
	at org.apache.tomcat.websocket.server.WsFrameServer.onDataAvailable(WsFrameServer.java:85)
	at org.apache.tomcat.websocket.server.WsFrameServer.doOnDataAvailable(WsFrameServer.java:184)
	at org.apache.tomcat.websocket.server.WsFrameServer.notifyDataAvailable(WsFrameServer.java:164)
	at org.apache.tomcat.websocket.server.WsHttpUpgradeHandler.upgradeDispatch(WsHttpUpgradeHandler.java:152)
	at org.apache.coyote.http11.upgrade.UpgradeProcessorInternal.dispatch(UpgradeProcessorInternal.java:60)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:57)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:928)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1794)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:52)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1191)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.base/java.lang.Thread.run(Thread.java:834)
14:01:15.587 [http-nio-9201-exec-10] INFO  c.r.f.w.WebSocketUsers - [remove,117] - 
 正在移出用户 - 31
14:01:15.587 [http-nio-9201-exec-10] INFO  c.r.f.w.WebSocketUsers - [removeUserSessions,96] - 
 正在移出用户Session映射 - 100
14:01:20.624 [http-nio-9201-exec-3] INFO  c.r.f.w.WebSocketServer - [onOpen,100] - 
 建立连接 - org.apache.tomcat.websocket.WsSession@d97b8f1
14:01:20.624 [http-nio-9201-exec-3] INFO  c.r.f.w.WebSocketServer - [onOpen,101] - 
 当前人数 - 1
14:01:20.629 [http-nio-9201-exec-6] INFO  c.r.f.w.WebSocketServer - [onClose,113] - 
 关闭连接 - org.apache.tomcat.websocket.WsSession@d97b8f1
14:01:20.630 [http-nio-9201-exec-6] INFO  c.r.f.w.WebSocketUsers - [remove,117] - 
 正在移出用户 - 32
14:01:20.630 [http-nio-9201-exec-6] INFO  c.r.f.w.WebSocketUsers - [remove,122] - 
 移出结果 - 成功
14:01:20.630 [http-nio-9201-exec-6] INFO  c.r.f.w.WebSocketServer - [onError,132] - 
 连接异常 - 32
14:01:20.632 [http-nio-9201-exec-6] INFO  c.r.f.w.WebSocketServer - [onError,133] - 
 异常信息 - {}
java.lang.NullPointerException: null
	at java.base/java.util.concurrent.ConcurrentHashMap.get(ConcurrentHashMap.java:936)
	at com.ruoyi.framework.websocket.WebSocketUsers.getSessionByUserId(WebSocketUsers.java:148)
	at com.ruoyi.framework.websocket.WebSocketServer.onMessage(WebSocketServer.java:175)
	at jdk.internal.reflect.GeneratedMethodAccessor114.invoke(Unknown Source)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:566)
	at org.apache.tomcat.websocket.pojo.PojoMessageHandlerWholeBase.onMessage(PojoMessageHandlerWholeBase.java:101)
	at org.apache.tomcat.websocket.WsFrameBase.sendMessageText(WsFrameBase.java:390)
	at org.apache.tomcat.websocket.server.WsFrameServer.sendMessageText(WsFrameServer.java:130)
	at org.apache.tomcat.websocket.WsFrameBase.processDataText(WsFrameBase.java:484)
	at org.apache.tomcat.websocket.WsFrameBase.processData(WsFrameBase.java:284)
	at org.apache.tomcat.websocket.WsFrameBase.processInputBuffer(WsFrameBase.java:130)
	at org.apache.tomcat.websocket.server.WsFrameServer.onDataAvailable(WsFrameServer.java:85)
	at org.apache.tomcat.websocket.server.WsFrameServer.doOnDataAvailable(WsFrameServer.java:184)
	at org.apache.tomcat.websocket.server.WsFrameServer.notifyDataAvailable(WsFrameServer.java:164)
	at org.apache.tomcat.websocket.server.WsHttpUpgradeHandler.upgradeDispatch(WsHttpUpgradeHandler.java:152)
	at org.apache.coyote.http11.upgrade.UpgradeProcessorInternal.dispatch(UpgradeProcessorInternal.java:60)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:57)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:928)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1794)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:52)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1191)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.base/java.lang.Thread.run(Thread.java:834)
14:01:20.632 [http-nio-9201-exec-6] INFO  c.r.f.w.WebSocketUsers - [remove,117] - 
 正在移出用户 - 32
14:01:20.633 [http-nio-9201-exec-6] INFO  c.r.f.w.WebSocketUsers - [removeUserSessions,96] - 
 正在移出用户Session映射 - 100
14:01:25.664 [http-nio-9201-exec-4] INFO  c.r.f.w.WebSocketServer - [onOpen,100] - 
 建立连接 - org.apache.tomcat.websocket.WsSession@9f45787
14:01:25.664 [http-nio-9201-exec-4] INFO  c.r.f.w.WebSocketServer - [onOpen,101] - 
 当前人数 - 1
14:01:25.669 [http-nio-9201-exec-5] INFO  c.r.f.w.WebSocketServer - [onClose,113] - 
 关闭连接 - org.apache.tomcat.websocket.WsSession@9f45787
14:01:25.669 [http-nio-9201-exec-5] INFO  c.r.f.w.WebSocketUsers - [remove,117] - 
 正在移出用户 - 33
14:01:25.669 [http-nio-9201-exec-5] INFO  c.r.f.w.WebSocketUsers - [remove,122] - 
 移出结果 - 成功
14:01:25.669 [http-nio-9201-exec-5] INFO  c.r.f.w.WebSocketServer - [onError,132] - 
 连接异常 - 33
14:01:25.671 [http-nio-9201-exec-5] INFO  c.r.f.w.WebSocketServer - [onError,133] - 
 异常信息 - {}
java.lang.NullPointerException: null
	at java.base/java.util.concurrent.ConcurrentHashMap.get(ConcurrentHashMap.java:936)
	at com.ruoyi.framework.websocket.WebSocketUsers.getSessionByUserId(WebSocketUsers.java:148)
	at com.ruoyi.framework.websocket.WebSocketServer.onMessage(WebSocketServer.java:175)
	at jdk.internal.reflect.GeneratedMethodAccessor114.invoke(Unknown Source)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:566)
	at org.apache.tomcat.websocket.pojo.PojoMessageHandlerWholeBase.onMessage(PojoMessageHandlerWholeBase.java:101)
	at org.apache.tomcat.websocket.WsFrameBase.sendMessageText(WsFrameBase.java:390)
	at org.apache.tomcat.websocket.server.WsFrameServer.sendMessageText(WsFrameServer.java:130)
	at org.apache.tomcat.websocket.WsFrameBase.processDataText(WsFrameBase.java:484)
	at org.apache.tomcat.websocket.WsFrameBase.processData(WsFrameBase.java:284)
	at org.apache.tomcat.websocket.WsFrameBase.processInputBuffer(WsFrameBase.java:130)
	at org.apache.tomcat.websocket.server.WsFrameServer.onDataAvailable(WsFrameServer.java:85)
	at org.apache.tomcat.websocket.server.WsFrameServer.doOnDataAvailable(WsFrameServer.java:184)
	at org.apache.tomcat.websocket.server.WsFrameServer.notifyDataAvailable(WsFrameServer.java:164)
	at org.apache.tomcat.websocket.server.WsHttpUpgradeHandler.upgradeDispatch(WsHttpUpgradeHandler.java:152)
	at org.apache.coyote.http11.upgrade.UpgradeProcessorInternal.dispatch(UpgradeProcessorInternal.java:60)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:57)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:928)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1794)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:52)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1191)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.base/java.lang.Thread.run(Thread.java:834)
14:01:25.671 [http-nio-9201-exec-5] INFO  c.r.f.w.WebSocketUsers - [remove,117] - 
 正在移出用户 - 33
14:01:25.672 [http-nio-9201-exec-5] INFO  c.r.f.w.WebSocketUsers - [removeUserSessions,96] - 
 正在移出用户Session映射 - 100
14:01:30.711 [http-nio-9201-exec-8] INFO  c.r.f.w.WebSocketServer - [onOpen,100] - 
 建立连接 - org.apache.tomcat.websocket.WsSession@32d7df5f
14:01:30.711 [http-nio-9201-exec-8] INFO  c.r.f.w.WebSocketServer - [onOpen,101] - 
 当前人数 - 1
14:01:30.715 [http-nio-9201-exec-7] INFO  c.r.f.w.WebSocketServer - [onClose,113] - 
 关闭连接 - org.apache.tomcat.websocket.WsSession@32d7df5f
14:01:30.715 [http-nio-9201-exec-7] INFO  c.r.f.w.WebSocketUsers - [remove,117] - 
 正在移出用户 - 34
14:01:30.716 [http-nio-9201-exec-7] INFO  c.r.f.w.WebSocketUsers - [remove,122] - 
 移出结果 - 成功
14:01:30.716 [http-nio-9201-exec-7] INFO  c.r.f.w.WebSocketServer - [onError,132] - 
 连接异常 - 34
14:01:30.717 [http-nio-9201-exec-7] INFO  c.r.f.w.WebSocketServer - [onError,133] - 
 异常信息 - {}
java.lang.NullPointerException: null
	at java.base/java.util.concurrent.ConcurrentHashMap.get(ConcurrentHashMap.java:936)
	at com.ruoyi.framework.websocket.WebSocketUsers.getSessionByUserId(WebSocketUsers.java:148)
	at com.ruoyi.framework.websocket.WebSocketServer.onMessage(WebSocketServer.java:175)
	at jdk.internal.reflect.GeneratedMethodAccessor114.invoke(Unknown Source)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:566)
	at org.apache.tomcat.websocket.pojo.PojoMessageHandlerWholeBase.onMessage(PojoMessageHandlerWholeBase.java:101)
	at org.apache.tomcat.websocket.WsFrameBase.sendMessageText(WsFrameBase.java:390)
	at org.apache.tomcat.websocket.server.WsFrameServer.sendMessageText(WsFrameServer.java:130)
	at org.apache.tomcat.websocket.WsFrameBase.processDataText(WsFrameBase.java:484)
	at org.apache.tomcat.websocket.WsFrameBase.processData(WsFrameBase.java:284)
	at org.apache.tomcat.websocket.WsFrameBase.processInputBuffer(WsFrameBase.java:130)
	at org.apache.tomcat.websocket.server.WsFrameServer.onDataAvailable(WsFrameServer.java:85)
	at org.apache.tomcat.websocket.server.WsFrameServer.doOnDataAvailable(WsFrameServer.java:184)
	at org.apache.tomcat.websocket.server.WsFrameServer.notifyDataAvailable(WsFrameServer.java:164)
	at org.apache.tomcat.websocket.server.WsHttpUpgradeHandler.upgradeDispatch(WsHttpUpgradeHandler.java:152)
	at org.apache.coyote.http11.upgrade.UpgradeProcessorInternal.dispatch(UpgradeProcessorInternal.java:60)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:57)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:928)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1794)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:52)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1191)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.base/java.lang.Thread.run(Thread.java:834)
14:01:30.717 [http-nio-9201-exec-7] INFO  c.r.f.w.WebSocketUsers - [remove,117] - 
 正在移出用户 - 34
14:01:30.718 [http-nio-9201-exec-7] INFO  c.r.f.w.WebSocketUsers - [removeUserSessions,96] - 
 正在移出用户Session映射 - 100
14:01:35.754 [http-nio-9201-exec-9] INFO  c.r.f.w.WebSocketServer - [onOpen,100] - 
 建立连接 - org.apache.tomcat.websocket.WsSession@4172b434
14:01:35.755 [http-nio-9201-exec-9] INFO  c.r.f.w.WebSocketServer - [onOpen,101] - 
 当前人数 - 1
14:01:35.760 [http-nio-9201-exec-2] INFO  c.r.f.w.WebSocketServer - [onClose,113] - 
 关闭连接 - org.apache.tomcat.websocket.WsSession@4172b434
14:01:35.761 [http-nio-9201-exec-2] INFO  c.r.f.w.WebSocketUsers - [remove,117] - 
 正在移出用户 - 35
14:01:35.761 [http-nio-9201-exec-2] INFO  c.r.f.w.WebSocketUsers - [remove,122] - 
 移出结果 - 成功
14:01:35.761 [http-nio-9201-exec-2] INFO  c.r.f.w.WebSocketServer - [onError,132] - 
 连接异常 - 35
14:01:35.763 [http-nio-9201-exec-2] INFO  c.r.f.w.WebSocketServer - [onError,133] - 
 异常信息 - {}
java.lang.NullPointerException: null
	at java.base/java.util.concurrent.ConcurrentHashMap.get(ConcurrentHashMap.java:936)
	at com.ruoyi.framework.websocket.WebSocketUsers.getSessionByUserId(WebSocketUsers.java:148)
	at com.ruoyi.framework.websocket.WebSocketServer.onMessage(WebSocketServer.java:175)
	at jdk.internal.reflect.GeneratedMethodAccessor114.invoke(Unknown Source)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:566)
	at org.apache.tomcat.websocket.pojo.PojoMessageHandlerWholeBase.onMessage(PojoMessageHandlerWholeBase.java:101)
	at org.apache.tomcat.websocket.WsFrameBase.sendMessageText(WsFrameBase.java:390)
	at org.apache.tomcat.websocket.server.WsFrameServer.sendMessageText(WsFrameServer.java:130)
	at org.apache.tomcat.websocket.WsFrameBase.processDataText(WsFrameBase.java:484)
	at org.apache.tomcat.websocket.WsFrameBase.processData(WsFrameBase.java:284)
	at org.apache.tomcat.websocket.WsFrameBase.processInputBuffer(WsFrameBase.java:130)
	at org.apache.tomcat.websocket.server.WsFrameServer.onDataAvailable(WsFrameServer.java:85)
	at org.apache.tomcat.websocket.server.WsFrameServer.doOnDataAvailable(WsFrameServer.java:184)
	at org.apache.tomcat.websocket.server.WsFrameServer.notifyDataAvailable(WsFrameServer.java:164)
	at org.apache.tomcat.websocket.server.WsHttpUpgradeHandler.upgradeDispatch(WsHttpUpgradeHandler.java:152)
	at org.apache.coyote.http11.upgrade.UpgradeProcessorInternal.dispatch(UpgradeProcessorInternal.java:60)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:57)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:928)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1794)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:52)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1191)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.base/java.lang.Thread.run(Thread.java:834)
14:01:35.764 [http-nio-9201-exec-2] INFO  c.r.f.w.WebSocketUsers - [remove,117] - 
 正在移出用户 - 35
14:01:35.764 [http-nio-9201-exec-2] INFO  c.r.f.w.WebSocketUsers - [removeUserSessions,96] - 
 正在移出用户Session映射 - 100
14:01:40.803 [http-nio-9201-exec-1] INFO  c.r.f.w.WebSocketServer - [onOpen,100] - 
 建立连接 - org.apache.tomcat.websocket.WsSession@43e0d13f
14:01:40.803 [http-nio-9201-exec-1] INFO  c.r.f.w.WebSocketServer - [onOpen,101] - 
 当前人数 - 1
14:01:40.812 [http-nio-9201-exec-10] INFO  c.r.f.w.WebSocketServer - [onClose,113] - 
 关闭连接 - org.apache.tomcat.websocket.WsSession@43e0d13f
14:01:40.812 [http-nio-9201-exec-10] INFO  c.r.f.w.WebSocketUsers - [remove,117] - 
 正在移出用户 - 36
14:01:40.812 [http-nio-9201-exec-10] INFO  c.r.f.w.WebSocketUsers - [remove,122] - 
 移出结果 - 成功
14:01:40.813 [http-nio-9201-exec-10] INFO  c.r.f.w.WebSocketServer - [onError,132] - 
 连接异常 - 36
14:01:40.813 [http-nio-9201-exec-10] INFO  c.r.f.w.WebSocketServer - [onError,133] - 
 异常信息 - {}
java.lang.NullPointerException: null
	at java.base/java.util.concurrent.ConcurrentHashMap.get(ConcurrentHashMap.java:936)
	at com.ruoyi.framework.websocket.WebSocketUsers.getSessionByUserId(WebSocketUsers.java:148)
	at com.ruoyi.framework.websocket.WebSocketServer.onMessage(WebSocketServer.java:175)
	at jdk.internal.reflect.GeneratedMethodAccessor114.invoke(Unknown Source)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:566)
	at org.apache.tomcat.websocket.pojo.PojoMessageHandlerWholeBase.onMessage(PojoMessageHandlerWholeBase.java:101)
	at org.apache.tomcat.websocket.WsFrameBase.sendMessageText(WsFrameBase.java:390)
	at org.apache.tomcat.websocket.server.WsFrameServer.sendMessageText(WsFrameServer.java:130)
	at org.apache.tomcat.websocket.WsFrameBase.processDataText(WsFrameBase.java:484)
	at org.apache.tomcat.websocket.WsFrameBase.processData(WsFrameBase.java:284)
	at org.apache.tomcat.websocket.WsFrameBase.processInputBuffer(WsFrameBase.java:130)
	at org.apache.tomcat.websocket.server.WsFrameServer.onDataAvailable(WsFrameServer.java:85)
	at org.apache.tomcat.websocket.server.WsFrameServer.doOnDataAvailable(WsFrameServer.java:184)
	at org.apache.tomcat.websocket.server.WsFrameServer.notifyDataAvailable(WsFrameServer.java:164)
	at org.apache.tomcat.websocket.server.WsHttpUpgradeHandler.upgradeDispatch(WsHttpUpgradeHandler.java:152)
	at org.apache.coyote.http11.upgrade.UpgradeProcessorInternal.dispatch(UpgradeProcessorInternal.java:60)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:57)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:928)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1794)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:52)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1191)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.base/java.lang.Thread.run(Thread.java:834)
14:01:40.814 [http-nio-9201-exec-10] INFO  c.r.f.w.WebSocketUsers - [remove,117] - 
 正在移出用户 - 36
14:01:40.814 [http-nio-9201-exec-10] INFO  c.r.f.w.WebSocketUsers - [removeUserSessions,96] - 
 正在移出用户Session映射 - 100
14:01:45.856 [http-nio-9201-exec-3] INFO  c.r.f.w.WebSocketServer - [onOpen,100] - 
 建立连接 - org.apache.tomcat.websocket.WsSession@7e859c66
14:01:45.856 [http-nio-9201-exec-3] INFO  c.r.f.w.WebSocketServer - [onOpen,101] - 
 当前人数 - 1
14:01:45.861 [http-nio-9201-exec-6] INFO  c.r.f.w.WebSocketServer - [onClose,113] - 
 关闭连接 - org.apache.tomcat.websocket.WsSession@7e859c66
14:01:45.861 [http-nio-9201-exec-6] INFO  c.r.f.w.WebSocketUsers - [remove,117] - 
 正在移出用户 - 37
14:01:45.862 [http-nio-9201-exec-6] INFO  c.r.f.w.WebSocketUsers - [remove,122] - 
 移出结果 - 成功
14:01:45.862 [http-nio-9201-exec-6] INFO  c.r.f.w.WebSocketServer - [onError,132] - 
 连接异常 - 37
14:01:45.863 [http-nio-9201-exec-6] INFO  c.r.f.w.WebSocketServer - [onError,133] - 
 异常信息 - {}
java.lang.NullPointerException: null
	at java.base/java.util.concurrent.ConcurrentHashMap.get(ConcurrentHashMap.java:936)
	at com.ruoyi.framework.websocket.WebSocketUsers.getSessionByUserId(WebSocketUsers.java:148)
	at com.ruoyi.framework.websocket.WebSocketServer.onMessage(WebSocketServer.java:175)
	at jdk.internal.reflect.GeneratedMethodAccessor114.invoke(Unknown Source)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:566)
	at org.apache.tomcat.websocket.pojo.PojoMessageHandlerWholeBase.onMessage(PojoMessageHandlerWholeBase.java:101)
	at org.apache.tomcat.websocket.WsFrameBase.sendMessageText(WsFrameBase.java:390)
	at org.apache.tomcat.websocket.server.WsFrameServer.sendMessageText(WsFrameServer.java:130)
	at org.apache.tomcat.websocket.WsFrameBase.processDataText(WsFrameBase.java:484)
	at org.apache.tomcat.websocket.WsFrameBase.processData(WsFrameBase.java:284)
	at org.apache.tomcat.websocket.WsFrameBase.processInputBuffer(WsFrameBase.java:130)
	at org.apache.tomcat.websocket.server.WsFrameServer.onDataAvailable(WsFrameServer.java:85)
	at org.apache.tomcat.websocket.server.WsFrameServer.doOnDataAvailable(WsFrameServer.java:184)
	at org.apache.tomcat.websocket.server.WsFrameServer.notifyDataAvailable(WsFrameServer.java:164)
	at org.apache.tomcat.websocket.server.WsHttpUpgradeHandler.upgradeDispatch(WsHttpUpgradeHandler.java:152)
	at org.apache.coyote.http11.upgrade.UpgradeProcessorInternal.dispatch(UpgradeProcessorInternal.java:60)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:57)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:928)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1794)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:52)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1191)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.base/java.lang.Thread.run(Thread.java:834)
14:01:45.863 [http-nio-9201-exec-6] INFO  c.r.f.w.WebSocketUsers - [remove,117] - 
 正在移出用户 - 37
14:01:45.863 [http-nio-9201-exec-6] INFO  c.r.f.w.WebSocketUsers - [removeUserSessions,96] - 
 正在移出用户Session映射 - 100
14:01:50.905 [http-nio-9201-exec-4] INFO  c.r.f.w.WebSocketServer - [onOpen,100] - 
 建立连接 - org.apache.tomcat.websocket.WsSession@59055994
14:01:50.906 [http-nio-9201-exec-4] INFO  c.r.f.w.WebSocketServer - [onOpen,101] - 
 当前人数 - 1
14:01:50.910 [http-nio-9201-exec-5] INFO  c.r.f.w.WebSocketServer - [onClose,113] - 
 关闭连接 - org.apache.tomcat.websocket.WsSession@59055994
14:01:50.910 [http-nio-9201-exec-5] INFO  c.r.f.w.WebSocketUsers - [remove,117] - 
 正在移出用户 - 38
14:01:50.910 [http-nio-9201-exec-5] INFO  c.r.f.w.WebSocketUsers - [remove,122] - 
 移出结果 - 成功
14:01:50.910 [http-nio-9201-exec-5] INFO  c.r.f.w.WebSocketServer - [onError,132] - 
 连接异常 - 38
14:01:50.911 [http-nio-9201-exec-5] INFO  c.r.f.w.WebSocketServer - [onError,133] - 
 异常信息 - {}
java.lang.NullPointerException: null
	at java.base/java.util.concurrent.ConcurrentHashMap.get(ConcurrentHashMap.java:936)
	at com.ruoyi.framework.websocket.WebSocketUsers.getSessionByUserId(WebSocketUsers.java:148)
	at com.ruoyi.framework.websocket.WebSocketServer.onMessage(WebSocketServer.java:175)
	at jdk.internal.reflect.GeneratedMethodAccessor114.invoke(Unknown Source)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:566)
	at org.apache.tomcat.websocket.pojo.PojoMessageHandlerWholeBase.onMessage(PojoMessageHandlerWholeBase.java:101)
	at org.apache.tomcat.websocket.WsFrameBase.sendMessageText(WsFrameBase.java:390)
	at org.apache.tomcat.websocket.server.WsFrameServer.sendMessageText(WsFrameServer.java:130)
	at org.apache.tomcat.websocket.WsFrameBase.processDataText(WsFrameBase.java:484)
	at org.apache.tomcat.websocket.WsFrameBase.processData(WsFrameBase.java:284)
	at org.apache.tomcat.websocket.WsFrameBase.processInputBuffer(WsFrameBase.java:130)
	at org.apache.tomcat.websocket.server.WsFrameServer.onDataAvailable(WsFrameServer.java:85)
	at org.apache.tomcat.websocket.server.WsFrameServer.doOnDataAvailable(WsFrameServer.java:184)
	at org.apache.tomcat.websocket.server.WsFrameServer.notifyDataAvailable(WsFrameServer.java:164)
	at org.apache.tomcat.websocket.server.WsHttpUpgradeHandler.upgradeDispatch(WsHttpUpgradeHandler.java:152)
	at org.apache.coyote.http11.upgrade.UpgradeProcessorInternal.dispatch(UpgradeProcessorInternal.java:60)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:57)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:928)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1794)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:52)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1191)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.base/java.lang.Thread.run(Thread.java:834)
14:01:50.912 [http-nio-9201-exec-5] INFO  c.r.f.w.WebSocketUsers - [remove,117] - 
 正在移出用户 - 38
14:01:50.912 [http-nio-9201-exec-5] INFO  c.r.f.w.WebSocketUsers - [removeUserSessions,96] - 
 正在移出用户Session映射 - 100
14:01:55.952 [http-nio-9201-exec-8] INFO  c.r.f.w.WebSocketServer - [onOpen,100] - 
 建立连接 - org.apache.tomcat.websocket.WsSession@28a9f1eb
14:01:55.952 [http-nio-9201-exec-8] INFO  c.r.f.w.WebSocketServer - [onOpen,101] - 
 当前人数 - 1
14:01:55.957 [http-nio-9201-exec-7] INFO  c.r.f.w.WebSocketServer - [onClose,113] - 
 关闭连接 - org.apache.tomcat.websocket.WsSession@28a9f1eb
14:01:55.958 [http-nio-9201-exec-7] INFO  c.r.f.w.WebSocketUsers - [remove,117] - 
 正在移出用户 - 39
14:01:55.958 [http-nio-9201-exec-7] INFO  c.r.f.w.WebSocketUsers - [remove,122] - 
 移出结果 - 成功
14:01:55.958 [http-nio-9201-exec-7] INFO  c.r.f.w.WebSocketServer - [onError,132] - 
 连接异常 - 39
14:01:55.958 [http-nio-9201-exec-7] INFO  c.r.f.w.WebSocketServer - [onError,133] - 
 异常信息 - {}
java.lang.NullPointerException: null
	at java.base/java.util.concurrent.ConcurrentHashMap.get(ConcurrentHashMap.java:936)
	at com.ruoyi.framework.websocket.WebSocketUsers.getSessionByUserId(WebSocketUsers.java:148)
	at com.ruoyi.framework.websocket.WebSocketServer.onMessage(WebSocketServer.java:175)
	at jdk.internal.reflect.GeneratedMethodAccessor114.invoke(Unknown Source)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:566)
	at org.apache.tomcat.websocket.pojo.PojoMessageHandlerWholeBase.onMessage(PojoMessageHandlerWholeBase.java:101)
	at org.apache.tomcat.websocket.WsFrameBase.sendMessageText(WsFrameBase.java:390)
	at org.apache.tomcat.websocket.server.WsFrameServer.sendMessageText(WsFrameServer.java:130)
	at org.apache.tomcat.websocket.WsFrameBase.processDataText(WsFrameBase.java:484)
	at org.apache.tomcat.websocket.WsFrameBase.processData(WsFrameBase.java:284)
	at org.apache.tomcat.websocket.WsFrameBase.processInputBuffer(WsFrameBase.java:130)
	at org.apache.tomcat.websocket.server.WsFrameServer.onDataAvailable(WsFrameServer.java:85)
	at org.apache.tomcat.websocket.server.WsFrameServer.doOnDataAvailable(WsFrameServer.java:184)
	at org.apache.tomcat.websocket.server.WsFrameServer.notifyDataAvailable(WsFrameServer.java:164)
	at org.apache.tomcat.websocket.server.WsHttpUpgradeHandler.upgradeDispatch(WsHttpUpgradeHandler.java:152)
	at org.apache.coyote.http11.upgrade.UpgradeProcessorInternal.dispatch(UpgradeProcessorInternal.java:60)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:57)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:928)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1794)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:52)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1191)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.base/java.lang.Thread.run(Thread.java:834)
14:01:55.959 [http-nio-9201-exec-7] INFO  c.r.f.w.WebSocketUsers - [remove,117] - 
 正在移出用户 - 39
14:01:55.959 [http-nio-9201-exec-7] INFO  c.r.f.w.WebSocketUsers - [removeUserSessions,96] - 
 正在移出用户Session映射 - 100
14:02:00.983 [http-nio-9201-exec-9] INFO  c.r.f.w.WebSocketServer - [onOpen,100] - 
 建立连接 - org.apache.tomcat.websocket.WsSession@3c51d3aa
14:02:00.983 [http-nio-9201-exec-9] INFO  c.r.f.w.WebSocketServer - [onOpen,101] - 
 当前人数 - 1
14:02:00.988 [http-nio-9201-exec-2] INFO  c.r.f.w.WebSocketServer - [onClose,113] - 
 关闭连接 - org.apache.tomcat.websocket.WsSession@3c51d3aa
14:02:00.988 [http-nio-9201-exec-2] INFO  c.r.f.w.WebSocketUsers - [remove,117] - 
 正在移出用户 - 3a
14:02:00.988 [http-nio-9201-exec-2] INFO  c.r.f.w.WebSocketUsers - [remove,122] - 
 移出结果 - 成功
14:02:00.988 [http-nio-9201-exec-2] INFO  c.r.f.w.WebSocketServer - [onError,132] - 
 连接异常 - 3a
14:02:00.988 [http-nio-9201-exec-2] INFO  c.r.f.w.WebSocketServer - [onError,133] - 
 异常信息 - {}
java.lang.NullPointerException: null
	at java.base/java.util.concurrent.ConcurrentHashMap.get(ConcurrentHashMap.java:936)
	at com.ruoyi.framework.websocket.WebSocketUsers.getSessionByUserId(WebSocketUsers.java:148)
	at com.ruoyi.framework.websocket.WebSocketServer.onMessage(WebSocketServer.java:175)
	at jdk.internal.reflect.GeneratedMethodAccessor114.invoke(Unknown Source)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:566)
	at org.apache.tomcat.websocket.pojo.PojoMessageHandlerWholeBase.onMessage(PojoMessageHandlerWholeBase.java:101)
	at org.apache.tomcat.websocket.WsFrameBase.sendMessageText(WsFrameBase.java:390)
	at org.apache.tomcat.websocket.server.WsFrameServer.sendMessageText(WsFrameServer.java:130)
	at org.apache.tomcat.websocket.WsFrameBase.processDataText(WsFrameBase.java:484)
	at org.apache.tomcat.websocket.WsFrameBase.processData(WsFrameBase.java:284)
	at org.apache.tomcat.websocket.WsFrameBase.processInputBuffer(WsFrameBase.java:130)
	at org.apache.tomcat.websocket.server.WsFrameServer.onDataAvailable(WsFrameServer.java:85)
	at org.apache.tomcat.websocket.server.WsFrameServer.doOnDataAvailable(WsFrameServer.java:184)
	at org.apache.tomcat.websocket.server.WsFrameServer.notifyDataAvailable(WsFrameServer.java:164)
	at org.apache.tomcat.websocket.server.WsHttpUpgradeHandler.upgradeDispatch(WsHttpUpgradeHandler.java:152)
	at org.apache.coyote.http11.upgrade.UpgradeProcessorInternal.dispatch(UpgradeProcessorInternal.java:60)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:57)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:928)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1794)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:52)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1191)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.base/java.lang.Thread.run(Thread.java:834)
14:02:00.989 [http-nio-9201-exec-2] INFO  c.r.f.w.WebSocketUsers - [remove,117] - 
 正在移出用户 - 3a
14:02:00.989 [http-nio-9201-exec-2] INFO  c.r.f.w.WebSocketUsers - [removeUserSessions,96] - 
 正在移出用户Session映射 - 100
14:02:06.031 [http-nio-9201-exec-1] INFO  c.r.f.w.WebSocketServer - [onOpen,100] - 
 建立连接 - org.apache.tomcat.websocket.WsSession@54186396
14:02:06.031 [http-nio-9201-exec-1] INFO  c.r.f.w.WebSocketServer - [onOpen,101] - 
 当前人数 - 1
14:02:06.036 [http-nio-9201-exec-10] INFO  c.r.f.w.WebSocketServer - [onClose,113] - 
 关闭连接 - org.apache.tomcat.websocket.WsSession@54186396
14:02:06.037 [http-nio-9201-exec-10] INFO  c.r.f.w.WebSocketUsers - [remove,117] - 
 正在移出用户 - 3b
14:02:06.037 [http-nio-9201-exec-10] INFO  c.r.f.w.WebSocketUsers - [remove,122] - 
 移出结果 - 成功
14:02:06.038 [http-nio-9201-exec-10] INFO  c.r.f.w.WebSocketServer - [onError,132] - 
 连接异常 - 3b
14:02:06.038 [http-nio-9201-exec-10] INFO  c.r.f.w.WebSocketServer - [onError,133] - 
 异常信息 - {}
java.lang.NullPointerException: null
	at java.base/java.util.concurrent.ConcurrentHashMap.get(ConcurrentHashMap.java:936)
	at com.ruoyi.framework.websocket.WebSocketUsers.getSessionByUserId(WebSocketUsers.java:148)
	at com.ruoyi.framework.websocket.WebSocketServer.onMessage(WebSocketServer.java:175)
	at jdk.internal.reflect.GeneratedMethodAccessor114.invoke(Unknown Source)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:566)
	at org.apache.tomcat.websocket.pojo.PojoMessageHandlerWholeBase.onMessage(PojoMessageHandlerWholeBase.java:101)
	at org.apache.tomcat.websocket.WsFrameBase.sendMessageText(WsFrameBase.java:390)
	at org.apache.tomcat.websocket.server.WsFrameServer.sendMessageText(WsFrameServer.java:130)
	at org.apache.tomcat.websocket.WsFrameBase.processDataText(WsFrameBase.java:484)
	at org.apache.tomcat.websocket.WsFrameBase.processData(WsFrameBase.java:284)
	at org.apache.tomcat.websocket.WsFrameBase.processInputBuffer(WsFrameBase.java:130)
	at org.apache.tomcat.websocket.server.WsFrameServer.onDataAvailable(WsFrameServer.java:85)
	at org.apache.tomcat.websocket.server.WsFrameServer.doOnDataAvailable(WsFrameServer.java:184)
	at org.apache.tomcat.websocket.server.WsFrameServer.notifyDataAvailable(WsFrameServer.java:164)
	at org.apache.tomcat.websocket.server.WsHttpUpgradeHandler.upgradeDispatch(WsHttpUpgradeHandler.java:152)
	at org.apache.coyote.http11.upgrade.UpgradeProcessorInternal.dispatch(UpgradeProcessorInternal.java:60)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:57)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:928)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1794)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:52)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1191)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.base/java.lang.Thread.run(Thread.java:834)
14:02:06.039 [http-nio-9201-exec-10] INFO  c.r.f.w.WebSocketUsers - [remove,117] - 
 正在移出用户 - 3b
14:02:06.039 [http-nio-9201-exec-10] INFO  c.r.f.w.WebSocketUsers - [removeUserSessions,96] - 
 正在移出用户Session映射 - 100
14:02:11.070 [http-nio-9201-exec-3] INFO  c.r.f.w.WebSocketServer - [onOpen,100] - 
 建立连接 - org.apache.tomcat.websocket.WsSession@33cb1c85
14:02:11.070 [http-nio-9201-exec-3] INFO  c.r.f.w.WebSocketServer - [onOpen,101] - 
 当前人数 - 1
14:02:11.074 [http-nio-9201-exec-6] INFO  c.r.f.w.WebSocketServer - [onClose,113] - 
 关闭连接 - org.apache.tomcat.websocket.WsSession@33cb1c85
14:02:11.074 [http-nio-9201-exec-6] INFO  c.r.f.w.WebSocketUsers - [remove,117] - 
 正在移出用户 - 3c
14:02:11.074 [http-nio-9201-exec-6] INFO  c.r.f.w.WebSocketUsers - [remove,122] - 
 移出结果 - 成功
14:02:11.074 [http-nio-9201-exec-6] INFO  c.r.f.w.WebSocketServer - [onError,132] - 
 连接异常 - 3c
14:02:11.075 [http-nio-9201-exec-6] INFO  c.r.f.w.WebSocketServer - [onError,133] - 
 异常信息 - {}
java.lang.NullPointerException: null
	at java.base/java.util.concurrent.ConcurrentHashMap.get(ConcurrentHashMap.java:936)
	at com.ruoyi.framework.websocket.WebSocketUsers.getSessionByUserId(WebSocketUsers.java:148)
	at com.ruoyi.framework.websocket.WebSocketServer.onMessage(WebSocketServer.java:175)
	at jdk.internal.reflect.GeneratedMethodAccessor114.invoke(Unknown Source)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:566)
	at org.apache.tomcat.websocket.pojo.PojoMessageHandlerWholeBase.onMessage(PojoMessageHandlerWholeBase.java:101)
	at org.apache.tomcat.websocket.WsFrameBase.sendMessageText(WsFrameBase.java:390)
	at org.apache.tomcat.websocket.server.WsFrameServer.sendMessageText(WsFrameServer.java:130)
	at org.apache.tomcat.websocket.WsFrameBase.processDataText(WsFrameBase.java:484)
	at org.apache.tomcat.websocket.WsFrameBase.processData(WsFrameBase.java:284)
	at org.apache.tomcat.websocket.WsFrameBase.processInputBuffer(WsFrameBase.java:130)
	at org.apache.tomcat.websocket.server.WsFrameServer.onDataAvailable(WsFrameServer.java:85)
	at org.apache.tomcat.websocket.server.WsFrameServer.doOnDataAvailable(WsFrameServer.java:184)
	at org.apache.tomcat.websocket.server.WsFrameServer.notifyDataAvailable(WsFrameServer.java:164)
	at org.apache.tomcat.websocket.server.WsHttpUpgradeHandler.upgradeDispatch(WsHttpUpgradeHandler.java:152)
	at org.apache.coyote.http11.upgrade.UpgradeProcessorInternal.dispatch(UpgradeProcessorInternal.java:60)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:57)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:928)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1794)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:52)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1191)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.base/java.lang.Thread.run(Thread.java:834)
14:02:11.075 [http-nio-9201-exec-6] INFO  c.r.f.w.WebSocketUsers - [remove,117] - 
 正在移出用户 - 3c
14:02:11.075 [http-nio-9201-exec-6] INFO  c.r.f.w.WebSocketUsers - [removeUserSessions,96] - 
 正在移出用户Session映射 - 100
14:02:16.106 [http-nio-9201-exec-4] INFO  c.r.f.w.WebSocketServer - [onOpen,100] - 
 建立连接 - org.apache.tomcat.websocket.WsSession@3a944766
14:02:16.106 [http-nio-9201-exec-4] INFO  c.r.f.w.WebSocketServer - [onOpen,101] - 
 当前人数 - 1
14:02:16.110 [http-nio-9201-exec-5] INFO  c.r.f.w.WebSocketServer - [onClose,113] - 
 关闭连接 - org.apache.tomcat.websocket.WsSession@3a944766
14:02:16.110 [http-nio-9201-exec-5] INFO  c.r.f.w.WebSocketUsers - [remove,117] - 
 正在移出用户 - 3d
14:02:16.111 [http-nio-9201-exec-5] INFO  c.r.f.w.WebSocketUsers - [remove,122] - 
 移出结果 - 成功
14:02:16.111 [http-nio-9201-exec-5] INFO  c.r.f.w.WebSocketServer - [onError,132] - 
 连接异常 - 3d
14:02:16.112 [http-nio-9201-exec-5] INFO  c.r.f.w.WebSocketServer - [onError,133] - 
 异常信息 - {}
java.lang.NullPointerException: null
	at java.base/java.util.concurrent.ConcurrentHashMap.get(ConcurrentHashMap.java:936)
	at com.ruoyi.framework.websocket.WebSocketUsers.getSessionByUserId(WebSocketUsers.java:148)
	at com.ruoyi.framework.websocket.WebSocketServer.onMessage(WebSocketServer.java:175)
	at jdk.internal.reflect.GeneratedMethodAccessor114.invoke(Unknown Source)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:566)
	at org.apache.tomcat.websocket.pojo.PojoMessageHandlerWholeBase.onMessage(PojoMessageHandlerWholeBase.java:101)
	at org.apache.tomcat.websocket.WsFrameBase.sendMessageText(WsFrameBase.java:390)
	at org.apache.tomcat.websocket.server.WsFrameServer.sendMessageText(WsFrameServer.java:130)
	at org.apache.tomcat.websocket.WsFrameBase.processDataText(WsFrameBase.java:484)
	at org.apache.tomcat.websocket.WsFrameBase.processData(WsFrameBase.java:284)
	at org.apache.tomcat.websocket.WsFrameBase.processInputBuffer(WsFrameBase.java:130)
	at org.apache.tomcat.websocket.server.WsFrameServer.onDataAvailable(WsFrameServer.java:85)
	at org.apache.tomcat.websocket.server.WsFrameServer.doOnDataAvailable(WsFrameServer.java:184)
	at org.apache.tomcat.websocket.server.WsFrameServer.notifyDataAvailable(WsFrameServer.java:164)
	at org.apache.tomcat.websocket.server.WsHttpUpgradeHandler.upgradeDispatch(WsHttpUpgradeHandler.java:152)
	at org.apache.coyote.http11.upgrade.UpgradeProcessorInternal.dispatch(UpgradeProcessorInternal.java:60)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:57)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:928)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1794)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:52)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1191)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.base/java.lang.Thread.run(Thread.java:834)
14:02:16.113 [http-nio-9201-exec-5] INFO  c.r.f.w.WebSocketUsers - [remove,117] - 
 正在移出用户 - 3d
14:02:16.113 [http-nio-9201-exec-5] INFO  c.r.f.w.WebSocketUsers - [removeUserSessions,96] - 
 正在移出用户Session映射 - 100
14:02:21.148 [http-nio-9201-exec-8] INFO  c.r.f.w.WebSocketServer - [onOpen,100] - 
 建立连接 - org.apache.tomcat.websocket.WsSession@75856e45
14:02:21.149 [http-nio-9201-exec-8] INFO  c.r.f.w.WebSocketServer - [onOpen,101] - 
 当前人数 - 1
14:02:21.154 [http-nio-9201-exec-7] INFO  c.r.f.w.WebSocketServer - [onClose,113] - 
 关闭连接 - org.apache.tomcat.websocket.WsSession@75856e45
14:02:21.154 [http-nio-9201-exec-7] INFO  c.r.f.w.WebSocketUsers - [remove,117] - 
 正在移出用户 - 3e
14:02:21.154 [http-nio-9201-exec-7] INFO  c.r.f.w.WebSocketUsers - [remove,122] - 
 移出结果 - 成功
14:02:21.155 [http-nio-9201-exec-7] INFO  c.r.f.w.WebSocketServer - [onError,132] - 
 连接异常 - 3e
14:02:21.155 [http-nio-9201-exec-7] INFO  c.r.f.w.WebSocketServer - [onError,133] - 
 异常信息 - {}
java.lang.NullPointerException: null
	at java.base/java.util.concurrent.ConcurrentHashMap.get(ConcurrentHashMap.java:936)
	at com.ruoyi.framework.websocket.WebSocketUsers.getSessionByUserId(WebSocketUsers.java:148)
	at com.ruoyi.framework.websocket.WebSocketServer.onMessage(WebSocketServer.java:175)
	at jdk.internal.reflect.GeneratedMethodAccessor114.invoke(Unknown Source)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:566)
	at org.apache.tomcat.websocket.pojo.PojoMessageHandlerWholeBase.onMessage(PojoMessageHandlerWholeBase.java:101)
	at org.apache.tomcat.websocket.WsFrameBase.sendMessageText(WsFrameBase.java:390)
	at org.apache.tomcat.websocket.server.WsFrameServer.sendMessageText(WsFrameServer.java:130)
	at org.apache.tomcat.websocket.WsFrameBase.processDataText(WsFrameBase.java:484)
	at org.apache.tomcat.websocket.WsFrameBase.processData(WsFrameBase.java:284)
	at org.apache.tomcat.websocket.WsFrameBase.processInputBuffer(WsFrameBase.java:130)
	at org.apache.tomcat.websocket.server.WsFrameServer.onDataAvailable(WsFrameServer.java:85)
	at org.apache.tomcat.websocket.server.WsFrameServer.doOnDataAvailable(WsFrameServer.java:184)
	at org.apache.tomcat.websocket.server.WsFrameServer.notifyDataAvailable(WsFrameServer.java:164)
	at org.apache.tomcat.websocket.server.WsHttpUpgradeHandler.upgradeDispatch(WsHttpUpgradeHandler.java:152)
	at org.apache.coyote.http11.upgrade.UpgradeProcessorInternal.dispatch(UpgradeProcessorInternal.java:60)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:57)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:928)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1794)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:52)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1191)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.base/java.lang.Thread.run(Thread.java:834)
14:02:21.156 [http-nio-9201-exec-7] INFO  c.r.f.w.WebSocketUsers - [remove,117] - 
 正在移出用户 - 3e
14:02:21.156 [http-nio-9201-exec-7] INFO  c.r.f.w.WebSocketUsers - [removeUserSessions,96] - 
 正在移出用户Session映射 - 100
14:02:26.191 [http-nio-9201-exec-9] INFO  c.r.f.w.WebSocketServer - [onOpen,100] - 
 建立连接 - org.apache.tomcat.websocket.WsSession@2604554c
14:02:26.191 [http-nio-9201-exec-9] INFO  c.r.f.w.WebSocketServer - [onOpen,101] - 
 当前人数 - 1
14:02:26.197 [http-nio-9201-exec-2] INFO  c.r.f.w.WebSocketServer - [onClose,113] - 
 关闭连接 - org.apache.tomcat.websocket.WsSession@2604554c
14:02:26.197 [http-nio-9201-exec-2] INFO  c.r.f.w.WebSocketUsers - [remove,117] - 
 正在移出用户 - 3f
14:02:26.197 [http-nio-9201-exec-2] INFO  c.r.f.w.WebSocketUsers - [remove,122] - 
 移出结果 - 成功
14:02:26.197 [http-nio-9201-exec-2] INFO  c.r.f.w.WebSocketServer - [onError,132] - 
 连接异常 - 3f
14:02:26.198 [http-nio-9201-exec-2] INFO  c.r.f.w.WebSocketServer - [onError,133] - 
 异常信息 - {}
java.lang.NullPointerException: null
	at java.base/java.util.concurrent.ConcurrentHashMap.get(ConcurrentHashMap.java:936)
	at com.ruoyi.framework.websocket.WebSocketUsers.getSessionByUserId(WebSocketUsers.java:148)
	at com.ruoyi.framework.websocket.WebSocketServer.onMessage(WebSocketServer.java:175)
	at jdk.internal.reflect.GeneratedMethodAccessor114.invoke(Unknown Source)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:566)
	at org.apache.tomcat.websocket.pojo.PojoMessageHandlerWholeBase.onMessage(PojoMessageHandlerWholeBase.java:101)
	at org.apache.tomcat.websocket.WsFrameBase.sendMessageText(WsFrameBase.java:390)
	at org.apache.tomcat.websocket.server.WsFrameServer.sendMessageText(WsFrameServer.java:130)
	at org.apache.tomcat.websocket.WsFrameBase.processDataText(WsFrameBase.java:484)
	at org.apache.tomcat.websocket.WsFrameBase.processData(WsFrameBase.java:284)
	at org.apache.tomcat.websocket.WsFrameBase.processInputBuffer(WsFrameBase.java:130)
	at org.apache.tomcat.websocket.server.WsFrameServer.onDataAvailable(WsFrameServer.java:85)
	at org.apache.tomcat.websocket.server.WsFrameServer.doOnDataAvailable(WsFrameServer.java:184)
	at org.apache.tomcat.websocket.server.WsFrameServer.notifyDataAvailable(WsFrameServer.java:164)
	at org.apache.tomcat.websocket.server.WsHttpUpgradeHandler.upgradeDispatch(WsHttpUpgradeHandler.java:152)
	at org.apache.coyote.http11.upgrade.UpgradeProcessorInternal.dispatch(UpgradeProcessorInternal.java:60)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:57)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:928)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1794)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:52)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1191)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.base/java.lang.Thread.run(Thread.java:834)
14:02:26.198 [http-nio-9201-exec-2] INFO  c.r.f.w.WebSocketUsers - [remove,117] - 
 正在移出用户 - 3f
14:02:26.199 [http-nio-9201-exec-2] INFO  c.r.f.w.WebSocketUsers - [removeUserSessions,96] - 
 正在移出用户Session映射 - 100
14:02:31.243 [http-nio-9201-exec-1] INFO  c.r.f.w.WebSocketServer - [onOpen,100] - 
 建立连接 - org.apache.tomcat.websocket.WsSession@97265a
14:02:31.244 [http-nio-9201-exec-1] INFO  c.r.f.w.WebSocketServer - [onOpen,101] - 
 当前人数 - 1
14:02:31.248 [http-nio-9201-exec-10] INFO  c.r.f.w.WebSocketServer - [onClose,113] - 
 关闭连接 - org.apache.tomcat.websocket.WsSession@97265a
14:02:31.248 [http-nio-9201-exec-10] INFO  c.r.f.w.WebSocketUsers - [remove,117] - 
 正在移出用户 - 40
14:02:31.248 [http-nio-9201-exec-10] INFO  c.r.f.w.WebSocketUsers - [remove,122] - 
 移出结果 - 成功
14:02:31.249 [http-nio-9201-exec-10] INFO  c.r.f.w.WebSocketServer - [onError,132] - 
 连接异常 - 40
14:02:31.250 [http-nio-9201-exec-10] INFO  c.r.f.w.WebSocketServer - [onError,133] - 
 异常信息 - {}
java.lang.NullPointerException: null
	at java.base/java.util.concurrent.ConcurrentHashMap.get(ConcurrentHashMap.java:936)
	at com.ruoyi.framework.websocket.WebSocketUsers.getSessionByUserId(WebSocketUsers.java:148)
	at com.ruoyi.framework.websocket.WebSocketServer.onMessage(WebSocketServer.java:175)
	at jdk.internal.reflect.GeneratedMethodAccessor114.invoke(Unknown Source)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:566)
	at org.apache.tomcat.websocket.pojo.PojoMessageHandlerWholeBase.onMessage(PojoMessageHandlerWholeBase.java:101)
	at org.apache.tomcat.websocket.WsFrameBase.sendMessageText(WsFrameBase.java:390)
	at org.apache.tomcat.websocket.server.WsFrameServer.sendMessageText(WsFrameServer.java:130)
	at org.apache.tomcat.websocket.WsFrameBase.processDataText(WsFrameBase.java:484)
	at org.apache.tomcat.websocket.WsFrameBase.processData(WsFrameBase.java:284)
	at org.apache.tomcat.websocket.WsFrameBase.processInputBuffer(WsFrameBase.java:130)
	at org.apache.tomcat.websocket.server.WsFrameServer.onDataAvailable(WsFrameServer.java:85)
	at org.apache.tomcat.websocket.server.WsFrameServer.doOnDataAvailable(WsFrameServer.java:184)
	at org.apache.tomcat.websocket.server.WsFrameServer.notifyDataAvailable(WsFrameServer.java:164)
	at org.apache.tomcat.websocket.server.WsHttpUpgradeHandler.upgradeDispatch(WsHttpUpgradeHandler.java:152)
	at org.apache.coyote.http11.upgrade.UpgradeProcessorInternal.dispatch(UpgradeProcessorInternal.java:60)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:57)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:928)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1794)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:52)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1191)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.base/java.lang.Thread.run(Thread.java:834)
14:02:31.250 [http-nio-9201-exec-10] INFO  c.r.f.w.WebSocketUsers - [remove,117] - 
 正在移出用户 - 40
14:02:31.250 [http-nio-9201-exec-10] INFO  c.r.f.w.WebSocketUsers - [removeUserSessions,96] - 
 正在移出用户Session映射 - 100
14:02:36.289 [http-nio-9201-exec-3] INFO  c.r.f.w.WebSocketServer - [onOpen,100] - 
 建立连接 - org.apache.tomcat.websocket.WsSession@68c93668
14:02:36.289 [http-nio-9201-exec-3] INFO  c.r.f.w.WebSocketServer - [onOpen,101] - 
 当前人数 - 1
14:02:36.294 [http-nio-9201-exec-6] INFO  c.r.f.w.WebSocketServer - [onClose,113] - 
 关闭连接 - org.apache.tomcat.websocket.WsSession@68c93668
14:02:36.294 [http-nio-9201-exec-6] INFO  c.r.f.w.WebSocketUsers - [remove,117] - 
 正在移出用户 - 41
14:02:36.294 [http-nio-9201-exec-6] INFO  c.r.f.w.WebSocketUsers - [remove,122] - 
 移出结果 - 成功
14:02:36.295 [http-nio-9201-exec-6] INFO  c.r.f.w.WebSocketServer - [onError,132] - 
 连接异常 - 41
14:02:36.297 [http-nio-9201-exec-6] INFO  c.r.f.w.WebSocketServer - [onError,133] - 
 异常信息 - {}
java.lang.NullPointerException: null
	at java.base/java.util.concurrent.ConcurrentHashMap.get(ConcurrentHashMap.java:936)
	at com.ruoyi.framework.websocket.WebSocketUsers.getSessionByUserId(WebSocketUsers.java:148)
	at com.ruoyi.framework.websocket.WebSocketServer.onMessage(WebSocketServer.java:175)
	at jdk.internal.reflect.GeneratedMethodAccessor114.invoke(Unknown Source)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:566)
	at org.apache.tomcat.websocket.pojo.PojoMessageHandlerWholeBase.onMessage(PojoMessageHandlerWholeBase.java:101)
	at org.apache.tomcat.websocket.WsFrameBase.sendMessageText(WsFrameBase.java:390)
	at org.apache.tomcat.websocket.server.WsFrameServer.sendMessageText(WsFrameServer.java:130)
	at org.apache.tomcat.websocket.WsFrameBase.processDataText(WsFrameBase.java:484)
	at org.apache.tomcat.websocket.WsFrameBase.processData(WsFrameBase.java:284)
	at org.apache.tomcat.websocket.WsFrameBase.processInputBuffer(WsFrameBase.java:130)
	at org.apache.tomcat.websocket.server.WsFrameServer.onDataAvailable(WsFrameServer.java:85)
	at org.apache.tomcat.websocket.server.WsFrameServer.doOnDataAvailable(WsFrameServer.java:184)
	at org.apache.tomcat.websocket.server.WsFrameServer.notifyDataAvailable(WsFrameServer.java:164)
	at org.apache.tomcat.websocket.server.WsHttpUpgradeHandler.upgradeDispatch(WsHttpUpgradeHandler.java:152)
	at org.apache.coyote.http11.upgrade.UpgradeProcessorInternal.dispatch(UpgradeProcessorInternal.java:60)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:57)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:928)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1794)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:52)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1191)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.base/java.lang.Thread.run(Thread.java:834)
14:02:36.298 [http-nio-9201-exec-6] INFO  c.r.f.w.WebSocketUsers - [remove,117] - 
 正在移出用户 - 41
14:02:36.298 [http-nio-9201-exec-6] INFO  c.r.f.w.WebSocketUsers - [removeUserSessions,96] - 
 正在移出用户Session映射 - 100
14:02:42.825 [http-nio-9201-exec-4] INFO  c.r.f.w.WebSocketServer - [onOpen,100] - 
 建立连接 - org.apache.tomcat.websocket.WsSession@4f8daf3a
14:02:42.825 [http-nio-9201-exec-4] INFO  c.r.f.w.WebSocketServer - [onOpen,101] - 
 当前人数 - 1
14:02:42.863 [http-nio-9201-exec-5] INFO  c.r.f.w.WebSocketServer - [onClose,113] - 
 关闭连接 - org.apache.tomcat.websocket.WsSession@4f8daf3a
14:02:42.864 [http-nio-9201-exec-5] INFO  c.r.f.w.WebSocketUsers - [remove,117] - 
 正在移出用户 - 42
14:02:42.864 [http-nio-9201-exec-5] INFO  c.r.f.w.WebSocketUsers - [remove,122] - 
 移出结果 - 成功
14:02:42.864 [http-nio-9201-exec-5] INFO  c.r.f.w.WebSocketServer - [onError,132] - 
 连接异常 - 42
14:02:42.865 [http-nio-9201-exec-5] INFO  c.r.f.w.WebSocketServer - [onError,133] - 
 异常信息 - {}
java.lang.NullPointerException: null
	at java.base/java.util.concurrent.ConcurrentHashMap.get(ConcurrentHashMap.java:936)
	at com.ruoyi.framework.websocket.WebSocketUsers.getSessionByUserId(WebSocketUsers.java:148)
	at com.ruoyi.framework.websocket.WebSocketServer.onMessage(WebSocketServer.java:175)
	at jdk.internal.reflect.GeneratedMethodAccessor114.invoke(Unknown Source)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:566)
	at org.apache.tomcat.websocket.pojo.PojoMessageHandlerWholeBase.onMessage(PojoMessageHandlerWholeBase.java:101)
	at org.apache.tomcat.websocket.WsFrameBase.sendMessageText(WsFrameBase.java:390)
	at org.apache.tomcat.websocket.server.WsFrameServer.sendMessageText(WsFrameServer.java:130)
	at org.apache.tomcat.websocket.WsFrameBase.processDataText(WsFrameBase.java:484)
	at org.apache.tomcat.websocket.WsFrameBase.processData(WsFrameBase.java:284)
	at org.apache.tomcat.websocket.WsFrameBase.processInputBuffer(WsFrameBase.java:130)
	at org.apache.tomcat.websocket.server.WsFrameServer.onDataAvailable(WsFrameServer.java:85)
	at org.apache.tomcat.websocket.server.WsFrameServer.doOnDataAvailable(WsFrameServer.java:184)
	at org.apache.tomcat.websocket.server.WsFrameServer.notifyDataAvailable(WsFrameServer.java:164)
	at org.apache.tomcat.websocket.server.WsHttpUpgradeHandler.upgradeDispatch(WsHttpUpgradeHandler.java:152)
	at org.apache.coyote.http11.upgrade.UpgradeProcessorInternal.dispatch(UpgradeProcessorInternal.java:60)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:57)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:928)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1794)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:52)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1191)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.base/java.lang.Thread.run(Thread.java:834)
14:02:42.865 [http-nio-9201-exec-5] INFO  c.r.f.w.WebSocketUsers - [remove,117] - 
 正在移出用户 - 42
14:02:42.865 [http-nio-9201-exec-5] INFO  c.r.f.w.WebSocketUsers - [removeUserSessions,96] - 
 正在移出用户Session映射 - 100
14:02:47.902 [http-nio-9201-exec-8] INFO  c.r.f.w.WebSocketServer - [onOpen,100] - 
 建立连接 - org.apache.tomcat.websocket.WsSession@29bc1655
14:02:47.903 [http-nio-9201-exec-8] INFO  c.r.f.w.WebSocketServer - [onOpen,101] - 
 当前人数 - 1
14:02:47.905 [http-nio-9201-exec-7] INFO  c.r.f.w.WebSocketServer - [onClose,113] - 
 关闭连接 - org.apache.tomcat.websocket.WsSession@29bc1655
14:02:47.905 [http-nio-9201-exec-7] INFO  c.r.f.w.WebSocketUsers - [remove,117] - 
 正在移出用户 - 43
14:02:47.905 [http-nio-9201-exec-7] INFO  c.r.f.w.WebSocketUsers - [remove,122] - 
 移出结果 - 成功
14:02:47.905 [http-nio-9201-exec-7] INFO  c.r.f.w.WebSocketServer - [onError,132] - 
 连接异常 - 43
14:02:47.906 [http-nio-9201-exec-7] INFO  c.r.f.w.WebSocketServer - [onError,133] - 
 异常信息 - {}
java.lang.NullPointerException: null
	at java.base/java.util.concurrent.ConcurrentHashMap.get(ConcurrentHashMap.java:936)
	at com.ruoyi.framework.websocket.WebSocketUsers.getSessionByUserId(WebSocketUsers.java:148)
	at com.ruoyi.framework.websocket.WebSocketServer.onMessage(WebSocketServer.java:175)
	at jdk.internal.reflect.GeneratedMethodAccessor114.invoke(Unknown Source)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:566)
	at org.apache.tomcat.websocket.pojo.PojoMessageHandlerWholeBase.onMessage(PojoMessageHandlerWholeBase.java:101)
	at org.apache.tomcat.websocket.WsFrameBase.sendMessageText(WsFrameBase.java:390)
	at org.apache.tomcat.websocket.server.WsFrameServer.sendMessageText(WsFrameServer.java:130)
	at org.apache.tomcat.websocket.WsFrameBase.processDataText(WsFrameBase.java:484)
	at org.apache.tomcat.websocket.WsFrameBase.processData(WsFrameBase.java:284)
	at org.apache.tomcat.websocket.WsFrameBase.processInputBuffer(WsFrameBase.java:130)
	at org.apache.tomcat.websocket.server.WsFrameServer.onDataAvailable(WsFrameServer.java:85)
	at org.apache.tomcat.websocket.server.WsFrameServer.doOnDataAvailable(WsFrameServer.java:184)
	at org.apache.tomcat.websocket.server.WsFrameServer.notifyDataAvailable(WsFrameServer.java:164)
	at org.apache.tomcat.websocket.server.WsHttpUpgradeHandler.upgradeDispatch(WsHttpUpgradeHandler.java:152)
	at org.apache.coyote.http11.upgrade.UpgradeProcessorInternal.dispatch(UpgradeProcessorInternal.java:60)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:57)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:928)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1794)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:52)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1191)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.base/java.lang.Thread.run(Thread.java:834)
14:02:47.907 [http-nio-9201-exec-7] INFO  c.r.f.w.WebSocketUsers - [remove,117] - 
 正在移出用户 - 43
14:02:47.907 [http-nio-9201-exec-7] INFO  c.r.f.w.WebSocketUsers - [removeUserSessions,96] - 
 正在移出用户Session映射 - 100
14:02:52.934 [http-nio-9201-exec-9] INFO  c.r.f.w.WebSocketServer - [onOpen,100] - 
 建立连接 - org.apache.tomcat.websocket.WsSession@6f428c79
14:02:52.934 [http-nio-9201-exec-9] INFO  c.r.f.w.WebSocketServer - [onOpen,101] - 
 当前人数 - 1
14:02:52.937 [http-nio-9201-exec-2] INFO  c.r.f.w.WebSocketServer - [onClose,113] - 
 关闭连接 - org.apache.tomcat.websocket.WsSession@6f428c79
14:02:52.937 [http-nio-9201-exec-2] INFO  c.r.f.w.WebSocketUsers - [remove,117] - 
 正在移出用户 - 44
14:02:52.937 [http-nio-9201-exec-2] INFO  c.r.f.w.WebSocketUsers - [remove,122] - 
 移出结果 - 成功
14:02:52.937 [http-nio-9201-exec-2] INFO  c.r.f.w.WebSocketServer - [onError,132] - 
 连接异常 - 44
14:02:52.937 [http-nio-9201-exec-2] INFO  c.r.f.w.WebSocketServer - [onError,133] - 
 异常信息 - {}
java.lang.NullPointerException: null
	at java.base/java.util.concurrent.ConcurrentHashMap.get(ConcurrentHashMap.java:936)
	at com.ruoyi.framework.websocket.WebSocketUsers.getSessionByUserId(WebSocketUsers.java:148)
	at com.ruoyi.framework.websocket.WebSocketServer.onMessage(WebSocketServer.java:175)
	at jdk.internal.reflect.GeneratedMethodAccessor114.invoke(Unknown Source)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:566)
	at org.apache.tomcat.websocket.pojo.PojoMessageHandlerWholeBase.onMessage(PojoMessageHandlerWholeBase.java:101)
	at org.apache.tomcat.websocket.WsFrameBase.sendMessageText(WsFrameBase.java:390)
	at org.apache.tomcat.websocket.server.WsFrameServer.sendMessageText(WsFrameServer.java:130)
	at org.apache.tomcat.websocket.WsFrameBase.processDataText(WsFrameBase.java:484)
	at org.apache.tomcat.websocket.WsFrameBase.processData(WsFrameBase.java:284)
	at org.apache.tomcat.websocket.WsFrameBase.processInputBuffer(WsFrameBase.java:130)
	at org.apache.tomcat.websocket.server.WsFrameServer.onDataAvailable(WsFrameServer.java:85)
	at org.apache.tomcat.websocket.server.WsFrameServer.doOnDataAvailable(WsFrameServer.java:184)
	at org.apache.tomcat.websocket.server.WsFrameServer.notifyDataAvailable(WsFrameServer.java:164)
	at org.apache.tomcat.websocket.server.WsHttpUpgradeHandler.upgradeDispatch(WsHttpUpgradeHandler.java:152)
	at org.apache.coyote.http11.upgrade.UpgradeProcessorInternal.dispatch(UpgradeProcessorInternal.java:60)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:57)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:928)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1794)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:52)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1191)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.base/java.lang.Thread.run(Thread.java:834)
14:02:52.938 [http-nio-9201-exec-2] INFO  c.r.f.w.WebSocketUsers - [remove,117] - 
 正在移出用户 - 44
14:02:52.938 [http-nio-9201-exec-2] INFO  c.r.f.w.WebSocketUsers - [removeUserSessions,96] - 
 正在移出用户Session映射 - 100
14:02:57.958 [http-nio-9201-exec-1] INFO  c.r.f.w.WebSocketServer - [onOpen,100] - 
 建立连接 - org.apache.tomcat.websocket.WsSession@4774f5c9
14:02:57.958 [http-nio-9201-exec-1] INFO  c.r.f.w.WebSocketServer - [onOpen,101] - 
 当前人数 - 1
14:02:57.961 [http-nio-9201-exec-10] INFO  c.r.f.w.WebSocketServer - [onClose,113] - 
 关闭连接 - org.apache.tomcat.websocket.WsSession@4774f5c9
14:02:57.962 [http-nio-9201-exec-10] INFO  c.r.f.w.WebSocketUsers - [remove,117] - 
 正在移出用户 - 45
14:02:57.962 [http-nio-9201-exec-10] INFO  c.r.f.w.WebSocketUsers - [remove,122] - 
 移出结果 - 成功
14:02:57.962 [http-nio-9201-exec-10] INFO  c.r.f.w.WebSocketServer - [onError,132] - 
 连接异常 - 45
14:02:57.963 [http-nio-9201-exec-10] INFO  c.r.f.w.WebSocketServer - [onError,133] - 
 异常信息 - {}
java.lang.NullPointerException: null
	at java.base/java.util.concurrent.ConcurrentHashMap.get(ConcurrentHashMap.java:936)
	at com.ruoyi.framework.websocket.WebSocketUsers.getSessionByUserId(WebSocketUsers.java:148)
	at com.ruoyi.framework.websocket.WebSocketServer.onMessage(WebSocketServer.java:175)
	at jdk.internal.reflect.GeneratedMethodAccessor114.invoke(Unknown Source)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:566)
	at org.apache.tomcat.websocket.pojo.PojoMessageHandlerWholeBase.onMessage(PojoMessageHandlerWholeBase.java:101)
	at org.apache.tomcat.websocket.WsFrameBase.sendMessageText(WsFrameBase.java:390)
	at org.apache.tomcat.websocket.server.WsFrameServer.sendMessageText(WsFrameServer.java:130)
	at org.apache.tomcat.websocket.WsFrameBase.processDataText(WsFrameBase.java:484)
	at org.apache.tomcat.websocket.WsFrameBase.processData(WsFrameBase.java:284)
	at org.apache.tomcat.websocket.WsFrameBase.processInputBuffer(WsFrameBase.java:130)
	at org.apache.tomcat.websocket.server.WsFrameServer.onDataAvailable(WsFrameServer.java:85)
	at org.apache.tomcat.websocket.server.WsFrameServer.doOnDataAvailable(WsFrameServer.java:184)
	at org.apache.tomcat.websocket.server.WsFrameServer.notifyDataAvailable(WsFrameServer.java:164)
	at org.apache.tomcat.websocket.server.WsHttpUpgradeHandler.upgradeDispatch(WsHttpUpgradeHandler.java:152)
	at org.apache.coyote.http11.upgrade.UpgradeProcessorInternal.dispatch(UpgradeProcessorInternal.java:60)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:57)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:928)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1794)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:52)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1191)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.base/java.lang.Thread.run(Thread.java:834)
14:02:57.964 [http-nio-9201-exec-10] INFO  c.r.f.w.WebSocketUsers - [remove,117] - 
 正在移出用户 - 45
14:02:57.964 [http-nio-9201-exec-10] INFO  c.r.f.w.WebSocketUsers - [removeUserSessions,96] - 
 正在移出用户Session映射 - 100
14:03:02.980 [http-nio-9201-exec-3] INFO  c.r.f.w.WebSocketServer - [onOpen,100] - 
 建立连接 - org.apache.tomcat.websocket.WsSession@ea005dc
14:03:02.980 [http-nio-9201-exec-3] INFO  c.r.f.w.WebSocketServer - [onOpen,101] - 
 当前人数 - 1
14:03:02.983 [http-nio-9201-exec-6] INFO  c.r.f.w.WebSocketServer - [onClose,113] - 
 关闭连接 - org.apache.tomcat.websocket.WsSession@ea005dc
14:03:02.983 [http-nio-9201-exec-6] INFO  c.r.f.w.WebSocketUsers - [remove,117] - 
 正在移出用户 - 46
14:03:02.983 [http-nio-9201-exec-6] INFO  c.r.f.w.WebSocketUsers - [remove,122] - 
 移出结果 - 成功
14:03:02.984 [http-nio-9201-exec-6] INFO  c.r.f.w.WebSocketServer - [onError,132] - 
 连接异常 - 46
14:03:02.985 [http-nio-9201-exec-6] INFO  c.r.f.w.WebSocketServer - [onError,133] - 
 异常信息 - {}
java.lang.NullPointerException: null
	at java.base/java.util.concurrent.ConcurrentHashMap.get(ConcurrentHashMap.java:936)
	at com.ruoyi.framework.websocket.WebSocketUsers.getSessionByUserId(WebSocketUsers.java:148)
	at com.ruoyi.framework.websocket.WebSocketServer.onMessage(WebSocketServer.java:175)
	at jdk.internal.reflect.GeneratedMethodAccessor114.invoke(Unknown Source)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:566)
	at org.apache.tomcat.websocket.pojo.PojoMessageHandlerWholeBase.onMessage(PojoMessageHandlerWholeBase.java:101)
	at org.apache.tomcat.websocket.WsFrameBase.sendMessageText(WsFrameBase.java:390)
	at org.apache.tomcat.websocket.server.WsFrameServer.sendMessageText(WsFrameServer.java:130)
	at org.apache.tomcat.websocket.WsFrameBase.processDataText(WsFrameBase.java:484)
	at org.apache.tomcat.websocket.WsFrameBase.processData(WsFrameBase.java:284)
	at org.apache.tomcat.websocket.WsFrameBase.processInputBuffer(WsFrameBase.java:130)
	at org.apache.tomcat.websocket.server.WsFrameServer.onDataAvailable(WsFrameServer.java:85)
	at org.apache.tomcat.websocket.server.WsFrameServer.doOnDataAvailable(WsFrameServer.java:184)
	at org.apache.tomcat.websocket.server.WsFrameServer.notifyDataAvailable(WsFrameServer.java:164)
	at org.apache.tomcat.websocket.server.WsHttpUpgradeHandler.upgradeDispatch(WsHttpUpgradeHandler.java:152)
	at org.apache.coyote.http11.upgrade.UpgradeProcessorInternal.dispatch(UpgradeProcessorInternal.java:60)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:57)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:928)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1794)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:52)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1191)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.base/java.lang.Thread.run(Thread.java:834)
14:03:02.985 [http-nio-9201-exec-6] INFO  c.r.f.w.WebSocketUsers - [remove,117] - 
 正在移出用户 - 46
14:03:02.986 [http-nio-9201-exec-6] INFO  c.r.f.w.WebSocketUsers - [removeUserSessions,96] - 
 正在移出用户Session映射 - 100
14:03:08.010 [http-nio-9201-exec-4] INFO  c.r.f.w.WebSocketServer - [onOpen,100] - 
 建立连接 - org.apache.tomcat.websocket.WsSession@5196d5ac
14:03:08.011 [http-nio-9201-exec-4] INFO  c.r.f.w.WebSocketServer - [onOpen,101] - 
 当前人数 - 1
14:03:08.013 [http-nio-9201-exec-5] INFO  c.r.f.w.WebSocketServer - [onClose,113] - 
 关闭连接 - org.apache.tomcat.websocket.WsSession@5196d5ac
14:03:08.013 [http-nio-9201-exec-5] INFO  c.r.f.w.WebSocketUsers - [remove,117] - 
 正在移出用户 - 47
14:03:08.014 [http-nio-9201-exec-5] INFO  c.r.f.w.WebSocketUsers - [remove,122] - 
 移出结果 - 成功
14:03:08.014 [http-nio-9201-exec-5] INFO  c.r.f.w.WebSocketServer - [onError,132] - 
 连接异常 - 47
14:03:08.015 [http-nio-9201-exec-5] INFO  c.r.f.w.WebSocketServer - [onError,133] - 
 异常信息 - {}
java.lang.NullPointerException: null
	at java.base/java.util.concurrent.ConcurrentHashMap.get(ConcurrentHashMap.java:936)
	at com.ruoyi.framework.websocket.WebSocketUsers.getSessionByUserId(WebSocketUsers.java:148)
	at com.ruoyi.framework.websocket.WebSocketServer.onMessage(WebSocketServer.java:175)
	at jdk.internal.reflect.GeneratedMethodAccessor114.invoke(Unknown Source)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:566)
	at org.apache.tomcat.websocket.pojo.PojoMessageHandlerWholeBase.onMessage(PojoMessageHandlerWholeBase.java:101)
	at org.apache.tomcat.websocket.WsFrameBase.sendMessageText(WsFrameBase.java:390)
	at org.apache.tomcat.websocket.server.WsFrameServer.sendMessageText(WsFrameServer.java:130)
	at org.apache.tomcat.websocket.WsFrameBase.processDataText(WsFrameBase.java:484)
	at org.apache.tomcat.websocket.WsFrameBase.processData(WsFrameBase.java:284)
	at org.apache.tomcat.websocket.WsFrameBase.processInputBuffer(WsFrameBase.java:130)
	at org.apache.tomcat.websocket.server.WsFrameServer.onDataAvailable(WsFrameServer.java:85)
	at org.apache.tomcat.websocket.server.WsFrameServer.doOnDataAvailable(WsFrameServer.java:184)
	at org.apache.tomcat.websocket.server.WsFrameServer.notifyDataAvailable(WsFrameServer.java:164)
	at org.apache.tomcat.websocket.server.WsHttpUpgradeHandler.upgradeDispatch(WsHttpUpgradeHandler.java:152)
	at org.apache.coyote.http11.upgrade.UpgradeProcessorInternal.dispatch(UpgradeProcessorInternal.java:60)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:57)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:928)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1794)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:52)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1191)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.base/java.lang.Thread.run(Thread.java:834)
14:03:08.015 [http-nio-9201-exec-5] INFO  c.r.f.w.WebSocketUsers - [remove,117] - 
 正在移出用户 - 47
14:03:08.015 [http-nio-9201-exec-5] INFO  c.r.f.w.WebSocketUsers - [removeUserSessions,96] - 
 正在移出用户Session映射 - 100
14:03:13.035 [http-nio-9201-exec-8] INFO  c.r.f.w.WebSocketServer - [onOpen,100] - 
 建立连接 - org.apache.tomcat.websocket.WsSession@4be94d1b
14:03:13.035 [http-nio-9201-exec-8] INFO  c.r.f.w.WebSocketServer - [onOpen,101] - 
 当前人数 - 1
14:03:13.037 [http-nio-9201-exec-7] INFO  c.r.f.w.WebSocketServer - [onClose,113] - 
 关闭连接 - org.apache.tomcat.websocket.WsSession@4be94d1b
14:03:13.037 [http-nio-9201-exec-7] INFO  c.r.f.w.WebSocketUsers - [remove,117] - 
 正在移出用户 - 48
14:03:13.038 [http-nio-9201-exec-7] INFO  c.r.f.w.WebSocketUsers - [remove,122] - 
 移出结果 - 成功
14:03:13.038 [http-nio-9201-exec-7] INFO  c.r.f.w.WebSocketServer - [onError,132] - 
 连接异常 - 48
14:03:13.038 [http-nio-9201-exec-7] INFO  c.r.f.w.WebSocketServer - [onError,133] - 
 异常信息 - {}
java.lang.NullPointerException: null
	at java.base/java.util.concurrent.ConcurrentHashMap.get(ConcurrentHashMap.java:936)
	at com.ruoyi.framework.websocket.WebSocketUsers.getSessionByUserId(WebSocketUsers.java:148)
	at com.ruoyi.framework.websocket.WebSocketServer.onMessage(WebSocketServer.java:175)
	at jdk.internal.reflect.GeneratedMethodAccessor114.invoke(Unknown Source)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:566)
	at org.apache.tomcat.websocket.pojo.PojoMessageHandlerWholeBase.onMessage(PojoMessageHandlerWholeBase.java:101)
	at org.apache.tomcat.websocket.WsFrameBase.sendMessageText(WsFrameBase.java:390)
	at org.apache.tomcat.websocket.server.WsFrameServer.sendMessageText(WsFrameServer.java:130)
	at org.apache.tomcat.websocket.WsFrameBase.processDataText(WsFrameBase.java:484)
	at org.apache.tomcat.websocket.WsFrameBase.processData(WsFrameBase.java:284)
	at org.apache.tomcat.websocket.WsFrameBase.processInputBuffer(WsFrameBase.java:130)
	at org.apache.tomcat.websocket.server.WsFrameServer.onDataAvailable(WsFrameServer.java:85)
	at org.apache.tomcat.websocket.server.WsFrameServer.doOnDataAvailable(WsFrameServer.java:184)
	at org.apache.tomcat.websocket.server.WsFrameServer.notifyDataAvailable(WsFrameServer.java:164)
	at org.apache.tomcat.websocket.server.WsHttpUpgradeHandler.upgradeDispatch(WsHttpUpgradeHandler.java:152)
	at org.apache.coyote.http11.upgrade.UpgradeProcessorInternal.dispatch(UpgradeProcessorInternal.java:60)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:57)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:928)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1794)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:52)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1191)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.base/java.lang.Thread.run(Thread.java:834)
14:03:13.039 [http-nio-9201-exec-7] INFO  c.r.f.w.WebSocketUsers - [remove,117] - 
 正在移出用户 - 48
14:03:13.039 [http-nio-9201-exec-7] INFO  c.r.f.w.WebSocketUsers - [removeUserSessions,96] - 
 正在移出用户Session映射 - 100
14:03:18.065 [http-nio-9201-exec-9] INFO  c.r.f.w.WebSocketServer - [onOpen,100] - 
 建立连接 - org.apache.tomcat.websocket.WsSession@5c76d137
14:03:18.065 [http-nio-9201-exec-9] INFO  c.r.f.w.WebSocketServer - [onOpen,101] - 
 当前人数 - 1
14:03:18.068 [http-nio-9201-exec-2] INFO  c.r.f.w.WebSocketServer - [onClose,113] - 
 关闭连接 - org.apache.tomcat.websocket.WsSession@5c76d137
14:03:18.069 [http-nio-9201-exec-2] INFO  c.r.f.w.WebSocketUsers - [remove,117] - 
 正在移出用户 - 49
14:03:18.069 [http-nio-9201-exec-2] INFO  c.r.f.w.WebSocketUsers - [remove,122] - 
 移出结果 - 成功
14:03:18.069 [http-nio-9201-exec-2] INFO  c.r.f.w.WebSocketServer - [onError,132] - 
 连接异常 - 49
14:03:18.070 [http-nio-9201-exec-2] INFO  c.r.f.w.WebSocketServer - [onError,133] - 
 异常信息 - {}
java.lang.NullPointerException: null
	at java.base/java.util.concurrent.ConcurrentHashMap.get(ConcurrentHashMap.java:936)
	at com.ruoyi.framework.websocket.WebSocketUsers.getSessionByUserId(WebSocketUsers.java:148)
	at com.ruoyi.framework.websocket.WebSocketServer.onMessage(WebSocketServer.java:175)
	at jdk.internal.reflect.GeneratedMethodAccessor114.invoke(Unknown Source)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:566)
	at org.apache.tomcat.websocket.pojo.PojoMessageHandlerWholeBase.onMessage(PojoMessageHandlerWholeBase.java:101)
	at org.apache.tomcat.websocket.WsFrameBase.sendMessageText(WsFrameBase.java:390)
	at org.apache.tomcat.websocket.server.WsFrameServer.sendMessageText(WsFrameServer.java:130)
	at org.apache.tomcat.websocket.WsFrameBase.processDataText(WsFrameBase.java:484)
	at org.apache.tomcat.websocket.WsFrameBase.processData(WsFrameBase.java:284)
	at org.apache.tomcat.websocket.WsFrameBase.processInputBuffer(WsFrameBase.java:130)
	at org.apache.tomcat.websocket.server.WsFrameServer.onDataAvailable(WsFrameServer.java:85)
	at org.apache.tomcat.websocket.server.WsFrameServer.doOnDataAvailable(WsFrameServer.java:184)
	at org.apache.tomcat.websocket.server.WsFrameServer.notifyDataAvailable(WsFrameServer.java:164)
	at org.apache.tomcat.websocket.server.WsHttpUpgradeHandler.upgradeDispatch(WsHttpUpgradeHandler.java:152)
	at org.apache.coyote.http11.upgrade.UpgradeProcessorInternal.dispatch(UpgradeProcessorInternal.java:60)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:57)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:928)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1794)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:52)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1191)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.base/java.lang.Thread.run(Thread.java:834)
14:03:18.070 [http-nio-9201-exec-2] INFO  c.r.f.w.WebSocketUsers - [remove,117] - 
 正在移出用户 - 49
14:03:18.070 [http-nio-9201-exec-2] INFO  c.r.f.w.WebSocketUsers - [removeUserSessions,96] - 
 正在移出用户Session映射 - 100
14:03:23.104 [http-nio-9201-exec-1] INFO  c.r.f.w.WebSocketServer - [onOpen,100] - 
 建立连接 - org.apache.tomcat.websocket.WsSession@6793ddbf
14:03:23.104 [http-nio-9201-exec-1] INFO  c.r.f.w.WebSocketServer - [onOpen,101] - 
 当前人数 - 1
14:03:23.108 [http-nio-9201-exec-10] INFO  c.r.f.w.WebSocketServer - [onClose,113] - 
 关闭连接 - org.apache.tomcat.websocket.WsSession@6793ddbf
14:03:23.108 [http-nio-9201-exec-10] INFO  c.r.f.w.WebSocketUsers - [remove,117] - 
 正在移出用户 - 4a
14:03:23.108 [http-nio-9201-exec-10] INFO  c.r.f.w.WebSocketUsers - [remove,122] - 
 移出结果 - 成功
14:03:23.108 [http-nio-9201-exec-10] INFO  c.r.f.w.WebSocketServer - [onError,132] - 
 连接异常 - 4a
14:03:23.112 [http-nio-9201-exec-10] INFO  c.r.f.w.WebSocketServer - [onError,133] - 
 异常信息 - {}
java.lang.NullPointerException: null
	at java.base/java.util.concurrent.ConcurrentHashMap.get(ConcurrentHashMap.java:936)
	at com.ruoyi.framework.websocket.WebSocketUsers.getSessionByUserId(WebSocketUsers.java:148)
	at com.ruoyi.framework.websocket.WebSocketServer.onMessage(WebSocketServer.java:175)
	at jdk.internal.reflect.GeneratedMethodAccessor114.invoke(Unknown Source)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:566)
	at org.apache.tomcat.websocket.pojo.PojoMessageHandlerWholeBase.onMessage(PojoMessageHandlerWholeBase.java:101)
	at org.apache.tomcat.websocket.WsFrameBase.sendMessageText(WsFrameBase.java:390)
	at org.apache.tomcat.websocket.server.WsFrameServer.sendMessageText(WsFrameServer.java:130)
	at org.apache.tomcat.websocket.WsFrameBase.processDataText(WsFrameBase.java:484)
	at org.apache.tomcat.websocket.WsFrameBase.processData(WsFrameBase.java:284)
	at org.apache.tomcat.websocket.WsFrameBase.processInputBuffer(WsFrameBase.java:130)
	at org.apache.tomcat.websocket.server.WsFrameServer.onDataAvailable(WsFrameServer.java:85)
	at org.apache.tomcat.websocket.server.WsFrameServer.doOnDataAvailable(WsFrameServer.java:184)
	at org.apache.tomcat.websocket.server.WsFrameServer.notifyDataAvailable(WsFrameServer.java:164)
	at org.apache.tomcat.websocket.server.WsHttpUpgradeHandler.upgradeDispatch(WsHttpUpgradeHandler.java:152)
	at org.apache.coyote.http11.upgrade.UpgradeProcessorInternal.dispatch(UpgradeProcessorInternal.java:60)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:57)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:928)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1794)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:52)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1191)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.base/java.lang.Thread.run(Thread.java:834)
14:03:23.112 [http-nio-9201-exec-10] INFO  c.r.f.w.WebSocketUsers - [remove,117] - 
 正在移出用户 - 4a
14:03:23.112 [http-nio-9201-exec-10] INFO  c.r.f.w.WebSocketUsers - [removeUserSessions,96] - 
 正在移出用户Session映射 - 100
14:03:28.137 [http-nio-9201-exec-3] INFO  c.r.f.w.WebSocketServer - [onOpen,100] - 
 建立连接 - org.apache.tomcat.websocket.WsSession@7b789a7f
14:03:28.138 [http-nio-9201-exec-3] INFO  c.r.f.w.WebSocketServer - [onOpen,101] - 
 当前人数 - 1
14:03:28.141 [http-nio-9201-exec-6] INFO  c.r.f.w.WebSocketServer - [onClose,113] - 
 关闭连接 - org.apache.tomcat.websocket.WsSession@7b789a7f
14:03:28.142 [http-nio-9201-exec-6] INFO  c.r.f.w.WebSocketUsers - [remove,117] - 
 正在移出用户 - 4b
14:03:28.142 [http-nio-9201-exec-6] INFO  c.r.f.w.WebSocketUsers - [remove,122] - 
 移出结果 - 成功
14:03:28.142 [http-nio-9201-exec-6] INFO  c.r.f.w.WebSocketServer - [onError,132] - 
 连接异常 - 4b
14:03:28.142 [http-nio-9201-exec-6] INFO  c.r.f.w.WebSocketServer - [onError,133] - 
 异常信息 - {}
java.lang.NullPointerException: null
	at java.base/java.util.concurrent.ConcurrentHashMap.get(ConcurrentHashMap.java:936)
	at com.ruoyi.framework.websocket.WebSocketUsers.getSessionByUserId(WebSocketUsers.java:148)
	at com.ruoyi.framework.websocket.WebSocketServer.onMessage(WebSocketServer.java:175)
	at jdk.internal.reflect.GeneratedMethodAccessor114.invoke(Unknown Source)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:566)
	at org.apache.tomcat.websocket.pojo.PojoMessageHandlerWholeBase.onMessage(PojoMessageHandlerWholeBase.java:101)
	at org.apache.tomcat.websocket.WsFrameBase.sendMessageText(WsFrameBase.java:390)
	at org.apache.tomcat.websocket.server.WsFrameServer.sendMessageText(WsFrameServer.java:130)
	at org.apache.tomcat.websocket.WsFrameBase.processDataText(WsFrameBase.java:484)
	at org.apache.tomcat.websocket.WsFrameBase.processData(WsFrameBase.java:284)
	at org.apache.tomcat.websocket.WsFrameBase.processInputBuffer(WsFrameBase.java:130)
	at org.apache.tomcat.websocket.server.WsFrameServer.onDataAvailable(WsFrameServer.java:85)
	at org.apache.tomcat.websocket.server.WsFrameServer.doOnDataAvailable(WsFrameServer.java:184)
	at org.apache.tomcat.websocket.server.WsFrameServer.notifyDataAvailable(WsFrameServer.java:164)
	at org.apache.tomcat.websocket.server.WsHttpUpgradeHandler.upgradeDispatch(WsHttpUpgradeHandler.java:152)
	at org.apache.coyote.http11.upgrade.UpgradeProcessorInternal.dispatch(UpgradeProcessorInternal.java:60)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:57)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:928)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1794)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:52)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1191)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.base/java.lang.Thread.run(Thread.java:834)
14:03:28.142 [http-nio-9201-exec-6] INFO  c.r.f.w.WebSocketUsers - [remove,117] - 
 正在移出用户 - 4b
14:03:28.142 [http-nio-9201-exec-6] INFO  c.r.f.w.WebSocketUsers - [removeUserSessions,96] - 
 正在移出用户Session映射 - 100
14:03:33.172 [http-nio-9201-exec-4] INFO  c.r.f.w.WebSocketServer - [onOpen,100] - 
 建立连接 - org.apache.tomcat.websocket.WsSession@6ee5889f
14:03:33.172 [http-nio-9201-exec-4] INFO  c.r.f.w.WebSocketServer - [onOpen,101] - 
 当前人数 - 1
14:03:33.177 [http-nio-9201-exec-5] INFO  c.r.f.w.WebSocketServer - [onClose,113] - 
 关闭连接 - org.apache.tomcat.websocket.WsSession@6ee5889f
14:03:33.177 [http-nio-9201-exec-5] INFO  c.r.f.w.WebSocketUsers - [remove,117] - 
 正在移出用户 - 4c
14:03:33.177 [http-nio-9201-exec-5] INFO  c.r.f.w.WebSocketUsers - [remove,122] - 
 移出结果 - 成功
14:03:33.177 [http-nio-9201-exec-5] INFO  c.r.f.w.WebSocketServer - [onError,132] - 
 连接异常 - 4c
14:03:33.177 [http-nio-9201-exec-5] INFO  c.r.f.w.WebSocketServer - [onError,133] - 
 异常信息 - {}
java.lang.NullPointerException: null
	at java.base/java.util.concurrent.ConcurrentHashMap.get(ConcurrentHashMap.java:936)
	at com.ruoyi.framework.websocket.WebSocketUsers.getSessionByUserId(WebSocketUsers.java:148)
	at com.ruoyi.framework.websocket.WebSocketServer.onMessage(WebSocketServer.java:175)
	at jdk.internal.reflect.GeneratedMethodAccessor114.invoke(Unknown Source)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:566)
	at org.apache.tomcat.websocket.pojo.PojoMessageHandlerWholeBase.onMessage(PojoMessageHandlerWholeBase.java:101)
	at org.apache.tomcat.websocket.WsFrameBase.sendMessageText(WsFrameBase.java:390)
	at org.apache.tomcat.websocket.server.WsFrameServer.sendMessageText(WsFrameServer.java:130)
	at org.apache.tomcat.websocket.WsFrameBase.processDataText(WsFrameBase.java:484)
	at org.apache.tomcat.websocket.WsFrameBase.processData(WsFrameBase.java:284)
	at org.apache.tomcat.websocket.WsFrameBase.processInputBuffer(WsFrameBase.java:130)
	at org.apache.tomcat.websocket.server.WsFrameServer.onDataAvailable(WsFrameServer.java:85)
	at org.apache.tomcat.websocket.server.WsFrameServer.doOnDataAvailable(WsFrameServer.java:184)
	at org.apache.tomcat.websocket.server.WsFrameServer.notifyDataAvailable(WsFrameServer.java:164)
	at org.apache.tomcat.websocket.server.WsHttpUpgradeHandler.upgradeDispatch(WsHttpUpgradeHandler.java:152)
	at org.apache.coyote.http11.upgrade.UpgradeProcessorInternal.dispatch(UpgradeProcessorInternal.java:60)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:57)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:928)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1794)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:52)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1191)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.base/java.lang.Thread.run(Thread.java:834)
14:03:33.178 [http-nio-9201-exec-5] INFO  c.r.f.w.WebSocketUsers - [remove,117] - 
 正在移出用户 - 4c
14:03:33.178 [http-nio-9201-exec-5] INFO  c.r.f.w.WebSocketUsers - [removeUserSessions,96] - 
 正在移出用户Session映射 - 100
14:03:38.206 [http-nio-9201-exec-8] INFO  c.r.f.w.WebSocketServer - [onOpen,100] - 
 建立连接 - org.apache.tomcat.websocket.WsSession@310029d9
14:03:38.207 [http-nio-9201-exec-8] INFO  c.r.f.w.WebSocketServer - [onOpen,101] - 
 当前人数 - 1
14:03:38.211 [http-nio-9201-exec-7] INFO  c.r.f.w.WebSocketServer - [onClose,113] - 
 关闭连接 - org.apache.tomcat.websocket.WsSession@310029d9
14:03:38.211 [http-nio-9201-exec-7] INFO  c.r.f.w.WebSocketUsers - [remove,117] - 
 正在移出用户 - 4d
14:03:38.211 [http-nio-9201-exec-7] INFO  c.r.f.w.WebSocketUsers - [remove,122] - 
 移出结果 - 成功
14:03:38.211 [http-nio-9201-exec-7] INFO  c.r.f.w.WebSocketServer - [onError,132] - 
 连接异常 - 4d
14:03:38.212 [http-nio-9201-exec-7] INFO  c.r.f.w.WebSocketServer - [onError,133] - 
 异常信息 - {}
java.lang.NullPointerException: null
	at java.base/java.util.concurrent.ConcurrentHashMap.get(ConcurrentHashMap.java:936)
	at com.ruoyi.framework.websocket.WebSocketUsers.getSessionByUserId(WebSocketUsers.java:148)
	at com.ruoyi.framework.websocket.WebSocketServer.onMessage(WebSocketServer.java:175)
	at jdk.internal.reflect.GeneratedMethodAccessor114.invoke(Unknown Source)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:566)
	at org.apache.tomcat.websocket.pojo.PojoMessageHandlerWholeBase.onMessage(PojoMessageHandlerWholeBase.java:101)
	at org.apache.tomcat.websocket.WsFrameBase.sendMessageText(WsFrameBase.java:390)
	at org.apache.tomcat.websocket.server.WsFrameServer.sendMessageText(WsFrameServer.java:130)
	at org.apache.tomcat.websocket.WsFrameBase.processDataText(WsFrameBase.java:484)
	at org.apache.tomcat.websocket.WsFrameBase.processData(WsFrameBase.java:284)
	at org.apache.tomcat.websocket.WsFrameBase.processInputBuffer(WsFrameBase.java:130)
	at org.apache.tomcat.websocket.server.WsFrameServer.onDataAvailable(WsFrameServer.java:85)
	at org.apache.tomcat.websocket.server.WsFrameServer.doOnDataAvailable(WsFrameServer.java:184)
	at org.apache.tomcat.websocket.server.WsFrameServer.notifyDataAvailable(WsFrameServer.java:164)
	at org.apache.tomcat.websocket.server.WsHttpUpgradeHandler.upgradeDispatch(WsHttpUpgradeHandler.java:152)
	at org.apache.coyote.http11.upgrade.UpgradeProcessorInternal.dispatch(UpgradeProcessorInternal.java:60)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:57)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:928)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1794)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:52)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1191)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.base/java.lang.Thread.run(Thread.java:834)
14:03:38.213 [http-nio-9201-exec-7] INFO  c.r.f.w.WebSocketUsers - [remove,117] - 
 正在移出用户 - 4d
14:03:38.213 [http-nio-9201-exec-7] INFO  c.r.f.w.WebSocketUsers - [removeUserSessions,96] - 
 正在移出用户Session映射 - 100
14:03:43.241 [http-nio-9201-exec-9] INFO  c.r.f.w.WebSocketServer - [onOpen,100] - 
 建立连接 - org.apache.tomcat.websocket.WsSession@7e32e895
14:03:43.242 [http-nio-9201-exec-9] INFO  c.r.f.w.WebSocketServer - [onOpen,101] - 
 当前人数 - 1
14:03:43.245 [http-nio-9201-exec-2] INFO  c.r.f.w.WebSocketServer - [onClose,113] - 
 关闭连接 - org.apache.tomcat.websocket.WsSession@7e32e895
14:03:43.245 [http-nio-9201-exec-2] INFO  c.r.f.w.WebSocketUsers - [remove,117] - 
 正在移出用户 - 4e
14:03:43.245 [http-nio-9201-exec-2] INFO  c.r.f.w.WebSocketUsers - [remove,122] - 
 移出结果 - 成功
14:03:43.245 [http-nio-9201-exec-2] INFO  c.r.f.w.WebSocketServer - [onError,132] - 
 连接异常 - 4e
14:03:43.246 [http-nio-9201-exec-2] INFO  c.r.f.w.WebSocketServer - [onError,133] - 
 异常信息 - {}
java.lang.NullPointerException: null
	at java.base/java.util.concurrent.ConcurrentHashMap.get(ConcurrentHashMap.java:936)
	at com.ruoyi.framework.websocket.WebSocketUsers.getSessionByUserId(WebSocketUsers.java:148)
	at com.ruoyi.framework.websocket.WebSocketServer.onMessage(WebSocketServer.java:175)
	at jdk.internal.reflect.GeneratedMethodAccessor114.invoke(Unknown Source)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:566)
	at org.apache.tomcat.websocket.pojo.PojoMessageHandlerWholeBase.onMessage(PojoMessageHandlerWholeBase.java:101)
	at org.apache.tomcat.websocket.WsFrameBase.sendMessageText(WsFrameBase.java:390)
	at org.apache.tomcat.websocket.server.WsFrameServer.sendMessageText(WsFrameServer.java:130)
	at org.apache.tomcat.websocket.WsFrameBase.processDataText(WsFrameBase.java:484)
	at org.apache.tomcat.websocket.WsFrameBase.processData(WsFrameBase.java:284)
	at org.apache.tomcat.websocket.WsFrameBase.processInputBuffer(WsFrameBase.java:130)
	at org.apache.tomcat.websocket.server.WsFrameServer.onDataAvailable(WsFrameServer.java:85)
	at org.apache.tomcat.websocket.server.WsFrameServer.doOnDataAvailable(WsFrameServer.java:184)
	at org.apache.tomcat.websocket.server.WsFrameServer.notifyDataAvailable(WsFrameServer.java:164)
	at org.apache.tomcat.websocket.server.WsHttpUpgradeHandler.upgradeDispatch(WsHttpUpgradeHandler.java:152)
	at org.apache.coyote.http11.upgrade.UpgradeProcessorInternal.dispatch(UpgradeProcessorInternal.java:60)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:57)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:928)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1794)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:52)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1191)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.base/java.lang.Thread.run(Thread.java:834)
14:03:43.246 [http-nio-9201-exec-2] INFO  c.r.f.w.WebSocketUsers - [remove,117] - 
 正在移出用户 - 4e
14:03:43.246 [http-nio-9201-exec-2] INFO  c.r.f.w.WebSocketUsers - [removeUserSessions,96] - 
 正在移出用户Session映射 - 100
14:03:48.278 [http-nio-9201-exec-1] INFO  c.r.f.w.WebSocketServer - [onOpen,100] - 
 建立连接 - org.apache.tomcat.websocket.WsSession@7f64d63e
14:03:48.278 [http-nio-9201-exec-1] INFO  c.r.f.w.WebSocketServer - [onOpen,101] - 
 当前人数 - 1
14:03:48.283 [http-nio-9201-exec-10] INFO  c.r.f.w.WebSocketServer - [onClose,113] - 
 关闭连接 - org.apache.tomcat.websocket.WsSession@7f64d63e
14:03:48.283 [http-nio-9201-exec-10] INFO  c.r.f.w.WebSocketUsers - [remove,117] - 
 正在移出用户 - 4f
14:03:48.283 [http-nio-9201-exec-10] INFO  c.r.f.w.WebSocketUsers - [remove,122] - 
 移出结果 - 成功
14:03:48.283 [http-nio-9201-exec-10] INFO  c.r.f.w.WebSocketServer - [onError,132] - 
 连接异常 - 4f
14:03:48.283 [http-nio-9201-exec-10] INFO  c.r.f.w.WebSocketServer - [onError,133] - 
 异常信息 - {}
java.lang.NullPointerException: null
	at java.base/java.util.concurrent.ConcurrentHashMap.get(ConcurrentHashMap.java:936)
	at com.ruoyi.framework.websocket.WebSocketUsers.getSessionByUserId(WebSocketUsers.java:148)
	at com.ruoyi.framework.websocket.WebSocketServer.onMessage(WebSocketServer.java:175)
	at jdk.internal.reflect.GeneratedMethodAccessor114.invoke(Unknown Source)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:566)
	at org.apache.tomcat.websocket.pojo.PojoMessageHandlerWholeBase.onMessage(PojoMessageHandlerWholeBase.java:101)
	at org.apache.tomcat.websocket.WsFrameBase.sendMessageText(WsFrameBase.java:390)
	at org.apache.tomcat.websocket.server.WsFrameServer.sendMessageText(WsFrameServer.java:130)
	at org.apache.tomcat.websocket.WsFrameBase.processDataText(WsFrameBase.java:484)
	at org.apache.tomcat.websocket.WsFrameBase.processData(WsFrameBase.java:284)
	at org.apache.tomcat.websocket.WsFrameBase.processInputBuffer(WsFrameBase.java:130)
	at org.apache.tomcat.websocket.server.WsFrameServer.onDataAvailable(WsFrameServer.java:85)
	at org.apache.tomcat.websocket.server.WsFrameServer.doOnDataAvailable(WsFrameServer.java:184)
	at org.apache.tomcat.websocket.server.WsFrameServer.notifyDataAvailable(WsFrameServer.java:164)
	at org.apache.tomcat.websocket.server.WsHttpUpgradeHandler.upgradeDispatch(WsHttpUpgradeHandler.java:152)
	at org.apache.coyote.http11.upgrade.UpgradeProcessorInternal.dispatch(UpgradeProcessorInternal.java:60)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:57)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:928)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1794)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:52)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1191)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.base/java.lang.Thread.run(Thread.java:834)
14:03:48.283 [http-nio-9201-exec-10] INFO  c.r.f.w.WebSocketUsers - [remove,117] - 
 正在移出用户 - 4f
14:03:48.283 [http-nio-9201-exec-10] INFO  c.r.f.w.WebSocketUsers - [removeUserSessions,96] - 
 正在移出用户Session映射 - 100
14:03:53.319 [http-nio-9201-exec-3] INFO  c.r.f.w.WebSocketServer - [onOpen,100] - 
 建立连接 - org.apache.tomcat.websocket.WsSession@82da291
14:03:53.320 [http-nio-9201-exec-3] INFO  c.r.f.w.WebSocketServer - [onOpen,101] - 
 当前人数 - 1
14:03:53.326 [http-nio-9201-exec-6] INFO  c.r.f.w.WebSocketServer - [onClose,113] - 
 关闭连接 - org.apache.tomcat.websocket.WsSession@82da291
14:03:53.326 [http-nio-9201-exec-6] INFO  c.r.f.w.WebSocketUsers - [remove,117] - 
 正在移出用户 - 50
14:03:53.326 [http-nio-9201-exec-6] INFO  c.r.f.w.WebSocketUsers - [remove,122] - 
 移出结果 - 成功
14:03:53.326 [http-nio-9201-exec-6] INFO  c.r.f.w.WebSocketServer - [onError,132] - 
 连接异常 - 50
14:03:53.328 [http-nio-9201-exec-6] INFO  c.r.f.w.WebSocketServer - [onError,133] - 
 异常信息 - {}
java.lang.NullPointerException: null
	at java.base/java.util.concurrent.ConcurrentHashMap.get(ConcurrentHashMap.java:936)
	at com.ruoyi.framework.websocket.WebSocketUsers.getSessionByUserId(WebSocketUsers.java:148)
	at com.ruoyi.framework.websocket.WebSocketServer.onMessage(WebSocketServer.java:175)
	at jdk.internal.reflect.GeneratedMethodAccessor114.invoke(Unknown Source)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:566)
	at org.apache.tomcat.websocket.pojo.PojoMessageHandlerWholeBase.onMessage(PojoMessageHandlerWholeBase.java:101)
	at org.apache.tomcat.websocket.WsFrameBase.sendMessageText(WsFrameBase.java:390)
	at org.apache.tomcat.websocket.server.WsFrameServer.sendMessageText(WsFrameServer.java:130)
	at org.apache.tomcat.websocket.WsFrameBase.processDataText(WsFrameBase.java:484)
	at org.apache.tomcat.websocket.WsFrameBase.processData(WsFrameBase.java:284)
	at org.apache.tomcat.websocket.WsFrameBase.processInputBuffer(WsFrameBase.java:130)
	at org.apache.tomcat.websocket.server.WsFrameServer.onDataAvailable(WsFrameServer.java:85)
	at org.apache.tomcat.websocket.server.WsFrameServer.doOnDataAvailable(WsFrameServer.java:184)
	at org.apache.tomcat.websocket.server.WsFrameServer.notifyDataAvailable(WsFrameServer.java:164)
	at org.apache.tomcat.websocket.server.WsHttpUpgradeHandler.upgradeDispatch(WsHttpUpgradeHandler.java:152)
	at org.apache.coyote.http11.upgrade.UpgradeProcessorInternal.dispatch(UpgradeProcessorInternal.java:60)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:57)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:928)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1794)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:52)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1191)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.base/java.lang.Thread.run(Thread.java:834)
14:03:53.328 [http-nio-9201-exec-6] INFO  c.r.f.w.WebSocketUsers - [remove,117] - 
 正在移出用户 - 50
14:03:53.328 [http-nio-9201-exec-6] INFO  c.r.f.w.WebSocketUsers - [removeUserSessions,96] - 
 正在移出用户Session映射 - 100
14:03:58.368 [http-nio-9201-exec-4] INFO  c.r.f.w.WebSocketServer - [onOpen,100] - 
 建立连接 - org.apache.tomcat.websocket.WsSession@737e1f19
14:03:58.368 [http-nio-9201-exec-4] INFO  c.r.f.w.WebSocketServer - [onOpen,101] - 
 当前人数 - 1
14:03:58.372 [http-nio-9201-exec-5] INFO  c.r.f.w.WebSocketServer - [onClose,113] - 
 关闭连接 - org.apache.tomcat.websocket.WsSession@737e1f19
14:03:58.373 [http-nio-9201-exec-5] INFO  c.r.f.w.WebSocketUsers - [remove,117] - 
 正在移出用户 - 51
14:03:58.373 [http-nio-9201-exec-5] INFO  c.r.f.w.WebSocketUsers - [remove,122] - 
 移出结果 - 成功
14:03:58.374 [http-nio-9201-exec-5] INFO  c.r.f.w.WebSocketServer - [onError,132] - 
 连接异常 - 51
14:03:58.374 [http-nio-9201-exec-5] INFO  c.r.f.w.WebSocketServer - [onError,133] - 
 异常信息 - {}
java.lang.NullPointerException: null
	at java.base/java.util.concurrent.ConcurrentHashMap.get(ConcurrentHashMap.java:936)
	at com.ruoyi.framework.websocket.WebSocketUsers.getSessionByUserId(WebSocketUsers.java:148)
	at com.ruoyi.framework.websocket.WebSocketServer.onMessage(WebSocketServer.java:175)
	at jdk.internal.reflect.GeneratedMethodAccessor114.invoke(Unknown Source)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:566)
	at org.apache.tomcat.websocket.pojo.PojoMessageHandlerWholeBase.onMessage(PojoMessageHandlerWholeBase.java:101)
	at org.apache.tomcat.websocket.WsFrameBase.sendMessageText(WsFrameBase.java:390)
	at org.apache.tomcat.websocket.server.WsFrameServer.sendMessageText(WsFrameServer.java:130)
	at org.apache.tomcat.websocket.WsFrameBase.processDataText(WsFrameBase.java:484)
	at org.apache.tomcat.websocket.WsFrameBase.processData(WsFrameBase.java:284)
	at org.apache.tomcat.websocket.WsFrameBase.processInputBuffer(WsFrameBase.java:130)
	at org.apache.tomcat.websocket.server.WsFrameServer.onDataAvailable(WsFrameServer.java:85)
	at org.apache.tomcat.websocket.server.WsFrameServer.doOnDataAvailable(WsFrameServer.java:184)
	at org.apache.tomcat.websocket.server.WsFrameServer.notifyDataAvailable(WsFrameServer.java:164)
	at org.apache.tomcat.websocket.server.WsHttpUpgradeHandler.upgradeDispatch(WsHttpUpgradeHandler.java:152)
	at org.apache.coyote.http11.upgrade.UpgradeProcessorInternal.dispatch(UpgradeProcessorInternal.java:60)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:57)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:928)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1794)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:52)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1191)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.base/java.lang.Thread.run(Thread.java:834)
14:03:58.375 [http-nio-9201-exec-5] INFO  c.r.f.w.WebSocketUsers - [remove,117] - 
 正在移出用户 - 51
14:03:58.375 [http-nio-9201-exec-5] INFO  c.r.f.w.WebSocketUsers - [removeUserSessions,96] - 
 正在移出用户Session映射 - 100
14:04:03.413 [http-nio-9201-exec-8] INFO  c.r.f.w.WebSocketServer - [onOpen,100] - 
 建立连接 - org.apache.tomcat.websocket.WsSession@17e17be5
14:04:03.413 [http-nio-9201-exec-8] INFO  c.r.f.w.WebSocketServer - [onOpen,101] - 
 当前人数 - 1
14:04:03.417 [http-nio-9201-exec-7] INFO  c.r.f.w.WebSocketServer - [onClose,113] - 
 关闭连接 - org.apache.tomcat.websocket.WsSession@17e17be5
14:04:03.417 [http-nio-9201-exec-7] INFO  c.r.f.w.WebSocketUsers - [remove,117] - 
 正在移出用户 - 52
14:04:03.417 [http-nio-9201-exec-7] INFO  c.r.f.w.WebSocketUsers - [remove,122] - 
 移出结果 - 成功
14:04:03.419 [http-nio-9201-exec-7] INFO  c.r.f.w.WebSocketServer - [onError,132] - 
 连接异常 - 52
14:04:03.419 [http-nio-9201-exec-7] INFO  c.r.f.w.WebSocketServer - [onError,133] - 
 异常信息 - {}
java.lang.NullPointerException: null
	at java.base/java.util.concurrent.ConcurrentHashMap.get(ConcurrentHashMap.java:936)
	at com.ruoyi.framework.websocket.WebSocketUsers.getSessionByUserId(WebSocketUsers.java:148)
	at com.ruoyi.framework.websocket.WebSocketServer.onMessage(WebSocketServer.java:175)
	at jdk.internal.reflect.GeneratedMethodAccessor114.invoke(Unknown Source)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:566)
	at org.apache.tomcat.websocket.pojo.PojoMessageHandlerWholeBase.onMessage(PojoMessageHandlerWholeBase.java:101)
	at org.apache.tomcat.websocket.WsFrameBase.sendMessageText(WsFrameBase.java:390)
	at org.apache.tomcat.websocket.server.WsFrameServer.sendMessageText(WsFrameServer.java:130)
	at org.apache.tomcat.websocket.WsFrameBase.processDataText(WsFrameBase.java:484)
	at org.apache.tomcat.websocket.WsFrameBase.processData(WsFrameBase.java:284)
	at org.apache.tomcat.websocket.WsFrameBase.processInputBuffer(WsFrameBase.java:130)
	at org.apache.tomcat.websocket.server.WsFrameServer.onDataAvailable(WsFrameServer.java:85)
	at org.apache.tomcat.websocket.server.WsFrameServer.doOnDataAvailable(WsFrameServer.java:184)
	at org.apache.tomcat.websocket.server.WsFrameServer.notifyDataAvailable(WsFrameServer.java:164)
	at org.apache.tomcat.websocket.server.WsHttpUpgradeHandler.upgradeDispatch(WsHttpUpgradeHandler.java:152)
	at org.apache.coyote.http11.upgrade.UpgradeProcessorInternal.dispatch(UpgradeProcessorInternal.java:60)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:57)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:928)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1794)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:52)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1191)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.base/java.lang.Thread.run(Thread.java:834)
14:04:03.420 [http-nio-9201-exec-7] INFO  c.r.f.w.WebSocketUsers - [remove,117] - 
 正在移出用户 - 52
14:04:03.420 [http-nio-9201-exec-7] INFO  c.r.f.w.WebSocketUsers - [removeUserSessions,96] - 
 正在移出用户Session映射 - 100
14:04:08.446 [http-nio-9201-exec-9] INFO  c.r.f.w.WebSocketServer - [onOpen,100] - 
 建立连接 - org.apache.tomcat.websocket.WsSession@28418036
14:04:08.446 [http-nio-9201-exec-9] INFO  c.r.f.w.WebSocketServer - [onOpen,101] - 
 当前人数 - 1
14:04:08.449 [http-nio-9201-exec-2] INFO  c.r.f.w.WebSocketServer - [onClose,113] - 
 关闭连接 - org.apache.tomcat.websocket.WsSession@28418036
14:04:08.450 [http-nio-9201-exec-2] INFO  c.r.f.w.WebSocketUsers - [remove,117] - 
 正在移出用户 - 53
14:04:08.450 [http-nio-9201-exec-2] INFO  c.r.f.w.WebSocketUsers - [remove,122] - 
 移出结果 - 成功
14:04:08.450 [http-nio-9201-exec-2] INFO  c.r.f.w.WebSocketServer - [onError,132] - 
 连接异常 - 53
14:04:08.451 [http-nio-9201-exec-2] INFO  c.r.f.w.WebSocketServer - [onError,133] - 
 异常信息 - {}
java.lang.NullPointerException: null
	at java.base/java.util.concurrent.ConcurrentHashMap.get(ConcurrentHashMap.java:936)
	at com.ruoyi.framework.websocket.WebSocketUsers.getSessionByUserId(WebSocketUsers.java:148)
	at com.ruoyi.framework.websocket.WebSocketServer.onMessage(WebSocketServer.java:175)
	at jdk.internal.reflect.GeneratedMethodAccessor114.invoke(Unknown Source)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:566)
	at org.apache.tomcat.websocket.pojo.PojoMessageHandlerWholeBase.onMessage(PojoMessageHandlerWholeBase.java:101)
	at org.apache.tomcat.websocket.WsFrameBase.sendMessageText(WsFrameBase.java:390)
	at org.apache.tomcat.websocket.server.WsFrameServer.sendMessageText(WsFrameServer.java:130)
	at org.apache.tomcat.websocket.WsFrameBase.processDataText(WsFrameBase.java:484)
	at org.apache.tomcat.websocket.WsFrameBase.processData(WsFrameBase.java:284)
	at org.apache.tomcat.websocket.WsFrameBase.processInputBuffer(WsFrameBase.java:130)
	at org.apache.tomcat.websocket.server.WsFrameServer.onDataAvailable(WsFrameServer.java:85)
	at org.apache.tomcat.websocket.server.WsFrameServer.doOnDataAvailable(WsFrameServer.java:184)
	at org.apache.tomcat.websocket.server.WsFrameServer.notifyDataAvailable(WsFrameServer.java:164)
	at org.apache.tomcat.websocket.server.WsHttpUpgradeHandler.upgradeDispatch(WsHttpUpgradeHandler.java:152)
	at org.apache.coyote.http11.upgrade.UpgradeProcessorInternal.dispatch(UpgradeProcessorInternal.java:60)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:57)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:928)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1794)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:52)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1191)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.base/java.lang.Thread.run(Thread.java:834)
14:04:08.451 [http-nio-9201-exec-2] INFO  c.r.f.w.WebSocketUsers - [remove,117] - 
 正在移出用户 - 53
14:04:08.451 [http-nio-9201-exec-2] INFO  c.r.f.w.WebSocketUsers - [removeUserSessions,96] - 
 正在移出用户Session映射 - 100
14:04:13.481 [http-nio-9201-exec-1] INFO  c.r.f.w.WebSocketServer - [onOpen,100] - 
 建立连接 - org.apache.tomcat.websocket.WsSession@60e3b37b
14:04:13.481 [http-nio-9201-exec-1] INFO  c.r.f.w.WebSocketServer - [onOpen,101] - 
 当前人数 - 1
14:04:13.485 [http-nio-9201-exec-10] INFO  c.r.f.w.WebSocketServer - [onClose,113] - 
 关闭连接 - org.apache.tomcat.websocket.WsSession@60e3b37b
14:04:13.486 [http-nio-9201-exec-10] INFO  c.r.f.w.WebSocketUsers - [remove,117] - 
 正在移出用户 - 54
14:04:13.486 [http-nio-9201-exec-10] INFO  c.r.f.w.WebSocketUsers - [remove,122] - 
 移出结果 - 成功
14:04:13.486 [http-nio-9201-exec-10] INFO  c.r.f.w.WebSocketServer - [onError,132] - 
 连接异常 - 54
14:04:13.487 [http-nio-9201-exec-10] INFO  c.r.f.w.WebSocketServer - [onError,133] - 
 异常信息 - {}
java.lang.NullPointerException: null
	at java.base/java.util.concurrent.ConcurrentHashMap.get(ConcurrentHashMap.java:936)
	at com.ruoyi.framework.websocket.WebSocketUsers.getSessionByUserId(WebSocketUsers.java:148)
	at com.ruoyi.framework.websocket.WebSocketServer.onMessage(WebSocketServer.java:175)
	at jdk.internal.reflect.GeneratedMethodAccessor114.invoke(Unknown Source)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:566)
	at org.apache.tomcat.websocket.pojo.PojoMessageHandlerWholeBase.onMessage(PojoMessageHandlerWholeBase.java:101)
	at org.apache.tomcat.websocket.WsFrameBase.sendMessageText(WsFrameBase.java:390)
	at org.apache.tomcat.websocket.server.WsFrameServer.sendMessageText(WsFrameServer.java:130)
	at org.apache.tomcat.websocket.WsFrameBase.processDataText(WsFrameBase.java:484)
	at org.apache.tomcat.websocket.WsFrameBase.processData(WsFrameBase.java:284)
	at org.apache.tomcat.websocket.WsFrameBase.processInputBuffer(WsFrameBase.java:130)
	at org.apache.tomcat.websocket.server.WsFrameServer.onDataAvailable(WsFrameServer.java:85)
	at org.apache.tomcat.websocket.server.WsFrameServer.doOnDataAvailable(WsFrameServer.java:184)
	at org.apache.tomcat.websocket.server.WsFrameServer.notifyDataAvailable(WsFrameServer.java:164)
	at org.apache.tomcat.websocket.server.WsHttpUpgradeHandler.upgradeDispatch(WsHttpUpgradeHandler.java:152)
	at org.apache.coyote.http11.upgrade.UpgradeProcessorInternal.dispatch(UpgradeProcessorInternal.java:60)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:57)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:928)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1794)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:52)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1191)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.base/java.lang.Thread.run(Thread.java:834)
14:04:13.488 [http-nio-9201-exec-10] INFO  c.r.f.w.WebSocketUsers - [remove,117] - 
 正在移出用户 - 54
14:04:13.488 [http-nio-9201-exec-10] INFO  c.r.f.w.WebSocketUsers - [removeUserSessions,96] - 
 正在移出用户Session映射 - 100
14:04:18.523 [http-nio-9201-exec-3] INFO  c.r.f.w.WebSocketServer - [onOpen,100] - 
 建立连接 - org.apache.tomcat.websocket.WsSession@637ae7
14:04:18.523 [http-nio-9201-exec-3] INFO  c.r.f.w.WebSocketServer - [onOpen,101] - 
 当前人数 - 1
14:04:18.528 [http-nio-9201-exec-6] INFO  c.r.f.w.WebSocketServer - [onClose,113] - 
 关闭连接 - org.apache.tomcat.websocket.WsSession@637ae7
14:04:18.528 [http-nio-9201-exec-6] INFO  c.r.f.w.WebSocketUsers - [remove,117] - 
 正在移出用户 - 55
14:04:18.528 [http-nio-9201-exec-6] INFO  c.r.f.w.WebSocketUsers - [remove,122] - 
 移出结果 - 成功
14:04:18.529 [http-nio-9201-exec-6] INFO  c.r.f.w.WebSocketServer - [onError,132] - 
 连接异常 - 55
14:04:18.530 [http-nio-9201-exec-6] INFO  c.r.f.w.WebSocketServer - [onError,133] - 
 异常信息 - {}
java.lang.NullPointerException: null
	at java.base/java.util.concurrent.ConcurrentHashMap.get(ConcurrentHashMap.java:936)
	at com.ruoyi.framework.websocket.WebSocketUsers.getSessionByUserId(WebSocketUsers.java:148)
	at com.ruoyi.framework.websocket.WebSocketServer.onMessage(WebSocketServer.java:175)
	at jdk.internal.reflect.GeneratedMethodAccessor114.invoke(Unknown Source)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:566)
	at org.apache.tomcat.websocket.pojo.PojoMessageHandlerWholeBase.onMessage(PojoMessageHandlerWholeBase.java:101)
	at org.apache.tomcat.websocket.WsFrameBase.sendMessageText(WsFrameBase.java:390)
	at org.apache.tomcat.websocket.server.WsFrameServer.sendMessageText(WsFrameServer.java:130)
	at org.apache.tomcat.websocket.WsFrameBase.processDataText(WsFrameBase.java:484)
	at org.apache.tomcat.websocket.WsFrameBase.processData(WsFrameBase.java:284)
	at org.apache.tomcat.websocket.WsFrameBase.processInputBuffer(WsFrameBase.java:130)
	at org.apache.tomcat.websocket.server.WsFrameServer.onDataAvailable(WsFrameServer.java:85)
	at org.apache.tomcat.websocket.server.WsFrameServer.doOnDataAvailable(WsFrameServer.java:184)
	at org.apache.tomcat.websocket.server.WsFrameServer.notifyDataAvailable(WsFrameServer.java:164)
	at org.apache.tomcat.websocket.server.WsHttpUpgradeHandler.upgradeDispatch(WsHttpUpgradeHandler.java:152)
	at org.apache.coyote.http11.upgrade.UpgradeProcessorInternal.dispatch(UpgradeProcessorInternal.java:60)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:57)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:928)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1794)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:52)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1191)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.base/java.lang.Thread.run(Thread.java:834)
14:04:18.530 [http-nio-9201-exec-6] INFO  c.r.f.w.WebSocketUsers - [remove,117] - 
 正在移出用户 - 55
14:04:18.531 [http-nio-9201-exec-6] INFO  c.r.f.w.WebSocketUsers - [removeUserSessions,96] - 
 正在移出用户Session映射 - 100
14:04:23.563 [http-nio-9201-exec-4] INFO  c.r.f.w.WebSocketServer - [onOpen,100] - 
 建立连接 - org.apache.tomcat.websocket.WsSession@bbd0072
14:04:23.563 [http-nio-9201-exec-4] INFO  c.r.f.w.WebSocketServer - [onOpen,101] - 
 当前人数 - 1
14:04:23.567 [http-nio-9201-exec-5] INFO  c.r.f.w.WebSocketServer - [onClose,113] - 
 关闭连接 - org.apache.tomcat.websocket.WsSession@bbd0072
14:04:23.568 [http-nio-9201-exec-5] INFO  c.r.f.w.WebSocketUsers - [remove,117] - 
 正在移出用户 - 56
14:04:23.568 [http-nio-9201-exec-5] INFO  c.r.f.w.WebSocketUsers - [remove,122] - 
 移出结果 - 成功
14:04:23.568 [http-nio-9201-exec-5] INFO  c.r.f.w.WebSocketServer - [onError,132] - 
 连接异常 - 56
14:04:23.569 [http-nio-9201-exec-5] INFO  c.r.f.w.WebSocketServer - [onError,133] - 
 异常信息 - {}
java.lang.NullPointerException: null
	at java.base/java.util.concurrent.ConcurrentHashMap.get(ConcurrentHashMap.java:936)
	at com.ruoyi.framework.websocket.WebSocketUsers.getSessionByUserId(WebSocketUsers.java:148)
	at com.ruoyi.framework.websocket.WebSocketServer.onMessage(WebSocketServer.java:175)
	at jdk.internal.reflect.GeneratedMethodAccessor114.invoke(Unknown Source)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:566)
	at org.apache.tomcat.websocket.pojo.PojoMessageHandlerWholeBase.onMessage(PojoMessageHandlerWholeBase.java:101)
	at org.apache.tomcat.websocket.WsFrameBase.sendMessageText(WsFrameBase.java:390)
	at org.apache.tomcat.websocket.server.WsFrameServer.sendMessageText(WsFrameServer.java:130)
	at org.apache.tomcat.websocket.WsFrameBase.processDataText(WsFrameBase.java:484)
	at org.apache.tomcat.websocket.WsFrameBase.processData(WsFrameBase.java:284)
	at org.apache.tomcat.websocket.WsFrameBase.processInputBuffer(WsFrameBase.java:130)
	at org.apache.tomcat.websocket.server.WsFrameServer.onDataAvailable(WsFrameServer.java:85)
	at org.apache.tomcat.websocket.server.WsFrameServer.doOnDataAvailable(WsFrameServer.java:184)
	at org.apache.tomcat.websocket.server.WsFrameServer.notifyDataAvailable(WsFrameServer.java:164)
	at org.apache.tomcat.websocket.server.WsHttpUpgradeHandler.upgradeDispatch(WsHttpUpgradeHandler.java:152)
	at org.apache.coyote.http11.upgrade.UpgradeProcessorInternal.dispatch(UpgradeProcessorInternal.java:60)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:57)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:928)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1794)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:52)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1191)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.base/java.lang.Thread.run(Thread.java:834)
14:04:23.570 [http-nio-9201-exec-5] INFO  c.r.f.w.WebSocketUsers - [remove,117] - 
 正在移出用户 - 56
14:04:23.570 [http-nio-9201-exec-5] INFO  c.r.f.w.WebSocketUsers - [removeUserSessions,96] - 
 正在移出用户Session映射 - 100
14:04:28.605 [http-nio-9201-exec-8] INFO  c.r.f.w.WebSocketServer - [onOpen,100] - 
 建立连接 - org.apache.tomcat.websocket.WsSession@5948ef24
14:04:28.606 [http-nio-9201-exec-8] INFO  c.r.f.w.WebSocketServer - [onOpen,101] - 
 当前人数 - 1
14:04:28.610 [http-nio-9201-exec-7] INFO  c.r.f.w.WebSocketServer - [onClose,113] - 
 关闭连接 - org.apache.tomcat.websocket.WsSession@5948ef24
14:04:28.610 [http-nio-9201-exec-7] INFO  c.r.f.w.WebSocketUsers - [remove,117] - 
 正在移出用户 - 57
14:04:28.610 [http-nio-9201-exec-7] INFO  c.r.f.w.WebSocketUsers - [remove,122] - 
 移出结果 - 成功
14:04:28.610 [http-nio-9201-exec-7] INFO  c.r.f.w.WebSocketServer - [onError,132] - 
 连接异常 - 57
14:04:28.611 [http-nio-9201-exec-7] INFO  c.r.f.w.WebSocketServer - [onError,133] - 
 异常信息 - {}
java.lang.NullPointerException: null
	at java.base/java.util.concurrent.ConcurrentHashMap.get(ConcurrentHashMap.java:936)
	at com.ruoyi.framework.websocket.WebSocketUsers.getSessionByUserId(WebSocketUsers.java:148)
	at com.ruoyi.framework.websocket.WebSocketServer.onMessage(WebSocketServer.java:175)
	at jdk.internal.reflect.GeneratedMethodAccessor114.invoke(Unknown Source)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:566)
	at org.apache.tomcat.websocket.pojo.PojoMessageHandlerWholeBase.onMessage(PojoMessageHandlerWholeBase.java:101)
	at org.apache.tomcat.websocket.WsFrameBase.sendMessageText(WsFrameBase.java:390)
	at org.apache.tomcat.websocket.server.WsFrameServer.sendMessageText(WsFrameServer.java:130)
	at org.apache.tomcat.websocket.WsFrameBase.processDataText(WsFrameBase.java:484)
	at org.apache.tomcat.websocket.WsFrameBase.processData(WsFrameBase.java:284)
	at org.apache.tomcat.websocket.WsFrameBase.processInputBuffer(WsFrameBase.java:130)
	at org.apache.tomcat.websocket.server.WsFrameServer.onDataAvailable(WsFrameServer.java:85)
	at org.apache.tomcat.websocket.server.WsFrameServer.doOnDataAvailable(WsFrameServer.java:184)
	at org.apache.tomcat.websocket.server.WsFrameServer.notifyDataAvailable(WsFrameServer.java:164)
	at org.apache.tomcat.websocket.server.WsHttpUpgradeHandler.upgradeDispatch(WsHttpUpgradeHandler.java:152)
	at org.apache.coyote.http11.upgrade.UpgradeProcessorInternal.dispatch(UpgradeProcessorInternal.java:60)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:57)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:928)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1794)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:52)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1191)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.base/java.lang.Thread.run(Thread.java:834)
14:04:28.612 [http-nio-9201-exec-7] INFO  c.r.f.w.WebSocketUsers - [remove,117] - 
 正在移出用户 - 57
14:04:28.612 [http-nio-9201-exec-7] INFO  c.r.f.w.WebSocketUsers - [removeUserSessions,96] - 
 正在移出用户Session映射 - 100
14:04:33.654 [http-nio-9201-exec-9] INFO  c.r.f.w.WebSocketServer - [onOpen,100] - 
 建立连接 - org.apache.tomcat.websocket.WsSession@7181b9d7
14:04:33.655 [http-nio-9201-exec-9] INFO  c.r.f.w.WebSocketServer - [onOpen,101] - 
 当前人数 - 1
14:04:33.660 [http-nio-9201-exec-2] INFO  c.r.f.w.WebSocketServer - [onClose,113] - 
 关闭连接 - org.apache.tomcat.websocket.WsSession@7181b9d7
14:04:33.660 [http-nio-9201-exec-2] INFO  c.r.f.w.WebSocketUsers - [remove,117] - 
 正在移出用户 - 58
14:04:33.660 [http-nio-9201-exec-2] INFO  c.r.f.w.WebSocketUsers - [remove,122] - 
 移出结果 - 成功
14:04:33.660 [http-nio-9201-exec-2] INFO  c.r.f.w.WebSocketServer - [onError,132] - 
 连接异常 - 58
14:04:33.661 [http-nio-9201-exec-2] INFO  c.r.f.w.WebSocketServer - [onError,133] - 
 异常信息 - {}
java.lang.NullPointerException: null
	at java.base/java.util.concurrent.ConcurrentHashMap.get(ConcurrentHashMap.java:936)
	at com.ruoyi.framework.websocket.WebSocketUsers.getSessionByUserId(WebSocketUsers.java:148)
	at com.ruoyi.framework.websocket.WebSocketServer.onMessage(WebSocketServer.java:175)
	at jdk.internal.reflect.GeneratedMethodAccessor114.invoke(Unknown Source)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:566)
	at org.apache.tomcat.websocket.pojo.PojoMessageHandlerWholeBase.onMessage(PojoMessageHandlerWholeBase.java:101)
	at org.apache.tomcat.websocket.WsFrameBase.sendMessageText(WsFrameBase.java:390)
	at org.apache.tomcat.websocket.server.WsFrameServer.sendMessageText(WsFrameServer.java:130)
	at org.apache.tomcat.websocket.WsFrameBase.processDataText(WsFrameBase.java:484)
	at org.apache.tomcat.websocket.WsFrameBase.processData(WsFrameBase.java:284)
	at org.apache.tomcat.websocket.WsFrameBase.processInputBuffer(WsFrameBase.java:130)
	at org.apache.tomcat.websocket.server.WsFrameServer.onDataAvailable(WsFrameServer.java:85)
	at org.apache.tomcat.websocket.server.WsFrameServer.doOnDataAvailable(WsFrameServer.java:184)
	at org.apache.tomcat.websocket.server.WsFrameServer.notifyDataAvailable(WsFrameServer.java:164)
	at org.apache.tomcat.websocket.server.WsHttpUpgradeHandler.upgradeDispatch(WsHttpUpgradeHandler.java:152)
	at org.apache.coyote.http11.upgrade.UpgradeProcessorInternal.dispatch(UpgradeProcessorInternal.java:60)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:57)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:928)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1794)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:52)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1191)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.base/java.lang.Thread.run(Thread.java:834)
14:04:33.661 [http-nio-9201-exec-2] INFO  c.r.f.w.WebSocketUsers - [remove,117] - 
 正在移出用户 - 58
14:04:33.662 [http-nio-9201-exec-2] INFO  c.r.f.w.WebSocketUsers - [removeUserSessions,96] - 
 正在移出用户Session映射 - 100
14:04:38.703 [http-nio-9201-exec-1] INFO  c.r.f.w.WebSocketServer - [onOpen,100] - 
 建立连接 - org.apache.tomcat.websocket.WsSession@2949b5a4
14:04:38.703 [http-nio-9201-exec-1] INFO  c.r.f.w.WebSocketServer - [onOpen,101] - 
 当前人数 - 1
14:04:38.707 [http-nio-9201-exec-10] INFO  c.r.f.w.WebSocketServer - [onClose,113] - 
 关闭连接 - org.apache.tomcat.websocket.WsSession@2949b5a4
14:04:38.708 [http-nio-9201-exec-10] INFO  c.r.f.w.WebSocketUsers - [remove,117] - 
 正在移出用户 - 59
14:04:38.708 [http-nio-9201-exec-10] INFO  c.r.f.w.WebSocketUsers - [remove,122] - 
 移出结果 - 成功
14:04:38.708 [http-nio-9201-exec-10] INFO  c.r.f.w.WebSocketServer - [onError,132] - 
 连接异常 - 59
14:04:38.709 [http-nio-9201-exec-10] INFO  c.r.f.w.WebSocketServer - [onError,133] - 
 异常信息 - {}
java.lang.NullPointerException: null
	at java.base/java.util.concurrent.ConcurrentHashMap.get(ConcurrentHashMap.java:936)
	at com.ruoyi.framework.websocket.WebSocketUsers.getSessionByUserId(WebSocketUsers.java:148)
	at com.ruoyi.framework.websocket.WebSocketServer.onMessage(WebSocketServer.java:175)
	at jdk.internal.reflect.GeneratedMethodAccessor114.invoke(Unknown Source)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:566)
	at org.apache.tomcat.websocket.pojo.PojoMessageHandlerWholeBase.onMessage(PojoMessageHandlerWholeBase.java:101)
	at org.apache.tomcat.websocket.WsFrameBase.sendMessageText(WsFrameBase.java:390)
	at org.apache.tomcat.websocket.server.WsFrameServer.sendMessageText(WsFrameServer.java:130)
	at org.apache.tomcat.websocket.WsFrameBase.processDataText(WsFrameBase.java:484)
	at org.apache.tomcat.websocket.WsFrameBase.processData(WsFrameBase.java:284)
	at org.apache.tomcat.websocket.WsFrameBase.processInputBuffer(WsFrameBase.java:130)
	at org.apache.tomcat.websocket.server.WsFrameServer.onDataAvailable(WsFrameServer.java:85)
	at org.apache.tomcat.websocket.server.WsFrameServer.doOnDataAvailable(WsFrameServer.java:184)
	at org.apache.tomcat.websocket.server.WsFrameServer.notifyDataAvailable(WsFrameServer.java:164)
	at org.apache.tomcat.websocket.server.WsHttpUpgradeHandler.upgradeDispatch(WsHttpUpgradeHandler.java:152)
	at org.apache.coyote.http11.upgrade.UpgradeProcessorInternal.dispatch(UpgradeProcessorInternal.java:60)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:57)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:928)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1794)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:52)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1191)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.base/java.lang.Thread.run(Thread.java:834)
14:04:38.710 [http-nio-9201-exec-10] INFO  c.r.f.w.WebSocketUsers - [remove,117] - 
 正在移出用户 - 59
14:04:38.710 [http-nio-9201-exec-10] INFO  c.r.f.w.WebSocketUsers - [removeUserSessions,96] - 
 正在移出用户Session映射 - 100
14:04:43.745 [http-nio-9201-exec-3] INFO  c.r.f.w.WebSocketServer - [onOpen,100] - 
 建立连接 - org.apache.tomcat.websocket.WsSession@a5bd435
14:04:43.745 [http-nio-9201-exec-3] INFO  c.r.f.w.WebSocketServer - [onOpen,101] - 
 当前人数 - 1
14:04:43.747 [http-nio-9201-exec-6] INFO  c.r.f.w.WebSocketServer - [onClose,113] - 
 关闭连接 - org.apache.tomcat.websocket.WsSession@a5bd435
14:04:43.747 [http-nio-9201-exec-6] INFO  c.r.f.w.WebSocketUsers - [remove,117] - 
 正在移出用户 - 5a
14:04:43.748 [http-nio-9201-exec-6] INFO  c.r.f.w.WebSocketUsers - [remove,122] - 
 移出结果 - 成功
14:04:43.748 [http-nio-9201-exec-6] INFO  c.r.f.w.WebSocketServer - [onError,132] - 
 连接异常 - 5a
14:04:43.748 [http-nio-9201-exec-6] INFO  c.r.f.w.WebSocketServer - [onError,133] - 
 异常信息 - {}
java.lang.NullPointerException: null
	at java.base/java.util.concurrent.ConcurrentHashMap.get(ConcurrentHashMap.java:936)
	at com.ruoyi.framework.websocket.WebSocketUsers.getSessionByUserId(WebSocketUsers.java:148)
	at com.ruoyi.framework.websocket.WebSocketServer.onMessage(WebSocketServer.java:175)
	at jdk.internal.reflect.GeneratedMethodAccessor114.invoke(Unknown Source)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:566)
	at org.apache.tomcat.websocket.pojo.PojoMessageHandlerWholeBase.onMessage(PojoMessageHandlerWholeBase.java:101)
	at org.apache.tomcat.websocket.WsFrameBase.sendMessageText(WsFrameBase.java:390)
	at org.apache.tomcat.websocket.server.WsFrameServer.sendMessageText(WsFrameServer.java:130)
	at org.apache.tomcat.websocket.WsFrameBase.processDataText(WsFrameBase.java:484)
	at org.apache.tomcat.websocket.WsFrameBase.processData(WsFrameBase.java:284)
	at org.apache.tomcat.websocket.WsFrameBase.processInputBuffer(WsFrameBase.java:130)
	at org.apache.tomcat.websocket.server.WsFrameServer.onDataAvailable(WsFrameServer.java:85)
	at org.apache.tomcat.websocket.server.WsFrameServer.doOnDataAvailable(WsFrameServer.java:184)
	at org.apache.tomcat.websocket.server.WsFrameServer.notifyDataAvailable(WsFrameServer.java:164)
	at org.apache.tomcat.websocket.server.WsHttpUpgradeHandler.upgradeDispatch(WsHttpUpgradeHandler.java:152)
	at org.apache.coyote.http11.upgrade.UpgradeProcessorInternal.dispatch(UpgradeProcessorInternal.java:60)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:57)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:928)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1794)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:52)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1191)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.base/java.lang.Thread.run(Thread.java:834)
14:04:43.749 [http-nio-9201-exec-6] INFO  c.r.f.w.WebSocketUsers - [remove,117] - 
 正在移出用户 - 5a
14:04:43.749 [http-nio-9201-exec-6] INFO  c.r.f.w.WebSocketUsers - [removeUserSessions,96] - 
 正在移出用户Session映射 - 100
14:04:48.769 [http-nio-9201-exec-4] INFO  c.r.f.w.WebSocketServer - [onOpen,100] - 
 建立连接 - org.apache.tomcat.websocket.WsSession@26cfe501
14:04:48.769 [http-nio-9201-exec-4] INFO  c.r.f.w.WebSocketServer - [onOpen,101] - 
 当前人数 - 1
14:04:48.771 [http-nio-9201-exec-5] INFO  c.r.f.w.WebSocketServer - [onClose,113] - 
 关闭连接 - org.apache.tomcat.websocket.WsSession@26cfe501
14:04:48.772 [http-nio-9201-exec-5] INFO  c.r.f.w.WebSocketUsers - [remove,117] - 
 正在移出用户 - 5b
14:04:48.772 [http-nio-9201-exec-5] INFO  c.r.f.w.WebSocketUsers - [remove,122] - 
 移出结果 - 成功
14:04:48.772 [http-nio-9201-exec-5] INFO  c.r.f.w.WebSocketServer - [onError,132] - 
 连接异常 - 5b
14:04:48.773 [http-nio-9201-exec-5] INFO  c.r.f.w.WebSocketServer - [onError,133] - 
 异常信息 - {}
java.lang.NullPointerException: null
	at java.base/java.util.concurrent.ConcurrentHashMap.get(ConcurrentHashMap.java:936)
	at com.ruoyi.framework.websocket.WebSocketUsers.getSessionByUserId(WebSocketUsers.java:148)
	at com.ruoyi.framework.websocket.WebSocketServer.onMessage(WebSocketServer.java:175)
	at jdk.internal.reflect.GeneratedMethodAccessor114.invoke(Unknown Source)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:566)
	at org.apache.tomcat.websocket.pojo.PojoMessageHandlerWholeBase.onMessage(PojoMessageHandlerWholeBase.java:101)
	at org.apache.tomcat.websocket.WsFrameBase.sendMessageText(WsFrameBase.java:390)
	at org.apache.tomcat.websocket.server.WsFrameServer.sendMessageText(WsFrameServer.java:130)
	at org.apache.tomcat.websocket.WsFrameBase.processDataText(WsFrameBase.java:484)
	at org.apache.tomcat.websocket.WsFrameBase.processData(WsFrameBase.java:284)
	at org.apache.tomcat.websocket.WsFrameBase.processInputBuffer(WsFrameBase.java:130)
	at org.apache.tomcat.websocket.server.WsFrameServer.onDataAvailable(WsFrameServer.java:85)
	at org.apache.tomcat.websocket.server.WsFrameServer.doOnDataAvailable(WsFrameServer.java:184)
	at org.apache.tomcat.websocket.server.WsFrameServer.notifyDataAvailable(WsFrameServer.java:164)
	at org.apache.tomcat.websocket.server.WsHttpUpgradeHandler.upgradeDispatch(WsHttpUpgradeHandler.java:152)
	at org.apache.coyote.http11.upgrade.UpgradeProcessorInternal.dispatch(UpgradeProcessorInternal.java:60)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:57)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:928)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1794)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:52)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1191)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.base/java.lang.Thread.run(Thread.java:834)
14:04:48.773 [http-nio-9201-exec-5] INFO  c.r.f.w.WebSocketUsers - [remove,117] - 
 正在移出用户 - 5b
14:04:48.773 [http-nio-9201-exec-5] INFO  c.r.f.w.WebSocketUsers - [removeUserSessions,96] - 
 正在移出用户Session映射 - 100
14:04:53.789 [http-nio-9201-exec-8] INFO  c.r.f.w.WebSocketServer - [onOpen,100] - 
 建立连接 - org.apache.tomcat.websocket.WsSession@2bdcda17
14:04:53.789 [http-nio-9201-exec-8] INFO  c.r.f.w.WebSocketServer - [onOpen,101] - 
 当前人数 - 1
14:04:53.793 [http-nio-9201-exec-7] INFO  c.r.f.w.WebSocketServer - [onClose,113] - 
 关闭连接 - org.apache.tomcat.websocket.WsSession@2bdcda17
14:04:53.793 [http-nio-9201-exec-7] INFO  c.r.f.w.WebSocketUsers - [remove,117] - 
 正在移出用户 - 5c
14:04:53.793 [http-nio-9201-exec-7] INFO  c.r.f.w.WebSocketUsers - [remove,122] - 
 移出结果 - 成功
14:04:53.793 [http-nio-9201-exec-7] INFO  c.r.f.w.WebSocketServer - [onError,132] - 
 连接异常 - 5c
14:04:53.794 [http-nio-9201-exec-7] INFO  c.r.f.w.WebSocketServer - [onError,133] - 
 异常信息 - {}
java.lang.NullPointerException: null
	at java.base/java.util.concurrent.ConcurrentHashMap.get(ConcurrentHashMap.java:936)
	at com.ruoyi.framework.websocket.WebSocketUsers.getSessionByUserId(WebSocketUsers.java:148)
	at com.ruoyi.framework.websocket.WebSocketServer.onMessage(WebSocketServer.java:175)
	at jdk.internal.reflect.GeneratedMethodAccessor114.invoke(Unknown Source)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:566)
	at org.apache.tomcat.websocket.pojo.PojoMessageHandlerWholeBase.onMessage(PojoMessageHandlerWholeBase.java:101)
	at org.apache.tomcat.websocket.WsFrameBase.sendMessageText(WsFrameBase.java:390)
	at org.apache.tomcat.websocket.server.WsFrameServer.sendMessageText(WsFrameServer.java:130)
	at org.apache.tomcat.websocket.WsFrameBase.processDataText(WsFrameBase.java:484)
	at org.apache.tomcat.websocket.WsFrameBase.processData(WsFrameBase.java:284)
	at org.apache.tomcat.websocket.WsFrameBase.processInputBuffer(WsFrameBase.java:130)
	at org.apache.tomcat.websocket.server.WsFrameServer.onDataAvailable(WsFrameServer.java:85)
	at org.apache.tomcat.websocket.server.WsFrameServer.doOnDataAvailable(WsFrameServer.java:184)
	at org.apache.tomcat.websocket.server.WsFrameServer.notifyDataAvailable(WsFrameServer.java:164)
	at org.apache.tomcat.websocket.server.WsHttpUpgradeHandler.upgradeDispatch(WsHttpUpgradeHandler.java:152)
	at org.apache.coyote.http11.upgrade.UpgradeProcessorInternal.dispatch(UpgradeProcessorInternal.java:60)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:57)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:928)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1794)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:52)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1191)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.base/java.lang.Thread.run(Thread.java:834)
14:04:53.794 [http-nio-9201-exec-7] INFO  c.r.f.w.WebSocketUsers - [remove,117] - 
 正在移出用户 - 5c
14:04:53.794 [http-nio-9201-exec-7] INFO  c.r.f.w.WebSocketUsers - [removeUserSessions,96] - 
 正在移出用户Session映射 - 100
14:04:58.813 [http-nio-9201-exec-9] INFO  c.r.f.w.WebSocketServer - [onOpen,100] - 
 建立连接 - org.apache.tomcat.websocket.WsSession@6c210adc
14:04:58.813 [http-nio-9201-exec-9] INFO  c.r.f.w.WebSocketServer - [onOpen,101] - 
 当前人数 - 1
14:04:58.816 [http-nio-9201-exec-2] INFO  c.r.f.w.WebSocketServer - [onClose,113] - 
 关闭连接 - org.apache.tomcat.websocket.WsSession@6c210adc
14:04:58.816 [http-nio-9201-exec-2] INFO  c.r.f.w.WebSocketUsers - [remove,117] - 
 正在移出用户 - 5d
14:04:58.816 [http-nio-9201-exec-2] INFO  c.r.f.w.WebSocketUsers - [remove,122] - 
 移出结果 - 成功
14:04:58.816 [http-nio-9201-exec-2] INFO  c.r.f.w.WebSocketServer - [onError,132] - 
 连接异常 - 5d
14:04:58.817 [http-nio-9201-exec-2] INFO  c.r.f.w.WebSocketServer - [onError,133] - 
 异常信息 - {}
java.lang.NullPointerException: null
	at java.base/java.util.concurrent.ConcurrentHashMap.get(ConcurrentHashMap.java:936)
	at com.ruoyi.framework.websocket.WebSocketUsers.getSessionByUserId(WebSocketUsers.java:148)
	at com.ruoyi.framework.websocket.WebSocketServer.onMessage(WebSocketServer.java:175)
	at jdk.internal.reflect.GeneratedMethodAccessor114.invoke(Unknown Source)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:566)
	at org.apache.tomcat.websocket.pojo.PojoMessageHandlerWholeBase.onMessage(PojoMessageHandlerWholeBase.java:101)
	at org.apache.tomcat.websocket.WsFrameBase.sendMessageText(WsFrameBase.java:390)
	at org.apache.tomcat.websocket.server.WsFrameServer.sendMessageText(WsFrameServer.java:130)
	at org.apache.tomcat.websocket.WsFrameBase.processDataText(WsFrameBase.java:484)
	at org.apache.tomcat.websocket.WsFrameBase.processData(WsFrameBase.java:284)
	at org.apache.tomcat.websocket.WsFrameBase.processInputBuffer(WsFrameBase.java:130)
	at org.apache.tomcat.websocket.server.WsFrameServer.onDataAvailable(WsFrameServer.java:85)
	at org.apache.tomcat.websocket.server.WsFrameServer.doOnDataAvailable(WsFrameServer.java:184)
	at org.apache.tomcat.websocket.server.WsFrameServer.notifyDataAvailable(WsFrameServer.java:164)
	at org.apache.tomcat.websocket.server.WsHttpUpgradeHandler.upgradeDispatch(WsHttpUpgradeHandler.java:152)
	at org.apache.coyote.http11.upgrade.UpgradeProcessorInternal.dispatch(UpgradeProcessorInternal.java:60)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:57)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:928)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1794)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:52)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1191)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.base/java.lang.Thread.run(Thread.java:834)
14:04:58.817 [http-nio-9201-exec-2] INFO  c.r.f.w.WebSocketUsers - [remove,117] - 
 正在移出用户 - 5d
14:04:58.817 [http-nio-9201-exec-2] INFO  c.r.f.w.WebSocketUsers - [removeUserSessions,96] - 
 正在移出用户Session映射 - 100
14:05:03.851 [http-nio-9201-exec-1] INFO  c.r.f.w.WebSocketServer - [onOpen,100] - 
 建立连接 - org.apache.tomcat.websocket.WsSession@61b7eebb
14:05:03.851 [http-nio-9201-exec-1] INFO  c.r.f.w.WebSocketServer - [onOpen,101] - 
 当前人数 - 1
14:05:03.855 [http-nio-9201-exec-10] INFO  c.r.f.w.WebSocketServer - [onClose,113] - 
 关闭连接 - org.apache.tomcat.websocket.WsSession@61b7eebb
14:05:03.856 [http-nio-9201-exec-10] INFO  c.r.f.w.WebSocketUsers - [remove,117] - 
 正在移出用户 - 5e
14:05:03.856 [http-nio-9201-exec-10] INFO  c.r.f.w.WebSocketUsers - [remove,122] - 
 移出结果 - 成功
14:05:03.856 [http-nio-9201-exec-10] INFO  c.r.f.w.WebSocketServer - [onError,132] - 
 连接异常 - 5e
14:05:03.857 [http-nio-9201-exec-10] INFO  c.r.f.w.WebSocketServer - [onError,133] - 
 异常信息 - {}
java.lang.NullPointerException: null
	at java.base/java.util.concurrent.ConcurrentHashMap.get(ConcurrentHashMap.java:936)
	at com.ruoyi.framework.websocket.WebSocketUsers.getSessionByUserId(WebSocketUsers.java:148)
	at com.ruoyi.framework.websocket.WebSocketServer.onMessage(WebSocketServer.java:175)
	at jdk.internal.reflect.GeneratedMethodAccessor114.invoke(Unknown Source)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:566)
	at org.apache.tomcat.websocket.pojo.PojoMessageHandlerWholeBase.onMessage(PojoMessageHandlerWholeBase.java:101)
	at org.apache.tomcat.websocket.WsFrameBase.sendMessageText(WsFrameBase.java:390)
	at org.apache.tomcat.websocket.server.WsFrameServer.sendMessageText(WsFrameServer.java:130)
	at org.apache.tomcat.websocket.WsFrameBase.processDataText(WsFrameBase.java:484)
	at org.apache.tomcat.websocket.WsFrameBase.processData(WsFrameBase.java:284)
	at org.apache.tomcat.websocket.WsFrameBase.processInputBuffer(WsFrameBase.java:130)
	at org.apache.tomcat.websocket.server.WsFrameServer.onDataAvailable(WsFrameServer.java:85)
	at org.apache.tomcat.websocket.server.WsFrameServer.doOnDataAvailable(WsFrameServer.java:184)
	at org.apache.tomcat.websocket.server.WsFrameServer.notifyDataAvailable(WsFrameServer.java:164)
	at org.apache.tomcat.websocket.server.WsHttpUpgradeHandler.upgradeDispatch(WsHttpUpgradeHandler.java:152)
	at org.apache.coyote.http11.upgrade.UpgradeProcessorInternal.dispatch(UpgradeProcessorInternal.java:60)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:57)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:928)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1794)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:52)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1191)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.base/java.lang.Thread.run(Thread.java:834)
14:05:03.857 [http-nio-9201-exec-10] INFO  c.r.f.w.WebSocketUsers - [remove,117] - 
 正在移出用户 - 5e
14:05:03.857 [http-nio-9201-exec-10] INFO  c.r.f.w.WebSocketUsers - [removeUserSessions,96] - 
 正在移出用户Session映射 - 100
14:05:08.938 [http-nio-9201-exec-3] INFO  c.r.f.w.WebSocketServer - [onOpen,100] - 
 建立连接 - org.apache.tomcat.websocket.WsSession@37d0beee
14:05:08.938 [http-nio-9201-exec-3] INFO  c.r.f.w.WebSocketServer - [onOpen,101] - 
 当前人数 - 1
14:05:08.948 [http-nio-9201-exec-6] INFO  c.r.f.w.WebSocketServer - [onClose,113] - 
 关闭连接 - org.apache.tomcat.websocket.WsSession@37d0beee
14:05:08.949 [http-nio-9201-exec-6] INFO  c.r.f.w.WebSocketUsers - [remove,117] - 
 正在移出用户 - 5f
14:05:08.949 [http-nio-9201-exec-6] INFO  c.r.f.w.WebSocketUsers - [remove,122] - 
 移出结果 - 成功
14:05:08.949 [http-nio-9201-exec-6] INFO  c.r.f.w.WebSocketServer - [onError,132] - 
 连接异常 - 5f
14:05:08.950 [http-nio-9201-exec-6] INFO  c.r.f.w.WebSocketServer - [onError,133] - 
 异常信息 - {}
java.lang.NullPointerException: null
	at java.base/java.util.concurrent.ConcurrentHashMap.get(ConcurrentHashMap.java:936)
	at com.ruoyi.framework.websocket.WebSocketUsers.getSessionByUserId(WebSocketUsers.java:148)
	at com.ruoyi.framework.websocket.WebSocketServer.onMessage(WebSocketServer.java:175)
	at jdk.internal.reflect.GeneratedMethodAccessor114.invoke(Unknown Source)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:566)
	at org.apache.tomcat.websocket.pojo.PojoMessageHandlerWholeBase.onMessage(PojoMessageHandlerWholeBase.java:101)
	at org.apache.tomcat.websocket.WsFrameBase.sendMessageText(WsFrameBase.java:390)
	at org.apache.tomcat.websocket.server.WsFrameServer.sendMessageText(WsFrameServer.java:130)
	at org.apache.tomcat.websocket.WsFrameBase.processDataText(WsFrameBase.java:484)
	at org.apache.tomcat.websocket.WsFrameBase.processData(WsFrameBase.java:284)
	at org.apache.tomcat.websocket.WsFrameBase.processInputBuffer(WsFrameBase.java:130)
	at org.apache.tomcat.websocket.server.WsFrameServer.onDataAvailable(WsFrameServer.java:85)
	at org.apache.tomcat.websocket.server.WsFrameServer.doOnDataAvailable(WsFrameServer.java:184)
	at org.apache.tomcat.websocket.server.WsFrameServer.notifyDataAvailable(WsFrameServer.java:164)
	at org.apache.tomcat.websocket.server.WsHttpUpgradeHandler.upgradeDispatch(WsHttpUpgradeHandler.java:152)
	at org.apache.coyote.http11.upgrade.UpgradeProcessorInternal.dispatch(UpgradeProcessorInternal.java:60)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:57)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:928)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1794)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:52)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1191)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.base/java.lang.Thread.run(Thread.java:834)
14:05:08.950 [http-nio-9201-exec-6] INFO  c.r.f.w.WebSocketUsers - [remove,117] - 
 正在移出用户 - 5f
14:05:08.950 [http-nio-9201-exec-6] INFO  c.r.f.w.WebSocketUsers - [removeUserSessions,96] - 
 正在移出用户Session映射 - 100
14:05:13.989 [http-nio-9201-exec-4] INFO  c.r.f.w.WebSocketServer - [onOpen,100] - 
 建立连接 - org.apache.tomcat.websocket.WsSession@16c3668f
14:05:13.989 [http-nio-9201-exec-4] INFO  c.r.f.w.WebSocketServer - [onOpen,101] - 
 当前人数 - 1
14:05:13.994 [http-nio-9201-exec-5] INFO  c.r.f.w.WebSocketServer - [onClose,113] - 
 关闭连接 - org.apache.tomcat.websocket.WsSession@16c3668f
14:05:13.994 [http-nio-9201-exec-5] INFO  c.r.f.w.WebSocketUsers - [remove,117] - 
 正在移出用户 - 60
14:05:13.994 [http-nio-9201-exec-5] INFO  c.r.f.w.WebSocketUsers - [remove,122] - 
 移出结果 - 成功
14:05:13.995 [http-nio-9201-exec-5] INFO  c.r.f.w.WebSocketServer - [onError,132] - 
 连接异常 - 60
14:05:13.995 [http-nio-9201-exec-5] INFO  c.r.f.w.WebSocketServer - [onError,133] - 
 异常信息 - {}
java.lang.NullPointerException: null
	at java.base/java.util.concurrent.ConcurrentHashMap.get(ConcurrentHashMap.java:936)
	at com.ruoyi.framework.websocket.WebSocketUsers.getSessionByUserId(WebSocketUsers.java:148)
	at com.ruoyi.framework.websocket.WebSocketServer.onMessage(WebSocketServer.java:175)
	at jdk.internal.reflect.GeneratedMethodAccessor114.invoke(Unknown Source)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:566)
	at org.apache.tomcat.websocket.pojo.PojoMessageHandlerWholeBase.onMessage(PojoMessageHandlerWholeBase.java:101)
	at org.apache.tomcat.websocket.WsFrameBase.sendMessageText(WsFrameBase.java:390)
	at org.apache.tomcat.websocket.server.WsFrameServer.sendMessageText(WsFrameServer.java:130)
	at org.apache.tomcat.websocket.WsFrameBase.processDataText(WsFrameBase.java:484)
	at org.apache.tomcat.websocket.WsFrameBase.processData(WsFrameBase.java:284)
	at org.apache.tomcat.websocket.WsFrameBase.processInputBuffer(WsFrameBase.java:130)
	at org.apache.tomcat.websocket.server.WsFrameServer.onDataAvailable(WsFrameServer.java:85)
	at org.apache.tomcat.websocket.server.WsFrameServer.doOnDataAvailable(WsFrameServer.java:184)
	at org.apache.tomcat.websocket.server.WsFrameServer.notifyDataAvailable(WsFrameServer.java:164)
	at org.apache.tomcat.websocket.server.WsHttpUpgradeHandler.upgradeDispatch(WsHttpUpgradeHandler.java:152)
	at org.apache.coyote.http11.upgrade.UpgradeProcessorInternal.dispatch(UpgradeProcessorInternal.java:60)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:57)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:928)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1794)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:52)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1191)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.base/java.lang.Thread.run(Thread.java:834)
14:05:13.996 [http-nio-9201-exec-5] INFO  c.r.f.w.WebSocketUsers - [remove,117] - 
 正在移出用户 - 60
14:05:13.996 [http-nio-9201-exec-5] INFO  c.r.f.w.WebSocketUsers - [removeUserSessions,96] - 
 正在移出用户Session映射 - 100
14:05:19.025 [http-nio-9201-exec-8] INFO  c.r.f.w.WebSocketServer - [onOpen,100] - 
 建立连接 - org.apache.tomcat.websocket.WsSession@2b141db0
14:05:19.026 [http-nio-9201-exec-8] INFO  c.r.f.w.WebSocketServer - [onOpen,101] - 
 当前人数 - 1
14:05:19.029 [http-nio-9201-exec-7] INFO  c.r.f.w.WebSocketServer - [onClose,113] - 
 关闭连接 - org.apache.tomcat.websocket.WsSession@2b141db0
14:05:19.030 [http-nio-9201-exec-7] INFO  c.r.f.w.WebSocketUsers - [remove,117] - 
 正在移出用户 - 61
14:05:19.030 [http-nio-9201-exec-7] INFO  c.r.f.w.WebSocketUsers - [remove,122] - 
 移出结果 - 成功
14:05:19.030 [http-nio-9201-exec-7] INFO  c.r.f.w.WebSocketServer - [onError,132] - 
 连接异常 - 61
14:05:19.031 [http-nio-9201-exec-7] INFO  c.r.f.w.WebSocketServer - [onError,133] - 
 异常信息 - {}
java.lang.NullPointerException: null
	at java.base/java.util.concurrent.ConcurrentHashMap.get(ConcurrentHashMap.java:936)
	at com.ruoyi.framework.websocket.WebSocketUsers.getSessionByUserId(WebSocketUsers.java:148)
	at com.ruoyi.framework.websocket.WebSocketServer.onMessage(WebSocketServer.java:175)
	at jdk.internal.reflect.GeneratedMethodAccessor114.invoke(Unknown Source)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:566)
	at org.apache.tomcat.websocket.pojo.PojoMessageHandlerWholeBase.onMessage(PojoMessageHandlerWholeBase.java:101)
	at org.apache.tomcat.websocket.WsFrameBase.sendMessageText(WsFrameBase.java:390)
	at org.apache.tomcat.websocket.server.WsFrameServer.sendMessageText(WsFrameServer.java:130)
	at org.apache.tomcat.websocket.WsFrameBase.processDataText(WsFrameBase.java:484)
	at org.apache.tomcat.websocket.WsFrameBase.processData(WsFrameBase.java:284)
	at org.apache.tomcat.websocket.WsFrameBase.processInputBuffer(WsFrameBase.java:130)
	at org.apache.tomcat.websocket.server.WsFrameServer.onDataAvailable(WsFrameServer.java:85)
	at org.apache.tomcat.websocket.server.WsFrameServer.doOnDataAvailable(WsFrameServer.java:184)
	at org.apache.tomcat.websocket.server.WsFrameServer.notifyDataAvailable(WsFrameServer.java:164)
	at org.apache.tomcat.websocket.server.WsHttpUpgradeHandler.upgradeDispatch(WsHttpUpgradeHandler.java:152)
	at org.apache.coyote.http11.upgrade.UpgradeProcessorInternal.dispatch(UpgradeProcessorInternal.java:60)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:57)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:928)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1794)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:52)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1191)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.base/java.lang.Thread.run(Thread.java:834)
14:05:19.031 [http-nio-9201-exec-7] INFO  c.r.f.w.WebSocketUsers - [remove,117] - 
 正在移出用户 - 61
14:05:19.032 [http-nio-9201-exec-7] INFO  c.r.f.w.WebSocketUsers - [removeUserSessions,96] - 
 正在移出用户Session映射 - 100
14:05:24.089 [http-nio-9201-exec-9] INFO  c.r.f.w.WebSocketServer - [onOpen,100] - 
 建立连接 - org.apache.tomcat.websocket.WsSession@21c35482
14:05:24.091 [http-nio-9201-exec-9] INFO  c.r.f.w.WebSocketServer - [onOpen,101] - 
 当前人数 - 1
14:05:24.098 [http-nio-9201-exec-9] INFO  c.r.f.w.WebSocketServer - [onMessage,178] - 
 分享人通讯异常，无法处理 - 62
14:07:56.554 [http-nio-9201-exec-2] INFO  c.r.f.w.WebSocketServer - [onClose,113] - 
 关闭连接 - org.apache.tomcat.websocket.WsSession@21c35482
14:07:56.556 [http-nio-9201-exec-2] INFO  c.r.f.w.WebSocketUsers - [removeUserSessionsBySessionId,109] - 
 正在根据sessionId移出用户Session映射 - 62
14:07:56.557 [http-nio-9201-exec-2] INFO  c.r.f.w.WebSocketUsers - [removeUserSessionsBySessionId,122] - 
 移出用户Session映射结果 - 成功
14:07:56.557 [http-nio-9201-exec-2] INFO  c.r.f.w.WebSocketUsers - [remove,136] - 
 正在移出用户 - 62
14:07:56.557 [http-nio-9201-exec-2] INFO  c.r.f.w.WebSocketUsers - [remove,140] - 
 移出结果 - 成功
14:12:02.389 [http-nio-9201-exec-1] INFO  c.r.f.w.WebSocketServer - [onOpen,100] - 
 建立连接 - org.apache.tomcat.websocket.WsSession@7cb57a12
14:12:02.389 [http-nio-9201-exec-1] INFO  c.r.f.w.WebSocketServer - [onOpen,101] - 
 当前人数 - 1
14:12:02.396 [http-nio-9201-exec-10] INFO  c.r.f.w.WebSocketServer - [onMessage,182] - 
 分享人通讯异常，无法处理 - 63
14:12:12.710 [http-nio-9201-exec-6] INFO  c.r.f.w.WebSocketServer - [onClose,113] - 
 关闭连接 - org.apache.tomcat.websocket.WsSession@7cb57a12
14:12:12.710 [http-nio-9201-exec-6] INFO  c.r.f.w.WebSocketUsers - [removeUserSessionsBySessionId,109] - 
 正在根据sessionId移出用户Session映射 - 63
14:12:12.710 [http-nio-9201-exec-6] INFO  c.r.f.w.WebSocketUsers - [removeUserSessionsBySessionId,122] - 
 移出用户Session映射结果 - 成功
14:12:12.712 [http-nio-9201-exec-6] INFO  c.r.f.w.WebSocketUsers - [remove,136] - 
 正在移出用户 - 63
14:12:12.712 [http-nio-9201-exec-6] INFO  c.r.f.w.WebSocketUsers - [remove,140] - 
 移出结果 - 成功
14:12:17.770 [http-nio-9201-exec-4] INFO  c.r.f.w.WebSocketServer - [onOpen,100] - 
 建立连接 - org.apache.tomcat.websocket.WsSession@283c1a40
14:12:17.771 [http-nio-9201-exec-4] INFO  c.r.f.w.WebSocketServer - [onOpen,101] - 
 当前人数 - 1
14:12:17.773 [http-nio-9201-exec-5] INFO  c.r.f.w.WebSocketServer - [onMessage,182] - 
 分享人通讯异常，无法处理 - 64
14:12:41.560 [http-nio-9201-exec-10] INFO  c.r.f.t.WebSocketToTcpProxy - [onClose,50] - 📡 TBOX模拟器WebSocket连接关闭: 13
14:12:41.580 [pool-6-thread-3] INFO  c.r.f.t.TcpClientHandler - [closeConnection,153] - 🔌 TBOX客户端连接已关闭: /127.0.0.1:53987
14:12:41.776 [http-nio-9201-exec-6] INFO  c.r.f.t.WebSocketToTcpProxy - [onOpen,34] - 📡 TBOX模拟器WebSocket连接建立: 65
14:12:42.910 [http-nio-9201-exec-4] INFO  c.r.f.t.WebSocketToTcpProxy - [onMessage,79] - 📡 收到TBOX模拟器指令: {"action":"connect","host":"localhost","port":9999}
14:12:42.911 [http-nio-9201-exec-4] INFO  c.r.f.t.WebSocketToTcpProxy - [handleConnect,116] - 🔌 TBOX模拟器尝试连接TCP服务器: localhost:9999
14:12:42.914 [pool-6-thread-1] INFO  c.r.f.tcp.TcpServer - [acceptConnections,63] - 📱 新的TBOX客户端连接: /127.0.0.1:63022
14:12:42.915 [http-nio-9201-exec-4] INFO  c.r.f.t.WebSocketToTcpProxy - [handleConnect,127] - ✅ TBOX模拟器TCP连接建立成功
14:12:42.919 [pool-6-thread-3] INFO  c.r.f.t.TcpClientHandler - [run,49] - 🔄 开始处理TBOX客户端: /127.0.0.1:63022
14:12:58.803 [http-nio-9201-exec-5] INFO  c.r.f.w.WebSocketServer - [onClose,113] - 
 关闭连接 - org.apache.tomcat.websocket.WsSession@283c1a40
14:12:58.803 [http-nio-9201-exec-5] INFO  c.r.f.w.WebSocketUsers - [removeUserSessionsBySessionId,109] - 
 正在根据sessionId移出用户Session映射 - 64
14:12:58.803 [http-nio-9201-exec-5] INFO  c.r.f.w.WebSocketUsers - [removeUserSessionsBySessionId,122] - 
 移出用户Session映射结果 - 成功
14:12:58.803 [http-nio-9201-exec-5] INFO  c.r.f.w.WebSocketUsers - [remove,136] - 
 正在移出用户 - 64
14:12:58.803 [http-nio-9201-exec-5] INFO  c.r.f.w.WebSocketUsers - [remove,140] - 
 移出结果 - 成功
14:13:00.499 [http-nio-9201-exec-8] INFO  c.r.f.w.WebSocketServer - [onOpen,100] - 
 建立连接 - org.apache.tomcat.websocket.WsSession@5eb4d105
14:13:00.500 [http-nio-9201-exec-8] INFO  c.r.f.w.WebSocketServer - [onOpen,101] - 
 当前人数 - 1
14:13:00.508 [http-nio-9201-exec-7] INFO  c.r.f.w.WebSocketServer - [onMessage,182] - 
 分享人通讯异常，无法处理 - 66
14:13:22.457 [http-nio-9201-exec-1] INFO  c.r.f.w.WebSocketServer - [handle4GControlRequest,205] - 🚗 收到4G控车请求: lock
14:13:22.458 [http-nio-9201-exec-1] INFO  c.r.f.t.TcpControlService - [generateLockCommand,40] - 🔒 生成闭锁指令: 7E000A000A000102030405060708090A0000303330323030303103007E
14:13:22.464 [Thread-70] INFO  c.r.f.t.WebSocketToTcpProxy - [listenForMessages,230] - 📥 TBOX模拟器收到TCP消息: 7E000A000A000102030405060708090A0000303330323030303103007E
14:13:22.464 [http-nio-9201-exec-1] INFO  c.r.f.t.TcpClientHandler - [sendControlCommand,128] - 📤 发送控制指令给TBOX: 7E000A000A000102030405060708090A0000303330323030303103007E
14:13:22.464 [http-nio-9201-exec-1] INFO  c.r.f.t.TcpClientHandler - [sendCommandToAllTbox,172] - 📡 向所有TBOX客户端发送指令: 7E000A000A000102030405060708090A0000303330323030303103007E
14:13:22.465 [http-nio-9201-exec-1] INFO  c.r.f.t.TcpControlService - [sendLockCommandToAllTbox,107] - 🔒 已发送闭锁指令给所有TBOX设备
14:13:27.261 [http-nio-9201-exec-10] INFO  c.r.f.w.WebSocketServer - [handle4GControlRequest,205] - 🚗 收到4G控车请求: unlock
14:13:27.262 [http-nio-9201-exec-10] INFO  c.r.f.t.TcpControlService - [generateUnlockCommand,30] - 🔓 生成解锁指令: 7E000A000A000102030405060708090A0000303330323030303104007E
14:13:27.263 [Thread-70] INFO  c.r.f.t.WebSocketToTcpProxy - [listenForMessages,230] - 📥 TBOX模拟器收到TCP消息: 7E000A000A000102030405060708090A0000303330323030303104007E
14:13:27.263 [http-nio-9201-exec-10] INFO  c.r.f.t.TcpClientHandler - [sendControlCommand,128] - 📤 发送控制指令给TBOX: 7E000A000A000102030405060708090A0000303330323030303104007E
14:13:27.263 [http-nio-9201-exec-10] INFO  c.r.f.t.TcpClientHandler - [sendCommandToAllTbox,172] - 📡 向所有TBOX客户端发送指令: 7E000A000A000102030405060708090A0000303330323030303104007E
14:13:27.263 [http-nio-9201-exec-10] INFO  c.r.f.t.TcpControlService - [sendUnlockCommandToAllTbox,98] - 🔓 已发送解锁指令给所有TBOX设备
14:13:34.051 [http-nio-9201-exec-3] INFO  c.r.f.w.WebSocketServer - [handle4GControlRequest,205] - 🚗 收到4G控车请求: lock
14:13:34.052 [http-nio-9201-exec-3] INFO  c.r.f.t.TcpControlService - [generateLockCommand,40] - 🔒 生成闭锁指令: 7E000A000A000102030405060708090A0000303330323030303103007E
14:13:34.052 [Thread-70] INFO  c.r.f.t.WebSocketToTcpProxy - [listenForMessages,230] - 📥 TBOX模拟器收到TCP消息: 7E000A000A000102030405060708090A0000303330323030303103007E
14:13:34.052 [http-nio-9201-exec-3] INFO  c.r.f.t.TcpClientHandler - [sendControlCommand,128] - 📤 发送控制指令给TBOX: 7E000A000A000102030405060708090A0000303330323030303103007E
14:13:34.052 [http-nio-9201-exec-3] INFO  c.r.f.t.TcpClientHandler - [sendCommandToAllTbox,172] - 📡 向所有TBOX客户端发送指令: 7E000A000A000102030405060708090A0000303330323030303103007E
14:13:34.052 [http-nio-9201-exec-3] INFO  c.r.f.t.TcpControlService - [sendLockCommandToAllTbox,107] - 🔒 已发送闭锁指令给所有TBOX设备
14:13:38.055 [http-nio-9201-exec-6] INFO  c.r.f.t.WebSocketToTcpProxy - [onMessage,79] - 📡 收到TBOX模拟器指令: {"action":"status"}
14:13:38.056 [http-nio-9201-exec-6] INFO  c.r.f.t.WebSocketToTcpProxy - [sendMessage,223] - 📤 TBOX模拟器发送TCP消息: 7E000A000A000102030405060708090A000030333032303030313034007E
14:13:38.056 [pool-6-thread-3] INFO  c.r.f.t.TcpClientHandler - [run,54] - 📨 收到TBOX消息: 7E000A000A000102030405060708090A000030333032303030313034007E from /127.0.0.1:63022
14:13:38.057 [http-nio-9201-exec-6] INFO  c.r.f.t.WebSocketToTcpProxy - [handleStatusReport,168] - 📊 TBOX模拟器发送状态报告
14:13:38.057 [pool-6-thread-3] INFO  c.r.f.t.TcpClientHandler - [handleTboxMessage,89] - 🚗 车辆状态更新: locked (车辆已闭锁)
14:13:38.058 [pool-6-thread-3] INFO  c.r.f.t.TcpClientHandler - [notifyMobileApp,115] - 📤 已通知手机端车辆状态: 车辆已闭锁
14:13:41.581 [http-nio-9201-exec-4] INFO  c.r.f.t.WebSocketToTcpProxy - [onMessage,79] - 📡 收到TBOX模拟器指令: {"action":"status"}
14:13:41.582 [pool-6-thread-3] INFO  c.r.f.t.TcpClientHandler - [run,54] - 📨 收到TBOX消息: 7E000A000A000102030405060708090A000030333032303030313034007E from /127.0.0.1:63022
14:13:41.582 [http-nio-9201-exec-4] INFO  c.r.f.t.WebSocketToTcpProxy - [sendMessage,223] - 📤 TBOX模拟器发送TCP消息: 7E000A000A000102030405060708090A000030333032303030313034007E
14:13:41.583 [http-nio-9201-exec-4] INFO  c.r.f.t.WebSocketToTcpProxy - [handleStatusReport,168] - 📊 TBOX模拟器发送状态报告
14:13:41.583 [pool-6-thread-3] INFO  c.r.f.t.TcpClientHandler - [handleTboxMessage,89] - 🚗 车辆状态更新: locked (车辆已闭锁)
14:13:41.583 [pool-6-thread-3] INFO  c.r.f.t.TcpClientHandler - [notifyMobileApp,115] - 📤 已通知手机端车辆状态: 车辆已闭锁
14:13:47.134 [http-nio-9201-exec-7] INFO  c.r.f.w.WebSocketServer - [handle4GControlRequest,205] - 🚗 收到4G控车请求: unlock
14:13:47.134 [http-nio-9201-exec-7] INFO  c.r.f.t.TcpControlService - [generateUnlockCommand,30] - 🔓 生成解锁指令: 7E000A000A000102030405060708090A0000303330323030303104007E
14:13:47.135 [Thread-70] INFO  c.r.f.t.WebSocketToTcpProxy - [listenForMessages,230] - 📥 TBOX模拟器收到TCP消息: 7E000A000A000102030405060708090A0000303330323030303104007E
14:13:47.135 [http-nio-9201-exec-7] INFO  c.r.f.t.TcpClientHandler - [sendControlCommand,128] - 📤 发送控制指令给TBOX: 7E000A000A000102030405060708090A0000303330323030303104007E
14:13:47.135 [http-nio-9201-exec-7] INFO  c.r.f.t.TcpClientHandler - [sendCommandToAllTbox,172] - 📡 向所有TBOX客户端发送指令: 7E000A000A000102030405060708090A0000303330323030303104007E
14:13:47.135 [http-nio-9201-exec-7] INFO  c.r.f.t.TcpControlService - [sendUnlockCommandToAllTbox,98] - 🔓 已发送解锁指令给所有TBOX设备
14:13:57.559 [http-nio-9201-exec-9] INFO  c.r.f.t.WebSocketToTcpProxy - [onMessage,79] - 📡 收到TBOX模拟器指令: {"action":"status"}
14:13:57.559 [http-nio-9201-exec-9] INFO  c.r.f.t.WebSocketToTcpProxy - [sendMessage,223] - 📤 TBOX模拟器发送TCP消息: 7E000A000A000102030405060708090A000030333032303030313034007E
14:13:57.560 [http-nio-9201-exec-9] INFO  c.r.f.t.WebSocketToTcpProxy - [handleStatusReport,168] - 📊 TBOX模拟器发送状态报告
14:13:57.559 [pool-6-thread-3] INFO  c.r.f.t.TcpClientHandler - [run,54] - 📨 收到TBOX消息: 7E000A000A000102030405060708090A000030333032303030313034007E from /127.0.0.1:63022
14:13:57.560 [pool-6-thread-3] INFO  c.r.f.t.TcpClientHandler - [handleTboxMessage,89] - 🚗 车辆状态更新: locked (车辆已闭锁)
14:13:57.560 [pool-6-thread-3] INFO  c.r.f.t.TcpClientHandler - [notifyMobileApp,115] - 📤 已通知手机端车辆状态: 车辆已闭锁
14:14:07.695 [http-nio-9201-exec-1] INFO  c.r.f.w.WebSocketServer - [onClose,113] - 
 关闭连接 - org.apache.tomcat.websocket.WsSession@5eb4d105
14:14:07.696 [http-nio-9201-exec-1] INFO  c.r.f.w.WebSocketUsers - [removeUserSessionsBySessionId,109] - 
 正在根据sessionId移出用户Session映射 - 66
14:14:07.696 [http-nio-9201-exec-1] INFO  c.r.f.w.WebSocketUsers - [removeUserSessionsBySessionId,122] - 
 移出用户Session映射结果 - 成功
14:14:07.697 [http-nio-9201-exec-1] INFO  c.r.f.w.WebSocketUsers - [remove,136] - 
 正在移出用户 - 66
14:14:07.697 [http-nio-9201-exec-1] INFO  c.r.f.w.WebSocketUsers - [remove,140] - 
 移出结果 - 成功
14:14:09.763 [http-nio-9201-exec-10] INFO  c.r.f.w.WebSocketServer - [onOpen,100] - 
 建立连接 - org.apache.tomcat.websocket.WsSession@18963cd4
14:14:09.764 [http-nio-9201-exec-10] INFO  c.r.f.w.WebSocketServer - [onOpen,101] - 
 当前人数 - 1
14:14:09.816 [http-nio-9201-exec-6] INFO  c.r.f.w.WebSocketServer - [onMessage,182] - 
 分享人通讯异常，无法处理 - 67
14:16:49.517 [http-nio-9201-exec-5] INFO  c.r.f.w.WebSocketServer - [onClose,113] - 
 关闭连接 - org.apache.tomcat.websocket.WsSession@18963cd4
14:16:49.517 [http-nio-9201-exec-5] INFO  c.r.f.w.WebSocketUsers - [removeUserSessionsBySessionId,109] - 
 正在根据sessionId移出用户Session映射 - 67
14:16:49.517 [http-nio-9201-exec-5] INFO  c.r.f.w.WebSocketUsers - [removeUserSessionsBySessionId,122] - 
 移出用户Session映射结果 - 成功
14:16:49.517 [http-nio-9201-exec-5] INFO  c.r.f.w.WebSocketUsers - [remove,136] - 
 正在移出用户 - 67
14:16:49.518 [http-nio-9201-exec-5] INFO  c.r.f.w.WebSocketUsers - [remove,140] - 
 移出结果 - 成功
14:16:51.987 [http-nio-9201-exec-8] INFO  c.r.f.w.WebSocketServer - [onOpen,100] - 
 建立连接 - org.apache.tomcat.websocket.WsSession@18ad6599
14:16:51.987 [http-nio-9201-exec-8] INFO  c.r.f.w.WebSocketServer - [onOpen,101] - 
 当前人数 - 1
14:16:52.031 [http-nio-9201-exec-9] INFO  c.r.f.w.WebSocketServer - [onMessage,182] - 
 分享人通讯异常，无法处理 - 68
14:17:10.141 [http-nio-9201-exec-2] INFO  c.r.f.w.WebSocketServer - [onClose,113] - 
 关闭连接 - org.apache.tomcat.websocket.WsSession@18ad6599
14:17:10.142 [http-nio-9201-exec-2] INFO  c.r.f.w.WebSocketUsers - [removeUserSessionsBySessionId,109] - 
 正在根据sessionId移出用户Session映射 - 68
14:17:10.142 [http-nio-9201-exec-2] INFO  c.r.f.w.WebSocketUsers - [removeUserSessionsBySessionId,122] - 
 移出用户Session映射结果 - 成功
14:17:10.142 [http-nio-9201-exec-2] INFO  c.r.f.w.WebSocketUsers - [remove,136] - 
 正在移出用户 - 68
14:17:10.142 [http-nio-9201-exec-2] INFO  c.r.f.w.WebSocketUsers - [remove,140] - 
 移出结果 - 成功
14:17:12.171 [http-nio-9201-exec-1] INFO  c.r.f.w.WebSocketServer - [onOpen,100] - 
 建立连接 - org.apache.tomcat.websocket.WsSession@40fc461
14:17:12.171 [http-nio-9201-exec-1] INFO  c.r.f.w.WebSocketServer - [onOpen,101] - 
 当前人数 - 1
14:17:12.196 [http-nio-9201-exec-6] INFO  c.r.f.w.WebSocketServer - [onMessage,182] - 
 分享人通讯异常，无法处理 - 69
14:17:19.733 [http-nio-9201-exec-3] INFO  c.r.f.w.WebSocketServer - [onClose,113] - 
 关闭连接 - org.apache.tomcat.websocket.WsSession@40fc461
14:17:19.733 [http-nio-9201-exec-3] INFO  c.r.f.w.WebSocketUsers - [removeUserSessionsBySessionId,109] - 
 正在根据sessionId移出用户Session映射 - 69
14:17:19.734 [http-nio-9201-exec-3] INFO  c.r.f.w.WebSocketUsers - [removeUserSessionsBySessionId,122] - 
 移出用户Session映射结果 - 成功
14:17:19.734 [http-nio-9201-exec-3] INFO  c.r.f.w.WebSocketUsers - [remove,136] - 
 正在移出用户 - 69
14:17:19.734 [http-nio-9201-exec-3] INFO  c.r.f.w.WebSocketUsers - [remove,140] - 
 移出结果 - 成功
14:17:21.105 [http-nio-9201-exec-4] INFO  c.r.f.w.WebSocketServer - [onOpen,100] - 
 建立连接 - org.apache.tomcat.websocket.WsSession@4552a078
14:17:21.106 [http-nio-9201-exec-4] INFO  c.r.f.w.WebSocketServer - [onOpen,101] - 
 当前人数 - 1
14:17:21.115 [http-nio-9201-exec-5] INFO  c.r.f.w.WebSocketServer - [onMessage,182] - 
 分享人通讯异常，无法处理 - 6a
14:17:31.742 [http-nio-9201-exec-9] INFO  c.r.f.w.WebSocketServer - [handle4GControlRequest,205] - 🚗 收到4G控车请求: lock
14:17:31.743 [http-nio-9201-exec-9] INFO  c.r.f.t.TcpControlService - [generateLockCommand,40] - 🔒 生成闭锁指令: 7E000A000A000102030405060708090A0000303330323030303103007E
14:17:31.743 [Thread-70] INFO  c.r.f.t.WebSocketToTcpProxy - [listenForMessages,230] - 📥 TBOX模拟器收到TCP消息: 7E000A000A000102030405060708090A0000303330323030303103007E
14:17:31.743 [http-nio-9201-exec-9] INFO  c.r.f.t.TcpClientHandler - [sendControlCommand,128] - 📤 发送控制指令给TBOX: 7E000A000A000102030405060708090A0000303330323030303103007E
14:17:31.744 [http-nio-9201-exec-9] INFO  c.r.f.t.TcpClientHandler - [sendCommandToAllTbox,172] - 📡 向所有TBOX客户端发送指令: 7E000A000A000102030405060708090A0000303330323030303103007E
14:17:31.744 [http-nio-9201-exec-9] INFO  c.r.f.t.TcpControlService - [sendLockCommandToAllTbox,107] - 🔒 已发送闭锁指令给所有TBOX设备
14:17:34.409 [http-nio-9201-exec-7] INFO  c.r.f.w.WebSocketServer - [handle4GControlRequest,205] - 🚗 收到4G控车请求: unlock
14:17:34.410 [http-nio-9201-exec-7] INFO  c.r.f.t.TcpControlService - [generateUnlockCommand,30] - 🔓 生成解锁指令: 7E000A000A000102030405060708090A0000303330323030303104007E
14:17:34.410 [http-nio-9201-exec-7] INFO  c.r.f.t.TcpClientHandler - [sendControlCommand,128] - 📤 发送控制指令给TBOX: 7E000A000A000102030405060708090A0000303330323030303104007E
14:17:34.410 [Thread-70] INFO  c.r.f.t.WebSocketToTcpProxy - [listenForMessages,230] - 📥 TBOX模拟器收到TCP消息: 7E000A000A000102030405060708090A0000303330323030303104007E
14:17:34.410 [http-nio-9201-exec-7] INFO  c.r.f.t.TcpClientHandler - [sendCommandToAllTbox,172] - 📡 向所有TBOX客户端发送指令: 7E000A000A000102030405060708090A0000303330323030303104007E
14:17:34.410 [http-nio-9201-exec-7] INFO  c.r.f.t.TcpControlService - [sendUnlockCommandToAllTbox,98] - 🔓 已发送解锁指令给所有TBOX设备
14:17:43.186 [http-nio-9201-exec-2] INFO  c.r.f.w.WebSocketServer - [onClose,113] - 
 关闭连接 - org.apache.tomcat.websocket.WsSession@4552a078
14:17:43.187 [http-nio-9201-exec-2] INFO  c.r.f.w.WebSocketUsers - [removeUserSessionsBySessionId,109] - 
 正在根据sessionId移出用户Session映射 - 6a
14:17:43.187 [http-nio-9201-exec-2] INFO  c.r.f.w.WebSocketUsers - [removeUserSessionsBySessionId,122] - 
 移出用户Session映射结果 - 成功
14:17:43.187 [http-nio-9201-exec-2] INFO  c.r.f.w.WebSocketUsers - [remove,136] - 
 正在移出用户 - 6a
14:17:43.187 [http-nio-9201-exec-2] INFO  c.r.f.w.WebSocketUsers - [remove,140] - 
 移出结果 - 成功
14:17:44.768 [http-nio-9201-exec-1] INFO  c.r.f.w.WebSocketServer - [onOpen,100] - 
 建立连接 - org.apache.tomcat.websocket.WsSession@23915378
14:17:44.768 [http-nio-9201-exec-1] INFO  c.r.f.w.WebSocketServer - [onOpen,101] - 
 当前人数 - 1
14:17:44.779 [http-nio-9201-exec-6] INFO  c.r.f.w.WebSocketServer - [onMessage,182] - 
 分享人通讯异常，无法处理 - 6b
14:17:56.692 [http-nio-9201-exec-3] INFO  c.r.f.t.WebSocketToTcpProxy - [onMessage,79] - 📡 收到TBOX模拟器指令: {"action":"status"}
14:17:56.693 [pool-6-thread-3] INFO  c.r.f.t.TcpClientHandler - [run,54] - 📨 收到TBOX消息: 7E000A000A000102030405060708090A000030333032303030313034007E from /127.0.0.1:63022
14:17:56.693 [http-nio-9201-exec-3] INFO  c.r.f.t.WebSocketToTcpProxy - [sendMessage,223] - 📤 TBOX模拟器发送TCP消息: 7E000A000A000102030405060708090A000030333032303030313034007E
14:17:56.693 [pool-6-thread-3] INFO  c.r.f.t.TcpClientHandler - [handleTboxMessage,89] - 🚗 车辆状态更新: locked (车辆已闭锁)
14:17:56.694 [http-nio-9201-exec-3] INFO  c.r.f.t.WebSocketToTcpProxy - [handleStatusReport,168] - 📊 TBOX模拟器发送状态报告
14:17:56.694 [pool-6-thread-3] INFO  c.r.f.t.TcpClientHandler - [notifyMobileApp,115] - 📤 已通知手机端车辆状态: 车辆已闭锁
14:18:00.958 [http-nio-9201-exec-4] INFO  c.r.f.w.WebSocketServer - [handle4GControlRequest,205] - 🚗 收到4G控车请求: unlock
14:18:00.958 [http-nio-9201-exec-4] INFO  c.r.f.t.TcpControlService - [generateUnlockCommand,30] - 🔓 生成解锁指令: 7E000A000A000102030405060708090A0000303330323030303104007E
14:18:00.960 [http-nio-9201-exec-4] INFO  c.r.f.t.TcpClientHandler - [sendControlCommand,128] - 📤 发送控制指令给TBOX: 7E000A000A000102030405060708090A0000303330323030303104007E
14:18:00.960 [Thread-70] INFO  c.r.f.t.WebSocketToTcpProxy - [listenForMessages,230] - 📥 TBOX模拟器收到TCP消息: 7E000A000A000102030405060708090A0000303330323030303104007E
14:18:00.960 [http-nio-9201-exec-4] INFO  c.r.f.t.TcpClientHandler - [sendCommandToAllTbox,172] - 📡 向所有TBOX客户端发送指令: 7E000A000A000102030405060708090A0000303330323030303104007E
14:18:00.960 [http-nio-9201-exec-4] INFO  c.r.f.t.TcpControlService - [sendUnlockCommandToAllTbox,98] - 🔓 已发送解锁指令给所有TBOX设备
14:18:02.024 [http-nio-9201-exec-5] INFO  c.r.f.t.WebSocketToTcpProxy - [onMessage,79] - 📡 收到TBOX模拟器指令: {"action":"status"}
14:18:02.024 [http-nio-9201-exec-5] INFO  c.r.f.t.WebSocketToTcpProxy - [sendMessage,223] - 📤 TBOX模拟器发送TCP消息: 7E000A000A000102030405060708090A000030333032303030313034007E
14:18:02.024 [pool-6-thread-3] INFO  c.r.f.t.TcpClientHandler - [run,54] - 📨 收到TBOX消息: 7E000A000A000102030405060708090A000030333032303030313034007E from /127.0.0.1:63022
14:18:02.024 [pool-6-thread-3] INFO  c.r.f.t.TcpClientHandler - [handleTboxMessage,89] - 🚗 车辆状态更新: locked (车辆已闭锁)
14:18:02.025 [http-nio-9201-exec-5] INFO  c.r.f.t.WebSocketToTcpProxy - [handleStatusReport,168] - 📊 TBOX模拟器发送状态报告
14:18:02.025 [pool-6-thread-3] INFO  c.r.f.t.TcpClientHandler - [notifyMobileApp,115] - 📤 已通知手机端车辆状态: 车辆已闭锁
14:18:04.996 [http-nio-9201-exec-8] INFO  c.r.f.t.WebSocketToTcpProxy - [onMessage,79] - 📡 收到TBOX模拟器指令: {"action":"status"}
14:18:04.997 [pool-6-thread-3] INFO  c.r.f.t.TcpClientHandler - [run,54] - 📨 收到TBOX消息: 7E000A000A000102030405060708090A000030333032303030313034007E from /127.0.0.1:63022
14:18:04.997 [http-nio-9201-exec-8] INFO  c.r.f.t.WebSocketToTcpProxy - [sendMessage,223] - 📤 TBOX模拟器发送TCP消息: 7E000A000A000102030405060708090A000030333032303030313034007E
14:18:04.997 [pool-6-thread-3] INFO  c.r.f.t.TcpClientHandler - [handleTboxMessage,89] - 🚗 车辆状态更新: locked (车辆已闭锁)
14:18:04.998 [pool-6-thread-3] INFO  c.r.f.t.TcpClientHandler - [notifyMobileApp,115] - 📤 已通知手机端车辆状态: 车辆已闭锁
14:18:04.998 [http-nio-9201-exec-8] INFO  c.r.f.t.WebSocketToTcpProxy - [handleStatusReport,168] - 📊 TBOX模拟器发送状态报告
14:18:06.377 [http-nio-9201-exec-9] INFO  c.r.f.w.WebSocketServer - [handle4GControlRequest,205] - 🚗 收到4G控车请求: unlock
14:18:06.377 [http-nio-9201-exec-9] INFO  c.r.f.t.TcpControlService - [generateUnlockCommand,30] - 🔓 生成解锁指令: 7E000A000A000102030405060708090A0000303330323030303104007E
14:18:06.378 [Thread-70] INFO  c.r.f.t.WebSocketToTcpProxy - [listenForMessages,230] - 📥 TBOX模拟器收到TCP消息: 7E000A000A000102030405060708090A0000303330323030303104007E
14:18:06.378 [http-nio-9201-exec-9] INFO  c.r.f.t.TcpClientHandler - [sendControlCommand,128] - 📤 发送控制指令给TBOX: 7E000A000A000102030405060708090A0000303330323030303104007E
14:18:06.378 [http-nio-9201-exec-9] INFO  c.r.f.t.TcpClientHandler - [sendCommandToAllTbox,172] - 📡 向所有TBOX客户端发送指令: 7E000A000A000102030405060708090A0000303330323030303104007E
14:18:06.378 [http-nio-9201-exec-9] INFO  c.r.f.t.TcpControlService - [sendUnlockCommandToAllTbox,98] - 🔓 已发送解锁指令给所有TBOX设备
14:18:08.430 [http-nio-9201-exec-7] INFO  c.r.f.w.WebSocketServer - [handle4GControlRequest,205] - 🚗 收到4G控车请求: lock
14:18:08.430 [http-nio-9201-exec-7] INFO  c.r.f.t.TcpControlService - [generateLockCommand,40] - 🔒 生成闭锁指令: 7E000A000A000102030405060708090A0000303330323030303103007E
14:18:08.430 [http-nio-9201-exec-7] INFO  c.r.f.t.TcpClientHandler - [sendControlCommand,128] - 📤 发送控制指令给TBOX: 7E000A000A000102030405060708090A0000303330323030303103007E
14:18:08.430 [Thread-70] INFO  c.r.f.t.WebSocketToTcpProxy - [listenForMessages,230] - 📥 TBOX模拟器收到TCP消息: 7E000A000A000102030405060708090A0000303330323030303103007E
14:18:08.430 [http-nio-9201-exec-7] INFO  c.r.f.t.TcpClientHandler - [sendCommandToAllTbox,172] - 📡 向所有TBOX客户端发送指令: 7E000A000A000102030405060708090A0000303330323030303103007E
14:18:08.431 [http-nio-9201-exec-7] INFO  c.r.f.t.TcpControlService - [sendLockCommandToAllTbox,107] - 🔒 已发送闭锁指令给所有TBOX设备
14:18:11.086 [http-nio-9201-exec-2] INFO  c.r.f.t.WebSocketToTcpProxy - [onMessage,79] - 📡 收到TBOX模拟器指令: {"action":"status"}
14:18:11.087 [http-nio-9201-exec-2] INFO  c.r.f.t.WebSocketToTcpProxy - [sendMessage,223] - 📤 TBOX模拟器发送TCP消息: 7E000A000A000102030405060708090A000030333032303030313034007E
14:18:11.087 [pool-6-thread-3] INFO  c.r.f.t.TcpClientHandler - [run,54] - 📨 收到TBOX消息: 7E000A000A000102030405060708090A000030333032303030313034007E from /127.0.0.1:63022
14:18:11.087 [pool-6-thread-3] INFO  c.r.f.t.TcpClientHandler - [handleTboxMessage,89] - 🚗 车辆状态更新: locked (车辆已闭锁)
14:18:11.087 [pool-6-thread-3] INFO  c.r.f.t.TcpClientHandler - [notifyMobileApp,115] - 📤 已通知手机端车辆状态: 车辆已闭锁
14:18:11.087 [http-nio-9201-exec-2] INFO  c.r.f.t.WebSocketToTcpProxy - [handleStatusReport,168] - 📊 TBOX模拟器发送状态报告
14:18:11.805 [http-nio-9201-exec-1] INFO  c.r.f.t.WebSocketToTcpProxy - [onMessage,79] - 📡 收到TBOX模拟器指令: {"action":"status"}
14:18:11.805 [http-nio-9201-exec-1] INFO  c.r.f.t.WebSocketToTcpProxy - [sendMessage,223] - 📤 TBOX模拟器发送TCP消息: 7E000A000A000102030405060708090A000030333032303030313034007E
14:18:11.805 [pool-6-thread-3] INFO  c.r.f.t.TcpClientHandler - [run,54] - 📨 收到TBOX消息: 7E000A000A000102030405060708090A000030333032303030313034007E from /127.0.0.1:63022
14:18:11.806 [pool-6-thread-3] INFO  c.r.f.t.TcpClientHandler - [handleTboxMessage,89] - 🚗 车辆状态更新: locked (车辆已闭锁)
14:18:11.806 [pool-6-thread-3] INFO  c.r.f.t.TcpClientHandler - [notifyMobileApp,115] - 📤 已通知手机端车辆状态: 车辆已闭锁
14:18:11.806 [http-nio-9201-exec-1] INFO  c.r.f.t.WebSocketToTcpProxy - [handleStatusReport,168] - 📊 TBOX模拟器发送状态报告
14:18:12.805 [http-nio-9201-exec-6] INFO  c.r.f.w.WebSocketServer - [handle4GControlRequest,205] - 🚗 收到4G控车请求: unlock
14:18:12.805 [http-nio-9201-exec-6] INFO  c.r.f.t.TcpControlService - [generateUnlockCommand,30] - 🔓 生成解锁指令: 7E000A000A000102030405060708090A0000303330323030303104007E
14:18:12.806 [Thread-70] INFO  c.r.f.t.WebSocketToTcpProxy - [listenForMessages,230] - 📥 TBOX模拟器收到TCP消息: 7E000A000A000102030405060708090A0000303330323030303104007E
14:18:12.806 [http-nio-9201-exec-6] INFO  c.r.f.t.TcpClientHandler - [sendControlCommand,128] - 📤 发送控制指令给TBOX: 7E000A000A000102030405060708090A0000303330323030303104007E
14:18:12.806 [http-nio-9201-exec-6] INFO  c.r.f.t.TcpClientHandler - [sendCommandToAllTbox,172] - 📡 向所有TBOX客户端发送指令: 7E000A000A000102030405060708090A0000303330323030303104007E
14:18:12.806 [http-nio-9201-exec-6] INFO  c.r.f.t.TcpControlService - [sendUnlockCommandToAllTbox,98] - 🔓 已发送解锁指令给所有TBOX设备
14:18:13.694 [http-nio-9201-exec-10] INFO  c.r.f.t.WebSocketToTcpProxy - [onMessage,79] - 📡 收到TBOX模拟器指令: {"action":"status"}
14:18:13.695 [pool-6-thread-3] INFO  c.r.f.t.TcpClientHandler - [run,54] - 📨 收到TBOX消息: 7E000A000A000102030405060708090A000030333032303030313034007E from /127.0.0.1:63022
14:18:13.695 [http-nio-9201-exec-10] INFO  c.r.f.t.WebSocketToTcpProxy - [sendMessage,223] - 📤 TBOX模拟器发送TCP消息: 7E000A000A000102030405060708090A000030333032303030313034007E
14:18:13.695 [pool-6-thread-3] INFO  c.r.f.t.TcpClientHandler - [handleTboxMessage,89] - 🚗 车辆状态更新: locked (车辆已闭锁)
14:18:13.696 [pool-6-thread-3] INFO  c.r.f.t.TcpClientHandler - [notifyMobileApp,115] - 📤 已通知手机端车辆状态: 车辆已闭锁
14:18:13.696 [http-nio-9201-exec-10] INFO  c.r.f.t.WebSocketToTcpProxy - [handleStatusReport,168] - 📊 TBOX模拟器发送状态报告
14:18:31.467 [http-nio-9201-exec-3] INFO  c.r.f.w.WebSocketServer - [onClose,113] - 
 关闭连接 - org.apache.tomcat.websocket.WsSession@23915378
14:18:31.467 [http-nio-9201-exec-3] INFO  c.r.f.w.WebSocketUsers - [removeUserSessionsBySessionId,109] - 
 正在根据sessionId移出用户Session映射 - 6b
14:18:31.468 [http-nio-9201-exec-3] INFO  c.r.f.w.WebSocketUsers - [removeUserSessionsBySessionId,122] - 
 移出用户Session映射结果 - 成功
14:18:31.468 [http-nio-9201-exec-3] INFO  c.r.f.w.WebSocketUsers - [remove,136] - 
 正在移出用户 - 6b
14:18:31.468 [http-nio-9201-exec-3] INFO  c.r.f.w.WebSocketUsers - [remove,140] - 
 移出结果 - 成功
14:18:33.505 [http-nio-9201-exec-4] INFO  c.r.f.w.WebSocketServer - [onOpen,100] - 
 建立连接 - org.apache.tomcat.websocket.WsSession@3a2606d0
14:18:33.506 [http-nio-9201-exec-4] INFO  c.r.f.w.WebSocketServer - [onOpen,101] - 
 当前人数 - 1
14:18:33.520 [http-nio-9201-exec-5] INFO  c.r.f.w.WebSocketServer - [onMessage,182] - 
 分享人通讯异常，无法处理 - 6c
14:20:02.169 [http-nio-9201-exec-7] INFO  c.r.f.w.WebSocketServer - [onClose,113] - 
 关闭连接 - org.apache.tomcat.websocket.WsSession@3a2606d0
14:20:02.170 [http-nio-9201-exec-7] INFO  c.r.f.w.WebSocketUsers - [removeUserSessionsBySessionId,109] - 
 正在根据sessionId移出用户Session映射 - 6c
14:20:02.170 [http-nio-9201-exec-7] INFO  c.r.f.w.WebSocketUsers - [removeUserSessionsBySessionId,122] - 
 移出用户Session映射结果 - 成功
14:20:02.170 [http-nio-9201-exec-7] INFO  c.r.f.w.WebSocketUsers - [remove,136] - 
 正在移出用户 - 6c
14:20:02.170 [http-nio-9201-exec-7] INFO  c.r.f.w.WebSocketUsers - [remove,140] - 
 移出结果 - 成功
14:20:03.896 [http-nio-9201-exec-2] INFO  c.r.f.w.WebSocketServer - [onOpen,100] - 
 建立连接 - org.apache.tomcat.websocket.WsSession@3cea58b5
14:20:03.896 [http-nio-9201-exec-2] INFO  c.r.f.w.WebSocketServer - [onOpen,101] - 
 当前人数 - 1
14:20:03.901 [http-nio-9201-exec-1] INFO  c.r.f.w.WebSocketServer - [onMessage,182] - 
 分享人通讯异常，无法处理 - 6d
14:20:23.870 [http-nio-9201-exec-10] INFO  c.r.f.w.WebSocketServer - [handle4GControlRequest,205] - 🚗 收到4G控车请求: lock
14:20:23.871 [http-nio-9201-exec-10] INFO  c.r.f.t.TcpControlService - [generateLockCommand,40] - 🔒 生成闭锁指令: 7E000A000A000102030405060708090A0000303330323030303103007E
14:20:23.871 [http-nio-9201-exec-10] INFO  c.r.f.t.TcpClientHandler - [sendControlCommand,128] - 📤 发送控制指令给TBOX: 7E000A000A000102030405060708090A0000303330323030303103007E
14:20:23.871 [Thread-70] INFO  c.r.f.t.WebSocketToTcpProxy - [listenForMessages,230] - 📥 TBOX模拟器收到TCP消息: 7E000A000A000102030405060708090A0000303330323030303103007E
14:20:23.871 [http-nio-9201-exec-10] INFO  c.r.f.t.TcpClientHandler - [sendCommandToAllTbox,172] - 📡 向所有TBOX客户端发送指令: 7E000A000A000102030405060708090A0000303330323030303103007E
14:20:23.871 [http-nio-9201-exec-10] INFO  c.r.f.t.TcpControlService - [sendLockCommandToAllTbox,107] - 🔒 已发送闭锁指令给所有TBOX设备
14:20:29.338 [http-nio-9201-exec-3] INFO  c.r.f.w.WebSocketServer - [handle4GControlRequest,205] - 🚗 收到4G控车请求: unlock
14:20:29.338 [http-nio-9201-exec-3] INFO  c.r.f.t.TcpControlService - [generateUnlockCommand,30] - 🔓 生成解锁指令: 7E000A000A000102030405060708090A0000303330323030303104007E
14:20:29.339 [http-nio-9201-exec-3] INFO  c.r.f.t.TcpClientHandler - [sendControlCommand,128] - 📤 发送控制指令给TBOX: 7E000A000A000102030405060708090A0000303330323030303104007E
14:20:29.339 [Thread-70] INFO  c.r.f.t.WebSocketToTcpProxy - [listenForMessages,230] - 📥 TBOX模拟器收到TCP消息: 7E000A000A000102030405060708090A0000303330323030303104007E
14:20:29.339 [http-nio-9201-exec-3] INFO  c.r.f.t.TcpClientHandler - [sendCommandToAllTbox,172] - 📡 向所有TBOX客户端发送指令: 7E000A000A000102030405060708090A0000303330323030303104007E
14:20:29.339 [http-nio-9201-exec-3] INFO  c.r.f.t.TcpControlService - [sendUnlockCommandToAllTbox,98] - 🔓 已发送解锁指令给所有TBOX设备
14:20:33.898 [http-nio-9201-exec-4] INFO  c.r.f.w.WebSocketServer - [handle4GControlRequest,205] - 🚗 收到4G控车请求: lock
14:20:33.898 [http-nio-9201-exec-4] INFO  c.r.f.t.TcpControlService - [generateLockCommand,40] - 🔒 生成闭锁指令: 7E000A000A000102030405060708090A0000303330323030303103007E
14:20:33.898 [http-nio-9201-exec-4] INFO  c.r.f.t.TcpClientHandler - [sendControlCommand,128] - 📤 发送控制指令给TBOX: 7E000A000A000102030405060708090A0000303330323030303103007E
14:20:33.898 [Thread-70] INFO  c.r.f.t.WebSocketToTcpProxy - [listenForMessages,230] - 📥 TBOX模拟器收到TCP消息: 7E000A000A000102030405060708090A0000303330323030303103007E
14:20:33.899 [http-nio-9201-exec-4] INFO  c.r.f.t.TcpClientHandler - [sendCommandToAllTbox,172] - 📡 向所有TBOX客户端发送指令: 7E000A000A000102030405060708090A0000303330323030303103007E
14:20:33.899 [http-nio-9201-exec-4] INFO  c.r.f.t.TcpControlService - [sendLockCommandToAllTbox,107] - 🔒 已发送闭锁指令给所有TBOX设备
14:20:49.175 [http-nio-9201-exec-5] INFO  c.r.f.w.WebSocketServer - [handle4GControlRequest,205] - 🚗 收到4G控车请求: unlock
14:20:49.176 [http-nio-9201-exec-5] INFO  c.r.f.t.TcpControlService - [generateUnlockCommand,30] - 🔓 生成解锁指令: 7E000A000A000102030405060708090A0000303330323030303104007E
14:20:49.177 [Thread-70] INFO  c.r.f.t.WebSocketToTcpProxy - [listenForMessages,230] - 📥 TBOX模拟器收到TCP消息: 7E000A000A000102030405060708090A0000303330323030303104007E
14:20:49.177 [http-nio-9201-exec-5] INFO  c.r.f.t.TcpClientHandler - [sendControlCommand,128] - 📤 发送控制指令给TBOX: 7E000A000A000102030405060708090A0000303330323030303104007E
14:20:49.177 [http-nio-9201-exec-5] INFO  c.r.f.t.TcpClientHandler - [sendCommandToAllTbox,172] - 📡 向所有TBOX客户端发送指令: 7E000A000A000102030405060708090A0000303330323030303104007E
14:20:49.177 [http-nio-9201-exec-5] INFO  c.r.f.t.TcpControlService - [sendUnlockCommandToAllTbox,98] - 🔓 已发送解锁指令给所有TBOX设备
14:20:52.168 [http-nio-9201-exec-8] INFO  c.r.f.w.WebSocketServer - [handle4GControlRequest,205] - 🚗 收到4G控车请求: lock
14:20:52.169 [http-nio-9201-exec-8] INFO  c.r.f.t.TcpControlService - [generateLockCommand,40] - 🔒 生成闭锁指令: 7E000A000A000102030405060708090A0000303330323030303103007E
14:20:52.169 [http-nio-9201-exec-8] INFO  c.r.f.t.TcpClientHandler - [sendControlCommand,128] - 📤 发送控制指令给TBOX: 7E000A000A000102030405060708090A0000303330323030303103007E
14:20:52.169 [Thread-70] INFO  c.r.f.t.WebSocketToTcpProxy - [listenForMessages,230] - 📥 TBOX模拟器收到TCP消息: 7E000A000A000102030405060708090A0000303330323030303103007E
14:20:52.169 [http-nio-9201-exec-8] INFO  c.r.f.t.TcpClientHandler - [sendCommandToAllTbox,172] - 📡 向所有TBOX客户端发送指令: 7E000A000A000102030405060708090A0000303330323030303103007E
14:20:52.169 [http-nio-9201-exec-8] INFO  c.r.f.t.TcpControlService - [sendLockCommandToAllTbox,107] - 🔒 已发送闭锁指令给所有TBOX设备
14:21:17.520 [http-nio-9201-exec-7] INFO  c.r.f.w.WebSocketServer - [handle4GControlRequest,205] - 🚗 收到4G控车请求: unlock
14:21:17.520 [http-nio-9201-exec-7] INFO  c.r.f.t.TcpControlService - [generateUnlockCommand,30] - 🔓 生成解锁指令: 7E000A000A000102030405060708090A0000303330323030303104007E
14:21:17.520 [Thread-70] INFO  c.r.f.t.WebSocketToTcpProxy - [listenForMessages,230] - 📥 TBOX模拟器收到TCP消息: 7E000A000A000102030405060708090A0000303330323030303104007E
14:21:17.520 [http-nio-9201-exec-7] INFO  c.r.f.t.TcpClientHandler - [sendControlCommand,128] - 📤 发送控制指令给TBOX: 7E000A000A000102030405060708090A0000303330323030303104007E
14:21:17.521 [http-nio-9201-exec-7] INFO  c.r.f.t.TcpClientHandler - [sendCommandToAllTbox,172] - 📡 向所有TBOX客户端发送指令: 7E000A000A000102030405060708090A0000303330323030303104007E
14:21:17.521 [http-nio-9201-exec-7] INFO  c.r.f.t.TcpControlService - [sendUnlockCommandToAllTbox,98] - 🔓 已发送解锁指令给所有TBOX设备
14:21:22.978 [http-nio-9201-exec-2] INFO  c.r.f.w.WebSocketServer - [handle4GControlRequest,205] - 🚗 收到4G控车请求: lock
14:21:22.978 [http-nio-9201-exec-2] INFO  c.r.f.t.TcpControlService - [generateLockCommand,40] - 🔒 生成闭锁指令: 7E000A000A000102030405060708090A0000303330323030303103007E
14:21:22.979 [Thread-70] INFO  c.r.f.t.WebSocketToTcpProxy - [listenForMessages,230] - 📥 TBOX模拟器收到TCP消息: 7E000A000A000102030405060708090A0000303330323030303103007E
14:21:22.979 [http-nio-9201-exec-2] INFO  c.r.f.t.TcpClientHandler - [sendControlCommand,128] - 📤 发送控制指令给TBOX: 7E000A000A000102030405060708090A0000303330323030303103007E
14:21:22.979 [http-nio-9201-exec-2] INFO  c.r.f.t.TcpClientHandler - [sendCommandToAllTbox,172] - 📡 向所有TBOX客户端发送指令: 7E000A000A000102030405060708090A0000303330323030303103007E
14:21:22.979 [http-nio-9201-exec-2] INFO  c.r.f.t.TcpControlService - [sendLockCommandToAllTbox,107] - 🔒 已发送闭锁指令给所有TBOX设备
14:21:38.527 [http-nio-9201-exec-1] INFO  c.r.f.w.WebSocketServer - [handle4GControlRequest,205] - 🚗 收到4G控车请求: unlock
14:21:38.528 [http-nio-9201-exec-1] INFO  c.r.f.t.TcpControlService - [generateUnlockCommand,30] - 🔓 生成解锁指令: 7E000A000A000102030405060708090A0000303330323030303104007E
14:21:38.528 [http-nio-9201-exec-1] INFO  c.r.f.t.TcpClientHandler - [sendControlCommand,128] - 📤 发送控制指令给TBOX: 7E000A000A000102030405060708090A0000303330323030303104007E
14:21:38.528 [Thread-70] INFO  c.r.f.t.WebSocketToTcpProxy - [listenForMessages,230] - 📥 TBOX模拟器收到TCP消息: 7E000A000A000102030405060708090A0000303330323030303104007E
14:21:38.528 [http-nio-9201-exec-1] INFO  c.r.f.t.TcpClientHandler - [sendCommandToAllTbox,172] - 📡 向所有TBOX客户端发送指令: 7E000A000A000102030405060708090A0000303330323030303104007E
14:21:38.528 [http-nio-9201-exec-1] INFO  c.r.f.t.TcpControlService - [sendUnlockCommandToAllTbox,98] - 🔓 已发送解锁指令给所有TBOX设备
14:21:41.516 [http-nio-9201-exec-6] INFO  c.r.f.t.WebSocketToTcpProxy - [onMessage,79] - 📡 收到TBOX模拟器指令: {"action":"status"}
14:21:41.517 [http-nio-9201-exec-6] INFO  c.r.f.t.WebSocketToTcpProxy - [sendMessage,223] - 📤 TBOX模拟器发送TCP消息: 7E000A000A000102030405060708090A000030333032303030313034007E
14:21:41.517 [pool-6-thread-3] INFO  c.r.f.t.TcpClientHandler - [run,54] - 📨 收到TBOX消息: 7E000A000A000102030405060708090A000030333032303030313034007E from /127.0.0.1:63022
14:21:41.517 [pool-6-thread-3] INFO  c.r.f.t.TcpClientHandler - [handleTboxMessage,89] - 🚗 车辆状态更新: locked (车辆已闭锁)
14:21:41.517 [http-nio-9201-exec-6] INFO  c.r.f.t.WebSocketToTcpProxy - [handleStatusReport,168] - 📊 TBOX模拟器发送状态报告
14:21:41.517 [pool-6-thread-3] INFO  c.r.f.t.TcpClientHandler - [notifyMobileApp,115] - 📤 已通知手机端车辆状态: 车辆已闭锁
14:22:14.490 [http-nio-9201-exec-10] INFO  c.r.f.w.WebSocketServer - [onClose,113] - 
 关闭连接 - org.apache.tomcat.websocket.WsSession@3cea58b5
14:22:14.490 [http-nio-9201-exec-10] INFO  c.r.f.w.WebSocketUsers - [removeUserSessionsBySessionId,109] - 
 正在根据sessionId移出用户Session映射 - 6d
14:22:14.490 [http-nio-9201-exec-10] INFO  c.r.f.w.WebSocketUsers - [removeUserSessionsBySessionId,122] - 
 移出用户Session映射结果 - 成功
14:22:14.490 [http-nio-9201-exec-10] INFO  c.r.f.w.WebSocketUsers - [remove,136] - 
 正在移出用户 - 6d
14:22:14.491 [http-nio-9201-exec-10] INFO  c.r.f.w.WebSocketUsers - [remove,140] - 
 移出结果 - 成功
14:22:15.802 [http-nio-9201-exec-3] INFO  c.r.f.w.WebSocketServer - [onOpen,100] - 
 建立连接 - org.apache.tomcat.websocket.WsSession@1c3e4e7
14:22:15.803 [http-nio-9201-exec-3] INFO  c.r.f.w.WebSocketServer - [onOpen,101] - 
 当前人数 - 1
14:22:15.808 [http-nio-9201-exec-4] INFO  c.r.f.w.WebSocketServer - [onMessage,182] - 
 分享人通讯异常，无法处理 - 6e
14:22:22.713 [http-nio-9201-exec-8] INFO  c.r.f.t.WebSocketToTcpProxy - [onMessage,79] - 📡 收到TBOX模拟器指令: {"action":"status"}
14:22:22.713 [http-nio-9201-exec-8] INFO  c.r.f.t.WebSocketToTcpProxy - [sendMessage,223] - 📤 TBOX模拟器发送TCP消息: 7E000A000A000102030405060708090A000030333032303030313034007E
14:22:22.713 [pool-6-thread-3] INFO  c.r.f.t.TcpClientHandler - [run,54] - 📨 收到TBOX消息: 7E000A000A000102030405060708090A000030333032303030313034007E from /127.0.0.1:63022
14:22:22.713 [pool-6-thread-3] INFO  c.r.f.t.TcpClientHandler - [handleTboxMessage,89] - 🚗 车辆状态更新: locked (车辆已闭锁)
14:22:22.713 [http-nio-9201-exec-8] INFO  c.r.f.t.WebSocketToTcpProxy - [handleStatusReport,168] - 📊 TBOX模拟器发送状态报告
14:22:22.713 [pool-6-thread-3] INFO  c.r.f.t.TcpClientHandler - [notifyMobileApp,115] - 📤 已通知手机端车辆状态: 车辆已闭锁
14:23:19.648 [http-nio-9201-exec-7] INFO  c.r.f.w.WebSocketServer - [onClose,113] - 
 关闭连接 - org.apache.tomcat.websocket.WsSession@1c3e4e7
14:23:19.648 [http-nio-9201-exec-7] INFO  c.r.f.w.WebSocketUsers - [removeUserSessionsBySessionId,109] - 
 正在根据sessionId移出用户Session映射 - 6e
14:23:19.649 [http-nio-9201-exec-7] INFO  c.r.f.w.WebSocketUsers - [removeUserSessionsBySessionId,122] - 
 移出用户Session映射结果 - 成功
14:23:19.649 [http-nio-9201-exec-7] INFO  c.r.f.w.WebSocketUsers - [remove,136] - 
 正在移出用户 - 6e
14:23:19.649 [http-nio-9201-exec-7] INFO  c.r.f.w.WebSocketUsers - [remove,140] - 
 移出结果 - 成功
14:23:21.812 [http-nio-9201-exec-2] INFO  c.r.f.w.WebSocketServer - [onOpen,100] - 
 建立连接 - org.apache.tomcat.websocket.WsSession@58113db9
14:23:21.813 [http-nio-9201-exec-2] INFO  c.r.f.w.WebSocketServer - [onOpen,101] - 
 当前人数 - 1
14:23:21.836 [http-nio-9201-exec-1] INFO  c.r.f.w.WebSocketServer - [onMessage,182] - 
 分享人通讯异常，无法处理 - 6f
14:23:32.649 [http-nio-9201-exec-10] INFO  c.r.f.w.WebSocketServer - [onClose,113] - 
 关闭连接 - org.apache.tomcat.websocket.WsSession@58113db9
14:23:32.649 [http-nio-9201-exec-10] INFO  c.r.f.w.WebSocketUsers - [removeUserSessionsBySessionId,109] - 
 正在根据sessionId移出用户Session映射 - 6f
14:23:32.649 [http-nio-9201-exec-10] INFO  c.r.f.w.WebSocketUsers - [removeUserSessionsBySessionId,122] - 
 移出用户Session映射结果 - 成功
14:23:32.649 [http-nio-9201-exec-10] INFO  c.r.f.w.WebSocketUsers - [remove,136] - 
 正在移出用户 - 6f
14:23:32.649 [http-nio-9201-exec-10] INFO  c.r.f.w.WebSocketUsers - [remove,140] - 
 移出结果 - 成功
14:23:35.455 [http-nio-9201-exec-3] INFO  c.r.f.w.WebSocketServer - [onOpen,100] - 
 建立连接 - org.apache.tomcat.websocket.WsSession@52bd9ebc
14:23:35.455 [http-nio-9201-exec-3] INFO  c.r.f.w.WebSocketServer - [onOpen,101] - 
 当前人数 - 1
14:23:35.459 [http-nio-9201-exec-4] INFO  c.r.f.w.WebSocketServer - [onMessage,182] - 
 分享人通讯异常，无法处理 - 70
14:28:43.331 [http-nio-9201-exec-9] INFO  c.r.f.w.WebSocketServer - [onClose,113] - 
 关闭连接 - org.apache.tomcat.websocket.WsSession@52bd9ebc
14:28:43.333 [http-nio-9201-exec-9] INFO  c.r.f.w.WebSocketUsers - [removeUserSessionsBySessionId,109] - 
 正在根据sessionId移出用户Session映射 - 70
14:28:43.333 [http-nio-9201-exec-9] INFO  c.r.f.w.WebSocketUsers - [removeUserSessionsBySessionId,122] - 
 移出用户Session映射结果 - 成功
14:28:43.334 [http-nio-9201-exec-9] INFO  c.r.f.w.WebSocketUsers - [remove,136] - 
 正在移出用户 - 70
14:28:43.334 [http-nio-9201-exec-9] INFO  c.r.f.w.WebSocketUsers - [remove,140] - 
 移出结果 - 成功
14:35:40.088 [http-nio-9201-exec-7] INFO  c.r.f.t.WebSocketToTcpProxy - [onClose,50] - 📡 TBOX模拟器WebSocket连接关闭: 65
14:35:40.091 [pool-6-thread-3] INFO  c.r.f.t.TcpClientHandler - [closeConnection,153] - 🔌 TBOX客户端连接已关闭: /127.0.0.1:63022
14:37:46.292 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,95] - De-registering from Nacos Server now...
14:37:46.309 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,115] - De-registration finished.
14:37:46.333 [SpringApplicationShutdownHook] INFO  c.r.f.tcp.TcpServer - [stop,83] - 🛑 TCP服务器已关闭
14:37:46.334 [SpringApplicationShutdownHook] INFO  c.r.f.tcp.TcpServer - [stop,88] - 🛑 TCP线程池已关闭
14:37:46.492 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,217] - dynamic-datasource start closing ....
14:37:46.498 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2174] - {dataSource-1} closing ...
14:37:46.507 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2247] - {dataSource-1} closed
14:37:46.507 [SpringApplicationShutdownHook] INFO  c.b.d.d.d.DefaultDataSourceDestroyer - [destroy,98] - dynamic-datasource close the datasource named [master] success,
14:37:46.507 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,221] - dynamic-datasource all closed success,bye
14:37:52.745 [main] INFO  c.a.n.c.e.SearchableProperties - [sortPropertySourceDefaultOrder,197] - properties search order:PROPERTIES->JVM->ENV->DEFAULT_SETTING
14:37:52.786 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
14:37:53.072 [main] INFO  c.a.n.p.a.s.c.ClientAuthPluginManager - [init,56] - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.impl.NacosClientAuthServiceImpl success.
14:37:53.073 [main] INFO  c.a.n.p.a.s.c.ClientAuthPluginManager - [init,56] - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.ram.RamClientAuthServiceImpl success.
14:37:54.943 [main] INFO  c.r.s.RuoYiSystemApplication - [logStartupProfileInfo,638] - The following 1 profile is active: "dev"
14:37:56.818 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9201"]
14:37:56.819 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
14:37:56.820 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.83]
14:37:56.954 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
14:37:57.647 [main] INFO  c.a.d.p.DruidDataSource - [init,1009] - {dataSource-1,master} inited
14:37:57.648 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,160] - dynamic-datasource - add a datasource named [master] success
14:37:57.649 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [afterPropertiesSet,243] - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
14:37:59.065 [main] INFO  c.r.f.tcp.TcpServer - [start,49] - 🚀 TCP服务器启动成功，监听端口: 9999
14:37:59.926 [main] INFO  c.a.c.s.SentinelWebMvcConfigurer - [addInterceptors,52] - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
14:38:01.869 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9201"]
14:38:01.898 [main] INFO  c.a.n.p.a.s.c.ClientAuthPluginManager - [init,56] - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.impl.NacosClientAuthServiceImpl success.
14:38:01.898 [main] INFO  c.a.n.p.a.s.c.ClientAuthPluginManager - [init,56] - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.ram.RamClientAuthServiceImpl success.
14:38:01.964 [http-nio-9201-exec-1] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
14:38:02.019 [http-nio-9201-exec-1] INFO  c.r.f.t.WebSocketToTcpProxy - [onOpen,34] - 📡 TBOX模拟器WebSocket连接建立: 0
14:38:02.043 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,76] - nacos registry, DEFAULT_GROUP ruoyi-system 192.168.43.78:9201 register finished
14:38:02.727 [main] INFO  c.r.s.RuoYiSystemApplication - [logStarted,61] - Started RuoYiSystemApplication in 10.953 seconds (JVM running for 12.742)
14:38:02.741 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=ruoyi-system, group=DEFAULT_GROUP
14:38:02.742 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=ruoyi-system.yml, group=DEFAULT_GROUP
14:38:02.743 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=ruoyi-system-dev.yml, group=DEFAULT_GROUP
14:38:03.779 [http-nio-9201-exec-4] INFO  c.r.f.t.WebSocketToTcpProxy - [onClose,50] - 📡 TBOX模拟器WebSocket连接关闭: 0
14:38:04.023 [http-nio-9201-exec-6] INFO  c.r.f.t.WebSocketToTcpProxy - [onOpen,34] - 📡 TBOX模拟器WebSocket连接建立: 1
14:39:27.558 [http-nio-9201-exec-8] INFO  c.r.f.w.WebSocketServer - [onOpen,100] - 
 建立连接 - org.apache.tomcat.websocket.WsSession@557437ec
14:39:27.559 [http-nio-9201-exec-8] INFO  c.r.f.w.WebSocketServer - [onOpen,101] - 
 当前人数 - 1
14:39:27.643 [http-nio-9201-exec-10] INFO  c.r.f.w.WebSocketServer - [onMessage,182] - 
 分享人通讯异常，无法处理 - 2
14:45:09.059 [http-nio-9201-exec-1] INFO  c.r.f.t.WebSocketToTcpProxy - [onMessage,79] - 📡 收到TBOX模拟器指令: {"action":"connect","host":"localhost","port":9999}
14:45:09.067 [http-nio-9201-exec-1] INFO  c.r.f.t.WebSocketToTcpProxy - [handleConnect,116] - 🔌 TBOX模拟器尝试连接TCP服务器: localhost:9999
14:45:09.069 [pool-6-thread-1] INFO  c.r.f.tcp.TcpServer - [acceptConnections,63] - 📱 新的TBOX客户端连接: /127.0.0.1:50920
14:45:09.075 [http-nio-9201-exec-1] INFO  c.r.f.t.WebSocketToTcpProxy - [handleConnect,127] - ✅ TBOX模拟器TCP连接建立成功
14:45:09.076 [pool-6-thread-2] INFO  c.r.f.t.TcpClientHandler - [run,49] - 🔄 开始处理TBOX客户端: /127.0.0.1:50920
15:21:22.921 [http-nio-9201-exec-2] INFO  c.r.f.w.WebSocketServer - [handle4GControlRequest,205] - 🚗 收到4G控车请求: lock
15:21:22.922 [http-nio-9201-exec-2] INFO  c.r.f.t.TcpControlService - [generateLockCommand,40] - 🔒 生成闭锁指令: 7E000A000A000102030405060708090A0000303330323030303103007E
15:21:22.929 [Thread-61] INFO  c.r.f.t.WebSocketToTcpProxy - [listenForMessages,230] - 📥 TBOX模拟器收到TCP消息: 7E000A000A000102030405060708090A0000303330323030303103007E
15:21:22.928 [http-nio-9201-exec-2] INFO  c.r.f.t.TcpClientHandler - [sendControlCommand,128] - 📤 发送控制指令给TBOX: 7E000A000A000102030405060708090A0000303330323030303103007E
15:21:22.929 [http-nio-9201-exec-2] INFO  c.r.f.t.TcpClientHandler - [sendCommandToAllTbox,172] - 📡 向所有TBOX客户端发送指令: 7E000A000A000102030405060708090A0000303330323030303103007E
15:21:22.929 [http-nio-9201-exec-2] INFO  c.r.f.t.TcpControlService - [sendLockCommandToAllTbox,107] - 🔒 已发送闭锁指令给所有TBOX设备
15:21:25.403 [http-nio-9201-exec-4] INFO  c.r.f.w.WebSocketServer - [handle4GControlRequest,205] - 🚗 收到4G控车请求: unlock
15:21:25.404 [http-nio-9201-exec-4] INFO  c.r.f.t.TcpControlService - [generateUnlockCommand,30] - 🔓 生成解锁指令: 7E000A000A000102030405060708090A0000303330323030303104007E
15:21:25.405 [Thread-61] INFO  c.r.f.t.WebSocketToTcpProxy - [listenForMessages,230] - 📥 TBOX模拟器收到TCP消息: 7E000A000A000102030405060708090A0000303330323030303104007E
15:21:25.405 [http-nio-9201-exec-4] INFO  c.r.f.t.TcpClientHandler - [sendControlCommand,128] - 📤 发送控制指令给TBOX: 7E000A000A000102030405060708090A0000303330323030303104007E
15:21:25.405 [http-nio-9201-exec-4] INFO  c.r.f.t.TcpClientHandler - [sendCommandToAllTbox,172] - 📡 向所有TBOX客户端发送指令: 7E000A000A000102030405060708090A0000303330323030303104007E
15:21:25.406 [http-nio-9201-exec-4] INFO  c.r.f.t.TcpControlService - [sendUnlockCommandToAllTbox,98] - 🔓 已发送解锁指令给所有TBOX设备
15:21:38.027 [http-nio-9201-exec-3] INFO  c.r.f.t.WebSocketToTcpProxy - [onMessage,79] - 📡 收到TBOX模拟器指令: {"action":"status"}
15:21:38.028 [http-nio-9201-exec-3] INFO  c.r.f.t.WebSocketToTcpProxy - [sendMessage,223] - 📤 TBOX模拟器发送TCP消息: 7E000A000A000102030405060708090A000030333032303030313034007E
15:21:38.028 [pool-6-thread-2] INFO  c.r.f.t.TcpClientHandler - [run,54] - 📨 收到TBOX消息: 7E000A000A000102030405060708090A000030333032303030313034007E from /127.0.0.1:50920
15:21:38.029 [http-nio-9201-exec-3] INFO  c.r.f.t.WebSocketToTcpProxy - [handleStatusReport,168] - 📊 TBOX模拟器发送状态报告
15:21:38.029 [pool-6-thread-2] INFO  c.r.f.t.TcpClientHandler - [handleTboxMessage,89] - 🚗 车辆状态更新: locked (车辆已闭锁)
15:21:38.029 [pool-6-thread-2] INFO  c.r.f.t.TcpClientHandler - [notifyMobileApp,115] - 📤 已通知手机端车辆状态: 车辆已闭锁
15:32:06.078 [http-nio-9201-exec-6] INFO  c.r.f.w.WebSocketServer - [onClose,113] - 
 关闭连接 - org.apache.tomcat.websocket.WsSession@557437ec
15:32:06.079 [http-nio-9201-exec-6] INFO  c.r.f.w.WebSocketUsers - [removeUserSessionsBySessionId,109] - 
 正在根据sessionId移出用户Session映射 - 2
15:32:06.080 [http-nio-9201-exec-6] INFO  c.r.f.w.WebSocketUsers - [removeUserSessionsBySessionId,122] - 
 移出用户Session映射结果 - 成功
15:32:06.081 [http-nio-9201-exec-6] INFO  c.r.f.w.WebSocketUsers - [remove,136] - 
 正在移出用户 - 2
15:32:06.081 [http-nio-9201-exec-6] INFO  c.r.f.w.WebSocketUsers - [remove,140] - 
 移出结果 - 成功
15:32:09.619 [http-nio-9201-exec-5] INFO  c.r.f.w.WebSocketServer - [onOpen,100] - 
 建立连接 - org.apache.tomcat.websocket.WsSession@2aa1c6e9
15:32:09.620 [http-nio-9201-exec-5] INFO  c.r.f.w.WebSocketServer - [onOpen,101] - 
 当前人数 - 1
15:32:09.627 [http-nio-9201-exec-8] INFO  c.r.f.w.WebSocketServer - [onMessage,182] - 
 分享人通讯异常，无法处理 - 3
15:32:40.522 [http-nio-9201-exec-7] INFO  c.r.f.w.WebSocketServer - [onClose,113] - 
 关闭连接 - org.apache.tomcat.websocket.WsSession@2aa1c6e9
15:32:40.523 [http-nio-9201-exec-7] INFO  c.r.f.w.WebSocketUsers - [removeUserSessionsBySessionId,109] - 
 正在根据sessionId移出用户Session映射 - 3
15:32:40.523 [http-nio-9201-exec-7] INFO  c.r.f.w.WebSocketUsers - [removeUserSessionsBySessionId,122] - 
 移出用户Session映射结果 - 成功
15:32:40.525 [http-nio-9201-exec-7] INFO  c.r.f.w.WebSocketUsers - [remove,136] - 
 正在移出用户 - 3
15:32:40.525 [http-nio-9201-exec-7] INFO  c.r.f.w.WebSocketUsers - [remove,140] - 
 移出结果 - 成功
15:32:43.294 [http-nio-9201-exec-9] INFO  c.r.f.w.WebSocketServer - [onOpen,100] - 
 建立连接 - org.apache.tomcat.websocket.WsSession@5e29101f
15:32:43.295 [http-nio-9201-exec-9] INFO  c.r.f.w.WebSocketServer - [onOpen,101] - 
 当前人数 - 1
15:32:43.347 [http-nio-9201-exec-2] INFO  c.r.f.w.WebSocketServer - [onMessage,182] - 
 分享人通讯异常，无法处理 - 4
15:33:42.911 [http-nio-9201-exec-4] INFO  c.r.f.w.WebSocketServer - [onClose,113] - 
 关闭连接 - org.apache.tomcat.websocket.WsSession@5e29101f
15:33:42.911 [http-nio-9201-exec-4] INFO  c.r.f.w.WebSocketUsers - [removeUserSessionsBySessionId,109] - 
 正在根据sessionId移出用户Session映射 - 4
15:33:42.911 [http-nio-9201-exec-4] INFO  c.r.f.w.WebSocketUsers - [removeUserSessionsBySessionId,122] - 
 移出用户Session映射结果 - 成功
15:33:42.911 [http-nio-9201-exec-4] INFO  c.r.f.w.WebSocketUsers - [remove,136] - 
 正在移出用户 - 4
15:33:42.911 [http-nio-9201-exec-4] INFO  c.r.f.w.WebSocketUsers - [remove,140] - 
 移出结果 - 成功
15:33:45.132 [http-nio-9201-exec-6] INFO  c.r.f.w.WebSocketServer - [onOpen,100] - 
 建立连接 - org.apache.tomcat.websocket.WsSession@7bae5d8e
15:33:45.133 [http-nio-9201-exec-6] INFO  c.r.f.w.WebSocketServer - [onOpen,101] - 
 当前人数 - 1
15:33:45.141 [http-nio-9201-exec-5] INFO  c.r.f.w.WebSocketServer - [onMessage,182] - 
 分享人通讯异常，无法处理 - 5
15:34:16.643 [http-nio-9201-exec-10] INFO  c.r.f.w.WebSocketServer - [onClose,113] - 
 关闭连接 - org.apache.tomcat.websocket.WsSession@7bae5d8e
15:34:16.643 [http-nio-9201-exec-10] INFO  c.r.f.w.WebSocketUsers - [removeUserSessionsBySessionId,109] - 
 正在根据sessionId移出用户Session映射 - 5
15:34:16.645 [http-nio-9201-exec-10] INFO  c.r.f.w.WebSocketUsers - [removeUserSessionsBySessionId,122] - 
 移出用户Session映射结果 - 成功
15:34:16.645 [http-nio-9201-exec-10] INFO  c.r.f.w.WebSocketUsers - [remove,136] - 
 正在移出用户 - 5
15:34:16.645 [http-nio-9201-exec-10] INFO  c.r.f.w.WebSocketUsers - [remove,140] - 
 移出结果 - 成功
15:34:19.059 [http-nio-9201-exec-7] INFO  c.r.f.w.WebSocketServer - [onOpen,100] - 
 建立连接 - org.apache.tomcat.websocket.WsSession@170ce3c0
15:34:19.059 [http-nio-9201-exec-7] INFO  c.r.f.w.WebSocketServer - [onOpen,101] - 
 当前人数 - 1
15:34:19.097 [http-nio-9201-exec-2] INFO  c.r.f.w.WebSocketServer - [onMessage,182] - 
 分享人通讯异常，无法处理 - 6
15:34:33.390 [http-nio-9201-exec-1] INFO  c.r.f.w.WebSocketServer - [onClose,113] - 
 关闭连接 - org.apache.tomcat.websocket.WsSession@170ce3c0
15:34:33.390 [http-nio-9201-exec-1] INFO  c.r.f.w.WebSocketUsers - [removeUserSessionsBySessionId,109] - 
 正在根据sessionId移出用户Session映射 - 6
15:34:33.391 [http-nio-9201-exec-1] INFO  c.r.f.w.WebSocketUsers - [removeUserSessionsBySessionId,122] - 
 移出用户Session映射结果 - 成功
15:34:33.391 [http-nio-9201-exec-1] INFO  c.r.f.w.WebSocketUsers - [remove,136] - 
 正在移出用户 - 6
15:34:33.391 [http-nio-9201-exec-1] INFO  c.r.f.w.WebSocketUsers - [remove,140] - 
 移出结果 - 成功
15:34:33.447 [http-nio-9201-exec-4] INFO  c.r.f.w.WebSocketServer - [onOpen,100] - 
 建立连接 - org.apache.tomcat.websocket.WsSession@3cf1d6d8
15:34:33.447 [http-nio-9201-exec-4] INFO  c.r.f.w.WebSocketServer - [onOpen,101] - 
 当前人数 - 1
15:34:33.458 [http-nio-9201-exec-3] INFO  c.r.f.w.WebSocketServer - [onMessage,182] - 
 分享人通讯异常，无法处理 - 7
15:34:33.765 [http-nio-9201-exec-5] INFO  c.r.f.w.WebSocketServer - [onClose,113] - 
 关闭连接 - org.apache.tomcat.websocket.WsSession@3cf1d6d8
15:34:33.766 [http-nio-9201-exec-5] INFO  c.r.f.w.WebSocketUsers - [removeUserSessionsBySessionId,109] - 
 正在根据sessionId移出用户Session映射 - 7
15:34:33.766 [http-nio-9201-exec-5] INFO  c.r.f.w.WebSocketUsers - [removeUserSessionsBySessionId,122] - 
 移出用户Session映射结果 - 成功
15:34:33.766 [http-nio-9201-exec-5] INFO  c.r.f.w.WebSocketUsers - [remove,136] - 
 正在移出用户 - 7
15:34:33.766 [http-nio-9201-exec-5] INFO  c.r.f.w.WebSocketUsers - [remove,140] - 
 移出结果 - 成功
15:34:36.635 [http-nio-9201-exec-8] INFO  c.r.f.w.WebSocketServer - [onOpen,100] - 
 建立连接 - org.apache.tomcat.websocket.WsSession@3e01eff9
15:34:36.636 [http-nio-9201-exec-8] INFO  c.r.f.w.WebSocketServer - [onOpen,101] - 
 当前人数 - 1
15:34:36.678 [http-nio-9201-exec-7] INFO  c.r.f.w.WebSocketServer - [onMessage,182] - 
 分享人通讯异常，无法处理 - 8
15:34:52.775 [http-nio-9201-exec-2] INFO  c.r.f.w.WebSocketServer - [onClose,113] - 
 关闭连接 - org.apache.tomcat.websocket.WsSession@3e01eff9
15:34:52.776 [http-nio-9201-exec-2] INFO  c.r.f.w.WebSocketUsers - [removeUserSessionsBySessionId,109] - 
 正在根据sessionId移出用户Session映射 - 8
15:34:52.776 [http-nio-9201-exec-2] INFO  c.r.f.w.WebSocketUsers - [removeUserSessionsBySessionId,122] - 
 移出用户Session映射结果 - 成功
15:34:52.776 [http-nio-9201-exec-2] INFO  c.r.f.w.WebSocketUsers - [remove,136] - 
 正在移出用户 - 8
15:34:52.776 [http-nio-9201-exec-2] INFO  c.r.f.w.WebSocketUsers - [remove,140] - 
 移出结果 - 成功
15:34:54.953 [http-nio-9201-exec-9] INFO  c.r.f.w.WebSocketServer - [onOpen,100] - 
 建立连接 - org.apache.tomcat.websocket.WsSession@26331446
15:34:54.954 [http-nio-9201-exec-9] INFO  c.r.f.w.WebSocketServer - [onOpen,101] - 
 当前人数 - 1
15:34:54.984 [http-nio-9201-exec-4] INFO  c.r.f.w.WebSocketServer - [onMessage,182] - 
 分享人通讯异常，无法处理 - 9
15:35:27.858 [http-nio-9201-exec-3] INFO  c.r.f.w.WebSocketServer - [onClose,113] - 
 关闭连接 - org.apache.tomcat.websocket.WsSession@26331446
15:35:27.858 [http-nio-9201-exec-3] INFO  c.r.f.w.WebSocketUsers - [removeUserSessionsBySessionId,109] - 
 正在根据sessionId移出用户Session映射 - 9
15:35:27.858 [http-nio-9201-exec-3] INFO  c.r.f.w.WebSocketUsers - [removeUserSessionsBySessionId,122] - 
 移出用户Session映射结果 - 成功
15:35:27.858 [http-nio-9201-exec-3] INFO  c.r.f.w.WebSocketUsers - [remove,136] - 
 正在移出用户 - 9
15:35:27.858 [http-nio-9201-exec-3] INFO  c.r.f.w.WebSocketUsers - [remove,140] - 
 移出结果 - 成功
15:35:30.116 [http-nio-9201-exec-6] INFO  c.r.f.w.WebSocketServer - [onOpen,100] - 
 建立连接 - org.apache.tomcat.websocket.WsSession@7cbf5b31
15:35:30.116 [http-nio-9201-exec-6] INFO  c.r.f.w.WebSocketServer - [onOpen,101] - 
 当前人数 - 1
15:35:30.221 [http-nio-9201-exec-8] INFO  c.r.f.w.WebSocketServer - [onMessage,182] - 
 分享人通讯异常，无法处理 - a
15:38:05.867 [http-nio-9201-exec-10] INFO  c.r.f.w.WebSocketServer - [onClose,113] - 
 关闭连接 - org.apache.tomcat.websocket.WsSession@7cbf5b31
15:38:05.868 [http-nio-9201-exec-10] INFO  c.r.f.w.WebSocketUsers - [removeUserSessionsBySessionId,109] - 
 正在根据sessionId移出用户Session映射 - a
15:38:05.869 [http-nio-9201-exec-10] INFO  c.r.f.w.WebSocketUsers - [removeUserSessionsBySessionId,122] - 
 移出用户Session映射结果 - 成功
15:38:05.869 [http-nio-9201-exec-10] INFO  c.r.f.w.WebSocketUsers - [remove,136] - 
 正在移出用户 - a
15:38:05.869 [http-nio-9201-exec-10] INFO  c.r.f.w.WebSocketUsers - [remove,140] - 
 移出结果 - 成功
15:51:08.701 [http-nio-9201-exec-2] INFO  c.r.f.w.WebSocketServer - [onOpen,100] - 
 建立连接 - org.apache.tomcat.websocket.WsSession@eb6d79
15:51:08.701 [http-nio-9201-exec-2] INFO  c.r.f.w.WebSocketServer - [onOpen,101] - 
 当前人数 - 1
15:51:08.761 [http-nio-9201-exec-4] INFO  c.r.f.w.WebSocketServer - [onMessage,182] - 
 分享人通讯异常，无法处理 - b
15:56:36.220 [http-nio-9201-exec-3] INFO  c.r.f.w.WebSocketServer - [onClose,113] - 
 关闭连接 - org.apache.tomcat.websocket.WsSession@eb6d79
15:56:36.220 [http-nio-9201-exec-3] INFO  c.r.f.w.WebSocketUsers - [removeUserSessionsBySessionId,109] - 
 正在根据sessionId移出用户Session映射 - b
15:56:36.220 [http-nio-9201-exec-3] INFO  c.r.f.w.WebSocketUsers - [removeUserSessionsBySessionId,122] - 
 移出用户Session映射结果 - 成功
15:56:36.220 [http-nio-9201-exec-3] INFO  c.r.f.w.WebSocketUsers - [remove,136] - 
 正在移出用户 - b
15:56:36.220 [http-nio-9201-exec-3] INFO  c.r.f.w.WebSocketUsers - [remove,140] - 
 移出结果 - 成功
16:00:26.978 [http-nio-9201-exec-6] INFO  c.r.f.w.WebSocketServer - [onOpen,100] - 
 建立连接 - org.apache.tomcat.websocket.WsSession@15b47a66
16:00:26.978 [http-nio-9201-exec-6] INFO  c.r.f.w.WebSocketServer - [onOpen,101] - 
 当前人数 - 1
16:00:26.986 [http-nio-9201-exec-5] INFO  c.r.f.w.WebSocketServer - [onMessage,182] - 
 分享人通讯异常，无法处理 - c
16:00:27.867 [http-nio-9201-exec-8] INFO  c.r.f.w.WebSocketServer - [onClose,113] - 
 关闭连接 - org.apache.tomcat.websocket.WsSession@15b47a66
16:00:27.867 [http-nio-9201-exec-8] INFO  c.r.f.w.WebSocketUsers - [removeUserSessionsBySessionId,109] - 
 正在根据sessionId移出用户Session映射 - c
16:00:27.867 [http-nio-9201-exec-8] INFO  c.r.f.w.WebSocketUsers - [removeUserSessionsBySessionId,122] - 
 移出用户Session映射结果 - 成功
16:00:27.867 [http-nio-9201-exec-8] INFO  c.r.f.w.WebSocketUsers - [remove,136] - 
 正在移出用户 - c
16:00:27.867 [http-nio-9201-exec-8] INFO  c.r.f.w.WebSocketUsers - [remove,140] - 
 移出结果 - 成功
16:00:32.899 [http-nio-9201-exec-7] INFO  c.r.f.w.WebSocketServer - [onOpen,100] - 
 建立连接 - org.apache.tomcat.websocket.WsSession@57fd4e78
16:00:32.899 [http-nio-9201-exec-7] INFO  c.r.f.w.WebSocketServer - [onOpen,101] - 
 当前人数 - 1
16:00:32.903 [http-nio-9201-exec-10] INFO  c.r.f.w.WebSocketServer - [onMessage,182] - 
 分享人通讯异常，无法处理 - d
16:18:14.996 [http-nio-9201-exec-2] INFO  c.r.f.w.WebSocketServer - [onClose,113] - 
 关闭连接 - org.apache.tomcat.websocket.WsSession@57fd4e78
16:18:14.997 [http-nio-9201-exec-2] INFO  c.r.f.w.WebSocketUsers - [removeUserSessionsBySessionId,109] - 
 正在根据sessionId移出用户Session映射 - d
16:18:14.997 [http-nio-9201-exec-2] INFO  c.r.f.w.WebSocketUsers - [removeUserSessionsBySessionId,122] - 
 移出用户Session映射结果 - 成功
16:18:14.997 [http-nio-9201-exec-2] INFO  c.r.f.w.WebSocketUsers - [remove,136] - 
 正在移出用户 - d
16:18:14.997 [http-nio-9201-exec-2] INFO  c.r.f.w.WebSocketUsers - [remove,140] - 
 移出结果 - 成功
16:24:33.535 [http-nio-9201-exec-4] INFO  c.r.f.w.WebSocketServer - [onOpen,100] - 
 建立连接 - org.apache.tomcat.websocket.WsSession@3d703134
16:24:33.536 [http-nio-9201-exec-4] INFO  c.r.f.w.WebSocketServer - [onOpen,101] - 
 当前人数 - 1
16:24:33.539 [http-nio-9201-exec-9] INFO  c.r.f.w.WebSocketServer - [onMessage,182] - 
 分享人通讯异常，无法处理 - e
16:30:02.262 [http-nio-9201-exec-1] INFO  c.r.f.w.WebSocketServer - [onClose,113] - 
 关闭连接 - org.apache.tomcat.websocket.WsSession@3d703134
16:30:02.262 [http-nio-9201-exec-1] INFO  c.r.f.w.WebSocketUsers - [removeUserSessionsBySessionId,109] - 
 正在根据sessionId移出用户Session映射 - e
16:30:02.263 [http-nio-9201-exec-1] INFO  c.r.f.w.WebSocketUsers - [removeUserSessionsBySessionId,122] - 
 移出用户Session映射结果 - 成功
16:30:02.263 [http-nio-9201-exec-1] INFO  c.r.f.w.WebSocketUsers - [remove,136] - 
 正在移出用户 - e
16:30:02.263 [http-nio-9201-exec-1] INFO  c.r.f.w.WebSocketUsers - [remove,140] - 
 移出结果 - 成功
16:30:05.659 [http-nio-9201-exec-3] INFO  c.r.f.w.WebSocketServer - [onOpen,100] - 
 建立连接 - org.apache.tomcat.websocket.WsSession@2992ba7f
16:30:05.659 [http-nio-9201-exec-3] INFO  c.r.f.w.WebSocketServer - [onOpen,101] - 
 当前人数 - 1
16:30:05.706 [http-nio-9201-exec-6] INFO  c.r.f.w.WebSocketServer - [onMessage,182] - 
 分享人通讯异常，无法处理 - f
16:30:10.940 [http-nio-9201-exec-5] INFO  c.r.f.w.WebSocketServer - [handle4GControlRequest,205] - 🚗 收到4G控车请求: lock
16:30:10.941 [http-nio-9201-exec-5] INFO  c.r.f.t.TcpControlService - [generateLockCommand,40] - 🔒 生成闭锁指令: 7E000A000A000102030405060708090A0000303330323030303103007E
16:30:10.941 [http-nio-9201-exec-5] INFO  c.r.f.t.TcpClientHandler - [sendControlCommand,128] - 📤 发送控制指令给TBOX: 7E000A000A000102030405060708090A0000303330323030303103007E
16:30:10.941 [http-nio-9201-exec-5] INFO  c.r.f.t.TcpClientHandler - [sendCommandToAllTbox,172] - 📡 向所有TBOX客户端发送指令: 7E000A000A000102030405060708090A0000303330323030303103007E
16:30:10.943 [http-nio-9201-exec-5] INFO  c.r.f.t.TcpControlService - [sendLockCommandToAllTbox,107] - 🔒 已发送闭锁指令给所有TBOX设备
16:30:10.943 [Thread-61] INFO  c.r.f.t.WebSocketToTcpProxy - [listenForMessages,230] - 📥 TBOX模拟器收到TCP消息: 7E000A000A000102030405060708090A0000303330323030303103007E
16:30:12.532 [http-nio-9201-exec-8] INFO  c.r.f.w.WebSocketServer - [handle4GControlRequest,205] - 🚗 收到4G控车请求: unlock
16:30:12.532 [http-nio-9201-exec-8] INFO  c.r.f.t.TcpControlService - [generateUnlockCommand,30] - 🔓 生成解锁指令: 7E000A000A000102030405060708090A0000303330323030303104007E
16:30:12.532 [http-nio-9201-exec-8] INFO  c.r.f.t.TcpClientHandler - [sendControlCommand,128] - 📤 发送控制指令给TBOX: 7E000A000A000102030405060708090A0000303330323030303104007E
16:30:12.532 [Thread-61] INFO  c.r.f.t.WebSocketToTcpProxy - [listenForMessages,230] - 📥 TBOX模拟器收到TCP消息: 7E000A000A000102030405060708090A0000303330323030303104007E
16:30:12.532 [http-nio-9201-exec-8] INFO  c.r.f.t.TcpClientHandler - [sendCommandToAllTbox,172] - 📡 向所有TBOX客户端发送指令: 7E000A000A000102030405060708090A0000303330323030303104007E
16:30:12.532 [http-nio-9201-exec-8] INFO  c.r.f.t.TcpControlService - [sendUnlockCommandToAllTbox,98] - 🔓 已发送解锁指令给所有TBOX设备
16:30:25.641 [http-nio-9201-exec-7] INFO  c.r.f.w.WebSocketServer - [handle4GControlRequest,205] - 🚗 收到4G控车请求: lock
16:30:25.642 [http-nio-9201-exec-7] INFO  c.r.f.t.TcpControlService - [generateLockCommand,40] - 🔒 生成闭锁指令: 7E000A000A000102030405060708090A0000303330323030303103007E
16:30:25.642 [Thread-61] INFO  c.r.f.t.WebSocketToTcpProxy - [listenForMessages,230] - 📥 TBOX模拟器收到TCP消息: 7E000A000A000102030405060708090A0000303330323030303103007E
16:30:25.642 [http-nio-9201-exec-7] INFO  c.r.f.t.TcpClientHandler - [sendControlCommand,128] - 📤 发送控制指令给TBOX: 7E000A000A000102030405060708090A0000303330323030303103007E
16:30:25.642 [http-nio-9201-exec-7] INFO  c.r.f.t.TcpClientHandler - [sendCommandToAllTbox,172] - 📡 向所有TBOX客户端发送指令: 7E000A000A000102030405060708090A0000303330323030303103007E
16:30:25.642 [http-nio-9201-exec-7] INFO  c.r.f.t.TcpControlService - [sendLockCommandToAllTbox,107] - 🔒 已发送闭锁指令给所有TBOX设备
16:30:34.564 [http-nio-9201-exec-10] INFO  c.r.f.w.WebSocketServer - [onClose,113] - 
 关闭连接 - org.apache.tomcat.websocket.WsSession@2992ba7f
16:30:34.565 [http-nio-9201-exec-10] INFO  c.r.f.w.WebSocketUsers - [removeUserSessionsBySessionId,109] - 
 正在根据sessionId移出用户Session映射 - f
16:30:34.565 [http-nio-9201-exec-10] INFO  c.r.f.w.WebSocketUsers - [removeUserSessionsBySessionId,122] - 
 移出用户Session映射结果 - 成功
16:30:34.565 [http-nio-9201-exec-10] INFO  c.r.f.w.WebSocketUsers - [remove,136] - 
 正在移出用户 - f
16:30:34.566 [http-nio-9201-exec-10] INFO  c.r.f.w.WebSocketUsers - [remove,140] - 
 移出结果 - 成功
16:30:36.309 [http-nio-9201-exec-2] INFO  c.r.f.w.WebSocketServer - [onOpen,100] - 
 建立连接 - org.apache.tomcat.websocket.WsSession@3bbb534b
16:30:36.309 [http-nio-9201-exec-2] INFO  c.r.f.w.WebSocketServer - [onOpen,101] - 
 当前人数 - 1
16:30:36.318 [http-nio-9201-exec-4] INFO  c.r.f.w.WebSocketServer - [onMessage,182] - 
 分享人通讯异常，无法处理 - 10
16:30:40.698 [SpringApplicationShutdownHook] INFO  c.r.f.w.WebSocketServer - [onClose,113] - 
 关闭连接 - org.apache.tomcat.websocket.WsSession@3bbb534b
16:30:40.699 [SpringApplicationShutdownHook] INFO  c.r.f.w.WebSocketUsers - [removeUserSessionsBySessionId,109] - 
 正在根据sessionId移出用户Session映射 - 10
16:30:40.699 [SpringApplicationShutdownHook] INFO  c.r.f.w.WebSocketUsers - [removeUserSessionsBySessionId,122] - 
 移出用户Session映射结果 - 成功
16:30:40.699 [SpringApplicationShutdownHook] INFO  c.r.f.w.WebSocketUsers - [remove,136] - 
 正在移出用户 - 10
16:30:40.700 [SpringApplicationShutdownHook] INFO  c.r.f.w.WebSocketUsers - [remove,140] - 
 移出结果 - 成功
16:30:40.701 [SpringApplicationShutdownHook] INFO  c.r.f.t.WebSocketToTcpProxy - [onClose,50] - 📡 TBOX模拟器WebSocket连接关闭: 1
16:30:40.705 [pool-6-thread-2] INFO  c.r.f.t.TcpClientHandler - [closeConnection,153] - 🔌 TBOX客户端连接已关闭: /127.0.0.1:50920
16:30:40.792 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,95] - De-registering from Nacos Server now...
16:30:41.115 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,115] - De-registration finished.
16:30:41.159 [SpringApplicationShutdownHook] INFO  c.r.f.tcp.TcpServer - [stop,83] - 🛑 TCP服务器已关闭
16:30:41.159 [SpringApplicationShutdownHook] INFO  c.r.f.tcp.TcpServer - [stop,88] - 🛑 TCP线程池已关闭
16:30:41.361 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,217] - dynamic-datasource start closing ....
16:30:41.375 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2174] - {dataSource-1} closing ...
16:30:41.411 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2247] - {dataSource-1} closed
16:30:41.411 [SpringApplicationShutdownHook] INFO  c.b.d.d.d.DefaultDataSourceDestroyer - [destroy,98] - dynamic-datasource close the datasource named [master] success,
16:30:41.412 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,221] - dynamic-datasource all closed success,bye

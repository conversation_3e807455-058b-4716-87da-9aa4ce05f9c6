# 数字钥匙运营系统数据库升级说明

## 📋 升级脚本

**唯一脚本文件**：`sql/数字钥匙运营系统完整升级脚本.sql`

这是一个完整、统一的数据库升级脚本，包含了所有必要的数据库变更。

## 🎯 业务改动总览

### 原系统 → 新系统
- **原系统**：简单的蓝牙密钥管理
- **新系统**：完整的数字钥匙运营管理系统

### 核心改动
1. **扩展钥匙管理** - 在现有基础上增加用户权限控制
2. **新增车辆管理** - 统一管理车辆基础信息和状态  
3. **新增申请管理** - 用户申请→审核→分配的完整流程
4. **新增分配管理** - 车辆与用户的分配记录和状态跟踪
5. **完善运营功能** - 支持运营人员的日常管理工作

## 📊 数据库变更详情

### 【改动1】扩展现有表 `dk_vehicle_bluetooth_keys`
```sql
-- 新增6个字段，支持运营管理功能
ALTER TABLE dk_vehicle_bluetooth_keys ADD COLUMN user_status CHAR(1) DEFAULT '0';          -- 用户权限状态
ALTER TABLE dk_vehicle_bluetooth_keys ADD COLUMN status_update_time DATETIME;              -- 状态更新时间  
ALTER TABLE dk_vehicle_bluetooth_keys ADD COLUMN operator_id BIGINT(20);                   -- 操作人员ID
ALTER TABLE dk_vehicle_bluetooth_keys ADD COLUMN remark VARCHAR(500);                      -- 备注信息
ALTER TABLE dk_vehicle_bluetooth_keys ADD COLUMN vehicle_id BIGINT(20);                    -- 车辆ID关联
ALTER TABLE dk_vehicle_bluetooth_keys ADD COLUMN assignment_id BIGINT(20);                 -- 分配记录ID关联
```

### 【改动2】新增表 `dk_vehicle_info` - 车辆信息管理
```sql
-- 车辆基础信息：VIN码、品牌型号、车牌号、位置、状态等
-- 支持车辆选择功能，替代手动输入VIN码
```

### 【改动3】新增表 `dk_user_application` - 用户申请管理  
```sql
-- 申请信息：申请单号、用户资料、证件照片、审核状态等
-- 支持完整的申请→审核→分配流程
```

### 【改动4】新增表 `dk_vehicle_assignment` - 车辆分配记录
```sql
-- 分配记录：用户车辆关联、分配时间、归还时间、使用状态等
-- 支持分配状态跟踪和管理
```

### 【改动5】性能优化索引
```sql
-- 为新增字段创建索引，提升查询性能
CREATE INDEX idx_user_status ON dk_vehicle_bluetooth_keys(user_status);
CREATE INDEX idx_vehicle_id ON dk_vehicle_bluetooth_keys(vehicle_id);
-- 等等...
```

### 【改动6】数据字典管理
```java
// 采用Java枚举类管理，无需数据库字典表
// DkStatusEnum.java - 统一管理所有状态枚举
// DkDictUtils.java - 提供字典转换工具方法
// DkDictController.java - 提供字典数据API接口
```

### 【改动7】示例数据
```sql
-- 插入5条车辆信息示例数据，便于功能测试
```

## ✅ 兼容性保证

### 对现有业务零影响
- ✅ **只新增，不修改** - 所有操作都是ADD，不删除或修改现有字段
- ✅ **数据完整保留** - 现有所有数据完全不受影响
- ✅ **功能继续工作** - 现有所有查询、插入、更新操作正常
- ✅ **默认值设置** - 新增字段都有合理默认值

### 现有代码兼容性
```sql
-- 这些现有查询完全不受影响
SELECT user_id, vehicle_vin, bluetooth_temp_key FROM dk_vehicle_bluetooth_keys;
INSERT INTO dk_vehicle_bluetooth_keys (user_id, vehicle_vin) VALUES (1, 'VIN123');
UPDATE dk_vehicle_bluetooth_keys SET bluetooth_perm_key = 'key' WHERE user_id = 1;
```

## 🚀 执行方式

### 1. 备份数据库（强烈推荐）
```bash
mysqldump -u root -p digital_key_manage > backup_before_upgrade_$(date +%Y%m%d_%H%M%S).sql
```

### 2. 执行升级脚本
```bash
mysql -u root -p digital_key_manage < "sql/数字钥匙运营系统完整升级脚本.sql"
```

### 3. 验证升级结果
脚本执行后会自动显示升级总结和验证信息。

## 🔄 回滚方案（如需要）

如果需要回滚到原始状态：
```sql
-- 删除新增字段
ALTER TABLE dk_vehicle_bluetooth_keys 
DROP COLUMN user_status,
DROP COLUMN status_update_time,
DROP COLUMN operator_id,
DROP COLUMN remark,
DROP COLUMN vehicle_id,
DROP COLUMN assignment_id;

-- 删除新增表
DROP TABLE IF EXISTS dk_vehicle_info;
DROP TABLE IF EXISTS dk_user_application;  
DROP TABLE IF EXISTS dk_vehicle_assignment;

-- 删除Java枚举类文件（如需要）
-- rm ruoyi-modules/ruoyi-system/src/main/java/com/ruoyi/dk/enums/DkStatusEnum.java
-- rm ruoyi-modules/ruoyi-system/src/main/java/com/ruoyi/dk/utils/DkDictUtils.java
-- rm ruoyi-modules/ruoyi-system/src/main/java/com/ruoyi/dk/controller/DkDictController.java
```

## 📈 升级后效果

### 运营管理能力提升
- 🎯 **完整业务流程** - 申请→审核→分配→使用→管理
- 🚗 **车辆统一管理** - 不再需要手动输入VIN码
- 👥 **用户权限控制** - 支持权限限制和恢复
- 📊 **运营数据支持** - 为工作台提供数据基础

### 用户体验改善  
- 🔍 **车辆可视化选择** - 从列表选择而非输入VIN码
- 📋 **申请状态跟踪** - 完整的申请处理流程
- ⚡ **操作更高效** - 运营人员工作更便捷

## 🎉 总结

这个统一的升级脚本完整地实现了从"简单钥匙管理"到"完整运营系统"的升级，同时确保对现有业务零影响。

**一个脚本，完整升级，安全可靠！**

<mxfile host="65bd71144e">
    <diagram id="digital-key-architecture" name="数字钥匙三端架构图">
        <mxGraphModel dx="1110" dy="1139" grid="1" gridSize="10" guides="1" tooltips="1" connect="1" arrows="1" fold="1" page="1" pageScale="1" pageWidth="1169" pageHeight="827" math="0" shadow="0">
            <root>
                <mxCell id="0"/>
                <mxCell id="1" parent="0"/>
                <mxCell id="title" value="数字钥匙三端架构" style="text;html=1;strokeColor=none;fillColor=none;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=28;fontStyle=1;fontColor=#0066CC;" parent="1" vertex="1">
                    <mxGeometry x="300" y="30" width="570" height="50" as="geometry"/>
                </mxCell>
                <mxCell id="security-center" value="安全层" style="shape=hexagon;perimeter=hexagonPerimeter2;whiteSpace=wrap;html=1;fixedSize=1;size=40;fillColor=#f8cecc;strokeColor=#b85450;gradientColor=#ea6b66;fontSize=18;fontStyle=1;shadow=1;" vertex="1" parent="1">
                    <mxGeometry x="485" y="350" width="200" height="170" as="geometry"/>
                </mxCell>
                <mxCell id="security-content" value="&lt;div style=&quot;font-size: 12px; text-align: left;&quot;&gt;• 端到端加密通信&lt;br&gt;• 多因素身份认证&lt;br&gt;• 密钥生成与分发&lt;br&gt;• 时间同步与防重放&lt;br&gt;• 异常行为监控&lt;br&gt;• 安全区域存储&lt;/div&gt;" style="text;html=1;strokeColor=none;fillColor=none;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=12;" parent="1" vertex="1">
                    <mxGeometry x="505" y="390" width="160" height="100" as="geometry"/>
                </mxCell>
                <mxCell id="cloud-platform" value="钥匙云平台" style="shape=hexagon;perimeter=hexagonPerimeter2;whiteSpace=wrap;html=1;fixedSize=1;size=30;fillColor=#dae8fc;strokeColor=#6c8ebf;gradientColor=#7ea6e0;fontSize=18;fontStyle=1;shadow=1;" vertex="1" parent="1">
                    <mxGeometry x="485" y="130" width="200" height="160" as="geometry"/>
                </mxCell>
                <mxCell id="mobile-side" value="手机端" style="shape=hexagon;perimeter=hexagonPerimeter2;whiteSpace=wrap;html=1;fixedSize=1;size=30;fillColor=#d5e8d4;strokeColor=#82b366;gradientColor=#97d077;fontSize=18;fontStyle=1;shadow=1;" vertex="1" parent="1">
                    <mxGeometry x="245" y="355" width="180" height="160" as="geometry"/>
                </mxCell>
                <mxCell id="car-side" value="车端" style="shape=hexagon;perimeter=hexagonPerimeter2;whiteSpace=wrap;html=1;fixedSize=1;size=30;fillColor=#ffe6cc;strokeColor=#d79b00;gradientColor=#ffce9f;fontSize=18;fontStyle=1;shadow=1;" vertex="1" parent="1">
                    <mxGeometry x="745" y="355" width="180" height="160" as="geometry"/>
                </mxCell>
                <mxCell id="cloud-to-mobile" value="" style="endArrow=classic;startArrow=classic;html=1;rounded=0;curved=1;strokeWidth=3;exitX=0.15;exitY=0.8;exitDx=0;exitDy=0;exitPerimeter=0;entryX=0.85;entryY=0.2;entryDx=0;entryDy=0;entryPerimeter=0;strokeColor=#4D4D4D;shadow=0;" parent="1" source="cloud-platform" target="mobile-side" edge="1">
                    <mxGeometry width="50" height="50" relative="1" as="geometry">
                        <mxPoint x="480" y="290" as="sourcePoint"/>
                        <mxPoint x="310" y="350" as="targetPoint"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="cloud-to-mobile-protocol" value="HTTPS/API" style="text;html=1;strokeColor=#666666;fillColor=#f5f5f5;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=1;fontSize=11;fontStyle=1;gradientColor=#b3b3b3;" vertex="1" parent="1">
                    <mxGeometry x="355" y="260" width="80" height="20" as="geometry"/>
                </mxCell>
                <mxCell id="cloud-to-mobile-functions" value="钥匙授权、身份认证、配置同步" style="text;html=1;strokeColor=none;fillColor=none;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=10;fontColor=#4D4D4D;" vertex="1" parent="1">
                    <mxGeometry x="300" y="280" width="160" height="20" as="geometry"/>
                </mxCell>
                <mxCell id="cloud-to-car" value="" style="endArrow=classic;startArrow=classic;html=1;rounded=0;curved=1;strokeWidth=3;entryX=0.15;entryY=0.2;entryDx=0;entryDy=0;entryPerimeter=0;exitX=0.85;exitY=0.8;exitDx=0;exitDy=0;exitPerimeter=0;strokeColor=#4D4D4D;" edge="1" parent="1" source="cloud-platform" target="car-side">
                    <mxGeometry width="50" height="50" relative="1" as="geometry">
                        <mxPoint x="660" y="290" as="sourcePoint"/>
                        <mxPoint x="780" y="350" as="targetPoint"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="cloud-to-car-protocol" value="MQTT/WebSocket" style="text;html=1;strokeColor=#666666;fillColor=#f5f5f5;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=1;fontSize=11;fontStyle=1;gradientColor=#b3b3b3;" vertex="1" parent="1">
                    <mxGeometry x="735" y="260" width="110" height="20" as="geometry"/>
                </mxCell>
                <mxCell id="cloud-to-car-functions" value="车辆绑定、钥匙权限更新、远程控制" style="text;html=1;strokeColor=none;fillColor=none;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=10;fontColor=#4D4D4D;" vertex="1" parent="1">
                    <mxGeometry x="710" y="280" width="180" height="20" as="geometry"/>
                </mxCell>
                <mxCell id="mobile-to-car" value="" style="endArrow=classic;startArrow=classic;html=1;rounded=0;curved=1;strokeWidth=3;entryX=0;entryY=0.5;entryDx=0;entryDy=0;exitX=1;exitY=0.5;exitDx=0;exitDy=0;strokeColor=#4D4D4D;" edge="1" parent="1" source="mobile-side" target="car-side">
                    <mxGeometry width="50" height="50" relative="1" as="geometry">
                        <mxPoint x="430" y="435" as="sourcePoint"/>
                        <mxPoint x="740" y="435" as="targetPoint"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="mobile-to-car-protocol" value="蓝牙/RSSI" style="text;html=1;strokeColor=#666666;fillColor=#f5f5f5;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=1;fontSize=11;fontStyle=1;gradientColor=#b3b3b3;" vertex="1" parent="1">
                    <mxGeometry x="535" y="410" width="100" height="20" as="geometry"/>
                </mxCell>
                <mxCell id="mobile-to-car-functions" value="暗号交换、距离感知、车辆控制指令" style="text;html=1;strokeColor=none;fillColor=none;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=10;fontColor=#4D4D4D;" vertex="1" parent="1">
                    <mxGeometry x="505" y="430" width="160" height="20" as="geometry"/>
                </mxCell>
                <mxCell id="cloud-modules" value="&lt;ul style=&quot;font-size: 11px; text-align: left; margin: 0px; padding-left: 20px;&quot;&gt;&lt;li&gt;钥匙生命周期管理&lt;/li&gt;&lt;li&gt;车辆关联服务&lt;/li&gt;&lt;li&gt;安全认证中心&lt;/li&gt;&lt;li&gt;密钥管理系统&lt;/li&gt;&lt;li&gt;统一接口服务&lt;/li&gt;&lt;li&gt;时间服务器 (NTP/PTP)&lt;/li&gt;&lt;li&gt;异常监控与处理&lt;/li&gt;&lt;/ul&gt;" style="rounded=1;whiteSpace=wrap;html=1;arcSize=5;fillColor=#f5f5f5;strokeColor=#666666;shadow=1;gradientColor=#b3b3b3;fontSize=11;" vertex="1" parent="1">
                    <mxGeometry x="485" y="550" width="200" height="150" as="geometry"/>
                </mxCell>
                <mxCell id="mobile-modules" value="&lt;ul style=&quot;font-size: 11px; text-align: left; margin: 0px; padding-left: 20px;&quot;&gt;&lt;li&gt;钥匙管理模块&lt;/li&gt;&lt;li&gt;车辆控制模块&lt;/li&gt;&lt;li&gt;安全存储模块&lt;/li&gt;&lt;li&gt;蓝牙通信模块&lt;/li&gt;&lt;li&gt;时间同步模块&lt;/li&gt;&lt;li&gt;暗号交换模块&lt;/li&gt;&lt;li&gt;智能场景管理&lt;/li&gt;&lt;li&gt;标定模块&lt;/li&gt;&lt;/ul&gt;" style="rounded=1;whiteSpace=wrap;html=1;arcSize=5;fillColor=#f5f5f5;strokeColor=#666666;shadow=1;gradientColor=#b3b3b3;fontSize=11;" vertex="1" parent="1">
                    <mxGeometry x="245" y="550" width="180" height="140" as="geometry"/>
                </mxCell>
                <mxCell id="car-modules" value="&lt;ul style=&quot;font-size: 11px; text-align: left; margin: 0px; padding-left: 20px;&quot;&gt;&lt;li&gt;蓝牙通信模块&lt;/li&gt;&lt;li&gt;钥匙验证模块&lt;/li&gt;&lt;li&gt;指令执行模块&lt;/li&gt;&lt;li&gt;安全存储模块&lt;/li&gt;&lt;li&gt;远程通信模块&lt;/li&gt;&lt;li&gt;时间同步模块&lt;/li&gt;&lt;li&gt;暗号交换模块&lt;/li&gt;&lt;li&gt;用户行为分析模块&lt;/li&gt;&lt;/ul&gt;" style="rounded=1;whiteSpace=wrap;html=1;arcSize=5;fillColor=#f5f5f5;strokeColor=#666666;shadow=1;gradientColor=#b3b3b3;fontSize=11;" vertex="1" parent="1">
                    <mxGeometry x="745" y="550" width="180" height="140" as="geometry"/>
                </mxCell>
                <mxCell id="modules-title-cloud" value="云平台功能模块" style="text;html=1;strokeColor=none;fillColor=none;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=14;fontStyle=1;" vertex="1" parent="1">
                    <mxGeometry x="485" y="530" width="200" height="20" as="geometry"/>
                </mxCell>
                <mxCell id="modules-title-mobile" value="手机端功能模块" style="text;html=1;strokeColor=none;fillColor=none;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=14;fontStyle=1;" vertex="1" parent="1">
                    <mxGeometry x="245" y="530" width="180" height="20" as="geometry"/>
                </mxCell>
                <mxCell id="modules-title-car" value="车端功能模块" style="text;html=1;strokeColor=none;fillColor=none;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=14;fontStyle=1;" vertex="1" parent="1">
                    <mxGeometry x="745" y="530" width="180" height="20" as="geometry"/>
                </mxCell>
                <mxCell id="modules-to-cloud" value="" style="endArrow=classic;html=1;rounded=0;exitX=0.5;exitY=0;exitDx=0;exitDy=0;entryX=0.5;entryY=1;entryDx=0;entryDy=0;strokeWidth=1;dashed=1;dashPattern=1 1;strokeColor=#666666;" edge="1" parent="1" source="cloud-modules" target="cloud-platform">
                    <mxGeometry width="50" height="50" relative="1" as="geometry">
                        <mxPoint x="585" y="545" as="sourcePoint"/>
                        <mxPoint x="585" y="290" as="targetPoint"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="modules-to-mobile" value="" style="endArrow=classic;html=1;rounded=0;exitX=0.5;exitY=0;exitDx=0;exitDy=0;entryX=0.5;entryY=1;entryDx=0;entryDy=0;strokeWidth=1;dashed=1;dashPattern=1 1;strokeColor=#666666;" edge="1" parent="1" source="mobile-modules" target="mobile-side">
                    <mxGeometry width="50" height="50" relative="1" as="geometry">
                        <mxPoint x="245" y="545" as="sourcePoint"/>
                        <mxPoint x="245" y="465" as="targetPoint"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="modules-to-car" value="" style="endArrow=classic;html=1;rounded=0;exitX=0.5;exitY=0;exitDx=0;exitDy=0;entryX=0.5;entryY=1;entryDx=0;entryDy=0;strokeWidth=1;dashed=1;dashPattern=1 1;strokeColor=#666666;" edge="1" parent="1" source="car-modules" target="car-side">
                    <mxGeometry width="50" height="50" relative="1" as="geometry">
                        <mxPoint x="835" y="545" as="sourcePoint"/>
                        <mxPoint x="835" y="465" as="targetPoint"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="interactions-title" value="核心交互流程" style="text;html=1;strokeColor=none;fillColor=none;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=16;fontStyle=1;fontColor=#0066CC;" vertex="1" parent="1">
                    <mxGeometry x="485" y="730" width="200" height="20" as="geometry"/>
                </mxCell>
                <mxCell id="interactions-content" value="&lt;ol style=&quot;font-size: 12px; text-align: left;&quot;&gt;&lt;li&gt;&lt;b&gt;钥匙授权流程&lt;/b&gt;：云平台→手机端→车端&lt;/li&gt;&lt;li&gt;&lt;b&gt;无感操作流程&lt;/b&gt;：手机端↔车端(RSSI距离感知)&lt;/li&gt;&lt;li&gt;&lt;b&gt;安全校验流程&lt;/b&gt;：暗号交换+时间同步&lt;/li&gt;&lt;li&gt;&lt;b&gt;远程控制流程&lt;/b&gt;：云平台→车端&lt;/li&gt;&lt;li&gt;&lt;b&gt;状态反馈流程&lt;/b&gt;：车端→云平台→手机端&lt;/li&gt;&lt;/ol&gt;" style="rounded=1;whiteSpace=wrap;html=1;arcSize=5;fillColor=#f5f5f5;strokeColor=#666666;shadow=1;gradientColor=#b3b3b3;align=center;verticalAlign=top;spacingLeft=5;spacingRight=5;spacingTop=5;" vertex="1" parent="1">
                    <mxGeometry x="435" y="770" width="300" height="140" as="geometry"/>
                </mxCell>
            </root>
        </mxGraphModel>
    </diagram>
</mxfile>
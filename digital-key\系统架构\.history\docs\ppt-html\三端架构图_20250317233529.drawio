<mxfile host="65bd71144e">
    <diagram id="digital-key-architecture" name="数字钥匙三端架构图">
        <mxGraphModel dx="1110" dy="1966" grid="1" gridSize="10" guides="1" tooltips="1" connect="1" arrows="1" fold="1" page="1" pageScale="1" pageWidth="1169" pageHeight="827" math="0" shadow="0">
            <root>
                <mxCell id="0"/>
                <mxCell id="1" parent="0"/>
                <!-- 背景 -->
                <mxCell id="background" value="" style="rounded=0;whiteSpace=wrap;html=1;fillColor=#f9f9f9;strokeColor=none;" vertex="1" parent="1">
                    <mxGeometry y="-120" width="1169" height="940" as="geometry"/>
                </mxCell>
                
                <!-- 标题 -->
                <mxCell id="title" value="数字钥匙三端架构" style="text;html=1;strokeColor=none;fillColor=none;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=28;fontStyle=1;fontColor=#0066CC;" parent="1" vertex="1">
                    <mxGeometry x="300" y="-90" width="570" height="50" as="geometry"/>
                </mxCell>
                
                <!-- 钥匙云平台 -->
                <mxCell id="cloud-platform" value="钥匙云平台" style="shape=hexagon;perimeter=hexagonPerimeter2;whiteSpace=wrap;html=1;fixedSize=1;size=30;fillColor=#dae8fc;strokeColor=#6c8ebf;gradientColor=#7ea6e0;fontSize=18;fontStyle=1;shadow=1;" vertex="1" parent="1">
                    <mxGeometry x="485" y="40" width="200" height="160" as="geometry"/>
                </mxCell>
                
                <!-- 云平台职责描述 -->
                <mxCell id="cloud-responsibilities" value="&lt;div style=&quot;text-align: left; font-size: 12px;&quot;&gt;&lt;b&gt;核心职责：&lt;/b&gt;&lt;/div&gt;&lt;div style=&quot;text-align: left; font-size: 12px;&quot;&gt;• 管理钥匙全生命周期&lt;/div&gt;&lt;div style=&quot;text-align: left; font-size: 12px;&quot;&gt;• 执行用户身份认证&lt;/div&gt;&lt;div style=&quot;text-align: left; font-size: 12px;&quot;&gt;• 安全分发密钥与权限&lt;/div&gt;&lt;div style=&quot;text-align: left; font-size: 12px;&quot;&gt;• 提供精准时间基准&lt;/div&gt;&lt;div style=&quot;text-align: left; font-size: 12px;&quot;&gt;• 监控系统安全状态&lt;/div&gt;" style="text;html=1;strokeColor=none;fillColor=none;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=12;" vertex="1" parent="1">
                    <mxGeometry x="487.5" y="80" width="195" height="90" as="geometry"/>
                </mxCell>
                
                <!-- 手机端 -->
                <mxCell id="mobile-side" value="手机端" style="shape=hexagon;perimeter=hexagonPerimeter2;whiteSpace=wrap;html=1;fixedSize=1;size=30;fillColor=#d5e8d4;strokeColor=#82b366;gradientColor=#97d077;fontSize=18;fontStyle=1;shadow=1;" vertex="1" parent="1">
                    <mxGeometry x="245" y="270" width="180" height="160" as="geometry"/>
                </mxCell>
                
                <!-- 手机端职责描述 -->
                <mxCell id="mobile-responsibilities" value="&lt;div style=&quot;text-align: left; font-size: 12px;&quot;&gt;&lt;b&gt;核心职责：&lt;/b&gt;&lt;/div&gt;&lt;div style=&quot;text-align: left; font-size: 12px;&quot;&gt;• 接收并存储数字钥匙&lt;/div&gt;&lt;div style=&quot;text-align: left; font-size: 12px;&quot;&gt;• 与车端协商临时暗号&lt;/div&gt;&lt;div style=&quot;text-align: left; font-size: 12px;&quot;&gt;• 发送车辆控制指令&lt;/div&gt;&lt;div style=&quot;text-align: left; font-size: 12px;&quot;&gt;• 校准RSSI与距离关系&lt;/div&gt;&lt;div style=&quot;text-align: left; font-size: 12px;&quot;&gt;• 执行无感接近唤醒&lt;/div&gt;" style="text;html=1;strokeColor=none;fillColor=none;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=12;" vertex="1" parent="1">
                    <mxGeometry x="247.5" y="310" width="175" height="90" as="geometry"/>
                </mxCell>
                
                <!-- 车端 -->
                <mxCell id="car-side" value="车端" style="shape=hexagon;perimeter=hexagonPerimeter2;whiteSpace=wrap;html=1;fixedSize=1;size=30;fillColor=#ffe6cc;strokeColor=#d79b00;gradientColor=#ffce9f;fontSize=18;fontStyle=1;shadow=1;" vertex="1" parent="1">
                    <mxGeometry x="745" y="270" width="180" height="160" as="geometry"/>
                </mxCell>
                
                <!-- 车端职责描述 -->
                <mxCell id="car-responsibilities" value="&lt;div style=&quot;text-align: left; font-size: 12px;&quot;&gt;&lt;b&gt;核心职责：&lt;/b&gt;&lt;/div&gt;&lt;div style=&quot;text-align: left; font-size: 12px;&quot;&gt;• 验证钥匙有效性&lt;/div&gt;&lt;div style=&quot;text-align: left; font-size: 12px;&quot;&gt;• 执行车辆控制命令&lt;/div&gt;&lt;div style=&quot;text-align: left; font-size: 12px;&quot;&gt;• 计算用户接近距离&lt;/div&gt;&lt;div style=&quot;text-align: left; font-size: 12px;&quot;&gt;• 发送加密蓝牙广播&lt;/div&gt;&lt;div style=&quot;text-align: left; font-size: 12px;&quot;&gt;• 监控异常操作行为&lt;/div&gt;" style="text;html=1;strokeColor=none;fillColor=none;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=12;" vertex="1" parent="1">
                    <mxGeometry x="747.5" y="310" width="175" height="90" as="geometry"/>
                </mxCell>
                
                <!-- 安全层 -->
                <mxCell id="security-center" value="安全层" style="shape=hexagon;perimeter=hexagonPerimeter2;whiteSpace=wrap;html=1;fixedSize=1;size=40;fillColor=#f8cecc;strokeColor=#b85450;gradientColor=#ea6b66;fontSize=18;fontStyle=1;shadow=1;" parent="1" vertex="1">
                    <mxGeometry x="485" y="470" width="200" height="170" as="geometry"/>
                </mxCell>
                
                <mxCell id="security-content" value="&lt;div style=&quot;font-size: 12px; text-align: left;&quot;&gt;• 端到端加密通信&lt;br&gt;• 多因素身份认证&lt;br&gt;• 密钥生成与分发&lt;br&gt;• 时间同步与防重放&lt;br&gt;• 异常行为监控&lt;br&gt;• 安全区域存储&lt;/div&gt;" style="text;html=1;strokeColor=none;fillColor=none;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=12;" parent="1" vertex="1">
                    <mxGeometry x="505" y="510" width="160" height="100" as="geometry"/>
                </mxCell>
                
                <!-- 连接线和交互描述 -->
                <!-- 云平台到手机端 -->
                <mxCell id="cloud-to-mobile" value="" style="endArrow=classic;startArrow=classic;html=1;rounded=0;curved=1;strokeWidth=3;exitX=0.15;exitY=0.8;exitDx=0;exitDy=0;exitPerimeter=0;entryX=0.85;entryY=0.2;entryDx=0;entryDy=0;entryPerimeter=0;strokeColor=#4D4D4D;shadow=0;" parent="1" source="cloud-platform" target="mobile-side" edge="1">
                    <mxGeometry width="50" height="50" relative="1" as="geometry">
                        <mxPoint x="480" y="200" as="sourcePoint"/>
                        <mxPoint x="310" y="260" as="targetPoint"/>
                    </mxGeometry>
                </mxCell>
                
                <mxCell id="cloud-to-mobile-protocol" value="HTTPS/API" style="text;html=1;strokeColor=#666666;fillColor=#f5f5f5;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=1;fontSize=11;fontStyle=1;gradientColor=#b3b3b3;" parent="1" vertex="1">
                    <mxGeometry x="355" y="175" width="80" height="20" as="geometry"/>
                </mxCell>
                
                <mxCell id="cloud-mobile-interaction" value="" style="rounded=1;whiteSpace=wrap;html=1;fontSize=11;fillColor=#f5f5f5;strokeColor=#666666;gradientColor=#b3b3b3;shadow=1;" vertex="1" parent="1">
                    <mxGeometry x="255" y="200" width="235" height="50" as="geometry"/>
                </mxCell>
                
                <mxCell id="cloud-mobile-desc" value="&lt;div style=&quot;text-align: left; font-size: 11px;&quot;&gt;&lt;b&gt;交互内容：&lt;/b&gt;&lt;/div&gt;&lt;div style=&quot;text-align: left; font-size: 11px;&quot;&gt;• 创建钥匙 • 授予/撤销权限 • 用户认证&lt;/div&gt;&lt;div style=&quot;text-align: left; font-size: 11px;&quot;&gt;• 同步车辆列表 • 获取使用记录 • 时间校准&lt;/div&gt;" style="text;html=1;strokeColor=none;fillColor=none;align=left;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=11;" vertex="1" parent="1">
                    <mxGeometry x="260" y="205" width="225" height="40" as="geometry"/>
                </mxCell>
                
                <!-- 云平台到车端 -->
                <mxCell id="cloud-to-car" value="" style="endArrow=classic;startArrow=classic;html=1;rounded=0;curved=1;strokeWidth=3;entryX=0.15;entryY=0.2;entryDx=0;entryDy=0;entryPerimeter=0;exitX=0.85;exitY=0.8;exitDx=0;exitDy=0;exitPerimeter=0;strokeColor=#4D4D4D;" parent="1" source="cloud-platform" target="car-side" edge="1">
                    <mxGeometry width="50" height="50" relative="1" as="geometry">
                        <mxPoint x="660" y="200" as="sourcePoint"/>
                        <mxPoint x="780" y="260" as="targetPoint"/>
                    </mxGeometry>
                </mxCell>
                
                <mxCell id="cloud-to-car-protocol" value="MQTT/WebSocket" style="text;html=1;strokeColor=#666666;fillColor=#f5f5f5;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=1;fontSize=11;fontStyle=1;gradientColor=#b3b3b3;" parent="1" vertex="1">
                    <mxGeometry x="735" y="175" width="110" height="20" as="geometry"/>
                </mxCell>
                
                <mxCell id="cloud-car-interaction" value="" style="rounded=1;whiteSpace=wrap;html=1;fontSize=11;fillColor=#f5f5f5;strokeColor=#666666;gradientColor=#b3b3b3;shadow=1;" vertex="1" parent="1">
                    <mxGeometry x="680" y="200" width="235" height="50" as="geometry"/>
                </mxCell>
                
                <mxCell id="cloud-car-desc" value="&lt;div style=&quot;text-align: left; font-size: 11px;&quot;&gt;&lt;b&gt;交互内容：&lt;/b&gt;&lt;/div&gt;&lt;div style=&quot;text-align: left; font-size: 11px;&quot;&gt;• 车辆身份验证 • 钥匙权限同步 • 远程控制&lt;/div&gt;&lt;div style=&quot;text-align: left; font-size: 11px;&quot;&gt;• 安全配置更新 • 上报使用状态 • 时间同步&lt;/div&gt;" style="text;html=1;strokeColor=none;fillColor=none;align=left;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=11;" vertex="1" parent="1">
                    <mxGeometry x="685" y="205" width="225" height="40" as="geometry"/>
                </mxCell>
                
                <!-- 手机端到车端 -->
                <mxCell id="mobile-to-car" value="" style="endArrow=classic;startArrow=classic;html=1;rounded=0;curved=1;strokeWidth=3;entryX=0;entryY=0.5;entryDx=0;entryDy=0;exitX=1;exitY=0.5;exitDx=0;exitDy=0;strokeColor=#4D4D4D;" parent="1" source="mobile-side" target="car-side" edge="1">
                    <mxGeometry width="50" height="50" relative="1" as="geometry">
                        <mxPoint x="430" y="350" as="sourcePoint"/>
                        <mxPoint x="740" y="350" as="targetPoint"/>
                    </mxGeometry>
                </mxCell>
                
                <mxCell id="mobile-car-interaction" value="" style="rounded=1;whiteSpace=wrap;html=1;fontSize=11;fillColor=#f5f5f5;strokeColor=#666666;gradientColor=#b3b3b3;shadow=1;" vertex="1" parent="1">
                    <mxGeometry x="467.5" y="310" width="235" height="85" as="geometry"/>
                </mxCell>
                
                <mxCell id="mobile-to-car-protocol" value="蓝牙/RSSI" style="text;html=1;strokeColor=#666666;fillColor=#f5f5f5;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=1;fontSize=11;fontStyle=1;gradientColor=#b3b3b3;" parent="1" vertex="1">
                    <mxGeometry x="535" y="280" width="100" height="20" as="geometry"/>
                </mxCell>
                
                <mxCell id="mobile-car-desc" value="&lt;div style=&quot;text-align: left; font-size: 11px;&quot;&gt;&lt;b&gt;交互内容：&lt;/b&gt;&lt;/div&gt;&lt;div style=&quot;text-align: left; font-size: 11px;&quot;&gt;• 暗号交换：通过挑战-响应方式建立临时会话&lt;/div&gt;&lt;div style=&quot;text-align: left; font-size: 11px;&quot;&gt;• 距离感知：基于RSSI信号强度计算距离&lt;/div&gt;&lt;div style=&quot;text-align: left; font-size: 11px;&quot;&gt;• 控制指令：发送/接收车辆解锁、启动等指令&lt;/div&gt;&lt;div style=&quot;text-align: left; font-size: 11px;&quot;&gt;• 状态同步：获取车辆状态变化实时反馈&lt;/div&gt;" style="text;html=1;strokeColor=none;fillColor=none;align=left;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=11;" vertex="1" parent="1">
                    <mxGeometry x="472.5" y="315" width="225" height="75" as="geometry"/>
                </mxCell>
                
                <!-- 典型交互流程框 -->
                <mxCell id="interactions-title" value="典型交互流程" style="text;html=1;strokeColor=none;fillColor=none;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=16;fontStyle=1;fontColor=#0066CC;" parent="1" vertex="1">
                    <mxGeometry x="485" y="660" width="200" height="20" as="geometry"/>
                </mxCell>
                
                <mxCell id="interactions-content" value="" style="rounded=1;whiteSpace=wrap;html=1;arcSize=5;fillColor=#f5f5f5;strokeColor=#666666;shadow=1;gradientColor=#b3b3b3;align=center;verticalAlign=top;spacingLeft=5;spacingRight=5;spacingTop=5;" parent="1" vertex="1">
                    <mxGeometry x="290" y="695" width="600" height="105" as="geometry"/>
                </mxCell>
                
                <!-- 流程1：钥匙授权 -->
                <mxCell id="flow1-title" value="1. 钥匙授权流程" style="text;html=1;strokeColor=none;fillColor=none;align=left;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=12;fontStyle=1;" vertex="1" parent="1">
                    <mxGeometry x="300" y="705" width="110" height="20" as="geometry"/>
                </mxCell>
                
                <mxCell id="flow1-cloud" value="云平台" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#dae8fc;strokeColor=#6c8ebf;fontSize=11;fontStyle=1;" vertex="1" parent="1">
                    <mxGeometry x="300" y="725" width="60" height="25" as="geometry"/>
                </mxCell>
                
                <mxCell id="flow1-arrow1" value="" style="endArrow=classic;html=1;rounded=0;fontSize=11;exitX=1;exitY=0.5;exitDx=0;exitDy=0;entryX=0;entryY=0.5;entryDx=0;entryDy=0;" edge="1" parent="1" source="flow1-cloud" target="flow1-mobile">
                    <mxGeometry width="50" height="50" relative="1" as="geometry">
                        <mxPoint x="370" y="737.5" as="sourcePoint"/>
                        <mxPoint x="400" y="737.5" as="targetPoint"/>
                    </mxGeometry>
                </mxCell>
                
                <mxCell id="flow1-mobile" value="手机端" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#d5e8d4;strokeColor=#82b366;fontSize=11;fontStyle=1;" vertex="1" parent="1">
                    <mxGeometry x="400" y="725" width="60" height="25" as="geometry"/>
                </mxCell>
                
                <mxCell id="flow1-arrow2" value="" style="endArrow=classic;html=1;rounded=0;fontSize=11;exitX=1;exitY=0.5;exitDx=0;exitDy=0;entryX=0;entryY=0.5;entryDx=0;entryDy=0;" edge="1" parent="1" source="flow1-mobile" target="flow1-car">
                    <mxGeometry width="50" height="50" relative="1" as="geometry">
                        <mxPoint x="470" y="737.5" as="sourcePoint"/>
                        <mxPoint x="500" y="737.5" as="targetPoint"/>
                    </mxGeometry>
                </mxCell>
                
                <mxCell id="flow1-car" value="车端" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#ffe6cc;strokeColor=#d79b00;fontSize=11;fontStyle=1;" vertex="1" parent="1">
                    <mxGeometry x="500" y="725" width="60" height="25" as="geometry"/>
                </mxCell>
                
                <mxCell id="flow1-text" value="创建钥匙、设置权限" style="text;html=1;strokeColor=none;fillColor=none;align=left;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=10;" vertex="1" parent="1">
                    <mxGeometry x="300" y="750" width="110" height="20" as="geometry"/>
                </mxCell>
                
                <mxCell id="flow1-text2" value="钥匙验证、本地存储" style="text;html=1;strokeColor=none;fillColor=none;align=left;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=10;" vertex="1" parent="1">
                    <mxGeometry x="400" y="750" width="110" height="20" as="geometry"/>
                </mxCell>
                
                <mxCell id="flow1-text3" value="钥匙验证与使用" style="text;html=1;strokeColor=none;fillColor=none;align=left;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=10;" vertex="1" parent="1">
                    <mxGeometry x="500" y="750" width="90" height="20" as="geometry"/>
                </mxCell>
                
                <!-- 流程2：无感操作 -->
                <mxCell id="flow2-title" value="2. 无感操作流程" style="text;html=1;strokeColor=none;fillColor=none;align=left;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=12;fontStyle=1;" vertex="1" parent="1">
                    <mxGeometry x="590" y="705" width="110" height="20" as="geometry"/>
                </mxCell>
                
                <mxCell id="flow2-mobile" value="手机端" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#d5e8d4;strokeColor=#82b366;fontSize=11;fontStyle=1;" vertex="1" parent="1">
                    <mxGeometry x="590" y="725" width="60" height="25" as="geometry"/>
                </mxCell>
                
                <mxCell id="flow2-arrow" value="" style="endArrow=classic;startArrow=classic;html=1;rounded=0;fontSize=11;exitX=1;exitY=0.5;exitDx=0;exitDy=0;entryX=0;entryY=0.5;entryDx=0;entryDy=0;" edge="1" parent="1" source="flow2-mobile" target="flow2-car">
                    <mxGeometry width="50" height="50" relative="1" as="geometry">
                        <mxPoint x="660" y="737.5" as="sourcePoint"/>
                        <mxPoint x="690" y="737.5" as="targetPoint"/>
                    </mxGeometry>
                </mxCell>
                
                <mxCell id="flow2-car" value="车端" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#ffe6cc;strokeColor=#d79b00;fontSize=11;fontStyle=1;" vertex="1" parent="1">
                    <mxGeometry x="690" y="725" width="60" height="25" as="geometry"/>
                </mxCell>
                
                <mxCell id="flow2-text" value="RSSI信号强度测量" style="text;html=1;strokeColor=none;fillColor=none;align=left;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=10;" vertex="1" parent="1">
                    <mxGeometry x="590" y="750" width="110" height="20" as="geometry"/>
                </mxCell>
                
                <mxCell id="flow2-text2" value="根据距离触发解锁" style="text;html=1;strokeColor=none;fillColor=none;align=left;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=10;" vertex="1" parent="1">
                    <mxGeometry x="690" y="750" width="110" height="20" as="geometry"/>
                </mxCell>
                
                <!-- 流程3：远程控制 -->
                <mxCell id="flow3-title" value="3. 远程控制流程" style="text;html=1;strokeColor=none;fillColor=none;align=left;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=12;fontStyle=1;" vertex="1" parent="1">
                    <mxGeometry x="790" y="705" width="110" height="20" as="geometry"/>
                </mxCell>
                
                <mxCell id="flow3-cloud" value="云平台" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#dae8fc;strokeColor=#6c8ebf;fontSize=11;fontStyle=1;" vertex="1" parent="1">
                    <mxGeometry x="790" y="725" width="60" height="25" as="geometry"/>
                </mxCell>
                
                <mxCell id="flow3-arrow" value="" style="endArrow=classic;html=1;rounded=0;fontSize=11;exitX=1;exitY=0.5;exitDx=0;exitDy=0;entryX=0;entryY=0.5;entryDx=0;entryDy=0;" edge="1" parent="1" source="flow3-cloud" target="flow3-car">
                    <mxGeometry width="50" height="50" relative="1" as="geometry">
                        <mxPoint x="860" y="737.5" as="sourcePoint"/>
                        <mxPoint x="890" y="737.5" as="targetPoint"/>
                    </mxGeometry>
                </mxCell>
                
                <mxCell id="flow3-car" value="车端" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#ffe6cc;strokeColor=#d79b00;fontSize=11;fontStyle=1;" vertex="1" parent="1">
                    <mxGeometry x="890" y="725" width="60" height="25" as="geometry"/>
                </mxCell>
                
                <mxCell id="flow3-text" value="指令发送与验证" style="text;html=1;strokeColor=none;fillColor=none;align=left;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=10;" vertex="1" parent="1">
                    <mxGeometry x="790" y="750" width="90" height="20" as="geometry"/>
                </mxCell>
                
                <mxCell id="flow3-text2" value="接收并执行指令" style="text;html=1;strokeColor=none;fillColor=none;align=left;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=10;" vertex="1" parent="1">
                    <mxGeometry x="890" y="750" width="90" height="20" as="geometry"/>
                </mxCell>
            </root>
        </mxGraphModel>
    </diagram>
</mxfile>
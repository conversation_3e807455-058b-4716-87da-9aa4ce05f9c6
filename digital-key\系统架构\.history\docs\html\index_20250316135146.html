<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>数字钥匙系统技术架构</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        :root {
            --primary-color: #1976d2;
            --primary-light: #4791db;
            --primary-dark: #115293;
            --accent-color: #7B1FA2;
            --success-color: #4caf50;
            --warning-color: #ff9800;
            --danger-color: #f44336;
            --gray-100: #f5f5f5;
            --gray-200: #eeeeee;
            --gray-300: #e0e0e0;
            --gray-800: #424242;
            --text-primary: #212121;
            --text-secondary: #757575;
            --border-radius: 8px;
            --box-shadow: 0 4px 20px rgba(0,0,0,0.1);
            --transition: all 0.3s ease;
        }
        
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: "Microsoft YaHei", Arial, sans-serif;
            line-height: 1.6;
            color: var(--text-primary);
            background-color: #f9f9f9;
            padding: 0;
            margin: 0;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 20px;
        }
        
        header {
            background: linear-gradient(135deg, var(--primary-color), var(--accent-color));
            color: white;
            padding: 40px 0;
            text-align: center;
            margin-bottom: 40px;
            position: relative;
            overflow: hidden;
        }
        
        header::before {
            content: "";
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background-image: url('data:image/svg+xml;utf8,<svg width="100" height="100" viewBox="0 0 100 100" xmlns="http://www.w3.org/2000/svg"><path d="M0 0L100 100M30 0L100 70M60 0L100 40M0 30L70 100M0 60L40 100" stroke="rgba(255,255,255,0.1)" stroke-width="1"/></svg>');
            z-index: 0;
        }
        
        header .container {
            position: relative;
            z-index: 1;
        }
        
        .logo-placeholder {
            background-color: rgba(255, 255, 255, 0.2);
            width: 100px;
            height: 100px;
            margin: 0 auto 20px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 24px;
        }
        
        h1, h2, h3, h4 {
            font-weight: 600;
            line-height: 1.3;
        }
        
        h1 {
            font-size: 2.5rem;
            margin-bottom: 10px;
            color: white;
        }
        
        h2 {
            font-size: 2rem;
            color: var(--primary-color);
            margin: 60px 0 30px;
            padding-bottom: 10px;
            border-bottom: 3px solid var(--primary-light);
            position: relative;
            display: inline-block;
        }
        
        h2::after {
            content: "";
            position: absolute;
            bottom: -3px;
            left: 0;
            width: 60px;
            height: 3px;
            background-color: var(--accent-color);
        }
        
        h3 {
            font-size: 1.5rem;
            color: var(--primary-dark);
            margin: 30px 0 20px;
            display: flex;
            align-items: center;
        }
        
        h3::before {
            content: "";
            display: inline-block;
            width: 8px;
            height: 20px;
            background-color: var(--accent-color);
            margin-right: 12px;
            border-radius: 4px;
        }
        
        h4 {
            font-size: 1.2rem;
            color: var(--accent-color);
            margin: 20px 0 15px;
        }
        
        .header-subtitle {
            font-size: 1.2rem;
            opacity: 0.9;
            max-width: 700px;
            margin: 0 auto;
        }
        
        nav {
            background-color: white;
            border-radius: var(--border-radius);
            padding: 20px;
            margin-bottom: 40px;
            box-shadow: var(--box-shadow);
        }
        
        nav h3 {
            margin-top: 0;
            margin-bottom: 20px;
            color: var(--primary-dark);
            text-align: center;
        }
        
        nav ul {
            list-style: none;
            padding: 0;
            display: flex;
            flex-wrap: wrap;
            gap: 10px;
            justify-content: center;
        }
        
        nav li {
            margin: 5px;
        }
        
        nav a {
            display: inline-block;
            padding: 10px 15px;
            color: var(--primary-color);
            text-decoration: none;
            border-radius: var(--border-radius);
            background-color: var(--gray-100);
            transition: var(--transition);
            border: 1px solid var(--gray-300);
        }
        
        nav a:hover {
            background-color: var(--primary-color);
            color: white;
            transform: translateY(-2px);
        }
        
        section {
            background-color: white;
            border-radius: var(--border-radius);
            padding: 30px;
            margin-bottom: 40px;
            box-shadow: var(--box-shadow);
        }
        
        p {
            margin-bottom: 20px;
            color: var(--text-secondary);
            font-size: 1.05rem;
        }
        
        ul {
            padding-left: 20px;
            margin-bottom: 20px;
        }
        
        li {
            margin-bottom: 10px;
            position: relative;
        }
        
        table {
            width: 100%;
            border-collapse: collapse;
            margin: 25px 0;
            box-shadow: 0 2px 10px rgba(0,0,0,0.05);
            border-radius: var(--border-radius);
            overflow: hidden;
        }
        
        th, td {
            padding: 15px;
            text-align: left;
            border-bottom: 1px solid var(--gray-300);
        }
        
        th {
            background-color: var(--primary-color);
            color: white;
            font-weight: 600;
        }
        
        tr:nth-child(even) {
            background-color: var(--gray-100);
        }
        
        .note {
            background-color: #fff8e1;
            padding: 20px;
            border-left: 4px solid var(--warning-color);
            margin: 25px 0;
            border-radius: 0 var(--border-radius) var(--border-radius) 0;
        }
        
        .component {
            background-color: #e3f2fd;
            padding: 20px;
            border-radius: var(--border-radius);
            margin: 20px 0;
            border-left: 4px solid var(--primary-color);
            box-shadow: 0 2px 10px rgba(0,0,0,0.05);
        }
        
        footer {
            text-align: center;
            margin-top: 60px;
            padding: 30px 0;
            background-color: var(--gray-800);
            color: white;
        }
        
        code {
            font-family: Consolas, Monaco, monospace;
            background-color: var(--gray-100);
            padding: 2px 5px;
            border-radius: 3px;
            font-size: 0.9em;
            color: var(--danger-color);
        }
        
        .flow-diagram {
            display: flex;
            justify-content: space-between;
            overflow-x: auto;
            margin: 30px 0;
            padding: 20px 0;
            gap: 15px;
        }
        
        .flow-step {
            flex: 1;
            min-width: 150px;
            text-align: center;
            padding: 20px 15px;
            background-color: #e8f5e9;
            border-radius: var(--border-radius);
            position: relative;
            box-shadow: 0 3px 10px rgba(0,0,0,0.08);
            border-left: 4px solid var(--success-color);
            transition: var(--transition);
        }
        
        .flow-step:hover {
            transform: translateY(-5px);
            box-shadow: 0 8px 15px rgba(0,0,0,0.1);
        }
        
        .flow-step:not(:last-child)::after {
            content: "\f054";
            font-family: "Font Awesome 6 Free";
            font-weight: 900;
            position: absolute;
            right: -22px;
            top: 50%;
            font-size: 24px;
            color: var(--success-color);
            transform: translateY(-50%);
            z-index: 2;
        }
        
        .diagram-note {
            font-style: italic;
            color: var(--text-secondary);
            margin-top: 15px;
            text-align: center;
            font-size: 0.9rem;
        }
        
        .component-container {
            display: flex;
            flex-wrap: wrap;
            gap: 20px;
            margin: 25px 0;
        }
        
        .component {
            flex: 1;
            min-width: 300px;
            background-color: #e3f2fd;
            padding: 20px;
            border-radius: var(--border-radius);
            border-left: 4px solid var(--primary-color);
            box-shadow: 0 3px 10px rgba(0,0,0,0.08);
            transition: var(--transition);
        }
        
        .component:hover {
            transform: translateY(-3px);
            box-shadow: 0 8px 15px rgba(0,0,0,0.1);
        }
        
        .component h4 {
            margin-top: 0;
            color: var(--primary-color);
            border-bottom: 1px solid #bbdefb;
            padding-bottom: 10px;
            display: flex;
            align-items: center;
        }
        
        .component h4 i {
            margin-right: 10px;
        }
        
        .architecture-diagram {
            background-color: white;
            padding: 20px;
            border-radius: var(--border-radius);
            box-shadow: 0 3px 10px rgba(0,0,0,0.08);
            margin: 30px 0;
        }
        
        .architecture-diagram h4 {
            text-align: center;
            margin-bottom: 20px;
            font-size: 1.3rem;
        }
        
        .card-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
            gap: 20px;
            margin: 25px 0;
        }
        
        .card {
            background-color: white;
            border-radius: var(--border-radius);
            padding: 20px;
            box-shadow: 0 3px 10px rgba(0,0,0,0.08);
            transition: var(--transition);
        }
        
        .card:hover {
            transform: translateY(-5px);
            box-shadow: 0 8px 15px rgba(0,0,0,0.1);
        }
        
        .card-icon {
            width: 60px;
            height: 60px;
            background-color: var(--primary-light);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-bottom: 15px;
            color: white;
            font-size: 24px;
        }
        
        .card h4 {
            margin-top: 0;
        }
        
        .card p {
            margin-bottom: 0;
            color: var(--text-secondary);
        }
        
        .tag {
            display: inline-block;
            padding: 5px 10px;
            border-radius: 30px;
            font-size: 0.8rem;
            font-weight: 600;
            margin-right: 8px;
            margin-bottom: 8px;
        }
        
        .tag-primary {
            background-color: #e3f2fd;
            color: var(--primary-color);
        }
        
        .tag-success {
            background-color: #e8f5e9;
            color: var(--success-color);
        }
        
        .tag-warning {
            background-color: #fff8e1;
            color: var(--warning-color);
        }
        
        .feature-list {
            padding-left: 0;
            list-style: none;
        }
        
        .feature-list li {
            padding-left: 30px;
            position: relative;
            margin-bottom: 15px;
        }
        
        .feature-list li::before {
            content: "\f058";
            font-family: "Font Awesome 6 Free";
            font-weight: 900;
            color: var(--success-color);
            position: absolute;
            left: 0;
            top: 2px;
        }
        
        .grid-2 {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 30px;
        }
        
        @media (max-width: 768px) {
            .grid-2 {
                grid-template-columns: 1fr;
            }
            
            h1 {
                font-size: 2rem;
            }
            
            h2 {
                font-size: 1.6rem;
            }
            
            nav ul {
                flex-direction: column;
            }
        }
        
        .device-icon {
            margin: 0 auto;
            display: block;
            max-width: 100%;
        }
        
        .highlight-box {
            background-color: #f3e5f5;
            border-left: 4px solid var(--accent-color);
            padding: 20px;
            margin: 25px 0;
            border-radius: 0 var(--border-radius) var(--border-radius) 0;
        }
        
        .highlight-title {
            color: var(--accent-color);
            font-weight: 600;
            margin-bottom: 10px;
            display: block;
        }
        
        .btn {
            display: inline-block;
            padding: 10px 20px;
            background-color: var(--primary-color);
            color: white;
            border-radius: var(--border-radius);
            text-decoration: none;
            transition: var(--transition);
            border: none;
            cursor: pointer;
            font-weight: 600;
        }
        
        .btn:hover {
            background-color: var(--primary-dark);
            transform: translateY(-3px);
            box-shadow: 0 5px 10px rgba(0,0,0,0.1);
        }
        
        .btn-outline {
            background-color: transparent;
            border: 2px solid var(--primary-color);
            color: var(--primary-color);
        }
        
        .btn-outline:hover {
            background-color: var(--primary-color);
            color: white;
        }
    </style>
</head>
<body>
    <!-- 封面 -->
    <header>
        <div class="container">
            <div class="logo-placeholder"><i class="fas fa-key"></i></div>
            <h1>数字钥匙系统技术架构</h1>
            <p class="header-subtitle">完整的汽车数字钥匙解决方案 - 实现安全、便捷的数字车钥匙服务</p>
        </div>
    </header>

    <div class="container">
        <!-- 目录 -->
        <nav>
            <h3><i class="fas fa-list"></i> 目录</h3>
            <ul>
                <li><a href="#overview"><i class="fas fa-info-circle"></i> 1. 系统概述</a></li>
                <li><a href="#architecture"><i class="fas fa-project-diagram"></i> 2. 系统架构</a></li>
                <li><a href="#components"><i class="fas fa-puzzle-piece"></i> 3. 核心组件</a></li>
                <li><a href="#process"><i class="fas fa-exchange-alt"></i> 4. 关键流程</a></li>
                <li><a href="#security"><i class="fas fa-shield-alt"></i> 5. 安全策略设计</a></li>
                <li><a href="#challenges"><i class="fas fa-exclamation-triangle"></i> 6. 技术难点与解决方案</a></li>
                <li><a href="#integration"><i class="fas fa-plug"></i> 7. 平台交互与集成</a></li>
                <li><a href="#future"><i class="fas fa-rocket"></i> 8. 未来扩展与规划</a></li>
            </ul>
        </nav>

        <!-- 项目概述 -->
        <section id="overview">
            <h2><i class="fas fa-info-circle"></i> 系统概述</h2>
            
            <div class="card-grid">
                <div class="card">
                    <div class="card-icon">
                        <i class="fas fa-history"></i>
                    </div>
                    <h4>背景介绍</h4>
                    <p>传统物理钥匙在使用过程中存在诸多痛点，如易丢失、不便携带、无法远程共享等问题，随着智能手机的普及和物联网技术的发展，数字化钥匙解决方案应运而生。</p>
                </div>
                
                <div class="card">
                    <div class="card-icon">
                        <i class="fas fa-file-alt"></i>
                    </div>
                    <h4>项目定义</h4>
                    <p>数字钥匙系统是一套完整的汽车数字钥匙解决方案，旨在取代传统的物理钥匙，提供更安全、便捷的车辆访问控制机制。</p>
                </div>
            </div>
            
            <h3>核心价值</h3>
            <ul class="feature-list">
                <li>提供便捷的车辆访问控制，同时保障安全性</li>
                <li>降低钥匙管理成本</li>
                <li>实现灵活的权限控制与实时监控</li>
                <li>支持多种设备与场景的互操作性</li>
            </ul>

            <div class="grid-2">
                <div>
                    <h3>系统组成</h3>
                    <div class="component">
                        <h4><i class="fas fa-mobile-alt"></i> 手机APP及SDK</h4>
                        <p>用户使用的客户端应用，包含核心钥匙管理功能</p>
                    </div>
                    <div class="component">
                        <h4><i class="fas fa-cloud"></i> 云平台SDK</h4>
                        <p>提供云端服务支持，管理钥匙生命周期</p>
                    </div>
                    <div class="component">
                        <h4><i class="fas fa-car"></i> 车端软硬件</h4>
                        <p>车辆内部集成的数字钥匙接收和处理模块</p>
                    </div>
                </div>
                
                <div>
                    <h3>典型应用场景</h3>
                    <div class="highlight-box">
                        <span class="highlight-title"><i class="fas fa-walking"></i> 无感控车</span>
                        <p>用户靠近车辆自动开锁，离开车辆自动上锁，全程无需掏出手机</p>
                    </div>
                    <div class="highlight-box">
                        <span class="highlight-title"><i class="fas fa-bluetooth"></i> 蓝牙控车</span>
                        <p>通过手机APP使用蓝牙连接车辆并进行控制</p>
                    </div>
                    <div class="highlight-box">
                        <span class="highlight-title"><i class="fas fa-share-alt"></i> 远程共享钥匙</span>
                        <p>车主可远程授权他人使用车辆，并设置使用权限和时间限制</p>
                    </div>
                    <div class="highlight-box">
                        <span class="highlight-title"><i class="fas fa-network-wired"></i> 远程控制车辆</span>
                        <p>通过网络连接远程控制车辆，如开启空调、查询位置等</p>
                    </div>
                </div>
            </div>
        </section>

        <!-- 系统架构 -->
        <section id="architecture">
            <h2><i class="fas fa-project-diagram"></i> 系统架构</h2>
            
            <div class="architecture-diagram">
                <h4>系统整体架构图</h4>
                <svg width="100%" height="800" viewBox="0 0 1000 800" xmlns="http://www.w3.org/2000/svg">
                    <!-- SVG内容保持不变 -->
                </svg>
                <p class="diagram-note">数字钥匙系统架构图 - 展示了移动端、云端与车辆端三大系统的核心组件及其交互关系</p>
            </div>
            
            <div class="note">
                <strong><i class="fas fa-lightbulb"></i> 设计重点：</strong> 本架构基于车端、云端及移动端三层设计，实现安全、便捷的数字车钥匙服务。采用分层设计理念确保系统各组件高内聚低耦合，便于后续升级与维护。
            </div>
            
            <h3>三层架构说明</h3>
            <div class="component-container">
                <div class="component">
                    <h4><i class="fas fa-cloud"></i> 钥匙云平台</h4>
                    <p>负责数字钥匙的全生命周期管理，提供认证、授权、撤销等服务，同时管理车辆与用户关系</p>
                    <div class="tags">
                        <span class="tag tag-primary">钥匙生命周期</span>
                        <span class="tag tag-primary">安全认证</span>
                        <span class="tag tag-primary">数据管理</span>
                    </div>
                </div>
                
                <div class="component">
                    <h4><i class="fas fa-mobile-alt"></i> 移动智能终端</h4>
                    <p>用户使用的手机应用，负责钥匙存储、车辆控制、与车辆建立蓝牙连接</p>
                    <div class="tags">
                        <span class="tag tag-success">蓝牙通信</span>
                        <span class="tag tag-success">安全存储</span>
                        <span class="tag tag-success">用户界面</span>
                    </div>
                </div>
                
                <div class="component">
                    <h4><i class="fas fa-car"></i> 车辆终端</h4>
                    <p>车辆内置的数字钥匙接收模块，负责验证钥匙有效性并执行相应车辆控制指令</p>
                    <div class="tags">
                        <span class="tag tag-warning">钥匙验证</span>
                        <span class="tag tag-warning">控制执行</span>
                        <span class="tag tag-warning">安全机制</span>
                    </div>
                </div>
            </div>
            
            <h3>关键技术特点</h3>
            <div class="grid-2">
                <ul class="feature-list">
                    <li><strong>安全双向认证</strong>：手机端与车辆端相互验证身份，防止未授权访问</li>
                    <li><strong>多层次加密</strong>：采用业界领先的加密技术保护通信安全</li>
                    <li><strong>距离精确测量</strong>：结合RSSI信号强度与环境感知算法实现精确距离计算</li>
                    <li><strong>灵活授权机制</strong>：支持临时授权、紧急授权等多种授权方式</li>
                </ul>
                
                <ul class="feature-list">
                    <li><strong>低功耗设计</strong>：优化电池使用，确保长时间稳定运行</li>
                    <li><strong>离线可用</strong>：无需网络也能使用基本功能</li>
                    <li><strong>时间同步机制</strong>：确保系统时间一致性，防止重放攻击</li>
                    <li><strong>故障自恢复</strong>：具备自检与恢复机制，提高系统可靠性</li>
                </ul>
            </div>
        </section>

        <!-- 关键流程 -->
        <section id="process">
            <h2><i class="fas fa-exchange-alt"></i> 关键流程</h2>
            
            <h3>首次配对流程</h3>
            <div class="flow-diagram">
                <div class="flow-step">
                    <i class="fas fa-user-plus fa-2x"></i>
                    <h4>用户注册</h4>
                    <p>创建账号并完成身份验证</p>
                </div>
                <div class="flow-step">
                    <i class="fas fa-car-side fa-2x"></i>
                    <h4>车辆识别</h4>
                    <p>扫描或输入车辆VIN码</p>
                </div>
                <div class="flow-step">
                    <i class="fas fa-key fa-2x"></i>
                    <h4>钥匙创建</h4>
                    <p>云端生成专属钥匙数据</p>
                </div>
                <div class="flow-step">
                    <i class="fas fa-exchange-alt fa-2x"></i>
                    <h4>密钥交换</h4>
                    <p>手机与车辆建立安全通道</p>
                </div>
                <div class="flow-step">
                    <i class="fas fa-check-circle fa-2x"></i>
                    <h4>配对完成</h4>
                    <p>配对成功并保存设置</p>
                </div>
            </div>
            
            <h3>无感控车流程</h3>
            <div class="architecture-diagram">
                <!-- 无感控车流程图 SVG 内容保持不变 -->
            </div>
            
            <h4>关键步骤说明：</h4>
            <div class="flow-diagram">
                <div class="flow-step">
                    <i class="fas fa-power-off fa-2x"></i>
                    <p>系统唤醒后台APP</p>
                </div>
                <div class="flow-step">
                    <i class="fas fa-bluetooth fa-2x"></i>
                    <p>自动发起蓝牙连接</p>
                </div>
                <div class="flow-step">
                    <i class="fas fa-shield-alt fa-2x"></i>
                    <p>安全认证与通道建立</p>
                </div>
                <div class="flow-step">
                    <i class="fas fa-ruler fa-2x"></i>
                    <p>距离计算与状态同步</p>
                </div>
                <div class="flow-step">
                    <i class="fas fa-car fa-2x"></i>
                    <p>执行自动控车操作</p>
                </div>
            </div>
            
            <h4>优化策略：</h4>
            <div class="card-grid">
                <div class="card">
                    <div class="card-icon">
                        <i class="fas fa-battery-full"></i>
                    </div>
                    <h4>系统级唤醒</h4>
                    <p>利用系统API实现低功耗唤醒，减少电池消耗</p>
                </div>
                <div class="card">
                    <div class="card-icon">
                        <i class="fas fa-map-marker-alt"></i>
                    </div>
                    <h4>上下文感知</h4>
                    <p>根据用户行为和环境动态调整扫描频率</p>
                </div>
                <div class="card">
                    <div class="card-icon">
                        <i class="fas fa-tachometer-alt"></i>
                    </div>
                    <h4>电量自适应</h4>
                    <p>根据电池状态智能调整工作模式，延长使用时间</p>
                </div>
                <div class="card">
                    <div class="card-icon">
                        <i class="fas fa-ruler-combined"></i>
                    </div>
                    <h4>距离精确计算</h4>
                    <p>使用RSSI信号强度分析和环境干扰补偿提高准确性</p>
                </div>
            </div>
        </section>

        <!-- 安全策略设计 -->
        <section id="security">
            <h2><i class="fas fa-shield-alt"></i> 安全策略设计</h2>
            
            <h3>通信与认证</h3>
            <div class="architecture-diagram">
                <h4>通信安全层次架构</h4>
                <!-- 通信安全层次架构 SVG 内容保持不变 -->
            </div>
            
            <div class="grid-2">
                <div>
                    <h4><i class="fas fa-exchange-alt"></i> 通信安全措施</h4>
                    <ul class="feature-list">
                        <li><strong>TLS 1.3</strong>：网络通信采用最新TLS协议，确保端到端加密</li>
                        <li><strong>蓝牙加密</strong>：基于蓝牙4.2标准实现数据保护</li>
                        <li><strong>安全会话</strong>：基于会话密钥建立安全通道</li>
                        <li><strong>数据完整性</strong>：确保数据传输过程中不被篡改</li>
                    </ul>
                </div>
                
                <div>
                    <h4><i class="fas fa-user-check"></i> 认证机制</h4>
                    <ul class="feature-list">
                        <li><strong>双向认证</strong>：确保通信双方身份真实可靠</li>
                        <li><strong>多因素验证</strong>：结合多种因素提高安全性</li>
                        <li><strong>挑战-响应机制</strong>：防止重放和会话劫持</li>
                        <li><strong>证书验证</strong>：基于PKI架构的证书验证体系</li>
                    </ul>
                </div>
            </div>
            
            <h3>数据与密钥</h3>
            <div class="architecture-diagram">
                <!-- 密钥管理架构图 SVG 内容保持不变 -->
            </div>
            
            <div class="grid-2">
                <div>
                    <h4><i class="fas fa-database"></i> 数据安全</h4>
                    <ul class="feature-list">
                        <li><strong>数据分类保护</strong>：根据敏感程度实施分级保护</li>
                        <li><strong>敏感数据加密</strong>：所有敏感信息强制加密存储</li>
                        <li><strong>数据完整性验证</strong>：防止数据被篡改</li>
                        <li><strong>个人信息脱敏</strong>：减少隐私泄露风险</li>
                    </ul>
                </div>
                
                <div>
                    <h4><i class="fas fa-key"></i> 密钥管理</h4>
                    <ul class="feature-list">
                        <li><strong>分层密钥架构</strong>：根密钥、中间密钥、应用密钥</li>
                        <li><strong>密钥生成与安全存储</strong>：安全随机数生成器，硬件保护</li>
                        <li><strong>根密钥管理流程</strong>：严格的访问控制和多人授权</li>
                        <li><strong>定期更新与备份机制</strong>：降低密钥泄露风险</li>
                        <li><strong>密钥撤销方案</strong>：应对密钥泄露情况</li>
                    </ul>
                </div>
            </div>
            
            <h3>防护机制</h3>
            <div class="architecture-diagram">
                <!-- 防护机制图 SVG 内容保持不变 -->
            </div>
            
            <div class="grid-2">
                <div>
                    <h4><i class="fas fa-clock"></i> 时间同步机制</h4>
                    <ul class="feature-list">
                        <li><strong>NTP同步与时间戳验证</strong>：确保系统时间准确</li>
                        <li><strong>容错机制与离线处理</strong>：适应网络不稳定情况</li>
                        <li><strong>时间源验证</strong>：防止伪造时间源</li>
                    </ul>
                </div>
                
                <div>
                    <h4><i class="fas fa-shield-alt"></i> 安全防护</h4>
                    <ul class="feature-list">
                        <li><strong>防重放攻击措施</strong>：时间戳、随机数和序列号</li>
                        <li><strong>防中继攻击技术</strong>：距离测量和时间限制</li>
                        <li><strong>安全存储与运算</strong>：TEE/SE安全环境保护</li>
                        <li><strong>异常检测与响应</strong>：实时监控与威胁处理</li>
                    </ul>
                </div>
            </div>
            
            <div class="note">
                <p><strong><i class="fas fa-lightbulb"></i> 安全最佳实践：</strong> 数字钥匙系统安全设计遵循"深度防御"原则，构建多层次防护体系。即使某一层被突破，其他层次仍能提供有效保护，确保系统整体安全性。</p>
            </div>
        </section>

        <!-- 其他部分类似修改，添加图标和视觉元素 -->

        <!-- 页脚 -->
        <footer>
            <p>数字钥匙系统技术架构 | 版本：1.0.0 | 最后更新：2023年12月25日</p>
            <p>Copyright © 2023 数字钥匙系统团队. All Rights Reserved.</p>
        </footer>
    </div>
</body>
</html>

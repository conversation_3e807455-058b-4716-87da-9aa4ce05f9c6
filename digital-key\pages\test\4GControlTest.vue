<template>
  <view class="container">
    <view class="header">
      <text class="title">4G控车功能测试</text>
    </view>
    
    <view class="status-section">
      <view class="status-item">
        <text class="label">连接状态:</text>
        <view class="status-indicator">
          <view :class="'dot ' + (connected ? 'connected' : 'disconnected')"></view>
          <text>{{ connected ? '已连接' : '未连接' }}</text>
        </view>
      </view>
      
      <view v-if="vehicleStatus" class="status-item">
        <text class="label">车门状态:</text>
        <text class="value">{{ vehicleStatus.locked ? '已闭锁' : '已解锁' }}</text>
      </view>
      
      <view v-if="lastMessage" class="status-item">
        <text class="label">最后消息:</text>
        <text class="value">{{ lastMessage }}</text>
      </view>
    </view>
    
    <view class="control-section">
      <button 
        class="control-btn connect-btn" 
        @click="handleConnect"
        :disabled="connected"
      >
        {{ connected ? '已连接' : '连接4G控车' }}
      </button>
      
      <button 
        class="control-btn disconnect-btn" 
        @click="handleDisconnect"
        :disabled="!connected"
      >
        断开连接
      </button>
      
      <button 
        class="control-btn unlock-btn" 
        @click="handleUnlock"
        :disabled="!connected"
      >
        解锁车门
      </button>
      
      <button 
        class="control-btn lock-btn" 
        @click="handleLock"
        :disabled="!connected"
      >
        闭锁车门
      </button>
      
      <button 
        class="control-btn query-btn" 
        @click="handleQuery"
        :disabled="!connected"
      >
        查询状态
      </button>
    </view>
    
    <view class="log-section">
      <view class="log-header">
        <text class="log-title">消息日志</text>
        <button class="clear-btn" @click="clearLogs">清空</button>
      </view>
      <scroll-view class="log-content" scroll-y>
        <view 
          v-for="(log, index) in logs" 
          :key="index" 
          class="log-item"
          :class="log.type"
        >
          <text class="log-time">{{ log.time }}</text>
          <text class="log-message">{{ log.message }}</text>
        </view>
      </scroll-view>
    </view>
  </view>
</template>

<script>
import fourGControlWebSocket from '@/utils/websocket/4GControlWebSocket.js'

export default {
  data() {
    return {
      connected: false,
      vehicleStatus: null,
      lastMessage: '',
      logs: []
    }
  },
  
  onLoad() {
    this.initWebSocket()
  },
  
  onUnload() {
    fourGControlWebSocket.disconnect()
  },
  
  methods: {
    initWebSocket() {
      // 设置连接状态回调
      fourGControlWebSocket.setConnectionStatusCallback((connected) => {
        this.connected = connected
        this.addLog(connected ? 'success' : 'error', connected ? '连接成功' : '连接断开')
      })
      
      // 设置车辆状态回调
      fourGControlWebSocket.setVehicleStatusCallback((status) => {
        this.vehicleStatus = status
        this.addLog('info', `车辆状态更新: ${JSON.stringify(status)}`)
      })
      
      // 添加消息处理器
      fourGControlWebSocket.addMessageHandler('control_system', (message) => {
        this.lastMessage = message.message
        this.addLog('info', `控车系统: ${message.message}`)
      })
      
      fourGControlWebSocket.addMessageHandler('tbox', (message) => {
        this.lastMessage = message.message
        this.addLog('info', `TBOX设备: ${message.message}`)
      })
      
      fourGControlWebSocket.addMessageHandler('general', (message) => {
        this.lastMessage = message.message
        this.addLog('info', `一般消息: ${message.message}`)
      })
    },
    
    handleConnect() {
      this.addLog('info', '正在连接4G控车...')
      fourGControlWebSocket.connect()
    },
    
    handleDisconnect() {
      this.addLog('info', '正在断开连接...')
      fourGControlWebSocket.disconnect()
    },
    
    handleUnlock() {
      this.addLog('info', '发送解锁指令...')
      fourGControlWebSocket.sendUnlockCommand((result) => {
        this.addLog(result.success ? 'success' : 'error', result.message)
      })
    },
    
    handleLock() {
      this.addLog('info', '发送闭锁指令...')
      fourGControlWebSocket.sendLockCommand((result) => {
        this.addLog(result.success ? 'success' : 'error', result.message)
      })
    },
    
    handleQuery() {
      this.addLog('info', '查询车辆状态...')
      fourGControlWebSocket.queryVehicleStatus((result) => {
        this.addLog(result.success ? 'success' : 'error', result.message)
      })
    },
    
    addLog(type, message) {
      const now = new Date()
      const time = `${now.getHours().toString().padStart(2, '0')}:${now.getMinutes().toString().padStart(2, '0')}:${now.getSeconds().toString().padStart(2, '0')}`
      
      this.logs.unshift({
        type,
        time,
        message
      })
      
      // 限制日志数量
      if (this.logs.length > 100) {
        this.logs = this.logs.slice(0, 100)
      }
    },
    
    clearLogs() {
      this.logs = []
    }
  }
}
</script>

<style lang="scss" scoped>
.container {
  padding: 20px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  min-height: 100vh;
}

.header {
  text-align: center;
  margin-bottom: 30px;
  
  .title {
    color: white;
    font-size: 24px;
    font-weight: bold;
  }
}

.status-section {
  background: rgba(255, 255, 255, 0.1);
  border-radius: 12px;
  padding: 20px;
  margin-bottom: 20px;
  backdrop-filter: blur(10px);
  
  .status-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 15px;
    
    &:last-child {
      margin-bottom: 0;
    }
    
    .label {
      color: white;
      font-weight: 500;
    }
    
    .status-indicator {
      display: flex;
      align-items: center;
      
      .dot {
        width: 10px;
        height: 10px;
        border-radius: 50%;
        margin-right: 8px;
        
        &.connected {
          background: #4CAF50;
          box-shadow: 0 0 10px rgba(76, 175, 80, 0.6);
        }
        
        &.disconnected {
          background: #f44336;
          box-shadow: 0 0 10px rgba(244, 67, 54, 0.6);
        }
      }
      
      text {
        color: white;
      }
    }
    
    .value {
      color: white;
    }
  }
}

.control-section {
  margin-bottom: 20px;
  
  .control-btn {
    width: 100%;
    height: 50px;
    border-radius: 25px;
    border: none;
    color: white;
    font-size: 16px;
    font-weight: 500;
    margin-bottom: 15px;
    
    &:disabled {
      opacity: 0.5;
    }
    
    &.connect-btn {
      background: linear-gradient(45deg, #4CAF50, #45a049);
    }
    
    &.disconnect-btn {
      background: linear-gradient(45deg, #f44336, #da190b);
    }
    
    &.unlock-btn {
      background: linear-gradient(45deg, #2196F3, #0b7dda);
    }
    
    &.lock-btn {
      background: linear-gradient(45deg, #FF9800, #e68900);
    }
    
    &.query-btn {
      background: linear-gradient(45deg, #9C27B0, #7b1fa2);
    }
  }
}

.log-section {
  background: rgba(255, 255, 255, 0.1);
  border-radius: 12px;
  padding: 20px;
  backdrop-filter: blur(10px);
  height: 300px;
  
  .log-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 15px;
    
    .log-title {
      color: white;
      font-weight: 500;
      font-size: 16px;
    }
    
    .clear-btn {
      background: rgba(255, 255, 255, 0.2);
      border: none;
      border-radius: 15px;
      padding: 5px 15px;
      color: white;
      font-size: 12px;
    }
  }
  
  .log-content {
    height: 240px;
    
    .log-item {
      margin-bottom: 8px;
      padding: 8px 12px;
      border-radius: 6px;
      
      &.success {
        background: rgba(76, 175, 80, 0.2);
      }
      
      &.error {
        background: rgba(244, 67, 54, 0.2);
      }
      
      &.info {
        background: rgba(33, 150, 243, 0.2);
      }
      
      .log-time {
        color: rgba(255, 255, 255, 0.7);
        font-size: 12px;
        margin-right: 10px;
      }
      
      .log-message {
        color: white;
        font-size: 14px;
      }
    }
  }
}
</style>

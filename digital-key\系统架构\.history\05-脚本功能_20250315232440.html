<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>脚本功能 - 数字钥匙系统技术方案</title>
    <!-- 添加jsPlumb库 -->
    <script src="https://cdn.jsdelivr.net/npm/jsplumb@2.15.6/dist/js/jsplumb.min.js"></script>
    <link rel="stylesheet" href="00-头部样式.html">
</head>
<body>

<h1>数字钥匙系统技术方案</h1>

<script>
    // 页面加载完成后执行
    document.addEventListener('DOMContentLoaded', function() {
        // 折叠面板功能
        var coll = document.getElementsByClassName("collapsible");
        for (var i = 0; i < coll.length; i++) {
            coll[i].addEventListener("click", function() {
                this.classList.toggle("active");
                var content = this.nextElementSibling;
                if (content.style.maxHeight) {
                    content.style.maxHeight = null;
                } else {
                    content.style.maxHeight = content.scrollHeight + "px";
                }
            });
        }
        
        // 添加模块卡片的悬停效果
        var modules = document.getElementsByClassName("architecture-module");
        for (var i = 0; i < modules.length; i++) {
            modules[i].addEventListener("mouseenter", function() {
                this.style.transform = "translateY(-5px)";
                this.style.boxShadow = "0 8px 16px rgba(0,0,0,0.15)";
            });
            modules[i].addEventListener("mouseleave", function() {
                this.style.transform = "translateY(0)";
                this.style.boxShadow = "0 4px 8px rgba(0,0,0,0.1)";
            });
        }
        
        // 初始化jsPlumb连接
        if (typeof jsPlumb !== 'undefined') {
            var instance = jsPlumb.getInstance({
                Connector: ["Bezier", { curviness: 50 }],
                Endpoint: ["Dot", { radius: 5 }],
                EndpointStyle: { fill: "#0066cc" },
                PaintStyle: { stroke: "#0066cc", strokeWidth: 2 },
                HoverPaintStyle: { stroke: "#0088ff", strokeWidth: 3 },
                ConnectionOverlays: [
                    ["Arrow", { location: 1, width: 10, length: 10 }]
                ]
            });
            
            // 设置所有元素可拖动
            instance.draggable(document.querySelectorAll(".flow-module"));
            
            // 连接流程图中的元素
            setTimeout(function() {
                var flowModules = document.querySelectorAll(".flow-module");
                if (flowModules.length > 1) {
                    for (var i = 0; i < flowModules.length - 1; i++) {
                        instance.connect({
                            source: flowModules[i].id,
                            target: flowModules[i + 1].id,
                            anchor: ["Right", "Left"]
                        });
                    }
                }
            }, 500);
        }
        
        // 添加流程图交互效果
        setupFlowInteractions();
    });
    
    // 设置流程图交互
    function setupFlowInteractions() {
        // 获取所有流程端点
        const flowEndpoints = document.querySelectorAll('.flow-endpoint');
        
        // 为每个端点添加鼠标事件
        flowEndpoints.forEach(endpoint => {
            endpoint.addEventListener('mouseenter', () => {
                // 高亮当前端点
                endpoint.style.opacity = '1';
                endpoint.style.transform = 'scale(1.1)';
                
                // 降低其他端点的不透明度
                flowEndpoints.forEach(otherEndpoint => {
                    if (otherEndpoint !== endpoint) {
                        otherEndpoint.style.opacity = '0.3';
                    }
                });
                
                // 高亮相关箭头
                highlightRelatedArrows(endpoint);
            });
            
            endpoint.addEventListener('mouseleave', () => {
                // 恢复当前端点样式
                endpoint.style.transform = 'scale(1)';
                
                // 恢复其他端点的不透明度
                flowEndpoints.forEach(otherEndpoint => {
                    otherEndpoint.style.opacity = '1';
                });
                
                // 恢复所有箭头样式
                resetArrowsHighlight();
            });
        });
    }
    
    // 高亮相关箭头
    function highlightRelatedArrows(endpoint) {
        // 获取端点的类名，用于确定是哪个端点
        const classList = endpoint.classList;
        const arrows = document.querySelectorAll('.flow-arrow');
        
        arrows.forEach(arrow => {
            // 默认降低所有箭头的不透明度
            arrow.style.opacity = '0.3';
            
            // 根据端点类型高亮相关箭头
            if (classList.contains('mobile-endpoint')) {
                // 手机端相关箭头
                if (arrow.classList.contains('arrow1') || 
                    arrow.classList.contains('arrow2') || 
                    arrow.classList.contains('arrow3') || 
                    arrow.classList.contains('arrow4') || 
                    arrow.classList.contains('arrow5') || 
                    arrow.classList.contains('arrow6')) {
                    arrow.style.opacity = '1';
                    if (arrow.classList.contains('right-arrow')) {
                        arrow.style.height = '3px';
                    } else if (arrow.classList.contains('left-arrow')) {
                        arrow.style.height = '3px';
                    } else if (arrow.classList.contains('diagonal-arrow')) {
                        arrow.style.height = '3px';
                    }
                }
            } else if (classList.contains('cloud-endpoint')) {
                // 云平台相关箭头
                if (arrow.classList.contains('arrow1') || 
                    arrow.classList.contains('arrow2') || 
                    arrow.classList.contains('arrow3') || 
                    arrow.classList.contains('arrow4') || 
                    arrow.classList.contains('arrow6') || 
                    arrow.classList.contains('arrow7')) {
                    arrow.style.opacity = '1';
                    if (arrow.classList.contains('right-arrow')) {
                        arrow.style.height = '3px';
                    } else if (arrow.classList.contains('left-arrow')) {
                        arrow.style.height = '3px';
                    }
                }
            } else if (classList.contains('car-endpoint')) {
                // 车端相关箭头
                if (arrow.classList.contains('arrow5') || 
                    arrow.classList.contains('arrow6') || 
                    arrow.classList.contains('arrow7')) {
                    arrow.style.opacity = '1';
                    if (arrow.classList.contains('right-arrow')) {
                        arrow.style.height = '3px';
                    } else if (arrow.classList.contains('left-arrow')) {
                        arrow.style.height = '3px';
                    } else if (arrow.classList.contains('diagonal-arrow')) {
                        arrow.style.height = '3px';
                    }
                }
            }
        });
    }
    
    // 重置箭头高亮
    function resetArrowsHighlight() {
        const arrows = document.querySelectorAll('.flow-arrow');
        
        arrows.forEach(arrow => {
            arrow.style.opacity = '1';
            arrow.style.height = '2px';
        });
    }
</script>

</body>
</html> 
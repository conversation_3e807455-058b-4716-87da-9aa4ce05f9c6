package com.ruoyi.dk.domain;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.ruoyi.common.core.annotation.Excel;
import com.ruoyi.common.core.web.domain.BaseEntity;
import org.apache.commons.lang3.builder.ToStringBuilder;

import java.util.Date;

/**
 * 车辆数字钥匙分享详情对象 digital_key_share_detail
 * 
 * <AUTHOR>
 * @date 2024-08-12
 */
public class DigitalKeyShareDetail extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 详情ID（主键） */
    private Long detailId;

    /** 分享人ID */
    private Long sharerId;

    /** 分享人姓名 */
    @Excel(name = "分享人")
    private String sharerName;

    /** 被分享人ID */
    private Long shareeId;

    /** 被分享人姓名 */
    @Excel(name = "被分享人")
    private String shareeName;

    /** 分享车辆VIN码 */
    @Excel(name = "分享车辆VIN码")
    private String shareVehicleVin;

    /** 蓝牙通讯 - 正式密钥 */
    private String bluetoothPermKey;

    /** 分享开始时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "分享开始时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date shareStartTime;

    /** 分享结束时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "分享结束时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date shareEndTime;

    /** 当前时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date currentTime;

    /** 模糊匹配的车辆vinCode */
    private String vehicleVinOfFuzzyMatch;

    /** 是否过期（true: 已过期，false: 未过期） */
    private Boolean isExpired;

    public Long getDetailId() {
        return detailId;
    }

    public void setDetailId(Long detailId) {
        this.detailId = detailId;
    }

    public void setSharerId(Long sharerId)
    {
        this.sharerId = sharerId;
    }

    public Long getSharerId() 
    {
        return sharerId;
    }
    public void setShareeId(Long shareeId) 
    {
        this.shareeId = shareeId;
    }

    public Long getShareeId() 
    {
        return shareeId;
    }

    public String getSharerName() {
        return sharerName;
    }

    public void setSharerName(String sharerName) {
        this.sharerName = sharerName;
    }

    public String getShareeName() {
        return shareeName;
    }

    public void setShareeName(String shareeName) {
        this.shareeName = shareeName;
    }

    public void setShareVehicleVin(String shareVehicleVin)
    {
        this.shareVehicleVin = shareVehicleVin;
    }

    public String getShareVehicleVin() 
    {
        return shareVehicleVin;
    }

    public String getBluetoothPermKey() {
        return bluetoothPermKey;
    }

    public void setBluetoothPermKey(String bluetoothPermKey) {
        this.bluetoothPermKey = bluetoothPermKey;
    }

    public void setShareStartTime(Date shareStartTime)
    {
        this.shareStartTime = shareStartTime;
    }

    public Date getShareStartTime() 
    {
        return shareStartTime;
    }
    public void setShareEndTime(Date shareEndTime) 
    {
        this.shareEndTime = shareEndTime;
    }

    public Date getShareEndTime() 
    {
        return shareEndTime;
    }

    public Date getCurrentTime() {
        return currentTime;
    }

    public void setCurrentTime(Date currentTime) {
        this.currentTime = currentTime;
    }

    /**
     * 获取是否过期
     * @return 过期状态（true: 已过期，false: 未过期）
     */
    public Boolean getIsExpired() {
        return isExpired;
    }

    /**
     * 设置是否过期
     * @param isExpired 过期状态（true: 已过期，false: 未过期）
     */
    public void setIsExpired(Boolean isExpired) {
        this.isExpired = isExpired;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this)
                .append("detailId", detailId)
                .append("sharerId", sharerId)
                .append("sharerName", sharerName)
                .append("shareeId", shareeId)
                .append("shareeName", shareeName)
                .append("shareVehicleVin", shareVehicleVin)
                .append("shareStartTime", shareStartTime)
                .append("shareEndTime", shareEndTime)
                .toString();
    }
}

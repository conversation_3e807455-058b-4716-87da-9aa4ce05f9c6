// 文件注册系统
(function() {
    // 如果localStorage中没有菜单数据，初始化一个空数组
    if (!localStorage.getItem('htmlFiles')) {
        localStorage.setItem('htmlFiles', JSON.stringify([]));
    }
    
    // 获取当前文件名
    const currentPath = window.location.pathname;
    const fileName = currentPath.substring(currentPath.lastIndexOf('/') + 1);
    
    // 如果是index.html，不进行注册
    if (fileName === 'index.html' || fileName === '') {
        return;
    }
    
    // 将当前文件注册到菜单列表中
    const htmlFiles = JSON.parse(localStorage.getItem('htmlFiles'));
    
    // 检查文件是否已注册
    const fileExists = htmlFiles.some(file => file.file === fileName);
    
    if (!fileExists) {
        // 简单处理文件名作为显示名称
        const displayName = fileName.replace('.html', '').replace(/-/g, ' ');
        const id = displayName.toLowerCase().replace(/[^a-z0-9]/g, '');
        
        htmlFiles.push({
            id: id,
            name: displayName,
            file: fileName
        });
        
        localStorage.setItem('htmlFiles', JSON.stringify(htmlFiles));
    }
})(); 
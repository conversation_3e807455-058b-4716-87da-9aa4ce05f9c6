# TBOX设备TCP通信对接文档

## 📋 文档概述

本文档专门为TBOX设备开发人员提供与4G控车云平台的TCP通信对接指南。

## 🌐 TCP服务器信息

### 连接参数
- **服务器地址**: `dk-api.scsoi.com`
- **TCP端口**: `19999`
- **连接方式**: TBOX作为TCP客户端主动连接云平台

### 完整连接地址
```
dk-api.scsoi.com:19999
```


### 连接要求
- **长连接**: 保持TCP连接不断开，用于实时接收控车指令
- **心跳机制**: 建议每30秒发送一次心跳消息保持连接活跃
- **自动重连**: 连接断开后需要自动重连
- **并发支持**: 云平台支持多个TBOX设备同时连接

## 📡 4G控车通信协议

### 完整协议格式
```
7E000A000A000102030405060708090A00003033303230303031[状态码]007E
```

### 状态码定义
- **03**: 闭锁状态
- **04**: 解锁状态

### 标准消息示例

#### 解锁状态消息
```
7E000A000A000102030405060708090A000030333032303030313034007E
```

#### 闭锁状态消息
```
7E000A000A000102030405060708090A000030333032303030313033007E
```

## 🚗 TCP通信流程

### 1. 建立连接
```
TBOX设备 → TCP连接 → 云平台服务器(dk-api.scsoi.com:19999)
```

### 2. 接收云平台控车指令
云平台通过TCP连接向TBOX发送控车指令，TBOX需要监听TCP连接接收：
- **解锁指令**: `7E000A000A000102030405060708090A000030333032303030313034007E`
- **闭锁指令**: `7E000A000A000102030405060708090A000030333032303030313033007E`

### 3. 执行控车操作并上报状态
TBOX收到指令后：
1. 执行相应的车辆控制操作（解锁/闭锁）
2. 通过TCP连接向云平台上报执行结果：
   - **解锁成功**: 发送解锁状态消息
   - **闭锁成功**: 发送闭锁状态消息
   - **操作失败**: 发送错误状态消息

### 4. 心跳保活
定期通过TCP连接发送心跳消息保持连接：
```
7E000A000A000102030405060708090A000030333032303030313030007E
```

## 🔍 TCP连接测试

### 使用telnet测试连接
```bash
telnet dk-api.scsoi.com 19999
```

### 连接成功后测试发送消息
连接成功后，可以手动发送测试消息验证通信：

#### 发送解锁状态上报
```
7E000A000A000102030405060708090A000030333032303030313034007E
```

#### 发送闭锁状态上报
```
7E000A000A000102030405060708090A000030333032303030313033007E
```

#### 发送心跳消息
```
7E000A000A000102030405060708090A000030333032303030313030007E
```

## 📋 TBOX开发要点总结

### 核心功能要求
1. **建立TCP连接**: 作为客户端连接到 `dk-api.scsoi.com:19999`
2. **监听控车指令**: 通过TCP连接接收云平台发送的控车指令
3. **执行车辆操作**: 根据指令执行实际的车辆解锁/闭锁操作
4. **状态上报**: 将车辆操作结果通过TCP连接上报给云平台
5. **保持连接**: 通过心跳机制保持TCP连接活跃
6. **自动重连**: 连接断开时自动重连


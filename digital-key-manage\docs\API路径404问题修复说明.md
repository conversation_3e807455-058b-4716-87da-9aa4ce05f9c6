# API路径404问题修复说明

## 问题描述
手机端查看申请状态时报错：
```
http://localhost:8080/userApplication/list
{"code":500,"msg":"404 NOT_FOUND"}
```

后台断点也没有进入，说明请求没有到达后端控制器。

## 问题分析

### 1. 系统架构分析
这是一个基于若依框架的微服务架构：
- **网关服务**：运行在8080端口，负责路由转发
- **系统模块**：运行在9201端口，包含用户申请相关接口
- **前端应用**：请求8080端口，通过网关路由到具体服务

### 2. 路径映射问题
```
前端请求：http://localhost:8080/userApplication/list
网关路由：需要转发到系统模块的 /system/userApplication/list
实际路径：系统模块中的 @RequestMapping("/userApplication") + @GetMapping("/list")
```

### 3. 问题根本原因
前端API调用缺少系统模块的路由前缀 `/system`，导致网关无法正确路由请求。

## 修复方案

### 1. 修复前端API路径
将所有用户申请相关的API路径添加 `/system` 前缀：

#### 修复前：
```javascript
// 错误的API路径
url: '/userApplication/list'           // ❌ 缺少系统模块前缀
url: '/userApplication'                // ❌ 缺少系统模块前缀
url: '/userApplication/withdraw/1'     // ❌ 缺少系统模块前缀
```

#### 修复后：
```javascript
// 正确的API路径
url: '/system/userApplication/list'           // ✅ 包含系统模块前缀
url: '/system/userApplication'                // ✅ 包含系统模块前缀
url: '/system/userApplication/withdraw/1'     // ✅ 包含系统模块前缀
```

### 2. 修复的API接口列表

#### 用户申请相关API
- `submitUserApplication`: `/userApplication` → `/system/userApplication`
- `getUserApplicationList`: `/userApplication/list` → `/system/userApplication/list`
- `getUserApplicationDetail`: `/userApplication/{id}` → `/system/userApplication/{id}`
- `withdrawApplication`: `/userApplication/withdraw/{id}` → `/system/userApplication/withdraw/{id}`
- `supplementApplication`: `/userApplication/supplement` → `/system/userApplication/supplement`
- `uploadCertificate`: `/userApplication/upload-certificate` → `/system/userApplication/upload-certificate`

#### 数字钥匙相关API
- `getUserDigitalKeys`: `/dk/digitalKey/my-keys` → `/system/digitalKey/my-keys`
- `activateDigitalKey`: `/dk/digitalKey/activate/{id}` → `/system/digitalKey/activate/{id}`
- `checkKeyPermission`: `/dk/digitalKey/check-permission/{id}` → `/system/digitalKey/check-permission/{id}`

#### 车辆控制相关API
- `unlockVehicle`: `/dk/vehicle/unlock` → `/system/vehicle/unlock`
- `lockVehicle`: `/dk/vehicle/lock` → `/system/vehicle/lock`
- `startVehicle`: `/dk/vehicle/start` → `/system/vehicle/start`
- `getVehicleStatus`: `/dk/vehicle/status/{vin}` → `/system/vehicle/status/{vin}`

## 修复文件

### 前端文件
- `digital-key/api/dk.js` - 修复所有API路径，添加 `/system` 前缀

### 测试文件
- `digital-key/pages/test/api-test.vue` - 添加调试日志，便于测试验证

## 验证步骤

### 1. 重新编译前端
```bash
cd digital-key
npm run dev:mp-weixin
```

### 2. 测试API调用
1. **访问API测试页面**：`/pages/test/api-test`
2. **点击"测试获取申请列表"按钮**
3. **查看控制台日志**：
   ```
   正在调用API: /system/userApplication/list
   API调用成功: {...}
   ```

### 3. 测试申请状态页面
1. **打开申请状态页面**
2. **验证页面正常加载**，不再出现404错误
3. **查看申请列表数据**是否正确显示

### 4. 测试完整申请流程
1. **提交新申请** → 验证提交成功
2. **查看申请状态** → 验证状态正确显示
3. **后台审核** → 验证审核流程正常
4. **前端状态更新** → 验证状态同步正确

## 网关路由配置说明

### 若依框架默认路由规则
```yaml
# 系统模块路由配置（通常在Nacos配置中心）
spring:
  cloud:
    gateway:
      routes:
        - id: ruoyi-system
          uri: lb://ruoyi-system
          predicates:
            - Path=/system/**
          filters:
            - StripPrefix=1
```

### 路由转发流程
```
前端请求: /system/userApplication/list
↓
网关接收: localhost:8080/system/userApplication/list
↓
路由匹配: Path=/system/**
↓
去除前缀: StripPrefix=1 (去除/system)
↓
转发到系统模块: localhost:9201/userApplication/list
↓
控制器处理: @RequestMapping("/userApplication") + @GetMapping("/list")
```

## 预期结果

### ✅ 修复后应该实现
1. **API调用成功**：前端请求能够正确路由到后端
2. **申请状态页面正常**：能够获取和显示申请列表
3. **完整流程正常**：提交申请 → 查看状态 → 审核处理
4. **后台断点生效**：调试时能够进入后端控制器方法

### 🧪 测试验证点
1. **网络请求**：浏览器开发者工具中看到200响应
2. **控制台日志**：前端日志显示API调用成功
3. **后台日志**：后端日志显示请求处理
4. **数据显示**：页面正确显示申请数据

## 注意事项

### 1. 微服务路由规则
- 所有系统模块的API都需要 `/system` 前缀
- 网关会自动去除前缀并转发到对应服务
- 确保Nacos配置中心的路由配置正确

### 2. API一致性
- 前端API路径必须与网关路由规则匹配
- 后端控制器路径保持不变
- 新增API时记得添加正确的前缀

### 3. 调试技巧
- 使用浏览器开发者工具查看网络请求
- 检查请求URL是否包含正确的前缀
- 查看响应状态码和错误信息

## 后续优化建议

1. **统一API管理**：创建API路径常量文件，避免硬编码
2. **自动化测试**：添加API接口的自动化测试
3. **错误处理**：完善404等错误的用户友好提示
4. **文档维护**：保持API文档与实际路径同步

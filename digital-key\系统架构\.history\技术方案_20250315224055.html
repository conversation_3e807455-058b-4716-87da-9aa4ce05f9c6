<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>数字钥匙系统技术方案</title>
    <style>
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            line-height: 1.6;
            color: #333;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f7fa;
        }
        h1, h2, h3, h4 {
            color: #0066cc;
        }
        h1 {
            text-align: center;
            border-bottom: 2px solid #0066cc;
            padding-bottom: 10px;
            margin-bottom: 30px;
        }
        h2 {
            border-bottom: 1px solid #ccc;
            padding-bottom: 5px;
            margin-top: 40px;
            margin-bottom: 20px;
        }
        h3 {
            margin-top: 30px;
        }
        .highlight {
            background-color: #f8f9fa;
            border-left: 4px solid #0066cc;
            padding: 15px;
            margin: 15px 0;
        }
        .note {
            background-color: #e8f4f8;
            border: 1px solid #a8d1e0;
            border-radius: 8px;
            padding: 15px;
            margin: 15px 0;
            box-shadow: 0 2px 4px rgba(0,0,0,0.05);
        }
        .warning {
            background-color: #fff3cd;
            border: 1px solid #ffeeba;
            border-radius: 8px;
            padding: 15px;
            margin: 15px 0;
            box-shadow: 0 2px 4px rgba(0,0,0,0.05);
        }
        .flow-container {
            background-color: #f5f5f5;
            padding: 20px;
            border-radius: 8px;
            margin: 20px 0;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }
        .flow-item {
            margin-bottom: 15px;
            padding: 15px;
            background-color: white;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            transition: transform 0.2s, box-shadow 0.2s;
        }
        .flow-item:hover {
            transform: translateY(-3px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.15);
        }
        .flow-item-number {
            display: inline-block;
            width: 28px;
            height: 28px;
            background-color: #0066cc;
            color: white;
            text-align: center;
            border-radius: 50%;
            margin-right: 10px;
            font-weight: bold;
            line-height: 28px;
        }
        table {
            width: 100%;
            border-collapse: collapse;
            margin: 20px 0;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            border-radius: 8px;
            overflow: hidden;
        }
        th, td {
            border: 1px solid #ddd;
            padding: 12px 15px;
            text-align: left;
        }
        th {
            background-color: #0066cc;
            color: white;
        }
        tr:nth-child(even) {
            background-color: #f2f2f2;
        }
        tr:hover {
            background-color: #e6f2ff;
        }
        img {
            max-width: 100%;
            height: auto;
            border-radius: 8px;
            box-shadow: 0 4px 8px rgba(0,0,0,0.1);
        }
        .security-box {
            border: 2px solid #28a745;
            border-radius: 8px;
            padding: 20px;
            margin: 20px 0;
            background-color: #f8fff8;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }
        
        /* 新增的模块化架构样式 */
        .architecture-container {
            display: flex;
            flex-wrap: wrap;
            gap: 20px;
            margin: 30px 0;
            justify-content: center;
        }
        
        .architecture-module {
            flex: 1 1 300px;
            max-width: 500px;
            border-radius: 12px;
            overflow: hidden;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
            transition: transform 0.3s, box-shadow 0.3s;
            background-color: white;
        }
        
        .architecture-module:hover {
            transform: translateY(-5px);
            box-shadow: 0 8px 25px rgba(0,0,0,0.15);
        }
        
        .module-header {
            background-color: #0066cc;
            color: white;
            padding: 15px 20px;
            font-size: 1.2em;
            font-weight: bold;
            display: flex;
            align-items: center;
            justify-content: space-between;
        }
        
        .module-icon {
            font-size: 1.5em;
            margin-right: 10px;
        }
        
        .module-body {
            padding: 20px;
        }
        
        .module-list {
            list-style-type: none;
            padding: 0;
            margin: 0;
        }
        
        .module-list li {
            padding: 10px 0;
            border-bottom: 1px solid #eee;
            display: flex;
            align-items: flex-start;
        }
        
        .module-list li:last-child {
            border-bottom: none;
        }
        
        .module-list-icon {
            color: #0066cc;
            margin-right: 10px;
            font-size: 1.1em;
            flex-shrink: 0;
            margin-top: 3px;
        }
        
        .module-footer {
            background-color: #f5f7fa;
            padding: 12px 20px;
            font-size: 0.9em;
            color: #666;
            border-top: 1px solid #eee;
        }
        
        /* 连接线样式 */
        .architecture-connections {
            position: relative;
            margin: 20px 0;
            height: 80px;
            display: flex;
            justify-content: center;
            align-items: center;
        }
        
        .connection-line {
            height: 4px;
            background-color: #0066cc;
            position: absolute;
            top: 50%;
            transform: translateY(-50%);
        }
        
        .connection-vertical {
            width: 4px;
            height: 40px;
            background-color: #0066cc;
            position: absolute;
        }
        
        .connection-arrow {
            width: 0;
            height: 0;
            border-left: 8px solid transparent;
            border-right: 8px solid transparent;
            border-top: 12px solid #0066cc;
            position: absolute;
            bottom: 10px;
        }
        
        /* 响应式调整 */
        @media (max-width: 768px) {
            .architecture-container {
                flex-direction: column;
                align-items: center;
            }
            
            .architecture-module {
                width: 100%;
                max-width: 100%;
            }
        }
        
        /* 原有的模块卡片样式优化 */
        .module-card {
            border: 1px solid #ddd;
            border-radius: 8px;
            margin-bottom: 25px;
            overflow: hidden;
            box-shadow: 0 3px 10px rgba(0,0,0,0.1);
            transition: transform 0.2s, box-shadow 0.2s;
        }
        
        .module-card:hover {
            transform: translateY(-3px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.15);
        }
        
        .module-card-header {
            background-color: #0066cc;
            color: white;
            padding: 12px 18px;
            font-weight: bold;
            font-size: 1.1em;
        }
        
        .module-card-body {
            padding: 18px;
            background-color: white;
        }
        
        /* 交互式折叠面板 */
        .collapsible {
            background-color: #f1f8ff;
            color: #0066cc;
            cursor: pointer;
            padding: 15px;
            width: 100%;
            border: none;
            text-align: left;
            outline: none;
            font-size: 1.1em;
            font-weight: bold;
            border-radius: 8px;
            margin-bottom: 10px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            box-shadow: 0 2px 5px rgba(0,0,0,0.05);
        }
        
        .collapsible:after {
            content: '\002B';
            color: #0066cc;
            font-weight: bold;
            float: right;
            margin-left: 5px;
        }
        
        .active:after {
            content: "\2212";
        }
        
        .collapsible-content {
            padding: 0 18px;
            max-height: 0;
            overflow: hidden;
            transition: max-height 0.3s ease-out;
            background-color: white;
            border-radius: 0 0 8px 8px;
            margin-bottom: 15px;
        }
        
        /* 架构图样式 */
        .architecture-connections-container {
            margin: 40px 0;
            padding: 20px;
            background-color: white;
            border-radius: 12px;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
        }
        
        .architecture-diagram {
            margin: 30px 0;
        }
        
        .diagram-container {
            position: relative;
            height: 450px;
            margin: 0 auto;
            max-width: 800px;
        }
        
        .diagram-module {
            position: absolute;
            width: 150px;
            height: 100px;
            background-color: white;
            border-radius: 10px;
            box-shadow: 0 4px 10px rgba(0,0,0,0.15);
            overflow: hidden;
            transition: transform 0.3s, box-shadow 0.3s;
        }
        
        .diagram-module:hover {
            transform: translateY(-5px);
            box-shadow: 0 8px 20px rgba(0,0,0,0.2);
            z-index: 10;
        }
        
        #mobile-module {
            top: 50px;
            left: 50px;
        }
        
        #car-module {
            top: 50px;
            right: 50px;
        }
        
        #cloud-module {
            bottom: 50px;
            left: 50px;
        }
        
        #external-module {
            bottom: 50px;
            right: 50px;
        }
        
        .diagram-header {
            background-color: #0066cc;
            color: white;
            padding: 8px 10px;
            font-weight: bold;
            text-align: center;
            font-size: 0.9em;
        }
        
        .diagram-body {
            display: flex;
            justify-content: center;
            align-items: center;
            height: calc(100% - 35px);
        }
        
        .diagram-icon {
            font-size: 2em;
        }
        
        .connection-lines {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            z-index: 1;
        }
        
        .diagram-legend {
            display: flex;
            justify-content: center;
            flex-wrap: wrap;
            gap: 20px;
            margin-top: 20px;
        }
        
        .legend-item {
            display: flex;
            align-items: center;
            margin: 0 10px;
        }
        
        .legend-color {
            width: 20px;
            height: 3px;
            margin-right: 8px;
        }
        
        .legend-icon {
            margin-right: 8px;
            font-size: 1.2em;
        }
        
        .legend-text {
            font-size: 0.9em;
            color: #666;
        }
        
        @media (max-width: 768px) {
            .diagram-container {
                height: 700px;
            }
            
            #mobile-module, #car-module, #cloud-module, #external-module {
                position: relative;
                margin: 20px auto;
                top: auto;
                left: auto;
                right: auto;
                bottom: auto;
            }
            
            .connection-lines {
                display: none;
            }
        }
        
        /* PPT风格的架构图样式 */
        .ppt-slide {
            background-color: white;
            border-radius: 12px;
            box-shadow: 0 5px 20px rgba(0,0,0,0.15);
            margin: 40px 0;
            overflow: hidden;
            transition: transform 0.3s, box-shadow 0.3s;
        }
        
        .ppt-slide:hover {
            transform: translateY(-5px);
            box-shadow: 0 8px 30px rgba(0,0,0,0.2);
        }
        
        .slide-header {
            background-color: #0066cc;
            color: white;
            padding: 15px 25px;
            text-align: center;
        }
        
        .slide-header h2 {
            margin: 0;
            padding: 0;
            border: none;
            color: white;
            font-size: 1.8em;
        }
        
        .slide-content {
            padding: 30px;
        }
        
        /* 全局架构图样式 */
        .global-architecture {
            position: relative;
            height: 500px;
            margin: 0 auto;
            max-width: 900px;
        }
        
        .arch-layer {
            position: relative;
            width: 100%;
            margin-bottom: 60px;
            text-align: center;
        }
        
        .layer-title {
            background-color: #f1f8ff;
            color: #0066cc;
            padding: 8px 15px;
            border-radius: 20px;
            display: inline-block;
            font-weight: bold;
            margin-bottom: 20px;
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
        }
        
        .layer-modules {
            display: flex;
            justify-content: center;
            gap: 30px;
            flex-wrap: wrap;
        }
        
        .arch-module {
            width: 100px;
            height: 100px;
            background-color: white;
            border-radius: 10px;
            box-shadow: 0 3px 10px rgba(0,0,0,0.1);
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            transition: transform 0.3s, box-shadow 0.3s;
            cursor: pointer;
            position: relative;
            z-index: 2;
        }
        
        .arch-module:hover {
            transform: translateY(-5px) scale(1.05);
            box-shadow: 0 5px 15px rgba(0,0,0,0.15);
        }
        
        .highlight-module {
            border: 2px solid #0066cc;
            box-shadow: 0 3px 15px rgba(0,102,204,0.2);
        }
        
        .module-icon {
            font-size: 2.5em;
            margin-bottom: 8px;
        }
        
        .module-name {
            font-size: 0.9em;
            font-weight: bold;
            color: #333;
        }
        
        /* 连接线样式 */
        .arch-connections {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            z-index: 1;
            pointer-events: none; /* 确保连接线不会阻挡点击事件 */
        }
        
        .connections-svg {
            width: 100%;
            height: 100%;
            overflow: visible; /* 确保SVG内容不会被裁剪 */
        }
        
        .connection-line {
            stroke: #0066cc;
            stroke-width: 3;
            fill: none;
            stroke-dasharray: 5,5;
            animation: dash 30s linear infinite;
        }
        
        @keyframes dash {
            to {
                stroke-dashoffset: 1000;
            }
        }
        
        .connection-text {
            fill: #0066cc;
            font-size: 14px;
            font-weight: bold;
        }
        
        /* 新增：固定层级位置 */
        .user-layer {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
        }
        
        .app-layer {
            position: absolute;
            top: 120px;
            left: 0;
            width: 100%;
        }
        
        .service-layer {
            position: absolute;
            top: 240px;
            left: 0;
            width: 100%;
        }
        
        .car-layer {
            position: absolute;
            top: 360px;
            left: 0;
            width: 100%;
        }
        
        /* 图例样式 */
        .architecture-legend {
            margin-top: 30px;
            text-align: center;
        }
        
        .legend-title {
            font-weight: bold;
            margin-bottom: 10px;
            color: #555;
        }
        
        .legend-items {
            display: flex;
            justify-content: center;
            gap: 20px;
            flex-wrap: wrap;
        }
        
        .legend-item {
            display: flex;
            align-items: center;
            margin: 0 10px;
        }
        
        .legend-color {
            width: 30px;
            height: 3px;
            margin-right: 8px;
        }
        
        .connection-legend {
            background-color: #0066cc;
            height: 3px;
            width: 30px;
        }
        
        .legend-module {
            width: 20px;
            height: 20px;
            border-radius: 4px;
            margin-right: 8px;
        }
        
        .highlight-legend {
            background-color: white;
            border: 2px solid #0066cc;
        }
        
        .normal-legend {
            background-color: white;
            border: 1px solid #ddd;
        }
        
        .legend-text {
            font-size: 0.9em;
            color: #666;
        }
        
        /* 核心组件卡片样式 */
        .core-components {
            display: flex;
            justify-content: space-between;
            gap: 20px;
            flex-wrap: wrap;
        }
        
        .component-card {
            flex: 1 1 250px;
            background-color: white;
            border-radius: 10px;
            box-shadow: 0 3px 15px rgba(0,0,0,0.1);
            overflow: hidden;
            transition: transform 0.3s, box-shadow 0.3s;
        }
        
        .component-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 8px 25px rgba(0,0,0,0.15);
        }
        
        .component-header {
            background-color: #0066cc;
            color: white;
            padding: 15px;
            display: flex;
            align-items: center;
        }
        
        .component-icon {
            font-size: 2em;
            margin-right: 15px;
        }
        
        .component-title {
            font-size: 1.3em;
            font-weight: bold;
        }
        
        .component-body {
            padding: 20px;
        }
        
        .component-body p {
            margin-top: 0;
            margin-bottom: 20px;
            color: #555;
        }
        
        .key-features {
            display: flex;
            flex-wrap: wrap;
            gap: 15px;
        }
        
        .feature {
            display: flex;
            align-items: center;
            background-color: #f5f7fa;
            padding: 8px 15px;
            border-radius: 20px;
            flex: 1 1 auto;
        }
        
        .feature-icon {
            margin-right: 8px;
            font-size: 1.2em;
        }
        
        .feature-text {
            font-size: 0.9em;
            font-weight: bold;
            color: #444;
        }
        
        /* 响应式调整 */
        @media (max-width: 768px) {
            .global-architecture {
                height: auto;
            }
            
            .arch-layer {
                margin-bottom: 30px;
            }
            
            .arch-connections {
                display: none;
            }
            
            .component-card {
                margin-bottom: 20px;
            }
        }
        
        /* 数据流和交互关系样式 */
        .data-flow-container {
            position: relative;
            display: grid;
            grid-template-columns: 1fr 1fr 1fr;
            grid-template-rows: auto auto;
            gap: 30px;
            margin: 30px 0;
        }
        
        .flow-endpoint {
            background-color: white;
            border-radius: 10px;
            box-shadow: 0 3px 15px rgba(0,0,0,0.1);
            padding: 20px;
            text-align: center;
            transition: transform 0.3s, box-shadow 0.3s;
        }
        
        .flow-endpoint:hover {
            transform: translateY(-5px);
            box-shadow: 0 8px 25px rgba(0,0,0,0.15);
        }
        
        #mobile-endpoint {
            grid-column: 1;
            grid-row: 1;
        }
        
        #cloud-endpoint {
            grid-column: 3;
            grid-row: 1;
        }
        
        #car-endpoint {
            grid-column: 2;
            grid-row: 2;
        }
        
        .endpoint-icon {
            font-size: 3em;
            margin-bottom: 10px;
        }
        
        .endpoint-title {
            font-size: 1.2em;
            font-weight: bold;
            margin-bottom: 15px;
            color: #0066cc;
        }
        
        .endpoint-features {
            display: flex;
            flex-direction: column;
            gap: 8px;
        }
        
        .endpoint-feature {
            background-color: #f5f7fa;
            padding: 8px;
            border-radius: 5px;
            font-size: 0.9em;
            color: #444;
        }
        
        .flow-arrows {
            grid-column: 2;
            grid-row: 1;
            display: flex;
            flex-direction: column;
            justify-content: space-around;
            align-items: center;
            gap: 15px;
        }
        
        .flow-group {
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: 5px;
        }
        
        .flow-label {
            font-size: 0.9em;
            font-weight: bold;
            color: #0066cc;
        }
        
        .flow-arrow {
            position: relative;
            width: 150px;
            height: 30px;
        }
        
        .arrow-line {
            position: absolute;
            top: 50%;
            left: 0;
            right: 0;
            height: 3px;
            background-color: #0066cc;
            transform: translateY(-50%);
        }
        
        .right-arrow .arrow-head {
            position: absolute;
            top: 50%;
            right: -5px;
            width: 0;
            height: 0;
            border-top: 8px solid transparent;
            border-bottom: 8px solid transparent;
            border-left: 12px solid #0066cc;
            transform: translateY(-50%);
        }
        
        .left-arrow .arrow-line {
            background-color: #28a745;
        }
        
        .left-arrow .arrow-head {
            position: absolute;
            top: 50%;
            left: -5px;
            width: 0;
            height: 0;
            border-top: 8px solid transparent;
            border-bottom: 8px solid transparent;
            border-right: 12px solid #28a745;
            transform: translateY(-50%);
        }
        
        .diagonal-arrow-down {
            transform: rotate(45deg);
            width: 120px;
        }
        
        .diagonal-arrow-down .arrow-line {
            background-color: #ff9800;
        }
        
        .diagonal-arrow-down .arrow-head {
            position: absolute;
            top: 50%;
            right: -5px;
            width: 0;
            height: 0;
            border-top: 8px solid transparent;
            border-bottom: 8px solid transparent;
            border-left: 12px solid #ff9800;
            transform: translateY(-50%);
        }
        
        .diagonal-arrow-up {
            transform: rotate(-45deg);
            width: 120px;
        }
        
        .diagonal-arrow-up .arrow-line {
            background-color: #9c27b0;
        }
        
        .diagonal-arrow-up .arrow-head {
            position: absolute;
            top: 50%;
            right: -5px;
            width: 0;
            height: 0;
            border-top: 8px solid transparent;
            border-bottom: 8px solid transparent;
            border-left: 12px solid #9c27b0;
            transform: translateY(-50%);
        }
        
        .data-flow-description {
            margin-top: 40px;
            background-color: #f5f7fa;
            padding: 20px;
            border-radius: 10px;
        }
        
        .data-flow-description h3 {
            margin-top: 0;
            color: #0066cc;
            margin-bottom: 15px;
        }
        
        .data-flow-description ol {
            padding-left: 20px;
        }
        
        .data-flow-description li {
            margin-bottom: 10px;
            color: #444;
        }
        
        /* 响应式调整 */
        @media (max-width: 768px) {
            .global-architecture {
                height: auto;
            }
            
            .arch-layer {
                margin-bottom: 30px;
            }
            
            .arch-connections {
                display: none;
            }
            
            .component-card {
                margin-bottom: 20px;
            }
            
            .data-flow-container {
                grid-template-columns: 1fr;
                grid-template-rows: auto auto auto auto;
            }
            
            #mobile-endpoint, #cloud-endpoint, #car-endpoint {
                grid-column: 1;
            }
            
            #mobile-endpoint {
                grid-row: 1;
            }
            
            .flow-arrows {
                grid-column: 1;
                grid-row: 2;
                margin: 20px 0;
            }
            
            #cloud-endpoint {
                grid-row: 3;
            }
            
            #car-endpoint {
                grid-row: 4;
            }
            
            .flow-arrow {
                width: 100px;
            }
        }
    </style>
</head>
<body>

<h1>数字钥匙系统技术方案</h1>

<div class="note">
    <strong>项目简介：</strong>数字钥匙系统是一套完整的汽车数字钥匙解决方案，包含手机APP对应的SDK、云平台SDK以及完整的车端软硬件开发的集成产品。该系统允许用户通过手机APP远程或近场控制车辆，实现传统物理钥匙的数字化替代。
</div>

<!-- 新增专业架构图展示 -->
<div class="architecture-overview">
    <h2>系统整体架构</h2>
    <div class="architecture-diagram-container">
        <div class="architecture-layers">
            <!-- 用户层 -->
            <div class="arch-layer-container">
                <div class="arch-layer-title">用户层</div>
                <div class="arch-layer-content">
                    <div class="arch-component primary-user">
                        <div class="component-icon">👤</div>
                        <div class="component-label">车主</div>
                    </div>
                    <div class="arch-component secondary-user">
                        <div class="component-icon">👥</div>
                        <div class="component-label">共享用户</div>
                    </div>
                </div>
            </div>
            
            <!-- 应用层 -->
            <div class="arch-layer-container">
                <div class="arch-layer-title">应用层</div>
                <div class="arch-layer-content">
                    <div class="arch-component core-component">
                        <div class="component-icon">📱</div>
                        <div class="component-label">手机端</div>
                        <div class="component-details">
                            <span class="detail-item">钥匙管理</span>
                            <span class="detail-item">车辆控制</span>
                            <span class="detail-item">安全存储</span>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- 服务层 -->
            <div class="arch-layer-container">
                <div class="arch-layer-title">服务层</div>
                <div class="arch-layer-content">
                    <div class="arch-component core-component">
                        <div class="component-icon">☁️</div>
                        <div class="component-label">钥匙云平台</div>
                        <div class="component-details">
                            <span class="detail-item">钥匙生成</span>
                            <span class="detail-item">安全认证</span>
                            <span class="detail-item">权限管理</span>
                        </div>
                    </div>
                    <div class="arch-component">
                        <div class="component-icon">🌐</div>
                        <div class="component-label">TSP平台</div>
                    </div>
                    <div class="arch-component">
                        <div class="component-icon">🏭</div>
                        <div class="component-label">OEM平台</div>
                    </div>
                </div>
            </div>
            
            <!-- 车端层 -->
            <div class="arch-layer-container">
                <div class="arch-layer-title">车端层</div>
                <div class="arch-layer-content">
                    <div class="arch-component core-component">
                        <div class="component-icon">🚗</div>
                        <div class="component-label">车端</div>
                        <div class="component-details">
                            <span class="detail-item">钥匙验证</span>
                            <span class="detail-item">指令执行</span>
                            <span class="detail-item">通信模块</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- 连接线 -->
        <div class="connection-container">
            <svg class="connections-svg" viewBox="0 0 1000 600" preserveAspectRatio="xMidYMid meet">
                <!-- 定义箭头 -->
                <defs>
                    <marker id="arrow-https" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
                        <polygon points="0 0, 10 3.5, 0 7" fill="#0066cc" />
                    </marker>
                    <marker id="arrow-bluetooth" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
                        <polygon points="0 0, 10 3.5, 0 7" fill="#5c6bc0" />
                    </marker>
                    <marker id="arrow-4g" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
                        <polygon points="0 0, 10 3.5, 0 7" fill="#43a047" />
                    </marker>
                </defs>
                
                <!-- 用户到应用层 -->
                <path d="M500,120 L500,180" stroke="#0066cc" stroke-width="2" fill="none" marker-end="url(#arrow-https)" />
                
                <!-- 应用层到服务层 -->
                <path d="M500,280 L500,340" stroke="#0066cc" stroke-width="2" fill="none" marker-end="url(#arrow-https)" />
                <text x="510" y="310" class="connection-label https-label">HTTPS</text>
                
                <!-- 服务层到车端层 -->
                <path d="M500,440 L500,500" stroke="#43a047" stroke-width="2" fill="none" marker-end="url(#arrow-4g)" />
                <text x="510" y="470" class="connection-label cellular-label">4G/5G</text>
                
                <!-- 应用层到车端层 -->
                <path d="M400,280 C350,380 350,450 400,500" stroke="#5c6bc0" stroke-width="2" fill="none" stroke-dasharray="5,3" marker-end="url(#arrow-bluetooth)" />
                <text x="330" y="390" class="connection-label bluetooth-label">蓝牙</text>
            </svg>
        </div>
    </div>
    
    <!-- 图例 -->
    <div class="architecture-legend">
        <div class="legend-title">图例说明</div>
        <div class="legend-items">
            <div class="legend-item">
                <div class="legend-line https-line"></div>
                <div class="legend-text">HTTPS通信</div>
            </div>
            <div class="legend-item">
                <div class="legend-line bluetooth-line"></div>
                <div class="legend-text">蓝牙通信</div>
            </div>
            <div class="legend-item">
                <div class="legend-line cellular-line"></div>
                <div class="legend-text">4G/5G通信</div>
            </div>
            <div class="legend-item">
                <div class="legend-box core-box"></div>
                <div class="legend-text">核心组件</div>
            </div>
            <div class="legend-item">
                <div class="legend-box normal-box"></div>
                <div class="legend-text">外部组件</div>
            </div>
        </div>
    </div>
    
    <!-- 架构说明 -->
    <div class="architecture-description">
        <h3>架构设计说明</h3>
        <p>数字钥匙系统采用分层架构设计，从上至下分为用户层、应用层、服务层和车端层，各层之间通过标准化接口进行通信：</p>
        <ul>
            <li><strong>用户层</strong>：包括车主和共享用户，他们通过手机APP与系统交互</li>
            <li><strong>应用层</strong>：手机端APP，负责用户交互、钥匙管理和车辆控制，通过HTTPS与云平台通信，通过蓝牙与车端直接通信</li>
            <li><strong>服务层</strong>：包括核心的钥匙云平台和外部的TSP平台、OEM平台，负责钥匙生命周期管理、安全认证和数据处理</li>
            <li><strong>车端层</strong>：车载设备，负责验证钥匙并执行控制指令，可通过蓝牙与手机直接通信，也可通过4G/5G与云平台通信</li>
        </ul>
        <p>系统支持两种控车模式：</p>
        <ol>
            <li><strong>近场控车</strong>：手机通过蓝牙直接与车辆通信，实现低延迟的控车体验</li>
            <li><strong>远程控车</strong>：手机通过HTTPS与云平台通信，云平台再通过4G/5G与车辆通信</li>
        </ol>
    </div>
</div>

<!-- 新增PPT风格的整体架构图 -->
<div class="ppt-slide">
    <div class="slide-header">
        <h2>数字钥匙系统整体架构</h2>
    </div>
    <div class="slide-content">
        <div class="global-architecture">
            <!-- 顶部层：用户层 -->
            <div class="arch-layer user-layer">
                <div class="layer-title">用户层</div>
                <div class="layer-modules">
                    <div class="arch-module">
                        <div class="module-icon">👤</div>
                        <div class="module-name">车主</div>
                    </div>
                    <div class="arch-module">
                        <div class="module-icon">👥</div>
                        <div class="module-name">共享用户</div>
                    </div>
                </div>
            </div>
            
            <!-- 第二层：应用层 -->
            <div class="arch-layer app-layer">
                <div class="layer-title">应用层</div>
                <div class="layer-modules">
                    <div class="arch-module highlight-module">
                        <div class="module-icon">📱</div>
                        <div class="module-name">手机端</div>
                    </div>
                </div>
            </div>
            
            <!-- 第三层：服务层 -->
            <div class="arch-layer service-layer">
                <div class="layer-title">服务层</div>
                <div class="layer-modules">
                    <div class="arch-module highlight-module">
                        <div class="module-icon">☁️</div>
                        <div class="module-name">钥匙云平台</div>
                    </div>
                    <div class="arch-module">
                        <div class="module-icon">🌐</div>
                        <div class="module-name">TSP平台</div>
                    </div>
                    <div class="arch-module">
                        <div class="module-icon">🏭</div>
                        <div class="module-name">OEM平台</div>
                    </div>
                </div>
            </div>
            
            <!-- 第四层：车端层 -->
            <div class="arch-layer car-layer">
                <div class="layer-title">车端层</div>
                <div class="layer-modules">
                    <div class="arch-module highlight-module">
                        <div class="module-icon">🚗</div>
                        <div class="module-name">车端</div>
                    </div>
                </div>
            </div>
            
            <!-- 连接线 -->
            <div class="arch-connections">
                <svg class="connections-svg" viewBox="0 0 1000 600" preserveAspectRatio="xMidYMid meet">
                    <!-- 箭头标记定义 -->
                    <defs>
                        <marker id="arrowhead" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
                            <polygon points="0 0, 10 3.5, 0 7" fill="#0066cc" />
                        </marker>
                    </defs>
                    
                    <!-- 用户层到应用层 -->
                    <path d="M500,100 L500,140" class="connection-line" marker-end="url(#arrowhead)" />
                    
                    <!-- 应用层到服务层 -->
                    <path d="M500,220 L500,260" class="connection-line" marker-end="url(#arrowhead)" />
                    <text x="520" y="240" class="connection-text">HTTPS</text>
                    
                    <!-- 服务层到车端层 -->
                    <path d="M500,340 L500,380" class="connection-line" marker-end="url(#arrowhead)" />
                    <text x="520" y="360" class="connection-text">4G/5G</text>
                    
                    <!-- 应用层到车端层 -->
                    <path d="M400,220 C350,300 350,350 400,380" class="connection-line" marker-end="url(#arrowhead)" />
                    <text x="320" y="300" class="connection-text">蓝牙</text>
                    
                    <!-- TSP平台到车端 -->
                    <path d="M600,320 C650,350 650,380 600,400" class="connection-line" marker-end="url(#arrowhead)" />
                    <text x="660" y="360" class="connection-text">远程控制</text>
                </svg>
            </div>
        </div>
        
        <div class="architecture-legend">
            <div class="legend-title">图例说明</div>
            <div class="legend-items">
                <div class="legend-item">
                    <div class="legend-color connection-legend"></div>
                    <div class="legend-text">数据流向</div>
                </div>
                <div class="legend-item">
                    <div class="legend-module highlight-legend"></div>
                    <div class="legend-text">核心组件</div>
                </div>
                <div class="legend-item">
                    <div class="legend-module normal-legend"></div>
                    <div class="legend-text">外部组件</div>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="ppt-slide">
    <div class="slide-header">
        <h2>核心组件功能概述</h2>
    </div>
    <div class="slide-content">
        <div class="core-components">
            <div class="component-card">
                <div class="component-header">
                    <div class="component-icon">📱</div>
                    <div class="component-title">手机端</div>
                </div>
                <div class="component-body">
                    <p>负责用户交互、钥匙管理和车辆控制，通过蓝牙与车辆通信，通过网络与云平台交互。</p>
                    <div class="key-features">
                        <div class="feature">
                            <div class="feature-icon">🔑</div>
                            <div class="feature-text">钥匙管理</div>
                        </div>
                        <div class="feature">
                            <div class="feature-icon">🚗</div>
                            <div class="feature-text">车辆控制</div>
                        </div>
                        <div class="feature">
                            <div class="feature-icon">🔒</div>
                            <div class="feature-text">安全存储</div>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="component-card">
                <div class="component-header">
                    <div class="component-icon">☁️</div>
                    <div class="component-title">钥匙云平台</div>
                </div>
                <div class="component-body">
                    <p>系统的核心服务中心，负责钥匙生命周期管理、安全认证和数据处理。</p>
                    <div class="key-features">
                        <div class="feature">
                            <div class="feature-icon">🔑</div>
                            <div class="feature-text">钥匙生成</div>
                        </div>
                        <div class="feature">
                            <div class="feature-icon">🔒</div>
                            <div class="feature-text">安全认证</div>
                        </div>
                        <div class="feature">
                            <div class="feature-icon">🌐</div>
                            <div class="feature-text">接口服务</div>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="component-card">
                <div class="component-header">
                    <div class="component-icon">🚗</div>
                    <div class="component-title">车端</div>
                </div>
                <div class="component-body">
                    <p>安装在车辆上的硬件和软件系统，负责验证钥匙并执行控制指令。</p>
                    <div class="key-features">
                        <div class="feature">
                            <div class="feature-icon">🔒</div>
                            <div class="feature-text">钥匙验证</div>
                        </div>
                        <div class="feature">
                            <div class="feature-icon">⚙️</div>
                            <div class="feature-text">指令执行</div>
                        </div>
                        <div class="feature">
                            <div class="feature-icon">📡</div>
                            <div class="feature-text">通信模块</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<h2>一、系统架构概述</h2>

<p>数字钥匙系统由以下几个主要部分组成：</p>

<div class="architecture-container">
    <div class="architecture-module">
        <div class="module-header">
            <span class="module-icon">📱</span>
            <span>手机端</span>
        </div>
        <div class="module-body">
            <ul class="module-list">
                <li class="module-list-item">
                    <span class="module-list-icon">🔑</span>
                    <strong>钥匙管理模块</strong>：管理用户的数字钥匙，包括添加、删除、共享等功能
                </li>
                <li class="module-list-item">
                    <span class="module-list-icon">🚗</span>
                    <strong>车辆控制模块</strong>：提供车辆控制功能，如开锁、关锁、启动等
                </li>
                <li class="module-list-item">
                    <span class="module-list-icon">🎧</span>
                    <strong>蓝牙通信模块</strong>：负责与车端进行蓝牙通信
                </li>
                <li class="module-list-item">
                    <span class="module-list-icon">🔒</span>
                    <strong>安全存储模块</strong>：安全存储数字钥匙和相关密钥材料
                </li>
                <li class="module-list-item">
                    <span class="module-list-icon">⏰</span>
                    <strong>时间同步模块</strong>：与服务器进行时间同步，确保安全认证的时间准确性
                </li>
                <li class="module-list-item">
                    <span class="module-list-icon">🔒</span>
                    <strong>暗号交换模块</strong>：负责与车端协商生成临时暗号，确保通信安全
                </li>
                <li class="module-list-item">
                    <span class="module-list-icon">🤖</span>
                    <strong>智能场景管理模块</strong>：负责检测用户状态与环境，优化电量与连接策略
                </li>
            </ul>
        </div>
    </div>

    <div class="architecture-module">
        <div class="module-header">
            <span class="module-icon">🚗</span>
            <span>车端</span>
        </div>
        <div class="module-body">
            <ul class="module-list">
                <li class="module-list-item">
                    <span class="module-list-icon">🎧</span>
                    <strong>蓝牙通信模块</strong>：负责与手机端进行蓝牙通信
                </li>
                <li class="module-list-item">
                    <span class="module-list-icon">🔒</span>
                    <strong>钥匙验证模块</strong>：验证数字钥匙的有效性和权限
                </li>
                <li class="module-list-item">
                    <span class="module-list-icon">🚗</span>
                    <strong>指令执行模块</strong>：执行控制指令，如开锁、关锁、启动等
                </li>
                <li class="module-list-item">
                    <span class="module-list-icon">🔒</span>
                    <strong>安全存储模块</strong>：安全存储密钥材料和相关配置
                </li>
                <li class="module-list-item">
                    <span class="module-list-icon">🌐</span>
                    <strong>远程通信模块(T-Box)</strong>：与TSP平台进行通信，接收远程控制指令
                </li>
                <li class="module-list-item">
                    <span class="module-list-icon">⏰</span>
                    <strong>时间同步模块</strong>：与服务器进行时间同步，确保安全认证准确
                </li>
                <li class="module-list-item">
                    <span class="module-list-icon">🔒</span>
                    <strong>暗号交换模块</strong>：与手机端协商生成临时暗号，确保通信安全
                </li>
                <li class="module-list-item">
                    <span class="module-list-icon">🤖</span>
                    <strong>用户行为分析模块</strong>：实现无感控车的核心算法，处理距离计算
                </li>
            </ul>
        </div>
    </div>

    <div class="architecture-module">
        <div class="module-header">
            <span class="module-icon">🔒</span>
            <span>钥匙云平台</span>
        </div>
        <div class="module-body">
            <ul class="module-list">
                <li class="module-list-item">
                    <span class="module-list-icon">🔑</span>
                    <strong>钥匙生命周期管理</strong>：管理数字钥匙的创建、更新、撤销等
                </li>
                <li class="module-list-item">
                    <span class="module-list-icon">🚗</span>
                    <strong>车辆关联服务</strong>：将VIN码与车辆信息关联
                </li>
                <li class="module-list-item">
                    <span class="module-list-icon">🔒</span>
                    <strong>安全认证中心</strong>：提供安全认证服务
                </li>
                <li class="module-list-item">
                    <span class="module-list-icon">🔒</span>
                    <strong>密钥管理系统</strong>：管理系统中的各类密钥
                </li>
                <li class="module-list-item">
                    <span class="module-list-icon">⏰</span>
                    <strong>时间服务器</strong>：提供标准时间服务
                </li>
                <li class="module-list-item">
                    <span class="module-list-icon">🌐</span>
                    <strong>统一接口服务</strong>：提供对外接口服务
                </li>
                <li class="module-list-item">
                    <span class="module-list-icon">🔒</span>
                    <strong>安全通信通道</strong>：提供加密通信，防止消息被窃听或篡改
                </li>
                <li class="module-list-item">
                    <span class="module-list-icon">🤖</span>
                    <strong>异常监控与处理</strong>：监控异常登录，分析异常使用模式
                </li>
            </ul>
        </div>
    </div>

    <div class="architecture-module">
        <div class="module-header">
            <span class="module-icon">🌐</span>
            <span>外部平台</span>
        </div>
        <div class="module-body">
            <ul class="module-list">
                <li class="module-list-item">
                    <span class="module-list-icon">🌐</span>
                    <strong>TSP平台</strong>：车联网服务提供商平台，提供车辆远程监控、远程控制等
                </li>
                <li class="module-list-item">
                    <span class="module-list-icon">🚗</span>
                    <strong>OEM平台</strong>：汽车制造商平台，提供车辆生产信息、配置信息等
                </li>
            </ul>
        </div>
    </div>
</div>

<div class="architecture-connections-container">
    <h3>系统架构连接关系</h3>
    <div class="architecture-diagram">
        <div class="diagram-container">
            <!-- 手机端 -->
            <div class="diagram-module" id="mobile-module">
                <div class="diagram-header">手机端</div>
                <div class="diagram-body">
                    <div class="diagram-icon">📱</div>
                </div>
            </div>
            
            <!-- 车端 -->
            <div class="diagram-module" id="car-module">
                <div class="diagram-header">车端</div>
                <div class="diagram-body">
                    <div class="diagram-icon">🚗</div>
                </div>
            </div>
            
            <!-- 云平台 -->
            <div class="diagram-module" id="cloud-module">
                <div class="diagram-header">钥匙云平台</div>
                <div class="diagram-body">
                    <div class="diagram-icon">☁️</div>
                </div>
            </div>
            
            <!-- 外部平台 -->
            <div class="diagram-module" id="external-module">
                <div class="diagram-header">外部平台</div>
                <div class="diagram-body">
                    <div class="diagram-icon">🌐</div>
                </div>
            </div>
            
            <!-- 连接线 -->
            <svg class="connection-lines">
                <!-- 手机端到车端 -->
                <path d="M150,100 C150,150 350,150 350,100" stroke="#0066cc" stroke-width="3" fill="none" marker-end="url(#arrowhead)"></path>
                <text x="250" y="170" text-anchor="middle" fill="#0066cc">蓝牙通信</text>
                
                <!-- 手机端到云平台 -->
                <path d="M120,150 C120,250 120,250 120,300" stroke="#0066cc" stroke-width="3" fill="none" marker-end="url(#arrowhead)"></path>
                <text x="80" y="225" text-anchor="middle" fill="#0066cc">数据同步</text>
                
                <!-- 车端到云平台 -->
                <path d="M380,150 C380,250 380,250 380,300" stroke="#0066cc" stroke-width="3" fill="none" marker-end="url(#arrowhead)"></path>
                <text x="420" y="225" text-anchor="middle" fill="#0066cc">远程控制</text>
                
                <!-- 云平台到外部平台 -->
                <path d="M200,350 C300,350 300,350 400,350" stroke="#0066cc" stroke-width="3" fill="none" marker-end="url(#arrowhead)"></path>
                <text x="300" y="330" text-anchor="middle" fill="#0066cc">数据交换</text>
                
                <!-- 箭头标记定义 -->
                <defs>
                    <marker id="arrowhead" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
                        <polygon points="0 0, 10 3.5, 0 7" fill="#0066cc" />
                    </marker>
                </defs>
            </svg>
        </div>
    </div>
    <div class="diagram-legend">
        <div class="legend-item">
            <div class="legend-color" style="background-color: #0066cc;"></div>
            <div class="legend-text">数据流向</div>
        </div>
        <div class="legend-item">
            <div class="legend-icon">📱</div>
            <div class="legend-text">手机端组件</div>
        </div>
        <div class="legend-item">
            <div class="legend-icon">🚗</div>
            <div class="legend-text">车端组件</div>
        </div>
        <div class="legend-item">
            <div class="legend-icon">☁️</div>
            <div class="legend-text">云平台组件</div>
        </div>
        <div class="legend-item">
            <div class="legend-icon">🌐</div>
            <div class="legend-text">外部平台</div>
        </div>
    </div>
</div>

<h2>二、系统功能流程</h2>

<h3>2.1 首次蓝牙配对流程</h3>

<div class="note">
    首次蓝牙配对是用户首次使用数字钥匙与车辆建立连接的过程，是整个系统安全性的基础。
</div>

<div class="flow-container">
    <div class="flow-item">
        <span class="flow-item-number">1</span>
        <strong>用户在APP中选择添加数字钥匙</strong>
    </div>
    <div class="flow-item">
        <span class="flow-item-number">2</span>
        <strong>扫描车辆二维码或输入VIN码识别车辆</strong>
    </div>
    <div class="flow-item">
        <span class="flow-item-number">3</span>
        <strong>云平台生成虚拟密钥和配对令牌</strong><br>
        这些关键信息会安全地传输到手机中，它们就像一把特殊的钥匙，只有持有这把钥匙的手机才能与对应的车辆建立连接。
    </div>
    <div class="flow-item">
        <span class="flow-item-number">4</span>
        <strong>云平台同时向车辆发送根密钥和授权手机信息</strong><br>
        根密钥是车辆安全验证的基础，授权手机信息让车辆知道哪些手机有权限连接。
    </div>
    <div class="flow-item">
        <span class="flow-item-number">5</span>
        <strong>手机与车辆建立蓝牙连接</strong><br>
        手机通过蓝牙搜索并连接到车辆。
    </div>
    <div class="flow-item">
        <span class="flow-item-number">6</span>
        <strong>双方进行身份验证和安全通道建立</strong><br>
        手机和车辆互相验证身份，确保双方都是授权的设备，并建立加密的通信通道。
    </div>
    <div class="flow-item">
        <span class="flow-item-number">7</span>
        <strong>存储配对信息到安全区域</strong><br>
        手机将配对信息存储在手机的安全区域（如TEE、SE），车辆将配对信息存储在车载安全单元中。
    </div>
    <div class="flow-item">
        <span class="flow-item-number">8</span>
        <strong>配对结果上报至云平台</strong><br>
        手机和车辆都向云平台报告配对结果，云平台记录配对关系。
    </div>
</div>

<div class="security-box">
    <strong>配对安全保障：</strong>
    <ul>
        <li>所有通信均采用TLS 1.3加密</li>
        <li>密钥生成使用非对称加密技术</li>
        <li>敏感信息存储在特殊安全区域</li>
        <li>配对过程使用挑战-响应机制防止重放攻击</li>
        <li>通过蓝牙信号强度(RSSI)验证距离，防止中继攻击</li>
    </ul>
</div>

<h3>2.2 无感控车流程</h3>

<div class="note">
    无感控车是手机在不需要用户主动操作的情况下，自动与车辆建立蓝牙连接并执行预设操作的功能。例如，用户走近车辆时自动开锁，走远时自动上锁。
</div>

<div class="flow-container">
    <div class="flow-item">
        <span class="flow-item-number">1</span>
        <strong>系统唤醒后台APP并检查连接条件</strong><br>
        系统会检查用户是否启用无感连接、手机电量是否充足、用户活动状态等条件。
    </div>
    <div class="flow-item">
        <span class="flow-item-number">2</span>
        <strong>手机自动发起蓝牙连接请求</strong><br>
        APP从安全区域读取连接信息，自动向车辆发起连接请求。
    </div>
    <div class="flow-item">
        <span class="flow-item-number">3</span>
        <strong>车端验证连接请求并接受连接</strong><br>
        车辆检查连接请求的合法性，包括验证设备ID和连接信息。
    </div>
    <div class="flow-item">
        <span class="flow-item-number">4</span>
        <strong>双方进行安全认证并建立安全通道</strong><br>
        手机和车辆通过挑战-响应机制相互验证身份，建立加密通信通道。
    </div>
    <div class="flow-item">
        <span class="flow-item-number">5</span>
        <strong>车端计算与手机的距离并同步状态</strong><br>
        车辆通过蓝牙信号强度(RSSI)计算与手机的距离，并向手机发送车辆当前状态。
    </div>
    <div class="flow-item">
        <span class="flow-item-number">6</span>
        <strong>根据距离和预设策略执行自动控车操作</strong><br>
        系统根据计算出的距离和用户预设的策略，自动执行相应的控车操作，如接近时解锁，离开时锁车。
    </div>
    <div class="flow-item">
        <span class="flow-item-number">7</span>
        <strong>持续监控距离变化，在用户离开时执行安全措施</strong><br>
        系统持续监控手机与车辆的距离，当用户离开时执行安全措施，如自动锁车。
    </div>
</div>

<div class="highlight">
    <strong>无感控车优化策略：</strong>
    <ul>
        <li><strong>系统级唤醒</strong> - 利用操作系统提供的后台唤醒机制</li>
        <li><strong>上下文感知扫描</strong> - 根据位置、时间、活动状态调整扫描频率</li>
        <li><strong>电量自适应</strong> - 根据手机电量调整扫描策略</li>
        <li><strong>学习优化</strong> - 记录用户习惯，优化扫描策略</li>
        <li><strong>多因素触发</strong> - 综合多种因素决定是否发起连接</li>
        <li><strong>防误触机制</strong> - 避免意外连接</li>
    </ul>
</div>

<h2>三、安全设计</h2>

<h3>3.1 通信安全</h3>

<div class="module-card">
    <div class="module-card-header">通信加密</div>
    <div class="module-card-body">
        <ul>
            <li>所有网络通信均采用TLS 1.3加密，支持双向TLS认证</li>
            <li>蓝牙通信支持蓝牙4.2及以上版本，使用加密通道</li>
            <li>关键数据传输时使用端到端加密，即使通道被攻破也无法读取</li>
        </ul>
    </div>
</div>

<div class="module-card">
    <div class="module-card-header">认证与授权</div>
    <div class="module-card-body">
        <ul>
            <li>基于PKI的证书认证 - 确保通信双方身份</li>
            <li>API Key + 签名认证 - 确保API调用安全</li>
            <li>OAuth 2.0认证 - 提供标准的授权框架</li>
            <li>设备身份认证 - 确保只有合法设备能接入系统</li>
            <li>权限分级管理 - 不同操作需要不同级别的安全验证</li>
        </ul>
    </div>
</div>

<h3>3.2 数据安全</h3>

<div class="module-card">
    <div class="module-card-body">
        <ul>
            <li>敏感数据传输和存储时进行加密，保护数据的机密性</li>
            <li>使用数据签名验证确保数据完整性，防止被篡改</li>
            <li>使用哈希函数等技术保障数据完整性</li>
            <li>对敏感个人信息进行脱敏处理，保护用户隐私</li>
            <li>定期数据备份和灾难恢复机制，确保数据安全</li>
        </ul>
    </div>
</div>

<h3>3.3 密钥管理</h3>

<div class="highlight">
    密钥管理是整个安全体系的核心，采用分层密钥架构，确保即使部分密钥泄露也不会危及整个系统。
</div>

<div class="module-card">
    <div class="module-card-header">密钥生成与存储</div>
    <div class="module-card-body">
        <ul>
            <li>在安全环境中生成密钥，确保随机性和强度</li>
            <li>密钥存储在安全单元中，如手机的TEE/SE，车辆的HSM</li>
            <li>密钥材料不以明文形式存在于内存或存储中</li>
            <li>密钥运算在安全环境中执行，防止运算过程中的数据泄露</li>
        </ul>
    </div>
</div>

<div class="module-card">
    <div class="module-card-header">分层密钥架构</div>
    <div class="module-card-body">
        <ul>
            <li><strong>根密钥</strong>：最高级别密钥，用于保护中间密钥</li>
            <li><strong>中间密钥</strong>：用于保护会话密钥，定期更新</li>
            <li><strong>会话密钥</strong>：用于实际的加密通信，频繁更新</li>
        </ul>
        <p>这种分层结构确保即使底层密钥泄露，也不会影响上层密钥的安全性。</p>
    </div>
</div>

<div class="module-card">
    <div class="module-card-header">密钥更新与撤销机制</div>
    <div class="module-card-body">
        <ul>
            <li>会话密钥定期更新，每次会话或固定时间后自动更新</li>
            <li>中间密钥根据安全策略定期更换</li>
            <li>支持密钥紧急撤销，应对安全事件</li>
            <li>密钥撤销后，所有相关的会话密钥也会失效</li>
        </ul>
    </div>
</div>

<h3>3.4 安全机制与防护</h3>

<div class="module-card">
    <div class="module-card-header">防重放攻击</div>
    <div class="module-card-body">
        <ul>
            <li>所有安全消息包含时间戳，超过有效期的消息会被拒绝</li>
            <li>使用随机数(nonce)确保每次通信的唯一性</li>
            <li>通信序列号机制，保证消息顺序，拒绝过期或乱序消息</li>
        </ul>
    </div>
</div>

<div class="module-card">
    <div class="module-card-header">防中继攻击</div>
    <div class="module-card-body">
        <p>中继攻击是指攻击者在远处截获正常用户的信号并转发到车辆，欺骗车辆认为用户在附近。</p>
        <ul>
            <li>采用距离测量技术，分析RSSI信号强度判断手机与车辆实际距离</li>
            <li>多节点定位技术，通过车辆多个接收点计算距离，提高精确度</li>
            <li>时间同步与响应时间监测，检测异常的通信延迟</li>
            <li>环境感知，结合车辆周围环境因素进行综合判断</li>
        </ul>
    </div>
</div>

<div class="security-box">
    <strong>蓝牙安全增强措施：</strong>
    <ul>
        <li>使用蓝牙4.2及以上版本，支持LE Privacy功能，防止设备被追踪</li>
        <li>蓝牙广播数据加密，防止第三方解析</li>
        <li>连接建立后使用强加密算法保护通信内容</li>
        <li>设备绑定后使用长期密钥验证身份，防止欺骗</li>
        <li>定期更换蓝牙广播参数，增加追踪难度</li>
    </ul>
</div>

<h3>3.5 异常检测与响应</h3>

<div class="module-card">
    <div class="module-card-body">
        <ul>
            <li>监控系统使用情况，检测异常行为模式</li>
            <li>记录安全日志，支持事后审计和分析</li>
            <li>异常行为触发安全警报，进行实时响应</li>
            <li>根据异常严重程度执行不同级别的安全响应，如警告、限制功能、撤销权限等</li>
            <li>安全事件上报至云平台，进行集中分析和处理</li>
        </ul>
    </div>
</div>

<h2>四、时间同步机制</h2>

<div class="note">
    时间同步机制确保系统各组件的时间一致性，是防重放攻击和安全验证的基础。
</div>

<div class="module-card">
    <div class="module-card-header">NTP同步</div>
    <div class="module-card-body">
        <ul>
            <li>云平台提供标准NTP服务，作为整个系统的时间基准</li>
            <li>手机端与云平台NTP服务器定期同步时间</li>
            <li>车端通过4G/5G网络或手机中转与云平台同步时间</li>
            <li>多级备份NTP服务器，确保时间服务可靠性</li>
        </ul>
    </div>
</div>

<div class="module-card">
    <div class="module-card-header">时间戳验证</div>
    <div class="module-card-body">
        <ul>
            <li>所有安全操作包含时间戳，确保操作的时效性</li>
            <li>时间戳与签名结合，防止时间戳被篡改</li>
            <li>设置合理的时间容错范围，通常为±30秒</li>
            <li>超出容错范围的操作会被拒绝，防止重放攻击</li>
        </ul>
    </div>
</div>

<div class="module-card">
    <div class="module-card-header">离线处理</div>
    <div class="module-card-body">
        <ul>
            <li>在网络不可用时，使用本地时钟作为备份</li>
            <li>记录最后同步时间和本地时钟偏差</li>
            <li>考虑本地时钟漂移因素，设置动态容错范围</li>
            <li>恢复网络后立即同步时间，并调整本地时钟</li>
        </ul>
    </div>
</div>

<h2>五、与外部平台的交互</h2>

<div class="highlight">
    数字钥匙系统需要与多个外部平台进行交互，主要包括TSP平台和OEM平台。
</div>

<h3>5.1 TSP平台交互</h3>

<div class="module-card">
    <div class="module-card-body">
        <p>TSP平台（车联网服务提供商平台）是车联网服务提供商的核心系统，负责车辆远程监控、远程控制、用户管理等功能。</p>
        <ul>
            <li><strong>车辆信息查询</strong>：查询车辆基本信息、状态信息、位置信息等</li>
            <li><strong>远程控制指令下发</strong>：下发远程控制指令（如远程开锁、远程启动等）</li>
            <li><strong>用户授权验证</strong>：验证用户对车辆的控制权限</li>
            <li><strong>事件通知</strong>：接收车辆事件通知（如车门状态变化、车辆启动等）</li>
            <li><strong>用户管理</strong>：对接TSP平台的用户系统，实现无缝用户体验</li>
        </ul>
    </div>
</div>

<h3>5.2 OEM平台交互</h3>

<div class="module-card">
    <div class="module-card-body">
        <p>OEM平台（汽车制造商平台）是汽车制造商的核心系统，负责车辆生产管理、配置管理、售后服务等功能。</p>
        <ul>
            <li><strong>车辆生产信息查询</strong>：查询车辆生产信息、配置信息等</li>
            <li><strong>车辆诊断</strong>：获取车辆诊断信息、故障码等</li>
            <li><strong>售后服务</strong>：获取车辆保修信息、维修记录等</li>
            <li><strong>车型支持确认</strong>：确认特定车型是否支持数字钥匙功能</li>
        </ul>
    </div>
</div>

<h2>六、扩展能力与后续计划</h2>

<div class="warning">
    <strong>待开发功能：</strong>以下是计划开发的功能和增强。
</div>

<div class="module-card">
    <div class="module-card-header">故障应急处理机制</div>
    <div class="module-card-body">
        <ul>
            <li>手机丢失处理 - 远程撤销数字钥匙，防止未授权使用</li>
            <li>手机更换处理 - 安全地将数字钥匙转移到新手机</li>
            <li>电池耗尽应急方案 - 特殊的低功耗蓝牙开锁模式</li>
            <li>系统故障降级措施 - 确保关键功能在异常情况下仍可用</li>
        </ul>
    </div>
</div>

<div class="module-card">
    <div class="module-card-header">平台兼容方案</div>
    <div class="module-card-body">
        <ul>
            <li>提高系统与不同车型和平台的兼容性</li>
            <li>支持主流汽车厂商的专有协议</li>
            <li>适配不同型号手机和操作系统</li>
            <li>兼容多种蓝牙版本，确保向后兼容性</li>
        </ul>
    </div>
</div>

<div class="module-card">
    <div class="module-card-header">钥匙分享方案</div>
    <div class="module-card-body">
        <ul>
            <li>一键分享 - 主用户可将临时钥匙分享给其他用户</li>
            <li>权限控制 - 支持分级权限设置，如仅解锁、完全控制等</li>
            <li>时间限制 - 设置分享钥匙的有效期</li>
            <li>使用限制 - 设置使用次数、区域限制等</li>
            <li>紧急撤销 - 主用户可随时撤销分享的钥匙</li>
        </ul>
    </div>
</div>

<h2>七、注意事项</h2>

<div class="warning">
    <ol>
        <li>本系统是一个完整的集成产品，包含手机APP对应的SDK、云平台SDK以及完整的车端软硬件开发。</li>
        <li>在每一块设计时，都需要考虑到与其它模块的关联，以及如何集成到一起。</li>
        <li>系统设计需要考虑安全性、可靠性、可扩展性等多方面因素。</li>
        <li>实际部署时需根据具体车型和用户需求进行定制化配置。</li>
        <li>系统应具备故障应急处理机制，确保在异常情况下仍能保障基本功能。</li>
        <li>所有密钥操作必须在安全环境中执行，确保密钥安全。</li>
    </ol>
</div>

<h2>八、总结</h2>

<p>数字钥匙系统通过综合运用蓝牙通信、先进加密技术、多层次安全验证和可靠时间同步机制，为用户提供安全、便捷的车辆接入和控制体验。系统架构采用手机端、车端、云平台三层结构，各层之间通过安全通道通信，确保数据传输的机密性、完整性和真实性。</p>

<p>安全设计贯穿整个系统，从通信安全、数据安全到密钥管理和异常处理，形成完整的安全防护体系。特别是在防重放攻击和防中继攻击方面，系统采用了多种先进技术，如时间戳验证、距离测量、多节点定位等，确保只有合法用户在合法距离内才能操作车辆。</p>

<p>系统还具备良好的可扩展性和兼容性，能够适应不同车型、不同手机平台，并与TSP平台、OEM平台等外部系统进行集成，为用户提供统一、连贯的使用体验。</p>

<div class="note">
    <p>后续开发将重点关注故障应急处理、平台兼容性提升、钥匙分享功能等方面，进一步提升系统的实用性和用户体验。</p>
</div>

<!-- 新增数据流和交互关系模块 -->
<div class="ppt-slide">
    <div class="slide-header">
        <h2>系统数据流与交互关系</h2>
    </div>
    <div class="slide-content">
        <div class="data-flow-container">
            <!-- 左侧：手机端 -->
            <div class="flow-endpoint" id="mobile-endpoint">
                <div class="endpoint-icon">📱</div>
                <div class="endpoint-title">手机端</div>
                <div class="endpoint-features">
                    <div class="endpoint-feature">用户交互</div>
                    <div class="endpoint-feature">钥匙管理</div>
                    <div class="endpoint-feature">安全存储</div>
                </div>
            </div>
            
            <!-- 中间：数据流向 -->
            <div class="flow-arrows">
                <div class="flow-group">
                    <div class="flow-label">钥匙申请</div>
                    <div class="flow-arrow right-arrow">
                        <div class="arrow-line"></div>
                        <div class="arrow-head"></div>
                    </div>
                </div>
                
                <div class="flow-group">
                    <div class="flow-label">钥匙下发</div>
                    <div class="flow-arrow left-arrow">
                        <div class="arrow-line"></div>
                        <div class="arrow-head"></div>
                    </div>
                </div>
                
                <div class="flow-group">
                    <div class="flow-label">控制指令</div>
                    <div class="flow-arrow diagonal-arrow-down">
                        <div class="arrow-line"></div>
                        <div class="arrow-head"></div>
                    </div>
                </div>
                
                <div class="flow-group">
                    <div class="flow-label">状态反馈</div>
                    <div class="flow-arrow diagonal-arrow-up">
                        <div class="arrow-line"></div>
                        <div class="arrow-head"></div>
                    </div>
                </div>
            </div>
            
            <!-- 右侧：云平台 -->
            <div class="flow-endpoint" id="cloud-endpoint">
                <div class="endpoint-icon">☁️</div>
                <div class="endpoint-title">钥匙云平台</div>
                <div class="endpoint-features">
                    <div class="endpoint-feature">钥匙生成</div>
                    <div class="endpoint-feature">安全认证</div>
                    <div class="endpoint-feature">权限管理</div>
                </div>
            </div>
            
            <!-- 底部：车端 -->
            <div class="flow-endpoint" id="car-endpoint">
                <div class="endpoint-icon">🚗</div>
                <div class="endpoint-title">车端</div>
                <div class="endpoint-features">
                    <div class="endpoint-feature">钥匙验证</div>
                    <div class="endpoint-feature">指令执行</div>
                    <div class="endpoint-feature">状态上报</div>
                </div>
            </div>
        </div>
        
        <div class="data-flow-description">
            <h3>主要数据流程</h3>
            <ol>
                <li><strong>钥匙申请与下发</strong>：手机端向云平台申请数字钥匙，云平台生成并下发钥匙</li>
                <li><strong>控制指令发送</strong>：手机端通过蓝牙或云平台向车端发送控制指令</li>
                <li><strong>状态反馈</strong>：车端执行指令后向手机端和云平台反馈执行结果</li>
                <li><strong>安全认证</strong>：全过程中各组件间进行双向安全认证，确保通信安全</li>
            </ol>
        </div>
    </div>
</div>

<h2>一、系统架构概述</h2>

<p>数字钥匙系统由以下几个主要部分组成：</p>

<div class="architecture-container">
    <div class="architecture-module">
        <div class="module-header">
            <span class="module-icon">📱</span>
            <span>手机端</span>
        </div>
        <div class="module-body">
            <ul class="module-list">
                <li class="module-list-item">
                    <span class="module-list-icon">🔑</span>
                    <strong>钥匙管理模块</strong>：管理用户的数字钥匙，包括添加、删除、共享等功能
                </li>
                <li class="module-list-item">
                    <span class="module-list-icon">🚗</span>
                    <strong>车辆控制模块</strong>：提供车辆控制功能，如开锁、关锁、启动等
                </li>
                <li class="module-list-item">
                    <span class="module-list-icon">🎧</span>
                    <strong>蓝牙通信模块</strong>：负责与车端进行蓝牙通信
                </li>
                <li class="module-list-item">
                    <span class="module-list-icon">🔒</span>
                    <strong>安全存储模块</strong>：安全存储数字钥匙和相关密钥材料
                </li>
                <li class="module-list-item">
                    <span class="module-list-icon">⏰</span>
                    <strong>时间同步模块</strong>：与服务器进行时间同步，确保安全认证的时间准确性
                </li>
                <li class="module-list-item">
                    <span class="module-list-icon">🔒</span>
                    <strong>暗号交换模块</strong>：负责与车端协商生成临时暗号，确保通信安全
                </li>
                <li class="module-list-item">
                    <span class="module-list-icon">🤖</span>
                    <strong>智能场景管理模块</strong>：负责检测用户状态与环境，优化电量与连接策略
                </li>
            </ul>
        </div>
    </div>

    <div class="architecture-module">
        <div class="module-header">
            <span class="module-icon">🚗</span>
            <span>车端</span>
        </div>
        <div class="module-body">
            <ul class="module-list">
                <li class="module-list-item">
                    <span class="module-list-icon">🎧</span>
                    <strong>蓝牙通信模块</strong>：负责与手机端进行蓝牙通信
                </li>
                <li class="module-list-item">
                    <span class="module-list-icon">🔒</span>
                    <strong>钥匙验证模块</strong>：验证数字钥匙的有效性和权限
                </li>
                <li class="module-list-item">
                    <span class="module-list-icon">🚗</span>
                    <strong>指令执行模块</strong>：执行控制指令，如开锁、关锁、启动等
                </li>
                <li class="module-list-item">
                    <span class="module-list-icon">🔒</span>
                    <strong>安全存储模块</strong>：安全存储密钥材料和相关配置
                </li>
                <li class="module-list-item">
                    <span class="module-list-icon">🌐</span>
                    <strong>远程通信模块(T-Box)</strong>：与TSP平台进行通信，接收远程控制指令
                </li>
                <li class="module-list-item">
                    <span class="module-list-icon">⏰</span>
                    <strong>时间同步模块</strong>：与服务器进行时间同步，确保安全认证准确
                </li>
                <li class="module-list-item">
                    <span class="module-list-icon">🔒</span>
                    <strong>暗号交换模块</strong>：与手机端协商生成临时暗号，确保通信安全
                </li>
                <li class="module-list-item">
                    <span class="module-list-icon">🤖</span>
                    <strong>用户行为分析模块</strong>：实现无感控车的核心算法，处理距离计算
                </li>
            </ul>
        </div>
    </div>

    <div class="architecture-module">
        <div class="module-header">
            <span class="module-icon">🔒</span>
            <span>钥匙云平台</span>
        </div>
        <div class="module-body">
            <ul class="module-list">
                <li class="module-list-item">
                    <span class="module-list-icon">🔑</span>
                    <strong>钥匙生命周期管理</strong>：管理数字钥匙的创建、更新、撤销等
                </li>
                <li class="module-list-item">
                    <span class="module-list-icon">🚗</span>
                    <strong>车辆关联服务</strong>：将VIN码与车辆信息关联
                </li>
                <li class="module-list-item">
                    <span class="module-list-icon">🔒</span>
                    <strong>安全认证中心</strong>：提供安全认证服务
                </li>
                <li class="module-list-item">
                    <span class="module-list-icon">🔒</span>
                    <strong>密钥管理系统</strong>：管理系统中的各类密钥
                </li>
                <li class="module-list-item">
                    <span class="module-list-icon">⏰</span>
                    <strong>时间服务器</strong>：提供标准时间服务
                </li>
                <li class="module-list-item">
                    <span class="module-list-icon">🌐</span>
                    <strong>统一接口服务</strong>：提供对外接口服务
                </li>
                <li class="module-list-item">
                    <span class="module-list-icon">🔒</span>
                    <strong>安全通信通道</strong>：提供加密通信，防止消息被窃听或篡改
                </li>
                <li class="module-list-item">
                    <span class="module-list-icon">🤖</span>
                    <strong>异常监控与处理</strong>：监控异常登录，分析异常使用模式
                </li>
            </ul>
        </div>
    </div>

    <div class="architecture-module">
        <div class="module-header">
            <span class="module-icon">🌐</span>
            <span>外部平台</span>
        </div>
        <div class="module-body">
            <ul class="module-list">
                <li class="module-list-item">
                    <span class="module-list-icon">🌐</span>
                    <strong>TSP平台</strong>：车联网服务提供商平台，提供车辆远程监控、远程控制等
                </li>
                <li class="module-list-item">
                    <span class="module-list-icon">🚗</span>
                    <strong>OEM平台</strong>：汽车制造商平台，提供车辆生产信息、配置信息等
                </li>
            </ul>
        </div>
    </div>
</div>

<div class="architecture-connections-container">
    <h3>系统架构连接关系</h3>
    <div class="architecture-diagram">
        <div class="diagram-container">
            <!-- 手机端 -->
            <div class="diagram-module" id="mobile-module">
                <div class="diagram-header">手机端</div>
                <div class="diagram-body">
                    <div class="diagram-icon">📱</div>
                </div>
            </div>
            
            <!-- 车端 -->
            <div class="diagram-module" id="car-module">
                <div class="diagram-header">车端</div>
                <div class="diagram-body">
                    <div class="diagram-icon">🚗</div>
                </div>
            </div>
            
            <!-- 云平台 -->
            <div class="diagram-module" id="cloud-module">
                <div class="diagram-header">钥匙云平台</div>
                <div class="diagram-body">
                    <div class="diagram-icon">☁️</div>
                </div>
            </div>
            
            <!-- 外部平台 -->
            <div class="diagram-module" id="external-module">
                <div class="diagram-header">外部平台</div>
                <div class="diagram-body">
                    <div class="diagram-icon">🌐</div>
                </div>
            </div>
            
            <!-- 连接线 -->
            <svg class="connection-lines">
                <!-- 手机端到车端 -->
                <path d="M150,100 C150,150 350,150 350,100" stroke="#0066cc" stroke-width="3" fill="none" marker-end="url(#arrowhead)"></path>
                <text x="250" y="170" text-anchor="middle" fill="#0066cc">蓝牙通信</text>
                
                <!-- 手机端到云平台 -->
                <path d="M120,150 C120,250 120,250 120,300" stroke="#0066cc" stroke-width="3" fill="none" marker-end="url(#arrowhead)"></path>
                <text x="80" y="225" text-anchor="middle" fill="#0066cc">数据同步</text>
                
                <!-- 车端到云平台 -->
                <path d="M380,150 C380,250 380,250 380,300" stroke="#0066cc" stroke-width="3" fill="none" marker-end="url(#arrowhead)"></path>
                <text x="420" y="225" text-anchor="middle" fill="#0066cc">远程控制</text>
                
                <!-- 云平台到外部平台 -->
                <path d="M200,350 C300,350 300,350 400,350" stroke="#0066cc" stroke-width="3" fill="none" marker-end="url(#arrowhead)"></path>
                <text x="300" y="330" text-anchor="middle" fill="#0066cc">数据交换</text>
                
                <!-- 箭头标记定义 -->
                <defs>
                    <marker id="arrowhead" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
                        <polygon points="0 0, 10 3.5, 0 7" fill="#0066cc" />
                    </marker>
                </defs>
            </svg>
        </div>
    </div>
    <div class="diagram-legend">
        <div class="legend-item">
            <div class="legend-color" style="background-color: #0066cc;"></div>
            <div class="legend-text">数据流向</div>
        </div>
        <div class="legend-item">
            <div class="legend-icon">📱</div>
            <div class="legend-text">手机端组件</div>
        </div>
        <div class="legend-item">
            <div class="legend-icon">🚗</div>
            <div class="legend-text">车端组件</div>
        </div>
        <div class="legend-item">
            <div class="legend-icon">☁️</div>
            <div class="legend-text">云平台组件</div>
        </div>
        <div class="legend-item">
            <div class="legend-icon">🌐</div>
            <div class="legend-text">外部平台</div>
        </div>
    </div>
</div>

<h2>二、系统功能流程</h2>

<h3>2.1 首次蓝牙配对流程</h3>

<div class="note">
    首次蓝牙配对是用户首次使用数字钥匙与车辆建立连接的过程，是整个系统安全性的基础。
</div>

<div class="flow-container">
    <div class="flow-item">
        <span class="flow-item-number">1</span>
        <strong>用户在APP中选择添加数字钥匙</strong>
    </div>
    <div class="flow-item">
        <span class="flow-item-number">2</span>
        <strong>扫描车辆二维码或输入VIN码识别车辆</strong>
    </div>
    <div class="flow-item">
        <span class="flow-item-number">3</span>
        <strong>云平台生成虚拟密钥和配对令牌</strong><br>
        这些关键信息会安全地传输到手机中，它们就像一把特殊的钥匙，只有持有这把钥匙的手机才能与对应的车辆建立连接。
    </div>
    <div class="flow-item">
        <span class="flow-item-number">4</span>
        <strong>云平台同时向车辆发送根密钥和授权手机信息</strong><br>
        根密钥是车辆安全验证的基础，授权手机信息让车辆知道哪些手机有权限连接。
    </div>
    <div class="flow-item">
        <span class="flow-item-number">5</span>
        <strong>手机与车辆建立蓝牙连接</strong><br>
        手机通过蓝牙搜索并连接到车辆。
    </div>
    <div class="flow-item">
        <span class="flow-item-number">6</span>
        <strong>双方进行身份验证和安全通道建立</strong><br>
        手机和车辆互相验证身份，确保双方都是授权的设备，并建立加密的通信通道。
    </div>
    <div class="flow-item">
        <span class="flow-item-number">7</span>
        <strong>存储配对信息到安全区域</strong><br>
        手机将配对信息存储在手机的安全区域（如TEE、SE），车辆将配对信息存储在车载安全单元中。
    </div>
    <div class="flow-item">
        <span class="flow-item-number">8</span>
        <strong>配对结果上报至云平台</strong><br>
        手机和车辆都向云平台报告配对结果，云平台记录配对关系。
    </div>
</div>

<div class="security-box">
    <strong>配对安全保障：</strong>
    <ul>
        <li>所有通信均采用TLS 1.3加密</li>
        <li>密钥生成使用非对称加密技术</li>
        <li>敏感信息存储在特殊安全区域</li>
        <li>配对过程使用挑战-响应机制防止重放攻击</li>
        <li>通过蓝牙信号强度(RSSI)验证距离，防止中继攻击</li>
    </ul>
</div>

<h3>2.2 无感控车流程</h3>

<div class="note">
    无感控车是手机在不需要用户主动操作的情况下，自动与车辆建立蓝牙连接并执行预设操作的功能。例如，用户走近车辆时自动开锁，走远时自动上锁。
</div>

<div class="flow-container">
    <div class="flow-item">
        <span class="flow-item-number">1</span>
        <strong>系统唤醒后台APP并检查连接条件</strong><br>
        系统会检查用户是否启用无感连接、手机电量是否充足、用户活动状态等条件。
    </div>
    <div class="flow-item">
        <span class="flow-item-number">2</span>
        <strong>手机自动发起蓝牙连接请求</strong><br>
        APP从安全区域读取连接信息，自动向车辆发起连接请求。
    </div>
    <div class="flow-item">
        <span class="flow-item-number">3</span>
        <strong>车端验证连接请求并接受连接</strong><br>
        车辆检查连接请求的合法性，包括验证设备ID和连接信息。
    </div>
    <div class="flow-item">
        <span class="flow-item-number">4</span>
        <strong>双方进行安全认证并建立安全通道</strong><br>
        手机和车辆通过挑战-响应机制相互验证身份，建立加密通信通道。
    </div>
    <div class="flow-item">
        <span class="flow-item-number">5</span>
        <strong>车端计算与手机的距离并同步状态</strong><br>
        车辆通过蓝牙信号强度(RSSI)计算与手机的距离，并向手机发送车辆当前状态。
    </div>
    <div class="flow-item">
        <span class="flow-item-number">6</span>
        <strong>根据距离和预设策略执行自动控车操作</strong><br>
        系统根据计算出的距离和用户预设的策略，自动执行相应的控车操作，如接近时解锁，离开时锁车。
    </div>
    <div class="flow-item">
        <span class="flow-item-number">7</span>
        <strong>持续监控距离变化，在用户离开时执行安全措施</strong><br>
        系统持续监控手机与车辆的距离，当用户离开时执行安全措施，如自动锁车。
    </div>
</div>

<div class="highlight">
    <strong>无感控车优化策略：</strong>
    <ul>
        <li><strong>系统级唤醒</strong> - 利用操作系统提供的后台唤醒机制</li>
        <li><strong>上下文感知扫描</strong> - 根据位置、时间、活动状态调整扫描频率</li>
        <li><strong>电量自适应</strong> - 根据手机电量调整扫描策略</li>
        <li><strong>学习优化</strong> - 记录用户习惯，优化扫描策略</li>
        <li><strong>多因素触发</strong> - 综合多种因素决定是否发起连接</li>
        <li><strong>防误触机制</strong> - 避免意外连接</li>
    </ul>
</div>

<h2>三、安全设计</h2>

<h3>3.1 通信安全</h3>

<div class="module-card">
    <div class="module-card-header">通信加密</div>
    <div class="module-card-body">
        <ul>
            <li>所有网络通信均采用TLS 1.3加密，支持双向TLS认证</li>
            <li>蓝牙通信支持蓝牙4.2及以上版本，使用加密通道</li>
            <li>关键数据传输时使用端到端加密，即使通道被攻破也无法读取</li>
        </ul>
    </div>
</div>

<div class="module-card">
    <div class="module-card-header">认证与授权</div>
    <div class="module-card-body">
        <ul>
            <li>基于PKI的证书认证 - 确保通信双方身份</li>
            <li>API Key + 签名认证 - 确保API调用安全</li>
            <li>OAuth 2.0认证 - 提供标准的授权框架</li>
            <li>设备身份认证 - 确保只有合法设备能接入系统</li>
            <li>权限分级管理 - 不同操作需要不同级别的安全验证</li>
        </ul>
    </div>
</div>

<h3>3.2 数据安全</h3>

<div class="module-card">
    <div class="module-card-body">
        <ul>
            <li>敏感数据传输和存储时进行加密，保护数据的机密性</li>
            <li>使用数据签名验证确保数据完整性，防止被篡改</li>
            <li>使用哈希函数等技术保障数据完整性</li>
            <li>对敏感个人信息进行脱敏处理，保护用户隐私</li>
            <li>定期数据备份和灾难恢复机制，确保数据安全</li>
        </ul>
    </div>
</div>

<h3>3.3 密钥管理</h3>

<div class="highlight">
    密钥管理是整个安全体系的核心，采用分层密钥架构，确保即使部分密钥泄露也不会危及整个系统。
</div>

<div class="module-card">
    <div class="module-card-header">密钥生成与存储</div>
    <div class="module-card-body">
        <ul>
            <li>在安全环境中生成密钥，确保随机性和强度</li>
            <li>密钥存储在安全单元中，如手机的TEE/SE，车辆的HSM</li>
            <li>密钥材料不以明文形式存在于内存或存储中</li>
            <li>密钥运算在安全环境中执行，防止运算过程中的数据泄露</li>
        </ul>
    </div>
</div>

<div class="module-card">
    <div class="module-card-header">分层密钥架构</div>
    <div class="module-card-body">
        <ul>
            <li><strong>根密钥</strong>：最高级别密钥，用于保护中间密钥</li>
            <li><strong>中间密钥</strong>：用于保护会话密钥，定期更新</li>
            <li><strong>会话密钥</strong>：用于实际的加密通信，频繁更新</li>
        </ul>
        <p>这种分层结构确保即使底层密钥泄露，也不会影响上层密钥的安全性。</p>
    </div>
</div>

<div class="module-card">
    <div class="module-card-header">密钥更新与撤销机制</div>
    <div class="module-card-body">
        <ul>
            <li>会话密钥定期更新，每次会话或固定时间后自动更新</li>
            <li>中间密钥根据安全策略定期更换</li>
            <li>支持密钥紧急撤销，应对安全事件</li>
            <li>密钥撤销后，所有相关的会话密钥也会失效</li>
        </ul>
    </div>
</div>

<h3>3.4 安全机制与防护</h3>

<div class="module-card">
    <div class="module-card-header">防重放攻击</div>
    <div class="module-card-body">
        <ul>
            <li>所有安全消息包含时间戳，超过有效期的消息会被拒绝</li>
            <li>使用随机数(nonce)确保每次通信的唯一性</li>
            <li>通信序列号机制，保证消息顺序，拒绝过期或乱序消息</li>
        </ul>
    </div>
</div>

<div class="module-card">
    <div class="module-card-header">防中继攻击</div>
    <div class="module-card-body">
        <p>中继攻击是指攻击者在远处截获正常用户的信号并转发到车辆，欺骗车辆认为用户在附近。</p>
        <ul>
            <li>采用距离测量技术，分析RSSI信号强度判断手机与车辆实际距离</li>
            <li>多节点定位技术，通过车辆多个接收点计算距离，提高精确度</li>
            <li>时间同步与响应时间监测，检测异常的通信延迟</li>
            <li>环境感知，结合车辆周围环境因素进行综合判断</li>
        </ul>
    </div>
</div>

<div class="security-box">
    <strong>蓝牙安全增强措施：</strong>
    <ul>
        <li>使用蓝牙4.2及以上版本，支持LE Privacy功能，防止设备被追踪</li>
        <li>蓝牙广播数据加密，防止第三方解析</li>
        <li>连接建立后使用强加密算法保护通信内容</li>
        <li>设备绑定后使用长期密钥验证身份，防止欺骗</li>
        <li>定期更换蓝牙广播参数，增加追踪难度</li>
    </ul>
</div>

<h3>3.5 异常检测与响应</h3>

<div class="module-card">
    <div class="module-card-body">
        <ul>
            <li>监控系统使用情况，检测异常行为模式</li>
            <li>记录安全日志，支持事后审计和分析</li>
            <li>异常行为触发安全警报，进行实时响应</li>
            <li>根据异常严重程度执行不同级别的安全响应，如警告、限制功能、撤销权限等</li>
            <li>安全事件上报至云平台，进行集中分析和处理</li>
        </ul>
    </div>
</div>

<h2>四、时间同步机制</h2>

<div class="note">
    时间同步机制确保系统各组件的时间一致性，是防重放攻击和安全验证的基础。
</div>

<div class="module-card">
    <div class="module-card-header">NTP同步</div>
    <div class="module-card-body">
        <ul>
            <li>云平台提供标准NTP服务，作为整个系统的时间基准</li>
            <li>手机端与云平台NTP服务器定期同步时间</li>
            <li>车端通过4G/5G网络或手机中转与云平台同步时间</li>
            <li>多级备份NTP服务器，确保时间服务可靠性</li>
        </ul>
    </div>
</div>

<div class="module-card">
    <div class="module-card-header">时间戳验证</div>
    <div class="module-card-body">
        <ul>
            <li>所有安全操作包含时间戳，确保操作的时效性</li>
            <li>时间戳与签名结合，防止时间戳被篡改</li>
            <li>设置合理的时间容错范围，通常为±30秒</li>
            <li>超出容错范围的操作会被拒绝，防止重放攻击</li>
        </ul>
    </div>
</div>

<div class="module-card">
    <div class="module-card-header">离线处理</div>
    <div class="module-card-body">
        <ul>
            <li>在网络不可用时，使用本地时钟作为备份</li>
            <li>记录最后同步时间和本地时钟偏差</li>
            <li>考虑本地时钟漂移因素，设置动态容错范围</li>
            <li>恢复网络后立即同步时间，并调整本地时钟</li>
        </ul>
    </div>
</div>

<h2>五、与外部平台的交互</h2>

<div class="highlight">
    数字钥匙系统需要与多个外部平台进行交互，主要包括TSP平台和OEM平台。
</div>

<h3>5.1 TSP平台交互</h3>

<div class="module-card">
    <div class="module-card-body">
        <p>TSP平台（车联网服务提供商平台）是车联网服务提供商的核心系统，负责车辆远程监控、远程控制、用户管理等功能。</p>
        <ul>
            <li><strong>车辆信息查询</strong>：查询车辆基本信息、状态信息、位置信息等</li>
            <li><strong>远程控制指令下发</strong>：下发远程控制指令（如远程开锁、远程启动等）</li>
            <li><strong>用户授权验证</strong>：验证用户对车辆的控制权限</li>
            <li><strong>事件通知</strong>：接收车辆事件通知（如车门状态变化、车辆启动等）</li>
            <li><strong>用户管理</strong>：对接TSP平台的用户系统，实现无缝用户体验</li>
        </ul>
    </div>
</div>

<h3>5.2 OEM平台交互</h3>

<div class="module-card">
    <div class="module-card-body">
        <p>OEM平台（汽车制造商平台）是汽车制造商的核心系统，负责车辆生产管理、配置管理、售后服务等功能。</p>
        <ul>
            <li><strong>车辆生产信息查询</strong>：查询车辆生产信息、配置信息等</li>
            <li><strong>车辆诊断</strong>：获取车辆诊断信息、故障码等</li>
            <li><strong>售后服务</strong>：获取车辆保修信息、维修记录等</li>
            <li><strong>车型支持确认</strong>：确认特定车型是否支持数字钥匙功能</li>
        </ul>
    </div>
</div>

<h2>六、扩展能力与后续计划</h2>

<div class="warning">
    <strong>待开发功能：</strong>以下是计划开发的功能和增强。
</div>

<div class="module-card">
    <div class="module-card-header">故障应急处理机制</div>
    <div class="module-card-body">
        <ul>
            <li>手机丢失处理 - 远程撤销数字钥匙，防止未授权使用</li>
            <li>手机更换处理 - 安全地将数字钥匙转移到新手机</li>
            <li>电池耗尽应急方案 - 特殊的低功耗蓝牙开锁模式</li>
            <li>系统故障降级措施 - 确保关键功能在异常情况下仍可用</li>
        </ul>
    </div>
</div>

<div class="module-card">
    <div class="module-card-header">平台兼容方案</div>
    <div class="module-card-body">
        <ul>
            <li>提高系统与不同车型和平台的兼容性</li>
            <li>支持主流汽车厂商的专有协议</li>
            <li>适配不同型号手机和操作系统</li>
            <li>兼容多种蓝牙版本，确保向后兼容性</li>
        </ul>
    </div>
</div>

<div class="module-card">
    <div class="module-card-header">钥匙分享方案</div>
    <div class="module-card-body">
        <ul>
            <li>一键分享 - 主用户可将临时钥匙分享给其他用户</li>
            <li>权限控制 - 支持分级权限设置，如仅解锁、完全控制等</li>
            <li>时间限制 - 设置分享钥匙的有效期</li>
            <li>使用限制 - 设置使用次数、区域限制等</li>
            <li>紧急撤销 - 主用户可随时撤销分享的钥匙</li>
        </ul>
    </div>
</div>

<h2>七、注意事项</h2>

<div class="warning">
    <ol>
        <li>本系统是一个完整的集成产品，包含手机APP对应的SDK、云平台SDK以及完整的车端软硬件开发。</li>
        <li>在每一块设计时，都需要考虑到与其它模块的关联，以及如何集成到一起。</li>
        <li>系统设计需要考虑安全性、可靠性、可扩展性等多方面因素。</li>
        <li>实际部署时需根据具体车型和用户需求进行定制化配置。</li>
        <li>系统应具备故障应急处理机制，确保在异常情况下仍能保障基本功能。</li>
        <li>所有密钥操作必须在安全环境中执行，确保密钥安全。</li>
    </ol>
</div>

<h2>八、总结</h2>

<p>数字钥匙系统通过综合运用蓝牙通信、先进加密技术、多层次安全验证和可靠时间同步机制，为用户提供安全、便捷的车辆接入和控制体验。系统架构采用手机端、车端、云平台三层结构，各层之间通过安全通道通信，确保数据传输的机密性、完整性和真实性。</p>

<p>安全设计贯穿整个系统，从通信安全、数据安全到密钥管理和异常处理，形成完整的安全防护体系。特别是在防重放攻击和防中继攻击方面，系统采用了多种先进技术，如时间戳验证、距离测量、多节点定位等，确保只有合法用户在合法距离内才能操作车辆。</p>

<p>系统还具备良好的可扩展性和兼容性，能够适应不同车型、不同手机平台，并与TSP平台、OEM平台等外部系统进行集成，为用户提供统一、连贯的使用体验。</p>

<div class="note">
    <p>后续开发将重点关注故障应急处理、平台兼容性提升、钥匙分享功能等方面，进一步提升系统的实用性和用户体验。</p>
</div>

</body>
<script>
    // 折叠面板功能
    document.addEventListener('DOMContentLoaded', function() {
        var coll = document.getElementsByClassName("collapsible");
        for (var i = 0; i < coll.length; i++) {
            coll[i].addEventListener("click", function() {
                this.classList.toggle("active");
                var content = this.nextElementSibling;
                if (content.style.maxHeight) {
                    content.style.maxHeight = null;
                } else {
                    content.style.maxHeight = content.scrollHeight + "px";
                }
            });
        }
        
        // 添加模块卡片的悬停效果
        var modules = document.getElementsByClassName("architecture-module");
        for (var i = 0; i < modules.length; i++) {
            modules[i].addEventListener("mouseover", function() {
                this.style.transform = "translateY(-5px)";
                this.style.boxShadow = "0 8px 25px rgba(0,0,0,0.15)";
            });
            modules[i].addEventListener("mouseout", function() {
                this.style.transform = "";
                this.style.boxShadow = "";
            });
        }
        
        // 自动展开第一个折叠面板（如果存在）
        if (coll.length > 0) {
            coll[0].click();
        }
        
        // 架构图模块交互
        var archModules = document.querySelectorAll('.arch-module');
        archModules.forEach(function(module) {
            // 点击模块时添加高亮效果
            module.addEventListener('click', function() {
                // 移除其他模块的高亮
                archModules.forEach(function(m) {
                    if (m !== module && !m.classList.contains('highlight-module')) {
                        m.style.opacity = '0.6';
                    }
                });
                
                // 如果当前模块已经是半透明，恢复所有模块
                if (this.style.opacity === '0.6') {
                    archModules.forEach(function(m) {
                        m.style.opacity = '1';
                    });
                } else {
                    // 当前模块保持不变
                    this.style.opacity = '1';
                }
            });
        });
        
        // 确保架构图正确显示
        function adjustArchitectureHeight() {
            var globalArch = document.querySelector('.global-architecture');
            if (globalArch) {
                // 确保高度足够显示所有层
                var lastLayer = document.querySelector('.car-layer');
                if (lastLayer) {
                    var lastLayerBottom = lastLayer.offsetTop + lastLayer.offsetHeight;
                    globalArch.style.height = (lastLayerBottom + 50) + 'px';
                }
            }
        }
        
        // 页面加载和窗口大小改变时调整高度
        window.addEventListener('load', adjustArchitectureHeight);
        window.addEventListener('resize', adjustArchitectureHeight);
    });
</script>
</html>

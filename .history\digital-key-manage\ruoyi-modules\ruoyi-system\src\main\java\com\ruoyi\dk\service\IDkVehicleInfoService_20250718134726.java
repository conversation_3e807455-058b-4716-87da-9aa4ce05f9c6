package com.ruoyi.dk.service;

import java.util.List;
import com.ruoyi.dk.domain.DkVehicleInfo;

/**
 * 车辆信息Service接口
 * 
 * <AUTHOR>
 * @date 2024-01-15
 */
public interface IDkVehicleInfoService {
    /**
     * 查询车辆信息
     * 
     * @param vehicleId 车辆信息主键
     * @return 车辆信息
     */
    public DkVehicleInfo selectDkVehicleInfoByVehicleId(Long vehicleId);

    /**
     * 查询车辆信息列表
     * 
     * @param dkVehicleInfo 车辆信息
     * @return 车辆信息集合
     */
    public List<DkVehicleInfo> selectDkVehicleInfoList(DkVehicleInfo dkVehicleInfo);

    /**
     * 新增车辆信息
     * 
     * @param dkVehicleInfo 车辆信息
     * @return 结果
     */
    public int insertDkVehicleInfo(DkVehicleInfo dkVehicleInfo);

    /**
     * 修改车辆信息
     * 
     * @param dkVehicleInfo 车辆信息
     * @return 结果
     */
    public int updateDkVehicleInfo(DkVehicleInfo dkVehicleInfo);

    /**
     * 批量删除车辆信息
     * 
     * @param vehicleIds 需要删除的车辆信息主键集合
     * @return 结果
     */
    public int deleteDkVehicleInfoByVehicleIds(Long[] vehicleIds);

    /**
     * 删除车辆信息信息
     * 
     * @param vehicleId 车辆信息主键
     * @return 结果
     */
    public int deleteDkVehicleInfoByVehicleId(Long vehicleId);

    /**
     * 根据VIN码查询车辆信息
     * 
     * @param vinCode VIN码
     * @return 车辆信息
     */
    public DkVehicleInfo selectDkVehicleInfoByVinCode(String vinCode);

    /**
     * 更新车辆状态
     * 
     * @param vehicleId 车辆ID
     * @param status    状态
     * @return 结果
     */
    public int updateVehicleStatus(Long vehicleId, String status);

    /**
     * 批量更新车辆状态
     * 
     * @param vehicleIds 车辆ID数组
     * @param status     状态
     * @return 结果
     */
    public int batchUpdateVehicleStatus(Long[] vehicleIds, String status);

    /**
     * 获取车辆统计信息
     * 
     * @return 统计信息
     */
    public java.util.Map<String, Object> getVehicleStatistics();

    /**
     * 获取可用车辆列表
     *
     * @return 可用车辆列表
     */
    public List<DkVehicleInfo> getAvailableVehicles();

    /**
     * 查询可用车辆列表
     *
     * @return 可用车辆列表
     */
    public List<DkVehicleInfo> selectAvailableVehicles();

    /**
     * 获取使用中车辆列表
     * 
     * @return 使用中车辆列表
     */
    public List<DkVehicleInfo> getInUseVehicles();

    /**
     * 获取维护中车辆列表
     * 
     * @return 维护中车辆列表
     */
    public List<DkVehicleInfo> getMaintenanceVehicles();

    /**
     * 根据品牌查询车辆列表
     * 
     * @param brand 品牌
     * @return 车辆列表
     */
    public List<DkVehicleInfo> selectDkVehicleInfoByBrand(String brand);

    /**
     * 根据型号查询车辆列表
     * 
     * @param model 型号
     * @return 车辆列表
     */
    public List<DkVehicleInfo> selectDkVehicleInfoByModel(String model);

    /**
     * 导入车辆数据
     * 
     * @param vehicleList     车辆数据列表
     * @param isUpdateSupport 是否更新支持，如果已存在，则进行更新数据
     * @param operName        操作用户
     * @return 结果
     */
    public String importVehicle(List<DkVehicleInfo> vehicleList, Boolean isUpdateSupport, String operName);
}

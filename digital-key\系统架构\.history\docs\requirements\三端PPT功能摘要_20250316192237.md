## 钥匙云平台核心架构

- **钥匙生命周期管理**：创建、授权、撤销数字钥匙，管理钥匙权限与有效期
  
- **车辆关联服务**：验证车辆信息，建立车辆与钥匙的安全绑定关系
  
- **安全认证中心**：确保用户身份可信，执行权限验证，防止未授权访问
  
- **密钥管理系统**：生成加密密钥，确保密钥安全存储与分发，支持密钥更新
  
- **统一接口服务**：提供标准化API，连接外部系统，确保接口安全
  
- **安全通信通道**：实现端到端加密通信，防止数据被窃听或篡改
  
- **异常监控与处理**：检测异常行为，执行安全响应，记录安全事件
  
- **时间服务器**：提供精确时间基准，支持NTP/PTP时间同步，防止重放攻击

## 手机端核心功能

- **钥匙管理模块**：查看、使用和管理个人数字钥匙，包括钥匙状态监控和权限查看
  
- **车辆控制模块**：发送控制指令，接收车辆状态反馈
  
- **安全存储模块**：在手机安全区域存储密钥、配对令牌和车辆连接信息
  
- **蓝牙通信模块**：扫描识别车辆广播，建立和维护与车辆的蓝牙连接
  
- **时间同步模块**：与云端时间服务器同步，提供可信时间戳，防止重放攻击
  
- **暗号交换模块**：与车端协商生成临时暗号，确保通信安全
  
- **智能场景管理模块**：根据位置、时间和活动状态调整扫描频率，优化电量使用
  
- **异常处理模块**：检测并处理连接异常、认证失败等问题，确保使用安全
  
- **标定模块**：用于校准RSSI信号与实际距离的对应关系，提高无感控车精度

## 车端核心功能

- **蓝牙通信模块**：发送加密广播信号，接收并处理连接请求
  
- **钥匙验证模块**：验证手机身份，确保只有授权设备能够连接
  
- **指令执行模块**：接收并执行来自手机的控制指令，反馈执行结果
  
- **安全存储模块**：在安全区域存储根密钥、会话密钥和授权设备信息
  
- **远程通信模块**：与云平台建立安全连接，同步状态和配置
  
- **时间同步模块**：与云端时间服务器同步，提供精确时间基准
  
- **暗号交换模块**：与手机端协商生成临时暗号，确保只有配对的双方能通信
  
- **用户行为分析模块**：基于RSSI信号强度计算与手机的距离，支持距离感知操作
  
- **异常处理模块**：处理异常连接请求，监控异常操作，执行安全措施
  
- **标定配置模块**：存储和管理RSSI信号标定数据，支持不同环境下的精确距离计算

## 钥匙分享功能

### 钥匙分享使用步骤

- **步骤一：发起分享**：车主在APP中选择"钥匙分享"功能，选择要分享的车辆
  
- **步骤二：设置权限**：设定接收方可使用的功能（如仅解锁/全部功能），使用时段和有效期限
  
- **步骤三：生成二维码**：系统根据设置的权限信息生成专属二维码，显示在车主手机屏幕上
  
- **步骤四：接收方扫码**：接收方使用手机扫描车主展示的二维码，系统自动跳转至APP或引导下载
  
- **步骤五：分享确认**：车主收到分享确认请求，确认后系统完成钥匙分享流程
  
- **步骤六：钥匙激活**：确认完成后，接收方的数字钥匙自动激活，可在授权范围内使用
  
- **步骤七：使用钥匙**：接收方在授权时段内使用APP控制车辆，权限范围内操作
  
- **步骤八：管理与撤销**：车主可随时在APP中查看分享状态，必要时调整权限或撤销钥匙





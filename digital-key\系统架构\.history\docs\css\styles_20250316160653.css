/* 调整整体背景和配色 */
body {
    /* 原来可能是深蓝色背景 */
    background-color: #f5f7fa;
    color: #2c3e50;
}

.header-container {
    /* 将标题区域改为渐变淡蓝色，保持科技感 */
    background: linear-gradient(135deg, #e0e8ff 0%, #c7d9ff 100%);
    padding: 3rem 0;
    border-bottom: 1px solid #eaeef5;
}

h1, h2, h3 {
    color: #3a539b;
    font-weight: 500;
}

/* 内容区域背景 */
.content-container {
    background-color: white;
    box-shadow: 0 0 15px rgba(0, 0, 0, 0.03);
    border-radius: 8px;
    margin: 2rem auto;
    padding: 2rem;
}

/* 模块卡片样式更新 */
.module-card {
    /* 原先可能有较重的阴影和边框 */
    background-color: rgba(255, 255, 255, 0.9);
    border-radius: 10px;
    padding: 1.5rem;
    margin: 1rem;
    transition: all 0.3s ease;
    box-shadow: 0 3px 10px rgba(0, 0, 0, 0.03);
    border: 1px solid #f0f3f9;
}

.module-card:hover {
    transform: translateY(-3px);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
}

.module-title {
    color: #2c3e50;
    margin: 1rem 0 0.5rem;
    font-weight: 500;
}

.module-desc {
    color: #7f8c8d;
    font-size: 0.95rem;
    line-height: 1.5;
} 
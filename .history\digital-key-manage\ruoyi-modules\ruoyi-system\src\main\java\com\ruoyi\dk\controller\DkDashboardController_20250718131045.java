package com.ruoyi.dk.controller;

import com.ruoyi.common.core.web.controller.BaseController;
import com.ruoyi.common.core.web.domain.AjaxResult;
import com.ruoyi.dk.service.IDkDashboardService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.HashMap;
import java.util.Map;
import java.util.List;
import java.util.ArrayList;

/**
 * 数字钥匙运营工作台Controller
 * 
 * <AUTHOR>
 * @date 2024-01-15
 */
@RestController
@RequestMapping("/dk/dashboard")
public class DkDashboardController extends BaseController {

    @Autowired
    private IDkDashboardService dkDashboardService;

    /**
     * 获取仪表板数据
     */
    @PreAuthorize("@ss.hasPermi('dk:dashboard:view')")
    @GetMapping("/data")
    public AjaxResult getDashboardData() {
        try {
            Map<String, Object> data = dkDashboardService.getDashboardData();
            return AjaxResult.success(data);
        } catch (Exception e) {
            // 如果服务还没实现，返回模拟数据
            Map<String, Object> mockData = new HashMap<>();
            mockData.put("pendingApplications", 8);
            mockData.put("todayAssignments", 15);
            mockData.put("availableVehicles", 32);
            mockData.put("restrictedUsers", 3);
            return AjaxResult.success(mockData);
        }
    }

    /**
     * 获取待处理事项列表
     */
    @PreAuthorize("@ss.hasPermi('dk:dashboard:view')")
    @GetMapping("/todoList")
    public AjaxResult getTodoList() {
        try {
            List<Map<String, Object>> todoList = dkDashboardService.getTodoList();
            return AjaxResult.success(todoList);
        } catch (Exception e) {
            // 返回模拟数据
            List<Map<String, Object>> mockTodoList = new ArrayList<>();
            
            Map<String, Object> todo1 = new HashMap<>();
            todo1.put("id", 1);
            todo1.put("title", "待审核申请");
            todo1.put("description", "有3个用户申请待审核");
            todo1.put("time", "10分钟前");
            todo1.put("type", "application");
            todo1.put("icon", "el-icon-document");
            todo1.put("color", "#409EFF");
            mockTodoList.add(todo1);
            
            Map<String, Object> todo2 = new HashMap<>();
            todo2.put("id", 2);
            todo2.put("title", "车辆维护提醒");
            todo2.put("description", "1辆车辆需要维护");
            todo2.put("time", "1小时前");
            todo2.put("type", "vehicle");
            todo2.put("icon", "el-icon-warning");
            todo2.put("color", "#E6A23C");
            mockTodoList.add(todo2);
            
            Map<String, Object> todo3 = new HashMap<>();
            todo3.put("id", 3);
            todo3.put("title", "权限异常用户");
            todo3.put("description", "2个用户权限异常需要处理");
            todo3.put("time", "2小时前");
            todo3.put("type", "key");
            todo3.put("icon", "el-icon-lock");
            todo3.put("color", "#F56C6C");
            mockTodoList.add(todo3);
            
            return AjaxResult.success(mockTodoList);
        }
    }

    /**
     * 获取车辆统计数据
     */
    @PreAuthorize("@ss.hasPermi('dk:dashboard:view')")
    @GetMapping("/vehicleStats")
    public AjaxResult getVehicleStats() {
        try {
            Map<String, Object> vehicleStats = dkDashboardService.getVehicleStats();
            return AjaxResult.success(vehicleStats);
        } catch (Exception e) {
            // 返回模拟数据
            Map<String, Object> mockStats = new HashMap<>();
            mockStats.put("total", 100);
            mockStats.put("inUse", 65);
            mockStats.put("available", 30);
            mockStats.put("maintenance", 3);
            mockStats.put("disabled", 2);
            return AjaxResult.success(mockStats);
        }
    }

    /**
     * 获取最近活动列表
     */
    @PreAuthorize("@ss.hasPermi('dk:dashboard:view')")
    @GetMapping("/activityList")
    public AjaxResult getActivityList() {
        try {
            List<Map<String, Object>> activityList = dkDashboardService.getActivityList();
            return AjaxResult.success(activityList);
        } catch (Exception e) {
            // 返回模拟数据
            List<Map<String, Object>> mockActivityList = new ArrayList<>();
            
            Map<String, Object> activity1 = new HashMap<>();
            activity1.put("id", 1);
            activity1.put("title", "用户申请审核通过");
            activity1.put("description", "张三的租车申请已审核通过");
            activity1.put("time", "2024-01-15 14:30:00");
            activity1.put("color", "#67C23A");
            mockActivityList.add(activity1);
            
            Map<String, Object> activity2 = new HashMap<>();
            activity2.put("id", 2);
            activity2.put("title", "数字钥匙分配");
            activity2.put("description", "为李四分配了比亚迪秦的数字钥匙");
            activity2.put("time", "2024-01-15 14:15:00");
            activity2.put("color", "#409EFF");
            mockActivityList.add(activity2);
            
            Map<String, Object> activity3 = new HashMap<>();
            activity3.put("id", 3);
            activity3.put("title", "用户权限限制");
            activity3.put("description", "王五因违规使用被限制权限");
            activity3.put("time", "2024-01-15 13:45:00");
            activity3.put("color", "#F56C6C");
            mockActivityList.add(activity3);
            
            Map<String, Object> activity4 = new HashMap<>();
            activity4.put("id", 4);
            activity4.put("title", "车辆归还");
            activity4.put("description", "特斯拉Model3已归还，状态更新为空闲");
            activity4.put("time", "2024-01-15 13:20:00");
            activity4.put("color", "#909399");
            mockActivityList.add(activity4);
            
            return AjaxResult.success(mockActivityList);
        }
    }

    /**
     * 获取运营统计数据
     */
    @PreAuthorize("@ss.hasPermi('dk:dashboard:view')")
    @GetMapping("/operationStats")
    public AjaxResult getOperationStats() {
        try {
            Map<String, Object> operationStats = dkDashboardService.getOperationStats();
            return AjaxResult.success(operationStats);
        } catch (Exception e) {
            // 返回模拟数据
            Map<String, Object> mockStats = new HashMap<>();
            mockStats.put("totalUsers", 1250);
            mockStats.put("activeUsers", 890);
            mockStats.put("totalVehicles", 100);
            mockStats.put("totalKeys", 1180);
            mockStats.put("todayApplications", 12);
            mockStats.put("todayAssignments", 15);
            mockStats.put("monthlyRevenue", 125000);
            mockStats.put("utilizationRate", 85.5);
            return AjaxResult.success(mockStats);
        }
    }
}

<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>数字钥匙系统 - 功能导航</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            display: flex;
            flex-direction: column;
            height: 100vh;
            background-color: #f5f5f5;
            color: #333;
        }
        
        header {
            background-color: #2c3e50;
            color: white;
            padding: 15px 20px;
            text-align: center;
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
        }
        
        .container {
            display: flex;
            flex: 1;
            overflow: hidden;
        }
        
        .nav {
            width: 250px;
            background-color: #34495e;
            color: white;
            padding: 20px 0;
            overflow-y: auto;
            box-shadow: 2px 0 5px rgba(0,0,0,0.1);
        }
        
        .nav-item {
            padding: 12px 20px;
            cursor: pointer;
            transition: background-color 0.3s;
            border-left: 3px solid transparent;
        }
        
        .nav-item:hover {
            background-color: #2c3e50;
            border-left-color: #3498db;
        }
        
        .nav-item.active {
            background-color: #2c3e50;
            border-left-color: #3498db;
            font-weight: bold;
        }
        
        .content {
            flex: 1;
            overflow: hidden;
            background-color: white;
            box-shadow: inset 2px 0 5px rgba(0,0,0,0.05);
        }
        
        iframe {
            width: 100%;
            height: 100%;
            border: none;
        }
        
        .loading {
            display: flex;
            align-items: center;
            justify-content: center;
            height: 100%;
            font-size: 18px;
            color: #7f8c8d;
        }
        
        @media (max-width: 768px) {
            .container {
                flex-direction: column;
            }
            
            .nav {
                width: 100%;
                padding: 10px 0;
            }
            
            .content {
                flex: 1;
            }
        }
    </style>
</head>
<body>
    <header>
        <h1>数字钥匙系统演示</h1>
    </header>
    
    <div class="container">
        <div class="nav" id="nav">
            <!-- 导航项将通过JavaScript动态添加 -->
        </div>
        
        <div class="content">
            <iframe id="content-frame" src="about:blank"></iframe>
        </div>
    </div>
    
    <!-- 引入文件列表JS -->
    <script src="files-list.js"></script>
    
    <script>
        // 从localStorage获取HTML文件列表
        function getRegisteredFiles() {
            const filesJson = localStorage.getItem('htmlFiles');
            if (filesJson) {
                return JSON.parse(filesJson);
            }
            return [];
        }
        
        // 创建导航菜单
        function createNavigation() {
            const navElement = document.getElementById('nav');
            navElement.innerHTML = ''; // 清空现有内容
            
            const pages = getRegisteredFiles();
            window.pages = pages;
            
            if (pages.length === 0) {
                navElement.innerHTML = '<div class="nav-item">没有可用的页面</div>';
                return;
            }
            
            pages.forEach(page => {
                const navItem = document.createElement('div');
                navItem.className = 'nav-item';
                navItem.textContent = page.name;
                navItem.dataset.file = page.file;
                navItem.onclick = function() {
                    // 设置当前项为激活状态
                    document.querySelectorAll('.nav-item').forEach(item => {
                        item.classList.remove('active');
                    });
                    this.classList.add('active');
                    
                    // 加载内容
                    loadContent(page.file);
                };
                
                navElement.appendChild(navItem);
            });
        }
        
        // 重置注册表（用于开发调试）
        function resetRegistry() {
            localStorage.removeItem('htmlFiles');
            createNavigation();
        }
        
        // 加载内容到iframe
        function loadContent(fileName) {
            const iframe = document.getElementById('content-frame');
            iframe.src = fileName;
        }
        
        // 初始化
        document.addEventListener('DOMContentLoaded', function() {
            // 添加重置按钮（开发时使用）
            const header = document.querySelector('header');
            const resetButton = document.createElement('button');
            resetButton.textContent = '重置菜单';
            resetButton.style.marginLeft = '10px';
            resetButton.onclick = resetRegistry;
            header.appendChild(resetButton);
            
            createNavigation();
            
            // 默认加载第一个页面
            if (window.pages && window.pages.length > 0) {
                const firstNavItem = document.querySelector('.nav-item');
                if (firstNavItem) {
                    firstNavItem.classList.add('active');
                    loadContent(window.pages[0].file);
                }
            }
        });
    </script>
</body>
</html> 
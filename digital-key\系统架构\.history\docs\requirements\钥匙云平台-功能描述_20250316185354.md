
## 钥匙云平台核心架构（一张PPT）

### 核心模块及作用

- **钥匙生命周期管理**：创建、授权、撤销数字钥匙，管理钥匙权限与有效期
  
- **车辆关联服务**：验证车辆信息，建立车辆与钥匙的安全绑定关系
  
- **安全认证中心**：确保用户身份可信，执行权限验证，防止未授权访问
  
- **密钥管理系统**：生成加密密钥，确保密钥安全存储与分发，支持密钥更新
  
- **统一接口服务**：提供标准化API，连接外部系统，确保接口安全
  
- **安全通信通道**：实现端到端加密通信，防止数据被窃听或篡改
  
- **异常监控与处理**：检测异常行为，执行安全响应，记录安全事件

## 手机端核心功能

- **数字钥匙管理**：查看、使用和管理个人数字钥匙，包括钥匙状态监控和权限查看
  
- **安全存储区域**：在手机安全区域存储密钥、配对令牌和车辆连接信息
  
- **蓝牙连接管理**：扫描识别车辆广播，建立和维护与车辆的蓝牙连接
  
- **无感连接功能**：后台自动与车辆建立连接，根据距离和用户习惯执行预设操作
  
- **安全认证机制**：执行双向身份验证，防止未授权访问和中继攻击
  
- **上下文感知优化**：根据位置、时间和活动状态调整扫描频率，优化电量使用
  
- **异常处理能力**：检测并处理连接异常、认证失败等问题，确保使用安全

## 车端核心功能

- **蓝牙通信模块**：发送加密广播信号，接收并处理连接请求
  
- **安全验证系统**：验证手机身份，确保只有授权设备能够连接
  
- **密钥存储管理**：在安全区域存储根密钥、会话密钥和授权设备信息
  
- **状态监控系统**：监控车辆状态变化，及时向已连接设备推送更新
  
- **智能广播策略**：根据环境和状态调整广播参数，平衡安全性和可用性
  
- **控车算法**：基于RSSI信号强度计算与手机的距离，支持距离感知操作

- **心跳机制**：维护连接状态，检测异常连接，防范安全威胁

/*
 * @Author: shuying <EMAIL>
 * @Date: 2024-05-14 16:10:28
 * @LastEditors: shuying <EMAIL>
 * @LastEditTime: 2024-05-14 16:26:44
 * @FilePath: \digital-key-manage\ruoyi-ui\src\api\dk\vehicleBluetoothKeys.js
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
import request from '@/utils/request'

// 查询蓝牙密钥列表
export function listVehicleBluetoothKeys(query) {
  return request({
    url: '/dk/vehicleBluetoothKeys/list',
    method: 'get',
    params: query
  })
}

// 查询蓝牙密钥详细
export function getVehicleBluetoothKeys(userId) {
  return request({
    url: '/dk/vehicleBluetoothKeys/' + userId,
    method: 'get'
  })
}

// 新增蓝牙密钥
export function addVehicleBluetoothKeys(data) {
  return request({
    url: '/dk/vehicleBluetoothKeys',
    method: 'post',
    data: data
  })
}

// 修改蓝牙密钥
export function updateVehicleBluetoothKeys(data) {
  return request({
    url: '/dk/vehicleBluetoothKeys',
    method: 'put',
    data: data
  })
}

// 删除蓝牙密钥
export function delVehicleBluetoothKeys(userId) {
  return request({
    url: '/dk/vehicleBluetoothKeys/' + userId,
    method: 'delete'
  })
}

我们要做汽车数字钥匙架构设计，明确的是，我们会有手机app、汽车BLE模块和Tbox、钥匙云平台等。

我们需要在架构设计里面体现出以下内容

1. 整体架构图：几个模块分别要实现哪些东西
2. 首次蓝牙配对过程设计，泳道图设计（手机、汽车、云平台三个平台交互细节）
3. 蓝牙通讯过程设计（手机、汽车、云平台三个平台交互细节）
4. 蓝牙通讯安全设计（如何保证通讯安全，认证和加密的细节）
5. 密钥在各端的存储、更新、撤销机制设计
6. 时间同步机制设计

后备点（如果时间允许）：
1. 故障应急处理机制设计（丢手机、换手机也包含在内）
2. 安全威胁处理
3. 平台兼容方案
4. 对接方案 (同app、TSP平台等对接方案)
5. 普通控车、无感控车方案
6. TSP平台对接服务
7. 配对1对N解决方案（车端根密钥、手机端虚拟密钥以及各自配对令牌存储策略设计）
8. 用户系统对接方案（本方案云端没有用户管理、权限管理，需要对接TSP等平台实现）
9. 密钥更新策略设计
10. TBOX上报操作记录整体处理策略（兼顾网络环境差异）
11. 钥匙分享
12. 手机省电策略
13. 钥匙主动、被动撤销



特别注意：
我们做的是一个包含手机APP对应的SDK、云平台SDK以及完整的车端软硬件开发的集成产品，所以在每一块设计的时候，一定要考虑到和其它模块的关联，以及如何集成到一起。
比如独立sdk是不会考虑用户登录、权限认证等问题，这些是在集成方案里可能会提到的，独立的技术方案里面就不会有身份验证这个设计



接下来：
1.安全设计补足
2.找朱疆、赵伟，完善车端逻辑
3.后备点里面必要部分筛出来，设计补足
4.抽象补足PPT



可能的问题：
1、无感控车前面的“启用场景”，具体是哪些场景？
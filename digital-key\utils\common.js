/**
* 显示消息提示框
* @param content 提示的标题
*/
export function toast(content) {
  uni.showToast({
    icon: 'none',
    title: content
  })
}

/**
* 显示模态弹窗
* @param content 提示的标题
*/
export function showConfirm(content) {
  return new Promise((resolve, reject) => {
    uni.showModal({
      title: '提示',
      content: content,
      cancelText: '取消',
      confirmText: '确定',
      success: function(res) {
        resolve(res)
      }
    })
  })
}

/**
* 参数处理
* @param params 参数
*/
export function tansParams(params) {
  let result = ''
  for (const propName of Object.keys(params)) {
    const value = params[propName]
    var part = encodeURIComponent(propName) + "="
    if (value !== null && value !== "" && typeof (value) !== "undefined") {
      if (typeof value === 'object') {
        for (const key of Object.keys(value)) {
          if (value[key] !== null && value[key] !== "" && typeof (value[key]) !== 'undefined') {
            let params = propName + '[' + key + ']'
            var subPart = encodeURIComponent(params) + "="
            result += subPart + encodeURIComponent(value[key]) + "&"
          }
        }
      } else {
        result += part + encodeURIComponent(value) + "&"
      }
    }
  }
  return result
}

/**
 * @description: 16进制字符串转UTF-8字符串
 * @return {*} UTF-8字符串
 */
export function hexToString(hexStr) {
  // 确保十六进制字符串长度是偶数
  if (hexStr.length % 2 !== 0) {
    throw new Error(
      "Hexadecimal string must have an even number of characters"
    );
  }

  // 将十六进制字符串转换为字节数组
  const bytes = [];
  for (let i = 0; i < hexStr.length; i += 2) {
    bytes.push(parseInt(hexStr.substr(i, 2), 16));
  }

  // 去掉末尾的null字符
  if (bytes[bytes.length - 1] === 0) {
    bytes.pop();
  }

  // 将字节数组转换为字符串
  const result = String.fromCharCode.apply(null, bytes);
  return result;
}

/**
 * @description: 字符串转16进制
 * @param {*} str
 * @return {*}
 */
export function stringToHex(str) {
  let hexString = "";
  for (let i = 0; i < str.length; i++) {
    let charCode = str.charCodeAt(i); // 获取字符的ASCII码
    let hex = charCode.toString(16).toUpperCase(); // 转换为16进制字符串，并转换为大写
    hexString += hex.length === 1 ? "0" + hex : hex; // 如果16进制数只有一位，则在前面补零
  }
  return hexString;
}

/**
 * @description: arraybuffer转16进制字符串（支持Android、Ios）
 * @param {*} buffer
 * @return {*}
 */
export function ab2hex(buffer) {
  const hexArr = Array.prototype.map.call(
  new Uint8Array(buffer),
  function(bit) {
    return ('00' + bit.toString(16)).slice(-2);
  }
  );
  return hexArr.join('');
}

/**
 * @description: 将传入值的长度用“0”补齐到传入的目标长度，并转成字符串返回
 * @param {*} value 需要补齐的值
 * @param {*} targetLength 需要补齐到的目标长度（默认补齐到20位）
 * @return {*}
 */
export function padValToString(value, targetLength) {
  let valStr = (value || "").toString();
  
  // 计算需要补的位数
  let paddingLength = (targetLength || 20) - valStr.length;

  // 用 '0' 补全
  if (paddingLength > 0) {
    valStr = '0'.repeat(paddingLength) + valStr;
  }

  return valStr;
}

/**
 * @description: 将传入值根据哈希值计算，最终返回入参的长度的字符串
 * @param {*} inputString 需要转换的值
 * @param {*} outputLength 需要转换到的长度（默认转换成6位）
 * @return {*}
 */
export function transformString(inputString, outputLength) {
  const alphabet = "abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789"; // 可以根据需要扩展字符集
  const inputLength = inputString?.length || 0; // 输入字符串的长度
  outputLength = outputLength || 6; // 如果没有传入有效的长度值，则输出字符串的固定长度默认为6个字符

  let hash = 0;
  // 计算哈希值，将输入字符串中每个字符的 ASCII 值加权并转换为一个数值
  for (let i = 0; i < inputLength; i++) {
      hash = (hash << 5) - hash + inputString.charCodeAt(i); // 哈希计算公式
      hash &= hash; // 确保哈希值为32位整数
  }

  let result = '';
  // 根据哈希值生成长度为入参长度的输出字符串
  for (let i = 0; i < outputLength; i++) {
      const charIndex = hash % alphabet.length; // 计算哈希值对字符集长度取模，确定下一个字符的索引
      result += alphabet.charAt(charIndex); // 根据索引获取字符并添加到结果字符串中
      hash = Math.floor(hash / alphabet.length); // 更新哈希值，相当于除以字符集长度
  }

  return result;
}
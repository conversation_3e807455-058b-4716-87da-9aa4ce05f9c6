/**
 * PlantUML源代码对话框处理脚本
 */
function showPumlSource() {
    var dialog = document.getElementById('puml-source-dialog');
    if (dialog) {
        dialog.style.display = 'block';
        document.body.style.overflow = 'hidden'; // 防止滚动背景
    }
}

function closePumlDialog() {
    var dialog = document.getElementById('puml-source-dialog');
    if (dialog) {
        dialog.style.display = 'none';
        document.body.style.overflow = ''; // 恢复滚动
    }
}

// 点击对话框外部关闭对话框
document.addEventListener('DOMContentLoaded', function() {
    var dialog = document.getElementById('puml-source-dialog');
    if (dialog) {
        dialog.addEventListener('click', function(event) {
            if (event.target === dialog) {
                closePumlDialog();
            }
        });
    }
    
    // ESC键关闭对话框
    document.addEventListener('keydown', function(event) {
        if (event.key === 'Escape') {
            closePumlDialog();
        }
    });
}); 
# API路径修复说明

## 问题描述
点击申请状态页面时出现错误：
```
{"msg":"请求参数类型不匹配，参数[applicationId]要求类型为：'java.lang.Long'，但输入值为：'my-applications'","code":500}
```

## 问题原因分析

### 1. 路径不匹配问题
- **前端API调用**：`/dk/userApplication/my-applications`
- **后端控制器**：`@RequestMapping("/userApplication")`
- **实际请求路径**：`/userApplication/my-applications`

### 2. 接口不存在问题
- 前端调用 `/userApplication/my-applications` 接口
- 后端没有对应的 `@GetMapping("/my-applications")` 方法
- 请求被路由到 `@GetMapping("/{applicationId}")` 方法
- `my-applications` 被当作 `applicationId` 参数传递
- 导致类型转换错误（String → Long）

## 修复方案

### 1. 统一API路径
将前端API调用路径修改为与后端控制器一致：

**修改前：**
```javascript
// 前端API
url: '/dk/userApplication/my-applications'  // 错误路径

// 后端控制器
@RequestMapping("/userApplication")  // 不匹配
```

**修改后：**
```javascript
// 前端API
url: '/userApplication/list'  // 使用现有的list接口

// 后端控制器
@RequestMapping("/userApplication")  // 匹配
@GetMapping("/list")  // 对应的方法存在
```

### 2. 修改的文件和内容

#### 前端API文件：`digital-key/api/dk.js`
```javascript
// 修改前
export function getUserApplicationList(params) {
  return request({
    url: '/dk/userApplication/my-applications',  // 错误路径
    method: 'get',
    params: params
  })
}

// 修改后
export function getUserApplicationList(params) {
  return request({
    url: '/userApplication/list',  // 正确路径
    method: 'get',
    params: params
  })
}
```

#### 其他API接口也进行了相应修改：
- `submitUserApplication`: `/dk/userApplication` → `/userApplication`
- `getUserApplicationDetail`: `/dk/userApplication/{id}` → `/userApplication/{id}`
- `withdrawApplication`: `/dk/userApplication/withdraw/{id}` → `/userApplication/withdraw/{id}`
- `supplementApplication`: `/dk/userApplication/supplement` → `/userApplication/supplement`

### 3. 新增后端接口

#### 在 `DkUserApplicationController.java` 中添加：
```java
/**
 * 撤销申请
 */
@PutMapping("/withdraw/{applicationId}")
public AjaxResult withdrawApplication(@PathVariable Long applicationId) {
    return toAjax(dkUserApplicationService.withdrawApplication(applicationId));
}

/**
 * 补充申请资料
 */
@PutMapping("/supplement")
public AjaxResult supplementApplication(@RequestBody DkUserApplication dkUserApplication) {
    return toAjax(dkUserApplicationService.updateDkUserApplication(dkUserApplication));
}
```

## 测试验证

### 1. 创建API测试页面
- 文件：`digital-key/pages/test/api-test.vue`
- 路由：`/pages/test/api-test`
- 功能：测试各个API接口是否正常工作

### 2. 测试步骤
1. **重新编译前端**：
   ```bash
   cd digital-key
   npm run dev:mp-weixin
   ```

2. **重启后端服务**：
   ```bash
   cd digital-key-manage
   mvn clean compile
   # 重启Spring Boot应用
   ```

3. **访问测试页面**：
   - 打开 `/pages/test/api-test` 页面
   - 点击各个测试按钮验证API是否正常

4. **测试申请状态页面**：
   - 打开申请状态页面
   - 确认不再出现参数类型错误

## 预期结果

### ✅ 修复后应该实现：
1. 申请状态页面正常加载，不再出现参数类型错误
2. 能够正常获取申请列表数据
3. 提交申请功能正常工作
4. 撤销申请功能正常工作
5. 查看申请详情功能正常工作

### 🧪 验证方法：
1. **申请状态页面**：应该显示申请列表或空状态，不再有错误
2. **提交申请**：能够成功提交并看到成功提示
3. **API测试页面**：所有API测试都应该返回成功结果

## 注意事项

1. **路径一致性**：确保前后端API路径完全一致
2. **接口完整性**：确保前端调用的所有接口在后端都有对应实现
3. **参数类型**：确保路径参数类型匹配（如 Long 类型的 applicationId）
4. **错误处理**：添加适当的错误处理和用户提示

## 后续优化建议

1. **统一API前缀**：考虑为所有数字钥匙相关接口添加统一前缀 `/api/dk/`
2. **接口文档**：创建完整的API接口文档
3. **自动化测试**：添加API接口的自动化测试
4. **错误码规范**：统一错误码和错误信息格式

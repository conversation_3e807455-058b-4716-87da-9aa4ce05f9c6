# 🚀 最简单的4G控车服务器

## 📋 项目说明

这是一个**完全独立**的4G控车TCP服务器，专门为解决复杂依赖问题而创建：

- ✅ **零数据库依赖**：不需要MySQL、H2或任何数据库
- ✅ **最小依赖**：只依赖SpringBoot Web + WebSocket + FastJSON2
- ✅ **开箱即用**：无需复杂配置，直接启动即可
- ✅ **专注核心功能**：TCP服务器 + WebSocket + 4G控车协议

## 🛠️ 启动步骤

### 1. 编译项目
```bash
cd digital-key-manage/simple-tcp-server
mvn clean compile
```

### 2. 启动服务器
```bash
mvn spring-boot:run
```

### 3. 验证启动成功
看到以下输出表示启动成功：
```
🚀 最简单的4G控车服务器启动成功！
📡 TCP服务器端口: 9999
🌐 WebSocket端口: 8080
🎯 专注4G控车核心功能，无数据库依赖
✅ TCP服务器启动成功，监听端口: 9999
```

## 🧪 功能测试

### 1. 测试WebSocket连接
打开浏览器访问：`file:///你的路径/digital-key-manage/simple-tcp-server/test.html`

### 2. 测试TCP连接
```bash
telnet localhost 9999
```

### 3. 模拟TBOX设备
连接TCP后发送测试消息：
```
7E000A000A000102030405060708090A000030333032303030313033007E
```

## 📡 协议说明

### TCP协议（TBOX设备）
- **端口**：9999
- **格式**：7E...007E
- **锁车状态**：末尾为`3007E`
- **解锁状态**：末尾为`4007E`

### WebSocket协议（移动端）
- **端口**：8080
- **路径**：`/websocket/message?userId=1001`
- **锁车命令**：`{"action":"lock","userId":"1001"}`
- **解锁命令**：`{"action":"unlock","userId":"1001"}`

## 🎯 核心优势

1. **彻底解决依赖问题**：不会再有数据库、Nacos等复杂依赖冲突
2. **启动速度快**：无需初始化数据库，秒级启动
3. **内存占用小**：只加载必要的组件
4. **易于调试**：日志清晰，问题定位简单
5. **完全独立**：可以单独部署，不依赖其他服务

## 🔧 如果还有问题

如果这个最简单的版本还有问题，那就说明是Java环境或Maven配置问题，我们可以：

1. **检查Java版本**：`java -version`
2. **检查Maven版本**：`mvn -version`
3. **清理Maven缓存**：`mvn clean`
4. **重新下载依赖**：`mvn dependency:resolve`

这个版本已经是最简化的实现，专注于4G控车的核心功能！🎉

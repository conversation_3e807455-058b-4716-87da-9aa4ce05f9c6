@startuml
skinparam monochrome true

participant "云平台" as Cloud
participant "手机" as Mobile
participant "汽车蓝牙模块" as CarBT

== 蓝牙配对流程 ==

Cloud -> CarBT : 1. 生成根密钥并下发到汽车
CarBT -> CarBT : 2. 存储根密钥
Cloud -> Mobile : 3. APP登录，下发虚拟密钥（VIRKEY）
Mobile -> Mobile : 4. 保存虚拟密钥到安全存储区
CarBT -> Mobile : 5. 发送蓝牙广播（包含MAC地址等信息）
Mobile -> CarBT : 6. 扫描并连接到汽车蓝牙模块
CarBT -> Mobile : 7. 发起蓝牙安全请求
Mobile -> CarBT : 8. 发送蓝牙配对请求
CarBT -> CarBT : 9. 输入或生成配对码
Mobile -> Mobile : 10. 输入或生成配对码
CarBT -> Mobile : 11. 校验配对码
Mobile -> CarBT : 12. 校验配对码
CarBT -> CarBT : 13. 配对成功，绑定设备信息
Mobile -> Mobile : 14. 配对成功，绑定设备信息
CarBT -> Mobile : 15. 通过虚拟密钥进行身份认证
Mobile -> CarBT : 16. 发送加密的虚拟密钥和指令
CarBT -> CarBT : 17. 验证虚拟密钥
CarBT -> Mobile : 18. 返回身份认证结果
@enduml
<mxfile host="app.diagrams.net" modified="2023-11-15T08:30:00.000Z" agent="Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36" etag="abc123" version="21.6.6" type="device">
  <diagram id="digital-key-architecture" name="数字钥匙三端架构图">
    <mxGraphModel dx="1422" dy="798" grid="1" gridSize="10" guides="1" tooltips="1" connect="1" arrows="1" fold="1" page="1" pageScale="1" pageWidth="1169" pageHeight="827" math="0" shadow="0">
      <root>
        <mxCell id="0" />
        <mxCell id="1" parent="0" />
        
        <!-- 标题 -->
        <mxCell id="title" value="数字钥匙三端架构" style="text;html=1;strokeColor=none;fillColor=none;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=24;fontStyle=1;fontColor=#0066CC;" vertex="1" parent="1">
          <mxGeometry x="400" y="40" width="370" height="40" as="geometry" />
        </mxCell>
        
        <!-- 钥匙云平台 -->
        <mxCell id="cloud" value="钥匙云平台" style="swimlane;fontStyle=1;align=center;verticalAlign=top;childLayout=stackLayout;horizontal=1;startSize=30;horizontalStack=0;resizeParent=1;resizeParentMax=0;resizeLast=0;collapsible=0;marginBottom=0;html=1;fillColor=#dae8fc;strokeColor=#6c8ebf;gradientColor=#7ea6e0;fontSize=16;" vertex="1" parent="1">
          <mxGeometry x="400" y="120" width="370" height="160" as="geometry" />
        </mxCell>
        
        <mxCell id="cloud-content" value="&lt;div&gt;- 钥匙生命周期管理 (创建、授权、撤销)&lt;/div&gt;&lt;div&gt;- 车辆关联服务&lt;/div&gt;&lt;div&gt;- 安全认证中心&lt;/div&gt;&lt;div&gt;- 密钥管理系统&lt;/div&gt;&lt;div&gt;- 统一接口服务&lt;/div&gt;&lt;div&gt;- 时间服务器 (NTP/PTP)&lt;/div&gt;&lt;div&gt;- 异常监控与处理&lt;/div&gt;" style="text;html=1;strokeColor=none;fillColor=none;align=left;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=14;spacing=5;spacingLeft=10;" vertex="1" parent="cloud">
          <mxGeometry y="30" width="370" height="130" as="geometry" />
        </mxCell>
        
        <!-- 手机端 -->
        <mxCell id="mobile" value="手机端" style="swimlane;fontStyle=1;align=center;verticalAlign=top;childLayout=stackLayout;horizontal=1;startSize=30;horizontalStack=0;resizeParent=1;resizeParentMax=0;resizeLast=0;collapsible=0;marginBottom=0;html=1;fillColor=#d5e8d4;strokeColor=#82b366;gradientColor=#97d077;fontSize=16;" vertex="1" parent="1">
          <mxGeometry x="120" y="340" width="280" height="170" as="geometry" />
        </mxCell>
        
        <mxCell id="mobile-content" value="&lt;div&gt;- 钥匙管理模块&lt;/div&gt;&lt;div&gt;- 车辆控制模块&lt;/div&gt;&lt;div&gt;- 安全存储模块&lt;/div&gt;&lt;div&gt;- 蓝牙通信模块&lt;/div&gt;&lt;div&gt;- 时间同步模块&lt;/div&gt;&lt;div&gt;- 暗号交换模块&lt;/div&gt;&lt;div&gt;- 智能场景管理&lt;/div&gt;&lt;div&gt;- 标定模块&lt;/div&gt;" style="text;html=1;strokeColor=none;fillColor=none;align=left;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=14;spacing=5;spacingLeft=10;" vertex="1" parent="mobile">
          <mxGeometry y="30" width="280" height="140" as="geometry" />
        </mxCell>
        
        <!-- 车端 -->
        <mxCell id="lock" value="车端" style="swimlane;fontStyle=1;align=center;verticalAlign=top;childLayout=stackLayout;horizontal=1;startSize=30;horizontalStack=0;resizeParent=1;resizeParentMax=0;resizeLast=0;collapsible=0;marginBottom=0;html=1;fillColor=#ffe6cc;strokeColor=#d79b00;gradientColor=#ffce9f;fontSize=16;" vertex="1" parent="1">
          <mxGeometry x="680" y="340" width="280" height="170" as="geometry" />
        </mxCell>
        
        <mxCell id="lock-content" value="&lt;div&gt;- 蓝牙通信模块&lt;/div&gt;&lt;div&gt;- 钥匙验证模块&lt;/div&gt;&lt;div&gt;- 指令执行模块&lt;/div&gt;&lt;div&gt;- 安全存储模块&lt;/div&gt;&lt;div&gt;- 远程通信模块&lt;/div&gt;&lt;div&gt;- 时间同步模块&lt;/div&gt;&lt;div&gt;- 暗号交换模块&lt;/div&gt;&lt;div&gt;- 用户行为分析模块&lt;/div&gt;" style="text;html=1;strokeColor=none;fillColor=none;align=left;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=14;spacing=5;spacingLeft=10;" vertex="1" parent="lock">
          <mxGeometry y="30" width="280" height="140" as="geometry" />
        </mxCell>
        
        <!-- 连接线 -->
        <!-- 云平台到手机端的连接 -->
        <mxCell id="cloud-to-mobile" value="HTTPS/API" style="endArrow=classic;startArrow=classic;html=1;rounded=0;exitX=0.25;exitY=1;exitDx=0;exitDy=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;strokeWidth=2;fontSize=12;fontColor=#0066CC;edgeStyle=orthogonalEdgeStyle;curved=1;" edge="1" parent="1" source="cloud-content" target="mobile">
          <mxGeometry x="-0.2" y="-10" width="50" height="50" relative="1" as="geometry">
            <mxPoint x="560" y="400" as="sourcePoint" />
            <mxPoint x="610" y="350" as="targetPoint" />
            <mxPoint as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="cloud-to-mobile-functions" value="钥匙授权、身份认证、配置同步" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];fontSize=11;fontColor=#000000;fontStyle=0;labelBackgroundColor=#FFFFFF;" vertex="1" connectable="0" parent="cloud-to-mobile">
          <mxGeometry x="0.3" y="0" relative="1" as="geometry">
            <mxPoint x="-65" y="-10" as="offset" />
          </mxGeometry>
        </mxCell>
        
        <!-- 云平台到车端的连接 -->
        <mxCell id="cloud-to-lock" value="MQTT/WebSocket" style="endArrow=classic;startArrow=classic;html=1;rounded=0;exitX=0.75;exitY=1;exitDx=0;exitDy=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;strokeWidth=2;fontSize=12;fontColor=#0066CC;edgeStyle=orthogonalEdgeStyle;curved=1;" edge="1" parent="1" source="cloud-content" target="lock">
          <mxGeometry x="-0.2" y="10" width="50" height="50" relative="1" as="geometry">
            <mxPoint x="560" y="400" as="sourcePoint" />
            <mxPoint x="610" y="350" as="targetPoint" />
            <mxPoint as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="cloud-to-lock-functions" value="车辆绑定、钥匙权限更新、远程控制" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];fontSize=11;fontColor=#000000;fontStyle=0;labelBackgroundColor=#FFFFFF;" vertex="1" connectable="0" parent="cloud-to-lock">
          <mxGeometry x="0.3" y="0" relative="1" as="geometry">
            <mxPoint x="65" y="-10" as="offset" />
          </mxGeometry>
        </mxCell>
        
        <!-- 手机端到车端的连接 -->
        <mxCell id="mobile-to-lock" value="蓝牙/RSSI" style="endArrow=classic;startArrow=classic;html=1;rounded=0;exitX=1;exitY=0.5;exitDx=0;exitDy=0;entryX=0;entryY=0.5;entryDx=0;entryDy=0;strokeWidth=2;fontSize=12;fontColor=#0066CC;" edge="1" parent="1" source="mobile-content" target="lock-content">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="560" y="400" as="sourcePoint" />
            <mxPoint x="610" y="350" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="mobile-to-lock-functions" value="暗号交换、距离感知、车辆控制指令" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];fontSize=11;fontColor=#000000;fontStyle=0;labelBackgroundColor=#FFFFFF;" vertex="1" connectable="0" parent="mobile-to-lock">
          <mxGeometry x="-0.1385" y="1" relative="1" as="geometry">
            <mxPoint y="-15" as="offset" />
          </mxGeometry>
        </mxCell>
        
        <!-- 安全层 -->
        <mxCell id="security" value="安全层" style="swimlane;fontStyle=1;align=center;verticalAlign=top;childLayout=stackLayout;horizontal=1;startSize=30;horizontalStack=0;resizeParent=1;resizeParentMax=0;resizeLast=0;collapsible=0;marginBottom=0;html=1;fillColor=#f8cecc;strokeColor=#b85450;gradientColor=#ea6b66;fontSize=16;" vertex="1" parent="1">
          <mxGeometry x="300" y="560" width="370" height="130" as="geometry" />
        </mxCell>
        
        <mxCell id="security-content" value="&lt;div&gt;- 端到端加密通信&lt;/div&gt;&lt;div&gt;- 多因素身份认证&lt;/div&gt;&lt;div&gt;- 密钥生成与分发&lt;/div&gt;&lt;div&gt;- 时间同步与防重放&lt;/div&gt;&lt;div&gt;- 异常行为监控与响应&lt;/div&gt;&lt;div&gt;- 安全区域存储&lt;/div&gt;" style="text;html=1;strokeColor=none;fillColor=none;align=left;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=14;spacing=5;spacingLeft=10;" vertex="1" parent="security">
          <mxGeometry y="30" width="370" height="100" as="geometry" />
        </mxCell>
        
        <!-- 安全层连接 -->
        <mxCell id="security-connection" value="" style="shape=curlyBracket;whiteSpace=wrap;html=1;rounded=1;flipH=1;labelPosition=right;verticalLabelPosition=middle;align=left;verticalAlign=middle;rotation=90;size=0.5;strokeWidth=2;fillColor=none;" vertex="1" parent="1">
          <mxGeometry x="475" y="430" width="20" height="440" as="geometry" />
        </mxCell>
        
        <!-- 数据流注解 -->
        <mxCell id="data-flow-title" value="数据流与交互" style="text;html=1;strokeColor=none;fillColor=none;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=16;fontStyle=1;fontColor=#0066CC;" vertex="1" parent="1">
          <mxGeometry x="800" y="560" width="160" height="30" as="geometry" />
        </mxCell>
        
        <mxCell id="data-flow-content" value="&lt;div style=&quot;text-align: left; font-size: 14px;&quot;&gt;1. 钥匙授权流程：云平台→手机端→车端&lt;/div&gt;&lt;div style=&quot;text-align: left; font-size: 14px;&quot;&gt;2. 无感操作流程：手机端↔车端(RSSI距离感知)&lt;/div&gt;&lt;div style=&quot;text-align: left; font-size: 14px;&quot;&gt;3. 安全校验流程：暗号交换+时间同步&lt;/div&gt;&lt;div style=&quot;text-align: left; font-size: 14px;&quot;&gt;4. 远程控制流程：云平台→车端&lt;/div&gt;&lt;div style=&quot;text-align: left; font-size: 14px;&quot;&gt;5. 状态反馈流程：车端→云平台→手机端&lt;/div&gt;" style="text;html=1;strokeColor=#666666;fillColor=#f5f5f5;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=1;fontSize=14;gradientColor=#b3b3b3;spacing=5;spacingLeft=10;" vertex="1" parent="1">
          <mxGeometry x="730" y="590" width="320" height="100" as="geometry" />
        </mxCell>
      </root>
    </mxGraphModel>
  </diagram>
</mxfile>

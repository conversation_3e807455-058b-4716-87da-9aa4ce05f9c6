-- 数字钥匙系统测试数据脚本
-- 执行前请确保已创建相关表结构

-- 清理现有测试数据
DELETE FROM dk_key_assignment WHERE assignment_id BETWEEN 1 AND 100;
DELETE FROM dk_user_application WHERE application_id BETWEEN 1 AND 100;
DELETE FROM dk_vehicle_info WHERE vehicle_id BETWEEN 1 AND 100;

-- 1. 车辆信息测试数据
INSERT INTO dk_vehicle_info (vehicle_id, vin_code, brand, model, color, license_plate, vehicle_type, location, status, mileage, fuel_level, remark, create_time, update_time) VALUES
(1, 'LSGKB54U8EA123456', '比亚迪', '秦PLUS DM-i', '珍珠白', '京A12345', '紧凑型轿车', '停车场A区01号', '0', 15230.5, 85, '', NOW(), NOW()),
(2, 'LFV2A21K8E4123456', '特斯拉', 'Model 3', '珍珠白多涂层', '京A67890', '中型轿车', '停车场B区05号', '1', 8750.2, 92, '', NOW(), NOW()),
(3, 'LHGCV1658EA123456', '本田', '雅阁', '星月白', '京A11111', '中型轿车', '停车场A区15号', '2', 45680.8, 25, '定期保养中', NOW(), NOW()),
(4, 'LHGCV1658EA789012', '本田', '思域', '炫动蓝', '京A22222', '紧凑型轿车', '停车场B区10号', '0', 12500.0, 78, '', NOW(), NOW()),
(5, 'LSGKB54U8EA789012', '比亚迪', '汉EV', '汉宫红', '京A33333', '中大型轿车', '停车场A区08号', '1', 6800.5, 88, '', NOW(), NOW()),
(6, 'LFV2A21K8E4789012', '特斯拉', 'Model Y', '深海蓝', '京A44444', '中型SUV', '停车场C区03号', '0', 3200.0, 95, '', NOW(), NOW()),
(7, 'LHGCV1658EA456789', '本田', 'CR-V', '奥夫特黑', '京A55555', '紧凑型SUV', '停车场A区20号', '2', 28900.3, 45, '例行检查', NOW(), NOW()),
(8, 'LSGKB54U8EA456789', '比亚迪', '宋PLUS DM-i', '苍穹灰', '京A66666', '紧凑型SUV', '停车场B区15号', '0', 18750.8, 72, '', NOW(), NOW()),
(9, 'LFV2A21K8E4456789', '特斯拉', 'Model S', '珍珠白多涂层', '京A77777', '大型轿车', '停车场C区01号', '1', 5600.2, 90, '', NOW(), NOW()),
(10, 'LHGCV1658EA234567', '本田', '奥德赛', '珠光白', '京A88888', 'MPV', '停车场A区25号', '0', 22100.5, 65, '', NOW(), NOW());

-- 2. 用户申请测试数据
INSERT INTO dk_user_application (application_id, application_no, user_id, real_name, phone, id_card, driving_license, application_reason, status, audit_user_id, audit_user_name, audit_time, audit_remark, create_time, update_time) VALUES
(1, 'APP20240115001', 1001, '张三', '13800138001', '110101199001011234', 'D110101199001011234', '商务出行需要', '1', 1, '管理员', '2024-01-15 10:00:00', '审核通过', '2024-01-15 09:30:00', NOW()),
(2, 'APP20240115002', 1002, '李四', '13800138002', '110101199002021234', 'D110101199002021234', '日常通勤使用', '1', 1, '管理员', '2024-01-15 11:00:00', '审核通过', '2024-01-15 10:15:00', NOW()),
(3, 'APP20240115003', 1003, '王五', '13800138003', '110101199003031234', NULL, '周末出游', '2', 1, '管理员', '2024-01-15 12:00:00', '资料不完整，请补充驾驶证信息', '2024-01-15 11:20:00', NOW()),
(4, 'APP20240115004', 1004, '赵六', '13800138004', '110101199004041234', 'D110101199004041234', '周末出游', '0', NULL, NULL, NULL, NULL, '2024-01-15 11:45:00', NOW()),
(5, 'APP20240115005', 1005, '钱七', '13800138005', '110101199005051234', 'D110101199005051234', '临时用车', '0', NULL, NULL, NULL, NULL, '2024-01-15 14:30:00', NOW()),
(6, 'APP20240115006', 1006, '孙八', '13800138006', '110101199006061234', 'D110101199006061234', '客户接待', '1', 1, '管理员', '2024-01-15 16:00:00', '审核通过', '2024-01-15 15:45:00', NOW()),
(7, 'APP20240115007', 1007, '周九', '13800138007', '110101199007071234', 'D110101199007071234', '出差用车', '3', 1, '管理员', '2024-01-15 17:00:00', '申请时间冲突', '2024-01-15 16:20:00', NOW()),
(8, 'APP20240115008', 1008, '吴十', '13800138008', '110101199008081234', 'D110101199008081234', '紧急用车', '0', NULL, NULL, NULL, NULL, '2024-01-15 17:10:00', NOW());

-- 3. 钥匙分配测试数据
INSERT INTO dk_key_assignment (assignment_id, user_id, user_name, vehicle_id, vin_code, vehicle_brand, vehicle_model, status, assignment_time, revoke_time, operator_id, operator_name, remark, valid_start_time, valid_end_time, permission_type, create_time, update_time) VALUES
(1, 1001, '张三', 1, 'LSGKB54U8EA123456', '比亚迪', '秦PLUS DM-i', '1', '2024-01-15 09:00:00', NULL, 1, '管理员', '商务出行', '2024-01-15 09:00:00', '2024-01-15 18:00:00', '1', NOW(), NOW()),
(2, 1002, '李四', 2, 'LFV2A21K8E4123456', '特斯拉', 'Model 3', '2', '2024-01-14 14:00:00', '2024-01-14 19:30:00', 1, '管理员', '日常通勤', '2024-01-14 14:00:00', '2024-01-14 20:00:00', '1', NOW(), NOW()),
(3, 1003, '王五', 3, 'LHGCV1658EA123456', '本田', '雅阁', '2', '2024-01-13 10:00:00', '2024-01-13 18:30:00', 1, '管理员', '周末出游', '2024-01-13 10:00:00', '2024-01-13 18:00:00', '1', NOW(), NOW()),
(4, 1006, '孙八', 4, 'LHGCV1658EA789012', '本田', '思域', '1', '2024-01-15 08:30:00', NULL, 1, '管理员', '客户接待', '2024-01-15 08:30:00', '2024-01-15 17:30:00', '1', NOW(), NOW()),
(5, 1005, '钱七', 5, 'LSGKB54U8EA789012', '比亚迪', '汉EV', '2', '2024-01-12 09:15:00', '2024-01-12 18:00:00', 1, '管理员', '临时用车', '2024-01-12 09:15:00', '2024-01-12 18:15:00', '1', NOW(), NOW()),
(6, 1007, '周九', 6, 'LFV2A21K8E4789012', '特斯拉', 'Model Y', '1', '2024-01-15 07:45:00', NULL, 1, '管理员', '出差用车', '2024-01-15 07:45:00', '2024-01-15 19:45:00', '1', NOW(), NOW()),
(7, 1008, '吴十', 7, 'LHGCV1658EA456789', '本田', 'CR-V', '0', '2024-01-11 11:20:00', NULL, 1, '管理员', '紧急用车', '2024-01-11 11:20:00', '2024-01-11 16:20:00', '1', NOW(), NOW()),
(8, 1004, '赵六', 8, 'LSGKB54U8EA456789', '比亚迪', '宋PLUS DM-i', '2', '2024-01-10 13:30:00', '2024-01-10 20:15:00', 1, '管理员', '周末出游', '2024-01-10 13:30:00', '2024-01-10 20:30:00', '1', NOW(), NOW());

-- 4. 数字钥匙蓝牙密钥测试数据（如果表存在）
INSERT INTO dk_vehicle_bluetooth_keys (user_id, vehicle_vin, bluetooth_temp_key, bluetooth_temp_key_time, bluetooth_perm_key, bluetooth_perm_key_time, user_status, status_update_time, operator_id, remark) VALUES
(1001, 'LSGKB54U8EA123456', 'TK001234567890ABCDEF', '2024-01-15 09:00:00', 'PK001234567890ABCDEF', '2024-01-15 09:00:00', '1', '2024-01-15 09:00:00', 1, '正常使用'),
(1002, 'LFV2A21K8E4123456', 'TK002234567890ABCDEF', '2024-01-14 14:00:00', 'PK002234567890ABCDEF', '2024-01-14 14:00:00', '1', '2024-01-14 14:00:00', 1, '正常使用'),
(1003, 'LHGCV1658EA123456', 'TK003234567890ABCDEF', '2024-01-13 10:00:00', 'PK003234567890ABCDEF', '2024-01-13 10:00:00', '2', '2024-01-13 18:30:00', 1, '权限受限'),
(1006, 'LHGCV1658EA789012', 'TK004234567890ABCDEF', '2024-01-15 08:30:00', 'PK004234567890ABCDEF', '2024-01-15 08:30:00', '1', '2024-01-15 08:30:00', 1, '正常使用'),
(1005, 'LSGKB54U8EA789012', 'TK005234567890ABCDEF', '2024-01-12 09:15:00', 'PK005234567890ABCDEF', '2024-01-12 09:15:00', '1', '2024-01-12 18:00:00', 1, '已归还'),
(1007, 'LFV2A21K8E4789012', 'TK006234567890ABCDEF', '2024-01-15 07:45:00', 'PK006234567890ABCDEF', '2024-01-15 07:45:00', '1', '2024-01-15 07:45:00', 1, '正常使用'),
(1008, 'LHGCV1658EA456789', 'TK007234567890ABCDEF', '2024-01-11 11:20:00', 'PK007234567890ABCDEF', '2024-01-11 11:20:00', '3', '2024-01-11 16:30:00', 1, '异常状态'),
(1004, 'LSGKB54U8EA456789', 'TK008234567890ABCDEF', '2024-01-10 13:30:00', 'PK008234567890ABCDEF', '2024-01-10 13:30:00', '1', '2024-01-10 20:15:00', 1, '已归还');

-- 5. 重置自增ID（如果需要）
-- ALTER TABLE dk_vehicle_info AUTO_INCREMENT = 11;
-- ALTER TABLE dk_user_application AUTO_INCREMENT = 9;
-- ALTER TABLE dk_key_assignment AUTO_INCREMENT = 9;

-- 提交事务
COMMIT;

-- 验证数据插入
SELECT '车辆信息' as table_name, COUNT(*) as record_count FROM dk_vehicle_info WHERE vehicle_id BETWEEN 1 AND 10
UNION ALL
SELECT '用户申请' as table_name, COUNT(*) as record_count FROM dk_user_application WHERE application_id BETWEEN 1 AND 8
UNION ALL
SELECT '钥匙分配' as table_name, COUNT(*) as record_count FROM dk_key_assignment WHERE assignment_id BETWEEN 1 AND 8
UNION ALL
SELECT '蓝牙密钥' as table_name, COUNT(*) as record_count FROM dk_vehicle_bluetooth_keys WHERE user_id BETWEEN 1001 AND 1008;

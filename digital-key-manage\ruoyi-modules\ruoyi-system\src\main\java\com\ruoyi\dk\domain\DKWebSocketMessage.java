package com.ruoyi.dk.domain;

import com.ruoyi.common.core.web.domain.BaseEntity;
import org.apache.commons.lang3.builder.ToStringBuilder;

/**
 * 车辆数字钥匙分享相关webSocket通讯消息对象
 * 
 * <AUTHOR>
 * @date 2024-08-14
 */
public class DKWebSocketMessage extends BaseEntity
{

    /** 消息类型（0：普通通知  1：分享确认通知  2：已确认分享通知  3：已拒绝分享通知） */
    private Integer messageType;

    /** 消息内容 */
    private String message;

    /** 消息发送人ID */
    private Long senderId;


    /** 消息发送人姓名 */
    private String senderName;

    /** 消息接收人ID */
    private Long recipientId;

    public Integer getMessageType() {
        return messageType;
    }

    public void setMessageType(Integer messageType) {
        this.messageType = messageType;
    }

    public String getMessage() {
        return message;
    }

    public void setMessage(String message) {
        this.message = message;
    }

    public Long getSenderId() {
        return senderId;
    }

    public void setSenderId(Long senderId) {
        this.senderId = senderId;
    }

    public String getSenderName() {
        return senderName;
    }

    public void setSenderName(String senderName) {
        this.senderName = senderName;
    }

    public Long getRecipientId() {
        return recipientId;
    }

    public void setRecipientId(Long recipientId) {
        this.recipientId = recipientId;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this)
                .append("messageType", messageType)
                .append("message", message)
                .append("senderId", senderId)
                .append("senderName", senderName)
                .append("recipientId", recipientId)
                .toString();
    }
}

<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <style>
        :root {
            --primary-color: #2171c1;
            --primary-light: #e8f1fb;
            --secondary-color: #f5f5f5;
            --border-color: #e0e0e0;
            --text-color: #333;
            --light-text: #757575;
            --module-header-bg: #e6f3ff;
            --module-bg: #f0f8ff;
            --function-bg: #1e88e5;
            --function-text: white;
            --card-shadow: 0 2px 4px rgba(0,0,0,0.1);
            --module-hover: #dbeeff;
            --header-gradient: linear-gradient(to right, #145d9c, #1976d2);
        }
        
        * {
            box-sizing: border-box;
            margin: 0;
            padding: 0;
        }
        
        body {
            font-family: 'Microsoft YaHei', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background-color: #fafafa;
            color: var(--text-color);
            line-height: 1.6;
        }
        
        .architecture-container {
            max-width: 1200px;
            margin: 0 auto;
            background-color: white;
            box-shadow: 0 2px 10px rgba(0,0,0,0.08);
        }
        
        /* 顶部导航栏 */
        .navigation-bar {
            background: var(--header-gradient);
            color: white;
            display: flex;
            align-items: center;
            padding: 0 20px;
            height: 60px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        
        .logo {
            display: flex;
            align-items: center;
            gap: 10px;
            font-weight: bold;
            font-size: 1.2em;
            margin-right: 40px;
        }
        
        .nav-links {
            display: flex;
            gap: 30px;
            height: 100%;
        }
        
        .nav-link {
            display: flex;
            align-items: center;
            padding: 0 15px;
            height: 100%;
            opacity: 0.85;
            transition: opacity 0.2s;
            cursor: pointer;
        }
        
        .nav-link:hover {
            opacity: 1;
            background-color: rgba(255,255,255,0.1);
        }
        
        .nav-link i {
            margin-right: 8px;
        }
        
        .active-link {
            opacity: 1;
            border-bottom: 3px solid white;
        }
        
        /* 内容区域 */
        .content-area {
            padding: 20px;
        }
        
        /* 简介部分 */
        .architecture-intro {
            padding: 20px;
            background-color: var(--primary-light);
            border-radius: 6px;
            margin-bottom: 25px;
            font-size: 0.95em;
            color: #444;
        }
        
        /* 系统标题 */
        .section-header {
            margin-bottom: 20px;
            padding-bottom: 10px;
            border-bottom: 2px solid #eee;
            display: flex;
            align-items: center;
            gap: 12px;
        }
        
        .section-header i {
            color: var(--primary-color);
            font-size: 1.4em;
        }
        
        .section-header h2 {
            font-size: 1.3em;
            font-weight: 600;
            color: #444;
        }
        
        /* 系统模块容器 */
        .system-block {
            margin: 0 20px 25px;
        }
        
        /* 系统模块标题 */
        .system-title {
            background-color: var(--primary-color);
            color: white;
            padding: 10px 15px;
            border-radius: 4px;
            font-weight: bold;
            display: flex;
            align-items: center;
            gap: 10px;
            margin-bottom: 15px;
        }
        
        /* 模块组 */
        .module-group {
            display: flex;
            flex-wrap: wrap;
            gap: 15px;
            margin-bottom: 20px;
        }
        
        /* 模块 */
        .module {
            background-color: var(--module-bg);
            border-radius: 4px;
            flex: 1;
            min-width: 230px;
            padding: 10px;
            position: relative;
            transition: background-color 0.2s;
        }
        
        .module:hover {
            background-color: var(--module-hover);
        }
        
        .module h3 {
            text-align: center;
            padding: 6px 0;
            margin-bottom: 8px;
            font-size: 0.95em;
            border-bottom: 1px solid rgba(0,0,0,0.1);
        }
        
        /* 功能列表 */
        .function-list {
            display: flex;
            flex-wrap: wrap;
            gap: 5px;
        }
        
        .function {
            background-color: var(--function-bg);
            color: var(--function-text);
            padding: 5px 8px;
            border-radius: 3px;
            font-size: 0.85em;
            text-align: center;
            flex: 1;
            min-width: 100px;
        }
        
        /* 工具提示 */
        .tooltip {
            position: absolute;
            top: calc(100% + 5px);
            left: 50%;
            transform: translateX(-50%);
            width: 280px;
            background-color: #2c3e50;
            color: white;
            padding: 8px 12px;
            border-radius: 4px;
            font-size: 0.8em;
            z-index: 100;
            box-shadow: 0 2px 8px rgba(0,0,0,0.3);
            display: none;
            text-align: left;
        }
        
        .module:hover .tooltip {
            display: block;
        }
        
        .tooltip::before {
            content: "";
            position: absolute;
            top: -5px;
            left: 50%;
            transform: translateX(-50%);
            border-left: 6px solid transparent;
            border-right: 6px solid transparent;
            border-bottom: 6px solid #2c3e50;
        }
        
        /* 外部平台 */
        .external-systems {
            display: flex;
            justify-content: center;
            flex-wrap: wrap;
            gap: 20px;
            margin: 20px;
        }
        
        .external-system {
            background-color: #fef9e7;
            border: 1px solid #f9e79f;
            border-radius: 6px;
            padding: 15px;
            text-align: center;
            width: 180px;
        }
        
        .external-system i {
            font-size: 2em;
            color: #f39c12;
            margin-bottom: 8px;
        }
        
        /* 连接关系 */
        .connection-section {
            margin: 30px 20px;
            background-color: #f9f9f9;
            border-left: 4px solid var(--primary-color);
            padding: 15px;
            border-radius: 4px;
        }
        
        .connection-title {
            font-weight: bold;
            color: var(--primary-color);
            margin-bottom: 10px;
        }
        
        .connection-list {
            list-style-type: none;
        }
        
        .connection-list li {
            margin-bottom: 8px;
            padding-left: 20px;
            position: relative;
        }
        
        .connection-list li:before {
            content: "→";
            position: absolute;
            left: 0;
            color: var(--primary-color);
        }
        
        /* 底部 */
        .page-footer {
            text-align: right;
            padding: 10px 20px;
            color: var(--light-text);
            font-size: 0.85em;
            border-top: 1px solid var(--border-color);
        }
        
        /* 数据库容器 */
        .database-section {
            margin: 15px 0;
        }
        
        .database-title {
            font-weight: bold;
            margin-bottom: 10px;
            color: var(--primary-color);
        }
        
        .database-container {
            display: flex;
            flex-wrap: wrap;
            gap: 15px;
        }
        
        .database {
            background-color: #f5f5f5;
            border-radius: 4px;
            padding: 12px;
            text-align: center;
            flex: 1;
            min-width: 120px;
            box-shadow: var(--card-shadow);
        }
        
        .database i {
            font-size: 1.5em;
            margin-bottom: 5px;
            color: var(--primary-color);
        }
    </style>
</head>
<body>
    <div class="architecture-container">
        
        <!-- 简介部分 -->
        <div class="architecture-intro">
            数字钥匙系统由三大核心模块组成，彼此之间协同工作，实现高安全性的车辆控制和管理功能。
        </div>
        
        <!-- 系统架构标题 -->
        <div class="architecture-title">
            数字钥匙系统架构
        </div>
        
        <!-- 手机端 -->
        <div class="system-block">
            <div class="system-title">
                <i class="fas fa-mobile-alt"></i> 手机端 - Digital Key Mobile SDK
            </div>
            
            <div class="module-group">
                <div class="module">
                    <h3>钥匙管理模块</h3>
                    <div class="function-list">
                        <div class="function">钥匙管理</div>
                        <div class="function">权限控制</div>
                        <div class="function">生命周期管理</div>
                    </div>
                    <div class="tooltip">
                        管理用户的数字钥匙，包括添加、删除、共享等功能
                    </div>
                </div>
                
                <div class="module">
                    <h3>车辆控制模块</h3>
                    <div class="function-list">
                        <div class="function">车门控制</div>
                        <div class="function">远程启动</div>
                        <div class="function">车辆状态查询</div>
                    </div>
                    <div class="tooltip">
                        提供车辆控制功能，如开锁、关锁、启动等，提供多种车型控制接口
                    </div>
                </div>
                
                <div class="module">
                    <h3>蓝牙通信模块</h3>
                    <div class="function-list">
                        <div class="function">BLE 5.0连接</div>
                        <div class="function">通信加密</div>
                        <div class="function">稳定性保障</div>
                    </div>
                    <div class="tooltip">
                        负责与车端进行蓝牙通信，支持BLE 5.0协议，确保通信高效可靠
                    </div>
                </div>
                
                <div class="module">
                    <h3>安全存储模块</h3>
                    <div class="function-list">
                        <div class="function">TEE/SE安全存储</div>
                        <div class="function">钥匙材料保护</div>
                        <div class="function">安全区域隔离</div>
                    </div>
                    <div class="tooltip">
                        安全存储数字钥匙和相关密钥材料，利用TEE/SE技术实现硬件级安全
                    </div>
                </div>
            </div>
            
            <div class="module-group">
                <div class="module">
                    <h3>时间同步模块</h3>
                    <div class="function-list">
                        <div class="function">NTP时间同步</div>
                        <div class="function">防篡改验证</div>
                        <div class="function">可信时间戳</div>
                    </div>
                    <div class="tooltip">
                        提供精确时间基准；支持NTP/PTP时间同步协议；防篡改时间源验证；为安全验证提供可信时间戳
                    </div>
                </div>
                
                <div class="module">
                    <h3>暗号交换模块</h3>
                    <div class="function-list">
                        <div class="function">临时暗号生成</div>
                        <div class="function">密钥协商</div>
                        <div class="function">会话密钥更新</div>
                    </div>
                    <div class="tooltip">
                        负责与车端协商生成临时暗号；确保只有配对的双方能通信；支持对称与非对称密钥交换算法
                    </div>
                </div>
                
                <div class="module">
                    <h3>智能场景管理模块</h3>
                    <div class="function-list">
                        <div class="function">场景识别</div>
                        <div class="function">无感连接触发</div>
                        <div class="function">电量优化</div>
                    </div>
                    <div class="tooltip">
                        负责检测用户状态与环境；决定无感连接触发条件；优化电量与连接策略
                    </div>
                </div>
                
                <div class="module">
                    <h3>异常处理模块</h3>
                    <div class="function-list">
                        <div class="function">通信异常监控</div>
                        <div class="function">自动重连</div>
                        <div class="function">异常日志记录</div>
                    </div>
                    <div class="tooltip">
                        监控通信异常；处理认证失败；实现自动重连；记录异常日志；支持指数退避重连策略
                    </div>
                </div>
            </div>
        </div>
        
        <!-- 车端 -->
        <div class="system-block">
            <div class="system-title">
                <i class="fas fa-car"></i> 车端 - Digital Key Vehicle Module
            </div>
            
            <div class="module-group">
                <div class="module">
                    <h3>蓝牙通信模块</h3>
                    <div class="function-list">
                        <div class="function">BLE 5.0通信</div>
                        <div class="function">信号强度监测</div>
                        <div class="function">安全连接管理</div>
                    </div>
                    <div class="tooltip">
                        负责与手机端建立安全的蓝牙通信连接，支持BLE 5.0通信协议
                    </div>
                </div>
                
                <div class="module">
                    <h3>钥匙验证模块</h3>
                    <div class="function-list">
                        <div class="function">多因素认证</div>
                        <div class="function">权限验证</div>
                        <div class="function">防重放检查</div>
                    </div>
                    <div class="tooltip">
                        验证数字钥匙的有效性和权限，执行多因素安全验证
                    </div>
                </div>
                
                <div class="module">
                    <h3>指令执行模块</h3>
                    <div class="function-list">
                        <div class="function">控制指令执行</div>
                        <div class="function">操作日志记录</div>
                        <div class="function">状态反馈</div>
                    </div>
                    <div class="tooltip">
                        执行控制指令，如开锁、关锁、启动等，并记录操作日志
                    </div>
                </div>
                
                <div class="module">
                    <h3>安全存储模块</h3>
                    <div class="function-list">
                        <div class="function">密钥安全存储</div>
                        <div class="function">防篡改保护</div>
                        <div class="function">硬件安全区域</div>
                    </div>
                    <div class="tooltip">
                        安全存储密钥材料和相关配置，利用防篡改硬件安全区域
                    </div>
                </div>
            </div>
            
            <div class="module-group">
                <div class="module">
                    <h3>远程通信模块</h3>
                    <div class="function-list">
                        <div class="function">4G/5G通信</div>
                        <div class="function">云平台连接</div>
                        <div class="function">远程指令接收</div>
                    </div>
                    <div class="tooltip">
                        与云平台建立通信连接，接收远程控制指令，支持4G/5G通信
                    </div>
                </div>
                
                <div class="module">
                    <h3>时间同步模块</h3>
                    <div class="function-list">
                        <div class="function">PTP时间同步</div>
                        <div class="function">离线时钟补偿</div>
                        <div class="function">时间戳生成</div>
                    </div>
                    <div class="tooltip">
                        与云端时间服务器定期同步；提供车端精确时间基准；支持离线时的时钟漂移补偿
                    </div>
                </div>
                
                <div class="module">
                    <h3>暗号交换模块</h3>
                    <div class="function-list">
                        <div class="function">安全协商机制</div>
                        <div class="function">根密钥保护</div>
                        <div class="function">会话密钥管理</div>
                    </div>
                    <div class="tooltip">
                        负责与手机端协商生成临时暗号；确保只有配对的双方能通信；支持挑战-响应认证机制
                    </div>
                </div>
                
                <div class="module">
                    <h3>用户行为分析模块</h3>
                    <div class="function-list">
                        <div class="function">RSSI信号分析</div>
                        <div class="function">距离计算</div>
                        <div class="function">防中继保护</div>
                    </div>
                    <div class="tooltip">
                        分析RSSI信号强度；执行高精度距离计算；支持多节点定位；提供距离安全策略
                    </div>
                </div>
            </div>
            
            <div class="module-group">
                <div class="module">
                    <h3>异常处理模块</h3>
                    <div class="function-list">
                        <div class="function">异常连接监控</div>
                        <div class="function">安全策略执行</div>
                        <div class="function">安全事件记录</div>
                    </div>
                    <div class="tooltip">
                        处理异常连接请求；监控异常操作；执行安全措施；记录安全事件
                    </div>
                </div>
            </div>
        </div>
        
        <!-- 钥匙云平台 -->
        <div class="system-block">
            <div class="system-title">
                <i class="fas fa-cloud"></i> 钥匙云平台 - Digital Key Cloud Platform
            </div>
            
            <div class="module-group">
                <div class="module">
                    <h3>钥匙生命周期管理</h3>
                    <div class="function-list">
                        <div class="function">钥匙创建</div>
                        <div class="function">钥匙授权</div>
                        <div class="function">钥匙撤销</div>
                        <div class="function">临时授权</div>
                    </div>
                    <div class="tooltip">
                        负责钥匙的创建、授权和撤销；管理配对状态；监控异常使用情况；支持临时钥匙授权
                    </div>
                </div>
                
                <div class="module">
                    <h3>车辆关联服务</h3>
                    <div class="function-list">
                        <div class="function">VIN码验证</div>
                        <div class="function">车辆信息管理</div>
                        <div class="function">车辆绑定</div>
                    </div>
                    <div class="tooltip">
                        验证VIN码有效性；管理车辆信息；处理车辆绑定请求；查询车辆配置和功能
                    </div>
                </div>
                
                <div class="module">
                    <h3>安全认证中心</h3>
                    <div class="function-list">
                        <div class="function">身份验证</div>
                        <div class="function">授权管理</div>
                        <div class="function">安全事件处理</div>
                    </div>
                    <div class="tooltip">
                        负责身份验证；确保只有授权用户能使用钥匙；处理安全事件
                    </div>
                </div>
                
                <div class="module">
                    <h3>密钥管理系统</h3>
                    <div class="function-list">
                        <div class="function">密钥生成</div>
                        <div class="function">密钥分发</div>
                        <div class="function">密钥更新</div>
                        <div class="function">密钥撤销</div>
                    </div>
                    <div class="tooltip">
                        生成和管理各种密钥；确保密钥安全存储；提供暗号交换基础；监控密钥使用情况
                    </div>
                </div>
            </div>
            
            <div class="module-group">
                <div class="module">
                    <h3>时间服务器</h3>
                    <div class="function-list">
                        <div class="function">标准时间同步</div>
                        <div class="function">时间戳验证</div>
                        <div class="function">时间证明</div>
                    </div>
                    <div class="tooltip">
                        提供精确的标准时间服务，支持NTP和PTP时间同步协议，为系统提供可信时间戳
                    </div>
                </div>
                
                <div class="module">
                    <h3>统一接口服务</h3>
                    <div class="function-list">
                        <div class="function">标准API接口</div>
                        <div class="function">安全通信</div>
                        <div class="function">消息防篡改</div>
                    </div>
                    <div class="tooltip">
                        提供标准接口；建立安全通信通道；防止消息被篡改或重放
                    </div>
                </div>
                
                <div class="module">
                    <h3>安全通信通道</h3>
                    <div class="function-list">
                        <div class="function">加密通信</div>
                        <div class="function">防重放机制</div>
                        <div class="function">身份双向验证</div>
                    </div>
                    <div class="tooltip">
                        提供加密通信；防止消息被窃听或篡改；支持暗号更新；监控通信异常
                    </div>
                </div>
                
                <div class="module">
                    <h3>异常监控与处理</h3>
                    <div class="function-list">
                        <div class="function">异常登录监控</div>
                        <div class="function">安全风控</div>
                        <div class="function">数据脱敏</div>
                    </div>
                    <div class="tooltip">
                        监控异常登录；分析异常使用模式；实施安全风控；触发安全预警
                    </div>
                </div>
            </div>
            
            <!-- 数据存储和安全基础设施 -->
            <div class="database-section">
                <div class="database-title">数据存储</div>
                <div class="database-container">
                    <div class="database">
                        <i class="fas fa-database"></i>
                        <div>钥匙数据</div>
                    </div>
                    <div class="database">
                        <i class="fas fa-key"></i>
                        <div>密钥材料</div>
                    </div>
                    <div class="database">
                        <i class="fas fa-exchange-alt"></i>
                        <div>通信记录</div>
                    </div>
                    <div class="database">
                        <i class="fas fa-history"></i>
                        <div>操作日志</div>
                    </div>
                </div>
            </div>
            
            <div class="database-section">
                <div class="database-title">安全基础设施</div>
                <div class="database-container">
                    <div class="database">
                        <i class="fas fa-lock"></i>
                        <div>密码机(HSM)</div>
                    </div>
                    <div class="database">
                        <i class="fas fa-certificate"></i>
                        <div>证书系统(PKI/CA)</div>
                    </div>
                    <div class="database">
                        <i class="fas fa-hdd"></i>
                        <div>密钥备份系统</div>
                    </div>
                    <div class="database">
                        <i class="fas fa-shield-alt"></i>
                        <div>安全监控</div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- 外部平台 -->
        <div class="external-systems">
            <div class="external-system">
                <i class="fas fa-car"></i>
                <div>车辆厂商服务</div>
                <div>Vehicle OEM Server</div>
            </div>
            <div class="external-system">
                <i class="fas fa-mobile-alt"></i>
                <div>终端厂商服务</div>
                <div>Device OEM Server</div>
            </div>
            <div class="external-system">
                <i class="fas fa-plug"></i>
                <div>第三方开放服务</div>
            </div>
        </div>
        
        <!-- 连接关系 -->
        <div class="connection-section">
            <div class="connection-title">核心连接关系说明</div>
            <ul class="connection-list">
                <li><strong>手机端与车端</strong>：通过BLE 5.0安全通道实现基于ECDHE的会话协商和双向认证</li>
                <li><strong>云平台与车端</strong>：通过4G/5G加密通道交换车辆状态和远程指令，支持断点续传</li>
                <li><strong>云平台与手机端</strong>：通过mTLS建立安全通道，实现钥匙信息同步和授权</li>
                <li><strong>时间同步机制</strong>：云平台时间服务器为手机端和车端提供精确时间同步，防篡改验证</li>
                <li><strong>暗号交换机制</strong>：手机端与车端执行基于NIST SP 800-56A的密钥协商，支持前向安全性</li>
                <li><strong>统一接口与外部平台</strong>：通过OAuth 2.0和JWT令牌建立身份联盟，支持跨域授权</li>
            </ul>
        </div>
        
        <!-- 页脚 -->
        <div class="page-footer">
            数字钥匙系统架构图 v1.0
        </div>
    </div>
</body>
</html> 
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>钥匙云平台</title>
    <!-- 引入html2canvas库 -->
    <script src="https://html2canvas.hertzen.com/dist/html2canvas.min.js"></script>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
            font-family: 'PingFang SC', 'Microsoft YaHei', sans-serif;
        }
        
        body {
            background: #f0f0f0;
            display: flex;
            justify-content: center;
            align-items: center;
            min-height: 100vh;
            color: #333;
            padding: 20px;
        }
        
        /* PPT边框指示器 */
        .ppt-container {
            position: relative;
            width: 1920px; /* 标准PPT宽度 */
            height: 1080px; /* 标准PPT高度 */
            box-shadow: 0 0 30px rgba(0, 0, 0, 0.2);
            border: 2px dashed #ccc;
            background-image: linear-gradient(45deg, #ccc 25%, transparent 25%), 
                              linear-gradient(-45deg, #ccc 25%, transparent 25%), 
                              linear-gradient(45deg, transparent 75%, #ccc 75%), 
                              linear-gradient(-45deg, transparent 75%, #ccc 75%);
            background-size: 20px 20px;
            background-position: 0 0, 0 10px, 10px -10px, -10px 0px;
        }
        
        .ppt-label {
            position: absolute;
            top: -40px;
            left: 0;
            background-color: #555;
            color: white;
            padding: 5px 15px;
            border-radius: 4px;
            font-size: 16px;
        }
        
        /* 复制按钮 */
        .copy-button {
            position: absolute;
            top: 20px;
            right: 20px;
            background: linear-gradient(135deg, #3498db, #2980b9);
            color: white;
            border: none;
            padding: 12px 25px;
            border-radius: 30px;
            font-size: 18px;
            cursor: pointer;
            z-index: 1000;
            box-shadow: 0 4px 10px rgba(0, 0, 0, 0.2);
            display: flex;
            align-items: center;
            gap: 10px;
            transition: all 0.3s ease;
        }
        
        /* 下载按钮 */
        .download-button {
            position: absolute;
            top: 20px;
            right: 230px;
            background: linear-gradient(135deg, #2ecc71, #27ae60);
            color: white;
            border: none;
            padding: 12px 25px;
            border-radius: 30px;
            font-size: 18px;
            cursor: pointer;
            z-index: 1000;
            box-shadow: 0 4px 10px rgba(0, 0, 0, 0.2);
            display: flex;
            align-items: center;
            gap: 10px;
            transition: all 0.3s ease;
        }
        
        .copy-button:hover, .download-button:hover {
            transform: translateY(-3px);
            box-shadow: 0 6px 15px rgba(0, 0, 0, 0.25);
        }
        
        .copy-button:active, .download-button:active {
            transform: translateY(0);
        }
        
        .copy-icon {
            width: 20px;
            height: 20px;
            background-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="white"><path d="M19,21H8V7H19M19,5H8A2,2 0 0,0 6,7V21A2,2 0 0,0 8,23H19A2,2 0 0,0 21,21V7A2,2 0 0,0 19,5M16,1H4A2,2 0 0,0 2,3V17H4V3H16V1Z" /></svg>');
            background-size: contain;
            background-repeat: no-repeat;
        }
        
        .download-icon {
            width: 20px;
            height: 20px;
            background-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="white"><path d="M5,20H19V18H5M19,9H15V3H9V9H5L12,16L19,9Z" /></svg>');
            background-size: contain;
            background-repeat: no-repeat;
        }
        
        /* 清晰度选择器样式 */
        .resolution-selector {
            position: absolute;
            top: 70px;
            right: 230px;
            display: flex;
            align-items: center;
            background: rgba(255, 255, 255, 0.9);
            padding: 8px 15px;
            border-radius: 30px;
            box-shadow: 0 4px 10px rgba(0, 0, 0, 0.1);
            z-index: 1000;
        }
        
        .resolution-label {
            margin-right: 10px;
            font-size: 14px;
            color: #555;
        }
        
        .resolution-btn {
            border: none;
            background: #f0f0f0;
            padding: 5px 10px;
            margin: 0 3px;
            border-radius: 15px;
            font-size: 14px;
            cursor: pointer;
            transition: all 0.2s ease;
            color: #555;
        }
        
        .resolution-btn:hover {
            background: #e0e0e0;
        }
        
        .resolution-btn.active {
            background: linear-gradient(135deg, #3498db, #2980b9);
            color: white;
        }
        
        /* 新设计开始 - 完全透明背景 */
        .slide {
            width: 100%;
            height: 100%;
            background-color: transparent; /* 完全透明背景 */
            padding: 60px;
            display: flex;
            flex-direction: column;
            overflow: hidden;
            position: relative;
        }
        
        /* 顶部标题区域 - 修改了标题样式，删除背景渐变 */
        .header {
            text-align: center;
            margin-bottom: 40px;
            position: relative;
            z-index: 10;
        }
        
        .header h1 {
            font-size: 64px;
            color: #2c3e50; /* 使用纯色而非渐变 */
            margin-bottom: 15px;
            position: relative;
            display: inline-block;
            font-weight: 700;
            background: none; /* 移除背景 */
        }
        
        .header .subtitle {
            font-size: 28px;
            color: #7f8c8d;
            font-weight: 400;
            max-width: 800px;
            margin: 0 auto;
        }
        
        /* 特色功能网格布局 - 修改为5列 */
        .features-grid {
            display: grid;
            grid-template-columns: repeat(5, 1fr);
            gap: 20px;
            padding: 0 40px;
            margin-top: 20px;
            position: relative;
            z-index: 10;
        }
        
        /* 特色功能卡片 - 适应更小的卡片 */
        .feature-card {
            background: white;
            border-radius: 16px;
            padding: 25px;
            display: flex;
            flex-direction: column;
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.05);
            height: 100%;
        }
        
        .feature-card:hover {
            transform: translateY(-10px);
            box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
        }
        
        .feature-icon {
            width: 70px;
            height: 70px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-bottom: 20px;
            position: relative;
        }
        
        .feature-icon::before {
            content: '';
            position: absolute;
            width: 70px;
            height: 70px;
            border-radius: 50%;
            background: linear-gradient(135deg, rgba(52, 152, 219, 0.2), rgba(41, 128, 185, 0.2));
            z-index: 1;
        }
        
        .feature-icon svg {
            width: 40px;
            height: 40px;
            z-index: 2;
        }
        
        /* 调整标题和描述字体大小 */
        .feature-title {
            font-size: 20px;
            font-weight: 600;
            color: #2c3e50;
            margin-bottom: 12px;
            position: relative;
        }
        
        .feature-title::after {
            content: '';
            position: absolute;
            bottom: -8px;
            left: 0;
            width: 40px;
            height: 3px;
            background: linear-gradient(90deg, #3498db, transparent);
            border-radius: 3px;
        }
        
        .feature-desc {
            font-size: 14px;
            color: #7f8c8d;
            line-height: 1.5;
            flex-grow: 1;
        }
        
        /* 自定义每个卡片的图标和颜色 */
        .feature-1 .feature-icon::before { background: linear-gradient(135deg, rgba(52, 152, 219, 0.2), rgba(41, 128, 185, 0.2)); }
        .feature-2 .feature-icon::before { background: linear-gradient(135deg, rgba(46, 204, 113, 0.2), rgba(39, 174, 96, 0.2)); }
        .feature-3 .feature-icon::before { background: linear-gradient(135deg, rgba(155, 89, 182, 0.2), rgba(142, 68, 173, 0.2)); }
        .feature-4 .feature-icon::before { background: linear-gradient(135deg, rgba(230, 126, 34, 0.2), rgba(211, 84, 0, 0.2)); }
        .feature-5 .feature-icon::before { background: linear-gradient(135deg, rgba(52, 73, 94, 0.2), rgba(44, 62, 80, 0.2)); }
        .feature-6 .feature-icon::before { background: linear-gradient(135deg, rgba(241, 196, 15, 0.2), rgba(243, 156, 18, 0.2)); }
        .feature-7 .feature-icon::before { background: linear-gradient(135deg, rgba(231, 76, 60, 0.2), rgba(192, 57, 43, 0.2)); }
        .feature-8 .feature-icon::before { background: linear-gradient(135deg, rgba(26, 188, 156, 0.2), rgba(22, 160, 133, 0.2)); }
        .feature-9 .feature-icon::before { background: linear-gradient(135deg, rgba(142, 68, 173, 0.2), rgba(125, 60, 152, 0.2)); }
        .feature-10 .feature-icon::before { background: linear-gradient(135deg, rgba(22, 160, 133, 0.2), rgba(19, 141, 117, 0.2)); }
        
        /* 卡片顶部颜色条 */
        .feature-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 5px;
            background: linear-gradient(90deg, transparent, #3498db, transparent);
        }
        
        .feature-1::before { background: linear-gradient(90deg, transparent, #3498db, transparent); }
        .feature-2::before { background: linear-gradient(90deg, transparent, #2ecc71, transparent); }
        .feature-3::before { background: linear-gradient(90deg, transparent, #9b59b6, transparent); }
        .feature-4::before { background: linear-gradient(90deg, transparent, #e67e22, transparent); }
        .feature-5::before { background: linear-gradient(90deg, transparent, #34495e, transparent); }
        .feature-6::before { background: linear-gradient(90deg, transparent, #f1c40f, transparent); }
        .feature-7::before { background: linear-gradient(90deg, transparent, #e74c3c, transparent); }
        .feature-8::before { background: linear-gradient(90deg, transparent, #1abc9c, transparent); }
        .feature-9::before { background: linear-gradient(90deg, transparent, #8e44ad, transparent); }
        .feature-10::before { background: linear-gradient(90deg, transparent, #16a085, transparent); }
        
        /* 底部装饰和版权 */
        .footer {
            position: absolute;
            bottom: 20px;
            width: 100%;
            text-align: center;
            font-size: 14px;
            color: #95a5a6;
            left: 0;
            z-index: 10;
        }
        
        /* 侧边装饰 */
        .side-decoration {
            position: absolute;
            bottom: 40px;
            right: 40px;
            display: flex;
            gap: 15px;
            z-index: 10;
        }
        
        .side-icon {
            width: 50px;
            height: 50px;
            opacity: 0.7;
            transition: all 0.3s ease;
        }
        
        .side-icon:hover {
            transform: scale(1.1);
            opacity: 1;
        }
        
        /* 消息提示 */
        .toast {
            position: fixed;
            top: 50px;
            left: 50%;
            transform: translateX(-50%);
            background-color: rgba(0, 0, 0, 0.8);
            color: white;
            padding: 12px 25px;
            border-radius: 30px;
            font-size: 16px;
            z-index: 2000;
            display: none;
            animation: fadeInOut 2s ease;
        }
        
        @keyframes fadeInOut {
            0% { opacity: 0; }
            20% { opacity: 1; }
            80% { opacity: 1; }
            100% { opacity: 0; }
        }
    </style>
</head>
<body>
    <div class="ppt-container" id="pptContainer">
        <div class="ppt-label">PPT尺寸(1920×1080) - 透明背景</div>
        <button class="copy-button" id="copyButton">
            <span class="copy-icon"></span>复制为PPT图片
        </button>
        <button class="download-button" id="downloadButton">
            <span class="download-icon"></span>下载透明PNG
        </button>
        
        <!-- 添加清晰度选择按钮组 -->
        <div class="resolution-selector" id="resolutionSelector">
            <span class="resolution-label">清晰度:</span>
            <button class="resolution-btn" data-scale="1">标准</button>
            <button class="resolution-btn active" data-scale="2">高清</button>
            <button class="resolution-btn" data-scale="3">超清</button>
        </div>
        
        <div class="slide" id="slide">
            <!-- 顶部标题 -->
            <div class="header">
                <h1>车载智能钥匙系统</h1>
                <div class="subtitle">安全、高效的车端数字钥匙核心功能组件</div>
            </div>
            
            <!-- 功能展示网格 -->
            <div class="features-grid" style="grid-template-columns: repeat(5, 1fr);">
                <!-- 功能1: 蓝牙通信模块 -->
                <div class="feature-card feature-1">
                    <div class="feature-icon">
                        <svg viewBox="0 0 24 24" fill="#3498db">
                            <path d="M14.88,16.29L13,18.17V14.41M13,5.83L14.88,7.71L13,9.58M17.71,7.71L12,2H11V9.58L6.41,5L5,6.41L10.59,12L5,17.58L6.41,19L11,14.41V22H12L17.71,16.29L13.41,12L17.71,7.71Z" />
                        </svg>
                    </div>
                    <div class="feature-title">蓝牙通信</div>
                    <div class="feature-desc">发送加密广播信号，接收并处理连接请求，建立安全的近场通信通道</div>
                </div>
                
                <!-- 功能2: 钥匙验证模块 -->
                <div class="feature-card feature-2">
                    <div class="feature-icon">
                        <svg viewBox="0 0 24 24" fill="#2ecc71">
                            <path d="M12,1L3,5V11C3,16.55 6.84,21.74 12,23C17.16,21.74 21,16.55 21,11V5L12,1M12,5A3,3 0 0,1 15,8A3,3 0 0,1 12,11A3,3 0 0,1 9,8A3,3 0 0,1 12,5M17.13,17C15.92,18.85 14.11,20.24 12,20.92C9.89,20.24 8.08,18.85 6.87,17C6.53,16.5 6.24,16 6,15.47C6,13.82 8.71,12.47 12,12.47C15.29,12.47 18,13.79 18,15.47C17.76,16 17.47,16.5 17.13,17Z" />
                        </svg>
                    </div>
                    <div class="feature-title">钥匙验证</div>
                    <div class="feature-desc">验证手机身份，确保只有授权设备能够连接，防止未授权访问</div>
                </div>
                
                <!-- 功能3: 指令执行模块 -->
                <div class="feature-card feature-3">
                    <div class="feature-icon">
                        <svg viewBox="0 0 24 24" fill="#9b59b6">
                            <path d="M20,19V7H4V19H20M20,3A2,2 0 0,1 22,5V19A2,2 0 0,1 20,21H4A2,2 0 0,1 2,19V5A2,2 0 0,1 4,3H20M13,17V15H18V17H13M9.58,13L5.57,9H8.4L11.7,12.3C12.09,12.69 12.09,13.33 11.7,13.72L8.42,17H5.59L9.58,13Z" />
                        </svg>
                    </div>
                    <div class="feature-title">指令执行</div>
                    <div class="feature-desc">接收并执行来自手机的控制指令，反馈执行结果，确保操作可靠性</div>
                </div>
                
                <!-- 功能4: 安全存储模块 -->
                <div class="feature-card feature-4">
                    <div class="feature-icon">
                        <svg viewBox="0 0 24 24" fill="#e67e22">
                            <path d="M7,14A2,2 0 0,1 5,12A2,2 0 0,1 7,10A2,2 0 0,1 9,12A2,2 0 0,1 7,14M12.65,10C11.83,7.67 9.61,6 7,6A6,6 0 0,0 1,12A6,6 0 0,0 7,18C9.61,18 11.83,16.33 12.65,14H17V18H21V14H23V10H12.65Z" />
                        </svg>
                    </div>
                    <div class="feature-title">安全存储</div>
                    <div class="feature-desc">在安全区域存储根密钥、会话密钥和授权设备信息，确保密钥信息不被窃取</div>
                </div>
                
                <!-- 功能5: 远程通信模块 -->
                <div class="feature-card feature-5">
                    <div class="feature-icon">
                        <svg viewBox="0 0 24 24" fill="#34495e">
                            <path d="M2,3H22C23.05,3 24,3.95 24,5V19C24,20.05 23.05,21 22,21H2C0.95,21 0,20.05 0,19V5C0,3.95 0.95,3 2,3M14,6V7H22V6H14M14,8V9H21.5L22,9V8H14M14,10V11H21V10H14M8,13.91C6,13.91 2,15 2,17V18H14V17C14,15 10,13.91 8,13.91M8,6A3,3 0 0,0 5,9A3,3 0 0,0 8,12A3,3 0 0,0 11,9A3,3 0 0,0 8,6Z" />
                        </svg>
                    </div>
                    <div class="feature-title">远程通信</div>
                    <div class="feature-desc">与云平台建立安全连接，同步状态和配置，实现远程管理与更新</div>
                </div>
                
                <!-- 功能6: 时间同步模块 -->
                <div class="feature-card feature-6">
                    <div class="feature-icon">
                        <svg viewBox="0 0 24 24" fill="#f1c40f">
                            <path d="M12,20A8,8 0 0,0 20,12A8,8 0 0,0 12,4A8,8 0 0,0 4,12A8,8 0 0,0 12,20M12,2A10,10 0 0,1 22,12A10,10 0 0,1 12,22C6.47,22 2,17.5 2,12A10,10 0 0,1 12,2M12.5,7V12.25L17,14.92L16.25,16.15L11,13V7H12.5Z" />
                        </svg>
                    </div>
                    <div class="feature-title">时间同步</div>
                    <div class="feature-desc">与云端时间服务器同步，提供精确时间基准，防止时间偏移导致的安全问题</div>
                </div>
                
                <!-- 功能7: 暗号交换模块 -->
                <div class="feature-card feature-7">
                    <div class="feature-icon">
                        <svg viewBox="0 0 24 24" fill="#e74c3c">
                            <path d="M12,17A2,2 0 0,0 14,15C14,13.89 13.1,13 12,13A2,2 0 0,0 10,15A2,2 0 0,0 12,17M18,8A2,2 0 0,1 20,10V20A2,2 0 0,1 18,22H6A2,2 0 0,1 4,20V10C4,8.89 4.9,8 6,8H7V6A5,5 0 0,1 12,1A5,5 0 0,1 17,6V8H18M12,3A3,3 0 0,0 9,6V8H15V6A3,3 0 0,0 12,3Z" />
                        </svg>
                    </div>
                    <div class="feature-title">暗号交换</div>
                    <div class="feature-desc">与手机端协商生成临时暗号，确保只有配对的双方能通信，增强安全性</div>
                </div>
                
                <!-- 功能8: 用户行为分析模块 -->
                <div class="feature-card feature-8">
                    <div class="feature-icon">
                        <svg viewBox="0 0 24 24" fill="#1abc9c">
                            <path d="M12,12H19C18.47,16.11 15.72,19.78 12,20.92V12H5V6.3L12,3.19M12,1L3,5V11C3,16.55 6.84,21.73 12,23C17.16,21.73 21,16.55 21,11V5L12,1Z" />
                        </svg>
                    </div>
                    <div class="feature-title">控车核心算法</div>
                    <div class="feature-desc">基于RSSI信号强度计算与手机的距离，支持距离感知操作，提升用户体验</div>
                </div>
                
                <!-- 功能9: 异常处理模块 -->
                <div class="feature-card feature-9">
                    <div class="feature-icon">
                        <svg viewBox="0 0 24 24" fill="#8e44ad">
                            <path d="M12,2L1,21H23M12,6L19.53,19H4.47M11,10V14H13V10M11,16V18H13V16" />
                        </svg>
                    </div>
                    <div class="feature-title">异常处理</div>
                    <div class="feature-desc">处理异常连接请求，监控异常操作，执行安全措施，保障系统安全</div>
                </div>
                
                <!-- 功能10: 标定配置模块 -->
                <div class="feature-card feature-10">
                    <div class="feature-icon">
                        <svg viewBox="0 0 24 24" fill="#16a085">
                            <path d="M12,15.5A3.5,3.5 0 0,1 8.5,12A3.5,3.5 0 0,1 12,8.5A3.5,3.5 0 0,1 15.5,12A3.5,3.5 0 0,1 12,15.5M19.43,12.97C19.47,12.65 19.5,12.33 19.5,12C19.5,11.67 19.47,11.34 19.43,11L21.54,9.37C21.73,9.22 21.78,8.95 21.66,8.73L19.66,5.27C19.54,5.05 19.27,4.96 19.05,5.05L16.56,6.05C16.04,5.66 15.5,5.32 14.87,5.07L14.5,2.42C14.46,2.18 14.25,2 14,2H10C9.75,2 9.54,2.18 9.5,2.42L9.13,5.07C8.5,5.32 7.96,5.66 7.44,6.05L4.95,5.05C4.73,4.96 4.46,5.05 4.34,5.27L2.34,8.73C2.21,8.95 2.27,9.22 2.46,9.37L4.57,11C4.53,11.34 4.5,11.67 4.5,12C4.5,12.33 4.53,12.65 4.57,12.97L2.46,14.63C2.27,14.78 2.21,15.05 2.34,15.27L4.34,18.73C4.46,18.95 4.73,19.03 4.95,18.95L7.44,17.94C7.96,18.34 8.5,18.68 9.13,18.93L9.5,21.58C9.54,21.82 9.75,22 10,22H14C14.25,22 14.46,21.82 14.5,21.58L14.87,18.93C15.5,18.67 16.04,18.34 16.56,17.94L19.05,18.95C19.27,19.03 19.54,18.95 19.66,18.73L21.66,15.27C21.78,15.05 21.73,14.78 21.54,14.63L19.43,12.97Z" />
                        </svg>
                    </div>
                    <div class="feature-title">标定配置</div>
                    <div class="feature-desc">存储和管理RSSI信号标定数据，支持不同环境下的精确距离计算和适配</div>
                </div>
            </div>
            
            
            <!-- 侧边装饰图标 -->
            <div class="side-decoration">
                <svg class="side-icon" viewBox="0 0 24 24" fill="#3498db">
                    <path d="M12,17A2,2 0 0,0 14,15C14,13.89 13.1,13 12,13A2,2 0 0,0 10,15A2,2 0 0,0 12,17M18,8A2,2 0 0,1 20,10V20A2,2 0 0,1 18,22H6A2,2 0 0,1 4,20V10C4,8.89 4.9,8 6,8H7V6A5,5 0 0,1 12,1A5,5 0 0,1 17,6V8H18M12,3A3,3 0 0,0 9,6V8H15V6A3,3 0 0,0 12,3Z" />
                </svg>
                <svg class="side-icon" viewBox="0 0 24 24" fill="#2ecc71">
                    <path d="M21,11C21,16.55 17.16,21.74 12,23C6.84,21.74 3,16.55 3,11V5L12,1L21,5V11M12,21C15.75,20 19,15.54 19,11.22V6.3L12,3.18L5,6.3V11.22C5,15.54 8.25,20 12,21Z" />
                </svg>
                <svg class="side-icon" viewBox="0 0 24 24" fill="#e67e22">
                    <path d="M12,12H19C18.47,16.11 15.72,19.78 12,20.92V12H5V6.3L12,3.19M12,1L3,5V11C3,16.55 6.84,21.73 12,23C17.16,21.73 21,16.55 21,11V5L12,1Z" />
                </svg>
            </div>
        </div>
    </div>
    
    <div class="toast" id="toast">操作成功！</div>
    
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const copyButton = document.getElementById('copyButton');
            const downloadButton = document.getElementById('downloadButton');
            const toast = document.getElementById('toast');
            const slide = document.getElementById('slide');
            const resolutionBtns = document.querySelectorAll('.resolution-btn');
            
            // 默认清晰度为2x (高清)
            let currentScale = 2;
            
            // 清晰度按钮点击事件
            resolutionBtns.forEach(btn => {
                btn.addEventListener('click', function() {
                    // 移除所有按钮的active类
                    resolutionBtns.forEach(b => b.classList.remove('active'));
                    // 给当前按钮添加active类
                    this.classList.add('active');
                    // 更新当前清晰度
                    currentScale = parseFloat(this.getAttribute('data-scale'));
                    
                    // 显示提示
                    showToast(`清晰度已设置为${this.textContent}模式`);
                });
            });
            
            // 显示提示消息
            function showToast(message) {
                toast.textContent = message;
                toast.style.display = 'block';
                setTimeout(function() {
                    toast.style.display = 'none';
                }, 2000);
            }
            
            // 生成透明背景图片
            function generateImage() {
                return html2canvas(slide, {
                    scale: currentScale, // 使用用户选择的清晰度
                    useCORS: true,
                    allowTaint: true,
                    backgroundColor: null // 关键：设置为null以支持透明背景
                });
            }
            
            // 复制按钮功能
            copyButton.addEventListener('click', function() {
                showToast("正在处理图片，请稍候...");
                
                generateImage().then(function(canvas) {
                    try {
                        // 使用现代剪贴板API
                        canvas.toBlob(function(blob) {
                            try {
                                const item = new ClipboardItem({ 'image/png': blob });
                                navigator.clipboard.write([item]).then(function() {
                                    showToast("已复制到剪贴板！现在可以粘贴到PPT中");
                                }).catch(function(err) {
                                    showToast("自动复制失败，请使用下载按钮获取图片");
                                    console.error("剪贴板API错误:", err);
                                });
                            } catch (e) {
                                showToast("您的浏览器不支持复制图片，请使用下载按钮");
                                console.error("ClipboardItem错误:", e);
                            }
                        });
                    } catch (e) {
                        showToast("复制功能不可用，请使用下载图片功能");
                        console.error("toBlob错误:", e);
                    }
                }).catch(function(err) {
                    showToast("生成图片失败，请尝试下载或截图");
                    console.error("html2canvas错误:", err);
                });
            });
            
            // 下载按钮功能
            downloadButton.addEventListener('click', function() {
                showToast(`正在生成${currentScale}x清晰度透明PNG图片，请稍候...`);
                
                generateImage().then(function(canvas) {
                    // 将Canvas转换为PNG并下载
                    const imageUrl = canvas.toDataURL('image/png');
                    const link = document.createElement('a');
                    link.download = `钥匙云平台-透明-${currentScale}x清晰度.png`;
                    link.href = imageUrl;
                    link.click();
                    
                    showToast(`${currentScale}x清晰度透明背景PNG已下载`);
                }).catch(function(err) {
                    showToast("生成图片失败，请尝试截图");
                    console.error("Error:", err);
                });
            });
        });
    </script>
</body>
</html> 
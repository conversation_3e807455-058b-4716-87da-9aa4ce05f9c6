package com.ruoyi.framework.tcp;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import javax.annotation.PreDestroy;
import java.io.IOException;
import java.net.ServerSocket;
import java.net.Socket;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;

/**
 * TCP服务器
 * 用于接收TBOX客户端连接，处理车辆解闭锁指令和状态反馈
 * 
 * <AUTHOR>
 */
@Component
public class TcpServer {
    
    private static final Logger LOGGER = LoggerFactory.getLogger(TcpServer.class);
    
    private final int port;
    private final boolean enabled;
    private ServerSocket serverSocket;
    private ExecutorService executorService;
    private volatile boolean running = false;
    
    public TcpServer(int port, boolean enabled) {
        this.port = port;
        this.enabled = enabled;
    }
    
    @PostConstruct
    public void start() {
        if (!enabled) {
            LOGGER.info("TCP服务器已禁用");
            return;
        }
        
        try {
            serverSocket = new ServerSocket(port);
            executorService = Executors.newCachedThreadPool();
            running = true;
            
            LOGGER.info("TCP服务器启动成功，监听端口: {}", port);
            
            // 在新线程中接受连接
            executorService.submit(this::acceptConnections);
            
        } catch (IOException e) {
            LOGGER.error("TCP服务器启动失败: {}", e.getMessage(), e);
        }
    }
    
    private void acceptConnections() {
        while (running && !serverSocket.isClosed()) {
            try {
                Socket clientSocket = serverSocket.accept();
                LOGGER.info("新的TBOX客户端连接: {}", clientSocket.getRemoteSocketAddress());
                
                // 为每个客户端创建处理线程
                executorService.submit(new TcpClientHandler(clientSocket));
                
            } catch (IOException e) {
                if (running) {
                    LOGGER.error("接受客户端连接时发生错误: {}", e.getMessage(), e);
                }
            }
        }
    }
    
    @PreDestroy
    public void stop() {
        running = false;
        
        try {
            if (serverSocket != null && !serverSocket.isClosed()) {
                serverSocket.close();
                LOGGER.info("TCP服务器已关闭");
            }
            
            if (executorService != null) {
                executorService.shutdown();
                LOGGER.info("TCP线程池已关闭");
            }
        } catch (IOException e) {
            LOGGER.error("关闭TCP服务器时发生错误: {}", e.getMessage(), e);
        }
    }
    
    public boolean isRunning() {
        return running;
    }
    
    public int getPort() {
        return port;
    }
}

<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="申请人" prop="realName">
        <el-input
          v-model="queryParams.realName"
          placeholder="请输入申请人姓名"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="手机号" prop="phone">
        <el-input
          v-model="queryParams.phone"
          placeholder="请输入手机号"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="申请状态" prop="status">
        <el-select v-model="queryParams.status" placeholder="请选择申请状态" clearable>
          <el-option
            v-for="item in applicationStatusOptions"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="el-icon-plus"
          size="mini"
          @click="handleAdd"
          v-hasPermi="['dk:application:add']"
        >新增</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="success"
          plain
          icon="el-icon-edit"
          size="mini"
          :disabled="single"
          @click="handleUpdate"
          v-hasPermi="['dk:application:edit']"
        >修改</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="danger"
          plain
          icon="el-icon-delete"
          size="mini"
          :disabled="multiple"
          @click="handleDelete"
          v-hasPermi="['dk:application:remove']"
        >删除</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="el-icon-download"
          size="mini"
          @click="handleExport"
          v-hasPermi="['dk:application:export']"
        >导出</el-button>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="applicationList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="申请单号" align="center" prop="applicationNo" />
      <el-table-column label="申请人" align="center" prop="realName" />
      <el-table-column label="手机号" align="center" prop="phone" />
      <el-table-column label="身份证号" align="center" prop="idCard" />
      <el-table-column label="申请状态" align="center" prop="status">
        <template slot-scope="scope">
          <el-tag :type="getApplicationStatusType(scope.row.status)" size="mini">
            {{ getApplicationStatusLabel(scope.row.status) }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column label="申请时间" align="center" prop="createTime" width="180">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.createTime, '{y}-{m}-{d} {h}:{i}:{s}') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="审核人" align="center" prop="auditUserName" />
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width" width="280">
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="text"
            icon="el-icon-view"
            @click="handleViewDetail(scope.row)"
          >查看详情</el-button>
          <el-button
            size="mini"
            type="text"
            icon="el-icon-edit"
            @click="handleUpdate(scope.row)"
            v-hasPermi="['dk:application:edit']"
          >修改</el-button>
          <el-button
            v-if="scope.row.status === '0'"
            size="mini"
            type="text"
            icon="el-icon-check"
            @click="handleAudit(scope.row)"
            v-hasPermi="['dk:application:audit']"
          >审核</el-button>
          <el-button
            size="mini"
            type="text"
            icon="el-icon-delete"
            @click="handleDelete(scope.row)"
            v-hasPermi="['dk:application:remove']"
          >删除</el-button>
        </template>
      </el-table-column>
    </el-table>
    
    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加或修改用户申请对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="500px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="80px">
        <el-form-item label="申请人" prop="realName">
          <el-input v-model="form.realName" placeholder="请输入申请人姓名" />
        </el-form-item>
        <el-form-item label="手机号" prop="phone">
          <el-input v-model="form.phone" placeholder="请输入手机号" />
        </el-form-item>
        <el-form-item label="身份证号" prop="idCard">
          <el-input v-model="form.idCard" placeholder="请输入身份证号" />
        </el-form-item>
        <el-form-item label="驾驶证号" prop="drivingLicense">
          <el-input v-model="form.drivingLicense" placeholder="请输入驾驶证号" />
        </el-form-item>
        <el-form-item label="申请原因" prop="applicationReason">
          <el-input v-model="form.applicationReason" type="textarea" placeholder="请输入申请原因" />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>

    <!-- 审核对话框 -->
    <el-dialog title="申请审核" :visible.sync="auditOpen" width="500px" append-to-body>
      <el-form ref="auditForm" :model="auditForm" :rules="auditRules" label-width="80px">
        <el-form-item label="审核结果" prop="status">
          <el-radio-group v-model="auditForm.status">
            <el-radio label="1">审核通过</el-radio>
            <el-radio label="2">审核拒绝</el-radio>
            <el-radio label="3">补充资料</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="审核备注" prop="auditRemark">
          <el-input v-model="auditForm.auditRemark" type="textarea" placeholder="请输入审核备注" />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitAudit">确 定</el-button>
        <el-button @click="cancelAudit">取 消</el-button>
      </div>
    </el-dialog>

    <!-- 申请详情对话框 -->
    <el-dialog title="申请详情" :visible.sync="detailOpen" width="800px" append-to-body>
      <el-row :gutter="20">
        <!-- 基本信息 -->
        <el-col :span="12">
          <el-card class="box-card" shadow="never">
            <div slot="header" class="clearfix">
              <span>基本信息</span>
            </div>
            <div class="detail-item">
              <label>申请单号：</label>
              <span>{{ detailForm.applicationNo }}</span>
            </div>
            <div class="detail-item">
              <label>申请人：</label>
              <span>{{ detailForm.realName }}</span>
            </div>
            <div class="detail-item">
              <label>手机号：</label>
              <span>{{ detailForm.phone }}</span>
            </div>
            <div class="detail-item">
              <label>身份证号：</label>
              <span>{{ detailForm.idCard }}</span>
            </div>
            <div class="detail-item">
              <label>驾驶证号：</label>
              <span>{{ detailForm.drivingLicense || '未填写' }}</span>
            </div>
            <div class="detail-item">
              <label>申请状态：</label>
              <el-tag :type="getApplicationStatusType(detailForm.status)" size="mini">
                {{ getApplicationStatusLabel(detailForm.status) }}
              </el-tag>
            </div>
            <div class="detail-item">
              <label>申请时间：</label>
              <span>{{ parseTime(detailForm.createTime, '{y}-{m}-{d} {h}:{i}:{s}') }}</span>
            </div>
            <div class="detail-item">
              <label>申请原因：</label>
              <p>{{ detailForm.applicationReason }}</p>
            </div>
          </el-card>
        </el-col>

        <!-- 证件照片 -->
        <el-col :span="12">
          <el-card class="box-card" shadow="never">
            <div slot="header" class="clearfix">
              <span>证件照片</span>
            </div>

            <!-- 身份证正面 -->
            <div class="photo-item" v-if="detailForm.idCardFrontUrl">
              <label>身份证正面：</label>
              <div class="photo-container">
                <el-image
                  :src="detailForm.idCardFrontUrl"
                  :preview-src-list="[detailForm.idCardFrontUrl]"
                  fit="cover"
                  class="certificate-photo"
                >
                  <div slot="error" class="image-slot">
                    <i class="el-icon-picture-outline"></i>
                  </div>
                </el-image>
              </div>
            </div>

            <!-- 身份证背面 -->
            <div class="photo-item" v-if="detailForm.idCardBackUrl">
              <label>身份证背面：</label>
              <div class="photo-container">
                <el-image
                  :src="detailForm.idCardBackUrl"
                  :preview-src-list="[detailForm.idCardBackUrl]"
                  fit="cover"
                  class="certificate-photo"
                >
                  <div slot="error" class="image-slot">
                    <i class="el-icon-picture-outline"></i>
                  </div>
                </el-image>
              </div>
            </div>

            <!-- 驾驶证 -->
            <div class="photo-item" v-if="detailForm.drivingLicenseUrl">
              <label>驾驶证：</label>
              <div class="photo-container">
                <el-image
                  :src="detailForm.drivingLicenseUrl"
                  :preview-src-list="[detailForm.drivingLicenseUrl]"
                  fit="cover"
                  class="certificate-photo"
                >
                  <div slot="error" class="image-slot">
                    <i class="el-icon-picture-outline"></i>
                  </div>
                </el-image>
              </div>
            </div>

            <!-- 无照片提示 -->
            <div v-if="!detailForm.idCardFrontUrl && !detailForm.idCardBackUrl && !detailForm.drivingLicenseUrl" class="no-photo">
              <i class="el-icon-picture-outline"></i>
              <p>暂无证件照片</p>
            </div>
          </el-card>
        </el-col>
      </el-row>

      <div slot="footer" class="dialog-footer">
        <el-button @click="detailOpen = false">关 闭</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { listUserApplication, getUserApplication, delUserApplication, addUserApplication, updateUserApplication, auditUserApplication } from "@/api/dk/userApplication";
import { APPLICATION_STATUS, dictToArray } from "@/utils/dkDict";

export default {
  name: "UserApplication",
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 用户申请表格数据
      applicationList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 是否显示审核弹出层
      auditOpen: false,
      // 是否显示详情弹出层
      detailOpen: false,
      // 申请状态选项
      applicationStatusOptions: dictToArray(APPLICATION_STATUS),
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        realName: null,
        phone: null,
        status: null
      },
      // 表单参数
      form: {},
      // 审核表单参数
      auditForm: {},
      // 详情表单参数
      detailForm: {},
      // 表单校验
      rules: {
        realName: [
          { required: true, message: "申请人姓名不能为空", trigger: "blur" }
        ],
        phone: [
          { required: true, message: "手机号不能为空", trigger: "blur" }
        ],
        idCard: [
          { required: true, message: "身份证号不能为空", trigger: "blur" }
        ]
      },
      // 审核表单校验
      auditRules: {
        status: [
          { required: true, message: "审核结果不能为空", trigger: "change" }
        ],
        auditRemark: [
          { required: true, message: "审核备注不能为空", trigger: "blur" }
        ]
      }
    };
  },
  created() {
    this.getList();
  },
  methods: {
    /** 查询用户申请列表 */
    getList() {
      this.loading = true;
      listUserApplication(this.queryParams).then(response => {
        this.applicationList = response.rows || [];
        this.total = response.total || 0;
        this.loading = false;
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        applicationId: null,
        realName: null,
        phone: null,
        idCard: null,
        drivingLicense: null,
        applicationReason: null
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.applicationId)
      this.single = selection.length!==1
      this.multiple = !selection.length
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "添加用户申请";
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const applicationId = row.applicationId || this.ids
      getUserApplication(applicationId).then(response => {
        this.form = response.data;
        this.open = true;
        this.title = "修改用户申请";
      });
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate(valid => {
        if (valid) {
          if (this.form.applicationId != null) {
            updateUserApplication(this.form).then(response => {
              this.$modal.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            });
          } else {
            addUserApplication(this.form).then(response => {
              this.$modal.msgSuccess("新增成功");
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const applicationIds = row.applicationId || this.ids;
      this.$modal.confirm('是否确认删除用户申请编号为"' + applicationIds + '"的数据项？').then(function() {
        return delUserApplication(applicationIds);
      }).then(() => {
        this.getList();
        this.$modal.msgSuccess("删除成功");
      }).catch(() => {});
    },
    /** 审核按钮操作 */
    handleAudit(row) {
      this.auditForm = {
        applicationId: row.applicationId,
        status: null,
        auditRemark: null
      };
      this.auditOpen = true;
    },
    /** 提交审核 */
    submitAudit() {
      this.$refs["auditForm"].validate(valid => {
        if (valid) {
          auditUserApplication(this.auditForm).then(response => {
            this.$modal.msgSuccess("审核成功");
            this.auditOpen = false;
            this.getList();
          });
        }
      });
    },
    /** 取消审核 */
    cancelAudit() {
      this.auditOpen = false;
      this.auditForm = {};
    },
    /** 查看详情 */
    handleViewDetail(row) {
      this.detailForm = { ...row };
      this.detailOpen = true;
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download('dk/userApplication/export', {
        ...this.queryParams
      }, `userApplication_${new Date().getTime()}.xlsx`)
    },
    /** 获取申请状态标签 */
    getApplicationStatusLabel(status) {
      const statusObj = Object.values(APPLICATION_STATUS).find(item => item.value === status);
      return statusObj ? statusObj.label : status;
    },
    /** 获取申请状态类型 */
    getApplicationStatusType(status) {
      const statusObj = Object.values(APPLICATION_STATUS).find(item => item.value === status);
      return statusObj ? statusObj.cssClass : 'default';
    }
  }
};
</script>

<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>首次配对流程泳道图</title>
    <style>
        body {
            font-family: 'Microsoft YaHei', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .swimlane-container {
            max-width: 1000px;
            margin: 0 auto;
            background-color: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
        }
        h1 {
            color: #2874a6;
            text-align: center;
            margin-bottom: 30px;
        }
        .swimlane {
            display: flex;
            margin-bottom: 40px;
        }
        .lane-title {
            width: 120px;
            padding: 10px;
            text-align: center;
            font-weight: bold;
            border-radius: 5px 0 0 5px;
        }
        .mobile-lane .lane-title {
            background-color: #d4e6f1;
            border: 2px solid #3498db;
        }
        .car-lane .lane-title {
            background-color: #d5f5e3;
            border: 2px solid #2ecc71;
        }
        .cloud-lane .lane-title {
            background-color: #ebdef0;
            border: 2px solid #9b59b6;
        }
        .external-lane .lane-title {
            background-color: #fdebd0;
            border: 2px solid #f39c12;
        }
        .lane-content {
            flex: 1;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 0 5px 5px 0;
        }
        .step {
            margin-bottom: 15px;
            padding: 10px;
            background-color: #f8f9f9;
            border-radius: 5px;
            position: relative;
        }
        .step-number {
            font-weight: bold;
            color: #3498db;
            margin-right: 5px;
        }
        .arrow {
            position: absolute;
            bottom: -15px;
            left: 50%;
            transform: translateX(-50%);
            color: #7f8c8d;
            font-size: 20px;
        }
        .note {
            margin: 20px 0;
            padding: 10px;
            background-color: #f9ebea;
            border-left: 4px solid #e74c3c;
            border-radius: 0 5px 5px 0;
        }
        .note-title {
            font-weight: bold;
            color: #c0392b;
            margin-bottom: 5px;
        }
        .phase {
            margin: 30px 0;
            padding: 10px;
            background-color: #eaeded;
            text-align: center;
            font-weight: bold;
            color: #2c3e50;
            border-radius: 5px;
        }
    </style>
</head>
<body>
    <div class="swimlane-container">
        <h1>数字钥匙首次配对流程</h1>
        
        <div class="phase">蓝牙配对流程</div>
        
        <div class="swimlane mobile-lane">
            <div class="lane-title">APP</div>
            <div class="lane-content">
                <div class="step">
                    <span class="step-number">1.</span> 在APP中选择添加数字钥匙
                    <div class="arrow">↓</div>
                </div>
                <div class="step">
                    <span class="step-number">2.</span> 扫描车辆二维码或输入VIN码识别车辆
                    <div class="arrow">↓</div>
                </div>
                <div class="step">
                    <span class="step-number">3.</span> 发送车辆VIN码和手机设备唯一标识，请求获取数字钥匙
                    <div class="arrow">→</div>
                </div>
            </div>
        </div>
        
        <div class="swimlane cloud-lane">
            <div class="lane-title">钥匙云平台</div>
            <div class="lane-content">
                <div class="step">
                    <span class="step-number">4.</span> 根据VIN码生成虚拟密钥和唯一配对令牌
                    <div class="arrow">↓</div>
                </div>
                <div class="step">
                    <span class="step-number">5.</span> 下发虚拟密钥和配对令牌到手机
                    <div class="arrow">←</div>
                </div>
            </div>
        </div>
        
        <div class="swimlane mobile-lane">
            <div class="lane-title">APP</div>
            <div class="lane-content">
                <div class="step">
                    <span class="step-number">6.</span> 保存虚拟密钥和配对令牌到安全区域
                </div>
            </div>
        </div>
        
        <div class="note">
            <div class="note-title">注意：</div>
            <p>钥匙云平台通过安全通道与TSP/OEM平台交互，获取车辆信息并建立安全连接</p>
        </div>
        
        <div class="swimlane cloud-lane">
            <div class="lane-title">钥匙云平台</div>
            <div class="lane-content">
                <div class="step">
                    <span class="step-number">7.</span> 查询VIN码对应的车辆信息
                    <div class="arrow">→</div>
                </div>
            </div>
        </div>
        
        <div class="swimlane external-lane">
            <div class="lane-title">TSP/OEM平台</div>
            <div class="lane-content">
                <div class="step">
                    <span class="step-number">8.</span> 返回车辆信息
                    <div class="arrow">←</div>
                </div>
            </div>
        </div>
        
        <div class="swimlane cloud-lane">
            <div class="lane-title">钥匙云平台</div>
            <div class="lane-content">
                <div class="step">
                    <span class="step-number">9.</span> 通过4G连接到车辆并下发：根密钥、授权手机标识码
                    <div class="arrow">→</div>
                </div>
            </div>
        </div>
        
        <div class="note">
            <div class="note-title">说明：</div>
            <ul>
                <li>根密钥相当于主钥匙，是后续所有安全通信的基础</li>
                <li>授权手机标识码基于手机号、IMEI和设备ID等信息生成</li>
                <li>车端将据此验证连接的手机是否确为授权设备</li>
            </ul>
        </div>
        
        <div class="swimlane car-lane">
            <div class="lane-title">车端</div>
            <div class="lane-content">
                <div class="step">
                    <span class="step-number">10.</span> 存储根密钥、授权手机标识码和配对令牌
                    <div class="arrow">↓</div>
                </div>
                <div class="step">
                    <span class="step-number">11.</span> 发送蓝牙广播信号（包含车辆标识信息"加密后的VIN"）
                    <div class="arrow">→</div>
                </div>
            </div>
        </div>
        
        <div class="note">
            <div class="note-title">说明：</div>
            <p>只有持有正确虚拟密钥的手机才能识别并连接到对应车辆，就像只有知道暗号的人才能找到正确的门</p>
        </div>
        
        <div class="swimlane mobile-lane">
            <div class="lane-title">APP</div>
            <div class="lane-content">
                <div class="step">
                    <span class="step-number">12a.</span> 识别广播中的车辆信息
                    <div class="arrow">↓</div>
                </div>
                <div class="step">
                    <span class="step-number">12b.</span> 连接到对应车辆的蓝牙
                    <div class="arrow">↓</div>
                </div>
                <div class="step">
                    <span class="step-number">13.</span> 发送配对请求（包含密钥和配对令牌）
                    <div class="arrow">→</div>
                </div>
            </div>
        </div>
        
        <div class="swimlane car-lane">
            <div class="lane-title">车端</div>
            <div class="lane-content">
                <div class="step">
                    <span class="step-number">14a.</span> 验证手机身份是否匹配授权手机标识码
                    <div class="arrow">↓</div>
                </div>
                <div class="step">
                    <span class="step-number">14b.</span> 发起安全验证请求
                    <div class="arrow">←</div>
                </div>
                <div class="step">
                    <span class="step-number">15.</span> 基于VIN码、授权手机标识码和时间戳生成一次性配对码
                </div>
            </div>
        </div>
        
        <div class="swimlane mobile-lane">
            <div class="lane-title">APP</div>
            <div class="lane-content">
                <div class="step">
                    <span class="step-number">16.</span> 基于同样的VIN码、手机标识码和时间戳生成配对码
                    <div class="arrow">↓</div>
                </div>
                <div class="step">
                    <span class="step-number">17.</span> 发送配对码进行双向验证
                    <div class="arrow">→</div>
                </div>
            </div>
        </div>
        
        <div class="swimlane car-lane">
            <div class="lane-title">车端</div>
            <div class="lane-content">
                <div class="step">
                    <span class="step-number">18a.</span> 验证配对码
                    <div class="arrow">↓</div>
                </div>
                <div class="step">
                    <span class="step-number">18b.</span> 返回验证结果
                    <div class="arrow">←</div>
                </div>
            </div>
        </div>
        
        <div class="note">
            <div class="note-title">双向验证确保：</div>
            <ol>
                <li>手机确实是授权的手机</li>
                <li>车辆确实是正确的车辆</li>
                <li>配对码是一次性的，具有时效性，即使被截获也无法重复使用</li>
            </ol>
        </div>
        
        <div class="phase">安全通道建立</div>
        
        <div class="swimlane mobile-lane">
            <div class="lane-title">APP</div>
            <div class="lane-content">
                <div class="step">
                    <span class="step-number">19a.</span> 准备创建临时暗号材料
                    <div class="arrow">↓</div>
                </div>
                <div class="step">
                    <span class="step-number">19b.</span> 发送部分暗号材料
                    <div class="arrow">→</div>
                </div>
            </div>
        </div>
        
        <div class="swimlane car-lane">
            <div class="lane-title">车端</div>
            <div class="lane-content">
                <div class="step">
                    <span class="step-number">20a.</span> 准备创建临时暗号材料
                    <div class="arrow">↓</div>
                </div>
                <div class="step">
                    <span class="step-number">20b.</span> 发送部分暗号材料
                    <div class="arrow">←</div>
                </div>
            </div>
        </div>
        
        <div class="note">
            <div class="note-title">暗号交换原理：</div>
            <p>手机和车辆通过特殊方式各自创建暗号:</p>
            <ol>
                <li>双方各自有一个秘密</li>
                <li>交换一些公开信息</li>
                <li>用自己的秘密和对方的公开信息计算出相同的结果</li>
                <li>这个结果就是它们共同的暗号(会话密钥)</li>
            </ol>
            <p>就像两个人不用告诉对方自己的秘密，却能算出同一个数字</p>
        </div>
        
        <div class="swimlane mobile-lane">
            <div class="lane-title">APP</div>
            <div class="lane-content">
                <div class="step">
                    <span class="step-number">21.</span> 计算出共同暗号(会话密钥)
                </div>
            </div>
        </div>
        
        <div class="swimlane car-lane">
            <div class="lane-title">车端</div>
            <div class="lane-content">
                <div class="step">
                    <span class="step-number">22.</span> 计算出共同暗号(会话密钥)
                </div>
            </div>
        </div>
        
        <div class="note">
            <div class="note-title">会话密钥特点：</div>
            <ol>
                <li>是临时的，定期更换</li>
                <li>存储在特殊安全区域</li>
                <li>只有配对的手机和车辆知道</li>
            </ol>
            <p>就像每天更换的通行密码</p>
        </div>
        
        <div class="swimlane car-lane">
            <div class="lane-title">车端</div>
            <div class="lane-content">
                <div class="step">
                    <span class="step-number">23.</span> 存储以下信息到安全区域:
                    <ul>
                        <li>会话密钥（临时暗号）</li>
                        <li>绑定的手机信息</li>
                        <li>密钥有效期</li>
                        <li>授权等级</li>
                    </ul>
                </div>
            </div>
        </div>
        
        <div class="swimlane mobile-lane">
            <div class="lane-title">APP</div>
            <div class="lane-content">
                <div class="step">
                    <span class="step-number">24.</span> 存储以下信息到手机安全区域:
                    <ul>
                        <li>会话密钥（临时暗号）</li>
                        <li>车辆信息</li>
                        <li>蓝牙连接信息</li>
                        <li>密钥有效期</li>
                        <li>权限等级</li>
                    </ul>
                </div>
            </div>
        </div>
        
        <div class="swimlane mobile-lane">
            <div class="lane-title">APP</div>
            <div class="lane-content">
                <div class="step">
                    <span class="step-number">25.</span> 建立加密安全通道
                    <div class="arrow">↔</div>
                </div>
            </div>
        </div>
        
        <div class="swimlane car-lane">
            <div class="lane-title">车端</div>
            <div class="lane-content">
                <div class="step">
                    <span class="step-number">25.</span> 建立加密安全通道
                    <div class="arrow">↔</div>
                </div>
            </div>
        </div>
        
        <div class="note">
            <div class="note-title">安全通道特点：</div>
            <ol>
                <li>所有消息都经过加密，外人无法读取</li>
                <li>每条消息都有标记，防止被重复使用</li>
                <li>能检测消息是否被篡改</li>
            </ol>
            <p>就像一条只有你和车能使用的秘密通道</p>
        </div>
        
        <div class="swimlane mobile-lane">
            <div class="lane-title">APP</div>
            <div class="lane-content">
                <div class="step">
                    <span class="step-number">26a.</span> 上报配对成功
                    <div class="arrow">→</div>
                </div>
            </div>
        </div>
        
        <div class="swimlane car-lane">
            <div class="lane-title">车端</div>
            <div class="lane-content">
                <div class="step">
                    <span class="step-number">26b.</span> 上报配对成功状态
                    <div class="arrow">→</div>
                </div>
            </div>
        </div>
        
        <div class="swimlane cloud-lane">
            <div class="lane-title">钥匙云平台</div>
            <div class="lane-content">
                <div class="step">
                    <span class="step-number">27.</span> 记录钥匙绑定关系:
                    <ul>
                        <li>用户与车辆的绑定关系</li>
                        <li>设备与钥匙的绑定关系</li>
                        <li>钥匙权限等级</li>
                        <li>有效期限</li>
                    </ul>
                    <div class="arrow">↓</div>
                </div>
                <div class="step">
                    <span class="step-number">28.</span> 返回数字钥匙信息和权限配置
                    <div class="arrow">←</div>
                </div>
            </div>
        </div>
        
        <div class="swimlane mobile-lane">
            <div class="lane-title">APP</div>
            <div class="lane-content">
                <div class="step">
                    <span class="step-number">29.</span> 显示数字钥匙添加成功
                </div>
            </div>
        </div>
        
        <div class="phase">异常处理</div>
        
        <div class="note">
            <div class="note-title">配对过程中可能出现的问题及处理方式：</div>
            <p><strong>配对超时：</strong></p>
            <ul>
                <li>检测到配对请求长时间无响应</li>
                <li>显示"配对超时，请重试"</li>
                <li>上报配对失败</li>
            </ul>
            <p><strong>验证失败：</strong></p>
            <ul>
                <li>返回验证失败信息</li>
                <li>显示"验证失败，请确认车辆状态"</li>
                <li>上报配对失败</li>
            </ul>
            <p><strong>暗号生成失败：</strong></p>
            <ul>
                <li>检测到密钥交换异常</li>
                <li>显示"连接异常，请重试"</li>
                <li>上报配对失败</li>
            </ul>
        </div>
        
        <div class="note">
            <div class="note-title">配对完成后各方保存的重要信息：</div>
            <ol>
                <li><strong>手机端:</strong> 临时暗号、车辆信息、权限配置</li>
                <li><strong>车端:</strong> 临时暗号、授权手机信息、权限等级</li>
                <li><strong>云平台:</strong> 用户-车辆-设备的关系记录</li>
            </ol>
            <p>这些信息用于后续蓝牙连接验证、功能权限控制和远程管理</p>
        </div>
        
        <div class="note">
            <div class="note-title">安全保障措施：</div>
            <ol>
                <li>临时暗号定期更换</li>
                <li>密钥存储在特殊安全区域(如SE、HSM)</li>
                <li>异常情况自动处理</li>
                <li>使用蓝牙4.2+安全特性确保配对安全</li>
                <li>设备身份认证确保仅合法设备可配对</li>
                <li>使用距离测量防止中继攻击</li>
                <li>密钥操作在安全环境中执行</li>
            </ol>
            <p>就像定期更换密码并存放在保险箱中</p>
        </div>
        
        <div class="note">
            <div class="note-title">技术实现细节：</div>
            <p>虚拟密钥的底层实现通常基于非对称加密技术，包含用户的公钥证书信息。根密钥在技术实现上可能是非对称密钥对，车端保存私钥部分。所有密码学操作均在硬件安全模块或可信执行环境中进行。</p>
        </div>
    </div>
</body>
</html> 
import request from '@/utils/request'

// 根据车辆VIN码生成密钥
export function generateKey(params) {
  return request({
    'url': '/system/api/vin/generate-key',
    method: 'get',
    params
  })
}

// 新增钥匙分享详情信息
export function addDigitalKeyShareDetail(data) {
  return request({
    'url': '/system/digitalKeyShareDetail',
    method: 'post',
    data: data
  })
}

// ==================== 用户申请相关API ====================

// 提交用户申请
export function submitUserApplication(data) {
  return request({
    url: '/system/userApplication',
    method: 'post',
    data: data
  })
}

// 查询当前用户的申请列表
export function getUserApplicationList(params) {
  return request({
    url: '/system/userApplication/list',
    method: 'get',
    params: params
  })
}

// 查询申请详情
export function getUserApplicationDetail(applicationId) {
  return request({
    url: '/system/userApplication/' + applicationId,
    method: 'get'
  })
}

// 撤销申请
export function withdrawApplication(applicationId) {
  return request({
    url: '/system/userApplication/withdraw/' + applicationId,
    method: 'put'
  })
}

// 补充申请资料
export function supplementApplication(data) {
  return request({
    url: '/userApplication/supplement',
    method: 'put',
    data: data
  })
}

// 上传证件照片
export function uploadCertificate(data) {
  return request({
    url: '/userApplication/upload-certificate',
    method: 'post',
    data: data,
    headers: {
      'Content-Type': 'multipart/form-data'
    }
  })
}

// ==================== 数字钥匙相关API ====================

// 获取用户的数字钥匙信息
export function getUserDigitalKeys() {
  return request({
    url: '/dk/digitalKey/my-keys',
    method: 'get'
  })
}

// 激活数字钥匙
export function activateDigitalKey(keyId) {
  return request({
    url: '/dk/digitalKey/activate/' + keyId,
    method: 'put'
  })
}

// 检查钥匙权限状态
export function checkKeyPermission(keyId) {
  return request({
    url: '/dk/digitalKey/check-permission/' + keyId,
    method: 'get'
  })
}

// ==================== 车辆控制相关API ====================

// 车辆解锁
export function unlockVehicle(data) {
  return request({
    url: '/dk/vehicle/unlock',
    method: 'post',
    data: data
  })
}

// 车辆上锁
export function lockVehicle(data) {
  return request({
    url: '/dk/vehicle/lock',
    method: 'post',
    data: data
  })
}

// 车辆启动
export function startVehicle(data) {
  return request({
    url: '/dk/vehicle/start',
    method: 'post',
    data: data
  })
}

// 获取车辆状态
export function getVehicleStatus(vinCode) {
  return request({
    url: '/dk/vehicle/status/' + vinCode,
    method: 'get'
  })
}

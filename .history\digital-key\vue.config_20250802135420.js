const path = require('path')

module.exports = {
  configureWebpack: {
    resolve: {
      alias: {
        '@': path.resolve(__dirname, './'),
        '@/components': path.resolve(__dirname, './components'),
        '@/utils': path.resolve(__dirname, './utils'),
        '@/api': path.resolve(__dirname, './api'),
        '@/store': path.resolve(__dirname, './store'),
        '@/config': path.resolve(__dirname, './config'),
        '@/static': path.resolve(__dirname, './static')
      }
    }
  },

  // 生产环境配置
  productionSourceMap: false
}

@startuml
skinparam componentStyle rectangle

package "数字钥匙" {
    package "手机端" {
        component "钥匙管理模块" as KeyManage
        component "车辆控制模块" as VehicleCtrl
        component "蓝牙通信模块" as BLEComm
        component "安全存储模块" as SecStorage
        component "时间同步模块" as TimeSync
    }
    
    package "车端" {
        component "BLE通信模块" as CarBLE
        component "钥匙验证模块" as KeyVerify
        component "指令执行模块" as CmdExec
        component "安全存储模块" as CarSecStorage
        component "T-Box通信模块" as TBoxComm
        component "时间同步模块" as CarTimeSync
    }
    
    package "钥匙云平台" {
        component "钥匙生命周期管理" as KeyLifecycle
        component "VIN码关联服务" as VINService
        component "安全认证中心" as AuthCenter
        component "密钥管理系统" as KeyMgmt
        component "时间服务器" as TimeServer
        component "API接口层" as APILayer
        component "外部平台集成服务" as ExtPlatformIntegration
    }
    
    package "外部平台" {
        package "TSP平台" {
            component "车辆管理服务" as TSPVehicleManage
            component "远程控制服务" as TSPRemoteControl
            component "用户授权服务" as TSPUserAuth
            component "数据分析服务" as TSPDataAnalysis
            component "API网关" as TSPAPIGateway
        }
        
        package "OEM平台" {
            component "车辆生产管理" as OEMProduction
            component "车辆配置服务" as OEMConfig
            component "售后服务系统" as OEMAfterSales
            component "车辆诊断系统" as OEMDiagnostic
            component "API网关" as OEMAPIGateway
        }
    }
    
    database "数据存储接口" {
        database "钥匙数据" as KeysDB
        database "密钥材料" as KeyMaterialsDB
    }
    
    database "安全基础设施" {
        database "密码机/HSM" as HSM
        database "PKI/CA系统" as PKICA
    }
}

' 手机端内部关系
KeyManage --> VehicleCtrl
VehicleCtrl --> BLEComm
BLEComm --> SecStorage
SecStorage --> TimeSync

' 车端内部关系
CarBLE --> KeyVerify
KeyVerify --> CmdExec
CmdExec --> CarSecStorage
CarSecStorage --> TBoxComm
TBoxComm --> CarTimeSync

' 钥匙服务内部关系
KeyLifecycle --> VINService
VINService --> AuthCenter
AuthCenter --> KeyMgmt
KeyMgmt --> TimeServer
TimeServer --> APILayer
APILayer --> ExtPlatformIntegration

' 跨系统关系
BLEComm <--> CarBLE : 蓝牙通信
APILayer <--> TBoxComm : 远程通信
APILayer <--> KeyManage : HTTPS通信
TimeServer --> TimeSync : 时间同步
TimeServer --> CarTimeSync : 时间同步

' 数据库关系
KeyLifecycle --> KeysDB
KeyMgmt --> KeyMaterialsDB
AuthCenter --> PKICA
KeyMgmt --> HSM

' 外部平台与钥匙云平台的关系
ExtPlatformIntegration <--> TSPAPIGateway : REST API/WebSocket
ExtPlatformIntegration <--> OEMAPIGateway : REST API/SOAP

' TSP平台内部关系
TSPAPIGateway --> TSPVehicleManage
TSPAPIGateway --> TSPRemoteControl
TSPAPIGateway --> TSPUserAuth
TSPAPIGateway --> TSPDataAnalysis

' OEM平台内部关系
OEMAPIGateway --> OEMProduction
OEMAPIGateway --> OEMConfig
OEMAPIGateway --> OEMAfterSales
OEMAPIGateway --> OEMDiagnostic

' TSP平台与车端关系
TSPRemoteControl <--> TBoxComm : 远程指令下发

' OEM平台与车端关系
OEMDiagnostic <--> TBoxComm : 远程诊断

@enduml
@echo off
echo ========================================
echo 4G控车系统测试脚本
echo ========================================

cd /d "%~dp0ruoyi-modules\ruoyi-system"

echo.
echo 正在编译项目...
mvn compile -q

if %ERRORLEVEL% NEQ 0 (
    echo 编译失败！
    pause
    exit /b 1
)

echo 编译成功！
echo.
echo 可以启动系统进行测试：
echo 1. 运行 start-4g-vehicle-control.bat 启动系统
echo 2. 访问 http://localhost:9201/tbox-simulator.html (TBOX模拟器)
echo 3. 访问 http://localhost:9201/mobile-control.html (手机控车APP)
echo.

pause

package com.ruoyi.dk.controller;

import com.ruoyi.common.core.web.controller.BaseController;
import com.ruoyi.common.core.web.domain.AjaxResult;
import com.ruoyi.common.core.web.page.TableDataInfo;
import com.ruoyi.dk.domain.DkUserApplication;
import com.ruoyi.dk.service.IDkUserApplicationService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 用户申请管理Controller
 * 
 * <AUTHOR>
 * @date 2024-01-15
 */
@RestController
@RequestMapping("/userApplication")
public class DkUserApplicationController extends BaseController {

    @Autowired(required = false)
    private IDkUserApplicationService dkUserApplicationService;

    /**
     * 查询用户申请列表
     */
    @GetMapping("/list")
    public TableDataInfo list(DkUserApplication dkUserApplication) {
        try {
            if (dkUserApplicationService != null) {
                startPage();
                List<DkUserApplication> list = dkUserApplicationService.selectDkUserApplicationList(dkUserApplication);
                return getDataTable(list);
            }
        } catch (Exception e) {
            logger.error("查询用户申请列表失败", e);
        }

    }

    /**
     * 获取用户申请详细信息
     */
    @GetMapping(value = "/{applicationId}")
    public AjaxResult getInfo(@PathVariable("applicationId") Long applicationId) {
        try {
            if (dkUserApplicationService != null) {
                return AjaxResult
                        .success(dkUserApplicationService.selectDkUserApplicationByApplicationId(applicationId));
            }
        } catch (Exception e) {
            logger.error("获取用户申请详细信息失败", e);
        }
    }

    /**
     * 新增用户申请
     */
    @PostMapping
    public AjaxResult add(@RequestBody DkUserApplication dkUserApplication) {
        try {
            if (dkUserApplicationService != null) {
                return toAjax(dkUserApplicationService.insertDkUserApplication(dkUserApplication));
            }
        } catch (Exception e) {
            logger.error("新增用户申请失败", e);
        }

        // 模拟成功
        return AjaxResult.success("新增成功");
    }

    /**
     * 修改用户申请
     */
    @PutMapping
    public AjaxResult edit(@RequestBody DkUserApplication dkUserApplication) {
        try {
            if (dkUserApplicationService != null) {
                return toAjax(dkUserApplicationService.updateDkUserApplication(dkUserApplication));
            }
        } catch (Exception e) {
            logger.error("修改用户申请失败", e);
        }

        // 模拟成功
        return AjaxResult.success("修改成功");
    }

    /**
     * 删除用户申请
     */
    @DeleteMapping("/{applicationIds}")
    public AjaxResult remove(@PathVariable Long[] applicationIds) {
        try {
            if (dkUserApplicationService != null) {
                return toAjax(dkUserApplicationService.deleteDkUserApplicationByApplicationIds(applicationIds));
            }
        } catch (Exception e) {
            logger.error("删除用户申请失败", e);
        }

        // 模拟成功
        return AjaxResult.success("删除成功");
    }

    /**
     * 审核用户申请
     */
    @PutMapping("/audit")
    public AjaxResult audit(@RequestBody Map<String, Object> auditData) {
        try {
            if (dkUserApplicationService != null) {
                Long applicationId = Long.valueOf(auditData.get("applicationId").toString());
                String status = auditData.get("status").toString();
                String auditRemark = auditData.get("auditRemark").toString();
                return toAjax(dkUserApplicationService.auditApplication(applicationId, status, auditRemark));
            }
        } catch (Exception e) {
            logger.error("审核用户申请失败", e);
        }

        // 模拟成功
        return AjaxResult.success("审核成功");
    }

    /**
     * 批量审核用户申请
     */
    @PutMapping("/batchAudit")
    public AjaxResult batchAudit(@RequestBody Map<String, Object> auditData) {
        try {
            if (dkUserApplicationService != null) {
                Long[] applicationIds = (Long[]) auditData.get("applicationIds");
                String status = auditData.get("status").toString();
                String auditRemark = auditData.get("auditRemark").toString();
                return toAjax(dkUserApplicationService.batchAuditApplication(applicationIds, status, auditRemark));
            }
        } catch (Exception e) {
            logger.error("批量审核用户申请失败", e);
        }

        // 模拟成功
        return AjaxResult.success("批量审核成功");
    }

    /**
     * 获取待审核申请数量
     */
    @GetMapping("/pendingCount")
    public AjaxResult getPendingApplicationCount() {
        try {
            if (dkUserApplicationService != null) {
                int count = dkUserApplicationService.getPendingApplicationCount();
                return AjaxResult.success(count);
            }
        } catch (Exception e) {
            logger.error("获取待审核申请数量失败", e);
        }
        return null;
    }
}

/**
 * 4G控车配置文件
 */

// 开发环境配置
const development = {
  // WebSocket服务器地址
  websocketUrl: 'ws://localhost:9201',
  
  // 是否启用4G控车功能
  enabled: true,
  
  // 重连配置
  reconnect: {
    maxAttempts: 5,
    interval: 5000
  },
  
  // 超时配置
  timeout: {
    connect: 10000,
    command: 5000
  }
}

// 生产环境配置
const production = {
  // WebSocket服务器地址 - 需要根据实际部署环境修改
  websocketUrl: 'ws://your-production-server:9201',
  
  // 是否启用4G控车功能
  enabled: true,
  
  // 重连配置
  reconnect: {
    maxAttempts: 3,
    interval: 10000
  },
  
  // 超时配置
  timeout: {
    connect: 15000,
    command: 8000
  }
}

// 测试环境配置
const testing = {
  // WebSocket服务器地址
  websocketUrl: 'ws://test-server:9201',
  
  // 是否启用4G控车功能
  enabled: true,
  
  // 重连配置
  reconnect: {
    maxAttempts: 5,
    interval: 3000
  },
  
  // 超时配置
  timeout: {
    connect: 8000,
    command: 3000
  }
}

// 根据环境变量选择配置
const getConfig = () => {
  // 可以通过环境变量或其他方式判断当前环境
  const env = process.env.NODE_ENV || 'development'
  
  switch (env) {
    case 'production':
      return production
    case 'testing':
      return testing
    case 'development':
    default:
      return development
  }
}

// 导出配置
export default getConfig()

// 也可以导出所有配置供调试使用
export {
  development,
  production,
  testing,
  getConfig
}

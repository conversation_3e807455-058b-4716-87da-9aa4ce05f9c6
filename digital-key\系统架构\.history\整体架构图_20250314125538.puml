@startuml
skinparam componentStyle rectangle

package "数字钥匙" {
    package "手机端" {
        component "钥匙管理模块" as KeyManage
        component "车辆控制模块" as VehicleCtrl
        component "蓝牙通信模块" as BLEComm
        component "安全存储模块" as SecStorage
        component "时间同步模块" as TimeSync
        component "暗号交换模块" as MobileKeyExchange
        note right of MobileKeyExchange
          负责与车端协商生成临时暗号；
          确保只有配对的双方能通信
        end note
    }
    
    package "车端" {
        component "蓝牙通信模块" as CarBLE
        component "钥匙验证模块" as KeyVerify
        component "指令执行模块" as CmdExec
        component "安全存储模块" as CarSecStorage
        component "远程通信模块" as TBoxComm
        component "时间同步模块" as CarTimeSync
        component "暗号交换模块" as CarKeyExchange
        note right of CarKeyExchange
          负责与手机端协商生成临时暗号；
          确保只有配对的双方能通信
        end note
    }
    
    package "钥匙云平台" {
        component "钥匙生命周期管理" as KeyLifecycle
        note right of KeyLifecycle
          负责钥匙的创建、授权和撤销；
          管理配对状态；
          监控异常使用情况
        end note
        
        component "车辆关联服务" as VINService
        component "安全认证中心" as AuthCenter
        note right of AuthCenter
          负责身份验证；
          确保只有授权用户能使用钥匙；
          处理安全事件
        end note
        
        component "密钥管理系统" as KeyMgmt
        note right of KeyMgmt
          生成和管理各种密钥；
          确保密钥安全存储；
          提供暗号交换基础；
          监控密钥使用情况
        end note
        
        component "时间服务器" as TimeServer
        component "统一接口服务" as UnifiedAPI
        note right of UnifiedAPI
          提供标准接口；
          建立安全通信通道；
          防止消息被篡改或重放
        end note
        
        component "安全通信通道" as SecureChannel
        note right of SecureChannel
          提供加密通信；
          防止消息被窃听或篡改；
          支持暗号更新；
          监控通信异常
        end note
    }
    
    ' 简化外部平台设计
    component "外部平台(TSP/OEM等)" as ExternalPlatforms
    
    database "数据存储" {
        database "钥匙数据" as KeysDB
        database "密钥材料" as KeyMaterialsDB
        database "通信记录" as SessionDB
        note right of SessionDB
          记录通信事件；
          支持异常检测；
          不存储密钥本身
        end note
    }
    
    database "安全基础设施" {
        database "密码机" as HSM
        note right of HSM
          执行密钥操作；
          确保密钥安全；
          提供物理防护
        end note
        
        database "证书系统" as PKICA
        database "密钥备份系统" as SecureBackup
        note right of SecureBackup
          安全备份关键信息；
          防止数据丢失；
          多重保护机制
        end note
        
        database "安全监控" as SecMonitor
        note right of SecMonitor
          监控系统使用情况；
          检测异常行为；
          触发安全响应；
          提供安全报告
        end note
    }
}

' 使用标记替代直接连线
' 手机端内部关系
KeyManage -[#blue]-> VehicleCtrl : A1
VehicleCtrl -[#blue]-> BLEComm : A2
BLEComm -[#blue]-> SecStorage : A3
SecStorage -[#blue]-> TimeSync : A4
BLEComm -[#blue]-> MobileKeyExchange : A5
MobileKeyExchange -[#blue]-> SecStorage : A6

' 车端内部关系
CarBLE -[#green]-> KeyVerify : B1
KeyVerify -[#green]-> CmdExec : B2
CmdExec -[#green]-> CarSecStorage : B3
CarSecStorage -[#green]-> TBoxComm : B4
TBoxComm -[#green]-> CarTimeSync : B5
CarBLE -[#green]-> CarKeyExchange : B6
CarKeyExchange -[#green]-> CarSecStorage : B7

' 钥匙服务内部关系
KeyLifecycle -[#red]-> VINService : C1
VINService -[#red]-> AuthCenter : C2
AuthCenter -[#red]-> KeyMgmt : C3
KeyMgmt -[#red]-> TimeServer : C4
TimeServer -[#red]-> UnifiedAPI : C5
KeyMgmt -[#red]-> SecureChannel : C6
SecureChannel -[#red]-> UnifiedAPI : C7

' 跨系统关系
BLEComm <-[#purple]-> CarBLE : D1
UnifiedAPI <-[#purple]-> TBoxComm : D2
UnifiedAPI <-[#purple]-> KeyManage : D3
TimeServer -[#purple]-> TimeSync : D4
TimeServer -[#purple]-> CarTimeSync : D5
MobileKeyExchange <-[#purple]-> CarKeyExchange : D6

' 数据库关系
KeyLifecycle -[#orange]-> KeysDB : E1
KeyMgmt -[#orange]-> KeyMaterialsDB : E2
AuthCenter -[#orange]-> PKICA : E3
KeyMgmt -[#orange]-> HSM : E4
KeyMgmt -[#orange]-> SecureBackup : E5
SecureChannel -[#orange]-> SessionDB : E6
KeyMgmt -[#orange]-> SecMonitor : E7

' 外部平台与钥匙云平台的关系 - 简化连接
UnifiedAPI <-[#brown]-> ExternalPlatforms : F1

note right of UnifiedAPI
  统一接口服务负责与车辆和外部系统通信，
  确保信息安全传递
end note

' 表示关键的关系
KeyMgmt <-[#magenta]-> HSM : G1
AuthCenter <-[#magenta]-> PKICA : G2
KeyLifecycle -[#magenta]-> KeyMgmt : G3
UnifiedAPI -[#magenta]-> AuthCenter : G4
SecureChannel <-[#magenta]-> SecMonitor : G5

legend
  <b>连接关系说明</b>
  
  <b>A系列</b>：手机端内部模块关系
  A1: 钥匙管理模块向车辆控制模块提供钥匙信息
  A2: 车辆控制模块通过蓝牙通信模块发送控制指令
  A3: 蓝牙通信模块使用安全存储模块中的密钥进行加密通信
  A4: 安全存储模块使用时间同步模块提供的时间戳进行安全验证
  A5: 蓝牙通信模块调用暗号交换模块进行密钥协商
  A6: 暗号交换模块将生成的临时暗号存入安全存储区
  
  <b>B系列</b>：车端内部模块关系
  B1: 蓝牙通信模块接收指令并传递给钥匙验证模块
  B2: 钥匙验证模块验证通过后，将指令传递给指令执行模块
  B3: 指令执行模块执行指令，并将结果存储在安全存储模块
  B4: 安全存储模块与远程通信模块交互，上报状态
  B5: 远程通信模块使用时间同步模块提供的时间戳进行安全验证
  B6: 蓝牙通信模块调用暗号交换模块进行密钥协商
  B7: 暗号交换模块将生成的临时暗号存入安全存储区
  
  <b>C系列</b>：钥匙云平台内部模块关系
  C1: 钥匙生命周期管理向车辆关联服务提供钥匙信息
  C2: 车辆关联服务向安全认证中心提供车辆信息
  C3: 安全认证中心向密钥管理系统请求密钥材料
  C4: 密钥管理系统向时间服务器请求时间戳
  C5: 时间服务器向统一接口服务提供时间同步服务
  C6: 密钥管理系统向安全通信通道提供密钥材料
  C7: 安全通信通道为统一接口服务提供加密通信支持
  
  <b>D系列</b>：跨系统通信关系
  D1: 手机端蓝牙通信模块与车端蓝牙通信模块进行蓝牙通信
  D2: 云平台统一接口服务与车端远程通信模块进行远程通信
  D3: 云平台统一接口服务与手机端钥匙管理模块进行网络通信
  D4: 云平台时间服务器与手机端时间同步模块进行时间同步
  D5: 云平台时间服务器与车端时间同步模块进行时间同步
  D6: 手机端暗号交换模块与车端暗号交换模块进行密钥协商
  
  <b>E系列</b>：数据存储关系
  E1: 钥匙生命周期管理模块访问钥匙数据库
  E2: 密钥管理系统访问密钥材料数据库
  E3: 安全认证中心访问证书系统
  E4: 密钥管理系统访问密码机进行密钥操作
  E5: 密钥管理系统将密钥材料备份到备份系统
  E6: 安全通信通道将通信记录存入会话数据库
  E7: 密钥管理系统向安全监控系统提供密钥使用数据
  
  <b>F系列</b>：外部平台交互
  F1: 统一接口服务与外部平台进行数据交互
  
  <b>G系列</b>：关键安全关系
  G1: 密钥管理系统与密码机交互进行密钥操作
  G2: 安全认证中心与证书系统交互进行证书管理
  G3: 钥匙生命周期管理向密钥管理系统发送钥匙生命周期事件
  G4: 统一接口服务向安全认证中心发送安全验证请求
  G5: 安全通信通道向安全监控系统提供通信事件数据
end legend

@enduml
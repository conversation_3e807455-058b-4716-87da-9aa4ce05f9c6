package com.ruoyi.common.redis.service;

import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.TimeUnit;
import org.springframework.stereotype.Service;
import org.springframework.context.annotation.Primary;

/**
 * Redis服务Mock实现
 * 使用内存Map模拟Redis功能，完全不依赖Redis库
 *
 * <AUTHOR>
 */
@Service("redisService")
@Primary
public class RedisService {

    // 使用内存Map模拟Redis存储
    private final Map<String, Object> cache = new ConcurrentHashMap<>();
    private final Map<String, Long> expireTime = new ConcurrentHashMap<>();

    public RedisService() {
        System.out.println("✅ RedisService Mock实现已初始化，使用内存缓存");
    }

    /**
     * 缓存基本的对象，Integer、String、实体类等
     *
     * @param key   缓存的键值
     * @param value 缓存的值
     */
    public <T> void setCacheObject(final String key, final T value) {
        cache.put(key, value);
    }

    /**
     * 缓存基本的对象，Integer、String、实体类等
     *
     * @param key      缓存的键值
     * @param value    缓存的值
     * @param timeout  时间
     * @param timeUnit 时间颗粒度
     */
    public <T> void setCacheObject(final String key, final T value, final Long timeout, final TimeUnit timeUnit) {
        cache.put(key, value);
        if (timeout != null && timeout > 0) {
            long expireTimeMillis = System.currentTimeMillis() + timeUnit.toMillis(timeout);
            expireTime.put(key, expireTimeMillis);
        }
    }

    /**
     * 设定缓存的时效时间
     *
     * @param key     缓存键值
     * @param timeout 超时时间
     * @param unit    时间单位
     * @return true=设置成功；false=设置失败
     */
    public boolean expire(final String key, final long timeout, final TimeUnit unit) {
        if (cache.containsKey(key)) {
            long expireTimeMillis = System.currentTimeMillis() + unit.toMillis(timeout);
            expireTime.put(key, expireTimeMillis);
            return true;
        }
        return false;
    }

    /**
     * 获得缓存的基本对象。
     *
     * @param key 缓存键值
     * @return 缓存键值对应的数据
     */
    @SuppressWarnings("unchecked")
    public <T> T getCacheObject(final String key) {
        if (isExpired(key)) {
            deleteObject(key);
            return null;
        }
        return (T) cache.get(key);
    }

    /**
     * 删除单个对象
     *
     * @param key 缓存键值
     */
    public boolean deleteObject(final String key) {
        cache.remove(key);
        expireTime.remove(key);
        return true;
    }

    /**
     * 删除集合对象
     *
     * @param collection 多个对象
     * @return 删除的数量
     */
    @SuppressWarnings("rawtypes")
    public long deleteObject(final Collection collection) {
        long count = 0;
        for (Object key : collection) {
            if (deleteObject(key.toString())) {
                count++;
            }
        }
        return count;
    }

    /**
     * 缓存List数据
     *
     * @param key      缓存的键值
     * @param dataList 待缓存的List数据
     * @return 缓存的对象
     */
    public <T> long setCacheList(final String key, final List<T> dataList) {
        cache.put(key, dataList);
        return dataList.size();
    }

    /**
     * 获得缓存的list对象
     *
     * @param key 缓存的键值
     * @return 缓存键值对应的数据
     */
    @SuppressWarnings("unchecked")
    public <T> List<T> getCacheList(final String key) {
        Object obj = getCacheObject(key);
        if (obj instanceof List) {
            return (List<T>) obj;
        }
        return new ArrayList<>();
    }

    /**
     * 缓存Set
     *
     * @param key     缓存键值
     * @param dataSet 缓存的数据
     * @return 缓存数据的对象
     */
    public <T> long setCacheSet(final String key, final Set<T> dataSet) {
        cache.put(key, dataSet);
        return dataSet.size();
    }

    /**
     * 获得缓存的set
     *
     * @param key 缓存的key
     * @return set对象
     */
    @SuppressWarnings("unchecked")
    public <T> Set<T> getCacheSet(final String key) {
        Object obj = getCacheObject(key);
        if (obj instanceof Set) {
            return (Set<T>) obj;
        }
        return new HashSet<>();
    }

    /**
     * 缓存Map
     *
     * @param key     缓存的键值
     * @param dataMap 缓存的数据
     */
    public <T> void setCacheMap(final String key, final Map<String, T> dataMap) {
        cache.put(key, dataMap);
    }

    /**
     * 获得缓存的Map
     *
     * @param key 缓存的键值
     * @return map对象
     */
    @SuppressWarnings("unchecked")
    public <T> Map<String, T> getCacheMap(final String key) {
        Object obj = getCacheObject(key);
        if (obj instanceof Map) {
            return (Map<String, T>) obj;
        }
        return new HashMap<>();
    }

    /**
     * 往Hash中存入数据
     *
     * @param key   Redis键
     * @param hKey  Hash键
     * @param value 值
     */
    @SuppressWarnings("unchecked")
    public <T> void setCacheMapValue(final String key, final String hKey, final T value) {
        Map<String, T> map = (Map<String, T>) cache.get(key);
        if (map == null) {
            map = new HashMap<>();
            cache.put(key, map);
        }
        map.put(hKey, value);
    }

    /**
     * 获取Hash中的数据
     *
     * @param key  Redis键
     * @param hKey Hash键
     * @return Hash中的对象
     */
    @SuppressWarnings("unchecked")
    public <T> T getCacheMapValue(final String key, final String hKey) {
        Map<String, T> map = (Map<String, T>) cache.get(key);
        if (map != null) {
            return map.get(hKey);
        }
        return null;
    }

    /**
     * 获取多个Hash中的数据
     *
     * @param key   Redis键
     * @param hKeys Hash键集合
     * @return Hash对象集合
     */
    @SuppressWarnings("unchecked")
    public <T> List<T> getMultiCacheMapValue(final String key, final Collection<Object> hKeys) {
        Map<String, T> map = (Map<String, T>) cache.get(key);
        List<T> result = new ArrayList<>();
        if (map != null) {
            for (Object hKey : hKeys) {
                result.add(map.get(hKey.toString()));
            }
        }
        return result;
    }

    /**
     * 删除Hash中的某条数据
     *
     * @param key  Redis键
     * @param hKey Hash键
     * @return 是否成功
     */
    @SuppressWarnings("unchecked")
    public boolean deleteCacheMapValue(final String key, final String hKey) {
        Map<String, Object> map = (Map<String, Object>) cache.get(key);
        if (map != null) {
            return map.remove(hKey) != null;
        }
        return false;
    }

    /**
     * 获得缓存的基本对象列表
     *
     * @param pattern 字符串前缀
     * @return 对象列表
     */
    public Collection<String> keys(final String pattern) {
        Set<String> result = new HashSet<>();
        for (String key : cache.keySet()) {
            if (hasKey(key) && key.matches(pattern.replace("*", ".*"))) {
                result.add(key);
            }
        }
        return result;
    }

    /**
     * 判断 key是否存在
     *
     * @param key 键
     * @return true 存在 false不存在
     */
    public Boolean hasKey(String key) {
        if (isExpired(key)) {
            deleteObject(key);
            return false;
        }
        return cache.containsKey(key);
    }

    /**
     * 检查key是否过期
     */
    private boolean isExpired(String key) {
        Long expireTimeMillis = expireTime.get(key);
        if (expireTimeMillis != null) {
            return System.currentTimeMillis() > expireTimeMillis;
        }
        return false;
    }
}

<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>数字钥匙系统技术方案</title>
    <!-- 添加jsPlumb库 -->
    <script src="https://cdn.jsdelivr.net/npm/jsplumb@2.15.6/dist/js/jsplumb.min.js"></script>
    <style>
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            line-height: 1.6;
            color: #333;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f9f9f9;
        }
        h1, h2, h3, h4 {
            color: #0066cc;
            margin-top: 30px;
        }
        h1 {
            text-align: center;
            padding: 20px 0;
            background-color: #0066cc;
            color: white;
            border-radius: 8px;
            margin-top: 0;
        }
        
        /* 架构图样式 */
        .architecture-overview {
            margin: 30px 0;
            background-color: white;
            border-radius: 10px;
            padding: 20px;
            box-shadow: 0 4px 12px rgba(0,0,0,0.1);
        }
        .architecture-overview h2 {
            text-align: center;
            margin-bottom: 25px;
            color: #0066cc;
            border-bottom: 2px solid #e0e0e0;
            padding-bottom: 10px;
        }
        .architecture-diagram-container {
            position: relative;
            margin: 20px 0;
            min-height: 600px;
        }
        .architecture-layers {
            display: flex;
            flex-direction: column;
            gap: 40px;
            position: relative;
            z-index: 2;
        }
        .arch-layer-container {
            position: relative;
            width: 100%;
        }
        .arch-layer-title {
            background-color: #0066cc;
            color: white;
            padding: 8px 15px;
            border-radius: 5px;
            display: inline-block;
            margin-bottom: 15px;
            font-weight: bold;
            position: relative;
            z-index: 3;
        }
        .arch-layer-content {
            display: flex;
            justify-content: center;
            gap: 40px;
            flex-wrap: wrap;
            position: relative;
            z-index: 2;
        }
        .arch-component {
            background-color: white;
            border: 2px solid #e0e0e0;
            border-radius: 8px;
            padding: 15px;
            min-width: 120px;
            text-align: center;
            transition: all 0.3s ease;
            box-shadow: 0 2px 6px rgba(0,0,0,0.1);
            position: relative;
            z-index: 3;
        }
        .arch-component:hover {
            transform: translateY(-5px);
            box-shadow: 0 6px 12px rgba(0,0,0,0.15);
        }
        .core-component {
            border-color: #0066cc;
            background-color: #f0f7ff;
        }
        .component-icon {
            font-size: 24px;
            margin-bottom: 10px;
        }
        .component-label {
            font-weight: bold;
            margin-bottom: 8px;
        }
        .component-details {
            display: flex;
            flex-direction: column;
            gap: 5px;
            font-size: 0.9em;
            color: #666;
        }
        .detail-item {
            background-color: #f5f5f5;
            padding: 3px 8px;
            border-radius: 4px;
        }
        
        /* 连接线样式 */
        .connection-container {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            pointer-events: none;
            z-index: 1;
        }
        .connections-svg {
            width: 100%;
            height: 100%;
        }
        .connection-label {
            font-size: 12px;
            font-weight: bold;
        }
        .https-label {
            fill: #0066cc;
        }
        .bluetooth-label {
            fill: #5c6bc0;
        }
        .cellular-label {
            fill: #43a047;
        }
        
        /* 图例样式 */
        .architecture-legend {
            margin-top: 30px;
            background-color: #f5f5f5;
            padding: 15px;
            border-radius: 8px;
        }
        .legend-title {
            font-weight: bold;
            margin-bottom: 10px;
            color: #0066cc;
        }
        .legend-items {
            display: flex;
            flex-wrap: wrap;
            gap: 20px;
        }
        .legend-item {
            display: flex;
            align-items: center;
            gap: 8px;
        }
        .legend-line {
            width: 30px;
            height: 2px;
        }
        .https-line {
            background-color: #0066cc;
        }
        .bluetooth-line {
            background-color: #5c6bc0;
            border-top: 2px dashed #5c6bc0;
        }
        .cellular-line {
            background-color: #43a047;
        }
        .legend-box {
            width: 15px;
            height: 15px;
            border-radius: 3px;
        }
        .core-box {
            background-color: #f0f7ff;
            border: 2px solid #0066cc;
        }
        .normal-box {
            background-color: white;
            border: 2px solid #e0e0e0;
        }
        
        /* 架构说明样式 */
        .architecture-description {
            margin-top: 30px;
            background-color: #f9f9f9;
            padding: 20px;
            border-radius: 8px;
            border-left: 4px solid #0066cc;
        }
        .architecture-description h3 {
            margin-top: 0;
            color: #0066cc;
        }
        .architecture-description ul, 
        .architecture-description ol {
            padding-left: 20px;
        }
        .architecture-description li {
            margin-bottom: 8px;
        }
        
        /* 响应式设计 */
        @media (max-width: 768px) {
            .arch-layer-content {
                flex-direction: column;
                align-items: center;
                gap: 20px;
            }
            .arch-component {
                width: 80%;
            }
        }
        
        /* 原有样式继续保留 */
        .note {
            background-color: #e1f5fe;
            border: 1px solid #a8d1e0;
            border-radius: 8px;
            padding: 15px;
            margin: 15px 0;
            box-shadow: 0 2px 4px rgba(0,0,0,0.05);
        }
        .warning {
            background-color: #fff3cd;
            border: 1px solid #ffeeba;
            border-radius: 8px;
            padding: 15px;
            margin: 15px 0;
            box-shadow: 0 2px 4px rgba(0,0,0,0.05);
        }
        .flow-container {
            background-color: #f5f5f5;
            padding: 20px;
            border-radius: 8px;
            margin: 20px 0;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }
        .flow-item {
            margin-bottom: 15px;
            padding: 15px;
            background-color: white;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            transition: transform 0.2s, box-shadow 0.2s;
        }
        .flow-item:hover {
            transform: translateY(-3px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.15);
        }
        .flow-item-number {
            display: inline-block;
            width: 28px;
            height: 28px;
            background-color: #0066cc;
            color: white;
            text-align: center;
            border-radius: 50%;
            margin-right: 10px;
            font-weight: bold;
            line-height: 28px;
        }
        table {
            width: 100%;
            border-collapse: collapse;
            margin: 20px 0;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            border-radius: 8px;
            overflow: hidden;
        }
        th, td {
            border: 1px solid #ddd;
            padding: 12px 15px;
            text-align: left;
        }
        th {
            background-color: #0066cc;
            color: white;
        }
        tr:nth-child(even) {
            background-color: #f2f2f2;
        }
        tr:hover {
            background-color: #e6f2ff;
        }
        img {
            max-width: 100%;
            height: auto;
            border-radius: 8px;
            box-shadow: 0 4px 8px rgba(0,0,0,0.1);
        }
        .security-box {
            border: 2px solid #28a745;
            border-radius: 8px;
            padding: 20px;
            margin: 20px 0;
            background-color: #f8fff8;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }
        
        /* 新增的模块化架构样式 */
        .architecture-container {
            display: flex;
            flex-wrap: wrap;
            gap: 20px;
            margin: 30px 0;
            justify-content: center;
        }
        
        .architecture-module {
            flex: 1 1 300px;
            max-width: 500px;
            border-radius: 12px;
            overflow: hidden;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
            transition: transform 0.3s, box-shadow 0.3s;
            background-color: white;
        }
        
        .architecture-module:hover {
            transform: translateY(-5px);
            box-shadow: 0 8px 25px rgba(0,0,0,0.15);
        }
        
        .module-header {
            background-color: #0066cc;
            color: white;
            padding: 15px 20px;
            font-size: 1.2em;
            font-weight: bold;
            display: flex;
            align-items: center;
            justify-content: space-between;
        }
        
        .module-icon {
            font-size: 1.5em;
            margin-right: 10px;
        }
        
        .module-body {
            padding: 20px;
        }
        
        .module-list {
            list-style-type: none;
            padding: 0;
            margin: 0;
        }
        
        .module-list li {
            padding: 10px 0;
            border-bottom: 1px solid #eee;
            display: flex;
            align-items: flex-start;
        }
        
        .module-list li:last-child {
            border-bottom: none;
        }
        
        .module-list-icon {
            color: #0066cc;
            margin-right: 10px;
            font-size: 1.1em;
            flex-shrink: 0;
            margin-top: 3px;
        }
        
        .module-footer {
            background-color: #f5f7fa;
            padding: 12px 20px;
            font-size: 0.9em;
            color: #666;
            border-top: 1px solid #eee;
        }
        
        /* 连接线样式 */
        .architecture-connections {
            position: relative;
            margin: 20px 0;
            height: 80px;
            display: flex;
            justify-content: center;
            align-items: center;
        }
        
        .connection-line {
            height: 4px;
            background-color: #0066cc;
            position: absolute;
            top: 50%;
            transform: translateY(-50%);
        }
        
        .connection-vertical {
            width: 4px;
            height: 40px;
            background-color: #0066cc;
            position: absolute;
        }
        
        .connection-arrow {
            width: 0;
            height: 0;
            border-left: 8px solid transparent;
            border-right: 8px solid transparent;
            border-top: 12px solid #0066cc;
            position: absolute;
            bottom: 10px;
        }
        
        /* 响应式调整 */
        @media (max-width: 768px) {
            .architecture-container {
                flex-direction: column;
                align-items: center;
            }
            
            .architecture-module {
                width: 100%;
                max-width: 100%;
            }
        }
        
        /* 原有的模块卡片样式优化 */
        .module-card {
            border: 1px solid #ddd;
            border-radius: 8px;
            margin-bottom: 25px;
            overflow: hidden;
            box-shadow: 0 3px 10px rgba(0,0,0,0.1);
            transition: transform 0.2s, box-shadow 0.2s;
        }
        
        .module-card:hover {
            transform: translateY(-3px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.15);
        }
        
        .module-card-header {
            background-color: #0066cc;
            color: white;
            padding: 12px 18px;
            font-weight: bold;
            font-size: 1.1em;
        }
        
        .module-card-body {
            padding: 18px;
            background-color: white;
        }
        
        /* 交互式折叠面板 */
        .collapsible {
            background-color: #f1f8ff;
            color: #0066cc;
            cursor: pointer;
            padding: 15px;
            width: 100%;
            border: none;
            text-align: left;
            outline: none;
            font-size: 1.1em;
            font-weight: bold;
            border-radius: 8px;
            margin-bottom: 10px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            box-shadow: 0 2px 5px rgba(0,0,0,0.05);
        }
        
        .collapsible:after {
            content: '\002B';
            color: #0066cc;
            font-weight: bold;
            float: right;
            margin-left: 5px;
        }
        
        .active:after {
            content: "\2212";
        }
        
        .collapsible-content {
            padding: 0 18px;
            max-height: 0;
            overflow: hidden;
            transition: max-height 0.3s ease-out;
            background-color: white;
            border-radius: 0 0 8px 8px;
            margin-bottom: 15px;
        }
        
        /* 架构图样式 */
        .architecture-connections-container {
            margin: 40px 0;
            padding: 20px;
            background-color: white;
            border-radius: 12px;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
        }
        
        .architecture-diagram {
            margin: 30px 0;
        }
        
        .diagram-container {
            position: relative;
            height: 450px;
            margin: 0 auto;
            max-width: 800px;
        }
        
        .diagram-module {
            position: absolute;
            width: 150px;
            height: 100px;
            background-color: white;
            border-radius: 10px;
            box-shadow: 0 4px 10px rgba(0,0,0,0.15);
            overflow: hidden;
            transition: transform 0.3s, box-shadow 0.3s;
        }
        
        .diagram-module:hover {
            transform: translateY(-5px);
            box-shadow: 0 8px 20px rgba(0,0,0,0.2);
            z-index: 10;
        }
        
        #mobile-module {
            top: 50px;
            left: 100px;
        }
        
        #car-module {
            top: 50px;
            right: 100px;
        }
        
        #cloud-module {
            bottom: 50px;
            left: 100px;
        }
        
        #external-module {
            bottom: 50px;
            right: 100px;
        }
        
        .diagram-header {
            background-color: #0066cc;
            color: white;
            padding: 8px 10px;
            font-weight: bold;
            text-align: center;
            font-size: 0.9em;
        }
        
        .diagram-body {
            display: flex;
            justify-content: center;
            align-items: center;
            height: calc(100% - 35px);
        }
        
        .diagram-icon {
            font-size: 2em;
        }
        
        .diagram-connections {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            z-index: 1;
        }
        
        .diagram-legend {
            display: flex;
            justify-content: center;
            flex-wrap: wrap;
            gap: 20px;
            margin-top: 20px;
        }
        
        .legend-item {
            display: flex;
            align-items: center;
            margin: 0 10px;
        }
        
        .legend-color {
            width: 20px;
            height: 3px;
            background-color: #0066cc;
            margin-right: 8px;
        }
        
        .legend-icon {
            margin-right: 8px;
            font-size: 1.2em;
        }
        
        .legend-text {
            font-size: 0.9em;
            color: #666;
        }
        
        .connection-text {
            font-size: 12px;
            font-weight: bold;
        }
        
        @media (max-width: 768px) {
            .diagram-container {
                height: 700px;
            }
            
            #mobile-module, #car-module, #cloud-module, #external-module {
                position: relative;
                margin: 20px auto;
                top: auto;
                left: auto;
                right: auto;
                bottom: auto;
            }
            
            .diagram-connections {
                display: none;
            }
        }
        
        /* PPT风格的架构图样式 */
        .ppt-slide {
            background-color: white;
            border-radius: 12px;
            box-shadow: 0 5px 20px rgba(0,0,0,0.15);
            margin: 40px 0;
            overflow: hidden;
            transition: transform 0.3s, box-shadow 0.3s;
        }
        
        .ppt-slide:hover {
            transform: translateY(-5px);
            box-shadow: 0 8px 30px rgba(0,0,0,0.2);
        }
        
        .slide-header {
            background-color: #0066cc;
            color: white;
            padding: 15px 25px;
            text-align: center;
        }
        
        .slide-header h2 {
            margin: 0;
            padding: 0;
            border: none;
            color: white;
            font-size: 1.8em;
        }
        
        .slide-content {
            padding: 30px;
        }
        
        /* 全局架构图样式 */
        .global-architecture {
            position: relative;
            height: 500px;
            margin: 0 auto;
            max-width: 900px;
        }
        
        .arch-layer {
            position: relative;
            width: 100%;
            margin-bottom: 60px;
            text-align: center;
        }
        
        .layer-title {
            background-color: #f1f8ff;
            color: #0066cc;
            padding: 8px 15px;
            border-radius: 20px;
            display: inline-block;
            font-weight: bold;
            margin-bottom: 20px;
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
        }
        
        .layer-modules {
            display: flex;
            justify-content: center;
            gap: 30px;
            flex-wrap: wrap;
        }
        
        .arch-module {
            width: 100px;
            height: 100px;
            background-color: white;
            border-radius: 10px;
            box-shadow: 0 3px 10px rgba(0,0,0,0.1);
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            transition: transform 0.3s, box-shadow 0.3s;
            cursor: pointer;
            position: relative;
            z-index: 2;
        }
        
        .arch-module:hover {
            transform: translateY(-5px) scale(1.05);
            box-shadow: 0 5px 15px rgba(0,0,0,0.15);
        }
        
        .highlight-module {
            border: 2px solid #0066cc;
            box-shadow: 0 3px 15px rgba(0,102,204,0.2);
        }
        
        .module-icon {
            font-size: 2.5em;
            margin-bottom: 8px;
        }
        
        .module-name {
            font-size: 0.9em;
            font-weight: bold;
            color: #333;
        }
        
        /* 连接线样式 */
        .arch-connections {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            z-index: 1;
            pointer-events: none; /* 确保连接线不会阻挡点击事件 */
        }
        
        .connections-svg {
            width: 100%;
            height: 100%;
            overflow: visible; /* 确保SVG内容不会被裁剪 */
        }
        
        .connection-line {
            stroke: #0066cc;
            stroke-width: 3;
            fill: none;
            stroke-dasharray: 5,5;
            animation: dash 30s linear infinite;
        }
        
        @keyframes dash {
            to {
                stroke-dashoffset: 1000;
            }
        }
        
        .connection-text {
            fill: #0066cc;
            font-size: 14px;
            font-weight: bold;
        }
        
        /* 新增：固定层级位置 */
        .user-layer {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
        }
        
        .app-layer {
            position: absolute;
            top: 120px;
            left: 0;
            width: 100%;
        }
        
        .service-layer {
            position: absolute;
            top: 240px;
            left: 0;
            width: 100%;
        }
        
        .car-layer {
            position: absolute;
            top: 360px;
            left: 0;
            width: 100%;
        }
        
        /* 图例样式 */
        .architecture-legend {
            margin-top: 30px;
            text-align: center;
        }
        
        .legend-title {
            font-weight: bold;
            margin-bottom: 10px;
            color: #555;
        }
        
        .legend-items {
            display: flex;
            justify-content: center;
            gap: 20px;
            flex-wrap: wrap;
        }
        
        .legend-item {
            display: flex;
            align-items: center;
            margin: 0 10px;
        }
        
        .legend-color {
            width: 30px;
            height: 3px;
            margin-right: 8px;
        }
        
        .connection-legend {
            background-color: #0066cc;
            height: 3px;
            width: 30px;
        }
        
        .legend-module {
            width: 20px;
            height: 20px;
            border-radius: 4px;
            margin-right: 8px;
        }
        
        .highlight-legend {
            background-color: white;
            border: 2px solid #0066cc;
        }
        
        .normal-legend {
            background-color: white;
            border: 1px solid #ddd;
        }
        
        .legend-text {
            font-size: 0.9em;
            color: #666;
        }
        
        /* 核心组件卡片样式 */
        .core-components {
            display: flex;
            justify-content: space-between;
            gap: 20px;
            flex-wrap: wrap;
        }
        
        .component-card {
            flex: 1 1 250px;
            background-color: white;
            border-radius: 10px;
            box-shadow: 0 3px 15px rgba(0,0,0,0.1);
            overflow: hidden;
            transition: transform 0.3s, box-shadow 0.3s;
        }
        
        .component-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 8px 25px rgba(0,0,0,0.15);
        }
        
        .component-header {
            background-color: #0066cc;
            color: white;
            padding: 15px;
            display: flex;
            align-items: center;
        }
        
        .component-icon {
            font-size: 2em;
            margin-right: 15px;
        }
        
        .component-title {
            font-size: 1.3em;
            font-weight: bold;
        }
        
        .component-body {
            padding: 20px;
        }
        
        .component-body p {
            margin-top: 0;
            margin-bottom: 20px;
            color: #555;
        }
        
        .key-features {
            display: flex;
            flex-wrap: wrap;
            gap: 15px;
        }
        
        .feature {
            display: flex;
            align-items: center;
            background-color: #f5f7fa;
            padding: 8px 15px;
            border-radius: 20px;
            flex: 1 1 auto;
        }
        
        .feature-icon {
            margin-right: 8px;
            font-size: 1.2em;
        }
        
        .feature-text {
            font-size: 0.9em;
            font-weight: bold;
            color: #444;
        }
        
        /* 响应式调整 */
        @media (max-width: 768px) {
            .global-architecture {
                height: auto;
            }
            
            .arch-layer {
                margin-bottom: 30px;
            }
            
            .arch-connections {
                display: none;
            }
            
            .component-card {
                margin-bottom: 20px;
            }
        }
        
        /* 数据流和交互关系样式 */
        .data-flow-container {
            position: relative;
            display: grid;
            grid-template-columns: 1fr 1fr 1fr;
            grid-template-rows: auto auto;
            gap: 30px;
            margin: 30px 0;
        }
        
        .flow-endpoint {
            background-color: white;
            border-radius: 10px;
            box-shadow: 0 3px 15px rgba(0,0,0,0.1);
            padding: 20px;
            text-align: center;
            transition: transform 0.3s, box-shadow 0.3s;
        }
        
        .flow-endpoint:hover {
            transform: translateY(-5px);
            box-shadow: 0 8px 25px rgba(0,0,0,0.15);
        }
        
        #mobile-endpoint {
            grid-column: 1;
            grid-row: 1;
        }
        
        #cloud-endpoint {
            grid-column: 3;
            grid-row: 1;
        }
        
        #car-endpoint {
            grid-column: 2;
            grid-row: 2;
        }
        
        .endpoint-icon {
            font-size: 3em;
            margin-bottom: 10px;
        }
        
        .endpoint-title {
            font-size: 1.2em;
            font-weight: bold;
            margin-bottom: 15px;
            color: #0066cc;
        }
        
        .endpoint-features {
            display: flex;
            flex-direction: column;
            gap: 8px;
        }
        
        .endpoint-feature {
            background-color: #f5f7fa;
            padding: 8px;
            border-radius: 5px;
            font-size: 0.9em;
            color: #444;
        }
        
        .flow-arrows {
            grid-column: 2;
            grid-row: 1;
            display: flex;
            flex-direction: column;
            justify-content: space-around;
            align-items: center;
            gap: 15px;
        }
        
        .flow-group {
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: 5px;
        }
        
        .flow-label {
            font-size: 0.9em;
            font-weight: bold;
            color: #0066cc;
        }
        
        .flow-arrow {
            position: relative;
            width: 150px;
            height: 30px;
        }
        
        .arrow-line {
            position: absolute;
            top: 50%;
            left: 0;
            right: 0;
            height: 3px;
            background-color: #0066cc;
            transform: translateY(-50%);
        }
        
        .right-arrow .arrow-head {
            position: absolute;
            top: 50%;
            right: -5px;
            width: 0;
            height: 0;
            border-top: 8px solid transparent;
            border-bottom: 8px solid transparent;
            border-left: 12px solid #0066cc;
            transform: translateY(-50%);
        }
        
        .left-arrow .arrow-line {
            background-color: #28a745;
        }
        
        .left-arrow .arrow-head {
            position: absolute;
            top: 50%;
            left: -5px;
            width: 0;
            height: 0;
            border-top: 8px solid transparent;
            border-bottom: 8px solid transparent;
            border-right: 12px solid #28a745;
            transform: translateY(-50%);
        }
        
        .diagonal-arrow-down {
            transform: rotate(45deg);
            width: 120px;
        }
        
        .diagonal-arrow-down .arrow-line {
            background-color: #ff9800;
        }
        
        .diagonal-arrow-down .arrow-head {
            position: absolute;
            top: 50%;
            right: -5px;
            width: 0;
            height: 0;
            border-top: 8px solid transparent;
            border-bottom: 8px solid transparent;
            border-left: 12px solid #ff9800;
            transform: translateY(-50%);
        }
        
        .diagonal-arrow-up {
            transform: rotate(-45deg);
            width: 120px;
        }
        
        .diagonal-arrow-up .arrow-line {
            background-color: #9c27b0;
        }
        
        .diagonal-arrow-up .arrow-head {
            position: absolute;
            top: 50%;
            right: -5px;
            width: 0;
            height: 0;
            border-top: 8px solid transparent;
            border-bottom: 8px solid transparent;
            border-left: 12px solid #9c27b0;
            transform: translateY(-50%);
        }
        
        .data-flow-description {
            margin-top: 40px;
            background-color: #f5f7fa;
            padding: 20px;
            border-radius: 10px;
        }
        
        .data-flow-description h3 {
            margin-top: 0;
            color: #0066cc;
            margin-bottom: 15px;
        }
        
        .data-flow-description ol {
            padding-left: 20px;
        }
        
        .data-flow-description li {
            margin-bottom: 10px;
            color: #444;
        }
        
        /* 响应式调整 */
        @media (max-width: 768px) {
            .global-architecture {
                height: auto;
            }
            
            .arch-layer {
                margin-bottom: 30px;
            }
            
            .arch-connections {
                display: none;
            }
            
            .component-card {
                margin-bottom: 20px;
            }
            
            .data-flow-container {
                grid-template-columns: 1fr;
                grid-template-rows: auto auto auto auto;
            }
            
            #mobile-endpoint, #cloud-endpoint, #car-endpoint {
                grid-column: 1;
            }
            
            #mobile-endpoint {
                grid-row: 1;
            }
            
            .flow-arrows {
                grid-column: 1;
                grid-row: 2;
                margin: 20px 0;
            }
            
            #cloud-endpoint {
                grid-row: 3;
            }
            
            #car-endpoint {
                grid-row: 4;
            }
            
            .flow-arrow {
                width: 100px;
            }
        }
        
        /* 数据流程图样式 */
        .section {
            margin: 40px 0;
            background-color: white;
            border-radius: 10px;
            padding: 25px;
            box-shadow: 0 4px 12px rgba(0,0,0,0.1);
        }
        .section h2 {
            text-align: center;
            margin-bottom: 25px;
            color: #0066cc;
            border-bottom: 2px solid #e0e0e0;
            padding-bottom: 10px;
        }
        .section-description {
            margin-bottom: 25px;
            color: #555;
            line-height: 1.6;
        }
        .data-flow-section {
            display: flex;
            flex-direction: column;
            gap: 30px;
        }
        .data-flow-container {
            position: relative;
            display: grid;
            grid-template-columns: 1fr 1fr 1fr;
            gap: 30px;
            padding: 30px;
            background-color: #f9f9f9;
            border-radius: 10px;
            min-height: 400px;
        }
        .flow-endpoint {
            background-color: white;
            border-radius: 10px;
            padding: 20px;
            box-shadow: 0 4px 8px rgba(0,0,0,0.1);
            display: flex;
            flex-direction: column;
            align-items: center;
            transition: all 0.3s ease;
            position: relative;
            z-index: 2;
        }
        .flow-endpoint:hover {
            transform: translateY(-5px);
            box-shadow: 0 8px 16px rgba(0,0,0,0.15);
        }
        .endpoint-icon {
            font-size: 36px;
            margin-bottom: 15px;
        }
        .endpoint-title {
            font-weight: bold;
            font-size: 18px;
            margin-bottom: 15px;
            color: #0066cc;
        }
        .endpoint-features {
            width: 100%;
            display: flex;
            flex-direction: column;
            gap: 8px;
        }
        .feature-item {
            background-color: #f0f7ff;
            border: 1px solid #d0e5ff;
            border-radius: 5px;
            padding: 8px 12px;
            text-align: center;
            font-size: 14px;
        }
        
        /* 数据流箭头样式 */
        .flow-arrow {
            position: absolute;
            z-index: 1;
        }
        .arrow-label {
            background-color: rgba(255,255,255,0.9);
            padding: 5px 10px;
            border-radius: 15px;
            font-size: 12px;
            font-weight: bold;
            color: #0066cc;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            white-space: nowrap;
        }
        .right-arrow {
            height: 2px;
            background-color: #0066cc;
        }
        .right-arrow::after {
            content: '';
            position: absolute;
            right: -10px;
            top: -4px;
            width: 0;
            height: 0;
            border-top: 5px solid transparent;
            border-bottom: 5px solid transparent;
            border-left: 10px solid #0066cc;
        }
        .left-arrow {
            height: 2px;
            background-color: #43a047;
        }
        .left-arrow::after {
            content: '';
            position: absolute;
            left: -10px;
            top: -4px;
            width: 0;
            height: 0;
            border-top: 5px solid transparent;
            border-bottom: 5px solid transparent;
            border-right: 10px solid #43a047;
        }
        .diagonal-arrow {
            height: 2px;
            background-color: #5c6bc0;
            transform-origin: left center;
            transform: rotate(45deg);
        }
        .diagonal-arrow::after {
            content: '';
            position: absolute;
            right: -10px;
            top: -4px;
            width: 0;
            height: 0;
            border-top: 5px solid transparent;
            border-bottom: 5px solid transparent;
            border-left: 10px solid #5c6bc0;
        }
        
        /* 具体箭头位置 */
        .arrow1 {
            width: 25%;
            top: 25%;
            left: 37%;
        }
        .arrow1 .arrow-label {
            position: absolute;
            top: -25px;
            left: 50%;
            transform: translateX(-50%);
        }
        .arrow2 {
            width: 25%;
            top: 35%;
            right: 37%;
        }
        .arrow2 .arrow-label {
            position: absolute;
            top: -25px;
            left: 50%;
            transform: translateX(-50%);
        }
        .arrow3 {
            width: 25%;
            top: 45%;
            left: 37%;
        }
        .arrow3 .arrow-label {
            position: absolute;
            top: 10px;
            left: 50%;
            transform: translateX(-50%);
        }
        .arrow4 {
            width: 25%;
            top: 55%;
            right: 37%;
        }
        .arrow4 .arrow-label {
            position: absolute;
            top: 10px;
            left: 50%;
            transform: translateX(-50%);
        }
        .arrow5 {
            width: 40%;
            top: 65%;
            left: 15%;
            transform: rotate(30deg);
        }
        .arrow5 .arrow-label {
            position: absolute;
            top: -25px;
            left: 50%;
            transform: translateX(-50%) rotate(-30deg);
        }
        .arrow6 {
            width: 25%;
            top: 75%;
            left: 37%;
        }
        .arrow6 .arrow-label {
            position: absolute;
            bottom: 10px;
            left: 50%;
            transform: translateX(-50%);
        }
        .arrow7 {
            width: 25%;
            top: 85%;
            right: 37%;
        }
        .arrow7 .arrow-label {
            position: absolute;
            bottom: 10px;
            left: 50%;
            transform: translateX(-50%);
        }
        
        /* 流程说明样式 */
        .flow-description {
            background-color: #f9f9f9;
            border-radius: 8px;
            padding: 20px;
            border-left: 4px solid #0066cc;
        }
        .flow-description h3 {
            margin-top: 0;
            color: #0066cc;
            margin-bottom: 15px;
        }
        .flow-description ol {
            padding-left: 20px;
        }
        .flow-description li {
            margin-bottom: 10px;
            line-height: 1.6;
        }
        
        /* 响应式设计 */
        @media (max-width: 992px) {
            .data-flow-container {
                grid-template-columns: 1fr;
                padding: 20px;
            }
            .flow-arrow {
                display: none;
            }
        }
        
        /* 系统架构连接关系图 */
        .connection-diagram {
            margin: 40px 0;
            background-color: white;
            border-radius: 10px;
            padding: 25px;
            box-shadow: 0 4px 12px rgba(0,0,0,0.1);
        }
        
        .connection-diagram h2 {
            text-align: center;
            margin-bottom: 25px;
            color: #0066cc;
            border-bottom: 2px solid #e0e0e0;
            padding-bottom: 10px;
        }
        
        .connection-description {
            margin-bottom: 25px;
            color: #555;
            line-height: 1.6;
        }
        
        .connection-diagram-container {
            position: relative;
            margin: 20px 0;
            min-height: 600px;
        }
        
        .connection-layers {
            display: flex;
            flex-direction: column;
            gap: 40px;
            position: relative;
            z-index: 2;
        }
        
        .conn-layer-container {
            position: relative;
            width: 100%;
        }
        
        .conn-layer-title {
            background-color: #0066cc;
            color: white;
            padding: 8px 15px;
            border-radius: 5px;
            display: inline-block;
            margin-bottom: 15px;
            font-weight: bold;
            position: relative;
            z-index: 3;
        }
        
        .conn-layer-content {
            display: flex;
            justify-content: center;
            gap: 40px;
            flex-wrap: wrap;
            position: relative;
            z-index: 2;
        }
        
        .conn-component {
            background-color: white;
            border: 2px solid #e0e0e0;
            border-radius: 8px;
            padding: 15px;
            min-width: 120px;
            text-align: center;
            transition: all 0.3s ease;
            box-shadow: 0 2px 6px rgba(0,0,0,0.1);
            position: relative;
            z-index: 3;
        }
        
        .conn-component:hover {
            transform: translateY(-5px);
            box-shadow: 0 6px 12px rgba(0,0,0,0.15);
        }
        
        .conn-core-component {
            border-color: #0066cc;
            background-color: #f0f7ff;
        }
        
        .conn-component-icon {
            font-size: 24px;
            margin-bottom: 10px;
        }
        
        .conn-component-label {
            font-weight: bold;
            margin-bottom: 8px;
        }
        
        .conn-component-details {
            display: flex;
            flex-direction: column;
            gap: 5px;
            font-size: 0.9em;
            color: #666;
        }
        
        .conn-detail-item {
            background-color: #f5f5f5;
            padding: 3px 8px;
            border-radius: 4px;
        }
        
        /* 连接线样式 */
        .conn-connection-container {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            pointer-events: none;
            z-index: 1;
        }
        
        .conn-connections-svg {
            width: 100%;
            height: 100%;
        }
        
        .conn-connection-label {
            font-size: 12px;
            font-weight: bold;
        }
        
        .conn-https-label {
            fill: #0066cc;
        }
        
        .conn-bluetooth-label {
            fill: #5c6bc0;
        }
        
        .conn-cellular-label {
            fill: #43a047;
        }
        
        /* 图例样式 */
        .conn-architecture-legend {
            margin-top: 30px;
            background-color: #f5f5f5;
            padding: 15px;
            border-radius: 8px;
        }
        
        .conn-legend-title {
            font-weight: bold;
            margin-bottom: 10px;
            color: #0066cc;
        }
        
        .conn-legend-items {
            display: flex;
            flex-wrap: wrap;
            gap: 20px;
        }
        
        .conn-legend-item {
            display: flex;
            align-items: center;
            gap: 8px;
        }
        
        .conn-legend-line {
            width: 30px;
            height: 2px;
        }
        
        .conn-https-line {
            background-color: #0066cc;
        }
        
        .conn-bluetooth-line {
            background-color: #5c6bc0;
            border-top: 2px dashed #5c6bc0;
        }
        
        .conn-cellular-line {
            background-color: #43a047;
        }
        
        .conn-legend-box {
            width: 15px;
            height: 15px;
            border-radius: 3px;
        }
        
        .conn-core-box {
            background-color: #f0f7ff;
            border: 2px solid #0066cc;
        }
        
        .conn-normal-box {
            background-color: white;
            border: 2px solid #e0e0e0;
        }
        
        /* 架构说明样式 */
        .conn-architecture-description {
            margin-top: 30px;
            background-color: #f9f9f9;
            padding: 20px;
            border-radius: 8px;
            border-left: 4px solid #0066cc;
        }
        
        .conn-architecture-description h3 {
            margin-top: 0;
            color: #0066cc;
        }
        
        .conn-architecture-description ul, 
        .conn-architecture-description ol {
            padding-left: 20px;
        }
        
        .conn-architecture-description li {
            margin-bottom: 8px;
        }
        
        /* 响应式设计 */
        @media (max-width: 768px) {
            .conn-layer-content {
                flex-direction: column;
                align-items: center;
                gap: 20px;
            }
            .conn-component {
                width: 80%;
            }
        }
        
        /* 原有样式继续保留 */
        .conn-note {
            background-color: #e1f5fe;
            border: 1px solid #a8d1e0;
            border-radius: 8px;
            padding: 15px;
            margin: 15px 0;
            box-shadow: 0 2px 4px rgba(0,0,0,0.05);
        }
        
        .conn-warning {
            background-color: #fff3cd;
            border: 1px solid #ffeeba;
            border-radius: 8px;
            padding: 15px;
            margin: 15px 0;
            box-shadow: 0 2px 4px rgba(0,0,0,0.05);
        }
        
        .conn-flow-container {
            background-color: #f5f5f5;
            padding: 20px;
            border-radius: 8px;
            margin: 20px 0;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }
        
        .conn-flow-item {
            margin-bottom: 15px;
            padding: 15px;
            background-color: white;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            transition: transform 0.2s, box-shadow 0.2s;
        }
        
        .conn-flow-item:hover {
            transform: translateY(-3px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.15);
        }
        
        .conn-flow-item-number {
            display: inline-block;
            width: 28px;
            height: 28px;
            background-color: #0066cc;
            color: white;
            text-align: center;
            border-radius: 50%;
            margin-right: 10px;
            font-weight: bold;
            line-height: 28px;
        }
        
        .conn-table {
            width: 100%;
            border-collapse: collapse;
            margin: 20px 0;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            border-radius: 8px;
            overflow: hidden;
        }
        
        .conn-th, .conn-td {
            border: 1px solid #ddd;
            padding: 12px 15px;
            text-align: left;
        }
        
        .conn-th {
            background-color: #0066cc;
            color: white;
        }
        
        .conn-tr:nth-child(even) {
            background-color: #f2f2f2;
        }
        
        .conn-tr:hover {
            background-color: #e6f2ff;
        }
        
        .conn-img {
            max-width: 100%;
            height: auto;
            border-radius: 8px;
            box-shadow: 0 4px 8px rgba(0,0,0,0.1);
        }
        
        .conn-security-box {
            border: 2px solid #28a745;
            border-radius: 8px;
            padding: 20px;
            margin: 20px 0;
            background-color: #f8fff8;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }
        
        /* 新增的模块化架构样式 */
        .conn-architecture-container {
            display: flex;
            flex-wrap: wrap;
            gap: 20px;
            margin: 30px 0;
            justify-content: center;
        }
        
        .conn-architecture-module {
            flex: 1 1 300px;
            max-width: 500px;
            border-radius: 12px;
            overflow: hidden;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
            transition: transform 0.3s, box-shadow 0.3s;
            background-color: white;
        }
        
        .conn-architecture-module:hover {
            transform: translateY(-5px);
            box-shadow: 0 8px 25px rgba(0,0,0,0.15);
        }
        
        .conn-module-header {
            background-color: #0066cc;
            color: white;
            padding: 15px 20px;
            font-size: 1.2em;
            font-weight: bold;
            display: flex;
            align-items: center;
            justify-content: space-between;
        }
        
        .conn-module-icon {
            font-size: 1.5em;
            margin-right: 10px;
        }
        
        .conn-module-body {
            padding: 20px;
        }
        
        .conn-module-list {
            list-style-type: none;
            padding: 0;
            margin: 0;
        }
        
        .conn-module-list li {
            padding: 10px 0;
            border-bottom: 1px solid #eee;
            display: flex;
            align-items: flex-start;
        }
        
        .conn-module-list li:last-child {
            border-bottom: none;
        }
        
        .conn-module-list-icon {
            color: #0066cc;
            margin-right: 10px;
            font-size: 1.1em;
            flex-shrink: 0;
            margin-top: 3px;
        }
        
        .conn-module-footer {
            background-color: #f5f7fa;
            padding: 12px 20px;
            font-size: 0.9em;
            color: #666;
            border-top: 1px solid #eee;
        }
        
        /* 连接线样式 */
        .conn-architecture-connections {
            position: relative;
            margin: 20px 0;
            height: 80px;
            display: flex;
            justify-content: center;
            align-items: center;
        }
        
        .conn-connection-line {
            height: 4px;
            background-color: #0066cc;
            position: absolute;
            top: 50%;
            transform: translateY(-50%);
        }
        
        .conn-connection-vertical {
            width: 4px;
            height: 40px;
            background-color: #0066cc;
            position: absolute;
        }
        
        .conn-connection-arrow {
            width: 0;
            height: 0;
            border-left: 8px solid transparent;
            border-right: 8px solid transparent;
            border-top: 12px solid #0066cc;
            position: absolute;
            bottom: 10px;
        }
        
        /* 响应式调整 */
        @media (max-width: 768px) {
            .conn-architecture-container {
                flex-direction: column;
                align-items: center;
            }
            
            .conn-architecture-module {
                width: 100%;
                max-width: 100%;
            }
        }
        
        /* 原有的模块卡片样式优化 */
        .conn-module-card {
            border: 1px solid #ddd;
            border-radius: 8px;
            margin-bottom: 25px;
            overflow: hidden;
            box-shadow: 0 3px 10px rgba(0,0,0,0.1);
            transition: transform 0.2s, box-shadow 0.2s;
        }
        
        .conn-module-card:hover {
            transform: translateY(-3px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.15);
        }
        
        .conn-module-card-header {
            background-color: #0066cc;
            color: white;
            padding: 12px 18px;
            font-weight: bold;
            font-size: 1.1em;
        }
        
        .conn-module-card-body {
            padding: 18px;
            background-color: white;
        }
        
        /* 交互式折叠面板 */
        .conn-collapsible {
            background-color: #f1f8ff;
            color: #0066cc;
            cursor: pointer;
            padding: 15px;
            width: 100%;
            border: none;
            text-align: left;
            outline: none;
            font-size: 1.1em;
            font-weight: bold;
            border-radius: 8px;
            margin-bottom: 10px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            box-shadow: 0 2px 5px rgba(0,0,0,0.05);
        }
        
        .conn-collapsible:after {
            content: '\002B';
            color: #0066cc;
            font-weight: bold;
            float: right;
            margin-left: 5px;
        }
        
        .conn-active:after {
            content: "\2212";
        }
        
        .conn-collapsible-content {
            padding: 0 18px;
            max-height: 0;
            overflow: hidden;
            transition: max-height 0.3s ease-out;
            background-color: white;
            border-radius: 0 0 8px 8px;
            margin-bottom: 15px;
        }
        
        /* 架构图样式 */
        .conn-architecture-connections-container {
            margin: 40px 0;
            padding: 20px;
            background-color: white;
            border-radius: 12px;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
        }
        
        .conn-architecture-diagram {
            margin: 30px 0;
        }
        
        .conn-diagram-container {
            position: relative;
            height: 450px;
            margin: 0 auto;
            max-width: 800px;
        }
        
        .conn-diagram-module {
            position: absolute;
            width: 150px;
            height: 100px;
            background-color: white;
            border-radius: 10px;
            box-shadow: 0 4px 10px rgba(0,0,0,0.15);
            overflow: hidden;
            transition: transform 0.3s, box-shadow 0.3s;
        }
        
        .conn-diagram-module:hover {
            transform: translateY(-5px);
            box-shadow: 0 8px 20px rgba(0,0,0,0.2);
            z-index: 10;
        }
        
        #conn-mobile-module {
            top: 50px;
            left: 100px;
        }
        
        #conn-car-module {
            top: 50px;
            right: 100px;
        }
        
        #conn-cloud-module {
            bottom: 50px;
            left: 100px;
        }
        
        #conn-external-module {
            bottom: 50px;
            right: 100px;
        }
        
        .conn-diagram-header {
            background-color: #0066cc;
            color: white;
            padding: 8px 10px;
            font-weight: bold;
            text-align: center;
            font-size: 0.9em;
        }
        
        .conn-diagram-body {
            display: flex;
            justify-content: center;
            align-items: center;
            height: calc(100% - 35px);
        }
        
        .conn-diagram-icon {
            font-size: 2em;
        }
        
        .conn-connection-lines {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            z-index: 1;
        }
        
        .conn-diagram-legend {
            display: flex;
            justify-content: center;
            flex-wrap: wrap;
            gap: 20px;
            margin-top: 20px;
        }
        
        .conn-legend-item {
            display: flex;
            align-items: center;
            margin: 0 10px;
        }
        
        .conn-legend-color {
            width: 20px;
            height: 3px;
            margin-right: 8px;
        }
        
        .conn-legend-icon {
            margin-right: 8px;
            font-size: 1.2em;
        }
        
        .conn-legend-text {
            font-size: 0.9em;
            color: #666;
        }
        
        @media (max-width: 768px) {
            .conn-diagram-container {
                height: 700px;
            }
            
            #conn-mobile-module, #conn-car-module, #conn-cloud-module, #conn-external-module {
                position: relative;
                margin: 20px auto;
                top: auto;
                left: auto;
                right: auto;
                bottom: auto;
            }
            
            .conn-connection-lines {
                display: none;
            }
        }
        
        /* PPT风格的架构图样式 */
        .conn-ppt-slide {
            background-color: white;
            border-radius: 12px;
            box-shadow: 0 5px 20px rgba(0,0,0,0.15);
            margin: 40px 0;
            overflow: hidden;
            transition: transform 0.3s, box-shadow 0.3s;
        }
        
        .conn-ppt-slide:hover {
            transform: translateY(-5px);
            box-shadow: 0 8px 30px rgba(0,0,0,0.2);
        }
        
        .conn-slide-header {
            background-color: #0066cc;
            color: white;
            padding: 15px 25px;
            text-align: center;
        }
        
        .conn-slide-header h2 {
            margin: 0;
            padding: 0;
            border: none;
            color: white;
            font-size: 1.8em;
        }
        
        .conn-slide-content {
            padding: 30px;
        }
        
        /* 全局架构图样式 */
        .conn-global-architecture {
            position: relative;
            height: 500px;
            margin: 0 auto;
            max-width: 900px;
        }
        
        .conn-arch-layer {
            position: relative;
            width: 100%;
            margin-bottom: 60px;
            text-align: center;
        }
        
        .conn-layer-title {
            background-color: #f1f8ff;
            color: #0066cc;
            padding: 8px 15px;
            border-radius: 20px;
            display: inline-block;
            font-weight: bold;
            margin-bottom: 20px;
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
        }
        
        .conn-layer-modules {
            display: flex;
            justify-content: center;
            gap: 30px;
            flex-wrap: wrap;
        }
        
        .conn-arch-module {
            width: 100px;
            height: 100px;
            background-color: white;
            border-radius: 10px;
            box-shadow: 0 3px 10px rgba(0,0,0,0.1);
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            transition: transform 0.3s, box-shadow 0.3s;
            cursor: pointer;
            position: relative;
            z-index: 2;
        }
        
        .conn-arch-module:hover {
            transform: translateY(-5px) scale(1.05);
            box-shadow: 0 5px 15px rgba(0,0,0,0.15);
        }
        
        .conn-highlight-module {
            border: 2px solid #0066cc;
            box-shadow: 0 3px 15px rgba(0,102,204,0.2);
        }
        
        .conn-module-icon {
            font-size: 2.5em;
            margin-bottom: 8px;
        }
        
        .conn-module-name {
            font-size: 0.9em;
            font-weight: bold;
            color: #333;
        }
        
        /* 连接线样式 */
        .conn-arch-connections {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            z-index: 1;
            pointer-events: none; /* 确保连接线不会阻挡点击事件 */
        }
        
        .conn-connections-svg {
            width: 100%;
            height: 100%;
            overflow: visible; /* 确保SVG内容不会被裁剪 */
        }
        
        .conn-connection-line {
            stroke: #0066cc;
            stroke-width: 3;
            fill: none;
            stroke-dasharray: 5,5;
            animation: dash 30s linear infinite;
        }
        
        @keyframes dash {
            to {
                stroke-dashoffset: 1000;
            }
        }
        
        .conn-connection-text {
            fill: #0066cc;
            font-size: 14px;
            font-weight: bold;
        }
        
        /* 新增：固定层级位置 */
        .conn-user-layer {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
        }
        
        .conn-app-layer {
            position: absolute;
            top: 120px;
            left: 0;
            width: 100%;
        }
        
        .conn-service-layer {
            position: absolute;
            top: 240px;
            left: 0;
            width: 100%;
        }
        
        .conn-car-layer {
            position: absolute;
            top: 360px;
            left: 0;
            width: 100%;
        }
        
        /* 图例样式 */
        .conn-architecture-legend {
            margin-top: 30px;
            text-align: center;
        }
        
        .conn-legend-title {
            font-weight: bold;
            margin-bottom: 10px;
            color: #555;
        }
        
        .conn-legend-items {
            display: flex;
            justify-content: center;
            gap: 20px;
            flex-wrap: wrap;
        }
        
        .conn-legend-item {
            display: flex;
            align-items: center;
            margin: 0 10px;
        }
        
        .conn-legend-color {
            width: 30px;
            height: 3px;
            margin-right: 8px;
        }
        
        .conn-connection-legend {
            background-color: #0066cc;
            height: 3px;
            width: 30px;
        }
        
        .conn-legend-module {
            width: 20px;
            height: 20px;
            border-radius: 4px;
            margin-right: 8px;
        }
        
        .conn-highlight-legend {
            background-color: white;
            border: 2px solid #0066cc;
        }
        
        .conn-normal-legend {
            background-color: white;
            border: 1px solid #ddd;
        }
        
        .conn-legend-text {
            font-size: 0.9em;
            color: #666;
        }
        
        /* 核心组件卡片样式 */
        .conn-core-components {
            display: flex;
            justify-content: space-between;
            gap: 20px;
            flex-wrap: wrap;
        }
        
        .conn-component-card {
            flex: 1 1 250px;
            background-color: white;
            border-radius: 10px;
            box-shadow: 0 3px 15px rgba(0,0,0,0.1);
            overflow: hidden;
            transition: transform 0.3s, box-shadow 0.3s;
        }
        
        .conn-component-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 8px 25px rgba(0,0,0,0.15);
        }
        
        .conn-component-header {
            background-color: #0066cc;
            color: white;
            padding: 15px;
            display: flex;
            align-items: center;
        }
        
        .conn-component-icon {
            font-size: 2em;
            margin-right: 15px;
        }
        
        .conn-component-title {
            font-size: 1.3em;
            font-weight: bold;
        }
        
        .conn-component-body {
            padding: 20px;
        }
        
        .conn-component-body p {
            margin-top: 0;
            margin-bottom: 20px;
            color: #555;
        }
        
        .conn-key-features {
            display: flex;
            flex-wrap: wrap;
            gap: 15px;
        }
        
        .conn-feature {
            display: flex;
            align-items: center;
            background-color: #f5f7fa;
            padding: 8px 15px;
            border-radius: 20px;
            flex: 1 1 auto;
        }
        
        .conn-feature-icon {
            margin-right: 8px;
            font-size: 1.2em;
        }
        
        .conn-feature-text {
            font-size: 0.9em;
            font-weight: bold;
            color: #444;
        }
        
        /* 响应式调整 */
        @media (max-width: 768px) {
            .conn-global-architecture {
                height: auto;
            }
            
            .conn-arch-layer {
                margin-bottom: 30px;
            }
            
            .conn-arch-connections {
                display: none;
            }
            
            .conn-component-card {
                margin-bottom: 20px;
            }
        }
        
        /* 数据流和交互关系样式 */
        .conn-data-flow-container {
            position: relative;
            display: grid;
            grid-template-columns: 1fr 1fr 1fr;
            grid-template-rows: auto auto;
            gap: 30px;
            margin: 30px 0;
        }
        
        .conn-flow-endpoint {
            background-color: white;
            border-radius: 10px;
            box-shadow: 0 3px 15px rgba(0,0,0,0.1);
            padding: 20px;
            text-align: center;
            transition: transform 0.3s, box-shadow 0.3s;
        }
        
        .conn-flow-endpoint:hover {
            transform: translateY(-5px);
            box-shadow: 0 8px 25px rgba(0,0,0,0.15);
        }
        
        #conn-mobile-endpoint {
            grid-column: 1;
            grid-row: 1;
        }
        
        #conn-cloud-endpoint {
            grid-column: 3;
            grid-row: 1;
        }
        
        #conn-car-endpoint {
            grid-column: 2;
            grid-row: 2;
        }
        
        .conn-endpoint-icon {
            font-size: 3em;
            margin-bottom: 10px;
        }
        
        .conn-endpoint-title {
            font-size: 1.2em;
            font-weight: bold;
            margin-bottom: 15px;
            color: #0066cc;
        }
        
        .conn-endpoint-features {
            display: flex;
            flex-direction: column;
            gap: 8px;
        }
        
        .conn-endpoint-feature {
            background-color: #f5f7fa;
            padding: 8px;
            border-radius: 5px;
            font-size: 0.9em;
            color: #444;
        }
        
        .conn-flow-arrows {
            grid-column: 2;
            grid-row: 1;
            display: flex;
            flex-direction: column;
            justify-content: space-around;
            align-items: center;
            gap: 15px;
        }
        
        .conn-flow-group {
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: 5px;
        }
        
        .conn-flow-label {
            font-size: 0.9em;
            font-weight: bold;
            color: #0066cc;
        }
        
        .conn-flow-arrow {
            position: relative;
            width: 150px;
            height: 30px;
        }
        
        .conn-arrow-line {
            position: absolute;
            top: 50%;
            left: 0;
            right: 0;
            height: 3px;
            background-color: #0066cc;
            transform: translateY(-50%);
        }
        
        .conn-right-arrow .conn-arrow-head {
            position: absolute;
            top: 50%;
            right: -5px;
            width: 0;
            height: 0;
            border-top: 8px solid transparent;
            border-bottom: 8px solid transparent;
            border-left: 12px solid #0066cc;
            transform: translateY(-50%);
        }
        
        .conn-left-arrow .conn-arrow-line {
            background-color: #28a745;
        }
        
        .conn-left-arrow .conn-arrow-head {
            position: absolute;
            top: 50%;
            left: -5px;
            width: 0;
            height: 0;
            border-top: 8px solid transparent;
            border-bottom: 8px solid transparent;
            border-right: 12px solid #28a745;
            transform: translateY(-50%);
        }
        
        .conn-diagonal-arrow-down {
            transform: rotate(45deg);
            width: 120px;
        }
        
        .conn-diagonal-arrow-down .conn-arrow-line {
            background-color: #ff9800;
        }
        
        .conn-diagonal-arrow-down .conn-arrow-head {
            position: absolute;
            top: 50%;
            right: -5px;
            width: 0;
            height: 0;
            border-top: 8px solid transparent;
            border-bottom: 8px solid transparent;
            border-left: 12px solid #ff9800;
            transform: translateY(-50%);
        }
        
        .conn-diagonal-arrow-up {
            transform: rotate(-45deg);
            width: 120px;
        }
        
        .conn-diagonal-arrow-up .conn-arrow-line {
            background-color: #9c27b0;
        }
        
        .conn-diagonal-arrow-up .conn-arrow-head {
            position: absolute;
            top: 50%;
            right: -5px;
            width: 0;
            height: 0;
            border-top: 8px solid transparent;
            border-bottom: 8px solid transparent;
            border-left: 12px solid #9c27b0;
            transform: translateY(-50%);
        }
        
        .conn-data-flow-description {
            margin-top: 40px;
            background-color: #f5f7fa;
            padding: 20px;
            border-radius: 10px;
        }
        
        .conn-data-flow-description h3 {
            margin-top: 0;
            color: #0066cc;
            margin-bottom: 15px;
        }
        
        .conn-data-flow-description ol {
            padding-left: 20px;
        }
        
        .conn-data-flow-description li {
            margin-bottom: 10px;
            color: #444;
        }
        
        /* 响应式调整 */
        @media (max-width: 768px) {
            .conn-global-architecture {
                height: auto;
            }
            
            .conn-arch-layer {
                margin-bottom: 30px;
            }
            
            .conn-arch-connections {
                display: none;
            }
            
            .conn-component-card {
                margin-bottom: 20px;
            }
            
            .conn-data-flow-container {
                grid-template-columns: 1fr;
                grid-template-rows: auto auto auto auto;
            }
            
            #conn-mobile-endpoint, #conn-cloud-endpoint, #conn-car-endpoint {
                grid-column: 1;
            }
            
            #conn-mobile-endpoint {
                grid-row: 1;
            }
            
            .conn-flow-arrows {
                grid-column: 1;
                grid-row: 2;
                margin: 20px 0;
            }
            
            #conn-cloud-endpoint {
                grid-row: 3;
            }
            
            #conn-car-endpoint {
                grid-row: 4;
            }
            
            .conn-flow-arrow {
                width: 100px;
            }
        }
    </style>
</head>
<body>

<h1>数字钥匙系统技术方案</h1>

<div class="note">
    <strong>项目简介：</strong>数字钥匙系统是一套完整的汽车数字钥匙解决方案，包含手机APP对应的SDK、云平台SDK以及完整的车端软硬件开发的集成产品。该系统允许用户通过手机APP远程或近场控制车辆，实现传统物理钥匙的数字化替代。
</div>

<!-- 新增专业架构图展示 -->
<div class="architecture-overview">
    <h2>系统整体架构</h2>
    <div class="architecture-diagram-container">
        <div class="architecture-layers" id="architecture-canvas">
            <!-- 用户层 -->
            <div class="arch-layer-container">
                <div class="arch-layer-title">用户层</div>
                <div class="arch-layer-content">
                    <div class="arch-component primary-user" id="component-car-owner">
                        <div class="component-icon">👤</div>
                        <div class="component-label">车主</div>
                    </div>
                    <div class="arch-component secondary-user" id="component-shared-user">
                        <div class="component-icon">👥</div>
                        <div class="component-label">共享用户</div>
                    </div>
                </div>
            </div>
            
            <!-- 应用层 -->
            <div class="arch-layer-container">
                <div class="arch-layer-title">应用层</div>
                <div class="arch-layer-content">
                    <div class="arch-component core-component" id="component-mobile">
                        <div class="component-icon">📱</div>
                        <div class="component-label">手机端</div>
                        <div class="component-details">
                            <span class="detail-item">钥匙管理</span>
                            <span class="detail-item">车辆控制</span>
                            <span class="detail-item">安全存储</span>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- 服务层 -->
            <div class="arch-layer-container">
                <div class="arch-layer-title">服务层</div>
                <div class="arch-layer-content">
                    <div class="arch-component core-component" id="component-key-cloud">
                        <div class="component-icon">☁️</div>
                        <div class="component-label">钥匙云平台</div>
                        <div class="component-details">
                            <span class="detail-item">钥匙生成</span>
                            <span class="detail-item">安全认证</span>
                            <span class="detail-item">权限管理</span>
                        </div>
                    </div>
                    <div class="arch-component" id="component-tsp">
                        <div class="component-icon">🌐</div>
                        <div class="component-label">TSP平台</div>
                    </div>
                    <div class="arch-component" id="component-oem">
                        <div class="component-icon">🏭</div>
                        <div class="component-label">OEM平台</div>
                    </div>
                </div>
            </div>
            
            <!-- 车端层 -->
            <div class="arch-layer-container">
                <div class="arch-layer-title">车端层</div>
                <div class="arch-layer-content">
                    <div class="arch-component core-component" id="component-car">
                        <div class="component-icon">🚗</div>
                        <div class="component-label">车端</div>
                        <div class="component-details">
                            <span class="detail-item">钥匙验证</span>
                            <span class="detail-item">指令执行</span>
                            <span class="detail-item">通信模块</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- 图例 -->
    <div class="architecture-legend">
        <div class="legend-title">图例说明</div>
        <div class="legend-items">
            <div class="legend-item">
                <div class="legend-line https-line"></div>
                <div class="legend-text">HTTPS通信</div>
            </div>
            <div class="legend-item">
                <div class="legend-line bluetooth-line"></div>
                <div class="legend-text">蓝牙通信</div>
            </div>
            <div class="legend-item">
                <div class="legend-line cellular-line"></div>
                <div class="legend-text">4G/5G通信</div>
            </div>
            <div class="legend-item">
                <div class="legend-box core-box"></div>
                <div class="legend-text">核心组件</div>
            </div>
            <div class="legend-item">
                <div class="legend-box normal-box"></div>
                <div class="legend-text">外部组件</div>
            </div>
        </div>
    </div>
    
    <!-- 架构说明 -->
    <div class="architecture-description">
        <h3>架构设计说明</h3>
        <p>数字钥匙系统采用分层架构设计，从上至下分为用户层、应用层、服务层和车端层，各层之间通过标准化接口进行通信：</p>
        <ul>
            <li><strong>用户层</strong>：包括车主和共享用户，他们通过手机APP与系统交互</li>
            <li><strong>应用层</strong>：手机端APP，负责用户交互、钥匙管理和车辆控制，通过HTTPS与云平台通信，通过蓝牙与车端直接通信</li>
            <li><strong>服务层</strong>：包括核心的钥匙云平台和外部的TSP平台、OEM平台，负责钥匙生命周期管理、安全认证和数据处理</li>
            <li><strong>车端层</strong>：车载设备，负责验证钥匙并执行控制指令，可通过蓝牙与手机直接通信，也可通过4G/5G与云平台通信</li>
        </ul>
        <p>系统支持两种控车模式：</p>
        <ol>
            <li><strong>近场控车</strong>：手机通过蓝牙直接与车辆通信，实现低延迟的控车体验</li>
            <li><strong>远程控车</strong>：手机通过HTTPS与云平台通信，云平台再通过4G/5G与车辆通信</li>
        </ol>
    </div>
</div>

<!-- 新增PPT风格的整体架构图 -->
<div class="ppt-slide">
    <div class="slide-header">
        <h2>数字钥匙系统整体架构</h2>
    </div>
    <div class="slide-content">
        <div class="global-architecture">
            <!-- 顶部层：用户层 -->
            <div class="arch-layer user-layer">
                <div class="layer-title">用户层</div>
                <div class="layer-modules">
                    <div class="arch-module">
                        <div class="module-icon">👤</div>
                        <div class="module-name">车主</div>
                    </div>
                    <div class="arch-module">
                        <div class="module-icon">👥</div>
                        <div class="module-name">共享用户</div>
                    </div>
                </div>
            </div>
            
            <!-- 第二层：应用层 -->
            <div class="arch-layer app-layer">
                <div class="layer-title">应用层</div>
                <div class="layer-modules">
                    <div class="arch-module highlight-module">
                        <div class="module-icon">📱</div>
                        <div class="module-name">手机端</div>
                    </div>
                </div>
            </div>
            
            <!-- 第三层：服务层 -->
            <div class="arch-layer service-layer">
                <div class="layer-title">服务层</div>
                <div class="layer-modules">
                    <div class="arch-module highlight-module">
                        <div class="module-icon">☁️</div>
                        <div class="module-name">钥匙云平台</div>
                    </div>
                    <div class="arch-module">
                        <div class="module-icon">🌐</div>
                        <div class="module-name">TSP平台</div>
                    </div>
                    <div class="arch-module">
                        <div class="module-icon">🏭</div>
                        <div class="module-name">OEM平台</div>
                    </div>
                </div>
            </div>
            
            <!-- 第四层：车端层 -->
            <div class="arch-layer car-layer">
                <div class="layer-title">车端层</div>
                <div class="layer-modules">
                    <div class="arch-module highlight-module">
                        <div class="module-icon">🚗</div>
                        <div class="module-name">车端</div>
                    </div>
                </div>
            </div>
            
            <!-- 连接线 -->
            <div class="arch-connections">
                <svg class="connections-svg" viewBox="0 0 1000 600" preserveAspectRatio="xMidYMid meet">
                    <!-- 箭头标记定义 -->
                    <defs>
                        <marker id="arrowhead" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
                            <polygon points="0 0, 10 3.5, 0 7" fill="#0066cc" />
                        </marker>
                    </defs>
                    
                    <!-- 用户层到应用层 -->
                    <path d="M500,100 L500,140" class="connection-line" marker-end="url(#arrowhead)" />
                    
                    <!-- 应用层到服务层 -->
                    <path d="M500,220 L500,260" class="connection-line" marker-end="url(#arrowhead)" />
                    <text x="520" y="240" class="connection-text">HTTPS</text>
                    
                    <!-- 服务层到车端层 -->
                    <path d="M500,340 L500,380" class="connection-line" marker-end="url(#arrowhead)" />
                    <text x="520" y="360" class="connection-text">4G/5G</text>
                    
                    <!-- 应用层到车端层 -->
                    <path d="M400,220 C350,300 350,350 400,380" class="connection-line" marker-end="url(#arrowhead)" />
                    <text x="320" y="300" class="connection-text">蓝牙</text>
                    
                    <!-- TSP平台到车端 -->
                    <path d="M600,320 C650,350 650,380 600,400" class="connection-line" marker-end="url(#arrowhead)" />
                    <text x="660" y="360" class="connection-text">远程控制</text>
                </svg>
            </div>
        </div>
        
        <div class="architecture-legend">
            <div class="legend-title">图例说明</div>
            <div class="legend-items">
                <div class="legend-item">
                    <div class="legend-color connection-legend"></div>
                    <div class="legend-text">数据流向</div>
                </div>
                <div class="legend-item">
                    <div class="legend-module highlight-legend"></div>
                    <div class="legend-text">核心组件</div>
                </div>
                <div class="legend-item">
                    <div class="legend-module normal-legend"></div>
                    <div class="legend-text">外部组件</div>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="ppt-slide">
    <div class="slide-header">
        <h2>核心组件功能概述</h2>
    </div>
    <div class="slide-content">
        <div class="core-components">
            <div class="component-card">
                <div class="component-header">
                    <div class="component-icon">📱</div>
                    <div class="component-title">手机端</div>
                </div>
                <div class="component-body">
                    <p>负责用户交互、钥匙管理和车辆控制，通过蓝牙与车辆通信，通过网络与云平台交互。</p>
                    <div class="key-features">
                        <div class="feature">
                            <div class="feature-icon">🔑</div>
                            <div class="feature-text">钥匙管理</div>
                        </div>
                        <div class="feature">
                            <div class="feature-icon">🚗</div>
                            <div class="feature-text">车辆控制</div>
                        </div>
                        <div class="feature">
                            <div class="feature-icon">🔒</div>
                            <div class="feature-text">安全存储</div>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="component-card">
                <div class="component-header">
                    <div class="component-icon">☁️</div>
                    <div class="component-title">钥匙云平台</div>
                </div>
                <div class="component-body">
                    <p>系统的核心服务中心，负责钥匙生命周期管理、安全认证和数据处理。</p>
                    <div class="key-features">
                        <div class="feature">
                            <div class="feature-icon">🔑</div>
                            <div class="feature-text">钥匙生成</div>
                        </div>
                        <div class="feature">
                            <div class="feature-icon">🔒</div>
                            <div class="feature-text">安全认证</div>
                        </div>
                        <div class="feature">
                            <div class="feature-icon">🌐</div>
                            <div class="feature-text">接口服务</div>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="component-card">
                <div class="component-header">
                    <div class="component-icon">🚗</div>
                    <div class="component-title">车端</div>
                </div>
                <div class="component-body">
                    <p>安装在车辆上的硬件和软件系统，负责验证钥匙并执行控制指令。</p>
                    <div class="key-features">
                        <div class="feature">
                            <div class="feature-icon">🔒</div>
                            <div class="feature-text">钥匙验证</div>
                        </div>
                        <div class="feature">
                            <div class="feature-icon">⚙️</div>
                            <div class="feature-text">指令执行</div>
                        </div>
                        <div class="feature">
                            <div class="feature-icon">📡</div>
                            <div class="feature-text">通信模块</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<h2>一、系统架构概述</h2>

<p>数字钥匙系统由以下几个主要部分组成：</p>

<div class="architecture-container">
    <div class="architecture-module">
        <div class="module-header">
            <span class="module-icon">📱</span>
            <span>手机端</span>
        </div>
        <div class="module-body">
            <ul class="module-list">
                <li class="module-list-item">
                    <span class="module-list-icon">🔑</span>
                    <strong>钥匙管理模块</strong>：管理用户的数字钥匙，包括添加、删除、共享等功能
                </li>
                <li class="module-list-item">
                    <span class="module-list-icon">🚗</span>
                    <strong>车辆控制模块</strong>：提供车辆控制功能，如开锁、关锁、启动等
                </li>
                <li class="module-list-item">
                    <span class="module-list-icon">🎧</span>
                    <strong>蓝牙通信模块</strong>：负责与车端进行蓝牙通信
                </li>
                <li class="module-list-item">
                    <span class="module-list-icon">🔒</span>
                    <strong>安全存储模块</strong>：安全存储数字钥匙和相关密钥材料
                </li>
                <li class="module-list-item">
                    <span class="module-list-icon">⏰</span>
                    <strong>时间同步模块</strong>：与服务器进行时间同步，确保安全认证的时间准确性
                </li>
                <li class="module-list-item">
                    <span class="module-list-icon">🔒</span>
                    <strong>暗号交换模块</strong>：负责与车端协商生成临时暗号，确保通信安全
                </li>
                <li class="module-list-item">
                    <span class="module-list-icon">🤖</span>
                    <strong>智能场景管理模块</strong>：负责检测用户状态与环境，优化电量与连接策略
                </li>
            </ul>
        </div>
    </div>

    <div class="architecture-module">
        <div class="module-header">
            <span class="module-icon">🚗</span>
            <span>车端</span>
        </div>
        <div class="module-body">
            <ul class="module-list">
                <li class="module-list-item">
                    <span class="module-list-icon">🎧</span>
                    <strong>蓝牙通信模块</strong>：负责与手机端进行蓝牙通信
                </li>
                <li class="module-list-item">
                    <span class="module-list-icon">🔒</span>
                    <strong>钥匙验证模块</strong>：验证数字钥匙的有效性和权限
                </li>
                <li class="module-list-item">
                    <span class="module-list-icon">🚗</span>
                    <strong>指令执行模块</strong>：执行控制指令，如开锁、关锁、启动等
                </li>
                <li class="module-list-item">
                    <span class="module-list-icon">🔒</span>
                    <strong>安全存储模块</strong>：安全存储密钥材料和相关配置
                </li>
                <li class="module-list-item">
                    <span class="module-list-icon">🌐</span>
                    <strong>远程通信模块(T-Box)</strong>：与TSP平台进行通信，接收远程控制指令
                </li>
                <li class="module-list-item">
                    <span class="module-list-icon">⏰</span>
                    <strong>时间同步模块</strong>：与服务器进行时间同步，确保安全认证准确
                </li>
                <li class="module-list-item">
                    <span class="module-list-icon">🔒</span>
                    <strong>暗号交换模块</strong>：与手机端协商生成临时暗号，确保通信安全
                </li>
                <li class="module-list-item">
                    <span class="module-list-icon">🤖</span>
                    <strong>用户行为分析模块</strong>：实现无感控车的核心算法，处理距离计算
                </li>
            </ul>
        </div>
    </div>

    <div class="architecture-module">
        <div class="module-header">
            <span class="module-icon">🔒</span>
            <span>钥匙云平台</span>
        </div>
        <div class="module-body">
            <ul class="module-list">
                <li class="module-list-item">
                    <span class="module-list-icon">🔑</span>
                    <strong>钥匙生命周期管理</strong>：管理数字钥匙的创建、更新、撤销等
                </li>
                <li class="module-list-item">
                    <span class="module-list-icon">🚗</span>
                    <strong>车辆关联服务</strong>：将VIN码与车辆信息关联
                </li>
                <li class="module-list-item">
                    <span class="module-list-icon">🔒</span>
                    <strong>安全认证中心</strong>：提供安全认证服务
                </li>
                <li class="module-list-item">
                    <span class="module-list-icon">🔒</span>
                    <strong>密钥管理系统</strong>：管理系统中的各类密钥
                </li>
                <li class="module-list-item">
                    <span class="module-list-icon">⏰</span>
                    <strong>时间服务器</strong>：提供标准时间服务
                </li>
                <li class="module-list-item">
                    <span class="module-list-icon">🌐</span>
                    <strong>统一接口服务</strong>：提供对外接口服务
                </li>
                <li class="module-list-item">
                    <span class="module-list-icon">🔒</span>
                    <strong>安全通信通道</strong>：提供加密通信，防止消息被窃听或篡改
                </li>
                <li class="module-list-item">
                    <span class="module-list-icon">🤖</span>
                    <strong>异常监控与处理</strong>：监控异常登录，分析异常使用模式
                </li>
            </ul>
        </div>
    </div>

    <div class="architecture-module">
        <div class="module-header">
            <span class="module-icon">🌐</span>
            <span>外部平台</span>
        </div>
        <div class="module-body">
            <ul class="module-list">
                <li class="module-list-item">
                    <span class="module-list-icon">🌐</span>
                    <strong>TSP平台</strong>：车联网服务提供商平台，提供车辆远程监控、远程控制等
                </li>
                <li class="module-list-item">
                    <span class="module-list-icon">🚗</span>
                    <strong>OEM平台</strong>：汽车制造商平台，提供车辆生产信息、配置信息等
                </li>
            </ul>
        </div>
    </div>
</div>

<div class="architecture-connections-container">
    <h3>系统架构连接关系</h3>
    <div class="architecture-diagram">
        <div class="diagram-container">
            <!-- 手机端 -->
            <div class="diagram-module" id="mobile-module">
                <div class="diagram-header">手机端</div>
                <div class="diagram-body">
                    <div class="diagram-icon">📱</div>
                </div>
            </div>
            
            <!-- 车端 -->
            <div class="diagram-module" id="car-module">
                <div class="diagram-header">车端</div>
                <div class="diagram-body">
                    <div class="diagram-icon">🚗</div>
                </div>
            </div>
            
            <!-- 云平台 -->
            <div class="diagram-module" id="cloud-module">
                <div class="diagram-header">钥匙云平台</div>
                <div class="diagram-body">
                    <div class="diagram-icon">☁️</div>
                </div>
            </div>
            
            <!-- 外部平台 -->
            <div class="diagram-module" id="external-module">
                <div class="diagram-header">外部平台</div>
                <div class="diagram-body">
                    <div class="diagram-icon">🌐</div>
                </div>
            </div>
            
            <!-- 连接线 -->
            <svg class="diagram-connections" viewBox="0 0 800 500" preserveAspectRatio="xMidYMid meet">
                <!-- 定义箭头 -->
                <defs>
                    <marker id="diagram-arrow" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
                        <polygon points="0 0, 10 3.5, 0 7" fill="#0066cc" />
                    </marker>
                </defs>
                
                <!-- 手机端到车端 - 蓝牙通信 -->
                <path d="M200,100 L400,100" stroke="#0066cc" stroke-width="2" fill="none" marker-end="url(#diagram-arrow)"></path>
                <text x="300" y="85" text-anchor="middle" fill="#0066cc" class="connection-text">蓝牙通信</text>
                
                <!-- 手机端到钥匙云平台 - 数据同步 -->
                <path d="M125,150 L125,350" stroke="#0066cc" stroke-width="2" fill="none" marker-end="url(#diagram-arrow)"></path>
                <text x="90" y="250" text-anchor="middle" fill="#0066cc" class="connection-text">数据同步</text>
                
                <!-- 钥匙云平台到车端 - 远程控制 -->
                <path d="M200,350 C300,300 350,200 400,150" stroke="#0066cc" stroke-width="2" fill="none" marker-end="url(#diagram-arrow)"></path>
                <text x="300" y="280" text-anchor="middle" fill="#0066cc" class="connection-text">远程控制</text>
                
                <!-- 钥匙云平台到外部平台 - 数据交换 -->
                <path d="M200,400 L400,400" stroke="#0066cc" stroke-width="2" fill="none" marker-end="url(#diagram-arrow)"></path>
                <text x="300" y="380" text-anchor="middle" fill="#0066cc" class="connection-text">数据交换</text>
            </svg>
        </div>
        
        <!-- 图例 -->
        <div class="diagram-legend">
            <div class="legend-title">图例说明</div>
            <div class="legend-items">
                <div class="legend-item">
                    <div class="legend-color"></div>
                    <div class="legend-text">数据流向</div>
                </div>
                <div class="legend-item">
                    <div class="legend-icon">📱</div>
                    <div class="legend-text">手机端组件</div>
                </div>
                <div class="legend-item">
                    <div class="legend-icon">🚗</div>
                    <div class="legend-text">车端组件</div>
                </div>
                <div class="legend-item">
                    <div class="legend-icon">☁️</div>
                    <div class="legend-text">云平台组件</div>
                </div>
                <div class="legend-item">
                    <div class="legend-icon">🌐</div>
                    <div class="legend-text">外部平台</div>
                </div>
            </div>
        </div>
    </div>
</div>

</body>
<script>
    // 页面加载完成后执行
    document.addEventListener('DOMContentLoaded', function() {
        // 初始化jsPlumb
        var jsPlumbInstance = jsPlumb.getInstance({
            Endpoint: ["Dot", { radius: 2 }],
            ConnectionOverlays: [
                ["Arrow", { 
                    location: 1,
                    id: "arrow",
                    length: 10,
                    width: 10
                }]
            ],
            Container: "architecture-canvas"
        });
        
        // 等待所有元素加载完成
        setTimeout(function() {
            // 设置所有组件为连接源和目标
            jsPlumbInstance.batch(function() {
                // 设置端点样式
                var endpointOptions = { 
                    isSource: true, 
                    isTarget: true,
                    connector: ["Flowchart", { cornerRadius: 5 }],
                    maxConnections: -1
                };
                
                // 为所有组件添加端点
                jsPlumbInstance.addEndpoint("component-car-owner", { anchor: "Bottom" }, endpointOptions);
                jsPlumbInstance.addEndpoint("component-shared-user", { anchor: "Bottom" }, endpointOptions);
                jsPlumbInstance.addEndpoint("component-mobile", { anchor: "Top" }, endpointOptions);
                jsPlumbInstance.addEndpoint("component-mobile", { anchor: "Bottom" }, endpointOptions);
                jsPlumbInstance.addEndpoint("component-key-cloud", { anchor: "Top" }, endpointOptions);
                jsPlumbInstance.addEndpoint("component-key-cloud", { anchor: "Bottom" }, endpointOptions);
                jsPlumbInstance.addEndpoint("component-key-cloud", { anchor: "Right" }, endpointOptions);
                jsPlumbInstance.addEndpoint("component-tsp", { anchor: "Top" }, endpointOptions);
                jsPlumbInstance.addEndpoint("component-tsp", { anchor: "Bottom" }, endpointOptions);
                jsPlumbInstance.addEndpoint("component-tsp", { anchor: "Left" }, endpointOptions);
                jsPlumbInstance.addEndpoint("component-tsp", { anchor: "Right" }, endpointOptions);
                jsPlumbInstance.addEndpoint("component-oem", { anchor: "Left" }, endpointOptions);
                jsPlumbInstance.addEndpoint("component-car", { anchor: "Top" }, endpointOptions);
                
                // 创建连接
                // 用户到应用层
                jsPlumbInstance.connect({
                    source: "component-car-owner",
                    target: "component-mobile",
                    paintStyle: { stroke: "#0066cc", strokeWidth: 2 },
                    endpointStyle: { fill: "transparent", outlineStroke: "none" },
                    overlays: [
                        ["Label", { label: "用户操作", location: 0.5, cssClass: "connection-label" }]
                    ]
                });
                
                // 应用层到服务层 - HTTPS连接
                jsPlumbInstance.connect({
                    source: "component-mobile",
                    target: "component-key-cloud",
                    paintStyle: { stroke: "#0066cc", strokeWidth: 2 },
                    endpointStyle: { fill: "transparent", outlineStroke: "none" },
                    overlays: [
                        ["Label", { label: "HTTPS", location: 0.5, cssClass: "connection-label" }]
                    ]
                });
                
                // 应用层到服务层 - HTTPS连接
                jsPlumbInstance.connect({
                    source: "component-mobile",
                    target: "component-tsp",
                    paintStyle: { stroke: "#0066cc", strokeWidth: 2 },
                    endpointStyle: { fill: "transparent", outlineStroke: "none" },
                    overlays: [
                        ["Label", { label: "HTTPS", location: 0.5, cssClass: "connection-label" }]
                    ]
                });
                
                // 服务层内部 - 数据交换
                jsPlumbInstance.connect({
                    source: "component-key-cloud",
                    target: "component-tsp",
                    paintStyle: { stroke: "#0066cc", strokeWidth: 2 },
                    endpointStyle: { fill: "transparent", outlineStroke: "none" },
                    overlays: [
                        ["Label", { label: "数据交换", location: 0.5, cssClass: "connection-label" }]
                    ]
                });
                
                // 服务层内部 - 数据交换
                jsPlumbInstance.connect({
                    source: "component-tsp",
                    target: "component-oem",
                    paintStyle: { stroke: "#0066cc", strokeWidth: 2 },
                    endpointStyle: { fill: "transparent", outlineStroke: "none" },
                    overlays: [
                        ["Label", { label: "数据交换", location: 0.5, cssClass: "connection-label" }]
                    ]
                });
                
                // 服务层到车端层 - 4G/5G连接
                jsPlumbInstance.connect({
                    source: "component-tsp",
                    target: "component-car",
                    paintStyle: { stroke: "#43a047", strokeWidth: 2 },
                    endpointStyle: { fill: "transparent", outlineStroke: "none" },
                    overlays: [
                        ["Label", { label: "4G/5G", location: 0.5, cssClass: "connection-label" }]
                    ]
                });
                
                // 应用层到车端层 - 蓝牙连接
                jsPlumbInstance.connect({
                    source: "component-mobile",
                    target: "component-car",
                    connector: ["Bezier", { curviness: 100 }],
                    paintStyle: { stroke: "#5c6bc0", strokeWidth: 2, dashstyle: "4 2" },
                    endpointStyle: { fill: "transparent", outlineStroke: "none" },
                    overlays: [
                        ["Label", { label: "蓝牙", location: 0.5, cssClass: "connection-label" }]
                    ]
                });
            });
            
            // 使端点不可见
            jsPlumbInstance.selectEndpoints().hide();
        }, 500);
        
        // 架构图交互
        initArchitectureInteraction();
        
        // 数据流程图交互
        initDataFlowInteraction();
    });
    
    // 初始化架构图交互
    function initArchitectureInteraction() {
        // 获取所有架构组件
        const archComponents = document.querySelectorAll('.arch-component');
        
        // 为每个组件添加鼠标悬停事件
        archComponents.forEach(component => {
            component.addEventListener('mouseenter', function() {
                // 高亮当前组件
                this.style.transform = 'translateY(-8px) scale(1.05)';
                this.style.boxShadow = '0 12px 20px rgba(0,0,0,0.2)';
                this.style.zIndex = '10';
                
                // 降低其他组件的不透明度
                archComponents.forEach(otherComponent => {
                    if (otherComponent !== this) {
                        otherComponent.style.opacity = '0.6';
                    }
                });
            });
            
            component.addEventListener('mouseleave', function() {
                // 恢复当前组件样式
                this.style.transform = '';
                this.style.boxShadow = '';
                this.style.zIndex = '';
                
                // 恢复其他组件的不透明度
                archComponents.forEach(otherComponent => {
                    otherComponent.style.opacity = '1';
                });
            });
        });
    }
    
    // 初始化数据流程图交互
    function initDataFlowInteraction() {
        // 获取所有数据流端点
        const flowEndpoints = document.querySelectorAll('.flow-endpoint');
        
        // 为每个端点添加鼠标悬停事件
        flowEndpoints.forEach(endpoint => {
            endpoint.addEventListener('mouseenter', function() {
                // 高亮当前端点
                this.style.transform = 'translateY(-8px) scale(1.05)';
                this.style.boxShadow = '0 12px 20px rgba(0,0,0,0.2)';
                this.style.zIndex = '10';
                
                // 降低其他端点的不透明度
                flowEndpoints.forEach(otherEndpoint => {
                    if (otherEndpoint !== this) {
                        otherEndpoint.style.opacity = '0.6';
                    }
                });
                
                // 高亮相关箭头
                highlightRelatedArrows(this);
            });
            
            endpoint.addEventListener('mouseleave', function() {
                // 恢复当前端点样式
                this.style.transform = '';
                this.style.boxShadow = '';
                this.style.zIndex = '';
                
                // 恢复其他端点的不透明度
                flowEndpoints.forEach(otherEndpoint => {
                    otherEndpoint.style.opacity = '1';
                });
                
                // 恢复所有箭头样式
                resetArrowsHighlight();
            });
        });
    }
    
    // 高亮相关箭头
    function highlightRelatedArrows(endpoint) {
        // 获取端点的类名，用于确定是哪个端点
        const classList = endpoint.classList;
        const arrows = document.querySelectorAll('.flow-arrow');
        
        arrows.forEach(arrow => {
            // 默认降低所有箭头的不透明度
            arrow.style.opacity = '0.3';
            
            // 根据端点类型高亮相关箭头
            if (classList.contains('mobile-endpoint')) {
                // 手机端相关箭头
                if (arrow.classList.contains('arrow1') || 
                    arrow.classList.contains('arrow2') || 
                    arrow.classList.contains('arrow3') || 
                    arrow.classList.contains('arrow4') || 
                    arrow.classList.contains('arrow5') || 
                    arrow.classList.contains('arrow6')) {
                    arrow.style.opacity = '1';
                    if (arrow.classList.contains('right-arrow')) {
                        arrow.style.height = '3px';
                    } else if (arrow.classList.contains('left-arrow')) {
                        arrow.style.height = '3px';
                    } else if (arrow.classList.contains('diagonal-arrow')) {
                        arrow.style.height = '3px';
                    }
                }
            } else if (classList.contains('cloud-endpoint')) {
                // 云平台相关箭头
                if (arrow.classList.contains('arrow1') || 
                    arrow.classList.contains('arrow2') || 
                    arrow.classList.contains('arrow3') || 
                    arrow.classList.contains('arrow4') || 
                    arrow.classList.contains('arrow6') || 
                    arrow.classList.contains('arrow7')) {
                    arrow.style.opacity = '1';
                    if (arrow.classList.contains('right-arrow')) {
                        arrow.style.height = '3px';
                    } else if (arrow.classList.contains('left-arrow')) {
                        arrow.style.height = '3px';
                    }
                }
            } else if (classList.contains('car-endpoint')) {
                // 车端相关箭头
                if (arrow.classList.contains('arrow5') || 
                    arrow.classList.contains('arrow6') || 
                    arrow.classList.contains('arrow7')) {
                    arrow.style.opacity = '1';
                    if (arrow.classList.contains('right-arrow')) {
                        arrow.style.height = '3px';
                    } else if (arrow.classList.contains('left-arrow')) {
                        arrow.style.height = '3px';
                    } else if (arrow.classList.contains('diagonal-arrow')) {
                        arrow.style.height = '3px';
                    }
                }
            }
        });
    }
    
    // 重置箭头高亮
    function resetArrowsHighlight() {
        const arrows = document.querySelectorAll('.flow-arrow');
        
        arrows.forEach(arrow => {
            arrow.style.opacity = '1';
            arrow.style.height = '2px';
        });
    }
</script>
</html>


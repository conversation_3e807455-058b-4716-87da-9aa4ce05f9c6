@startuml
skinparam monochrome true

participant "APP" as Mobile
participant "车端" as CarBT
participant "钥匙云平台" as Cloud

== 蓝牙配对流程 ==

Mobile -> Mobile : 1. 用户在APP中选择添加数字钥匙
Mobile -> Mobile : 2. 用户扫描车辆二维码或输入VIN码识别车辆
Mobile -> Cloud : 3. 发送车辆VIN码，请求获取数字钥匙
Cloud -> Cloud : 4. 根据VIN码生成虚拟密钥（VIRKEY）
Cloud -> Mobile : 5. 下发虚拟密钥到手机
Mobile -> Mobile : 6. 保存虚拟密钥到安全存储区
Cloud -> CarBT : 7. 生成根密钥并下发到汽车
CarBT -> CarBT : 8. 存储根密钥
CarBT -> Mobile : 9. 发送蓝牙广播（包含MAC地址等信息）
Mobile -> CarBT : 10. 扫描并连接到汽车蓝牙模块
Mobile -> CarBT : 11. 发送蓝牙配对请求
CarBT -> Mobile : 12. 发起蓝牙安全请求
CarBT -> CarBT : 13. 生成配对码
Mobile -> Mobile : 14. 生成或输入配对码
Mobile -> CarBT : 15. 发送配对码进行校验
CarBT -> Mobile : 16. 返回配对码校验结果
CarBT -> CarBT : 17. 配对成功，绑定设备信息
Mobile -> Mobile : 18. 配对成功，绑定设备信息
Mobile -> CarBT : 19. 发送加密的虚拟密钥和VIN码
CarBT -> CarBT : 20. 验证虚拟密钥和VIN码匹配
CarBT -> Mobile : 21. 返回验证结果
Mobile -> Cloud : 22. 上报配对结果
Cloud -> Cloud : 23. 记录钥匙绑定状态
@enduml
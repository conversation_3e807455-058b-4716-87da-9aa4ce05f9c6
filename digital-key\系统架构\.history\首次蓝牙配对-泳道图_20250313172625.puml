@startuml
!define Participant(name, description) class name << (P,orchestration) >> {
  description
}
!define Message(name, description) class name << (M,message) >> {
  description
}
!define swimlane(name, description) package name << (S,swimlane) >> {
  description
}

swimlane 车辆蓝牙模块 {
  Participant 车辆蓝牙模块
}
swimlane 移动终端 {
  Participant 移动终端
}

车辆蓝牙模块 -> 移动终端: 1. 发送蓝牙广播（含车辆识别信息）
移动终端 -> 车辆蓝牙模块: 2. 发起蓝牙连接请求
车辆蓝牙模块 -> 车辆蓝牙模块: 3. 进入蓝牙连接通道
移动终端 -> 移动终端: 3. 建立蓝牙连接通道
车辆蓝牙模块 -> 移动终端: 4. 发起蓝牙安全请求
移动终端 -> 车辆蓝牙模块: 5. 发送蓝牙配对请求
车辆蓝牙模块 -> 车辆蓝牙模块: 6. 输入或生成配对码
移动终端 -> 移动终端: 6. 输入或生成配对码
车辆蓝牙模块 -> 移动终端: 7. 校验配对码
移动终端 -> 车辆蓝牙模块: 7. 校验配对码
车辆蓝牙模块 -> 车辆蓝牙模块: 8. 配对成功，绑定设备信息
移动终端 -> 移动终端: 8. 配对成功，绑定设备信息
@enduml
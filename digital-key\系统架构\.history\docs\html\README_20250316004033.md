# 数字钥匙技术架构展示

这是一个基于HTML的PPT风格展示页面，用于展示数字钥匙的技术架构设计。

## 使用方法

1. 打开 `index.html` 文件，即可查看完整的技术架构展示。
2. 使用键盘方向键或点击屏幕两侧的箭头可以前进/后退浏览幻灯片。
3. 按 `ESC` 键可以查看幻灯片概览。
4. 点击架构图和泳道图可以查看更详细的内容。

## 文件结构

- `index.html` - 主展示页面
- `css/style.css` - 样式文件
- `js/image-loader.js` - 图片加载脚本
- `images/` - 包含详细的架构图和泳道图HTML文件
  - `overall-architecture.html` - 整体架构图
  - `pairing-swimlane.html` - 首次配对流程泳道图
  - `passive-control-swimlane.html` - 无感控车流程泳道图

## 技术架构概述

数字钥匙系统由三大核心模块组成：

1. **手机端**：用户交互界面与核心SDK
2. **车端**：BLE模块与TBOX
3. **钥匙云平台**：密钥管理与安全认证

系统实现了以下核心功能：

- 首次蓝牙配对过程
- 蓝牙通讯过程
- 蓝牙通讯安全设计
- 密钥管理机制
- 时间同步机制
- 无感控车方案
- 故障应急处理机制

## 注意事项

- 本展示页面使用了 [Reveal.js](https://revealjs.com/) 框架，需要联网加载相关资源。
- 点击架构图和泳道图可以查看更详细的内容，这些内容是通过HTML文件实现的，不需要额外的图片资源。 
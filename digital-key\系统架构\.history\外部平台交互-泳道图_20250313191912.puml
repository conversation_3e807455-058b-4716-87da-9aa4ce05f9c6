@startuml
skinparam monochrome true

participant "APP" as Mobile
participant "钥匙云平台" as API
participant "车端（TBOX）" as CarTBOX
participant "外部平台(TSP/OEM等)" as External

== 数字钥匙申请与车辆绑定流程 ==

Mobile -> Mobile : 1. 用户在APP中扫描车辆二维码或输入VIN码
Mobile -> API : 2. 发送车辆VIN码，请求获取数字钥匙
API -> External : 3. 查询VIN码对应的车辆信息和TBOX信息
External -> API : 4. 返回车辆信息和TBOX连接凭证
API -> API : 5. 生成虚拟密钥(VIRKEY)
API -> Mobile : 6. 下发虚拟密钥到手机

note right of API
  统一接口服务对外与TSP/OEM平台交互
  获取车辆信息和TBOX连接凭证
end note

API -> CarTBOX : 7. 直接与车辆TBOX建立连接
CarTBOX -> API : 8. TBOX身份验证和连接确认
API -> CarTBOX : 9. 下发证书和根密钥到车辆
CarTBOX -> CarTBOX : 10. 存储证书和根密钥
CarTBOX -> API : 11. 确认密钥接收成功
API -> Mobile : 12. 通知APP车辆绑定成功

note right of API
  统一接口服务对内与车端TBOX直接通信
  下发证书、密钥和控制指令
end note

== 远程控制车辆流程 ==

Mobile -> API : 1. 发送远程控制请求(如开锁)
API -> API : 2. 验证用户权限和密钥有效性
API -> External : 3. 验证用户对车辆的控制权限
External -> API : 4. 返回权限验证结果
API -> CarTBOX : 5. 直接下发控制指令到车辆TBOX
CarTBOX -> CarTBOX : 6. 执行控制指令
CarTBOX -> API : 7. 返回执行结果
API -> Mobile : 8. 通知APP执行结果

== 车辆诊断信息查询流程 ==

Mobile -> API : 1. 发送诊断信息查询请求
API -> External : 2. 获取车辆诊断服务访问权限
External -> API : 3. 返回诊断服务访问凭证
API -> CarTBOX : 4. 直接请求车辆实时诊断数据
CarTBOX -> API : 5. 返回诊断数据
API -> External : 6. 发送诊断数据进行分析(可选)
External -> API : 7. 返回诊断分析结果(可选)
API -> Mobile : 8. 转发诊断结果

@enduml 
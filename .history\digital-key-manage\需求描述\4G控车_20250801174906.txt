具体需求请参照 4G控车.txt

现在我们要开发一个4G控车功能，也就是手机小程序针对解闭锁按键
在连接蓝牙的情况下，保持用蓝牙发送指令
没有蓝牙连接的情况下，用websocket向服务器通讯，告诉服务器用户要通过4G控车了

服务器收到用户4G控车的指令以后，要通过TCP服务跟车辆的TBOX连接告诉车辆要执行解闭锁的操作

所以我们在服务端先要搭建一个TCP服务，具体可以参照之前websocket搭建的地方，放到差不多的位置，这次要的是服务器搭建TCP服务，
TBOX作为TCP客户端，与TCP服务链接以后，通过TCP数据包直接交换数据
比如：服务端会给TBOX发送解闭锁的指令，指令入下

7E000A000A000102030405060708090A000030333032303030313034007E		车辆上传解锁
7E000A000A000102030405060708090A000030333032303030313033007E		车辆上传闭锁

其实也就是倒数第五位，是4就是解锁，是3就是闭锁

TBOX也会搜集车辆的解闭锁状态回复一模一样的指令给TCP服务端，TCP服务端收到比如当前车门是闭锁，再把这个状态通过websocket链路反馈到手机上，正确显示当前解闭锁状态




这里手机端要做的是：在解闭锁的按钮后面加个判断，当前蓝牙连接怎么样，蓝牙不连接怎么样。。。并实时显示车辆当前车门的开锁闭锁状态
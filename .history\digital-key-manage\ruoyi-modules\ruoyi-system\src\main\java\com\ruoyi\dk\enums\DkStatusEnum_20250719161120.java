package com.ruoyi.dk.enums;

/**
 * 数字钥匙系统状态枚举类
 * 统一管理系统中的各种状态字典
 * 
 * <AUTHOR>
 * @date 2024-01-15
 */
public class DkStatusEnum {

    /**
     * 用户权限状态枚举
     */
    public enum UserStatus {
        NORMAL("0", "正常", "success"),
        RESTRICTED("1", "限制", "danger");

        private final String code;
        private final String desc;
        private final String cssClass;

        UserStatus(String code, String desc, String cssClass) {
            this.code = code;
            this.desc = desc;
            this.cssClass = cssClass;
        }

        public String getCode() {
            return code;
        }

        public String getDesc() {
            return desc;
        }

        public String getCssClass() {
            return cssClass;
        }

        /**
         * 根据代码获取描述
         */
        public static String getDescByCode(String code) {
            for (UserStatus status : UserStatus.values()) {
                if (status.getCode().equals(code)) {
                    return status.getDesc();
                }
            }
            return "未知状态";
        }

        /**
         * 根据代码获取CSS样式
         */
        public static String getCssClassByCode(String code) {
            for (UserStatus status : UserStatus.values()) {
                if (status.getCode().equals(code)) {
                    return status.getCssClass();
                }
            }
            return "default";
        }
    }

    /**
     * 车辆状态枚举
     */
    public enum VehicleStatus {
        AVAILABLE("0", "空闲", "success"),
        IN_USE("1", "使用中", "primary"),
        MAINTENANCE("2", "维护中", "warning"),
        DISABLED("3", "停用", "danger");

        private final String code;
        private final String desc;
        private final String cssClass;

        VehicleStatus(String code, String desc, String cssClass) {
            this.code = code;
            this.desc = desc;
            this.cssClass = cssClass;
        }

        public String getCode() {
            return code;
        }

        public String getDesc() {
            return desc;
        }

        public String getCssClass() {
            return cssClass;
        }

        /**
         * 根据代码获取描述
         */
        public static String getDescByCode(String code) {
            for (VehicleStatus status : VehicleStatus.values()) {
                if (status.getCode().equals(code)) {
                    return status.getDesc();
                }
            }
            return "未知状态";
        }

        /**
         * 根据代码获取CSS样式
         */
        public static String getCssClassByCode(String code) {
            for (VehicleStatus status : VehicleStatus.values()) {
                if (status.getCode().equals(code)) {
                    return status.getCssClass();
                }
            }
            return "default";
        }
    }

    /**
     * 申请状态枚举
     */
    public enum ApplicationStatus {
        PENDING("0", "待审核", "info"),
        APPROVED("1", "审核通过", "success"),
        REJECTED("2", "审核拒绝", "danger"),
        SUPPLEMENT("3", "补充资料", "warning"),
        WITHDRAWN("4", "已撤销", "default");

        private final String code;
        private final String desc;
        private final String cssClass;

        ApplicationStatus(String code, String desc, String cssClass) {
            this.code = code;
            this.desc = desc;
            this.cssClass = cssClass;
        }

        public String getCode() {
            return code;
        }

        public String getDesc() {
            return desc;
        }

        public String getCssClass() {
            return cssClass;
        }

        /**
         * 根据代码获取描述
         */
        public static String getDescByCode(String code) {
            for (ApplicationStatus status : ApplicationStatus.values()) {
                if (status.getCode().equals(code)) {
                    return status.getDesc();
                }
            }
            return "未知状态";
        }

        /**
         * 根据代码获取CSS样式
         */
        public static String getCssClassByCode(String code) {
            for (ApplicationStatus status : ApplicationStatus.values()) {
                if (status.getCode().equals(code)) {
                    return status.getCssClass();
                }
            }
            return "default";
        }
    }

    /**
     * 车辆分配状态枚举
     */
    public enum AssignmentStatus {
        IN_USE("1", "使用中", "primary"),
        RETURNED("2", "已归还", "success"),
        OVERDUE("3", "逾期", "warning"),
        ABNORMAL("4", "异常", "danger");

        private final String code;
        private final String desc;
        private final String cssClass;

        AssignmentStatus(String code, String desc, String cssClass) {
            this.code = code;
            this.desc = desc;
            this.cssClass = cssClass;
        }

        public String getCode() {
            return code;
        }

        public String getDesc() {
            return desc;
        }

        public String getCssClass() {
            return cssClass;
        }

        /**
         * 根据代码获取描述
         */
        public static String getDescByCode(String code) {
            for (AssignmentStatus status : AssignmentStatus.values()) {
                if (status.getCode().equals(code)) {
                    return status.getDesc();
                }
            }
            return "未知状态";
        }

        /**
         * 根据代码获取CSS样式
         */
        public static String getCssClassByCode(String code) {
            for (AssignmentStatus status : AssignmentStatus.values()) {
                if (status.getCode().equals(code)) {
                    return status.getCssClass();
                }
            }
            return "default";
        }
    }
}

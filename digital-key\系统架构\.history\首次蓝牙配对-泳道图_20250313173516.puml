@startuml
skinparam monochrome true

participant "云平台" as Cloud
participant "手机" as Mobile
participant "汽车蓝牙模块" as CarBT

== 蓝牙配对流程 ==

Mobile -> Cloud : 1. 用户通过APP登录并发起配对请求
Cloud -> Cloud : 2. 验证用户身份和权限
Cloud -> Mobile : 3. 下发虚拟密钥（VIRKEY）到手机
Mobile -> Mobile : 4. 保存虚拟密钥到安全存储区
Cloud -> CarBT : 5. 生成根密钥并下发到汽车
CarBT -> CarBT : 6. 存储根密钥
CarBT -> Mobile : 7. 发送蓝牙广播（包含MAC地址等信息）
Mobile -> CarBT : 8. 扫描并连接到汽车蓝牙模块
Mobile -> CarBT : 9. 发送蓝牙配对请求
CarBT -> Mobile : 10. 发起蓝牙安全请求
CarBT -> CarBT : 11. 输入或生成配对码
Mobile -> Mobile : 12. 输入或生成配对码
Mobile -> CarBT : 13. 发送配对码进行校验
CarBT -> Mobile : 14. 返回配对码校验结果
CarBT -> CarBT : 15. 配对成功，绑定设备信息
Mobile -> Mobile : 16. 配对成功，绑定设备信息
Mobile -> CarBT : 17. 发送加密的虚拟密钥和身份认证请求
CarBT -> CarBT : 18. 验证虚拟密钥
CarBT -> Mobile : 19. 返回身份认证结果
@enduml
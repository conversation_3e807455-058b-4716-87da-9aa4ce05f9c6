<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>数字钥匙系统 - 功能导航</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            display: flex;
            flex-direction: column;
            height: 100vh;
            background-color: #f5f5f5;
            color: #333;
        }
        
        header {
            background-color: #2c3e50;
            color: white;
            padding: 15px 20px;
            text-align: center;
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
        }
        
        .container {
            display: flex;
            flex: 1;
            overflow: hidden;
        }
        
        .nav {
            width: 250px;
            background-color: #34495e;
            color: white;
            padding: 20px 0;
            overflow-y: auto;
            box-shadow: 2px 0 5px rgba(0,0,0,0.1);
        }
        
        .nav-item {
            padding: 12px 20px;
            cursor: pointer;
            transition: background-color 0.3s;
            border-left: 3px solid transparent;
        }
        
        .nav-item:hover {
            background-color: #2c3e50;
            border-left-color: #3498db;
        }
        
        .nav-item.active {
            background-color: #2c3e50;
            border-left-color: #3498db;
            font-weight: bold;
        }
        
        .content {
            flex: 1;
            overflow: hidden;
            background-color: white;
            box-shadow: inset 2px 0 5px rgba(0,0,0,0.05);
        }
        
        iframe {
            width: 100%;
            height: 100%;
            border: none;
        }
        
        .loading {
            display: flex;
            align-items: center;
            justify-content: center;
            height: 100%;
            font-size: 18px;
            color: #7f8c8d;
        }
        
        @media (max-width: 768px) {
            .container {
                flex-direction: column;
            }
            
            .nav {
                width: 100%;
                padding: 10px 0;
            }
            
            .content {
                flex: 1;
            }
        }
    </style>
</head>
<body>
    <header>
        <h1>数字钥匙系统演示</h1>
    </header>
    
    <div class="container">
        <div class="nav" id="nav">
            <!-- 导航项将通过JavaScript动态添加 -->
        </div>
        
        <div class="content">
            <iframe id="content-frame" src="about:blank"></iframe>
        </div>
    </div>
    
    <script>
        // 使用Fetch API获取HTML文件列表
        function loadPages() {
            fetch('scan-html.php')
                .then(response => response.json())
                .then(data => {
                    // 保存页面数据
                    window.pages = data;
                    // 创建导航菜单
                    createNavigation();
                    
                    // 默认加载第一个页面（如果有的话）
                    if (window.pages.length > 0) {
                        const firstNavItem = document.querySelector('.nav-item');
                        if (firstNavItem) {
                            firstNavItem.classList.add('active');
                            loadContent(window.pages[0].file);
                        }
                    }
                })
                .catch(error => {
                    console.error('获取HTML文件列表失败:', error);
                    document.getElementById('nav').innerHTML = '<div class="nav-item">加载失败，请刷新页面重试</div>';
                });
        }
        
        // 创建导航菜单
        function createNavigation() {
            const navElement = document.getElementById('nav');
            navElement.innerHTML = ''; // 清空现有内容
            
            window.pages.forEach(page => {
                const navItem = document.createElement('div');
                navItem.className = 'nav-item';
                navItem.textContent = page.name;
                navItem.dataset.file = page.file;
                navItem.onclick = function() {
                    // 设置当前项为激活状态
                    document.querySelectorAll('.nav-item').forEach(item => {
                        item.classList.remove('active');
                    });
                    this.classList.add('active');
                    
                    // 加载内容
                    loadContent(page.file);
                };
                
                navElement.appendChild(navItem);
            });
        }
        
        // 加载内容到iframe
        function loadContent(fileName) {
            const iframe = document.getElementById('content-frame');
            iframe.src = fileName;
        }
        
        // 初始化
        document.addEventListener('DOMContentLoaded', function() {
            loadPages(); // 加载页面列表
        });
    </script>
</body>
</html> 
package com.ruoyi.dk.domain;

import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.core.annotation.Excel;
import com.ruoyi.common.core.web.domain.BaseEntity;

/**
 * 用户申请对象 dk_user_application
 * 
 * <AUTHOR>
 * @date 2024-01-15
 */
public class DkUserApplication extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /** 申请ID */
    private Long applicationId;

    /** 申请单号 */
    @Excel(name = "申请单号")
    private String applicationNo;

    /** 用户ID */
    private Long userId;

    /** 真实姓名 */
    @Excel(name = "真实姓名")
    private String realName;

    /** 手机号 */
    @Excel(name = "手机号")
    private String phone;

    /** 身份证号 */
    @Excel(name = "身份证号")
    private String idCard;

    /** 驾驶证号 */
    @Excel(name = "驾驶证号")
    private String drivingLicense;

    /** 身份证正面照片URL */
    private String idCardFrontUrl;

    /** 身份证背面照片URL */
    private String idCardBackUrl;

    /** 驾驶证照片URL */
    private String drivingLicenseUrl;

    /** 申请原因 */
    @Excel(name = "申请原因")
    private String applicationReason;

    /** 申请状态（0待审核 1审核通过 2审核拒绝 3补充资料） */
    @Excel(name = "申请状态", readConverterExp = "0=待审核,1=审核通过,2=审核拒绝,3=补充资料")
    private String status;

    /** 审核人ID */
    private Long auditUserId;

    /** 审核人姓名 */
    @Excel(name = "审核人")
    private String auditUserName;

    /** 审核时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "审核时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date auditTime;

    /** 审核备注 */
    @Excel(name = "审核备注")
    private String auditRemark;

    public void setApplicationId(Long applicationId) {
        this.applicationId = applicationId;
    }

    public Long getApplicationId() {
        return applicationId;
    }

    public void setApplicationNo(String applicationNo) {
        this.applicationNo = applicationNo;
    }

    public String getApplicationNo() {
        return applicationNo;
    }

    public void setUserId(Long userId) {
        this.userId = userId;
    }

    public Long getUserId() {
        return userId;
    }

    public void setRealName(String realName) {
        this.realName = realName;
    }

    public String getRealName() {
        return realName;
    }

    public void setPhone(String phone) {
        this.phone = phone;
    }

    public String getPhone() {
        return phone;
    }

    public void setIdCard(String idCard) {
        this.idCard = idCard;
    }

    public String getIdCard() {
        return idCard;
    }

    public void setDrivingLicense(String drivingLicense) {
        this.drivingLicense = drivingLicense;
    }

    public String getDrivingLicense() {
        return drivingLicense;
    }

    public void setIdCardFrontUrl(String idCardFrontUrl) {
        this.idCardFrontUrl = idCardFrontUrl;
    }

    public String getIdCardFrontUrl() {
        return idCardFrontUrl;
    }

    public void setIdCardBackUrl(String idCardBackUrl) {
        this.idCardBackUrl = idCardBackUrl;
    }

    public String getIdCardBackUrl() {
        return idCardBackUrl;
    }

    public void setDrivingLicenseUrl(String drivingLicenseUrl) {
        this.drivingLicenseUrl = drivingLicenseUrl;
    }

    public String getDrivingLicenseUrl() {
        return drivingLicenseUrl;
    }

    public void setApplicationReason(String applicationReason) {
        this.applicationReason = applicationReason;
    }

    public String getApplicationReason() {
        return applicationReason;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public String getStatus() {
        return status;
    }

    public void setAuditUserId(Long auditUserId) {
        this.auditUserId = auditUserId;
    }

    public Long getAuditUserId() {
        return auditUserId;
    }

    public void setAuditUserName(String auditUserName) {
        this.auditUserName = auditUserName;
    }

    public String getAuditUserName() {
        return auditUserName;
    }

    public void setAuditTime(Date auditTime) {
        this.auditTime = auditTime;
    }

    public Date getAuditTime() {
        return auditTime;
    }

    public void setAuditRemark(String auditRemark) {
        this.auditRemark = auditRemark;
    }

    public String getAuditRemark() {
        return auditRemark;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this, ToStringStyle.MULTI_LINE_STYLE)
                .append("applicationId", getApplicationId())
                .append("applicationNo", getApplicationNo())
                .append("userId", getUserId())
                .append("realName", getRealName())
                .append("phone", getPhone())
                .append("idCard", getIdCard())
                .append("drivingLicense", getDrivingLicense())
                .append("idCardFrontUrl", getIdCardFrontUrl())
                .append("idCardBackUrl", getIdCardBackUrl())
                .append("drivingLicenseUrl", getDrivingLicenseUrl())
                .append("applicationReason", getApplicationReason())
                .append("status", getStatus())
                .append("auditUserId", getAuditUserId())
                .append("auditUserName", getAuditUserName())
                .append("auditTime", getAuditTime())
                .append("auditRemark", getAuditRemark())
                .append("createBy", getCreateBy())
                .append("createTime", getCreateTime())
                .append("updateBy", getUpdateBy())
                .append("updateTime", getUpdateTime())
                .toString();
    }
}

<mxfile host="65bd71144e">
    <diagram id="digital-key-architecture" name="数字钥匙三端架构图">
        <mxGraphModel dx="1110" dy="1966" grid="1" gridSize="10" guides="1" tooltips="1" connect="1" arrows="1" fold="1" page="1" pageScale="1" pageWidth="1169" pageHeight="827" math="0" shadow="0">
            <root>
                <mxCell id="0"/>
                <mxCell id="1" parent="0"/>
                <mxCell id="title" value="数字钥匙架构 (Digital Key Architecture)" style="text;html=1;strokeColor=none;fillColor=none;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=28;fontStyle=1;fontColor=#0066CC;" parent="1" vertex="1">
                    <mxGeometry x="300" y="-90" width="570" height="50" as="geometry"/>
                </mxCell>
                <mxCell id="cloud-platform" value="钥匙云平台&#xa;(Key Cloud Platform)" style="shape=hexagon;perimeter=hexagonPerimeter2;whiteSpace=wrap;html=1;fixedSize=1;size=30;fillColor=#dae8fc;strokeColor=#6c8ebf;gradientColor=#7ea6e0;fontSize=18;fontStyle=1;shadow=1;" parent="1" vertex="1">
                    <mxGeometry x="485" width="200" height="120" as="geometry"/>
                </mxCell>
                <mxCell id="cloud-module-1" value="OAuth 2.0/OIDC 认证授权模块" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#dae8fc;strokeColor=#6c8ebf;gradientColor=#7ea6e0;shadow=1;fontSize=11;fontStyle=1;" parent="1" vertex="1">
                    <mxGeometry x="420" y="10" width="100" height="30" as="geometry"/>
                </mxCell>
                <mxCell id="cloud-module-2" value="PKI 密钥管理模块" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#dae8fc;strokeColor=#6c8ebf;gradientColor=#7ea6e0;shadow=1;fontSize=11;fontStyle=1;" parent="1" vertex="1">
                    <mxGeometry x="420" y="80" width="100" height="30" as="geometry"/>
                </mxCell>
                <mxCell id="cloud-module-3" value="NTP 同步服务" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#dae8fc;strokeColor=#6c8ebf;gradientColor=#7ea6e0;shadow=1;fontSize=11;fontStyle=1;" parent="1" vertex="1">
                    <mxGeometry x="650" y="10" width="100" height="30" as="geometry"/>
                </mxCell>
                <mxCell id="cloud-module-4" value="TSP 车辆关联服务" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#dae8fc;strokeColor=#6c8ebf;gradientColor=#7ea6e0;shadow=1;fontSize=11;fontStyle=1;" parent="1" vertex="1">
                    <mxGeometry x="650" y="80" width="100" height="30" as="geometry"/>
                </mxCell>
                <mxCell id="mobile-side" value="钥匙端&lt;br&gt;(Key)" style="shape=hexagon;perimeter=hexagonPerimeter2;whiteSpace=wrap;html=1;fixedSize=1;size=30;fillColor=#d5e8d4;strokeColor=#82b366;gradientColor=#97d077;fontSize=18;fontStyle=1;shadow=1;" parent="1" vertex="1">
                    <mxGeometry x="229" y="220" width="150" height="120" as="geometry"/>
                </mxCell>
                <mxCell id="mobile-module-1" value="控车指令发送模块" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#d5e8d4;strokeColor=#82b366;gradientColor=#97d077;shadow=1;fontSize=11;fontStyle=1;" parent="1" vertex="1">
                    <mxGeometry x="180" y="200" width="100" height="30" as="geometry"/>
                </mxCell>
                <mxCell id="mobile-module-2" value="BLE 交互模块" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#d5e8d4;strokeColor=#82b366;gradientColor=#97d077;shadow=1;fontSize=11;fontStyle=1;" parent="1" vertex="1">
                    <mxGeometry x="150" y="265" width="100" height="30" as="geometry"/>
                </mxCell>
                <mxCell id="mobile-module-3" value="TEE 安全存储" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#d5e8d4;strokeColor=#82b366;gradientColor=#97d077;shadow=1;fontSize=11;fontStyle=1;" parent="1" vertex="1">
                    <mxGeometry x="170" y="320" width="100" height="30" as="geometry"/>
                </mxCell>
                <mxCell id="mobile-module-4" value="RSSI 信号强度标定" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#d5e8d4;strokeColor=#82b366;gradientColor=#97d077;shadow=1;fontSize=11;fontStyle=1;" parent="1" vertex="1">
                    <mxGeometry x="340" y="320" width="100" height="30" as="geometry"/>
                </mxCell>
                <mxCell id="car-side" value="车端&#xa;(Vehicle Terminal)" style="shape=hexagon;perimeter=hexagonPerimeter2;whiteSpace=wrap;html=1;fixedSize=1;size=30;fillColor=#ffe6cc;strokeColor=#d79b00;gradientColor=#ffce9f;fontSize=18;fontStyle=1;shadow=1;" parent="1" vertex="1">
                    <mxGeometry x="790" y="220" width="150" height="120" as="geometry"/>
                </mxCell>
                <mxCell id="car-module-1" value="控车指令执行模块" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#ffe6cc;strokeColor=#d79b00;gradientColor=#ffce9f;shadow=1;fontSize=11;fontStyle=1;" parent="1" vertex="1">
                    <mxGeometry x="880" y="200" width="100" height="30" as="geometry"/>
                </mxCell>
                <mxCell id="car-module-2" value="BLE Secure Connection" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#ffe6cc;strokeColor=#d79b00;gradientColor=#ffce9f;shadow=1;fontSize=11;fontStyle=1;" parent="1" vertex="1">
                    <mxGeometry x="920" y="265" width="120" height="30" as="geometry"/>
                </mxCell>
                <mxCell id="car-module-3" value="HSM 安全存储" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#ffe6cc;strokeColor=#d79b00;gradientColor=#ffce9f;shadow=1;fontSize=11;fontStyle=1;" parent="1" vertex="1">
                    <mxGeometry x="900" y="320" width="100" height="30" as="geometry"/>
                </mxCell>
                <mxCell id="car-module-4" value="IDS 异常行为监控" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#ffe6cc;strokeColor=#d79b00;gradientColor=#ffce9f;shadow=1;fontSize=11;fontStyle=1;" parent="1" vertex="1">
                    <mxGeometry x="730" y="320" width="100" height="30" as="geometry"/>
                </mxCell>
                <mxCell id="security-center" value="安全层&#xa;(Security Layer)" style="shape=hexagon;perimeter=hexagonPerimeter2;whiteSpace=wrap;html=1;fixedSize=1;size=40;fillColor=#f8cecc;strokeColor=#b85450;gradientColor=#ea6b66;fontSize=18;fontStyle=1;shadow=1;" parent="1" vertex="1">
                    <mxGeometry x="475" y="400" width="200" height="120" as="geometry"/>
                </mxCell>
                <mxCell id="security-module-1" value="E2EE 端到端加密" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f8cecc;strokeColor=#b85450;gradientColor=#ea6b66;shadow=1;fontSize=11;fontStyle=1;" parent="1" vertex="1">
                    <mxGeometry x="390" y="445" width="100" height="30" as="geometry"/>
                </mxCell>
                <mxCell id="security-module-2" value="MFA 多因素认证" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f8cecc;strokeColor=#b85450;gradientColor=#ea6b66;shadow=1;fontSize=11;fontStyle=1;" parent="1" vertex="1">
                    <mxGeometry x="420" y="500" width="100" height="30" as="geometry"/>
                </mxCell>
                <mxCell id="security-module-3" value="ECDH 密钥交换" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f8cecc;strokeColor=#b85450;gradientColor=#ea6b66;shadow=1;fontSize=11;fontStyle=1;" parent="1" vertex="1">
                    <mxGeometry x="610" y="500" width="100" height="30" as="geometry"/>
                </mxCell>
                <mxCell id="security-module-4" value="TOTP 防重放保护" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f8cecc;strokeColor=#b85450;gradientColor=#ea6b66;shadow=1;fontSize=11;fontStyle=1;" parent="1" vertex="1">
                    <mxGeometry x="660" y="445" width="120" height="30" as="geometry"/>
                </mxCell>
                <mxCell id="cloud-to-mobile" value="" style="endArrow=classic;startArrow=classic;html=1;rounded=0;curved=1;strokeWidth=2;exitX=0.25;exitY=1;exitDx=0;exitDy=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;strokeColor=#4D4D4D;shadow=0;" parent="1" source="cloud-platform" target="mobile-side" edge="1">
                    <mxGeometry width="50" height="50" relative="1" as="geometry">
                        <mxPoint x="480" y="200" as="sourcePoint"/>
                        <mxPoint x="310" y="260" as="targetPoint"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="cloud-to-mobile-protocol" value="HTTPS/REST API" style="text;html=1;strokeColor=#666666;fillColor=#f5f5f5;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=1;fontSize=11;fontStyle=1;gradientColor=#b3b3b3;" parent="1" vertex="1">
                    <mxGeometry x="370" y="140" width="100" height="20" as="geometry"/>
                </mxCell>
                <mxCell id="cloud-to-car" value="" style="endArrow=classic;startArrow=classic;html=1;rounded=0;curved=1;strokeWidth=2;entryX=0.5;entryY=0;entryDx=0;entryDy=0;exitX=0.75;exitY=1;exitDx=0;exitDy=0;strokeColor=#4D4D4D;" parent="1" source="cloud-platform" target="car-side" edge="1">
                    <mxGeometry width="50" height="50" relative="1" as="geometry">
                        <mxPoint x="660" y="200" as="sourcePoint"/>
                        <mxPoint x="780" y="260" as="targetPoint"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="cloud-to-car-protocol" value="MQTT/WebSocket/HTTPS" style="text;html=1;strokeColor=#666666;fillColor=#f5f5f5;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=1;fontSize=11;fontStyle=1;gradientColor=#b3b3b3;" parent="1" vertex="1">
                    <mxGeometry x="705" y="140" width="150" height="20" as="geometry"/>
                </mxCell>
                <mxCell id="mobile-to-car" value="" style="endArrow=classic;startArrow=classic;html=1;rounded=0;curved=1;strokeWidth=2;entryX=0;entryY=0.5;entryDx=0;entryDy=0;exitX=1;exitY=0.5;exitDx=0;exitDy=0;strokeColor=#4D4D4D;" parent="1" source="mobile-side" target="car-side" edge="1">
                    <mxGeometry width="50" height="50" relative="1" as="geometry">
                        <mxPoint x="430" y="350" as="sourcePoint"/>
                        <mxPoint x="740" y="350" as="targetPoint"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="mobile-to-car-protocol" value="BLE 5.0+/GATT" style="text;html=1;strokeColor=#666666;fillColor=#f5f5f5;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=1;fontSize=11;fontStyle=1;gradientColor=#b3b3b3;" parent="1" vertex="1">
                    <mxGeometry x="535" y="250" width="100" height="20" as="geometry"/>
                </mxCell>
                <mxCell id="cloud-to-mobile-auth" value="Token请求/刷新" style="text;html=1;strokeColor=#666666;fillColor=#f5f5f5;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=1;fontSize=9;fontStyle=1;gradientColor=#b3b3b3;" parent="1" vertex="1">
                    <mxGeometry x="320" y="170" width="90" height="15" as="geometry"/>
                </mxCell>
                <mxCell id="cloud-to-mobile-key" value="授权密钥下发" style="text;html=1;strokeColor=#666666;fillColor=#f5f5f5;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=1;fontSize=9;fontStyle=1;gradientColor=#b3b3b3;" parent="1" vertex="1">
                    <mxGeometry x="410" y="170" width="80" height="15" as="geometry"/>
                </mxCell>
                <mxCell id="cloud-to-car-status" value="车辆状态上报" style="text;html=1;strokeColor=#666666;fillColor=#f5f5f5;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=1;fontSize=9;fontStyle=1;gradientColor=#b3b3b3;" parent="1" vertex="1">
                    <mxGeometry x="680" y="170" width="80" height="15" as="geometry"/>
                </mxCell>
                <mxCell id="cloud-to-car-ota" value="OTA更新" style="text;html=1;strokeColor=#666666;fillColor=#f5f5f5;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=1;fontSize=9;fontStyle=1;gradientColor=#b3b3b3;" parent="1" vertex="1">
                    <mxGeometry x="770" y="170" width="60" height="15" as="geometry"/>
                </mxCell>
                <mxCell id="mobile-to-car-broadcast" value="BLE广播接收" style="text;html=1;strokeColor=#666666;fillColor=#f5f5f5;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=1;fontSize=9;fontStyle=1;gradientColor=#b3b3b3;" parent="1" vertex="1">
                    <mxGeometry x="535" y="225" width="80" height="15" as="geometry"/>
                </mxCell>
                <mxCell id="mobile-to-car-command" value="控车指令加密传输" style="text;html=1;strokeColor=#666666;fillColor=#f5f5f5;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=1;fontSize=9;fontStyle=1;gradientColor=#b3b3b3;" parent="1" vertex="1">
                    <mxGeometry x="535" y="275" width="100" height="15" as="geometry"/>
                </mxCell>
                <mxCell id="mobile-to-car-pke" value="PKE被动进入" style="text;html=1;strokeColor=#666666;fillColor=#f5f5f5;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=1;fontSize=9;fontStyle=1;gradientColor=#b3b3b3;" parent="1" vertex="1">
                    <mxGeometry x="535" y="300" width="80" height="15" as="geometry"/>
                </mxCell>
                <mxCell id="security-to-all" value="" style="endArrow=classic;startArrow=classic;html=1;rounded=0;curved=1;strokeWidth=1;dashed=1;entryX=0.5;entryY=1;entryDx=0;entryDy=0;exitX=0;exitY=0.5;exitDx=0;exitDy=0;strokeColor=#b85450;" parent="1" source="security-center" target="mobile-side" edge="1">
                    <mxGeometry width="50" height="50" relative="1" as="geometry">
                        <mxPoint x="430" y="450" as="sourcePoint"/>
                        <mxPoint x="480" y="400" as="targetPoint"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="security-to-all2" value="" style="endArrow=classic;startArrow=classic;html=1;rounded=0;curved=1;strokeWidth=1;dashed=1;entryX=0.5;entryY=1;entryDx=0;entryDy=0;exitX=1;exitY=0.5;exitDx=0;exitDy=0;strokeColor=#b85450;" parent="1" source="security-center" target="car-side" edge="1">
                    <mxGeometry width="50" height="50" relative="1" as="geometry">
                        <mxPoint x="430" y="450" as="sourcePoint"/>
                        <mxPoint x="480" y="400" as="targetPoint"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="security-to-all3" value="" style="endArrow=classic;startArrow=classic;html=1;rounded=0;curved=1;strokeWidth=1;dashed=1;entryX=0.5;entryY=1;entryDx=0;entryDy=0;exitX=0.5;exitY=0;exitDx=0;exitDy=0;strokeColor=#b85450;" parent="1" source="security-center" target="cloud-platform" edge="1">
                    <mxGeometry width="50" height="50" relative="1" as="geometry">
                        <mxPoint x="430" y="450" as="sourcePoint"/>
                        <mxPoint x="480" y="400" as="targetPoint"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="security-tls" value="TLS 1.3 安全通道" style="text;html=1;strokeColor=#b85450;fillColor=#f8cecc;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=1;fontSize=9;fontStyle=1;gradientColor=#ea6b66;" parent="1" vertex="1">
                    <mxGeometry x="520" y="370" width="100" height="15" as="geometry"/>
                </mxCell>
                <mxCell id="user-app-layer" value="用户端应用层&#xa;(User App Layer)" style="shape=hexagon;perimeter=hexagonPerimeter2;whiteSpace=wrap;html=1;fixedSize=1;size=20;fillColor=#e1d5e7;strokeColor=#9673a6;gradientColor=#d5739d;fontSize=14;fontStyle=1;shadow=1;" parent="1" vertex="1">
                    <mxGeometry x="90" y="60" width="150" height="90" as="geometry"/>
                </mxCell>
                <mxCell id="user-app-ui" value="UI交互界面" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#e1d5e7;strokeColor=#9673a6;gradientColor=#d5739d;shadow=1;fontSize=10;fontStyle=1;" parent="1" vertex="1">
                    <mxGeometry x="229" y="100" width="80" height="20" as="geometry"/>
                </mxCell>
                <mxCell id="user-app-auth" value="FIDO2认证" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#e1d5e7;strokeColor=#9673a6;gradientColor=#d5739d;shadow=1;fontSize=10;fontStyle=1;" parent="1" vertex="1">
                    <mxGeometry x="200" y="50" width="80" height="20" as="geometry"/>
                </mxCell>
                <mxCell id="user-app-status" value="车辆状态展示" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#e1d5e7;strokeColor=#9673a6;gradientColor=#d5739d;shadow=1;fontSize=10;fontStyle=1;" parent="1" vertex="1">
                    <mxGeometry x="200" y="140" width="80" height="20" as="geometry"/>
                </mxCell>
                <mxCell id="user-app-to-mobile" value="" style="endArrow=classic;startArrow=classic;html=1;rounded=0;curved=1;strokeWidth=1;entryX=0.067;entryY=0.25;entryDx=0;entryDy=0;exitX=0.5;exitY=1;exitDx=0;exitDy=0;strokeColor=#9673a6;entryPerimeter=0;" parent="1" source="user-app-layer" target="mobile-side" edge="1">
                    <mxGeometry width="50" height="50" relative="1" as="geometry">
                        <mxPoint x="430" y="350" as="sourcePoint"/>
                        <mxPoint x="480" y="300" as="targetPoint"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="user-app-sdk" value="SDK集成" style="text;html=1;strokeColor=#9673a6;fillColor=#e1d5e7;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=1;fontSize=9;fontStyle=1;gradientColor=#d5739d;" parent="1" vertex="1">
                    <mxGeometry x="160" y="170" width="60" height="15" as="geometry"/>
                </mxCell>
                <mxCell id="vehicle-control-layer" value="车辆控制执行层&#xa;(Vehicle Control Layer)" style="shape=hexagon;perimeter=hexagonPerimeter2;whiteSpace=wrap;html=1;fixedSize=1;size=20;fillColor=#fff2cc;strokeColor=#d6b656;gradientColor=#ffd966;fontSize=14;fontStyle=1;shadow=1;" parent="1" vertex="1">
                    <mxGeometry x="950" y="60" width="150" height="90" as="geometry"/>
                </mxCell>
                <mxCell id="vehicle-can" value="CAN总线交互" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#fff2cc;strokeColor=#d6b656;gradientColor=#ffd966;shadow=1;fontSize=10;fontStyle=1;" parent="1" vertex="1">
                    <mxGeometry x="920" y="40" width="80" height="20" as="geometry"/>
                </mxCell>
                <mxCell id="vehicle-bcm" value="BCM通信" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#fff2cc;strokeColor=#d6b656;gradientColor=#ffd966;shadow=1;fontSize=10;fontStyle=1;" parent="1" vertex="1">
                    <mxGeometry x="880" y="90" width="80" height="20" as="geometry"/>
                </mxCell>
                <mxCell id="vehicle-door" value="车门锁执行器" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#fff2cc;strokeColor=#d6b656;gradientColor=#ffd966;shadow=1;fontSize=10;fontStyle=1;" parent="1" vertex="1">
                    <mxGeometry x="900" y="140" width="80" height="20" as="geometry"/>
                </mxCell>
                <mxCell id="vehicle-to-car" value="" style="endArrow=classic;startArrow=classic;html=1;rounded=0;curved=1;strokeWidth=1;entryX=0.933;entryY=0.25;entryDx=0;entryDy=0;exitX=0.5;exitY=1;exitDx=0;exitDy=0;strokeColor=#d6b656;entryPerimeter=0;" parent="1" source="vehicle-control-layer" target="car-side" edge="1">
                    <mxGeometry width="50" height="50" relative="1" as="geometry">
                        <mxPoint x="830" y="350" as="sourcePoint"/>
                        <mxPoint x="880" y="300" as="targetPoint"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="vehicle-can-protocol" value="CAN/LIN协议" style="text;html=1;strokeColor=#d6b656;fillColor=#fff2cc;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=1;fontSize=9;fontStyle=1;gradientColor=#ffd966;" parent="1" vertex="1">
                    <mxGeometry x="960" y="170" width="80" height="15" as="geometry"/>
                </mxCell>
                <mxCell id="legend-title" value="图例说明 (Legend)" style="text;html=1;strokeColor=none;fillColor=none;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=14;fontStyle=1;fontColor=#333333;" parent="1" vertex="1">
                    <mxGeometry x="70" y="550" width="150" height="20" as="geometry"/>
                </mxCell>
                <mxCell id="legend-line1" value="" style="endArrow=classic;startArrow=classic;html=1;rounded=0;strokeWidth=2;strokeColor=#4D4D4D;" parent="1" edge="1">
                    <mxGeometry width="50" height="50" relative="1" as="geometry">
                        <mxPoint x="100" y="590" as="sourcePoint"/>
                        <mxPoint x="160" y="590" as="targetPoint"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="legend-line1-text" value="主要数据交互" style="text;html=1;strokeColor=none;fillColor=none;align=left;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=10;" parent="1" vertex="1">
                    <mxGeometry x="170" y="580" width="100" height="20" as="geometry"/>
                </mxCell>
                <mxCell id="legend-line2" value="" style="endArrow=classic;startArrow=classic;html=1;rounded=0;strokeWidth=1;dashed=1;strokeColor=#b85450;" parent="1" edge="1">
                    <mxGeometry width="50" height="50" relative="1" as="geometry">
                        <mxPoint x="100" y="610" as="sourcePoint"/>
                        <mxPoint x="160" y="610" as="targetPoint"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="legend-line2-text" value="安全通道" style="text;html=1;strokeColor=none;fillColor=none;align=left;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=10;" parent="1" vertex="1">
                    <mxGeometry x="170" y="600" width="100" height="20" as="geometry"/>
                </mxCell>
                <mxCell id="legend-line3" value="" style="endArrow=classic;startArrow=classic;html=1;rounded=0;strokeWidth=1;strokeColor=#9673a6;" parent="1" edge="1">
                    <mxGeometry width="50" height="50" relative="1" as="geometry">
                        <mxPoint x="100" y="630" as="sourcePoint"/>
                        <mxPoint x="160" y="630" as="targetPoint"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="legend-line3-text" value="应用层交互" style="text;html=1;strokeColor=none;fillColor=none;align=left;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=10;" parent="1" vertex="1">
                    <mxGeometry x="170" y="620" width="100" height="20" as="geometry"/>
                </mxCell>
                <mxCell id="legend-note" value="注释: BLE (Bluetooth Low Energy), PKI (Public Key Infrastructure), HSM (Hardware Security Module), TSP (Telematics Service Provider), PKE (Passive Keyless Entry), FIDO2 (Fast Identity Online 2.0), BCM (Body Control Module)" style="text;html=1;strokeColor=none;fillColor=none;align=left;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=10;fontColor=#666666;" parent="1" vertex="1">
                    <mxGeometry x="70" y="660" width="500" height="30" as="geometry"/>
                </mxCell>
                <mxCell id="user-app-to-cloud" value="" style="endArrow=classic;startArrow=classic;html=1;rounded=0;curved=1;strokeWidth=1;entryX=0;entryY=0.25;entryDx=0;entryDy=0;exitX=1;exitY=0.25;exitDx=0;exitDy=0;strokeColor=#9673a6;" parent="1" source="user-app-layer" target="cloud-platform" edge="1">
                    <mxGeometry width="50" height="50" relative="1" as="geometry">
                        <mxPoint x="300" y="80" as="sourcePoint"/>
                        <mxPoint x="350" y="30" as="targetPoint"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="fido2-to-oauth" value="FIDO2-OAuth集成" style="text;html=1;strokeColor=#9673a6;fillColor=#e1d5e7;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=1;fontSize=9;fontStyle=1;gradientColor=#d5739d;" parent="1" vertex="1">
                    <mxGeometry x="322" y="52" width="100" height="15" as="geometry"/>
                </mxCell>
            </root>
        </mxGraphModel>
    </diagram>
</mxfile>
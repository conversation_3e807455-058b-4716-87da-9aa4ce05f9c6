<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>车载智能钥匙系统 - 优势介绍</title>
    <script src="https://html2canvas.hertzen.com/dist/html2canvas.min.js"></script>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
            font-family: 'PingFang SC', 'Microsoft YaHei', sans-serif;
        }
        
        body {
            background: #f0f0f0;
            display: flex;
            justify-content: center;
            align-items: center;
            min-height: 100vh;
            color: #333;
            padding: 20px;
        }
        
        .ppt-container {
            position: relative;
            width: 1920px;
            height: 1080px;
            box-shadow: 0 0 30px rgba(0, 0, 0, 0.2);
            border: 2px dashed #ccc;
            background-image: linear-gradient(45deg, #ccc 25%, transparent 25%), 
                            linear-gradient(-45deg, #ccc 25%, transparent 25%), 
                            linear-gradient(45deg, transparent 75%, #ccc 75%), 
                            linear-gradient(-45deg, transparent 75%, #ccc 75%);
            background-size: 20px 20px;
            background-position: 0 0, 0 10px, 10px -10px, -10px 0px;
        }
        
        .ppt-label {
            position: absolute;
            top: -40px;
            left: 0;
            background-color: #555;
            color: white;
            padding: 5px 15px;
            border-radius: 4px;
            font-size: 16px;
        }
        
        .copy-button, .download-button {
            position: absolute;
            top: 20px;
            right: 20px;
            background: linear-gradient(135deg, #3498db, #2980b9);
            color: white;
            border: none;
            padding: 12px 25px;
            border-radius: 30px;
            font-size: 18px;
            cursor: pointer;
            z-index: 1000;
            box-shadow: 0 4px 10px rgba(0, 0, 0, 0.2);
            display: flex;
            align-items: center;
            gap: 10px;
            transition: all 0.3s ease;
        }
        
        .download-button {
            right: 230px;
            background: linear-gradient(135deg, #2ecc71, #27ae60);
        }
        
        .slide {
            width: 100%;
            height: 100%;
            background-color: transparent;
            padding: 60px;
            display: flex;
            flex-direction: column;
            overflow: hidden;
            position: relative;
        }
        
        .header {
            text-align: center;
            margin-bottom: 40px;
        }
        
        .header h1 {
            font-size: 42px;
            margin-bottom: 15px;
            color: #2c3e50;
        }
        
        .header .subtitle {
            font-size: 22px;
            color: #7f8c8d;
            font-weight: 300;
        }
        
        .advantages-grid {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            gap: 25px;
            padding: 0 20px;
        }
        
        .advantage-card {
            background: white;
            border-radius: 20px;
            padding: 25px;
            display: flex;
            align-items: flex-start;
            gap: 20px;
            box-shadow: 0 8px 20px rgba(0, 0, 0, 0.07);
            transition: all 0.3s ease;
        }
        
        .advantage-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 12px 25px rgba(0, 0, 0, 0.1);
        }
        
        .advantage-icon {
            width: 60px;
            height: 60px;
            border-radius: 15px;
            display: flex;
            align-items: center;
            justify-content: center;
            flex-shrink: 0;
        }
        
        .advantage-icon.security {
            background: linear-gradient(135deg, #3498db, #2980b9);
        }
        
        .advantage-icon.convenience {
            background: linear-gradient(135deg, #2ecc71, #27ae60);
        }
        
        .advantage-icon.reliability {
            background: linear-gradient(135deg, #9b59b6, #8e44ad);
        }
        
        .advantage-icon.extensibility {
            background: linear-gradient(135deg, #e67e22, #d35400);
        }
        
        .advantage-icon.power {
            background: linear-gradient(135deg, #1abc9c, #16a085);
        }
        
        .advantage-icon.business {
            background: linear-gradient(135deg, #f39c12, #f1c40f);
        }
        
        .advantage-icon img {
            width: 30px;
            height: 30px;
            filter: brightness(0) invert(1);
        }
        
        .advantage-content {
            flex: 1;
        }
        
        .advantage-title {
            font-size: 22px;
            font-weight: 600;
            color: #2c3e50;
            margin-bottom: 10px;
        }
        
        .advantage-desc {
            font-size: 15px;
            color: #7f8c8d;
            line-height: 1.6;
        }
        
        .advantage-features {
            margin-top: 15px;
            list-style: none;
        }
        
        .advantage-features li {
            font-size: 14px;
            color: #666;
            margin-bottom: 8px;
            padding-left: 20px;
            position: relative;
        }
        
        .advantage-features li:before {
            content: "•";
            color: #3498db;
            position: absolute;
            left: 0;
        }
        
        .toast {
            position: fixed;
            top: 50px;
            left: 50%;
            transform: translateX(-50%);
            background-color: rgba(0, 0, 0, 0.8);
            color: white;
            padding: 12px 25px;
            border-radius: 30px;
            font-size: 16px;
            z-index: 2000;
            display: none;
            animation: fadeInOut 2s ease;
        }
        
        @keyframes fadeInOut {
            0% { opacity: 0; }
            20% { opacity: 1; }
            80% { opacity: 1; }
            100% { opacity: 0; }
        }
    </style>
</head>
<body>
    <div class="ppt-container" id="pptContainer">
        <div class="ppt-label">PPT尺寸(1920×1080) - 透明背景</div>
        <button class="copy-button" id="copyButton">
            <span class="copy-icon"></span>复制为PPT图片
        </button>
        <button class="download-button" id="downloadButton">
            <span class="download-icon"></span>下载透明PNG
        </button>
        
        <div class="slide" id="slide">
            <div class="header">
                <h1>车载智能钥匙系统优势</h1>
                <div class="subtitle">全方位解决方案，引领智能出行新体验</div>
            </div>
            
            <div class="advantages-grid">
                <!-- 安全性优势 -->
                <div class="advantage-card">
                    <div class="advantage-icon security">
                        <img src="data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHZpZXdCb3g9IjAgMCAyNCAyNCI+PHBhdGggZmlsbD0id2hpdGUiIGQ9Ik0xMiwxQzcuMDQsMSAzLDUuMDQgMywxMGMwLDQuOTYgNC4wNCw5IDksOXM5LTQuMDQgOS05QzIxLDUuMDQgMTYuOTYsMSAxMiwxeiBNMTIsMTlDNC4yOCwxOSA0LDEzLjcyIDQsMTJjMC0xLjcyLjI4LTcgOC03czgsNS4yOCA4LDdDMTksMTMuNzIgMTguNzIsMTkgMTIsMTl6Ii8+PC9zdmc+"/>
                    </div>
                    <div class="advantage-content">
                        <div class="advantage-title">全方位安全防护</div>
                        <div class="advantage-desc">多重安全机制确保数字钥匙安全性</div>
                        <ul class="advantage-features">
                            <li>端到端加密通信防止数据泄露</li>
                            <li>生物识别认证确保身份安全</li>
                            <li>防重放攻击机制避免密钥复制</li>
                            <li>实时安全监控快速响应异常</li>
                        </ul>
                    </div>
                </div>
                
                <!-- 便捷性优势 -->
                <div class="advantage-card">
                    <div class="advantage-icon convenience">
                        <img src="data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHZpZXdCb3g9IjAgMCAyNCAyNCI+PHBhdGggZmlsbD0id2hpdGUiIGQ9Ik0xMiwyQzYuNDgsMiAyLDYuNDggMiwxMnM0LjQ4LDEwIDEwLDEwIDEwLTQuNDggMTAtMTBTMTcuNTIsMiAxMiwyeiBNMTIsMjBjLTQuNDEsMC04LTMuNTktOC04czMuNTktOCA4LTggOCwzLjU5IDgsOFMxNi40MSwyMCAxMiwyMHoiLz48L3N2Zz4="/>
                    </div>
                    <div class="advantage-content">
                        <div class="advantage-title">极致便捷体验</div>
                        <div class="advantage-desc">简化用户操作流程提供无缝体验</div>
                        <ul class="advantage-features">
                            <li>无感解锁，靠近车辆自动识别</li>
                            <li>远程控制，随时随地管理车辆</li>
                            <li>一键分享，轻松授权家人使用</li>
                            <li>智能场景联动，定制化操作</li>
                        </ul>
                    </div>
                </div>
                
                <!-- 低功耗优势 - 新增 -->
                <div class="advantage-card">
                    <div class="advantage-icon power">
                        <img src="data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHZpZXdCb3g9IjAgMCAyNCAyNCI+PHBhdGggZmlsbD0id2hpdGUiIGQ9Ik0xMiwxMlYyMEwxMy41LDE0SDIwVjEySDEzLjVMMTIsNkw5LDEySDE2VjExSDloM3Y0SDQuNVYxMEgzVjE3SDQuNUwxMiwyMFYxNkgxNlYxNkgxOVYxMEgyMVYxMEgxNlYxMkgxMnoiLz48L3N2Zz4="/>
                    </div>
                    <div class="advantage-content">
                        <div class="advantage-title">智能节能技术</div>
                        <div class="advantage-desc">独创低功耗策略延长使用寿命</div>
                        <ul class="advantage-features">
                            <li>智能休眠模式，降低90%待机功耗</li>
                            <li>上下文感知唤醒，精准控制工作状态</li>
                            <li>动态功率管理，按需调整性能</li>
                            <li>光能微充技术，无需频繁更换电池</li>
                        </ul>
                    </div>
                </div>
                
                <!-- 可靠性优势 -->
                <div class="advantage-card">
                    <div class="advantage-icon reliability">
                        <img src="data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHZpZXdCb3g9IjAgMCAyNCAyNCI+PHBhdGggZmlsbD0id2hpdGUiIGQ9Ik0xMiwyQzYuNDgsMiAyLDYuNDggMiwxMnM0LjQ4LDEwIDEwLDEwIDEwLTQuNDggMTAtMTBTMTcuNTIsMiAxMiwyeiBNMTAsMTdsLTUtNWwxLjQxLTEuNDFMMTAsMTQuMTdsNy41OS03LjU5TDE5LDhsLTksOXoiLz48L3N2Zz4="/>
                    </div>
                    <div class="advantage-content">
                        <div class="advantage-title">高可靠性保障</div>
                        <div class="advantage-desc">多重保障机制确保系统稳定运行</div>
                        <ul class="advantage-features">
                            <li>双通道通信确保信号稳定</li>
                            <li>智能容错自动切换备用方案</li>
                            <li>防干扰技术抵御外部信号干扰</li>
                            <li>全天候环境适应性设计</li>
                        </ul>
                    </div>
                </div>
                
                <!-- 扩展性优势 -->
                <div class="advantage-card">
                    <div class="advantage-icon extensibility">
                        <img src="data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHZpZXdCb3g9IjAgMCAyNCAyNCI+PHBhdGggZmlsbD0id2hpdGUiIGQ9Ik0xOSwxM2gtNlY3aDZ2NnptLTgsNnYtNkg1djZoNnptOC0xMHY2aDZWN2MwLTEuMS0uOS0yLTItMkgxOXptLTgsMHY2SDVWN2g2ek0xOSw1SDVjLTEuMSwwLTIsMC45LTIsMnY2aDZWNzBoMTBWN0MyLDIgMiwwLjkgMiwxLjF6Ii8+PC9zdmc+"/>
                    </div>
                    <div class="advantage-content">
                        <div class="advantage-title">强大扩展能力</div>
                        <div class="advantage-desc">灵活系统架构支持功能持续扩展</div>
                        <ul class="advantage-features">
                            <li>模块化设计易于功能扩展</li>
                            <li>开放API支持第三方生态集成</li>
                            <li>OTA升级持续优化用户体验</li>
                            <li>多车型快速适配部署</li>
                        </ul>
                    </div>
                </div>
                
                <!-- 商业模式优势 - 新增 -->
                <div class="advantage-card">
                    <div class="advantage-icon business">
                        <img src="data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHZpZXdCb3g9IjAgMCAyNCAyNCI+PHBhdGggZmlsbD0id2hpdGUiIGQ9Ik0yMCw2QzIwLjU4LDYgMjEuMDUsNi4yIDIxLjQyLDYuNThDMjEuOCw2Ljk1IDIyLDcuNDIgMjIsOFYxOUMyMiwxOS41OCAyMS44LDIwLjA1IDIxLjQyLDIwLjQyQzIxLjA1LDIwLjggMjAuNTgsMjEgMjAsMjFIMTcuOTFDMTcuNTcsMjEgMTcuMjgsMjAuODUgMTcuMDQsMjAuNTdMMTIsMTQuMTdMMTMuMTcsMTNMMTcuOTEsMTlIMjBWOEgxNy45MUwxMiwyLjgzTDYuMDksOEg0VjE5SDYuMDlMMTAuNDMsMTVIMTMuMTdMMTAuMDMsMTlIMy42MUMzLjI4LDE5IDMsMTguNzIgMywyMC4zOVYxNy41OUw3LjUsMTQuMTdMNi4zMywxM0wzLjQsMTUuMzlDMy4xNSwxNS42NSAzLDE1Ljk0IDMsMTYuMjVWNUMzLDQuNDIgMy4yLDMuOTUgMy41OCwzLjU4QzMuOTUsMy4yIDQuNDIsMyA1LDNDNS4yNSwzIDUuNiwzLjA5IDYuMDksMy42NkwxMiw5LjE3TDE3LjkxLDMuNjZDMTguNCwzLjA5IDE4Ljc1LDMgMTksM0MxOS41OCwzIDIwLjA1LDMuMiAyMC40MiwzLjU4QzIwLjgsMy45NSAyMSw0LjQyIDIxLDVWMTZDMjEsMTYuNDEgMjAuODMsMTYuOCAyMC40OCwxNy4xN0wxOS4xNywxOC40MUMxOC41NSwxOSAxOCwxOS4yIDE3LjUsMTkuMkgxMC44OUw5LDE3LjM5VjE2LjA5TDYuMDksMTMuMTdMMy41OSw2LjA5TDYuMDksOC41OU0xMiw2TDE3LjAzLDlIMTUuNUwxMiw3TDguNDcsOUg2Ljk0TDEyLDZ6Ii8+PC9zdmc+"/>
                    </div>
                    <div class="advantage-content">
                        <div class="advantage-title">创新商业模式</div>
                        <div class="advantage-desc">突破传统边界创造多元价值</div>
                        <ul class="advantage-features">
                            <li>数字钥匙即服务(KaaS)商业模式</li>
                            <li>车主增值服务拓展收入渠道</li>
                            <li>共享出行无缝对接解决方案</li>
                            <li>数据价值挖掘提升运营效率</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="toast" id="toast">操作成功！</div>
    
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const copyButton = document.getElementById('copyButton');
            const downloadButton = document.getElementById('downloadButton');
            const toast = document.getElementById('toast');
            const slide = document.getElementById('slide');
            
            function showToast(message) {
                toast.textContent = message;
                toast.style.display = 'block';
                setTimeout(function() {
                    toast.style.display = 'none';
                }, 2000);
            }
            
            function generateImage() {
                return html2canvas(slide, {
                    scale: 2,
                    useCORS: true,
                    allowTaint: true,
                    backgroundColor: null
                });
            }
            
            copyButton.addEventListener('click', function() {
                showToast("正在处理图片，请稍候...");
                
                generateImage().then(function(canvas) {
                    try {
                        canvas.toBlob(function(blob) {
                            try {
                                const item = new ClipboardItem({ 'image/png': blob });
                                navigator.clipboard.write([item]).then(function() {
                                    showToast("已复制到剪贴板！现在可以粘贴到PPT中");
                                }).catch(function(err) {
                                    showToast("自动复制失败，请使用下载按钮获取图片");
                                    console.error("剪贴板API错误:", err);
                                });
                            } catch (e) {
                                showToast("您的浏览器不支持复制图片，请使用下载按钮");
                                console.error("ClipboardItem错误:", e);
                            }
                        });
                    } catch (e) {
                        showToast("复制功能不可用，请使用下载图片功能");
                        console.error("toBlob错误:", e);
                    }
                }).catch(function(err) {
                    showToast("生成图片失败，请尝试下载或截图");
                    console.error("html2canvas错误:", err);
                });
            });
            
            downloadButton.addEventListener('click', function() {
                showToast("正在生成高清透明PNG图片，请稍候...");
                
                generateImage().then(function(canvas) {
                    const imageUrl = canvas.toDataURL('image/png');
                    const link = document.createElement('a');
                    link.download = "车载智能钥匙系统优势-透明背景.png";
                    link.href = imageUrl;
                    link.click();
                    
                    showToast("高清透明背景PNG已下载");
                }).catch(function(err) {
                    showToast("生成图片失败，请尝试截图");
                    console.error("Error:", err);
                });
            });
        });
    </script>
</body>
</html> 
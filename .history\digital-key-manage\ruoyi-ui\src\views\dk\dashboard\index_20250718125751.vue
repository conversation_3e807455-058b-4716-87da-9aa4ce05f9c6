<template>
  <div class="dashboard-container">
    <!-- 统计卡片 -->
    <el-row :gutter="20" class="stats-row">
      <el-col :span="6">
        <el-card class="stats-card">
          <div class="stats-content">
            <div class="stats-icon pending">
              <i class="el-icon-document"></i>
            </div>
            <div class="stats-info">
              <div class="stats-number">{{ dashboardData.pendingApplications }}</div>
              <div class="stats-label">待审核申请</div>
            </div>
          </div>
          <div class="stats-footer">
            <el-button type="text" @click="goToApplications">立即处理</el-button>
          </div>
        </el-card>
      </el-col>
      
      <el-col :span="6">
        <el-card class="stats-card">
          <div class="stats-content">
            <div class="stats-icon success">
              <i class="el-icon-key"></i>
            </div>
            <div class="stats-info">
              <div class="stats-number">{{ dashboardData.todayAssignments }}</div>
              <div class="stats-label">今日钥匙分配</div>
            </div>
          </div>
          <div class="stats-footer">
            <el-button type="text" @click="goToKeyAssignment">查看详情</el-button>
          </div>
        </el-card>
      </el-col>
      
      <el-col :span="6">
        <el-card class="stats-card">
          <div class="stats-content">
            <div class="stats-icon primary">
              <i class="el-icon-truck"></i>
            </div>
            <div class="stats-info">
              <div class="stats-number">{{ dashboardData.availableVehicles }}</div>
              <div class="stats-label">可用车辆</div>
            </div>
          </div>
          <div class="stats-footer">
            <el-button type="text" @click="goToVehicles">车辆管理</el-button>
          </div>
        </el-card>
      </el-col>
      
      <el-col :span="6">
        <el-card class="stats-card">
          <div class="stats-content">
            <div class="stats-icon warning">
              <i class="el-icon-warning"></i>
            </div>
            <div class="stats-info">
              <div class="stats-number">{{ dashboardData.restrictedUsers }}</div>
              <div class="stats-label">权限限制用户</div>
            </div>
          </div>
          <div class="stats-footer">
            <el-button type="text" @click="goToKeyManagement">权限管理</el-button>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 主要内容区域 -->
    <el-row :gutter="20" class="content-row">
      <!-- 待处理事项 -->
      <el-col :span="12">
        <el-card class="content-card">
          <div slot="header" class="card-header">
            <span>待处理事项</span>
            <el-button style="float: right; padding: 3px 0" type="text" @click="refreshTodoList">刷新</el-button>
          </div>
          
          <div class="todo-list">
            <div v-for="item in todoList" :key="item.id" class="todo-item" @click="handleTodoClick(item)">
              <div class="todo-icon">
                <i :class="item.icon" :style="{color: item.color}"></i>
              </div>
              <div class="todo-content">
                <div class="todo-title">{{ item.title }}</div>
                <div class="todo-desc">{{ item.description }}</div>
              </div>
              <div class="todo-time">{{ item.time }}</div>
            </div>
            
            <div v-if="todoList.length === 0" class="empty-todo">
              <i class="el-icon-check"></i>
              <p>暂无待处理事项</p>
            </div>
          </div>
        </el-card>
      </el-col>

      <!-- 车辆状态概览 -->
      <el-col :span="12">
        <el-card class="content-card">
          <div slot="header" class="card-header">
            <span>车辆状态概览</span>
            <el-button style="float: right; padding: 3px 0" type="text" @click="refreshVehicleStats">刷新</el-button>
          </div>
          
          <div class="vehicle-stats">
            <div class="vehicle-chart">
              <div class="simple-chart">
                <div class="chart-item" v-for="(item, key) in vehicleStatsDisplay" :key="key">
                  <div class="chart-bar" :style="{height: item.percentage + '%', backgroundColor: item.color}"></div>
                  <div class="chart-label">{{ item.label }}</div>
                  <div class="chart-value">{{ item.value }}</div>
                </div>
              </div>
            </div>
            
            <div class="vehicle-details">
              <div class="vehicle-detail-item">
                <span class="detail-label">总车辆数：</span>
                <span class="detail-value">{{ vehicleStats.total }}</span>
              </div>
              <div class="vehicle-detail-item">
                <span class="detail-label">使用中：</span>
                <span class="detail-value" style="color: #409EFF;">{{ vehicleStats.inUse }}</span>
              </div>
              <div class="vehicle-detail-item">
                <span class="detail-label">空闲：</span>
                <span class="detail-value" style="color: #67C23A;">{{ vehicleStats.available }}</span>
              </div>
              <div class="vehicle-detail-item">
                <span class="detail-label">维护中：</span>
                <span class="detail-value" style="color: #E6A23C;">{{ vehicleStats.maintenance }}</span>
              </div>
              <div class="vehicle-detail-item">
                <span class="detail-label">停用：</span>
                <span class="detail-value" style="color: #F56C6C;">{{ vehicleStats.disabled }}</span>
              </div>
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 最近活动 -->
    <el-row :gutter="20" class="activity-row">
      <el-col :span="24">
        <el-card class="content-card">
          <div slot="header" class="card-header">
            <span>最近活动</span>
            <el-button style="float: right; padding: 3px 0" type="text" @click="refreshActivityList">查看更多</el-button>
          </div>
          
          <el-timeline>
            <el-timeline-item
              v-for="activity in activityList"
              :key="activity.id"
              :timestamp="activity.time"
              :color="activity.color"
            >
              <div class="activity-content">
                <div class="activity-title">{{ activity.title }}</div>
                <div class="activity-desc">{{ activity.description }}</div>
              </div>
            </el-timeline-item>
          </el-timeline>
        </el-card>
      </el-col>
    </el-row>
  </div>
</template>

<script>
import { getDashboardData, getTodoList, getVehicleStats, getActivityList } from "@/api/dk/dashboard";

export default {
  name: "OperationDashboard",
  data() {
    return {
      // 仪表板数据
      dashboardData: {
        pendingApplications: 0,
        todayAssignments: 0,
        availableVehicles: 0,
        restrictedUsers: 0
      },
      // 待处理事项
      todoList: [],
      // 车辆统计
      vehicleStats: {
        total: 0,
        inUse: 0,
        available: 0,
        maintenance: 0,
        disabled: 0
      },
      // 最近活动
      activityList: []
    };
  },
  computed: {
    vehicleStatsDisplay() {
      const total = this.vehicleStats.total || 1;
      return [
        {
          label: '使用中',
          value: this.vehicleStats.inUse,
          percentage: (this.vehicleStats.inUse / total) * 100,
          color: '#409EFF'
        },
        {
          label: '空闲',
          value: this.vehicleStats.available,
          percentage: (this.vehicleStats.available / total) * 100,
          color: '#67C23A'
        },
        {
          label: '维护中',
          value: this.vehicleStats.maintenance,
          percentage: (this.vehicleStats.maintenance / total) * 100,
          color: '#E6A23C'
        },
        {
          label: '停用',
          value: this.vehicleStats.disabled,
          percentage: (this.vehicleStats.disabled / total) * 100,
          color: '#F56C6C'
        }
      ];
    }
  },
  mounted() {
    this.loadDashboardData();
    this.loadTodoList();
    this.loadVehicleStats();
    this.loadActivityList();
  },
  methods: {
    /** 加载仪表板数据 */
    loadDashboardData() {
      getDashboardData().then(response => {
        this.dashboardData = response.data;
      });
    },
    
    /** 加载待处理事项 */
    loadTodoList() {
      getTodoList().then(response => {
        this.todoList = response.data;
      });
    },
    
    /** 加载车辆统计 */
    loadVehicleStats() {
      getVehicleStats().then(response => {
        this.vehicleStats = response.data;
      });
    },
    
    /** 加载最近活动 */
    loadActivityList() {
      getActivityList().then(response => {
        this.activityList = response.data;
      });
    },
    

    
    /** 处理待办事项点击 */
    handleTodoClick(item) {
      switch (item.type) {
        case 'application':
          this.goToApplications();
          break;
        case 'vehicle':
          this.goToVehicles();
          break;
        case 'key':
          this.goToKeyManagement();
          break;
        default:
          break;
      }
    },
    
    /** 跳转到申请管理 */
    goToApplications() {
      this.$router.push('/dk/application');
    },
    
    /** 跳转到钥匙分配 */
    goToKeyAssignment() {
      this.$router.push('/dk/keyAssignment');
    },
    
    /** 跳转到车辆管理 */
    goToVehicles() {
      this.$router.push('/dk/vehicle');
    },
    
    /** 跳转到钥匙管理 */
    goToKeyManagement() {
      this.$router.push('/dk/vehicleBluetoothKeys');
    },
    
    /** 刷新待办列表 */
    refreshTodoList() {
      this.loadTodoList();
    },
    
    /** 刷新车辆统计 */
    refreshVehicleStats() {
      this.loadVehicleStats();
    },
    
    /** 刷新活动列表 */
    refreshActivityList() {
      this.loadActivityList();
    }
  }
};
</script>

<style scoped>
.dashboard-container {
  padding: 20px;
}

.stats-row {
  margin-bottom: 20px;
}

.stats-card {
  height: 120px;
}

.stats-content {
  display: flex;
  align-items: center;
  height: 60px;
}

.stats-icon {
  width: 60px;
  height: 60px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 15px;
}

.stats-icon i {
  font-size: 24px;
  color: white;
}

.stats-icon.pending {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.stats-icon.success {
  background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
}

.stats-icon.primary {
  background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
}

.stats-icon.warning {
  background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
}

.stats-info {
  flex: 1;
}

.stats-number {
  font-size: 28px;
  font-weight: bold;
  color: #303133;
  line-height: 1;
}

.stats-label {
  font-size: 14px;
  color: #909399;
  margin-top: 5px;
}

.stats-footer {
  text-align: right;
  margin-top: 10px;
}

.content-row {
  margin-bottom: 20px;
}

.content-card {
  height: 350px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.todo-list {
  height: 280px;
  overflow-y: auto;
}

.todo-item {
  display: flex;
  align-items: center;
  padding: 12px 0;
  border-bottom: 1px solid #f0f0f0;
  cursor: pointer;
  transition: background-color 0.3s;
}

.todo-item:hover {
  background-color: #f5f7fa;
}

.todo-icon {
  width: 40px;
  text-align: center;
}

.todo-icon i {
  font-size: 18px;
}

.todo-content {
  flex: 1;
  margin-left: 10px;
}

.todo-title {
  font-weight: bold;
  color: #303133;
  margin-bottom: 4px;
}

.todo-desc {
  font-size: 12px;
  color: #909399;
}

.todo-time {
  font-size: 12px;
  color: #c0c4cc;
}

.empty-todo {
  text-align: center;
  padding: 40px 0;
  color: #909399;
}

.empty-todo i {
  font-size: 48px;
  margin-bottom: 10px;
}

.vehicle-stats {
  height: 280px;
  display: flex;
}

.vehicle-chart {
  flex: 1;
}

.simple-chart {
  display: flex;
  align-items: end;
  justify-content: space-around;
  height: 200px;
  padding: 20px;
}

.chart-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  width: 60px;
}

.chart-bar {
  width: 40px;
  min-height: 10px;
  border-radius: 4px 4px 0 0;
  margin-bottom: 8px;
  transition: all 0.3s ease;
}

.chart-bar:hover {
  opacity: 0.8;
}

.chart-label {
  font-size: 12px;
  color: #606266;
  margin-bottom: 4px;
}

.chart-value {
  font-size: 14px;
  font-weight: bold;
  color: #303133;
}

.vehicle-details {
  width: 120px;
  padding-left: 20px;
  display: flex;
  flex-direction: column;
  justify-content: center;
}

.vehicle-detail-item {
  margin-bottom: 15px;
  display: flex;
  justify-content: space-between;
}

.detail-label {
  font-size: 14px;
  color: #606266;
}

.detail-value {
  font-weight: bold;
  font-size: 16px;
}

.activity-row {
  margin-bottom: 20px;
}

.activity-content {
  margin-bottom: 10px;
}

.activity-title {
  font-weight: bold;
  color: #303133;
  margin-bottom: 4px;
}

.activity-desc {
  font-size: 14px;
  color: #606266;
}
</style>

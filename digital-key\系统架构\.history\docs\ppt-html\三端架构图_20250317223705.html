<!DOCTYPE html>
<html lang="zh-CN">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>系统整体架构</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
            font-family: 'PingFang SC', 'Microsoft YaHei', sans-serif;
        }

        body {
            display: flex;
            justify-content: center;
            align-items: center;
            min-height: 100vh;
            background: white;
            padding: 20px;
        }

        .architecture-container {
            width: 1200px;
            padding: 40px;
            position: relative;
        }

        .title {
            font-size: 32px;
            color: #333;
            margin-bottom: 30px;
            font-weight: bold;
        }

        .subtitle {
            font-size: 18px;
            color: #666;
            margin-bottom: 50px;
            color: purple;
        }

        /* Logo样式 */
        .logo-left {
            position: absolute;
            top: 20px;
            left: 20px;
            width: 50px;
            height: 50px;
            background: #ddd;
            clip-path: polygon(50% 0%, 100% 50%, 50% 100%, 0% 50%);
        }

        .logo-right {
            position: absolute;
            top: 20px;
            right: 20px;
            width: 150px;
            height: 40px;
            display: flex;
            align-items: center;
            justify-content: flex-end;
        }

        .system-layout {
            display: flex;
            flex-direction: column;
            gap: 30px;
            position: relative;
        }

        /* 云端部分 */
        .cloud-section {
            border: 2px dashed #6c5ce7;
            padding: 20px;
            border-radius: 10px;
            position: relative;
            margin-bottom: 40px;
        }

        .cloud-icon {
            position: absolute;
            left: 80px;
            bottom: -30px;
            width: 40px;
            height: 40px;
            background: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"><path fill="%234834d4" d="M19 18H6c-3.3 0-6-2.7-6-6s2.7-6 6-6h.1c.5-2.3 2.5-4 4.9-4 2.8 0 5 2.2 5 5v.1c2.3.5 4 2.5 4 4.9 0 2.8-2.2 5-5 5z"/></svg>') no-repeat center;
        }

        .section-label {
            position: absolute;
            top: -12px;
            left: 20px;
            background: white;
            padding: 0 10px;
            font-weight: bold;
        }

        .cloud-label {
            color: #6c5ce7;
        }

        .cloud-components {
            display: flex;
            justify-content: space-around;
            align-items: center;
            gap: 20px;
            position: relative;
        }

        .cloud-components::after {
            content: '';
            position: absolute;
            left: 30%;
            right: 30%;
            bottom: -30px;
            height: 2px;
            background: #6c5ce7;
        }

        .component {
            background: #a8e6cf;
            padding: 15px 30px;
            border-radius: 8px;
            text-align: center;
            min-width: 150px;
        }

        .tsm-component {
            background: #bde0fe;
        }

        /* 三端部分容器 */
        .three-sections {
            display: flex;
            justify-content: space-between;
            gap: 30px;
            margin-top: 20px;
        }

        /* 移动端部分 */
        .mobile-section {
            flex: 1;
            border: 2px dashed #00b894;
            padding: 20px;
            border-radius: 10px;
            position: relative;
        }

        .mobile-icon {
            position: absolute;
            top: 10px;
            left: 10px;
            width: 30px;
            height: 30px;
            background: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"><path fill="%2300b894" d="M17 2H7c-1.1 0-2 .9-2 2v16c0 1.1.9 2 2 2h10c1.1 0 2-.9 2-2V4c0-1.1-.9-2-2-2zm-5 19c-.8 0-1.5-.7-1.5-1.5S11.2 18 12 18s1.5.7 1.5 1.5S12.8 21 12 21z"/></svg>') no-repeat center;
        }

        /* 车辆端部分 */
        .vehicle-section {
            flex: 2;
            border: 2px dashed #00b894;
            padding: 20px;
            border-radius: 10px;
            position: relative;
        }

        .vehicle-modules {
            display: flex;
            justify-content: space-around;
            gap: 20px;
        }

        .nfc-module,
        .ble-module {
            border: 2px dashed #00b894;
            padding: 15px;
            border-radius: 8px;
            flex: 1;
        }

        /* 产线前装部分 */
        .production-section {
            flex: 1;
            border: 2px dashed #e17055;
            padding: 20px;
            border-radius: 10px;
            position: relative;
        }

        /* 连接线 */
        .connection-line {
            position: absolute;
            background: #6c5ce7;
        }

        .vertical-line {
            width: 2px;
            height: 30px;
        }

        .horizontal-line {
            height: 2px;
        }

        /* NFC卡片样式 */
        .nfc-card {
            background: #bde0fe;
            padding: 10px;
            border-radius: 5px;
            margin: 10px 0;
            text-align: center;
        }

        /* 组件样式 */
        .module {
            background: #a8e6cf;
            padding: 10px;
            border-radius: 6px;
            margin: 5px 0;
            text-align: center;
        }
    </style>
</head>

<body>
    <div class="architecture-container">
        <!-- Logo -->
        <div class="logo-left"></div>
        <div class="logo-right">零感智能</div>

        <h1 class="title">系统整体架构</h1>
        <p class="subtitle">数字车钥匙系统主要包括三个实体，即移动智能终端（移动终端或手机）、云端服务器与车辆端。</p>

        <div class="system-layout">
            <!-- 云端部分 -->
            <div class="cloud-section">
                <span class="section-label cloud-label">云端</span>
                <div class="cloud-components">
                    <div class="component">DK Server</div>
                    <div class="component tsm-component">TSM</div>
                    <div class="component">OEM TSP</div>
                </div>
                <div class="cloud-icon"></div>
            </div>

            <!-- 三端部分 -->
            <div class="three-sections">
                <!-- 移动端 -->
                <div class="mobile-section">
                    <span class="section-label">移动端</span>
                    <div class="mobile-icon"></div>
                    <div class="component">NFC</div>
                    <div class="component">BLE</div>
                    <div class="module">钥匙业务模块</div>
                    <div class="module">钥匙标准基础模块SDK</div>
                </div>

                <!-- 车辆端 -->
                <div class="vehicle-section">
                    <span class="section-label">车辆端</span>
                    <div class="vehicle-modules">
                        <div class="nfc-module">
                            <h4>数字钥匙NFC模组</h4>
                            <div class="nfc-card">NFC卡片钥匙</div>
                            <div class="component">射频天线</div>
                            <div class="component">CLF</div>
                        </div>
                        <div class="ble-module">
                            <h4>数字钥匙BLE模组</h4>
                            <div class="component">MCU</div>
                            <div class="component">BLE</div>
                        </div>
                    </div>
                    <div class="component" style="margin-top: 20px;">eSE</div>
                </div>

                <!-- 产线前装 -->
                <div class="production-section">
                    <span class="section-label">产线前装</span>
                    <div class="component">产线安装、检测</div>
                </div>
            </div>
        </div>
    </div>
</body>

</html>
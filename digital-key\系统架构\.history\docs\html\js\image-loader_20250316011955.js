/**
 * 内容加载处理脚本
 * 用于处理带有data-html-src属性的图片区域，直接加载并显示HTML内容
 */
document.addEventListener("DOMContentLoaded", function() {
    // 选择所有带有data-html-src属性的图片
    var imageContainers = document.querySelectorAll("img[data-html-src]");
    
    // 处理每个图片区域
    imageContainers.forEach(function(img) {
        // 获取HTML内容源
        var htmlSrc = img.getAttribute("data-html-src");
        var figcaption = img.nextElementSibling;
        
        // 创建iframe来加载完整内容，保留原始样式
        var iframe = document.createElement('iframe');
        iframe.className = 'content-iframe';
        iframe.src = htmlSrc;
        iframe.style.width = '100%';
        iframe.style.height = '600px'; // 可调整高度
        iframe.style.border = 'none';
        iframe.style.display = 'block';
        iframe.style.overflow = 'hidden';
        iframe.style.marginTop = '20px';
        iframe.style.marginBottom = '20px';
        iframe.scrolling = 'no'; // 防止出现滚动条
        
        // 在加载完成后调整iframe高度以适应内容
        iframe.onload = function() {
            try {
                // 尝试调整iframe高度以适应内容
                var iframeContent = iframe.contentWindow.document.body;
                iframe.style.height = (iframeContent.scrollHeight + 30) + 'px';
                
                // 如果iframe内有样式表引用，确保它们能够加载
                var styleLinks = iframe.contentWindow.document.querySelectorAll('link[rel="stylesheet"]');
                styleLinks.forEach(function(link) {
                    // 确保样式表路径是绝对路径
                    if (!link.href.startsWith('http') && !link.href.startsWith('/')) {
                        var basePath = htmlSrc.substring(0, htmlSrc.lastIndexOf('/') + 1);
                        link.href = basePath + link.getAttribute('href');
                    }
                });
                
                // 使iframe内容可以与主页面交互
                iframe.contentWindow.document.body.style.margin = '0';
                iframe.contentWindow.document.body.style.padding = '0';
                
                // 移除iframe内容中可能存在的页面导航元素
                var navElements = iframe.contentWindow.document.querySelectorAll('nav, header, footer');
                navElements.forEach(function(nav) {
                    nav.style.display = 'none';
                });
            } catch (e) {
                console.error('调整iframe高度时出错:', e);
            }
        };
        
        // 替换图片元素为iframe
        var container = img.parentNode;
        container.replaceChild(iframe, img);
        
        // 保留figcaption
        if (figcaption && figcaption.tagName === 'FIGCAPTION') {
            container.insertBefore(figcaption, iframe.nextSibling);
        }
        
        // 添加错误处理
        iframe.onerror = function() {
            console.error('加载' + htmlSrc + '时出错');
            
            // 显示错误信息
            var errorMessage = document.createElement('div');
            errorMessage.style.padding = '20px';
            errorMessage.style.color = '#e74c3c';
            errorMessage.style.textAlign = 'center';
            errorMessage.style.border = '1px solid #e74c3c';
            errorMessage.style.borderRadius = '8px';
            errorMessage.style.margin = '20px 0';
            errorMessage.innerHTML = '<strong>内容加载失败</strong><p>无法加载 ' + htmlSrc + '</p>';
            
            container.replaceChild(errorMessage, iframe);
        };
    });
    
    // 淡入动画效果
    var fadeInElements = document.querySelectorAll(".fade-in");
    
    function checkVisibility() {
        fadeInElements.forEach(function(element) {
            var rect = element.getBoundingClientRect();
            var windowHeight = window.innerHeight;
            
            // 当元素进入视口时应用动画
            if (rect.top < windowHeight * 0.9) {
                element.classList.add("visible");
            }
        });
    }
    
    // 确保初始内容可见
    setTimeout(function() {
        // 将所有元素设置为可见
        fadeInElements.forEach(function(element) {
            element.classList.add("visible");
        });
    }, 100);
    
    // 添加可见时的类
    document.addEventListener("scroll", checkVisibility);
    window.addEventListener("resize", checkVisibility);
    
    // 立即检查初始可见性
    checkVisibility();
}); 
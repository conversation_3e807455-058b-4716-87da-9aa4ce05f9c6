package com.ruoyi.framework.tcp;

import com.alibaba.fastjson2.JSON;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import javax.websocket.*;
import javax.websocket.server.ServerEndpoint;
import java.io.*;
import java.net.Socket;
import java.util.concurrent.ConcurrentHashMap;

/**
 * WebSocket到TCP代理
 * 允许HTML页面通过WebSocket模拟TBOX设备与TCP服务器通信
 * 
 * <AUTHOR>
 */
@Component
@ServerEndpoint("/websocket/tbox-proxy")
public class WebSocketToTcpProxy {
    
    private static final Logger LOGGER = LoggerFactory.getLogger(WebSocketToTcpProxy.class);
    
    // 存储WebSocket会话到TCP连接的映射
    private static final ConcurrentHashMap<String, TcpConnection> connections = new ConcurrentHashMap<>();
    
    /**
     * WebSocket连接建立
     */
    @OnOpen
    public void onOpen(Session session) {
        LOGGER.info("📡 TBOX模拟器WebSocket连接建立: {}", session.getId());
        
        try {
            // 发送连接成功消息
            sendMessage(session, createMessage("info", "TBOX模拟器已准备就绪，点击'连接云平台'建立TCP连接"));
            
        } catch (Exception e) {
            LOGGER.error("❌ 处理WebSocket连接时发生错误: {}", e.getMessage(), e);
        }
    }
    
    /**
     * WebSocket连接关闭
     */
    @OnClose
    public void onClose(Session session) {
        LOGGER.info("📡 TBOX模拟器WebSocket连接关闭: {}", session.getId());
        
        // 关闭对应的TCP连接
        TcpConnection tcpConn = connections.remove(session.getId());
        if (tcpConn != null) {
            tcpConn.close();
        }
    }
    
    /**
     * WebSocket错误处理
     */
    @OnError
    public void onError(Session session, Throwable error) {
        LOGGER.error("📡 TBOX模拟器WebSocket发生错误: {}", error.getMessage(), error);
        
        // 关闭TCP连接
        TcpConnection tcpConn = connections.remove(session.getId());
        if (tcpConn != null) {
            tcpConn.close();
        }
    }
    
    /**
     * 接收WebSocket消息
     */
    @OnMessage
    public void onMessage(String message, Session session) {
        try {
            LOGGER.info("📡 收到TBOX模拟器指令: {}", message);
            
            // 解析消息
            TboxCommand command = JSON.parseObject(message, TboxCommand.class);
            
            switch (command.getAction()) {
                case "connect":
                    handleConnect(session, command);
                    break;
                case "heartbeat":
                    handleHeartbeat(session, command);
                    break;
                case "status":
                    handleStatusReport(session, command);
                    break;
                case "disconnect":
                    handleDisconnect(session, command);
                    break;
                default:
                    sendMessage(session, createMessage("error", "未知的指令: " + command.getAction()));
                    break;
            }
            
        } catch (Exception e) {
            LOGGER.error("❌ 处理TBOX模拟器消息时发生错误: {}", e.getMessage(), e);
            sendMessage(session, createMessage("error", "处理指令失败: " + e.getMessage()));
        }
    }
    
    /**
     * 处理连接指令
     */
    private void handleConnect(Session session, TboxCommand command) {
        try {
            String host = command.getHost() != null ? command.getHost() : "localhost";
            int port = command.getPort() != null ? command.getPort() : 9999;
            
            LOGGER.info("🔌 TBOX模拟器尝试连接TCP服务器: {}:{}", host, port);
            
            // 建立TCP连接
            Socket socket = new Socket(host, port);
            TcpConnection tcpConn = new TcpConnection(socket, session);
            connections.put(session.getId(), tcpConn);
            
            // 启动TCP消息监听线程
            new Thread(tcpConn::listenForMessages).start();
            
            sendMessage(session, createMessage("success", "TCP连接建立成功: " + host + ":" + port));
            LOGGER.info("✅ TBOX模拟器TCP连接建立成功");
            
        } catch (Exception e) {
            LOGGER.error("❌ TBOX模拟器TCP连接失败: {}", e.getMessage(), e);
            sendMessage(session, createMessage("error", "TCP连接失败: " + e.getMessage()));
        }
    }
    
    /**
     * 处理心跳指令
     */
    private void handleHeartbeat(Session session, TboxCommand command) {
        TcpConnection tcpConn = connections.get(session.getId());
        if (tcpConn == null) {
            sendMessage(session, createMessage("error", "请先建立TCP连接"));
            return;
        }
        
        // 发送心跳包（简单的心跳消息）
        String heartbeatMsg = "7E000A000A000102030405060708090A000030333032303030313030007E";
        tcpConn.sendMessage(heartbeatMsg);
        
        sendMessage(session, createMessage("info", "心跳包已发送: " + heartbeatMsg));
        LOGGER.info("💓 TBOX模拟器发送心跳包");
    }
    
    /**
     * 处理状态上报指令
     */
    private void handleStatusReport(Session session, TboxCommand command) {
        TcpConnection tcpConn = connections.get(session.getId());
        if (tcpConn == null) {
            sendMessage(session, createMessage("error", "请先建立TCP连接"));
            return;
        }
        
        // 发送状态报告（模拟解锁状态）
        String statusMsg = "7E000A000A000102030405060708090A000030333032303030313034007E";
        tcpConn.sendMessage(statusMsg);
        
        sendMessage(session, createMessage("info", "状态报告已发送: " + statusMsg + " (解锁状态)"));
        LOGGER.info("📊 TBOX模拟器发送状态报告");
    }
    
    /**
     * 处理断开连接指令
     */
    private void handleDisconnect(Session session, TboxCommand command) {
        TcpConnection tcpConn = connections.remove(session.getId());
        if (tcpConn != null) {
            tcpConn.close();
            sendMessage(session, createMessage("info", "TCP连接已断开"));
            LOGGER.info("🔌 TBOX模拟器TCP连接已断开");
        } else {
            sendMessage(session, createMessage("info", "没有活动的TCP连接"));
        }
    }
    
    /**
     * 发送WebSocket消息
     */
    private void sendMessage(Session session, String message) {
        try {
            if (session.isOpen()) {
                session.getBasicRemote().sendText(message);
            }
        } catch (Exception e) {
            LOGGER.error("❌ 发送WebSocket消息失败: {}", e.getMessage(), e);
        }
    }
    
    /**
     * 创建消息
     */
    private String createMessage(String type, String content) {
        return JSON.toJSONString(new TboxMessage(type, content, System.currentTimeMillis()));
    }
    
    /**
     * TCP连接管理类
     */
    private static class TcpConnection {
        private final Socket socket;
        private final Session webSocketSession;
        private final PrintWriter out;
        private final BufferedReader in;
        
        public TcpConnection(Socket socket, Session webSocketSession) throws IOException {
            this.socket = socket;
            this.webSocketSession = webSocketSession;
            this.out = new PrintWriter(socket.getOutputStream(), true);
            this.in = new BufferedReader(new InputStreamReader(socket.getInputStream()));
        }
        
        public void sendMessage(String message) {
            out.println(message);
            LOGGER.info("📤 TBOX模拟器发送TCP消息: {}", message);
        }
        
        public void listenForMessages() {
            try {
                String message;
                while ((message = in.readLine()) != null && webSocketSession.isOpen()) {
                    LOGGER.info("📥 TBOX模拟器收到TCP消息: {}", message);
                    
                    // 转发到WebSocket
                    String wsMessage = JSON.toJSONString(new TboxMessage("tcp_message", 
                        "收到云平台指令: " + message, System.currentTimeMillis()));
                    webSocketSession.getBasicRemote().sendText(wsMessage);
                }
            } catch (Exception e) {
                LOGGER.error("❌ TBOX模拟器TCP消息监听异常: {}", e.getMessage(), e);
            }
        }
        
        public void close() {
            try {
                if (socket != null && !socket.isClosed()) {
                    socket.close();
                }
            } catch (IOException e) {
                LOGGER.error("❌ 关闭TCP连接时发生错误: {}", e.getMessage(), e);
            }
        }
    }
    
    /**
     * TBOX指令类
     */
    public static class TboxCommand {
        private String action;
        private String host;
        private Integer port;
        
        // getters and setters
        public String getAction() { return action; }
        public void setAction(String action) { this.action = action; }
        public String getHost() { return host; }
        public void setHost(String host) { this.host = host; }
        public Integer getPort() { return port; }
        public void setPort(Integer port) { this.port = port; }
    }
    
    /**
     * TBOX消息类
     */
    public static class TboxMessage {
        private String type;
        private String content;
        private long timestamp;
        
        public TboxMessage(String type, String content, long timestamp) {
            this.type = type;
            this.content = content;
            this.timestamp = timestamp;
        }
        
        // getters and setters
        public String getType() { return type; }
        public void setType(String type) { this.type = type; }
        public String getContent() { return content; }
        public void setContent(String content) { this.content = content; }
        public long getTimestamp() { return timestamp; }
        public void setTimestamp(long timestamp) { this.timestamp = timestamp; }
    }
}

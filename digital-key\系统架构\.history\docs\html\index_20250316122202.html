<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>数字钥匙技术架构</title>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/@fortawesome/fontawesome-free@6.4.0/css/all.min.css">
    <style>
        :root {
            --primary-color: #3498db;
            --secondary-color: #2c3e50;
            --accent-color: #e74c3c;
            --light-bg: #f8f9fa;
            --dark-bg: #2c3e50;
            --text-light: #f8f9fa;
            --text-dark: #333;
            --border-radius: 8px;
            --box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            --transition: all 0.3s ease;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            color: var(--text-dark);
            background-color: var(--light-bg);
            overflow-x: hidden;
        }
        
        .header {
            background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
            color: var(--text-light);
            padding: 2rem 0;
            margin-bottom: 2rem;
            box-shadow: var(--box-shadow);
        }
        
        .section {
            padding: 2rem;
            margin-bottom: 2rem;
            background-color: #fff;
            border-radius: var(--border-radius);
            box-shadow: var(--box-shadow);
            transition: var(--transition);
        }
        
        .section:hover {
            transform: translateY(-5px);
            box-shadow: 0 8px 15px rgba(0, 0, 0, 0.1);
        }
        
        .section-title {
            color: var(--primary-color);
            margin-bottom: 1.5rem;
            padding-bottom: 0.5rem;
            border-bottom: 2px solid var(--primary-color);
        }
        
        .component {
            padding: 1.5rem;
            margin-bottom: 1.5rem;
            background-color: var(--light-bg);
            border-radius: var(--border-radius);
            border-left: 4px solid var(--primary-color);
            transition: var(--transition);
        }
        
        .component:hover {
            background-color: #f0f8ff;
            border-left-color: var(--accent-color);
        }
        
        .component-title {
            font-weight: 600;
            color: var(--secondary-color);
            margin-bottom: 0.5rem;
        }
        
        .process-step {
            display: flex;
            margin-bottom: 1.5rem;
            padding: 1rem;
            background-color: var(--light-bg);
            border-radius: var(--border-radius);
            position: relative;
        }
        
        .step-number {
            display: flex;
            align-items: center;
            justify-content: center;
            width: 36px;
            height: 36px;
            background-color: var(--primary-color);
            color: white;
            border-radius: 50%;
            margin-right: 1rem;
            font-weight: bold;
        }
        
        .step-content {
            flex: 1;
        }
        
        .step-title {
            font-weight: 600;
            margin-bottom: 0.5rem;
            color: var(--secondary-color);
        }
        
        .process-connection {
            position: absolute;
            top: 100%;
            left: 18px;
            width: 2px;
            height: 20px;
            background-color: var(--primary-color);
            z-index: 1;
        }
        
        .process-step:last-child .process-connection {
            display: none;
        }
        
        .note {
            background-color: #fff3cd;
            border-left: 4px solid #ffc107;
            padding: 1rem;
            margin: 1rem 0;
            border-radius: var(--border-radius);
        }
        
        .security-feature {
            display: flex;
            align-items: center;
            margin-bottom: 1rem;
            padding: 0.75rem;
            border-radius: var(--border-radius);
            background-color: rgba(52, 152, 219, 0.1);
        }
        
        .security-icon {
            margin-right: 1rem;
            color: var(--primary-color);
            font-size: 1.5rem;
        }
        
        .footer {
            background-color: var(--dark-bg);
            color: var(--text-light);
            padding: 2rem 0;
            margin-top: 2rem;
        }
        
        @media (max-width: 768px) {
            .section {
                padding: 1.5rem;
            }
        }

        .diagram-container {
            text-align: center;
            margin: 2rem 0;
        }

        .diagram-img {
            max-width: 100%;
            height: auto;
            border-radius: var(--border-radius);
            box-shadow: var(--box-shadow);
        }

        .tab-content {
            padding: 1.5rem;
            border: 1px solid #dee2e6;
            border-top: none;
            border-radius: 0 0 var(--border-radius) var(--border-radius);
        }

        .nav-tabs .nav-link.active {
            background-color: var(--primary-color);
            color: white;
            border: none;
        }

        .nav-tabs .nav-link {
            border-radius: var(--border-radius) var(--border-radius) 0 0;
            color: var(--secondary-color);
            padding: 0.5rem 1rem;
        }
    </style>
</head>
<body>
    <header class="header">
        <div class="container text-center">
            <h1 class="display-4"><i class="fas fa-key me-3"></i>数字钥匙技术架构</h1>
            <p class="lead">安全、高效、智能的车辆数字钥匙解决方案</p>
        </div>
    </header>

    <div class="container">
        <section class="section">
            <h2 class="section-title"><i class="fas fa-project-diagram me-2"></i>整体架构概述</h2>
            <p class="lead">数字钥匙系统由手机端、车端和钥匙云平台三大核心组件构成，通过安全通信实现无感控车、远程授权等功能。</p>
            
            <div class="diagram-container">
                <img src="../images/architecture.png" alt="数字钥匙整体架构图" class="diagram-img">
                <p class="text-muted mt-2">图1: 数字钥匙系统整体架构</p>
            </div>
            
            <div class="row mt-4">
                <div class="col-md-4 mb-3">
                    <div class="component">
                        <h3 class="component-title"><i class="fas fa-mobile-alt me-2"></i>手机端</h3>
                        <p>负责用户交互、钥匙管理和车辆控制，通过蓝牙与车辆通信，实现无感解锁、远程控制等功能。</p>
                    </div>
                </div>
                <div class="col-md-4 mb-3">
                    <div class="component">
                        <h3 class="component-title"><i class="fas fa-car me-2"></i>车端</h3>
                        <p>负责验证钥匙有效性、执行控车指令，通过蓝牙与手机通信，实现安全认证和精确的距离计算。</p>
                    </div>
                </div>
                <div class="col-md-4 mb-3">
                    <div class="component">
                        <h3 class="component-title"><i class="fas fa-cloud me-2"></i>钥匙云平台</h3>
                        <p>管理钥匙生命周期、实现远程授权与撤销，为整个系统提供密钥管理和认证服务。</p>
                    </div>
                </div>
            </div>
        </section>

        <section class="section">
            <h2 class="section-title"><i class="fas fa-sitemap me-2"></i>详细模块组成</h2>
            
            <ul class="nav nav-tabs mb-3" id="moduleTabs" role="tablist">
                <li class="nav-item" role="presentation">
                    <button class="nav-link active" id="mobile-tab" data-bs-toggle="tab" data-bs-target="#mobile" type="button" role="tab" aria-controls="mobile" aria-selected="true">手机端</button>
                </li>
                <li class="nav-item" role="presentation">
                    <button class="nav-link" id="car-tab" data-bs-toggle="tab" data-bs-target="#car" type="button" role="tab" aria-controls="car" aria-selected="false">车端</button>
                </li>
                <li class="nav-item" role="presentation">
                    <button class="nav-link" id="cloud-tab" data-bs-toggle="tab" data-bs-target="#cloud" type="button" role="tab" aria-controls="cloud" aria-selected="false">云平台</button>
                </li>
            </ul>
            
            <div class="tab-content" id="moduleTabContent">
                <div class="tab-pane fade show active" id="mobile" role="tabpanel" aria-labelledby="mobile-tab">
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <div class="component">
                                <h4 class="component-title">钥匙管理模块</h4>
                                <p>管理用户数字钥匙，包括添加、删除、授权和查看钥匙状态等功能。</p>
                            </div>
                        </div>
                        <div class="col-md-6 mb-3">
                            <div class="component">
                                <h4 class="component-title">车辆控制模块</h4>
                                <p>执行车辆控制指令，如解锁、上锁、启动等操作，并处理操作反馈。</p>
                            </div>
                        </div>
                        <div class="col-md-6 mb-3">
                            <div class="component">
                                <h4 class="component-title">蓝牙通信模块</h4>
                                <p>负责与车辆建立和维护蓝牙连接，处理通信数据的加密和解密。</p>
                            </div>
                        </div>
                        <div class="col-md-6 mb-3">
                            <div class="component">
                                <h4 class="component-title">安全存储模块</h4>
                                <p>将敏感数据存储在手机安全区域，防止密钥和授权信息泄露。</p>
                            </div>
                        </div>
                        <div class="col-md-6 mb-3">
                            <div class="component">
                                <h4 class="component-title">时间同步模块</h4>
                                <p>确保手机系统时间准确，为安全验证提供可信时间戳，防止重放攻击。</p>
                            </div>
                        </div>
                        <div class="col-md-6 mb-3">
                            <div class="component">
                                <h4 class="component-title">智能场景管理模块</h4>
                                <p>检测用户状态与环境，优化无感连接触发条件，平衡便捷性与电量消耗。</p>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="tab-pane fade" id="car" role="tabpanel" aria-labelledby="car-tab">
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <div class="component">
                                <h4 class="component-title">蓝牙通信模块</h4>
                                <p>接收和发送蓝牙信号，与手机建立安全通信通道，处理连接请求。</p>
                            </div>
                        </div>
                        <div class="col-md-6 mb-3">
                            <div class="component">
                                <h4 class="component-title">钥匙验证模块</h4>
                                <p>验证数字钥匙的有效性和权限，确保只有授权用户能控制车辆。</p>
                            </div>
                        </div>
                        <div class="col-md-6 mb-3">
                            <div class="component">
                                <h4 class="component-title">指令执行模块</h4>
                                <p>接收验证后的控车指令，并将其转换为车辆操作，如解锁、上锁等。</p>
                            </div>
                        </div>
                        <div class="col-md-6 mb-3">
                            <div class="component">
                                <h4 class="component-title">用户行为分析模块</h4>
                                <p>核心无感控车算法，通过RSSI信号强度分析计算用户距离，支持多节点定位，提供安全策略。</p>
                            </div>
                        </div>
                        <div class="col-md-6 mb-3">
                            <div class="component">
                                <h4 class="component-title">暗号交换模块</h4>
                                <p>与手机端协商生成临时暗号，确保通信安全，支持会话密钥生命周期管理。</p>
                            </div>
                        </div>
                        <div class="col-md-6 mb-3">
                            <div class="component">
                                <h4 class="component-title">异常处理模块</h4>
                                <p>处理异常连接请求，监控异常操作，执行安全措施，支持断连场景自动安全策略。</p>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="tab-pane fade" id="cloud" role="tabpanel" aria-labelledby="cloud-tab">
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <div class="component">
                                <h4 class="component-title">钥匙生命周期管理</h4>
                                <p>负责钥匙的创建、授权和撤销，管理配对状态，支持临时钥匙授权和委托。</p>
                            </div>
                        </div>
                        <div class="col-md-6 mb-3">
                            <div class="component">
                                <h4 class="component-title">车辆关联服务</h4>
                                <p>验证VIN码有效性，管理车辆信息，处理车辆绑定请求，确认支持功能。</p>
                            </div>
                        </div>
                        <div class="col-md-6 mb-3">
                            <div class="component">
                                <h4 class="component-title">安全认证中心</h4>
                                <p>负责身份验证，确保只有授权用户能使用钥匙，处理安全事件。</p>
                            </div>
                        </div>
                        <div class="col-md-6 mb-3">
                            <div class="component">
                                <h4 class="component-title">密钥管理系统</h4>
                                <p>生成和管理各种密钥，确保密钥安全存储，管理根密钥下发流程，执行密钥更新。</p>
                            </div>
                        </div>
                        <div class="col-md-6 mb-3">
                            <div class="component">
                                <h4 class="component-title">统一接口服务</h4>
                                <p>提供标准接口，建立安全通信通道，防止消息被篡改或重放。</p>
                            </div>
                        </div>
                        <div class="col-md-6 mb-3">
                            <div class="component">
                                <h4 class="component-title">异常监控与处理</h4>
                                <p>监控异常登录，分析异常使用模式，实施安全风控，触发安全预警。</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <section class="section">
            <h2 class="section-title"><i class="fas fa-shield-alt me-2"></i>核心安全机制</h2>
            
            <div class="row">
                <div class="col-lg-6">
                    <div class="security-feature">
                        <div class="security-icon"><i class="fas fa-fingerprint"></i></div>
                        <div>
                            <h4>双向身份认证</h4>
                            <p>确保手机和车辆互相验证身份，防止伪装攻击。</p>
                        </div>
                    </div>
                </div>
                
                <div class="col-lg-6">
                    <div class="security-feature">
                        <div class="security-icon"><i class="fas fa-key"></i></div>
                        <div>
                            <h4>会话密钥机制</h4>
                            <p>使用临时会话密钥加密通信，定期轮换更新，提高安全性。</p>
                        </div>
                    </div>
                </div>
                
                <div class="col-lg-6">
                    <div class="security-feature">
                        <div class="security-icon"><i class="fas fa-clock"></i></div>
                        <div>
                            <h4>防重放保护</h4>
                            <p>每条消息都包含时间戳和序列号，防止重放攻击。</p>
                        </div>
                    </div>
                </div>
                
                <div class="col-lg-6">
                    <div class="security-feature">
                        <div class="security-icon"><i class="fas fa-ruler"></i></div>
                        <div>
                            <h4>距离测量算法</h4>
                            <p>通过RSSI信号强度分析计算用户距离，防止中继攻击。</p>
                        </div>
                    </div>
                </div>
                
                <div class="col-lg-6">
                    <div class="security-feature">
                        <div class="security-icon"><i class="fas fa-microchip"></i></div>
                        <div>
                            <h4>安全存储</h4>
                            <p>敏感数据存储在专用安全区域(SE/HSM)，防止被窃取或修改。</p>
                        </div>
                    </div>
                </div>
                
                <div class="col-lg-6">
                    <div class="security-feature">
                        <div class="security-icon"><i class="fas fa-random"></i></div>
                        <div>
                            <h4>随机挑战机制</h4>
                            <p>每次操作都使用一次性随机数，确保指令的唯一性和有效性。</p>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <section class="section">
            <h2 class="section-title"><i class="fas fa-exchange-alt me-2"></i>核心流程图解</h2>
            
            <ul class="nav nav-tabs mb-3" id="processTabs" role="tablist">
                <li class="nav-item" role="presentation">
                    <button class="nav-link active" id="firstpair-tab" data-bs-toggle="tab" data-bs-target="#firstpair" type="button" role="tab" aria-controls="firstpair" aria-selected="true">首次配对流程</button>
                </li>
                <li class="nav-item" role="presentation">
                    <button class="nav-link" id="sense-tab" data-bs-toggle="tab" data-bs-target="#sense" type="button" role="tab" aria-controls="sense" aria-selected="false">无感控车流程</button>
                </li>
            </ul>
            
            <div class="tab-content" id="processTabContent">
                <div class="tab-pane fade show active" id="firstpair" role="tabpanel" aria-labelledby="firstpair-tab">
                    <div class="diagram-container">
                        <img src="../images/firstpair.png" alt="首次配对流程图" class="diagram-img">
                        <p class="text-muted mt-2">图2: 首次配对流程示意图</p>
                    </div>
                    
                    <div class="mt-4">
                        <div class="process-step">
                            <div class="step-number">1</div>
                            <div class="step-content">
                                <h4 class="step-title">身份验证与车辆识别</h4>
                                <p>用户通过扫描车辆二维码或输入VIN码识别车辆，向云平台发送请求。</p>
                            </div>
                            <div class="process-connection"></div>
                        </div>
                        
                        <div class="process-step">
                            <div class="step-number">2</div>
                            <div class="step-content">
                                <h4 class="step-title">虚拟密钥和配对令牌生成</h4>
                                <p>云平台根据VIN码生成虚拟密钥和唯一配对令牌，并下发到手机。</p>
                            </div>
                            <div class="process-connection"></div>
                        </div>
                        
                        <div class="process-step">
                            <div class="step-number">3</div>
                            <div class="step-content">
                                <h4 class="step-title">蓝牙配对连接</h4>
                                <p>手机识别车辆蓝牙广播信号，建立蓝牙连接并发送配对请求。</p>
                            </div>
                            <div class="process-connection"></div>
                        </div>
                        
                        <div class="process-step">
                            <div class="step-number">4</div>
                            <div class="step-content">
                                <h4 class="step-title">安全通道建立</h4>
                                <p>双方交换暗号材料，各自计算出共同的会话密钥，建立加密安全通道。</p>
                            </div>
                            <div class="process-connection"></div>
                        </div>
                        
                        <div class="process-step">
                            <div class="step-number">5</div>
                            <div class="step-content">
                                <h4 class="step-title">配对关系确认</h4>
                                <p>手机和车辆分别向云平台上报配对成功，云平台记录钥匙绑定关系。</p>
                            </div>
                        </div>
                    </div>
                    
                    <div class="note mt-4">
                        <h5><i class="fas fa-info-circle me-2"></i>配对安全保障</h5>
                        <p>配对过程采用多重安全措施：临时暗号定期更换、密钥安全存储、双向身份认证、距离测量防中继攻击，确保整个配对过程不被攻击者破解或窃取。</p>
                    </div>
                </div>
                
                <div class="tab-pane fade" id="sense" role="tabpanel" aria-labelledby="sense-tab">
                    <div class="diagram-container">
                        <img src="../images/sensing.png" alt="无感控车流程图" class="diagram-img">
                        <p class="text-muted mt-2">图3: 无感控车流程示意图</p>
                    </div>
                    
                    <div class="mt-4">
                        <div class="process-step">
                            <div class="step-number">1</div>
                            <div class="step-content">
                                <h4 class="step-title">后台唤醒与条件检查</h4>
                                <p>系统唤醒后台APP，检查是否满足连接条件（如用户启用无感连接、电量充足等）。</p>
                            </div>
                            <div class="process-connection"></div>
                        </div>
                        
                        <div class="process-step">
                            <div class="step-number">2</div>
                            <div class="step-content">
                                <h4 class="step-title">建立蓝牙连接</h4>
                                <p>手机读取已存储的车辆连接信息，直接发起蓝牙连接请求，车端验证后接受连接。</p>
                            </div>
                            <div class="process-connection"></div>
                        </div>
                        
                        <div class="process-step">
                            <div class="step-number">3</div>
                            <div class="step-content">
                                <h4 class="step-title">安全认证与通道建立</h4>
                                <p>双方进行身份验证，建立安全通信通道，确保后续通信安全。</p>
                            </div>
                            <div class="process-connection"></div>
                        </div>
                        
                        <div class="process-step">
                            <div class="step-number">4</div>
                            <div class="step-content">
                                <h4 class="step-title">距离计算与状态同步</h4>
                                <p>车端计算与手机的距离，向手机发送当前状态信息。</p>
                            </div>
                            <div class="process-connection"></div>
                        </div>
                        
                        <div class="process-step">
                            <div class="step-number">5</div>
                            <div class="step-content">
                                <h4 class="step-title">自动控车执行</h4>
                                <p>当距离在安全范围内，手机根据用户预设策略发送控车指令，车端验证后执行操作。</p>
                            </div>
                        </div>
                    </div>
                    
                    <div class="note mt-4">
                        <h5><i class="fas fa-exclamation-triangle me-2"></i>安全距离控制</h5>
                        <p>系统会根据用户距离车辆的远近，自动调整允许的控车操作权限。当用户离车较远时，系统会自动执行安全措施，如车门上锁等，确保车辆安全。</p>
                    </div>
                </div>
            </div>
        </section>

        <section class="section">
            <h2 class="section-title"><i class="fas fa-cogs me-2"></i>技术特点与创新</h2>
            
            <div class="row">
                <div class="col-md-6 mb-4">
                    <div class="component">
                        <h4 class="component-title"><i class="fas fa-bolt me-2"></i>高效无感体验</h4>
                        <p>基于智能场景识别和用户行为分析，实现用户靠近车辆时自动解锁等无感操作，提供极致便捷体验。</p>
                    </div>
                </div>
                
                <div class="col-md-6 mb-4">
                    <div class="component">
                        <h4 class="component-title"><i class="fas fa-shield-alt me-2"></i>多层次安全设计</h4>
                        <p>采用多层次安全机制，包括硬件安全模块、会话密钥定期轮换、防重放攻击、距离测量等，确保系统安全可靠。</p>
                    </div>
                </div>
                
                <div class="col-md-6 mb-4">
                    <div class="component">
                        <h4 class="component-title"><i class="fas fa-share-alt me-2"></i>灵活钥匙共享</h4>
                        <p>支持钥匙授权共享，可设定权限范围和有效期，适用于多样化场景，如家庭成员共享、临时借车等。</p>
                    </div>
                </div>
                
                <div class="col-md-6 mb-4">
                    <div class="component">
                        <h4 class="component-title"><i class="fas fa-tachometer-alt me-2"></i>低功耗设计</h4>
                        <p>采用智能场景管理和电量优化策略，平衡便捷性与电量消耗，延长手机使用时间。</p>
                    </div>
                </div>
            </div>
        </section>
    </div>

    <footer class="footer">
        <div class="container text-center">
            <p>© 2023 数字钥匙技术团队 - 专业安全、高效、智能的车辆数字钥匙解决方案</p>
        </div>
    </footer>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // 初始化Bootstrap标签页
            var tabEls = document.querySelectorAll('button[data-bs-toggle="tab"]');
            tabEls.forEach(function(tabEl) {
                tabEl.addEventListener('shown.bs.tab', function (event) {
                    // 切换标签页时的动画效果
                    const target = document.querySelector(event.target.dataset.bsTarget);
                    target.querySelectorAll('.component').forEach((el, i) => {
                        el.style.opacity = 0;
                        el.style.transform = 'translateY(20px)';
                        setTimeout(() => {
                            el.style.transition = 'opacity 0.3s ease, transform 0.3s ease';
                            el.style.opacity = 1;
                            el.style.transform = 'translateY(0)';
                        }, i * 100);
                    });
                });
            });
            
            // 初始页面加载时的动画
            document.querySelectorAll('#mobile .component').forEach((el, i) => {
                el.style.opacity = 0;
                el.style.transform = 'translateY(20px)';
                setTimeout(() => {
                    el.style.transition = 'opacity 0.3s ease, transform 0.3s ease';
                    el.style.opacity = 1;
                    el.style.transform = 'translateY(0)';
                }, 300 + i * 100);
            });
        });
    </script>
</body>
</html>

/**
 * 本地储存键 types
 * **/

const TOKEN = "m_token"; // token
/**
 * 用户信息
 * nickname 昵称；     avatar  头像；
 *
 */
const USERINFO = "m_user_info"; // userinfo
const CODE = "m_code"; // code
const OPENID = "m_openid"; // openid
const RECEIVE = "m_receive"; // 收到的消息
const EQUIPID = "m_equipid"; // 设备id
const REGION = "m_region"; // 用户的地区 [省，市，区]
const HEARTCODE = "heart_code"; // 心跳数据

export { TOKEN, USERINFO, CODE, REGION, OPENID, EQUIPID, HEARTCODE };

## 钥匙云平台核心架构

- **钥匙生命周期管理**：创建、授权、撤销数字钥匙，管理钥匙权限与有效期
  
- **车辆关联服务**：验证车辆信息，建立车辆与钥匙的安全绑定关系
  
- **安全认证中心**：确保用户身份可信，执行权限验证，防止未授权访问
  
- **密钥管理系统**：生成加密密钥，确保密钥安全存储与分发，支持密钥更新
  
- **统一接口服务**：提供标准化API，连接外部系统，确保接口安全
  
- **安全通信通道**：实现端到端加密通信，防止数据被窃听或篡改
  
- **异常监控与处理**：检测异常行为，执行安全响应，记录安全事件
  
- **时间服务器**：提供精确时间基准，支持NTP/PTP时间同步，防止重放攻击

## 手机端核心功能

- **钥匙管理模块**：查看、使用和管理个人数字钥匙，包括钥匙状态监控和权限查看
  
- **车辆控制模块**：发送控制指令，接收车辆状态反馈
  
- **安全存储模块**：在手机安全区域存储密钥、配对令牌和车辆连接信息
  
- **蓝牙通信模块**：扫描识别车辆广播，建立和维护与车辆的蓝牙连接
  
- **时间同步模块**：与云端时间服务器同步，提供可信时间戳，防止重放攻击
  
- **暗号交换模块**：与车端协商生成临时暗号，确保通信安全
  
- **智能场景管理模块**：根据位置、时间和活动状态调整扫描频率，优化电量使用
  
- **异常处理模块**：检测并处理连接异常、认证失败等问题，确保使用安全
  
- **标定模块**：用于校准RSSI信号与实际距离的对应关系，提高无感控车精度

## 车端核心功能

- **蓝牙通信模块**：发送加密广播信号，接收并处理连接请求
  
- **钥匙验证模块**：验证手机身份，确保只有授权设备能够连接
  
- **指令执行模块**：接收并执行来自手机的控制指令，反馈执行结果
  
- **安全存储模块**：在安全区域存储根密钥、会话密钥和授权设备信息
  
- **远程通信模块**：与云平台建立安全连接，同步状态和配置
  
- **时间同步模块**：与云端时间服务器同步，提供精确时间基准
  
- **暗号交换模块**：与手机端协商生成临时暗号，确保只有配对的双方能通信
  
- **用户行为分析模块**：基于RSSI信号强度计算与手机的距离，支持距离感知操作
  
- **异常处理模块**：处理异常连接请求，监控异常操作，执行安全措施
  
- **标定配置模块**：存储和管理RSSI信号标定数据，支持不同环境下的精确距离计算

## 钥匙分享功能

### 用户操作流程
1. 车主打开APP，点击"钥匙分享"
2. 设置权限和有效期
3. 生成分享二维码
4. 接收方打开APP，扫描二维码
5. 车主确认分享请求
6. 接收方获得数字钥匙
7. 接收方使用钥匙控制车辆
8. 车主可随时查看或撤销分享

### 安全设计要点
- 分享前需身份二次验证
- 二维码加密且定时刷新
- 严格的权限隔离机制
- 完整的使用记录追踪
- 一键紧急撤销功能
- 离线状态下权限仍有效

## 钥匙配对功能

### 用户操作流程
1. 用户点击"添加新车钥匙"
2. 扫描车辆二维码或输入VIN码
3. 完成身份验证
4. 系统获取云端授权
5. 自动连接车辆蓝牙
6. 确认配对码一致
7. 配对成功，命名钥匙
8. 测试基本功能确认

### 安全设计要点
- 多因素认证保障
- 唯一配对令牌防复制
- 安全区域存储配对信息
- 配对过程暗号交换
- 异常配对行为监控
- 配对超时自动取消

## 无感控车功能

### 用户操作流程
1. 在设置中开启"无感控车"
2. 选择启用场景
3. 设置自动执行动作
4. 调整触发距离和灵敏度
5. 完成信号标定校准
6. 确认启用功能
7. 接近车辆自动执行预设动作
8. 收到操作执行通知

### 安全设计要点
- 多维度情境感知减少误触发
- 风险级别对应不同触发距离
- 信号模式分析防欺骗
- 加密心跳包确保连接安全
- 异常情况自动保护机制
- 电量自适应调整功能
- 距离变化触发安全措施
- 紧急禁用一键关闭选项

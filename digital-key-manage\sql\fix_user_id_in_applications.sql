-- 修复申请记录中的user_id字段
-- 执行时间：2025-01-19

-- 1. 查看当前申请记录的user_id字段状态
SELECT 
    application_id,
    application_no,
    user_id,
    real_name,
    phone,
    create_time
FROM dk_user_application 
ORDER BY create_time DESC;

-- 2. 根据手机号匹配用户，更新申请记录的user_id字段
UPDATE dk_user_application dua
INNER JOIN sys_user su ON dua.phone = su.phonenumber
SET dua.user_id = su.user_id
WHERE dua.user_id IS NULL;

-- 3. 验证修复结果
SELECT 
    dua.application_id,
    dua.application_no,
    dua.user_id,
    dua.real_name,
    dua.phone,
    su.user_name,
    su.nick_name
FROM dk_user_application dua
LEFT JOIN sys_user su ON dua.user_id = su.user_id
ORDER BY dua.create_time DESC;

-- 4. 检查是否还有未匹配的记录
SELECT 
    application_id,
    application_no,
    user_id,
    real_name,
    phone,
    create_time
FROM dk_user_application 
WHERE user_id IS NULL
ORDER BY create_time DESC;

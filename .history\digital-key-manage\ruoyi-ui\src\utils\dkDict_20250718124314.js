import request from '@/utils/request'

/**
 * 数字钥匙系统字典工具类
 * 管理系统中的各种状态字典数据
 */

// 字典数据缓存
const dictCache = {}

/**
 * 获取所有字典数据
 */
export function getAllDictData() {
  return request({
    url: '/dk/dict/all',
    method: 'get'
  })
}

/**
 * 获取用户权限状态字典
 */
export function getUserStatusDict() {
  return request({
    url: '/dk/dict/userStatus',
    method: 'get'
  })
}

/**
 * 获取车辆状态字典
 */
export function getVehicleStatusDict() {
  return request({
    url: '/dk/dict/vehicleStatus',
    method: 'get'
  })
}

/**
 * 获取申请状态字典
 */
export function getApplicationStatusDict() {
  return request({
    url: '/dk/dict/applicationStatus',
    method: 'get'
  })
}

/**
 * 获取分配状态字典
 */
export function getAssignmentStatusDict() {
  return request({
    url: '/dk/dict/assignmentStatus',
    method: 'get'
  })
}

/**
 * 根据字典类型获取字典数据
 */
export function getDictByType(dictType) {
  return request({
    url: `/dk/dict/type/${dictType}`,
    method: 'get'
  })
}

/**
 * 获取字典标签
 */
export function getDictLabel(dictType, dictValue) {
  return request({
    url: `/dk/dict/label/${dictType}/${dictValue}`,
    method: 'get'
  })
}

/**
 * 字典数据常量定义
 */
export const DK_DICT_TYPE = {
  USER_STATUS: 'dk_user_status',
  VEHICLE_STATUS: 'dk_vehicle_status',
  APPLICATION_STATUS: 'dk_application_status',
  ASSIGNMENT_STATUS: 'dk_assignment_status'
}

/**
 * 用户权限状态
 */
export const USER_STATUS = {
  NORMAL: { value: '0', label: '正常', cssClass: 'success' },
  RESTRICTED: { value: '1', label: '限制', cssClass: 'danger' }
}

/**
 * 车辆状态
 */
export const VEHICLE_STATUS = {
  AVAILABLE: { value: '0', label: '空闲', cssClass: 'success' },
  IN_USE: { value: '1', label: '使用中', cssClass: 'primary' },
  MAINTENANCE: { value: '2', label: '维护中', cssClass: 'warning' },
  DISABLED: { value: '3', label: '停用', cssClass: 'danger' }
}

/**
 * 申请状态
 */
export const APPLICATION_STATUS = {
  PENDING: { value: '0', label: '待审核', cssClass: 'info' },
  APPROVED: { value: '1', label: '审核通过', cssClass: 'success' },
  REJECTED: { value: '2', label: '审核拒绝', cssClass: 'danger' },
  SUPPLEMENT: { value: '3', label: '补充资料', cssClass: 'warning' }
}

/**
 * 分配状态
 */
export const ASSIGNMENT_STATUS = {
  IN_USE: { value: '1', label: '使用中', cssClass: 'primary' },
  RETURNED: { value: '2', label: '已归还', cssClass: 'success' },
  OVERDUE: { value: '3', label: '逾期', cssClass: 'warning' },
  ABNORMAL: { value: '4', label: '异常', cssClass: 'danger' }
}

/**
 * 根据字典类型和值获取标签
 */
export function getDictLabelByValue(dictType, value) {
  const dictMap = {
    [DK_DICT_TYPE.USER_STATUS]: USER_STATUS,
    [DK_DICT_TYPE.VEHICLE_STATUS]: VEHICLE_STATUS,
    [DK_DICT_TYPE.APPLICATION_STATUS]: APPLICATION_STATUS,
    [DK_DICT_TYPE.ASSIGNMENT_STATUS]: ASSIGNMENT_STATUS
  }

  const dict = dictMap[dictType]
  if (!dict) return value

  for (const key in dict) {
    if (dict[key].value === value) {
      return dict[key].label
    }
  }
  return value
}

/**
 * 根据字典类型和值获取CSS样式
 */
export function getDictCssClassByValue(dictType, value) {
  const dictMap = {
    [DK_DICT_TYPE.USER_STATUS]: USER_STATUS,
    [DK_DICT_TYPE.VEHICLE_STATUS]: VEHICLE_STATUS,
    [DK_DICT_TYPE.APPLICATION_STATUS]: APPLICATION_STATUS,
    [DK_DICT_TYPE.ASSIGNMENT_STATUS]: ASSIGNMENT_STATUS
  }

  const dict = dictMap[dictType]
  if (!dict) return 'default'

  for (const key in dict) {
    if (dict[key].value === value) {
      return dict[key].cssClass
    }
  }
  return 'default'
}

/**
 * 将字典对象转换为数组格式（用于el-select等组件）
 */
export function dictToArray(dictObj) {
  return Object.keys(dictObj).map(key => ({
    value: dictObj[key].value,
    label: dictObj[key].label,
    cssClass: dictObj[key].cssClass
  }))
}

/**
 * 获取字典数组（带缓存）
 */
export async function getDictArray(dictType) {
  if (dictCache[dictType]) {
    return dictCache[dictType]
  }

  try {
    const response = await getDictByType(dictType)
    dictCache[dictType] = response.data
    return response.data
  } catch (error) {
    console.error(`获取字典数据失败: ${dictType}`, error)
    return []
  }
}

/**
 * 清除字典缓存
 */
export function clearDictCache(dictType = null) {
  if (dictType) {
    delete dictCache[dictType]
  } else {
    Object.keys(dictCache).forEach(key => {
      delete dictCache[key]
    })
  }
}

@startuml
skinparam monochrome true

participant "APP" as Mobile
participant "车端（BLE & TBOX）" as CarBT
participant "钥匙云平台" as Cloud
participant "TSP/OEM平台" as ExternalSystem

== 蓝牙配对流程 ==

Mobile -> Mobile : 1. 在APP中选择添加数字钥匙
Mobile -> Mobile : 2. 扫描车辆二维码或输入VIN码识别车辆
Mobile -> Cloud : 3. 发送车辆VIN码，请求获取数字钥匙
Cloud -> Cloud : 4. 根据VIN码生成虚拟密钥（VIRKEY）和唯一配对令牌
Cloud -> Mobile : 5. 下发虚拟密钥和配对令牌到手机
Mobile -> Mobile : 6. 保存虚拟密钥和配对令牌到安全存储区

note over Cloud, ExternalSystem
  钥匙云平台通过安全通道与TSP/OEM平台交互
  获取车辆TBOX信息并建立安全连接
end note

Cloud -> ExternalSystem : 7. 通过统一接口适配层查询VIN码对应的TBOX信息
ExternalSystem -> Cloud : 8. 返回TBOX信息
Cloud -> CarBT : 9. 通过4G连接到车辆TBOX并生成根密钥下发到汽车

note over Cloud, CarBT
  根密钥(Root Key)是整个安全体系的基础
  用于后续蓝牙通信的加密和认证
end note

CarBT -> CarBT : 10. 存储根密钥和配对令牌
CarBT -> Mobile : 11. 发送蓝牙广播（包含MAC、加密VIN码和挑战码等信息）

note over Mobile, CarBT
  蓝牙广播中的加密VIN码只能被持有对应虚拟密钥的手机解密
  确保只有授权手机能识别并连接到正确的车辆
end note

Mobile -> Mobile : 12a. 解密广播中的VIN码并与本地存储的VIN码比对
Mobile -> CarBT : 12b. 根据广播中的加密VIN码，筛出配对车辆并连接到汽车蓝牙模块
Mobile -> CarBT : 13. 发送蓝牙配对请求（包含配对令牌和虚拟密钥派生的证明）
CarBT -> CarBT : 14a. 验证配对令牌的有效性
CarBT -> Mobile : 14b. 发起蓝牙安全请求（包含基于根密钥生成的挑战）

CarBT -> CarBT : 15. 生成配对码（基于根密钥和配对令牌）
Mobile -> Mobile : 16. 生成配对码（基于虚拟密钥和配对令牌）
Mobile -> CarBT : 17. 发送配对码和挑战响应进行双向验证
CarBT -> CarBT : 18a. 验证挑战响应和配对码
CarBT -> Mobile : 18b. 返回验证结果和车端证明

note over Mobile, CarBT
  双向验证确保:
  1. 手机持有正确的虚拟密钥和配对令牌
  2. 车辆持有正确的根密钥和配对令牌
  只有双方都通过验证才能完成配对
end note

== 会话密钥生成与安全通道建立 ==

Mobile -> Mobile : 19a. 生成ECDH临时密钥对(公钥+私钥)
Mobile -> CarBT : 19b. 发送ECDH公钥和时间戳(防重放)
CarBT -> CarBT : 20a. 生成ECDH临时密钥对(公钥+私钥)
CarBT -> Mobile : 20b. 发送ECDH公钥和时间戳(防重放)

note over Mobile, CarBT
  ECDH(椭圆曲线迪菲-赫尔曼)密钥交换:
  1. 双方各自生成临时公私钥对
  2. 交换公钥但保留私钥
  3. 使用对方公钥和自己私钥计算共享密钥
  4. 双方计算结果相同但窃听者无法计算
end note

Mobile -> Mobile : 21a. 使用ECDH私钥和车端公钥计算共享密钥
Mobile -> Mobile : 21b. 使用KDF(密钥派生函数)从共享密钥派生会话密钥
CarBT -> CarBT : 22a. 使用ECDH私钥和手机公钥计算共享密钥
CarBT -> CarBT : 22b. 使用KDF(密钥派生函数)从共享密钥派生会话密钥

note over Mobile, CarBT
  会话密钥特性:
  1. 使用AES-256位加密算法
  2. 包含加密密钥和认证密钥两部分
  3. 有效期为24小时或单次会话
  4. 存储在TEE/SE安全区域中
end note

CarBT -> CarBT : 23. 验证成功，存储以下信息到安全存储区
note right of CarBT
  - 会话密钥（Session Key）
  - 绑定的手机设备ID
  - 密钥有效期限
  - 授权等级（主/副用户）
end note

Mobile -> Mobile : 24. 验证成功，存储以下信息到手机安全区
note right of Mobile
  - 会话密钥（Session Key）
  - 车辆标识（VIN）
  - 蓝牙MAC地址
  - 密钥有效期限
  - 钥匙权限等级
end note

CarBT <-> Mobile : 25. 建立加密安全通道（基于会话密钥）

note over Mobile, CarBT
  安全通道特性:
  1. 所有消息使用AES-256-GCM加密(提供机密性和完整性)
  2. 每条消息包含递增序列号和时间戳(防重放)
  3. 消息包含HMAC认证码(确保完整性)
  4. 定期进行密钥验证(确保密钥未泄露)
end note

Mobile -> Cloud : 26. 上报配对成功状态，包含：设备ID、车辆VIN、配对时间戳
Cloud -> Cloud : 27. 在钥匙管理系统中记录钥匙绑定关系
note right of Cloud
  - 用户账号与车辆VIN绑定
  - 设备标识与钥匙ID绑定
  - 钥匙权限等级
  - 生效和过期时间
end note

Cloud -> Mobile : 28. 返回数字钥匙ID和完整权限配置信息
Mobile -> Mobile : 29. 显示数字钥匙添加成功，可用于解锁和启动车辆

== 异常处理流程 ==

note over Mobile, CarBT
  配对过程中可能的异常情况及处理:
end note

alt 配对超时
    Mobile -> Mobile : 检测到配对请求超过30秒无响应
    Mobile -> Mobile : 显示"配对超时，请重试"提示
    Mobile -> Cloud : 上报配对失败状态(超时)
    Cloud -> Cloud : 记录失败事件并分析原因
else 验证失败
    CarBT -> Mobile : 返回验证失败错误码
    Mobile -> Mobile : 显示"验证失败，请确认车辆状态"提示
    Mobile -> Cloud : 上报配对失败状态(验证失败)
    Cloud -> Cloud : 记录失败事件，若多次失败触发安全预警
else 会话密钥生成失败
    Mobile -> Mobile : 检测到密钥交换异常
    Mobile -> CarBT : 发送中止配对请求
    Mobile -> Mobile : 显示"密钥生成失败，请重试"提示
    Mobile -> Cloud : 上报配对失败详情
    Cloud -> Cloud : 分析失败原因，必要时更新安全策略
end

note over Cloud, CarBT
  完整配对流程结束后各方存储的核心信息:
  1. 手机端: 会话密钥、车辆信息、钥匙ID、权限配置
  2. 车端: 会话密钥、授权设备ID、权限等级
  3. 云平台: 用户-车辆-设备三者关系、钥匙状态管理信息
  
  这些信息将用于:
  - 后续蓝牙重连验证
  - 功能权限控制
  - 远程管理与授权
end note

note over Mobile, CarBT
  会话密钥安全保障措施:
  1. 定期轮换: 会话密钥每24小时或每次重连自动更新
  2. 防篡改存储: 密钥存储在TEE/SE安全区，应用层无法直接访问
  3. 完整性校验: 每次使用前验证密钥完整性
  4. 异常监控: 检测到异常连接模式时强制更新密钥
end note

@enduml
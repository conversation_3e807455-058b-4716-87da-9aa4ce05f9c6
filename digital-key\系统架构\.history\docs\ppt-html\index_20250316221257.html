<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>数字钥匙系统 - 功能导航</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            display: flex;
            flex-direction: column;
            height: 100vh;
            background-color: #f5f5f5;
            color: #333;
        }
        
        header {
            background-color: #2c3e50;
            color: white;
            padding: 15px 20px;
            text-align: center;
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
        }
        
        .container {
            display: flex;
            flex: 1;
            overflow: hidden;
        }
        
        .nav {
            width: 250px;
            background-color: #34495e;
            color: white;
            padding: 20px 0;
            overflow-y: auto;
            box-shadow: 2px 0 5px rgba(0,0,0,0.1);
        }
        
        .nav-item {
            padding: 12px 20px;
            cursor: pointer;
            transition: background-color 0.3s;
            border-left: 3px solid transparent;
        }
        
        .nav-item:hover {
            background-color: #2c3e50;
            border-left-color: #3498db;
        }
        
        .nav-item.active {
            background-color: #2c3e50;
            border-left-color: #3498db;
            font-weight: bold;
        }
        
        .content {
            flex: 1;
            overflow: hidden;
            background-color: white;
            box-shadow: inset 2px 0 5px rgba(0,0,0,0.05);
        }
        
        iframe {
            width: 100%;
            height: 100%;
            border: none;
        }
        
        .loading {
            display: flex;
            align-items: center;
            justify-content: center;
            height: 100%;
            font-size: 18px;
            color: #7f8c8d;
        }
        
        @media (max-width: 768px) {
            .container {
                flex-direction: column;
            }
            
            .nav {
                width: 100%;
                padding: 10px 0;
            }
            
            .content {
                flex: 1;
            }
        }
    </style>
</head>
<body>
    <header>
        <h1>数字钥匙系统演示</h1>
    </header>
    
    <div class="container">
        <div class="nav" id="nav">
            <!-- 导航项将通过JavaScript动态添加 -->
        </div>
        
        <div class="content">
            <iframe id="content-frame" src="about:blank"></iframe>
        </div>
    </div>
    
    <script>
        // 以下是模拟自动扫描的函数，在实际环境中，它会被替换成上述方案中的服务器请求
        function scanHTMLFiles() {
            // 通过AJAX请求获取当前目录的内容（需要服务器支持）
            // 这里我们使用硬编码的列表作为备选方案
            
            // 当你添加新文件时，只需将其放入下方数组
            // 格式：{ id: 文件名（小写无空格）, name: 显示名称, file: 文件名.html }
            return [
                { id: 'app', name: 'APP - 功能展示', file: 'APP - 功能展示.html' },
                { id: 'car', name: '车端 - 功能展示', file: '车端 - 功能展示.html' },
                { id: 'cloud', name: '钥匙云平台 - 功能展示', file: '钥匙云平台 - 功能展示.html' },
                { id: 'share', name: '钥匙分享', file: '钥匙分享.html' },
                { id: 'pair', name: '钥匙配对', file: '钥匙配对.html' },
                { id: 'auto', name: '无感控车', file: '无感控车.html' }
                // 在这里添加新文件，就无需修改其他代码
            ];
        }
        
        // 创建导航菜单
        function createNavigation() {
            const navElement = document.getElementById('nav');
            navElement.innerHTML = ''; // 清空现有内容
            
            const pages = scanHTMLFiles();
            window.pages = pages;
            
            pages.forEach(page => {
                const navItem = document.createElement('div');
                navItem.className = 'nav-item';
                navItem.textContent = page.name;
                navItem.dataset.file = page.file;
                navItem.onclick = function() {
                    // 设置当前项为激活状态
                    document.querySelectorAll('.nav-item').forEach(item => {
                        item.classList.remove('active');
                    });
                    this.classList.add('active');
                    
                    // 加载内容
                    loadContent(page.file);
                };
                
                navElement.appendChild(navItem);
            });
        }
        
        // 加载内容到iframe
        function loadContent(fileName) {
            const iframe = document.getElementById('content-frame');
            iframe.src = fileName;
        }
        
        // 初始化
        document.addEventListener('DOMContentLoaded', function() {
            createNavigation();
            
            // 默认加载第一个页面
            if (window.pages.length > 0) {
                const firstNavItem = document.querySelector('.nav-item');
                if (firstNavItem) {
                    firstNavItem.classList.add('active');
                    loadContent(window.pages[0].file);
                }
            }
        });
    </script>
</body>
</html> 
package com.ruoyi.dk.domain;

import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.core.annotation.Excel;
import com.ruoyi.common.core.web.domain.BaseEntity;

/**
 * 蓝牙密钥对象 dk_vehicle_bluetooth_keys
 * 
 * <AUTHOR>
 * @date 2024-05-14
 */
public class DkVehicleBluetoothKeys extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 用户 */
    @Excel(name = "用户")
    private Long userId;

    /** 用户名 */
    private String userName;

    /** 车辆VIN码 */
    @Excel(name = "车辆VIN码")
    private String vehicleVin;

    /** 临时密钥 */
    @Excel(name = "临时密钥")
    private String bluetoothTempKey;

    /** &lt; 生成时间 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "&lt; 生成时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date bluetoothTempKeyTime;

    /** 正式密钥 */
    @Excel(name = "正式密钥")
    private String bluetoothPermKey;

    /** &lt; 生成时间 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "&lt; 生成时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date bluetoothPermKeyTime;

    public void setUserId(Long userId) 
    {
        this.userId = userId;
    }

    public Long getUserId() 
    {
        return userId;
    }

    public String getUserName() {
        return userName;
    }

    public void setUserName(String userName) {
        this.userName = userName;
    }

    public void setVehicleVin(String vehicleVin)
    {
        this.vehicleVin = vehicleVin;
    }

    public String getVehicleVin() 
    {
        return vehicleVin;
    }
    public void setBluetoothTempKey(String bluetoothTempKey) 
    {
        this.bluetoothTempKey = bluetoothTempKey;
    }

    public String getBluetoothTempKey() 
    {
        return bluetoothTempKey;
    }
    public void setBluetoothTempKeyTime(Date bluetoothTempKeyTime) 
    {
        this.bluetoothTempKeyTime = bluetoothTempKeyTime;
    }

    public Date getBluetoothTempKeyTime() 
    {
        return bluetoothTempKeyTime;
    }
    public void setBluetoothPermKey(String bluetoothPermKey) 
    {
        this.bluetoothPermKey = bluetoothPermKey;
    }

    public String getBluetoothPermKey() 
    {
        return bluetoothPermKey;
    }
    public void setBluetoothPermKeyTime(Date bluetoothPermKeyTime) 
    {
        this.bluetoothPermKeyTime = bluetoothPermKeyTime;
    }

    public Date getBluetoothPermKeyTime() 
    {
        return bluetoothPermKeyTime;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("userId", getUserId())
            .append("userName", getUserName())
            .append("vehicleVin", getVehicleVin())
            .append("bluetoothTempKey", getBluetoothTempKey())
            .append("bluetoothTempKeyTime", getBluetoothTempKeyTime())
            .append("bluetoothPermKey", getBluetoothPermKey())
            .append("bluetoothPermKeyTime", getBluetoothPermKeyTime())
            .toString();
    }
}

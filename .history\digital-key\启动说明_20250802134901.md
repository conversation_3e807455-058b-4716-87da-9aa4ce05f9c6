# UniApp项目启动说明

## 问题解决

您遇到的编译错误已经修复：

### 修复的问题
1. **pages.json配置错误**: 移除了不存在的页面引用
   - 删除了 `pages/test/api-test.vue`
   - 删除了 `pages/test/button-test.vue` 
   - 删除了 `pages/test/index.vue`
   - 删除了 `pages/test/withdraw-test.vue`
   - 删除了 `pages/work/applicationStatus/index.vue`
   - 删除了 `pages/work/keyManagement/index.vue`
   - 删除了 `pages/work/userApplication/index.vue`

2. **tabBar配置更新**: 将不存在的钥匙管理页面改为工作台页面

## 启动方法

### 方法一：使用HBuilderX IDE（推荐）
1. 打开HBuilderX IDE
2. 点击"文件" -> "打开目录"
3. 选择项目目录：`d:\Develop\zj-zerosense\digital-key-all\digital-key`
4. 在HBuilderX中点击"运行" -> "运行到小程序模拟器" -> "微信开发者工具"

### 方法二：使用命令行（需要安装CLI工具）
```bash
# 1. 安装uniapp CLI工具
npm install -g @dcloudio/cli

# 2. 运行项目
npm run dev:mp-weixin
```

### 方法三：使用微信开发者工具
1. 确保已安装微信开发者工具
2. 在HBuilderX中运行项目到微信开发者工具
3. 或者直接在微信开发者工具中导入项目

## 4G控车功能测试

### 1. 主要功能页面
- **数字钥匙主页**: `pages/work/digitalKey/index.vue`
  - 包含4G控车状态显示
  - 智能切换蓝牙/4G控车
  - 实时车辆状态显示

- **4G控车测试页**: `pages/test/4GControlTest.vue`
  - 专门用于测试4G控车功能
  - 详细的消息日志
  - 手动控车操作

### 2. 测试步骤
1. **启动云平台服务**
   - 确保SpringBoot后端服务运行在 `localhost:9201`
   - 启动TBOX模拟器页面

2. **测试4G控车功能**
   - 在数字钥匙主页面，当蓝牙未连接时会显示4G控车状态
   - 点击门锁按钮测试控车功能
   - 观察状态变化和反馈信息

3. **使用测试页面**
   - 访问4G控车测试页面进行详细测试
   - 查看连接状态和消息日志

## 配置说明

### 服务器地址配置
编辑 `config/4GControlConfig.js` 文件修改服务器地址：

```javascript
const development = {
  websocketUrl: 'ws://localhost:9201',  // 开发环境地址
  enabled: true
}

const production = {
  websocketUrl: 'ws://your-server:9201',  // 生产环境地址
  enabled: true
}
```

## 版本信息

- **应用版本**: 1.2.0 (已更新)
- **版本代码**: 120
- **新增功能**: 4G控车支持

## 故障排除

### 1. 编译错误
- ✅ 已修复pages.json中的页面引用错误
- ✅ 已更新tabBar配置

### 2. 4G控车连接失败
- 检查云平台服务是否正常运行
- 确认WebSocket服务地址配置正确
- 查看浏览器控制台错误信息

### 3. 控车指令无响应
- 确认TBOX模拟器正常运行
- 检查TCP服务连接状态
- 查看服务器端日志

## 联系支持

如果仍有问题，请检查：
1. HBuilderX版本是否最新
2. 微信开发者工具是否正常
3. Node.js版本是否兼容
4. 网络连接是否正常

项目现在应该可以正常启动和运行了！🚀

@startuml
skinparam monochrome true

participant "APP" as Mobile
participant "钥匙云平台" as Cloud
participant "外部平台(TSP/OEM)" as External
participant "车端（TBOX）" as CarTBOX

== 数字钥匙申请与车辆绑定流程 ==

Mobile -> Mobile : 1. 用户在APP中扫描车辆二维码或输入VIN码
Mobile -> Cloud : 2. 发送车辆VIN码，请求获取数字钥匙
Cloud -> External : 3. 查询VIN码对应的车辆信息和TBOX信息
External -> Cloud : 4. 返回车辆信息和TBOX连接凭证
Cloud -> Cloud : 5. 生成虚拟密钥(VIRKEY)
Cloud -> Mobile : 6. 下发虚拟密钥到手机

note right of Cloud
  钥匙云平台通过外部平台(TSP/OEM)
  获取车辆信息和建立TBOX连接
end note

Cloud -> External : 7. 请求与车辆TBOX建立连接
External -> CarTBOX : 8. 通过蜂窝网络连接到车辆TBOX
CarTBOX -> External : 9. TBOX身份验证和连接确认
Cloud -> CarTBOX : 10. 通过外部平台下发证书和根密钥到车辆
CarTBOX -> CarTBOX : 11. 存储证书和根密钥
CarTBOX -> Cloud : 12. 确认密钥接收成功
Cloud -> Mobile : 13. 通知APP车辆绑定成功

== 远程控制车辆流程 ==

Mobile -> Cloud : 1. 发送远程控制请求(如开锁)
Cloud -> Cloud : 2. 验证用户权限和密钥有效性
Cloud -> External : 3. 发送远程控制指令
External -> CarTBOX : 4. 下发控制指令到车辆TBOX
CarTBOX -> CarTBOX : 5. 执行控制指令
CarTBOX -> External : 6. 返回执行结果
External -> Cloud : 7. 转发执行结果
Cloud -> Mobile : 8. 通知APP执行结果

== 车辆诊断信息查询流程 ==

Mobile -> Cloud : 1. 发送诊断信息查询请求
Cloud -> External : 2. 请求车辆诊断信息
External -> CarTBOX : 3. 请求车辆实时诊断数据
CarTBOX -> External : 4. 返回诊断数据
External -> Cloud : 5. 返回诊断结果和建议
Cloud -> Mobile : 6. 转发诊断结果

@enduml 
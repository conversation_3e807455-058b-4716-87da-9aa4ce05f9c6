<template>
  <view class="container">
    <view class="test-section">
      <text class="title">按钮点击测试</text>
      
      <!-- 简单按钮测试 -->
      <view class="test-item">
        <button @click="testSimpleClick" class="test-btn">简单点击测试</button>
      </view>
      
      <!-- 带参数的按钮测试 -->
      <view class="test-item">
        <button @click="testWithParam({ applicationId: 123, realName: '测试用户' })" class="test-btn">
          带参数点击测试
        </button>
      </view>
      
      <!-- 模拟撤销申请按钮 -->
      <view class="test-item">
        <button 
          class="cu-btn line-red sm round"
          @click.stop="withdrawApplication({ applicationId: 123, realName: '测试用户', status: '0' })"
        >
          模拟撤销申请按钮
        </button>
      </view>
      
      <!-- 显示测试结果 -->
      <view class="result-area">
        <text class="result-title">测试结果：</text>
        <text class="result-text">{{ testResult }}</text>
      </view>
    </view>
  </view>
</template>

<script>
import { withdrawApplication as withdrawApplicationApi } from '@/api/dk'

export default {
  data() {
    return {
      testResult: '未测试'
    }
  },
  
  methods: {
    testSimpleClick() {
      console.log('简单点击测试被调用')
      this.testResult = '简单点击测试成功 - ' + new Date().toLocaleTimeString()
      uni.showToast({ title: '简单点击成功', icon: 'success' })
    },
    
    testWithParam(item) {
      console.log('带参数点击测试被调用:', item)
      this.testResult = `带参数点击测试成功 - ID: ${item.applicationId}, 姓名: ${item.realName}`
      uni.showToast({ title: '带参数点击成功', icon: 'success' })
    },
    
    withdrawApplication(item) {
      console.log('=== 撤销申请方法被调用 ===')
      console.log('申请项目:', item)
      console.log('申请ID:', item.applicationId)

      this.testResult = `撤销申请方法被调用 - ID: ${item.applicationId}`

      const self = this // 保存this上下文

      uni.showModal({
        title: '确认撤销',
        content: '确定要撤销这个申请吗？撤销后无法恢复。',
        success: async (result) => {
          console.log('确认对话框结果:', result)
          console.log('result.confirm的值:', result.confirm)
          console.log('result.confirm的类型:', typeof result.confirm)

          if (result.confirm) {
            console.log('用户确认撤销，开始调用API')
            self.testResult += ' - 用户确认撤销'

            try {
              const apiResult = await withdrawApplicationApi(item.applicationId)
              console.log('撤销API调用成功:', apiResult)
              self.testResult += ' - API调用成功'
              uni.showToast({ title: '撤销成功', icon: 'success' })
            } catch (error) {
              console.error('撤销申请失败:', error)
              self.testResult += ' - API调用失败: ' + error.message
              uni.showToast({ title: '撤销失败', icon: 'none' })
            }
          } else {
            console.log('用户取消了撤销操作')
            console.log('完整的result对象:', JSON.stringify(result))
            self.testResult += ' - 用户取消撤销'
          }
        },
        fail: (error) => {
          console.error('显示确认对话框失败:', error)
          self.testResult += ' - 对话框显示失败: ' + error.message
        }
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.container {
  padding: 40rpx;
}

.title {
  font-size: 36rpx;
  font-weight: bold;
  text-align: center;
  margin-bottom: 40rpx;
}

.test-item {
  margin-bottom: 30rpx;
  padding: 20rpx;
  background: #f5f5f5;
  border-radius: 10rpx;
}

.test-btn {
  background: #007aff;
  color: white;
  border: none;
  padding: 20rpx;
  border-radius: 10rpx;
  width: 100%;
}

.result-area {
  margin-top: 40rpx;
  padding: 20rpx;
  background: #fff;
  border-radius: 10rpx;
  border: 1rpx solid #ddd;
}

.result-title {
  font-size: 28rpx;
  font-weight: bold;
  color: #333;
  display: block;
  margin-bottom: 10rpx;
}

.result-text {
  font-size: 26rpx;
  color: #666;
  line-height: 1.5;
}
</style>
</template>

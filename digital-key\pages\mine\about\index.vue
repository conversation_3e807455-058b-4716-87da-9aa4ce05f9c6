<template>
  <view class="container bg-cls">
    <!-- 自定义导航栏 -->
    <view class="custom-navbar">
      <text class="navbar-title">关于我们</text>
    </view>

    <!-- 页面内容 -->
    <view class="about-container">
      <view class="header-section text-center">
        <image style="width: 150rpx;height: 150rpx;" src="/static/logo.png" mode="widthFix">
        </image>
        <uni-title type="h2" title="数字钥匙"></uni-title>
      </view>

      <view class="content-section">
        <view class="menu-list">
          <view class="list-cell list-cell-arrow">
            <view class="menu-item-box">
              <view>版本信息</view>
              <view class="text-right">v{{version}}</view>
            </view>
          </view>
          <view class="list-cell list-cell-arrow">
            <view class="menu-item-box">
              <view>官方邮箱</view>
              <view class="text-right"><EMAIL></view>
            </view>
          </view>
          <view class="list-cell list-cell-arrow">
            <view class="menu-item-box">
              <view>服务热线</view>
              <view class="text-right">************</view>
            </view>
          </view>
          <view class="list-cell list-cell-arrow">
            <view class="menu-item-box">
              <view>公司网站</view>
              <view class="text-right">
                <uni-link :href="url" :text="url" showUnderLine="false"></uni-link>
              </view>
            </view>
          </view>
        </view>
      </view>

      <view class="copyright">
        <view>Copyright &copy; 2024 零感科技 All Rights Reserved.</view>
      </view>
    </view>
  </view>
</template>

<script>
  export default {
    data() {
      return {
        url: getApp().globalData.config.appInfo.site_url,
        version: getApp().globalData.config.appInfo.version
      }
    }
  }
</script>

<style lang="scss">
  page {
    /* 设置背景固定，不随页面滚动 */
    background-attachment: fixed;

    /* 其他样式，比如设置高度 */
    height: 70vh;
    /* 设置高度为视口高度 */
  }

  .list-cell {
    background-color: #ffffff17;
    border-bottom-style: solid;
    border-bottom-width: 1px;
    border-bottom-color: #b6b6b76b;
  }

  .custom-navbar {
    display: flex;
    justify-content: center;
    align-items: center;
    height: 184rpx; /* 导航栏高度 */
    border-radius: 0rpx 0rpx 0rpx 0rpx;
    background-color: rgba(0, 0, 0, 0); /* 自定义背景色 */
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    z-index: 999;
  }

  .navbar-title {
    margin-top: 75rpx;
    color: #121010; /* 标题颜色 */
  }

  .about-container {
    padding-top: 184rpx;
    width: 100%;
    height: 100%;
  }

  .copyright {
    margin-top: 50rpx;
    text-align: center;
    line-height: 60rpx;
    color: #999;
  }

  .header-section {
    display: flex;
    padding: 30rpx 0 0;
    flex-direction: column;
    align-items: center;
  }
</style>

package com.ruoyi.tcp;

import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.autoconfigure.jdbc.DataSourceAutoConfiguration;
import org.springframework.boot.autoconfigure.jdbc.DataSourceTransactionManagerAutoConfiguration;
import org.springframework.boot.autoconfigure.orm.jpa.HibernateJpaAutoConfiguration;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.socket.config.annotation.EnableWebSocket;
import org.springframework.web.socket.config.annotation.WebSocketConfigurer;
import org.springframework.web.socket.config.annotation.WebSocketHandlerRegistry;
import org.springframework.web.socket.server.support.HttpSessionHandshakeInterceptor;

import javax.annotation.PostConstruct;
import javax.annotation.PreDestroy;
import java.io.*;
import java.net.ServerSocket;
import java.net.Socket;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;

/**
 * 最简单的4G控车TCP服务器
 * 完全独立，不依赖任何数据库和复杂配置
 */
@SpringBootApplication(exclude = {
    DataSourceAutoConfiguration.class,
    DataSourceTransactionManagerAutoConfiguration.class,
    HibernateJpaAutoConfiguration.class
})
@EnableWebSocket
public class SimpleTcpServerApplication implements WebSocketConfigurer {

    public static void main(String[] args) {
        SpringApplication.run(SimpleTcpServerApplication.class, args);
        System.out.println("🚀 最简单的4G控车服务器启动成功！");
        System.out.println("📡 TCP服务器端口: 9999");
        System.out.println("🌐 WebSocket端口: 8080");
        System.out.println("🎯 专注4G控车核心功能，无数据库依赖");
    }

    @Override
    public void registerWebSocketHandlers(WebSocketHandlerRegistry registry) {
        registry.addHandler(new SimpleWebSocketHandler(), "/websocket/message")
                .addInterceptors(new HttpSessionHandshakeInterceptor())
                .setAllowedOrigins("*");
    }

    @Bean
    public SimpleTcpServer simpleTcpServer() {
        return new SimpleTcpServer();
    }
}

/**
 * 简单的TCP服务器
 */
@Configuration
class SimpleTcpServer {
    private ServerSocket serverSocket;
    private ExecutorService executor = Executors.newCachedThreadPool();
    private static final ConcurrentHashMap<String, Socket> tboxClients = new ConcurrentHashMap<>();
    
    @PostConstruct
    public void start() {
        // 注释掉自动启动，避免与system模块的TCP服务器冲突
        // 如果需要使用独立TCP服务器，请手动启动此应用
        System.out.println("⚠️  独立TCP服务器已禁用，避免端口冲突");
        System.out.println("💡 如需使用独立TCP服务器，请禁用system模块中的TCP服务器");
        return;
        /*
        try {
            serverSocket = new ServerSocket(9999);
            executor.submit(() -> {
                System.out.println("✅ TCP服务器启动成功，监听端口: 9999");
                while (!serverSocket.isClosed()) {
                    try {
                        Socket clientSocket = serverSocket.accept();
                        String clientId = "TBOX_" + System.currentTimeMillis();
                        tboxClients.put(clientId, clientSocket);
                        executor.submit(new TboxClientHandler(clientId, clientSocket));
                        System.out.println("📱 TBOX设备连接: " + clientId);
                    } catch (IOException e) {
                        if (!serverSocket.isClosed()) {
                            System.err.println("❌ 接受连接失败: " + e.getMessage());
                        }
                    }
                }
            });
        } catch (IOException e) {
            System.err.println("❌ TCP服务器启动失败: " + e.getMessage());
        }
    }
    
    @PreDestroy
    public void stop() {
        try {
            if (serverSocket != null && !serverSocket.isClosed()) {
                serverSocket.close();
            }
            executor.shutdown();
            System.out.println("🛑 TCP服务器已停止");
        } catch (IOException e) {
            System.err.println("❌ 停止TCP服务器失败: " + e.getMessage());
        }
    }
    
    public static void sendCommandToTbox(String tboxId, String command) {
        Socket socket = tboxClients.get(tboxId);
        if (socket != null && !socket.isClosed()) {
            try {
                PrintWriter out = new PrintWriter(socket.getOutputStream(), true);
                out.println(command);
                System.out.println("📤 发送命令到 " + tboxId + ": " + command);
            } catch (IOException e) {
                System.err.println("❌ 发送命令失败: " + e.getMessage());
                tboxClients.remove(tboxId);
            }
        } else {
            System.err.println("❌ TBOX设备未连接: " + tboxId);
        }
    }
}

/**
 * TBOX客户端处理器
 */
class TboxClientHandler implements Runnable {
    private final String clientId;
    private final Socket socket;
    
    public TboxClientHandler(String clientId, Socket socket) {
        this.clientId = clientId;
        this.socket = socket;
    }
    
    @Override
    public void run() {
        try (BufferedReader in = new BufferedReader(new InputStreamReader(socket.getInputStream()))) {
            String message;
            while ((message = in.readLine()) != null) {
                System.out.println("📥 收到 " + clientId + " 消息: " + message);
                
                // 解析TBOX协议消息
                if (message.startsWith("7E") && message.endsWith("007E")) {
                    String status = parseVehicleStatus(message);
                    System.out.println("🚗 车辆状态: " + status);
                    
                    // 通知WebSocket客户端
                    SimpleWebSocketHandler.notifyMobileApp(clientId, status);
                }
            }
        } catch (IOException e) {
            System.err.println("❌ 处理TBOX连接失败: " + e.getMessage());
        } finally {
            try {
                socket.close();
            } catch (IOException e) {
                // 忽略关闭异常
            }
            System.out.println("📱 TBOX设备断开: " + clientId);
        }
    }
    
    private String parseVehicleStatus(String message) {
        // 简单的协议解析
        if (message.contains("3033303230303031303")) {
            if (message.endsWith("3007E")) {
                return "已锁车";
            } else if (message.endsWith("4007E")) {
                return "已解锁";
            }
        }
        return "未知状态";
    }
}

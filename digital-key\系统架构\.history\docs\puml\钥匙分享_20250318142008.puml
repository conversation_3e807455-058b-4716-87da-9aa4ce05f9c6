@startuml
skinparam monochrome true

actor "车主(Owner)" as Owner
actor "被分享者(Recipient)" as Recipient
participant "车主APP" as OwnerApp
participant "被分享者APP" as RecipientApp
participant "钥匙云平台" as Cloud
participant "车辆(BLE & TBOX)" as Car
database "安全存储区域" as SecureStorage

== 钥匙分享发起 ==

Owner -> OwnerApp : 1. 选择"分享我的钥匙"功能
OwnerApp -> OwnerApp : 2. 显示可分享的车辆列表
Owner -> OwnerApp : 3. 选择要分享的车辆
OwnerApp -> OwnerApp : 4. 展示分享选项界面
Owner -> OwnerApp : 5. 设置：\n- 权限级别(完全/有限/仅访问)\n- 有效期限(临时/周期/长期)\n- 使用限制(时间/地点/功能)
OwnerApp -> Cloud : 6. 申请临时分享令牌
Cloud -> Cloud : 7. 生成包含车辆信息与权限的临时分享令牌
Cloud -> OwnerApp : 8. 返回临时分享令牌
OwnerApp -> OwnerApp : 9. 生成包含分享信息的二维码

note right of OwnerApp
  二维码内容包括：
  - 临时分享令牌
  - 车辆基本信息
  - 分享权限预设
  - 车主唯一标识
  - 有效期信息
  - 防伪签名
end note

Owner -> Recipient : 10. 出示二维码给被分享者

== 被分享者扫码接收流程 ==

Recipient -> RecipientApp : 11. 打开APP扫描二维码功能
RecipientApp -> RecipientApp : 12. 扫描并解析二维码信息
RecipientApp -> Cloud : 13. 发送接收请求(包含临时令牌)
Cloud -> Cloud : 14. 验证临时令牌有效性
Cloud -> OwnerApp : 15. 推送分享确认请求

note right of Cloud
  安全验证机制：
  1. 验证临时令牌是否在有效期内
  2. 确认是否由合法车主生成
  3. 防止二维码被复制盗用
end note

Owner -> OwnerApp : 16. 确认分享请求
OwnerApp -> Cloud : 17. 发送确认结果
Cloud -> Cloud : 18. 创建分享密钥：\n- 根据权限级别派生子密钥\n- 生成唯一分享ID\n- 设置有效期和限制条件\n- 关联车主和接收者身份
Cloud -> SecureStorage : 19. 记录分享信息(状态：已确认)
Cloud -> RecipientApp : 20. 下发加密的分享钥匙包
Cloud -> Car : 21. 通过TSP下发授权信息：\n- 添加被授权用户ID\n- 设置权限级别\n- 配置有效期\n- 同步使用限制

RecipientApp -> RecipientApp : 22. 将钥匙包存入设备安全区域(TEE/SE)
RecipientApp -> Recipient : 23. 显示"钥匙接收成功，可以使用"
Cloud -> OwnerApp : 24. 通知"分享已成功完成"

== 被分享钥匙使用流程 ==

Recipient -> RecipientApp : 25. 选择使用分享的车钥匙
RecipientApp -> RecipientApp : 26. 从安全区域读取钥匙信息
RecipientApp -> Car : 27. 发现并连接车辆蓝牙

Car -> Car : 28. 验证临时授权是否有效
RecipientApp <-> Car : 29. 执行双向认证
RecipientApp <-> Car : 30. 建立加密安全通道

RecipientApp -> Car : 31. 发送控车指令(带权限级别标识)
Car -> Car : 32. 验证指令权限并执行
Car -> RecipientApp : 33. 返回执行结果
RecipientApp -> Recipient : 34. 显示操作成功/失败

Cloud <- Car : 35. 上报使用记录
Cloud -> OwnerApp : 36. 通知车主"您的车被[接收者]使用"

== 钥匙分享管理 ==

Owner -> OwnerApp : 37. 查看"我的分享管理"
OwnerApp -> Cloud : 38. 获取所有分享记录
Cloud -> OwnerApp : 39. 返回分享列表和状态
OwnerApp -> Owner : 40. 显示所有分享记录：\n- 接收者\n- 分享状态\n- 最后使用时间\n- 剩余有效期

Owner -> OwnerApp : 41. 选择"撤销分享"
OwnerApp -> Cloud : 42. 发送撤销请求
Cloud -> Cloud : 43. 标记分享为已撤销
Cloud -> Car : 44. 通知车辆移除授权
Cloud -> RecipientApp : 45. 推送通知："您的钥匙已被撤销"
RecipientApp -> RecipientApp : 46. 删除本地钥匙数据
RecipientApp -> Recipient : 47. 显示"钥匙已失效"

== 自动过期处理 ==

note over Cloud
  当分享钥匙到达设定的过期时间
end note

Cloud -> Cloud : 48. 定时任务检测过期钥匙
Cloud -> Cloud : 49. 标记已过期的分享
Cloud -> Car : 50. 通知车辆移除过期授权
Cloud -> RecipientApp : 51. 推送通知："您的临时钥匙已过期"
Cloud -> OwnerApp : 52. 通知："分享给[接收者]的钥匙已过期"

RecipientApp -> RecipientApp : 53. 删除或归档过期钥匙
RecipientApp -> Recipient : 54. 显示"钥匙已过期"

== 异常处理 ==

alt 扫码超时
    note over Owner, Recipient
      二维码包含短期有效的临时令牌
      若超时未扫描，则需要车主重新生成
    end note
    
else 车主未确认
    note over Owner, Recipient
      若车主未在规定时间内确认分享
      系统将自动取消本次分享请求
      被分享者APP会收到相应提示
    end note
    
else 网络连接异常
    Cloud -> RecipientApp : 推送失败
    note over Cloud, RecipientApp
      系统会在网络恢复后重新尝试推送
      或在接收者下次登录时展示
    end note
else 车辆离线
    Cloud -> Car : 授权信息下发失败
    note over Cloud, Car
      系统会在车辆上线后自动同步
      同时在云端记录授权状态，确保一致性
    end note
else 安全异常
    Cloud -> Cloud : 检测到异常使用模式
    Cloud -> Owner : 发送安全预警
    Cloud -> SecureStorage : 记录安全事件
end

@enduml
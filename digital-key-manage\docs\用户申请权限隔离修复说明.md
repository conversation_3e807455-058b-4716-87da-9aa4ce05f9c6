# 用户申请权限隔离修复说明

## 问题描述
前端用户查看申请状态时，能看到所有用户的申请记录，而不是只看到自己的申请记录，存在数据权限问题。

## 问题分析

### 1. 原有接口问题
- **接口路径**：`/system/userApplication/list`
- **功能设计**：为后台管理员设计，返回所有用户的申请记录
- **权限控制**：没有按用户进行数据隔离

### 2. 数据安全风险
- 用户可以看到其他用户的个人信息（姓名、手机、身份证等）
- 违反了数据隐私保护原则
- 不符合前端用户的使用场景

## 修复方案

### 1. 新增专用接口
为前端用户新增专门的查询接口，只返回当前登录用户的申请记录：

#### 后端控制器修改
文件：`DkUserApplicationController.java`

```java
/**
 * 查询当前用户的申请列表（前端用户用）
 */
@GetMapping("/my-applications")
public TableDataInfo getMyApplications() {
    startPage();
    // 获取当前登录用户信息
    String currentUsername = SecurityUtils.getUsername();
    
    // 创建查询条件，只查询当前用户的申请
    DkUserApplication queryParam = new DkUserApplication();
    queryParam.setCreateBy(currentUsername);
    
    List<DkUserApplication> list = dkUserApplicationService.selectDkUserApplicationList(queryParam);
    return getDataTable(list);
}
```

### 2. 修改前端API调用
文件：`digital-key/api/dk.js`

```javascript
// 修改前端API路径
export function getUserApplicationList(params) {
  return request({
    url: '/system/userApplication/my-applications',  // 使用新的专用接口
    method: 'get',
    params: params
  })
}
```

### 3. 确保申请提交时设置正确的创建者
```java
/**
 * 新增用户申请
 */
@PostMapping
public AjaxResult add(@RequestBody DkUserApplication dkUserApplication) {
    // 设置创建者为当前登录用户
    dkUserApplication.setCreateBy(SecurityUtils.getUsername());
    return toAjax(dkUserApplicationService.insertDkUserApplication(dkUserApplication));
}
```

## 接口权限设计

### 1. 接口功能划分
```
/system/userApplication/list          - 后台管理员使用，查询所有申请
/system/userApplication/my-applications - 前端用户使用，只查询自己的申请
```

### 2. 数据隔离机制
- **后台管理接口**：不限制用户，返回所有申请记录
- **前端用户接口**：通过`createBy`字段过滤，只返回当前用户的记录

### 3. 安全控制
- 使用`SecurityUtils.getUsername()`获取当前登录用户
- 在查询条件中强制添加用户过滤条件
- 确保用户只能访问自己的数据

## 修复文件列表

### 后端文件
- `digital-key-manage/ruoyi-modules/ruoyi-system/src/main/java/com/ruoyi/dk/controller/DkUserApplicationController.java`
  - 新增`/my-applications`接口
  - 修改申请提交接口，设置正确的创建者
  - 添加SecurityUtils导入

### 前端文件
- `digital-key/api/dk.js`
  - 修改`getUserApplicationList`接口路径
- `digital-key/pages/work/applicationStatus/index.vue`
  - 优化数据处理逻辑，兼容TableDataInfo结构

## 测试验证

### 1. 数据隔离测试
1. **创建多个测试用户**
2. **分别登录不同用户提交申请**
3. **验证每个用户只能看到自己的申请记录**

### 2. 功能完整性测试
1. **提交申请** → 验证申请记录正确创建
2. **查看申请状态** → 验证只显示当前用户的记录
3. **申请操作** → 验证撤销、补充资料等功能正常

### 3. 权限安全测试
1. **尝试访问其他用户的申请详情** → 应该被拒绝
2. **检查API返回数据** → 确认不包含其他用户信息
3. **验证用户身份** → 确认SecurityUtils正确获取当前用户

## 预期结果

### ✅ 修复后应该实现
1. **数据隔离**：用户只能看到自己的申请记录
2. **隐私保护**：无法访问其他用户的个人信息
3. **功能正常**：申请、查看、操作等功能完整可用
4. **权限安全**：符合数据安全和隐私保护要求

### 🧪 验证方法
```bash
# 1. 重新编译后端
cd digital-key-manage
mvn clean compile

# 2. 重启系统模块
# 重启 ruoyi-system 服务

# 3. 重新编译前端
cd digital-key
npm run dev:mp-weixin

# 4. 测试验证
# - 使用不同用户登录
# - 分别提交申请
# - 验证数据隔离效果
```

## 注意事项

### 1. 用户身份验证
- 确保用户已正确登录
- SecurityUtils能正确获取用户名
- Token有效且未过期

### 2. 数据一致性
- 申请记录的createBy字段必须正确设置
- 查询条件必须包含用户过滤
- 避免数据泄露风险

### 3. 接口兼容性
- 保持原有后台管理接口不变
- 新增的前端接口不影响现有功能
- 确保前后端接口路径一致

## 后续优化建议

### 1. 权限注解
考虑使用Spring Security的权限注解进一步加强安全控制：
```java
@PreAuthorize("hasRole('USER')")
@GetMapping("/my-applications")
public TableDataInfo getMyApplications() {
    // ...
}
```

### 2. 数据脱敏
对敏感信息进行脱敏处理：
- 身份证号部分隐藏
- 手机号中间位数隐藏
- 其他个人敏感信息保护

### 3. 审计日志
添加用户操作审计日志：
- 记录用户查看申请记录的操作
- 记录申请提交、修改等关键操作
- 便于安全审计和问题排查

package com.ruoyi.framework.tcp;

import com.alibaba.fastjson2.JSON;
import com.ruoyi.dk.domain.DKWebSocketMessage;
import com.ruoyi.framework.websocket.WebSocketUsers;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.*;
import java.net.Socket;
import java.util.concurrent.ConcurrentHashMap;

/**
 * TCP客户端处理器
 * 处理TBOX客户端的连接和消息
 * 
 * <AUTHOR>
 */
public class TcpClientHandler implements Runnable {
    
    private static final Logger LOGGER = LoggerFactory.getLogger(TcpClientHandler.class);
    
    private final Socket clientSocket;
    private BufferedReader reader;
    private PrintWriter writer;
    private volatile boolean running = true;
    
    // 存储所有连接的TBOX客户端
    private static final ConcurrentHashMap<String, TcpClientHandler> TBOX_CLIENTS = new ConcurrentHashMap<>();
    
    public TcpClientHandler(Socket clientSocket) {
        this.clientSocket = clientSocket;
        try {
            this.reader = new BufferedReader(new InputStreamReader(clientSocket.getInputStream()));
            this.writer = new PrintWriter(clientSocket.getOutputStream(), true);
            
            // 将客户端添加到管理器中
            String clientId = clientSocket.getRemoteSocketAddress().toString();
            TBOX_CLIENTS.put(clientId, this);
            
        } catch (IOException e) {
            LOGGER.error("初始化TCP客户端处理器失败: {}", e.getMessage(), e);
        }
    }
    
    @Override
    public void run() {
        String clientAddress = clientSocket.getRemoteSocketAddress().toString();
        LOGGER.info("开始处理TBOX客户端: {}", clientAddress);
        
        try {
            String message;
            while (running && (message = reader.readLine()) != null) {
                LOGGER.info("收到TBOX消息: {} from {}", message, clientAddress);
                handleTboxMessage(message);
            }
        } catch (IOException e) {
            if (running) {
                LOGGER.error("处理TBOX客户端消息时发生错误: {}", e.getMessage(), e);
            }
        } finally {
            closeConnection();
        }
    }
    
    /**
     * 处理TBOX发送的消息
     */
    private void handleTboxMessage(String message) {
        try {
            // 解析TBOX消息格式: 7E000A000A000102030405060708090A000030333032303030313034007E
            if (message.startsWith("7E") && message.endsWith("7E") && message.length() >= 50) {
                // 提取倒数第五位，判断解锁(4)还是闭锁(3)
                String statusCode = message.substring(message.length() - 6, message.length() - 5);
                
                String lockStatus;
                if ("4".equals(statusCode)) {
                    lockStatus = "unlocked"; // 解锁
                } else if ("3".equals(statusCode)) {
                    lockStatus = "locked"; // 闭锁
                } else {
                    lockStatus = "unknown";
                }
                
                LOGGER.info("车辆状态更新: {}", lockStatus);
                
                // 通过WebSocket将状态反馈给手机端
                notifyMobileApp(lockStatus, message);
                
            } else {
                LOGGER.warn("收到格式不正确的TBOX消息: {}", message);
            }
        } catch (Exception e) {
            LOGGER.error("处理TBOX消息时发生错误: {}", e.getMessage(), e);
        }
    }
    
    /**
     * 通过WebSocket通知手机应用
     */
    private void notifyMobileApp(String lockStatus, String originalMessage) {
        try {
            DKWebSocketMessage wsMessage = new DKWebSocketMessage();
            wsMessage.setSenderName("TBOX");
            wsMessage.setMessage("车辆状态: " + lockStatus);
            wsMessage.setType("vehicle_status");
            
            // 可以添加更多状态信息
            wsMessage.setData(originalMessage);
            
            String jsonMessage = JSON.toJSONString(wsMessage);
            
            // 广播给所有WebSocket连接的用户
            WebSocketUsers.sendMessageToAllUsers(jsonMessage);
            
            LOGGER.info("已通过WebSocket发送车辆状态: {}", lockStatus);
            
        } catch (Exception e) {
            LOGGER.error("通过WebSocket发送消息时发生错误: {}", e.getMessage(), e);
        }
    }
    
    /**
     * 发送控制指令给TBOX
     */
    public void sendControlCommand(String command) {
        if (writer != null && !clientSocket.isClosed()) {
            writer.println(command);
            LOGGER.info("发送控制指令给TBOX: {}", command);
        } else {
            LOGGER.warn("无法发送指令，TBOX连接已断开");
        }
    }
    
    /**
     * 关闭连接
     */
    private void closeConnection() {
        running = false;
        String clientId = clientSocket.getRemoteSocketAddress().toString();
        
        try {
            if (reader != null) reader.close();
            if (writer != null) writer.close();
            if (clientSocket != null && !clientSocket.isClosed()) {
                clientSocket.close();
            }
            
            // 从管理器中移除
            TBOX_CLIENTS.remove(clientId);
            
            LOGGER.info("TBOX客户端连接已关闭: {}", clientId);
            
        } catch (IOException e) {
            LOGGER.error("关闭TBOX客户端连接时发生错误: {}", e.getMessage(), e);
        }
    }
    
    /**
     * 获取所有连接的TBOX客户端
     */
    public static ConcurrentHashMap<String, TcpClientHandler> getTboxClients() {
        return TBOX_CLIENTS;
    }
    
    /**
     * 向所有TBOX客户端发送指令
     */
    public static void sendCommandToAllTbox(String command) {
        TBOX_CLIENTS.values().forEach(handler -> handler.sendControlCommand(command));
    }
    
    /**
     * 向指定TBOX客户端发送指令
     */
    public static void sendCommandToTbox(String clientId, String command) {
        TcpClientHandler handler = TBOX_CLIENTS.get(clientId);
        if (handler != null) {
            handler.sendControlCommand(command);
        } else {
            LOGGER.warn("未找到指定的TBOX客户端: {}", clientId);
        }
    }
}

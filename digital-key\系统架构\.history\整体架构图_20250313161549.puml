@startuml
skinparam componentStyle rectangle

package "数字钥匙服务 Digital Key Server" {
    package "钥匙业务集群" {
        component "外部服务对接" as ExtService
        component "生产信息同步" as ProdSync
        component "售后信息同步" as AfterSync
        component "人车关系校验" as RelVerify
        component "定位标定服务" as LocService
        component "..."
    }
    
    package "钥匙业务" {
        component "车主钥匙开通/注销" as KeyMgmt
        component "钥匙分享/终止" as ShareKey
        component "钥匙冻结/解冻" as FreezeKey
        component "可信时钟服务" as TimeService
        component "..."
    }
    
    package "钥匙工厂" {
        component "多协议钥匙生成" as KeyGen
        component "钥匙加密" as KeyEnc
        component "远程服务" as RemoteServ
        component "远程控制" as RemoteCtrl
        component "SOTA" as SOTA
        component "..."
    }
    
    package "运维/运营集群" {
        component "监控系统" as Monitor
        component "日志系统" as LogSystem
        component "配置管理系统" as ConfigMgmt
        component "发布系统" as ReleaseSystem
        component "运营管理系统" as OperMgmt
    }
    
    database "业务数据库" as DB
    database "密码机/HSM" as HSM
    database "PKI/CA" as PKICA
    database "钥匙追踪服务/KTS (Key Track Server)" as KTS
}

ExtService --> ProdSync
ProdSync --> AfterSync
AfterSync --> RelVerify
RelVerify --> LocService

KeyMgmt --> ShareKey
ShareKey --> FreezeKey
FreezeKey --> TimeService

KeyGen --> KeyEnc
KeyEnc --> RemoteServ
RemoteServ --> RemoteCtrl
RemoteCtrl --> SOTA

Monitor --> LogSystem
LogSystem --> ConfigMgmt
ConfigMgmt --> ReleaseSystem
ReleaseSystem --> OperMgmt

DB -down- HSM
HSM -down- PKICA
PKICA -down- KTS

@enduml
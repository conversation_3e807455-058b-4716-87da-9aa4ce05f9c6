/**
 * 图片加载处理脚本
 * 用于处理带有data-html-src属性的图片，实现点击图片可以查看详细内容
 */
document.addEventListener("DOMContentLoaded", function() {
    // 选择所有带有data-html-src属性的图片
    var images = document.querySelectorAll("img[data-html-src]");
    
    // 为每个图片设置点击事件
    images.forEach(function(img) {
        // 创建一个占位SVG图
        var title = img.alt || "点击查看详细内容";
        var bgColor = "#f8f9fa";
        var textColor = "#3498db";
        
        // 设置不同类型图表的颜色
        if (img.alt.includes("配对")) {
            bgColor = "#eefbf5";
            textColor = "#2ecc71";
        } else if (img.alt.includes("无感")) {
            bgColor = "#f5eef8";
            textColor = "#9b59b6";
        }
        
        var svgImage = 'data:image/svg+xml;charset=utf-8,' + encodeURIComponent('<svg xmlns="http://www.w3.org/2000/svg" width="800" height="400" viewBox="0 0 800 400"><rect width="800" height="400" fill="' + bgColor + '"/><text x="400" y="200" text-anchor="middle" font-family="Arial, sans-serif" font-size="20" fill="' + textColor + '">' + title + ' - 点击查看详细内容</text></svg>');
        
        // 设置图片源为SVG
        img.src = svgImage;
        
        // 添加鼠标悬停时的提示
        img.title = "点击查看详细内容";
        
        // 设置鼠标样式为手型，表示可点击
        img.style.cursor = "pointer";
        
        // 添加点击事件处理器
        img.addEventListener("click", function() {
            // 在新窗口打开HTML文件
            window.open(this.getAttribute("data-html-src"), "_blank");
        });
    });
    
    // 淡入动画效果
    var fadeInElements = document.querySelectorAll(".fade-in");
    
    function checkVisibility() {
        fadeInElements.forEach(function(element) {
            var rect = element.getBoundingClientRect();
            var windowHeight = window.innerHeight;
            
            // 当元素进入视口时应用动画
            if (rect.top < windowHeight * 0.9) {
                element.classList.add("visible");
            }
        });
    }
    
    // 设置初始状态
    fadeInElements.forEach(function(element) {
        element.style.opacity = "0";
        element.style.transform = "translateY(20px)";
        element.style.transition = "opacity 0.8s ease, transform 0.8s ease";
    });
    
    // 添加可见时的类
    document.addEventListener("scroll", checkVisibility);
    window.addEventListener("resize", checkVisibility);
    
    // 检查初始可见性
    checkVisibility();
}); 
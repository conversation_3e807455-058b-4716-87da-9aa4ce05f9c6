<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.dk.mapper.DkVehicleInfoMapper">
    
    <resultMap type="DkVehicleInfo" id="DkVehicleInfoResult">
        <result property="vehicleId"    column="vehicle_id"    />
        <result property="vinCode"    column="vin_code"    />
        <result property="brand"    column="brand"    />
        <result property="model"    column="model"    />
        <result property="color"    column="color"    />
        <result property="licensePlate"    column="license_plate"    />
        <result property="vehicleType"    column="vehicle_type"    />
        <result property="location"    column="location"    />
        <result property="status"    column="status"    />
        <result property="mileage"    column="mileage"    />
        <result property="fuelLevel"    column="fuel_level"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="remark"    column="remark"    />
    </resultMap>

    <sql id="selectDkVehicleInfoVo">
        select vehicle_id, vin_code, brand, model, color, license_plate, vehicle_type, location, status, mileage, fuel_level, create_by, create_time, update_by, update_time, remark from dk_vehicle_info
    </sql>

    <select id="selectDkVehicleInfoList" parameterType="DkVehicleInfo" resultMap="DkVehicleInfoResult">
        <include refid="selectDkVehicleInfoVo"/>
        <where>  
            <if test="vinCode != null  and vinCode != ''"> and vin_code like concat('%', #{vinCode}, '%')</if>
            <if test="brand != null  and brand != ''"> and brand like concat('%', #{brand}, '%')</if>
            <if test="model != null  and model != ''"> and model like concat('%', #{model}, '%')</if>
            <if test="color != null  and color != ''"> and color = #{color}</if>
            <if test="licensePlate != null  and licensePlate != ''"> and license_plate like concat('%', #{licensePlate}, '%')</if>
            <if test="vehicleType != null  and vehicleType != ''"> and vehicle_type = #{vehicleType}</if>
            <if test="location != null  and location != ''"> and location like concat('%', #{location}, '%')</if>
            <if test="status != null  and status != ''"> and status = #{status}</if>
        </where>
        order by create_time desc
    </select>
    
    <select id="selectDkVehicleInfoByVehicleId" parameterType="Long" resultMap="DkVehicleInfoResult">
        <include refid="selectDkVehicleInfoVo"/>
        where vehicle_id = #{vehicleId}
    </select>
    
    <select id="selectDkVehicleInfoByVinCode" parameterType="String" resultMap="DkVehicleInfoResult">
        <include refid="selectDkVehicleInfoVo"/>
        where vin_code = #{vinCode}
    </select>
    
    <select id="selectDkVehicleInfoByStatus" parameterType="String" resultMap="DkVehicleInfoResult">
        <include refid="selectDkVehicleInfoVo"/>
        where status = #{status}
        order by create_time desc
    </select>
    
    <select id="selectDkVehicleInfoByBrand" parameterType="String" resultMap="DkVehicleInfoResult">
        <include refid="selectDkVehicleInfoVo"/>
        where brand = #{brand}
        order by create_time desc
    </select>
    
    <select id="selectDkVehicleInfoByModel" parameterType="String" resultMap="DkVehicleInfoResult">
        <include refid="selectDkVehicleInfoVo"/>
        where model = #{model}
        order by create_time desc
    </select>
    
    <select id="countVehicles" resultType="int">
        select count(*) from dk_vehicle_info
    </select>
    
    <select id="countVehiclesByStatus" parameterType="String" resultType="int">
        select count(*) from dk_vehicle_info where status = #{status}
    </select>

    <insert id="insertDkVehicleInfo" parameterType="DkVehicleInfo" useGeneratedKeys="true" keyProperty="vehicleId">
        insert into dk_vehicle_info
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="vinCode != null">vin_code,</if>
            <if test="brand != null">brand,</if>
            <if test="model != null">model,</if>
            <if test="color != null">color,</if>
            <if test="licensePlate != null">license_plate,</if>
            <if test="vehicleType != null">vehicle_type,</if>
            <if test="location != null">location,</if>
            <if test="status != null">status,</if>
            <if test="mileage != null">mileage,</if>
            <if test="fuelLevel != null">fuel_level,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="remark != null">remark,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="vinCode != null">#{vinCode},</if>
            <if test="brand != null">#{brand},</if>
            <if test="model != null">#{model},</if>
            <if test="color != null">#{color},</if>
            <if test="licensePlate != null">#{licensePlate},</if>
            <if test="vehicleType != null">#{vehicleType},</if>
            <if test="location != null">#{location},</if>
            <if test="status != null">#{status},</if>
            <if test="mileage != null">#{mileage},</if>
            <if test="fuelLevel != null">#{fuelLevel},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="remark != null">#{remark},</if>
         </trim>
    </insert>

    <update id="updateDkVehicleInfo" parameterType="DkVehicleInfo">
        update dk_vehicle_info
        <trim prefix="SET" suffixOverrides=",">
            <if test="vinCode != null">vin_code = #{vinCode},</if>
            <if test="brand != null">brand = #{brand},</if>
            <if test="model != null">model = #{model},</if>
            <if test="color != null">color = #{color},</if>
            <if test="licensePlate != null">license_plate = #{licensePlate},</if>
            <if test="vehicleType != null">vehicle_type = #{vehicleType},</if>
            <if test="location != null">location = #{location},</if>
            <if test="status != null">status = #{status},</if>
            <if test="mileage != null">mileage = #{mileage},</if>
            <if test="fuelLevel != null">fuel_level = #{fuelLevel},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="remark != null">remark = #{remark},</if>
        </trim>
        where vehicle_id = #{vehicleId}
    </update>
    
    <update id="updateVehicleStatus">
        update dk_vehicle_info set status = #{status}, update_time = now() where vehicle_id = #{vehicleId}
    </update>
    
    <update id="batchUpdateVehicleStatus">
        update dk_vehicle_info set status = #{status}, update_time = now() 
        where vehicle_id in 
        <foreach item="vehicleId" collection="vehicleIds" open="(" separator="," close=")">
            #{vehicleId}
        </foreach>
    </update>

    <delete id="deleteDkVehicleInfoByVehicleId" parameterType="Long">
        delete from dk_vehicle_info where vehicle_id = #{vehicleId}
    </delete>

    <delete id="deleteDkVehicleInfoByVehicleIds" parameterType="String">
        delete from dk_vehicle_info where vehicle_id in 
        <foreach item="vehicleId" collection="array" open="(" separator="," close=")">
            #{vehicleId}
        </foreach>
    </delete>
</mapper>

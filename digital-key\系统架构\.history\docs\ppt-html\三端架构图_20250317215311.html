<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>数字钥匙三端架构图</title>
    <script src="https://html2canvas.hertzen.com/dist/html2canvas.min.js"></script>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
            font-family: 'PingFang SC', 'Microsoft YaHei', sans-serif;
        }
        
        body {
            background: #f5f5f5;
            display: flex;
            justify-content: center;
            align-items: center;
            min-height: 100vh;
            padding: 20px;
        }
        
        .ppt-container {
            position: relative;
            width: 1920px;
            height: 1080px;
            background-color: white;
            box-shadow: 0 0 30px rgba(0, 0, 0, 0.1);
            border: 2px dashed #ddd;
        }
        
        .ppt-label {
            position: absolute;
            top: -40px;
            left: 0;
            background-color: #555;
            color: white;
            padding: 5px 15px;
            border-radius: 4px;
            font-size: 16px;
        }
        
        .copy-button, .download-button {
            position: absolute;
            top: 20px;
            padding: 12px 25px;
            border-radius: 30px;
            font-size: 18px;
            cursor: pointer;
            z-index: 1000;
            box-shadow: 0 4px 10px rgba(0, 0, 0, 0.2);
            display: flex;
            align-items: center;
            gap: 10px;
            transition: all 0.3s ease;
            border: none;
            color: white;
        }
        
        .copy-button {
            right: 20px;
            background: linear-gradient(135deg, #4a6cf7, #2952cc);
        }
        
        .download-button {
            right: 230px;
            background: linear-gradient(135deg, #36b37e, #2a8c62);
        }
        
        .resolution-selector {
            position: absolute;
            top: 70px;
            right: 230px;
            display: flex;
            align-items: center;
            background: rgba(255, 255, 255, 0.95);
            padding: 8px 15px;
            border-radius: 30px;
            box-shadow: 0 4px 10px rgba(0, 0, 0, 0.1);
            z-index: 1000;
        }
        
        .copy-icon, .download-icon {
            width: 20px;
            height: 20px;
            background-size: contain;
            background-repeat: no-repeat;
        }
        
        .resolution-label {
            margin-right: 10px;
            font-size: 14px;
            color: #555;
        }
        
        .resolution-btn {
            border: none;
            background: #f0f0f0;
            padding: 5px 10px;
            margin: 0 3px;
            border-radius: 15px;
            font-size: 14px;
            cursor: pointer;
            transition: all 0.2s ease;
            color: #555;
        }
        
        .resolution-btn.active {
            background: linear-gradient(135deg, #4a6cf7, #2952cc);
            color: white;
        }
        
        /* 内容区域样式 */
        .slide {
            width: 100%;
            height: 100%;
            padding: 60px;
            position: relative;
            overflow: hidden;
            background: linear-gradient(145deg, #fdfdfd, #f8f9fc);
        }
        
        /* 标题样式 */
        .header {
            text-align: center;
            margin-bottom: 40px;
        }
        
        .header h1 {
            font-size: 42px;
            color: #1a365d;
            margin-bottom: 15px;
            font-weight: 600;
            letter-spacing: 1px;
        }
        
        .header .subtitle {
            font-size: 22px;
            color: #4a5568;
            font-weight: 400;
        }
        
        /* 架构图容器 */
        .architecture-container {
            position: relative;
            width: 100%;
            height: 750px;
            margin: 0 auto;
        }
        
        /* 系统模块样式 */
        .system-module {
            position: absolute;
            border-radius: 16px;
            overflow: hidden;
            box-shadow: 0 15px 35px rgba(0, 0, 0, 0.08);
            background-color: #fff;
            transition: all 0.4s ease;
            border: 1px solid rgba(0, 0, 0, 0.05);
        }
        
        .system-module:hover {
            transform: translateY(-5px);
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
        }
        
        /* 模块标题栏 */
        .module-header {
            padding: 18px 24px;
            color: white;
            font-weight: 600;
            font-size: 22px;
            display: flex;
            align-items: center;
            text-shadow: 0 1px 2px rgba(0, 0, 0, 0.15);
        }
        
        .module-header i {
            margin-right: 12px;
            font-size: 24px;
        }
        
        /* 模块内容区 */
        .module-content {
            padding: 30px;
            background: linear-gradient(180deg, rgba(255,255,255,0.9) 0%, rgba(250,252,255,0.9) 100%);
        }
        
        /* 功能核心分组 */
        .function-group {
            margin-bottom: 25px;
        }
        
        .function-group-title {
            font-size: 18px;
            font-weight: 600;
            color: #2d3748;
            margin-bottom: 16px;
            border-left: 5px solid;
            padding-left: 14px;
            line-height: 1.4;
        }
        
        .cloud-group-title {
            border-color: #4a6cf7;
        }
        
        .mobile-group-title {
            border-color: #36b37e;
        }
        
        .car-group-title {
            border-color: #ed5c57;
        }
        
        /* 模块项目 */
        .module-item {
            background-color: #f8fafd;
            border: 1px solid #e9ecef;
            padding: 15px 18px;
            border-radius: 10px;
            margin-bottom: 14px;
            font-size: 15px;
            color: #2d3748;
            position: relative;
            display: flex;
            align-items: center;
            transition: all 0.3s ease;
            box-shadow: 0 2px 5px rgba(0,0,0,0.02);
        }
        
        .module-item:hover {
            background-color: #f0f5ff;
            transform: translateX(5px);
            box-shadow: 0 5px 15px rgba(74, 108, 247, 0.1);
            border-color: #d1dcfa;
        }
        
        .module-item-icon {
            width: 28px;
            height: 28px;
            min-width: 28px;
            margin-right: 14px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 50%;
            color: white;
            font-size: 14px;
            font-weight: bold;
            box-shadow: 0 4px 6px rgba(0,0,0,0.1);
        }
        
        .cloud-item-icon {
            background: linear-gradient(135deg, #4a6cf7, #2952cc);
        }
        
        .mobile-item-icon {
            background: linear-gradient(135deg, #36b37e, #2a8c62);
        }
        
        .car-item-icon {
            background: linear-gradient(135deg, #ed5c57, #c93b36);
        }
        
        /* 平台模块位置尺寸 */
        .cloud-platform {
            width: 1200px;
            height: 240px;
            top: 20px;
            left: 50%;
            transform: translateX(-50%);
        }
        
        .cloud-platform .module-header {
            background: linear-gradient(135deg, #4a6cf7, #2952cc);
        }
        
        .mobile-platform {
            width: 480px;
            height: 340px;
            top: 320px;
            left: 50%;
            transform: translateX(-50%);
        }
        
        .mobile-platform .module-header {
            background: linear-gradient(135deg, #36b37e, #2a8c62);
        }
        
        .car-platform {
            width: 1200px;
            height: 240px;
            bottom: 20px;
            left: 50%;
            transform: translateX(-50%);
        }
        
        .car-platform .module-header {
            background: linear-gradient(135deg, #ed5c57, #c93b36);
        }
        
        /* 连接线和标签容器 */
        .connections {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            pointer-events: none;
        }
        
        /* 连接标签 */
        .connection-label {
            position: absolute;
            background-color: white;
            padding: 8px 16px;
            border-radius: 20px;
            font-size: 14px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
            z-index: 5;
            white-space: nowrap;
            border: 1px solid;
            font-weight: 500;
            transition: all 0.3s ease;
        }
        
        .connection-label:hover {
            transform: translateY(-3px);
            box-shadow: 0 8px 20px rgba(0,0,0,0.15);
        }
        
        /* 标签颜色样式 */
        .label-cloud {
            color: #4a6cf7;
            border-color: rgba(74, 108, 247, 0.3);
        }
        
        .label-mobile {
            color: #36b37e;
            border-color: rgba(54, 179, 126, 0.3);
        }
        
        .label-car {
            color: #ed5c57;
            border-color: rgba(237, 92, 87, 0.3);
        }
        
        /* 距离标签 */
        .distance-tag {
            position: absolute;
            width: 36px;
            height: 36px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            font-size: 14px;
            color: white;
            z-index: 10;
            box-shadow: 0 5px 15px rgba(0,0,0,0.2);
            transition: all 0.3s ease;
        }
        
        .distance-tag:hover {
            transform: scale(1.1);
        }
        
        .tag-near {
            background: linear-gradient(135deg, #36b37e, #2a8c62);
        }
        
        .tag-far {
            background: linear-gradient(135deg, #4a6cf7, #2952cc);
        }
        
        /* 底部说明 */
        .footer {
            position: absolute;
            bottom: 20px;
            width: 100%;
            text-align: center;
            font-size: 15px;
            color: #718096;
            left: 0;
            font-weight: 500;
        }
        
        /* 提示框 */
        .toast {
            position: fixed;
            top: 50px;
            left: 50%;
            transform: translateX(-50%);
            background-color: rgba(0, 0, 0, 0.8);
            color: white;
            padding: 12px 25px;
            border-radius: 30px;
            font-size: 16px;
            z-index: 2000;
            display: none;
            animation: fadeInOut 2s ease;
        }
        
        @keyframes fadeInOut {
            0% { opacity: 0; }
            20% { opacity: 1; }
            80% { opacity: 1; }
            100% { opacity: 0; }
        }
        
        /* 添加连线动画 */
        @keyframes dashOffset {
            from {
                stroke-dashoffset: 50;
            }
            to {
                stroke-dashoffset: 0;
            }
        }
        
        .animated-path {
            animation: dashOffset 3s linear infinite;
        }
    </style>
</head>
<body>
    <div class="ppt-container" id="pptContainer">
        <div class="ppt-label">PPT尺寸(1920×1080) - 透明背景</div>
        <button class="copy-button" id="copyButton">
            <span class="copy-icon"></span>复制为PPT图片
        </button>
        <button class="download-button" id="downloadButton">
            <span class="download-icon"></span>下载透明PNG
        </button>
        
        <div class="resolution-selector" id="resolutionSelector">
            <span class="resolution-label">清晰度:</span>
            <button class="resolution-btn" data-scale="1">标准</button>
            <button class="resolution-btn active" data-scale="2">高清</button>
            <button class="resolution-btn" data-scale="3">超清</button>
        </div>
        
        <div class="slide" id="slide">
            <div class="header">
                <h1>数字钥匙三端架构图</h1>
                <div class="subtitle">车辆数字钥匙系统技术架构与通信流程</div>
            </div>
            
            <div class="architecture-container" id="architectureContainer">
                <!-- 云平台 -->
                <div class="system-module cloud-platform">
                    <div class="module-header">
                        <i>🌩️</i> 钥匙云平台 (管理与服务层)
                    </div>
                    <div class="module-content">
                        <!-- 密钥管理与安全 -->
                        <div class="function-group">
                            <div class="function-group-title cloud-group-title">密钥管理与安全</div>
                            <div class="module-item">
                                <div class="module-item-icon cloud-item-icon">1</div>
                                <span>钥匙生命周期管理</span>
                            </div>
                            <div class="module-item">
                                <div class="module-item-icon cloud-item-icon">2</div>
                                <span>密钥管理系统</span>
                            </div>
                        </div>
                        
                        <!-- 接口和通信服务 -->
                        <div class="function-group">
                            <div class="function-group-title cloud-group-title">接口和通信服务</div>
                            <div class="module-item">
                                <div class="module-item-icon cloud-item-icon">4</div>
                                <span>统一接口服务</span>
                            </div>
                            <div class="module-item">
                                <div class="module-item-icon cloud-item-icon">5</div>
                                <span>安全通信通道</span>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- 手机端 -->
                <div class="system-module mobile-platform">
                    <div class="module-header">
                        <i>📱</i> 手机端 (用户交互层)
                    </div>
                    <div class="module-content">
                        <div class="function-group">
                            <div class="function-group-title mobile-group-title">核心功能</div>
                            <div class="module-item">
                                <div class="module-item-icon mobile-item-icon">1</div>
                                <span>钥匙与车辆管理</span>
                            </div>
                            <div class="module-item">
                                <div class="module-item-icon mobile-item-icon">2</div>
                                <span>蓝牙近场通信</span>
                            </div>
                        </div>
                        
                        <div class="function-group">
                            <div class="function-group-title mobile-group-title">安全保障</div>
                            <div class="module-item">
                                <div class="module-item-icon mobile-item-icon">3</div>
                                <span>安全存储模块</span>
                            </div>
                            <div class="module-item">
                                <div class="module-item-icon mobile-item-icon">4</div>
                                <span>暗号交换机制</span>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- 车端 -->
                <div class="system-module car-platform">
                    <div class="module-header">
                        <i>🚗</i> 车端 (执行与控制层)
                    </div>
                    <div class="module-content">
                        <!-- 验证与执行 -->
                        <div class="function-group">
                            <div class="function-group-title car-group-title">验证与执行</div>
                            <div class="module-item">
                                <div class="module-item-icon car-item-icon">1</div>
                                <span>钥匙验证模块</span>
                            </div>
                            <div class="module-item">
                                <div class="module-item-icon car-item-icon">2</div>
                                <span>指令执行控制</span>
                            </div>
                        </div>
                        
                        <!-- 通信与安全 -->
                        <div class="function-group">
                            <div class="function-group-title car-group-title">通信与安全</div>
                            <div class="module-item">
                                <div class="module-item-icon car-item-icon">4</div>
                                <span>蓝牙通信模块</span>
                            </div>
                            <div class="module-item">
                                <div class="module-item-icon car-item-icon">5</div>
                                <span>远程通信模块</span>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- 连接线和标签将在这里通过SVG绘制 -->
                <div class="connections" id="connections"></div>
            </div>
            
            <div class="footer">
                数字钥匙三端架构 - 安全高效的车辆接入控制系统
            </div>
        </div>
    </div>
    
    <div class="toast" id="toast">操作成功！</div>
    
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const copyButton = document.getElementById('copyButton');
            const downloadButton = document.getElementById('downloadButton');
            const toast = document.getElementById('toast');
            const slide = document.getElementById('slide');
            const resolutionBtns = document.querySelectorAll('.resolution-btn');
            const connectionsContainer = document.getElementById('connections');
            
            // 创建SVG元素
            const svg = document.createElementNS('http://www.w3.org/2000/svg', 'svg');
            svg.setAttribute('width', '100%');
            svg.setAttribute('height', '100%');
            svg.style.position = 'absolute';
            svg.style.top = '0';
            svg.style.left = '0';
            svg.style.zIndex = '1';
            
            // 创建箭头定义
            const defs = document.createElementNS('http://www.w3.org/2000/svg', 'defs');
            
            // 创建渐变
            function createGradient(id, color1, color2) {
                const gradient = document.createElementNS('http://www.w3.org/2000/svg', 'linearGradient');
                gradient.setAttribute('id', id);
                gradient.setAttribute('x1', '0%');
                gradient.setAttribute('y1', '0%');
                gradient.setAttribute('x2', '100%');
                gradient.setAttribute('y2', '100%');
                
                const stop1 = document.createElementNS('http://www.w3.org/2000/svg', 'stop');
                stop1.setAttribute('offset', '0%');
                stop1.setAttribute('stop-color', color1);
                
                const stop2 = document.createElementNS('http://www.w3.org/2000/svg', 'stop');
                stop2.setAttribute('offset', '100%');
                stop2.setAttribute('stop-color', color2);
                
                gradient.appendChild(stop1);
                gradient.appendChild(stop2);
                defs.appendChild(gradient);
                
                return `url(#${id})`;
            }
            
            // 创建箭头标记
            function createMarker(id, fill) {
                const marker = document.createElementNS('http://www.w3.org/2000/svg', 'marker');
                marker.setAttribute('id', id);
                marker.setAttribute('viewBox', '0 0 10 10');
                marker.setAttribute('refX', '7');
                marker.setAttribute('refY', '5');
                marker.setAttribute('markerWidth', '8');
                marker.setAttribute('markerHeight', '8');
                marker.setAttribute('orient', 'auto-start-reverse');
                
                const polygon = document.createElementNS('http://www.w3.org/2000/svg', 'polygon');
                polygon.setAttribute('points', '0,0 10,5 0,10 2,5');
                polygon.setAttribute('fill', fill);
                marker.appendChild(polygon);
                defs.appendChild(marker);
                
                return `url(#${id})`;
            }
            
            // 创建渐变
            const cloudGradient = createGradient('cloud-gradient', '#4a6cf7', '#2952cc');
            const mobileGradient = createGradient('mobile-gradient', '#36b37e', '#2a8c62');
            const carGradient = createGradient('car-gradient', '#ed5c57', '#c93b36');
            
            // 创建箭头
            const cloudMarker = createMarker('arrow-cloud', '#4a6cf7');
            const mobileMarker = createMarker('arrow-mobile', '#36b37e');
            const carMarker = createMarker('arrow-car', '#ed5c57');
            
            svg.appendChild(defs);
            
            // 创建连接线
            
            // 云平台到手机端 - 自然曲线
            const cloudToMobile = document.createElementNS('http://www.w3.org/2000/svg', 'path');
            cloudToMobile.setAttribute('d', 'M 720 260 C 720 280, 720 300, 720 320');
            cloudToMobile.setAttribute('stroke', cloudGradient);
            cloudToMobile.setAttribute('stroke-width', '3');
            cloudToMobile.setAttribute('fill', 'none');
            cloudToMobile.setAttribute('marker-end', cloudMarker);
            cloudToMobile.classList.add('animated-path');
            svg.appendChild(cloudToMobile);
            
            // 手机端到云平台 - 自然曲线
            const mobileToCloud = document.createElementNS('http://www.w3.org/2000/svg', 'path');
            mobileToCloud.setAttribute('d', 'M 960 320 C 960 290, 960 270, 960 260');
            mobileToCloud.setAttribute('stroke', mobileGradient);
            mobileToCloud.setAttribute('stroke-width', '3');
            mobileToCloud.setAttribute('fill', 'none');
            mobileToCloud.setAttribute('marker-end', mobileMarker);
            mobileToCloud.classList.add('animated-path');
            svg.appendChild(mobileToCloud);
            
            // 手机端到车端 - 自然曲线
            const mobileToCar = document.createElementNS('http://www.w3.org/2000/svg', 'path');
            mobileToCar.setAttribute('d', 'M 720 660 C 720 620, 720 580, 720 530');
            mobileToCar.setAttribute('stroke', mobileGradient);
            mobileToCar.setAttribute('stroke-width', '3');
            mobileToCar.setAttribute('fill', 'none');
            mobileToCar.setAttribute('marker-end', mobileMarker);
            mobileToCar.classList.add('animated-path');
            svg.appendChild(mobileToCar);
            
            // 车端到手机端 - 自然曲线
            const carToMobile = document.createElementNS('http://www.w3.org/2000/svg', 'path');
            carToMobile.setAttribute('d', 'M 960 530 C 960 570, 960 620, 960 660');
            carToMobile.setAttribute('stroke', carGradient);
            carToMobile.setAttribute('stroke-width', '3');
            carToMobile.setAttribute('fill', 'none');
            carToMobile.setAttribute('marker-end', carMarker);
            carToMobile.classList.add('animated-path');
            svg.appendChild(carToMobile);
            
            // 云平台到车端 - 优美的弧形路径
            const cloudToCar = document.createElementNS('http://www.w3.org/2000/svg', 'path');
            cloudToCar.setAttribute('d', 'M 420 260 C 250 350, 200 450, 420 660');
            cloudToCar.setAttribute('stroke', cloudGradient);
            cloudToCar.setAttribute('stroke-width', '3');
            cloudToCar.setAttribute('stroke-dasharray', '12, 6');
            cloudToCar.setAttribute('fill', 'none');
            cloudToCar.setAttribute('marker-end', cloudMarker);
            svg.appendChild(cloudToCar);
            
            // 车端到云平台 - 优美的弧形路径
            const carToCloud = document.createElementNS('http://www.w3.org/2000/svg', 'path');
            carToCloud.setAttribute('d', 'M 1280 660 C 1450 450, 1500 350, 1280 260');
            carToCloud.setAttribute('stroke', carGradient);
            carToCloud.setAttribute('stroke-width', '3');
            carToCloud.setAttribute('stroke-dasharray', '12, 6');
            carToCloud.setAttribute('fill', 'none');
            carToCloud.setAttribute('marker-end', carMarker);
            svg.appendChild(carToCloud);
            
            connectionsContainer.appendChild(svg);
            
            // 添加连接标签
            function createLabel(text, className, left, top, transform = '') {
                const label = document.createElement('div');
                label.className = `connection-label ${className}`;
                label.textContent = text;
                label.style.left = `${left}px`;
                label.style.top = `${top}px`;
                if (transform) {
                    label.style.transform = transform;
                }
                connectionsContainer.appendChild(label);
                return label;
            }
            
            // 创建距离标签
            function createDistanceTag(text, className, left, top) {
                const tag = document.createElement('div');
                tag.className = `distance-tag ${className}`;
                tag.textContent = text;
                tag.style.left = `${left}px`;
                tag.style.top = `${top}px`;
                connectionsContainer.appendChild(tag);
                return tag;
            }
            
            // 添加垂直连接标签
            createLabel('钥匙授权/认证', 'label-cloud', 620, 280);
            createLabel('状态同步/更新', 'label-mobile', 970, 280);
            
            createLabel('蓝牙控制指令', 'label-mobile', 620, 590);
            createLabel('状态反馈/确认', 'label-car', 970, 590);
            
            // 添加弧形连接标签
            createLabel('远程控制/OTA更新', 'label-cloud', 300, 400, 'rotate(-25deg)');
            createLabel('状态上报/安全校验', 'label-car', 1370, 400, 'rotate(25deg)');
            
            // 添加距离标签
            createDistanceTag('近', 'tag-near', 600, 470);
            createDistanceTag('远', 'tag-far', 380, 400);
            
            // 默认清晰度为2x (高清)
            let currentScale = 2;
            
            // 清晰度按钮点击事件
            resolutionBtns.forEach(btn => {
                btn.addEventListener('click', function() {
                    // 移除所有按钮的active类
                    resolutionBtns.forEach(b => b.classList.remove('active'));
                    // 给当前按钮添加active类
                    this.classList.add('active');
                    // 更新当前清晰度
                    currentScale = parseFloat(this.getAttribute('data-scale'));
                    
                    // 显示提示
                    showToast(`清晰度已设置为${this.textContent}模式`);
                });
            });
            
            // 显示提示消息
            function showToast(message) {
                toast.textContent = message;
                toast.style.display = 'block';
                setTimeout(function() {
                    toast.style.display = 'none';
                }, 2000);
            }
            
            // 生成透明背景图片
            function generateImage() {
                return html2canvas(slide, {
                    scale: currentScale, // 使用用户选择的清晰度
                    useCORS: true,
                    allowTaint: true,
                    backgroundColor: null // 设置为null以支持透明背景
                });
            }
            
            // 复制按钮功能
            copyButton.addEventListener('click', function() {
                showToast("正在处理图片，请稍候...");
                
                generateImage().then(function(canvas) {
                    try {
                        canvas.toBlob(function(blob) {
                            try {
                                const item = new ClipboardItem({ 'image/png': blob });
                                navigator.clipboard.write([item]).then(function() {
                                    showToast("已复制到剪贴板！现在可以粘贴到PPT中");
                                }).catch(function(err) {
                                    showToast("自动复制失败，请使用下载按钮获取图片");
                                    console.error("剪贴板API错误:", err);
                                });
                            } catch (e) {
                                showToast("您的浏览器不支持复制图片，请使用下载按钮");
                                console.error("ClipboardItem错误:", e);
                            }
                        });
                    } catch (e) {
                        showToast("复制功能不可用，请使用下载图片功能");
                        console.error("toBlob错误:", e);
                    }
                }).catch(function(err) {
                    showToast("生成图片失败，请尝试下载或截图");
                    console.error("html2canvas错误:", err);
                });
            });
            
            // 下载按钮功能
            downloadButton.addEventListener('click', function () {
                showToast(`正在生成${currentScale}x清晰度透明PNG图片，请稍候...`);

                generateImage().then(function (canvas) {
                    // 将Canvas转换为PNG并下载
                    const imageUrl = canvas.toDataURL('image/png');
                    const link = document.createElement('a');

                    // 设置文件名
                    link.download = `数字钥匙三端架构图-${currentScale}x清晰度.png`;
                    link.href = imageUrl;
                    link.click();

                    showToast(`${currentScale}x清晰度透明背景PNG已下载`);
                }).catch(function (err) {
                    showToast("生成图片失败，请尝试截图");
                    console.error("Error:", err);
                });
            });
        });
    </script>
</body>
</html> 
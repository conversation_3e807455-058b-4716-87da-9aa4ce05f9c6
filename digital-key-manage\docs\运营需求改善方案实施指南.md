# 数字钥匙管理系统运营需求改善方案实施指南

## 概述

本文档详细说明了基于现有RuoYi框架的数字钥匙管理系统运营需求改善方案的实施步骤和技术细节。

## 需求分析

### 核心功能需求
1. **授权新用户使用车辆并下发数字钥匙** - 现有功能基本满足
2. **用户权限限制和恢复功能** - 新增功能
3. **删除用户功能** - 增强现有功能
4. **车辆状态查看和数字钥匙管理** - 现有功能基本满足

### 运营场景
- 用户驶出电子围栏或欠费时，运营人员可限制用户权限
- 问题解决后，运营人员可恢复用户权限
- 支持批量操作，提高运营效率
- 完整的操作日志和状态跟踪

## 实施步骤

### 1. 数据库表结构扩展

执行以下SQL脚本扩展`dk_vehicle_bluetooth_keys`表：

```sql
-- 添加用户权限状态字段
ALTER TABLE dk_vehicle_bluetooth_keys 
ADD COLUMN user_status CHAR(1) DEFAULT '0' COMMENT '用户状态（0正常 1限制）' AFTER bluetooth_perm_key_time;

-- 添加状态更新时间字段
ALTER TABLE dk_vehicle_bluetooth_keys 
ADD COLUMN status_update_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '状态更新时间' AFTER user_status;

-- 添加操作人员字段
ALTER TABLE dk_vehicle_bluetooth_keys 
ADD COLUMN operator_id BIGINT(20) DEFAULT NULL COMMENT '操作人员ID' AFTER status_update_time;

-- 添加备注字段
ALTER TABLE dk_vehicle_bluetooth_keys 
ADD COLUMN remark VARCHAR(500) DEFAULT '' COMMENT '备注信息' AFTER operator_id;
```

### 2. 后端代码修改

#### 2.1 实体类扩展
- 文件：`ruoyi-modules/ruoyi-system/src/main/java/com/ruoyi/dk/domain/DkVehicleBluetoothKeys.java`
- 新增字段：`userStatus`, `statusUpdateTime`, `operatorId`, `remark`
- 添加对应的getter/setter方法

#### 2.2 Mapper层扩展
- 文件：`ruoyi-modules/ruoyi-system/src/main/resources/mapper/dk/DkVehicleBluetoothKeysMapper.xml`
- 更新resultMap和SQL语句
- 新增权限管理相关的SQL方法

#### 2.3 Service层扩展
- 接口文件：`ruoyi-modules/ruoyi-system/src/main/java/com/ruoyi/dk/service/IDkVehicleBluetoothKeysService.java`
- 实现文件：`ruoyi-modules/ruoyi-system/src/main/java/com/ruoyi/dk/service/impl/DkVehicleBluetoothKeysServiceImpl.java`
- 新增方法：
  - `restrictUserPermission()` - 限制用户权限
  - `restoreUserPermission()` - 恢复用户权限
  - `batchRestrictUserPermission()` - 批量限制权限
  - `batchRestoreUserPermission()` - 批量恢复权限

#### 2.4 Controller层扩展
- 文件：`ruoyi-modules/ruoyi-system/src/main/java/com/ruoyi/dk/controller/DkVehicleBluetoothKeysController.java`
- 新增REST接口：
  - `PUT /restrict/{userId}` - 限制用户权限
  - `PUT /restore/{userId}` - 恢复用户权限
  - `PUT /batchRestrict` - 批量限制权限
  - `PUT /batchRestore` - 批量恢复权限

### 3. 前端代码修改

#### 3.1 API接口扩展
- 文件：`ruoyi-ui/src/api/dk/vehicleBluetoothKeys.js`
- 新增API函数：
  - `restrictUserPermission()` - 限制用户权限
  - `restoreUserPermission()` - 恢复用户权限
  - `batchRestrictUserPermission()` - 批量限制权限
  - `batchRestoreUserPermission()` - 批量恢复权限

#### 3.2 页面功能增强
- 文件：`ruoyi-ui/src/views/dk/vehicleBluetoothKeys/index.vue`
- 新增功能：
  - 用户状态列显示
  - 状态更新时间列
  - 权限限制/恢复按钮（根据状态动态显示）
  - 批量权限管理按钮
  - 操作确认对话框

### 4. 权限配置

需要在系统中配置以下权限：
- `dk:vehicleBluetoothKeys:restrict` - 限制用户权限
- `dk:vehicleBluetoothKeys:restore` - 恢复用户权限

### 5. 数据字典配置

执行以下SQL添加数据字典：

```sql
-- 创建数据字典类型
INSERT INTO sys_dict_type (dict_name, dict_type, status, create_by, create_time, remark) 
VALUES ('用户权限状态', 'dk_user_status', '0', 'admin', NOW(), '数字钥匙用户权限状态列表');

-- 创建数据字典项
INSERT INTO sys_dict_data (dict_sort, dict_label, dict_value, dict_type, css_class, list_class, is_default, status, create_by, create_time, remark) VALUES
(1, '正常', '0', 'dk_user_status', '', 'success', 'Y', '0', 'admin', NOW(), '用户权限正常状态'),
(2, '限制', '1', 'dk_user_status', '', 'danger', 'N', '0', 'admin', NOW(), '用户权限限制状态');
```

## 功能特性

### 1. 用户权限管理
- **权限限制**：运营人员可以限制用户的数字钥匙权限
- **权限恢复**：运营人员可以恢复被限制用户的权限
- **批量操作**：支持批量限制或恢复多个用户的权限
- **操作记录**：记录操作人员、操作时间和操作原因

### 2. 状态可视化
- **状态标识**：用不同颜色标识用户权限状态（正常/限制）
- **时间跟踪**：显示状态最后更新时间
- **动态按钮**：根据用户当前状态显示相应操作按钮

### 3. 操作安全性
- **权限控制**：基于RuoYi权限体系，确保只有授权人员可操作
- **操作确认**：所有权限变更操作都需要输入操作原因
- **审计日志**：完整记录所有操作历史

## 测试要点

### 1. 功能测试
- [ ] 限制用户权限功能正常
- [ ] 恢复用户权限功能正常
- [ ] 批量操作功能正常
- [ ] 状态显示正确
- [ ] 操作按钮根据状态正确显示/隐藏

### 2. 权限测试
- [ ] 无权限用户无法访问权限管理功能
- [ ] 有权限用户可以正常操作
- [ ] 操作日志正确记录

### 3. 数据完整性测试
- [ ] 状态更新正确保存到数据库
- [ ] 操作人员和时间正确记录
- [ ] 备注信息正确保存

## 部署注意事项

1. **数据库备份**：执行SQL脚本前请备份数据库
2. **权限配置**：部署后需要为相关角色分配新的权限
3. **缓存清理**：部署后清理相关缓存
4. **功能验证**：部署后进行完整的功能验证

## 后续扩展建议

1. **消息通知**：权限变更时向用户发送通知
2. **自动化规则**：基于业务规则自动限制/恢复权限
3. **统计报表**：权限管理操作的统计分析
4. **移动端适配**：支持移动端运营管理

## 总结

本改善方案基于现有RuoYi框架，采用最小化改动原则，在不破坏现有功能的基础上，新增了完整的用户权限管理功能，满足运营需求中的核心场景。方案具有良好的扩展性和维护性，为后续功能扩展奠定了基础。

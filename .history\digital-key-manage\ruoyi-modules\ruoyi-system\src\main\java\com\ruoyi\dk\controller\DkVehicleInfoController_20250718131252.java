package com.ruoyi.dk.controller;

import com.ruoyi.common.core.web.controller.BaseController;
import com.ruoyi.common.core.web.domain.AjaxResult;
import com.ruoyi.common.core.web.page.TableDataInfo;
import com.ruoyi.dk.domain.DkVehicleInfo;
import com.ruoyi.dk.service.IDkVehicleInfoService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.Map;

/**
 * 车辆信息管理Controller
 * 
 * <AUTHOR>
 * @date 2024-01-15
 */
@RestController
@RequestMapping("/dk/vehicleInfo")
public class DkVehicleInfoController extends BaseController {

    @Autowired(required = false)
    private IDkVehicleInfoService dkVehicleInfoService;

    /**
     * 查询车辆信息列表
     */
    @PreAuthorize("@ss.hasPermi('dk:vehicle:list')")
    @GetMapping("/list")
    public TableDataInfo list(DkVehicleInfo dkVehicleInfo) {
        try {
            if (dkVehicleInfoService != null) {
                startPage();
                List<DkVehicleInfo> list = dkVehicleInfoService.selectDkVehicleInfoList(dkVehicleInfo);
                return getDataTable(list);
            }
        } catch (Exception e) {
            logger.error("查询车辆信息列表失败", e);
        }
        
        // 返回模拟数据
        List<Map<String, Object>> mockList = new ArrayList<>();
        
        Map<String, Object> vehicle1 = new HashMap<>();
        vehicle1.put("vehicleId", 1L);
        vehicle1.put("vinCode", "LSGKB54U8EA123456");
        vehicle1.put("brand", "比亚迪");
        vehicle1.put("model", "秦PLUS DM-i");
        vehicle1.put("color", "珍珠白");
        vehicle1.put("licensePlate", "京A12345");
        vehicle1.put("vehicleType", "紧凑型轿车");
        vehicle1.put("location", "停车场A区01号");
        vehicle1.put("status", "0");
        vehicle1.put("mileage", 15230.5);
        vehicle1.put("fuelLevel", 85);
        vehicle1.put("remark", "");
        mockList.add(vehicle1);
        
        Map<String, Object> vehicle2 = new HashMap<>();
        vehicle2.put("vehicleId", 2L);
        vehicle2.put("vinCode", "LFV2A21K8E4123456");
        vehicle2.put("brand", "特斯拉");
        vehicle2.put("model", "Model 3");
        vehicle2.put("color", "珍珠白多涂层");
        vehicle2.put("licensePlate", "京A67890");
        vehicle2.put("vehicleType", "中型轿车");
        vehicle2.put("location", "停车场B区05号");
        vehicle2.put("status", "1");
        vehicle2.put("mileage", 8750.2);
        vehicle2.put("fuelLevel", 92);
        vehicle2.put("remark", "");
        mockList.add(vehicle2);
        
        Map<String, Object> vehicle3 = new HashMap<>();
        vehicle3.put("vehicleId", 3L);
        vehicle3.put("vinCode", "LHGCV1658EA123456");
        vehicle3.put("brand", "本田");
        vehicle3.put("model", "雅阁");
        vehicle3.put("color", "星月白");
        vehicle3.put("licensePlate", "京A11111");
        vehicle3.put("vehicleType", "中型轿车");
        vehicle3.put("location", "停车场A区15号");
        vehicle3.put("status", "2");
        vehicle3.put("mileage", 45680.8);
        vehicle3.put("fuelLevel", 25);
        vehicle3.put("remark", "定期保养中");
        mockList.add(vehicle3);
        
        TableDataInfo dataTable = new TableDataInfo();
        dataTable.setRows(mockList);
        dataTable.setTotal(mockList.size());
        return dataTable;
    }

    /**
     * 获取车辆信息详细信息
     */
    @PreAuthorize("@ss.hasPermi('dk:vehicle:query')")
    @GetMapping(value = "/{vehicleId}")
    public AjaxResult getInfo(@PathVariable("vehicleId") Long vehicleId) {
        try {
            if (dkVehicleInfoService != null) {
                return AjaxResult.success(dkVehicleInfoService.selectDkVehicleInfoByVehicleId(vehicleId));
            }
        } catch (Exception e) {
            logger.error("获取车辆信息详细信息失败", e);
        }
        
        // 返回模拟数据
        Map<String, Object> mockData = new HashMap<>();
        mockData.put("vehicleId", vehicleId);
        mockData.put("vinCode", "LSGKB54U8EA12345" + vehicleId);
        mockData.put("brand", "测试品牌" + vehicleId);
        mockData.put("model", "测试型号" + vehicleId);
        mockData.put("color", "测试颜色");
        mockData.put("licensePlate", "京A1234" + vehicleId);
        mockData.put("vehicleType", "测试类型");
        mockData.put("location", "停车场A区" + String.format("%02d", vehicleId) + "号");
        mockData.put("status", "0");
        mockData.put("mileage", 10000.0 + vehicleId * 1000);
        mockData.put("fuelLevel", 80);
        mockData.put("remark", "");
        
        return AjaxResult.success(mockData);
    }

    /**
     * 新增车辆信息
     */
    @PreAuthorize("@ss.hasPermi('dk:vehicle:add')")
    @PostMapping
    public AjaxResult add(@RequestBody DkVehicleInfo dkVehicleInfo) {
        try {
            if (dkVehicleInfoService != null) {
                return toAjax(dkVehicleInfoService.insertDkVehicleInfo(dkVehicleInfo));
            }
        } catch (Exception e) {
            logger.error("新增车辆信息失败", e);
        }
        
        // 模拟成功
        return AjaxResult.success("新增成功");
    }

    /**
     * 修改车辆信息
     */
    @PreAuthorize("@ss.hasPermi('dk:vehicle:edit')")
    @PutMapping
    public AjaxResult edit(@RequestBody DkVehicleInfo dkVehicleInfo) {
        try {
            if (dkVehicleInfoService != null) {
                return toAjax(dkVehicleInfoService.updateDkVehicleInfo(dkVehicleInfo));
            }
        } catch (Exception e) {
            logger.error("修改车辆信息失败", e);
        }
        
        // 模拟成功
        return AjaxResult.success("修改成功");
    }

    /**
     * 删除车辆信息
     */
    @PreAuthorize("@ss.hasPermi('dk:vehicle:remove')")
    @DeleteMapping("/{vehicleIds}")
    public AjaxResult remove(@PathVariable Long[] vehicleIds) {
        try {
            if (dkVehicleInfoService != null) {
                return toAjax(dkVehicleInfoService.deleteDkVehicleInfoByVehicleIds(vehicleIds));
            }
        } catch (Exception e) {
            logger.error("删除车辆信息失败", e);
        }
        
        // 模拟成功
        return AjaxResult.success("删除成功");
    }

    /**
     * 获取可用车辆列表
     */
    @PreAuthorize("@ss.hasPermi('dk:vehicle:query')")
    @GetMapping("/available")
    public AjaxResult listAvailableVehicles() {
        try {
            if (dkVehicleInfoService != null) {
                List<DkVehicleInfo> list = dkVehicleInfoService.selectAvailableVehicles();
                return AjaxResult.success(list);
            }
        } catch (Exception e) {
            logger.error("获取可用车辆列表失败", e);
        }
        
        // 返回模拟数据
        List<Map<String, Object>> mockList = new ArrayList<>();
        
        Map<String, Object> vehicle1 = new HashMap<>();
        vehicle1.put("vehicleId", 1L);
        vehicle1.put("vinCode", "LSGKB54U8EA123456");
        vehicle1.put("brand", "比亚迪");
        vehicle1.put("model", "秦PLUS DM-i");
        vehicle1.put("licensePlate", "京A12345");
        vehicle1.put("location", "停车场A区01号");
        mockList.add(vehicle1);
        
        Map<String, Object> vehicle2 = new HashMap<>();
        vehicle2.put("vehicleId", 4L);
        vehicle2.put("vinCode", "LHGCV1658EA789012");
        vehicle2.put("brand", "本田");
        vehicle2.put("model", "思域");
        vehicle2.put("licensePlate", "京A22222");
        vehicle2.put("location", "停车场B区10号");
        mockList.add(vehicle2);
        
        return AjaxResult.success(mockList);
    }

    /**
     * 更新车辆状态
     */
    @PreAuthorize("@ss.hasPermi('dk:vehicle:edit')")
    @PutMapping("/status/{vehicleId}")
    public AjaxResult updateVehicleStatus(@PathVariable Long vehicleId, @RequestBody Map<String, Object> statusData) {
        try {
            if (dkVehicleInfoService != null) {
                String status = statusData.get("status").toString();
                return toAjax(dkVehicleInfoService.updateVehicleStatus(vehicleId, status));
            }
        } catch (Exception e) {
            logger.error("更新车辆状态失败", e);
        }
        
        // 模拟成功
        return AjaxResult.success("状态更新成功");
    }

    /**
     * 批量更新车辆状态
     */
    @PreAuthorize("@ss.hasPermi('dk:vehicle:edit')")
    @PutMapping("/batchStatus")
    public AjaxResult batchUpdateVehicleStatus(@RequestBody Map<String, Object> statusData) {
        try {
            if (dkVehicleInfoService != null) {
                Long[] vehicleIds = (Long[]) statusData.get("vehicleIds");
                String status = statusData.get("status").toString();
                return toAjax(dkVehicleInfoService.batchUpdateVehicleStatus(vehicleIds, status));
            }
        } catch (Exception e) {
            logger.error("批量更新车辆状态失败", e);
        }
        
        // 模拟成功
        return AjaxResult.success("批量状态更新成功");
    }

    /**
     * 根据VIN码查询车辆
     */
    @PreAuthorize("@ss.hasPermi('dk:vehicle:query')")
    @GetMapping("/vin/{vinCode}")
    public AjaxResult getVehicleByVin(@PathVariable String vinCode) {
        try {
            if (dkVehicleInfoService != null) {
                DkVehicleInfo vehicle = dkVehicleInfoService.selectDkVehicleInfoByVinCode(vinCode);
                return AjaxResult.success(vehicle);
            }
        } catch (Exception e) {
            logger.error("根据VIN码查询车辆失败", e);
        }
        
        // 返回模拟数据
        Map<String, Object> mockData = new HashMap<>();
        mockData.put("vehicleId", 1L);
        mockData.put("vinCode", vinCode);
        mockData.put("brand", "测试品牌");
        mockData.put("model", "测试型号");
        mockData.put("licensePlate", "京A12345");
        
        return AjaxResult.success(mockData);
    }

    /**
     * 获取车辆统计信息
     */
    @PreAuthorize("@ss.hasPermi('dk:vehicle:query')")
    @GetMapping("/statistics")
    public AjaxResult getVehicleStatistics() {
        try {
            if (dkVehicleInfoService != null) {
                Map<String, Object> statistics = dkVehicleInfoService.getVehicleStatistics();
                return AjaxResult.success(statistics);
            }
        } catch (Exception e) {
            logger.error("获取车辆统计信息失败", e);
        }
        
        // 返回模拟数据
        Map<String, Object> mockStats = new HashMap<>();
        mockStats.put("total", 100);
        mockStats.put("available", 30);
        mockStats.put("inUse", 65);
        mockStats.put("maintenance", 3);
        mockStats.put("disabled", 2);
        
        return AjaxResult.success(mockStats);
    }
}

<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>数字钥匙系统 - PPT导航</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            display: flex;
            flex-direction: column;
            height: 100vh;
            background-color: #f5f5f5;
            color: #333;
            overflow: hidden;
        }
        
        header {
            background-color: #2c3e50;
            color: white;
            padding: 15px 20px;
            text-align: center;
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
            display: flex;
            justify-content: space-between;
            align-items: center;
            z-index: 10;
        }
        
        .header-title {
            flex: 1;
            text-align: center;
        }
        
        .view-toggle {
            background-color: #3498db;
            color: white;
            border: none;
            padding: 8px 12px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
            transition: background-color 0.3s;
            white-space: nowrap;
        }
        
        .view-toggle:hover {
            background-color: #2980b9;
        }
        
        .container {
            display: flex;
            flex: 1;
            overflow: hidden;
        }
        
        .nav {
            width: 250px;
            background-color: #34495e;
            color: white;
            padding: 20px 0;
            overflow-y: auto;
            box-shadow: 2px 0 5px rgba(0,0,0,0.1);
            z-index: 5;
        }
        
        .nav-item {
            padding: 12px 20px;
            cursor: pointer;
            transition: background-color 0.3s;
            border-left: 3px solid transparent;
        }
        
        .nav-item:hover {
            background-color: #2c3e50;
            border-left-color: #3498db;
        }
        
        .nav-item.active {
            background-color: #2c3e50;
            border-left-color: #3498db;
            font-weight: bold;
        }
        
        .content {
            flex: 1;
            background-color: #ddd;
            position: relative;
            overflow: auto;
        }
        
        .iframe-container {
            position: absolute;
            width: 100%;
            height: 100%;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        
        .iframe-container.fit-screen {
            overflow: hidden;
        }
        
        .iframe-container.original-size {
            overflow: auto;
        }
        
        .iframe-wrapper {
            width: 1920px;
            height: 1080px;
            position: relative;
            background-color: white;
            box-shadow: 0 0 15px rgba(0,0,0,0.2);
        }
        
        /* 适应屏幕模式 */
        .iframe-container.fit-screen .iframe-wrapper {
            transform-origin: center;
            transition: transform 0.3s;
        }
        
        /* 原始尺寸模式 */
        .iframe-container.original-size .iframe-wrapper {
            /* 不进行任何缩放 */
        }
        
        iframe {
            width: 100%;
            height: 100%;
            border: none;
            display: block;
        }
        
        .size-info {
            position: fixed;
            bottom: 10px;
            right: 10px;
            background-color: rgba(0,0,0,0.6);
            color: white;
            padding: 5px 10px;
            border-radius: 4px;
            font-size: 12px;
            z-index: 100;
        }
        
        @media (max-width: 768px) {
            .container {
                flex-direction: column;
            }
            
            .nav {
                width: 100%;
                padding: 10px 0;
            }
        }
    </style>
</head>
<body>
    <header>
        <div class="header-title">
            <h1>数字钥匙 - PPT导航</h1>
        </div>
        <button id="view-toggle" class="view-toggle">切换到原始大小</button>
    </header>
    
    <div class="container">
        <div class="nav" id="nav">
            <!-- 导航项将通过JavaScript动态添加 -->
        </div>
        
        <div class="content">
            <div id="iframe-container" class="iframe-container fit-screen">
                <div id="iframe-wrapper" class="iframe-wrapper">
                    <iframe id="content-frame" src="about:blank"></iframe>
                </div>
                <div class="size-info" id="size-info">适应屏幕模式</div>
            </div>
        </div>
    </div>
    
    <script>
        // 以下是模拟自动扫描的函数，在实际环境中，它会被替换成上述方案中的服务器请求
        function scanHTMLFiles() {
            // 通过AJAX请求获取当前目录的内容（需要服务器支持）
            // 这里我们使用硬编码的列表作为备选方案
            
            // 当你添加新文件时，只需将其放入下方数组
            // 格式：{ id: 文件名（小写无空格）, name: 显示名称, file: 文件名.html }
            return [
                { id: 'app', name: 'APP - 功能展示', file: 'APP - 功能展示.html' },
                { id: 'car', name: '车端 - 功能展示', file: '车端 - 功能展示.html' },
                { id: 'cloud', name: '钥匙云平台 - 功能展示', file: '钥匙云平台 - 功能展示.html' },
                { id: 'share', name: '钥匙分享', file: '钥匙分享.html' },
                { id: 'pair', name: '钥匙配对', file: '钥匙配对.html' },
                { id: 'auto', name: '无感控车', file: '无感控车.html' }
                // 在这里添加新文件，就无需修改其他代码
            ];
        }
        
        // 创建导航菜单
        function createNavigation() {
            const navElement = document.getElementById('nav');
            navElement.innerHTML = ''; // 清空现有内容
            
            const pages = scanHTMLFiles();
            window.pages = pages;
            
            pages.forEach(page => {
                const navItem = document.createElement('div');
                navItem.className = 'nav-item';
                navItem.textContent = page.name;
                navItem.dataset.file = page.file;
                navItem.onclick = function() {
                    // 设置当前项为激活状态
                    document.querySelectorAll('.nav-item').forEach(item => {
                        item.classList.remove('active');
                    });
                    this.classList.add('active');
                    
                    // 加载内容
                    loadContent(page.file);
                };
                
                navElement.appendChild(navItem);
            });
        }
        
        // 加载内容到iframe
        function loadContent(fileName) {
            const iframe = document.getElementById('content-frame');
            iframe.src = fileName;
            
            // 重新调整显示
            adjustView();
        }
        
        // 调整视图大小
        function adjustView() {
            const container = document.getElementById('iframe-container');
            const wrapper = document.getElementById('iframe-wrapper');
            
            if (container.classList.contains('fit-screen')) {
                // 计算缩放比例
                const contentWidth = document.querySelector('.content').clientWidth;
                const contentHeight = document.querySelector('.content').clientHeight;
                
                // 计算适合的缩放比例
                const scaleX = contentWidth / 1920;
                const scaleY = contentHeight / 1080;
                const scale = Math.min(scaleX, scaleY);
                
                // 应用缩放
                wrapper.style.transform = `scale(${scale})`;
                
                // 添加固定比例的样式
                wrapper.style.transformOrigin = 'center';
            } else {
                // 原始大小
                wrapper.style.transform = 'scale(1)';
                wrapper.style.transformOrigin = 'top left';
            }
        }
        
        // 切换显示模式
        function toggleViewMode() {
            const container = document.getElementById('iframe-container');
            const button = document.getElementById('view-toggle');
            const sizeInfo = document.getElementById('size-info');
            
            if (container.classList.contains('fit-screen')) {
                // 切换到原始大小
                container.classList.remove('fit-screen');
                container.classList.add('original-size');
                button.textContent = '适应屏幕';
                sizeInfo.textContent = '原始大小: 1920×1080';
            } else {
                // 切换到适应屏幕
                container.classList.remove('original-size');
                container.classList.add('fit-screen');
                button.textContent = '切换到原始大小';
                sizeInfo.textContent = '适应屏幕模式';
            }
            
            // 调整视图大小
            adjustView();
        }
        
        // 监听窗口大小变化，调整视图
        window.addEventListener('resize', adjustView);
        
        // 初始化
        document.addEventListener('DOMContentLoaded', function() {
            createNavigation();
            
            // 设置视图切换按钮事件
            document.getElementById('view-toggle').addEventListener('click', toggleViewMode);
            
            // 默认加载第一个页面
            if (window.pages.length > 0) {
                const firstNavItem = document.querySelector('.nav-item');
                if (firstNavItem) {
                    firstNavItem.classList.add('active');
                    loadContent(window.pages[0].file);
                }
            }
            
            // 初始调整视图
            adjustView();
        });
    </script>
</body>
</html> 
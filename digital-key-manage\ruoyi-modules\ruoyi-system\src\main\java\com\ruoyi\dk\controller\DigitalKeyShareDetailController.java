package com.ruoyi.dk.controller;

import java.util.List;
import javax.servlet.http.HttpServletResponse;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.log.annotation.Log;
import com.ruoyi.common.log.enums.BusinessType;
import com.ruoyi.common.security.annotation.RequiresPermissions;
import com.ruoyi.dk.domain.DigitalKeyShareDetail;
import com.ruoyi.dk.service.IDigitalKeyShareDetailService;
import com.ruoyi.common.core.web.controller.BaseController;
import com.ruoyi.common.core.web.domain.AjaxResult;
import com.ruoyi.common.core.utils.poi.ExcelUtil;
import com.ruoyi.common.core.web.page.TableDataInfo;

/**
 * 车辆数字钥匙分享详情Controller
 * 
 * <AUTHOR>
 * @date 2024-08-12
 */
@RestController
@RequestMapping("/digitalKeyShareDetail")
public class DigitalKeyShareDetailController extends BaseController
{
    @Autowired
    private IDigitalKeyShareDetailService digitalKeyShareDetailService;

    /**
     * 查询车辆数字钥匙分享详情列表
     */
    @RequiresPermissions("dk:digitalKeyShareDetail:list")
    @GetMapping("/list")
    public TableDataInfo list(DigitalKeyShareDetail digitalKeyShareDetail)
    {
        startPage();
        List<DigitalKeyShareDetail> list = digitalKeyShareDetailService.selectDigitalKeyShareDetailList(digitalKeyShareDetail);
        return getDataTable(list);
    }

    /**
     * 导出车辆数字钥匙分享详情列表
     */
    @RequiresPermissions("dk:digitalKeyShareDetail:export")
    @Log(title = "车辆数字钥匙分享详情", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, DigitalKeyShareDetail digitalKeyShareDetail)
    {
        List<DigitalKeyShareDetail> list = digitalKeyShareDetailService.selectDigitalKeyShareDetailList(digitalKeyShareDetail);
        ExcelUtil<DigitalKeyShareDetail> util = new ExcelUtil<DigitalKeyShareDetail>(DigitalKeyShareDetail.class);
        util.exportExcel(response, list, "车辆数字钥匙分享详情数据");
    }

    /**
     * 获取车辆数字钥匙分享详情详细信息
     */
    @GetMapping(value = "/{detailId}")
    public AjaxResult getInfo(@PathVariable("detailId") Long detailId)
    {
        return success(digitalKeyShareDetailService.selectDigitalKeyShareDetailByDetailId(detailId));
    }

    /**
     * 新增车辆数字钥匙分享详情
     */
    @Log(title = "车辆数字钥匙分享详情", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody DigitalKeyShareDetail digitalKeyShareDetail)
    {
        return toAjax(digitalKeyShareDetailService.insertDigitalKeyShareDetail(digitalKeyShareDetail));
    }

    /**
     * 修改车辆数字钥匙分享详情
     */
    @Log(title = "车辆数字钥匙分享详情", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody DigitalKeyShareDetail digitalKeyShareDetail)
    {
        return toAjax(digitalKeyShareDetailService.updateDigitalKeyShareDetail(digitalKeyShareDetail));
    }

    /**
     * 删除车辆数字钥匙分享详情
     */
    @Log(title = "车辆数字钥匙分享详情", businessType = BusinessType.DELETE)
	@DeleteMapping("/{detailIds}")
    public AjaxResult remove(@PathVariable Long[] detailIds)
    {
        return toAjax(digitalKeyShareDetailService.deleteDigitalKeyShareDetailByDetailIds(detailIds));
    }
}

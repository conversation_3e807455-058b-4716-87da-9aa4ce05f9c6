<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.dk.mapper.DkUserApplicationMapper">
    
    <resultMap type="DkUserApplication" id="DkUserApplicationResult">
        <result property="applicationId"    column="application_id"    />
        <result property="applicationNo"    column="application_no"    />
        <result property="userId"    column="user_id"    />
        <result property="realName"    column="real_name"    />
        <result property="phone"    column="phone"    />
        <result property="idCard"    column="id_card"    />
        <result property="drivingLicense"    column="driving_license"    />
        <result property="idCardFrontUrl"    column="id_card_front_url"    />
        <result property="idCardBackUrl"    column="id_card_back_url"    />
        <result property="drivingLicenseUrl"    column="driving_license_url"    />
        <result property="applicationReason"    column="application_reason"    />
        <result property="status"    column="status"    />
        <result property="auditUserId"    column="audit_user_id"    />
        <result property="auditUserName"    column="audit_user_name"    />
        <result property="auditTime"    column="audit_time"    />
        <result property="auditRemark"    column="audit_remark"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="delFlag"    column="del_flag"    />
    </resultMap>

    <sql id="selectDkUserApplicationVo">
        select application_id, application_no, user_id, real_name, phone, id_card, driving_license, id_card_front_url, id_card_back_url, driving_license_url, application_reason, status, audit_user_id, audit_user_name, audit_time, audit_remark, create_by, create_time, update_by, update_time, del_flag from dk_user_application
    </sql>

    <select id="selectDkUserApplicationList" parameterType="DkUserApplication" resultMap="DkUserApplicationResult">
        <include refid="selectDkUserApplicationVo"/>
        <where>  
            <if test="applicationNo != null  and applicationNo != ''"> and application_no like concat('%', #{applicationNo}, '%')</if>
            <if test="userId != null "> and user_id = #{userId}</if>
            <if test="realName != null  and realName != ''"> and real_name like concat('%', #{realName}, '%')</if>
            <if test="phone != null  and phone != ''"> and phone = #{phone}</if>
            <if test="idCard != null  and idCard != ''"> and id_card = #{idCard}</if>
            <if test="status != null  and status != ''"> and status = #{status}</if>
            <if test="auditUserName != null  and auditUserName != ''"> and audit_user_name like concat('%', #{auditUserName}, '%')</if>
        </where>
        order by create_time desc
    </select>
    
    <select id="selectDkUserApplicationByApplicationId" parameterType="Long" resultMap="DkUserApplicationResult">
        <include refid="selectDkUserApplicationVo"/>
        where application_id = #{applicationId}
    </select>
    
    <select id="selectDkUserApplicationByUserId" parameterType="Long" resultMap="DkUserApplicationResult">
        <include refid="selectDkUserApplicationVo"/>
        where user_id = #{userId}
        order by create_time desc
    </select>
    
    <select id="selectDkUserApplicationByVehicleId" parameterType="Long" resultMap="DkUserApplicationResult">
        <include refid="selectDkUserApplicationVo"/>
        where vehicle_id = #{vehicleId}
        order by create_time desc
    </select>
    
    <select id="countPendingApplications" resultType="int">
        select count(*) from dk_user_application where status = '0'
    </select>
    
    <select id="countApplicationsByStatus" parameterType="String" resultType="int">
        select count(*) from dk_user_application where status = #{status}
    </select>

    <insert id="insertDkUserApplication" parameterType="DkUserApplication" useGeneratedKeys="true" keyProperty="applicationId">
        insert into dk_user_application
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="applicationNo != null">application_no,</if>
            <if test="userId != null">user_id,</if>
            <if test="realName != null">real_name,</if>
            <if test="phone != null">phone,</if>
            <if test="idCard != null">id_card,</if>
            <if test="drivingLicense != null">driving_license,</if>
            <if test="idCardFrontUrl != null">id_card_front_url,</if>
            <if test="idCardBackUrl != null">id_card_back_url,</if>
            <if test="drivingLicenseUrl != null">driving_license_url,</if>
            <if test="applicationReason != null">application_reason,</if>
            <if test="status != null">status,</if>
            <if test="auditUserId != null">audit_user_id,</if>
            <if test="auditUserName != null">audit_user_name,</if>
            <if test="auditTime != null">audit_time,</if>
            <if test="auditRemark != null">audit_remark,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="delFlag != null">del_flag,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="applicationNo != null">#{applicationNo},</if>
            <if test="userId != null">#{userId},</if>
            <if test="realName != null">#{realName},</if>
            <if test="phone != null">#{phone},</if>
            <if test="idCard != null">#{idCard},</if>
            <if test="drivingLicense != null">#{drivingLicense},</if>
            <if test="idCardFrontUrl != null">#{idCardFrontUrl},</if>
            <if test="idCardBackUrl != null">#{idCardBackUrl},</if>
            <if test="drivingLicenseUrl != null">#{drivingLicenseUrl},</if>
            <if test="applicationReason != null">#{applicationReason},</if>
            <if test="status != null">#{status},</if>
            <if test="auditUserId != null">#{auditUserId},</if>
            <if test="auditUserName != null">#{auditUserName},</if>
            <if test="auditTime != null">#{auditTime},</if>
            <if test="auditRemark != null">#{auditRemark},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="delFlag != null">#{delFlag},</if>
         </trim>
    </insert>

    <update id="updateDkUserApplication" parameterType="DkUserApplication">
        update dk_user_application
        <trim prefix="SET" suffixOverrides=",">
            <if test="applicationNo != null">application_no = #{applicationNo},</if>
            <if test="userId != null">user_id = #{userId},</if>
            <if test="realName != null">real_name = #{realName},</if>
            <if test="phone != null">phone = #{phone},</if>
            <if test="idCard != null">id_card = #{idCard},</if>
            <if test="drivingLicense != null">driving_license = #{drivingLicense},</if>
            <if test="idCardFrontUrl != null">id_card_front_url = #{idCardFrontUrl},</if>
            <if test="idCardBackUrl != null">id_card_back_url = #{idCardBackUrl},</if>
            <if test="drivingLicenseUrl != null">driving_license_url = #{drivingLicenseUrl},</if>
            <if test="applicationReason != null">application_reason = #{applicationReason},</if>
            <if test="status != null">status = #{status},</if>
            <if test="auditUserId != null">audit_user_id = #{auditUserId},</if>
            <if test="auditUserName != null">audit_user_name = #{auditUserName},</if>
            <if test="auditTime != null">audit_time = #{auditTime},</if>
            <if test="auditRemark != null">audit_remark = #{auditRemark},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="remark != null">remark = #{remark},</if>
        </trim>
        where application_id = #{applicationId}
    </update>

    <delete id="deleteDkUserApplicationByApplicationId" parameterType="Long">
        delete from dk_user_application where application_id = #{applicationId}
    </delete>

    <delete id="deleteDkUserApplicationByApplicationIds" parameterType="String">
        delete from dk_user_application where application_id in 
        <foreach item="applicationId" collection="array" open="(" separator="," close=")">
            #{applicationId}
        </foreach>
    </delete>
</mapper>

package com.ruoyi.dk.controller;

import com.ruoyi.common.core.web.controller.BaseController;
import com.ruoyi.common.core.web.domain.AjaxResult;
import com.ruoyi.common.core.web.page.TableDataInfo;
import com.ruoyi.dk.domain.DkUserApplication;
import com.ruoyi.dk.service.IDkUserApplicationService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 用户申请管理Controller
 * 
 * <AUTHOR>
 * @date 2024-01-15
 */
@RestController
@RequestMapping("/dk/userApplication")
public class DkUserApplicationController extends BaseController {

    @Autowired(required = false)
    private IDkUserApplicationService dkUserApplicationService;

    /**
     * 查询用户申请列表
     */
    @GetMapping("/list")
    public TableDataInfo list(DkUserApplication dkUserApplication) {
        startPage();
        List<DkUserApplication> list = dkUserApplicationService.selectDkUserApplicationList(dkUserApplication);
        return getDataTable(list);
    }

    /**
     * 获取用户申请详细信息
     */
    @GetMapping(value = "/{applicationId}")
    public AjaxResult getInfo(@PathVariable Long applicationId) {
        return AjaxResult.success(dkUserApplicationService.selectDkUserApplicationByApplicationId(applicationId));
    }

    /**
     * 新增用户申请
     */
    @PostMapping
    public AjaxResult add(@RequestBody DkUserApplication dkUserApplication) {
        return toAjax(dkUserApplicationService.insertDkUserApplication(dkUserApplication));
    }

    /**
     * 修改用户申请
     */
    @PutMapping
    public AjaxResult edit(@RequestBody DkUserApplication dkUserApplication) {
        return toAjax(dkUserApplicationService.updateDkUserApplication(dkUserApplication));
    }

    /**
     * 删除用户申请
     */
    @DeleteMapping("/{applicationIds}")
    public AjaxResult remove(@PathVariable Long[] applicationIds) {
        return toAjax(dkUserApplicationService.deleteDkUserApplicationByApplicationIds(applicationIds));
    }

    /**
     * 审核用户申请
     */
    @PutMapping("/audit")
    public AjaxResult audit(@RequestBody Map<String, Object> auditData) {
        Long applicationId = Long.valueOf(auditData.get("applicationId").toString());
        String status = auditData.get("status").toString();
        String auditRemark = auditData.get("auditRemark").toString();
        return toAjax(dkUserApplicationService.auditApplication(applicationId, status, auditRemark));
    }

    /**
     * 批量审核用户申请
     */
    @PutMapping("/batchAudit")
    public AjaxResult batchAudit(@RequestBody Map<String, Object> auditData) {
        Long[] applicationIds = (Long[]) auditData.get("applicationIds");
        String status = auditData.get("status").toString();
        String auditRemark = auditData.get("auditRemark").toString();
        return toAjax(dkUserApplicationService.batchAuditApplication(applicationIds, status, auditRemark));
    }

    /**
     * 获取待审核申请数量
     */
    @GetMapping("/pendingCount")
    public AjaxResult getPendingApplicationCount() {
        int count = dkUserApplicationService.getPendingApplicationCount();
        return AjaxResult.success(count);
    }
}

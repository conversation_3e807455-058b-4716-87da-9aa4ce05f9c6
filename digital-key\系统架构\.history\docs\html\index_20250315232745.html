<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>数字钥匙系统技术方案</title>
    <!-- 添加jsPlumb库 -->
    <script src="https://cdn.jsdelivr.net/npm/jsplumb@2.15.6/dist/js/jsplumb.min.js"></script>
    <style>
        /* 基础样式 */
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            line-height: 1.6;
            color: #333;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f9f9f9;
        }
        h1, h2, h3, h4 {
            color: #0066cc;
            margin-top: 30px;
        }
        h1 {
            text-align: center;
            padding: 20px 0;
            background-color: #0066cc;
            color: white;
            border-radius: 8px;
            margin-top: 0;
        }

        /* 目录样式 */
        .toc {
            background-color: white;
            border-radius: 10px;
            padding: 20px;
            box-shadow: 0 4px 12px rgba(0,0,0,0.1);
            margin: 30px 0;
        }
        .toc-title {
            font-size: 24px;
            color: #0066cc;
            margin-bottom: 20px;
            border-bottom: 2px solid #e0e0e0;
            padding-bottom: 10px;
        }
        .toc-list {
            list-style-type: none;
            padding: 0;
        }
        .toc-item {
            margin: 15px 0;
            padding: 15px;
            background-color: #f5f9ff;
            border-radius: 8px;
            transition: transform 0.3s, box-shadow 0.3s;
            cursor: pointer;
        }
        .toc-item:hover {
            transform: translateY(-3px);
            box-shadow: 0 6px 12px rgba(0,0,0,0.1);
            background-color: #e6f0ff;
        }
        .toc-link {
            display: flex;
            align-items: center;
            text-decoration: none;
            color: #0066cc;
            font-weight: bold;
        }
        .toc-icon {
            font-size: 24px;
            margin-right: 15px;
        }
        .toc-desc {
            margin-top: 10px;
            color: #666;
            font-size: 14px;
        }

        /* 架构图样式 */
        .architecture-overview {
            margin: 30px 0;
            background-color: white;
            border-radius: 10px;
            padding: 20px;
            box-shadow: 0 4px 12px rgba(0,0,0,0.1);
        }
        .architecture-overview h2 {
            text-align: center;
            margin-bottom: 25px;
            color: #0066cc;
            border-bottom: 2px solid #e0e0e0;
            padding-bottom: 10px;
        }
        .architecture-diagram-container {
            position: relative;
            margin: 20px 0;
            min-height: 600px;
        }

        /* 组件卡片样式 */
        .component-container {
            display: flex;
            flex-wrap: wrap;
            justify-content: space-around;
            gap: 20px;
            margin: 30px 0;
        }
        .component-card {
            background-color: white;
            border-radius: 10px;
            box-shadow: 0 4px 8px rgba(0,0,0,0.1);
            width: 300px;
            transition: transform 0.3s, box-shadow 0.3s;
            overflow: hidden;
        }
        .component-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 8px 16px rgba(0,0,0,0.15);
        }
        .component-header {
            background-color: #0066cc;
            color: white;
            padding: 15px;
            display: flex;
            align-items: center;
        }
        .component-icon {
            font-size: 24px;
            margin-right: 10px;
        }
        .component-title {
            font-size: 18px;
            font-weight: bold;
        }
        .component-body {
            padding: 15px;
        }

        /* 架构模块样式 */
        .architecture-container {
            display: flex;
            flex-wrap: wrap;
            gap: 20px;
            margin: 30px 0;
        }
        .architecture-module {
            background-color: white;
            border-radius: 10px;
            box-shadow: 0 4px 8px rgba(0,0,0,0.1);
            flex: 1;
            min-width: 300px;
            transition: transform 0.3s;
        }
        .architecture-module:hover {
            transform: translateY(-5px);
        }
        .module-header {
            background-color: #0066cc;
            color: white;
            padding: 15px;
            border-radius: 10px 10px 0 0;
            font-size: 18px;
            font-weight: bold;
            display: flex;
            align-items: center;
        }
        .module-icon {
            margin-right: 10px;
            font-size: 24px;
        }
        .module-body {
            padding: 15px;
        }

        /* 流程图样式 */
        .flow-diagram {
            margin: 30px 0;
            background-color: white;
            border-radius: 10px;
            padding: 20px;
            box-shadow: 0 4px 12px rgba(0,0,0,0.1);
        }
        .flow-diagram h3 {
            text-align: center;
            margin-bottom: 25px;
            color: #0066cc;
            border-bottom: 2px solid #e0e0e0;
            padding-bottom: 10px;
        }
        .flow-container {
            position: relative;
            margin: 20px 0;
            min-height: 500px;
        }
        .flow-module {
            position: absolute;
            width: 150px;
            height: 80px;
            background-color: white;
            border: 2px solid #0066cc;
            border-radius: 10px;
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            text-align: center;
            padding: 10px;
            box-shadow: 0 4px 8px rgba(0,0,0,0.1);
            transition: transform 0.3s, box-shadow 0.3s;
            cursor: pointer;
        }
        .flow-module:hover {
            transform: scale(1.05);
            box-shadow: 0 8px 16px rgba(0,0,0,0.15);
        }

        /* 图片样式 */
        .img-container {
            text-align: center;
            margin: 20px 0;
        }
        .img-container img {
            max-width: 100%;
            height: auto;
            border-radius: 8px;
            box-shadow: 0 4px 12px rgba(0,0,0,0.1);
        }

        /* 页脚样式 */
        .footer {
            text-align: center;
            margin-top: 30px;
            padding: 20px;
            color: #666;
            font-size: 14px;
            border-top: 1px solid #e0e0e0;
        }

        /* 响应式设计 */
        @media (max-width: 768px) {
            body {
                padding: 10px;
            }
            .component-card {
                width: 100%;
            }
            .architecture-module {
                min-width: 100%;
            }
        }
    </style>
</head>
<body>
    <h1>数字钥匙系统技术方案</h1>

    <div class="toc">
        <div class="toc-title">目录</div>
        <ul class="toc-list">
            <li class="toc-item" onclick="scrollToSection('system-overview')">
                <div class="toc-link">
                    <span class="toc-icon">🏗️</span>
                    <span>系统整体架构</span>
                </div>
                <div class="toc-desc">展示系统的整体架构设计和分层结构</div>
            </li>
            <li class="toc-item" onclick="scrollToSection('digital-key-system')">
                <div class="toc-link">
                    <span class="toc-icon">🔑</span>
                    <span>数字钥匙系统架构</span>
                </div>
                <div class="toc-desc">详细介绍数字钥匙系统的架构和组件关系</div>
            </li>
            <li class="toc-item" onclick="scrollToSection('core-components')">
                <div class="toc-link">
                    <span class="toc-icon">⚙️</span>
                    <span>核心组件功能</span>
                </div>
                <div class="toc-desc">介绍系统核心组件的主要功能和特点</div>
            </li>
            <li class="toc-item" onclick="scrollToSection('system-architecture')">
                <div class="toc-link">
                    <span class="toc-icon">📊</span>
                    <span>系统架构详解</span>
                </div>
                <div class="toc-desc">详细说明系统各个模块的功能和连接关系</div>
            </li>
        </ul>
    </div>

    <section id="system-overview" class="architecture-overview">
        <h2>系统整体架构</h2>
        <div class="img-container">
            <img src="整体架构图.png" alt="系统整体架构图">
        </div>
    </section>

    <section id="digital-key-system" class="architecture-overview">
        <h2>数字钥匙系统架构</h2>
        <div class="architecture-container">
            <div class="architecture-module">
                <div class="module-header">
                    <span class="module-icon">📱</span>
                    手机端
                </div>
                <div class="module-body">
                    <p>负责用户交互和钥匙管理的移动应用程序</p>
                </div>
            </div>
            <div class="architecture-module">
                <div class="module-header">
                    <span class="module-icon">☁️</span>
                    云平台
                </div>
                <div class="module-body">
                    <p>处理数据存储、认证和业务逻辑的后端服务</p>
                </div>
            </div>
            <div class="architecture-module">
                <div class="module-header">
                    <span class="module-icon">🚗</span>
                    车端
                </div>
                <div class="module-body">
                    <p>执行车辆控制和安全验证的车载系统</p>
                </div>
            </div>
        </div>
    </section>

    <section id="core-components" class="flow-diagram">
        <h3>核心业务流程</h3>
        <div class="img-container">
            <img src="首次配对 - 泳道图.png" alt="首次配对流程图">
            <h4>首次配对流程</h4>
        </div>
        <div class="img-container">
            <img src="无感控车 - 泳道图.png" alt="无感控车流程图">
            <h4>无感控车流程</h4>
        </div>
    </section>

    <section id="system-architecture" class="architecture-overview">
        <h2>系统架构详解</h2>
        <div class="component-container">
            <div class="component-card">
                <div class="component-header">
                    <span class="component-icon">🔐</span>
                    <span class="component-title">安全认证模块</span>
                </div>
                <div class="component-body">
                    <p>负责用户身份验证、权限管理和数据加密</p>
                </div>
            </div>
            <div class="component-card">
                <div class="component-header">
                    <span class="component-icon">📡</span>
                    <span class="component-title">通信模块</span>
                </div>
                <div class="component-body">
                    <p>处理设备间的数据传输和协议适配</p>
                </div>
            </div>
            <div class="component-card">
                <div class="component-header">
                    <span class="component-icon">💾</span>
                    <span class="component-title">数据管理模块</span>
                </div>
                <div class="component-body">
                    <p>管理用户数据、车辆信息和操作日志</p>
                </div>
            </div>
        </div>
    </section>

    <div class="footer">
        <p>数字钥匙系统技术方案 &copy; 2024</p>
    </div>

    <script>
        // 页面滚动功能
        function scrollToSection(sectionId) {
            document.getElementById(sectionId).scrollIntoView({
                behavior: 'smooth'
            });
        }

        // 页面加载完成后执行
        document.addEventListener('DOMContentLoaded', function() {
            // 初始化jsPlumb连接
            if (typeof jsPlumb !== 'undefined') {
                var instance = jsPlumb.getInstance({
                    Connector: ["Bezier", { curviness: 50 }],
                    Endpoint: ["Dot", { radius: 5 }],
                    EndpointStyle: { fill: "#0066cc" },
                    PaintStyle: { stroke: "#0066cc", strokeWidth: 2 },
                    HoverPaintStyle: { stroke: "#0088ff", strokeWidth: 3 },
                    ConnectionOverlays: [
                        ["Arrow", { location: 1, width: 10, length: 10 }]
                    ]
                });
            }

            // 添加模块卡片的悬停效果
            var modules = document.getElementsByClassName("architecture-module");
            for (var i = 0; i < modules.length; i++) {
                modules[i].addEventListener("mouseenter", function() {
                    this.style.transform = "translateY(-5px)";
                    this.style.boxShadow = "0 8px 16px rgba(0,0,0,0.15)";
                });
                modules[i].addEventListener("mouseleave", function() {
                    this.style.transform = "translateY(0)";
                    this.style.boxShadow = "0 4px 8px rgba(0,0,0,0.1)";
                });
            }
        });
    </script>
</body>
</html> 
package com.ruoyi.framework.tcp;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.*;
import java.net.Socket;
import java.util.Scanner;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;

/**
 * TBOX模拟器
 * 用于测试TCP服务器和4G控车功能
 * 
 * <AUTHOR>
 */
public class TboxSimulator {
    
    private static final Logger LOGGER = LoggerFactory.getLogger(TboxSimulator.class);
    
    private static final String SERVER_HOST = "localhost";
    private static final int SERVER_PORT = 9999;
    
    // 4G控车协议常量
    private static final String UNLOCK_RESPONSE = "7E000A000A000102030405060708090A000030333032303030313034007E";
    private static final String LOCK_RESPONSE = "7E000A000A000102030405060708090A000030333032303030313033007E";
    
    private Socket socket;
    private BufferedReader reader;
    private PrintWriter writer;
    private volatile boolean running = false;
    private ScheduledExecutorService heartbeatExecutor;
    
    public static void main(String[] args) {
        TboxSimulator simulator = new TboxSimulator();
        simulator.start();
    }
    
    public void start() {
        try {
            // 连接到TCP服务器
            socket = new Socket(SERVER_HOST, SERVER_PORT);
            reader = new BufferedReader(new InputStreamReader(socket.getInputStream()));
            writer = new PrintWriter(socket.getOutputStream(), true);
            running = true;
            
            LOGGER.info("🚗 TBOX模拟器已连接到服务器: {}:{}", SERVER_HOST, SERVER_PORT);
            System.out.println("🚗 TBOX模拟器已启动");
            System.out.println("📡 已连接到服务器: " + SERVER_HOST + ":" + SERVER_PORT);
            System.out.println("💡 输入命令:");
            System.out.println("   unlock - 发送解锁状态");
            System.out.println("   lock   - 发送闭锁状态");
            System.out.println("   auto   - 开启自动心跳");
            System.out.println("   stop   - 停止自动心跳");
            System.out.println("   quit   - 退出程序");
            System.out.println("----------------------------------------");
            
            // 启动消息接收线程
            Thread receiveThread = new Thread(this::receiveMessages);
            receiveThread.setDaemon(true);
            receiveThread.start();
            
            // 启动命令行输入处理
            handleUserInput();
            
        } catch (IOException e) {
            LOGGER.error("❌ TBOX模拟器启动失败: {}", e.getMessage(), e);
            System.err.println("❌ 连接服务器失败: " + e.getMessage());
        }
    }
    
    private void receiveMessages() {
        try {
            String message;
            while (running && (message = reader.readLine()) != null) {
                LOGGER.info("📨 收到服务器消息: {}", message);
                System.out.println("📨 收到服务器指令: " + message);
                
                // 解析并响应控车指令
                handleControlCommand(message);
            }
        } catch (IOException e) {
            if (running) {
                LOGGER.error("❌ 接收消息时发生错误: {}", e.getMessage(), e);
                System.err.println("❌ 接收消息错误: " + e.getMessage());
            }
        }
    }
    
    private void handleControlCommand(String command) {
        try {
            if (command.contains("7E000A000A000102030405060708090A00003033303230303031")) {
                // 检查是解锁还是闭锁指令
                if (command.endsWith("04007E")) {
                    // 解锁指令，发送解锁状态响应
                    Thread.sleep(1000); // 模拟处理时间
                    sendUnlockStatus();
                    System.out.println("🔓 已响应解锁指令");
                    
                } else if (command.endsWith("03007E")) {
                    // 闭锁指令，发送闭锁状态响应
                    Thread.sleep(1000); // 模拟处理时间
                    sendLockStatus();
                    System.out.println("🔒 已响应闭锁指令");
                    
                } else {
                    System.out.println("⚠️ 未知的控车指令格式");
                }
            } else {
                System.out.println("⚠️ 收到非标准格式指令: " + command);
            }
        } catch (Exception e) {
            LOGGER.error("❌ 处理控车指令时发生错误: {}", e.getMessage(), e);
            System.err.println("❌ 处理指令错误: " + e.getMessage());
        }
    }
    
    private void handleUserInput() {
        Scanner scanner = new Scanner(System.in);
        
        while (running) {
            System.out.print("TBOX> ");
            String input = scanner.nextLine().trim().toLowerCase();
            
            switch (input) {
                case "unlock":
                    sendUnlockStatus();
                    System.out.println("✅ 已发送解锁状态");
                    break;
                    
                case "lock":
                    sendLockStatus();
                    System.out.println("✅ 已发送闭锁状态");
                    break;
                    
                case "auto":
                    startAutoHeartbeat();
                    System.out.println("✅ 已开启自动心跳");
                    break;
                    
                case "stop":
                    stopAutoHeartbeat();
                    System.out.println("✅ 已停止自动心跳");
                    break;
                    
                case "quit":
                case "exit":
                    stop();
                    System.out.println("👋 TBOX模拟器已退出");
                    return;
                    
                case "help":
                case "?":
                    printHelp();
                    break;
                    
                default:
                    System.out.println("❓ 未知命令: " + input + " (输入 help 查看帮助)");
                    break;
            }
        }
        
        scanner.close();
    }
    
    private void sendUnlockStatus() {
        if (writer != null) {
            writer.println(UNLOCK_RESPONSE);
            LOGGER.info("📤 发送解锁状态: {}", UNLOCK_RESPONSE);
        }
    }
    
    private void sendLockStatus() {
        if (writer != null) {
            writer.println(LOCK_RESPONSE);
            LOGGER.info("📤 发送闭锁状态: {}", LOCK_RESPONSE);
        }
    }
    
    private void startAutoHeartbeat() {
        if (heartbeatExecutor == null || heartbeatExecutor.isShutdown()) {
            heartbeatExecutor = Executors.newSingleThreadScheduledExecutor();
            heartbeatExecutor.scheduleAtFixedRate(() -> {
                // 随机发送解锁或闭锁状态
                if (Math.random() > 0.5) {
                    sendUnlockStatus();
                    System.out.println("💓 自动心跳: 解锁状态");
                } else {
                    sendLockStatus();
                    System.out.println("💓 自动心跳: 闭锁状态");
                }
            }, 5, 10, TimeUnit.SECONDS);
        }
    }
    
    private void stopAutoHeartbeat() {
        if (heartbeatExecutor != null && !heartbeatExecutor.isShutdown()) {
            heartbeatExecutor.shutdown();
        }
    }
    
    private void printHelp() {
        System.out.println("📖 TBOX模拟器命令帮助:");
        System.out.println("   unlock - 发送解锁状态响应");
        System.out.println("   lock   - 发送闭锁状态响应");
        System.out.println("   auto   - 开启自动心跳（每10秒随机发送状态）");
        System.out.println("   stop   - 停止自动心跳");
        System.out.println("   help   - 显示此帮助信息");
        System.out.println("   quit   - 退出程序");
        System.out.println("----------------------------------------");
        System.out.println("📡 协议说明:");
        System.out.println("   解锁: " + UNLOCK_RESPONSE);
        System.out.println("   闭锁: " + LOCK_RESPONSE);
    }
    
    public void stop() {
        running = false;
        stopAutoHeartbeat();
        
        try {
            if (reader != null) reader.close();
            if (writer != null) writer.close();
            if (socket != null && !socket.isClosed()) {
                socket.close();
            }
            LOGGER.info("🛑 TBOX模拟器已停止");
        } catch (IOException e) {
            LOGGER.error("❌ 关闭TBOX模拟器时发生错误: {}", e.getMessage(), e);
        }
    }
}

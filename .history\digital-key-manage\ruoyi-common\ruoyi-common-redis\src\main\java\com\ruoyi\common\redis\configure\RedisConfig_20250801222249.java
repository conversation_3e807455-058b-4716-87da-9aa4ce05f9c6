package com.ruoyi.common.redis.configure;

import org.springframework.cache.annotation.CachingConfigurerSupport;
import org.springframework.cache.annotation.EnableCaching;
import org.springframework.context.annotation.Configuration;

/**
 * redis配置 - Mock实现，不依赖真实Redis
 *
 * <AUTHOR>
 */
@Configuration
@EnableCaching
public class RedisConfig extends CachingConfigurerSupport {
    // Mock实现不需要RedisTemplate Bean
}

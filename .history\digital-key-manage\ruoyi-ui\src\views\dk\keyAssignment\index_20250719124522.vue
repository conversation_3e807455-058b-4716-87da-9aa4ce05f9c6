<template>
  <div class="app-container">
    <!-- 搜索区域 -->
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="申请人" prop="realName">
        <el-input
          v-model="queryParams.realName"
          placeholder="请输入申请人姓名"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="申请状态" prop="status">
        <el-select v-model="queryParams.status" placeholder="请选择申请状态" clearable>
          <el-option
            v-for="dict in applicationStatusOptions"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <!-- 待分配钥匙的申请列表 -->
    <el-table v-loading="loading" :data="applicationList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="申请单号" align="center" prop="applicationNo" />
      <el-table-column label="申请人" align="center" prop="realName" />
      <el-table-column label="手机号" align="center" prop="phone" />
      <el-table-column label="申请时间" align="center" prop="createTime" width="180">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.createTime, '{y}-{m}-{d} {h}:{i}:{s}') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="申请状态" align="center" prop="status">
        <template slot-scope="scope">
          <dict-tag :options="dict.type.dk_application_status" :value="scope.row.status"/>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width" width="200">
        <template slot-scope="scope">
          <el-button
            v-if="scope.row.status === '1'"
            size="mini"
            type="primary"
            icon="el-icon-key"
            @click="handleAssignKey(scope.row)"
          >分配钥匙</el-button>
          <el-button
            size="mini"
            type="text"
            icon="el-icon-view"
            @click="handleViewApplication(scope.row)"
          >查看详情</el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 钥匙分配对话框 -->
    <el-dialog title="分配数字钥匙" :visible.sync="assignDialogVisible" width="800px" append-to-body>
      <div class="assign-container">
        <!-- 用户信息 -->
        <el-card class="user-info-card" shadow="never">
          <div slot="header" class="clearfix">
            <span>用户信息</span>
          </div>
          <el-descriptions :column="2" border>
            <el-descriptions-item label="申请人">{{ currentApplication.realName }}</el-descriptions-item>
            <el-descriptions-item label="手机号">{{ currentApplication.phone }}</el-descriptions-item>
            <el-descriptions-item label="身份证号">{{ currentApplication.idCard }}</el-descriptions-item>
            <el-descriptions-item label="驾驶证号">{{ currentApplication.drivingLicense }}</el-descriptions-item>
          </el-descriptions>
        </el-card>

        <!-- 车辆选择 -->
        <el-card class="vehicle-select-card" shadow="never" style="margin-top: 20px;">
          <div slot="header" class="clearfix">
            <span>选择车辆</span>
            <el-button style="float: right; padding: 3px 0" type="text" @click="refreshVehicleList">刷新</el-button>
          </div>
          
          <!-- 车辆筛选 -->
          <el-form :inline="true" size="small" style="margin-bottom: 15px;">
            <el-form-item label="品牌">
              <el-select v-model="vehicleFilter.brand" placeholder="请选择品牌" clearable @change="filterVehicles">
                <el-option
                  v-for="brand in brandOptions"
                  :key="brand"
                  :label="brand"
                  :value="brand"
                />
              </el-select>
            </el-form-item>
            <el-form-item label="位置">
              <el-select v-model="vehicleFilter.location" placeholder="请选择位置" clearable @change="filterVehicles">
                <el-option
                  v-for="location in locationOptions"
                  :key="location"
                  :label="location"
                  :value="location"
                />
              </el-select>
            </el-form-item>
          </el-form>

          <!-- 车辆列表 -->
          <el-table :data="filteredVehicleList" @current-change="handleVehicleSelect" highlight-current-row>
            <el-table-column label="车辆信息" width="200">
              <template slot-scope="scope">
                <div class="vehicle-info">
                  <div class="vehicle-name">{{ scope.row.brand }} {{ scope.row.model }}</div>
                  <div class="vehicle-detail">{{ scope.row.color }} | {{ scope.row.licensePlate }}</div>
                </div>
              </template>
            </el-table-column>
            <el-table-column label="VIN码" prop="vinCode" />
            <el-table-column label="位置" prop="location" />
            <el-table-column label="燃油/电量" width="100">
              <template slot-scope="scope">
                <el-progress :percentage="scope.row.fuelLevel" :color="getFuelColor(scope.row.fuelLevel)" />
              </template>
            </el-table-column>
            <el-table-column label="状态" align="center" width="80">
              <template slot-scope="scope">
                <dict-tag :options="dict.type.dk_vehicle_status" :value="scope.row.status"/>
              </template>
            </el-table-column>
          </el-table>
        </el-card>

        <!-- 选中的车辆信息 -->
        <el-card v-if="selectedVehicle" class="selected-vehicle-card" shadow="never" style="margin-top: 20px;">
          <div slot="header" class="clearfix">
            <span>已选择车辆</span>
          </div>
          <el-descriptions :column="3" border>
            <el-descriptions-item label="车辆">{{ selectedVehicle.brand }} {{ selectedVehicle.model }}</el-descriptions-item>
            <el-descriptions-item label="车牌号">{{ selectedVehicle.licensePlate }}</el-descriptions-item>
            <el-descriptions-item label="VIN码">{{ selectedVehicle.vinCode }}</el-descriptions-item>
            <el-descriptions-item label="位置">{{ selectedVehicle.location }}</el-descriptions-item>
            <el-descriptions-item label="燃油/电量">{{ selectedVehicle.fuelLevel }}%</el-descriptions-item>
            <el-descriptions-item label="里程">{{ selectedVehicle.mileage }}km</el-descriptions-item>
          </el-descriptions>
        </el-card>

        <!-- 钥匙配置 -->
        <el-card v-if="selectedVehicle" class="key-config-card" shadow="never" style="margin-top: 20px;">
          <div slot="header" class="clearfix">
            <span>钥匙配置</span>
          </div>
          <el-form :model="keyConfig" label-width="120px">
            <el-form-item label="使用期限">
              <el-date-picker
                v-model="keyConfig.validPeriod"
                type="datetimerange"
                range-separator="至"
                start-placeholder="开始时间"
                end-placeholder="结束时间"
                format="yyyy-MM-dd HH:mm:ss"
                value-format="yyyy-MM-dd HH:mm:ss">
              </el-date-picker>
            </el-form-item>
            <el-form-item label="备注信息">
              <el-input
                v-model="keyConfig.remark"
                type="textarea"
                :rows="3"
                placeholder="请输入备注信息"
              />
            </el-form-item>
          </el-form>
        </el-card>
      </div>

      <div slot="footer" class="dialog-footer">
        <el-button @click="assignDialogVisible = false">取 消</el-button>
        <el-button type="primary" @click="confirmAssignKey" :disabled="!selectedVehicle">确认分配</el-button>
      </div>
    </el-dialog>

    <!-- 申请详情对话框 -->
    <el-dialog title="申请详情" :visible.sync="detailDialogVisible" width="600px" append-to-body>
      <el-descriptions :column="2" border>
        <el-descriptions-item label="申请单号">{{ applicationDetail.applicationNo }}</el-descriptions-item>
        <el-descriptions-item label="申请人">{{ applicationDetail.realName }}</el-descriptions-item>
        <el-descriptions-item label="手机号">{{ applicationDetail.phone }}</el-descriptions-item>
        <el-descriptions-item label="身份证号">{{ applicationDetail.idCard }}</el-descriptions-item>
        <el-descriptions-item label="驾驶证号">{{ applicationDetail.drivingLicense }}</el-descriptions-item>
        <el-descriptions-item label="申请原因" :span="2">{{ applicationDetail.applicationReason }}</el-descriptions-item>
        <el-descriptions-item label="申请时间">{{ parseTime(applicationDetail.createTime) }}</el-descriptions-item>
        <el-descriptions-item label="审核时间">{{ parseTime(applicationDetail.auditTime) }}</el-descriptions-item>
        <el-descriptions-item label="审核人">{{ applicationDetail.auditUserName }}</el-descriptions-item>
        <el-descriptions-item label="审核备注" :span="2">{{ applicationDetail.auditRemark }}</el-descriptions-item>
      </el-descriptions>
      
      <!-- 证件照片 -->
      <div style="margin-top: 20px;">
        <h4>证件照片</h4>
        <el-row :gutter="20">
          <el-col :span="8" v-if="applicationDetail.idCardFrontUrl">
            <div class="image-item">
              <div class="image-title">身份证正面</div>
              <el-image
                style="width: 100%; height: 120px"
                :src="applicationDetail.idCardFrontUrl"
                :preview-src-list="[applicationDetail.idCardFrontUrl]"
                fit="cover">
              </el-image>
            </div>
          </el-col>
          <el-col :span="8" v-if="applicationDetail.idCardBackUrl">
            <div class="image-item">
              <div class="image-title">身份证背面</div>
              <el-image
                style="width: 100%; height: 120px"
                :src="applicationDetail.idCardBackUrl"
                :preview-src-list="[applicationDetail.idCardBackUrl]"
                fit="cover">
              </el-image>
            </div>
          </el-col>
          <el-col :span="8" v-if="applicationDetail.drivingLicenseUrl">
            <div class="image-item">
              <div class="image-title">驾驶证</div>
              <el-image
                style="width: 100%; height: 120px"
                :src="applicationDetail.drivingLicenseUrl"
                :preview-src-list="[applicationDetail.drivingLicenseUrl]"
                fit="cover">
              </el-image>
            </div>
          </el-col>
        </el-row>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { listUserApplication, getUserApplication } from "@/api/dk/userApplication";
import { listAvailableVehicles } from "@/api/dk/vehicleInfo";
import { assignDigitalKey } from "@/api/dk/keyAssignment";
import { APPLICATION_STATUS, VEHICLE_STATUS, dictToArray } from "@/utils/dkDict";

export default {
  name: "KeyAssignment",
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 申请列表
      applicationList: [],
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        realName: null,
        status: '1' // 默认只显示审核通过的申请
      },
      // 分配对话框
      assignDialogVisible: false,
      // 详情对话框
      detailDialogVisible: false,
      // 当前申请
      currentApplication: {},
      // 申请详情
      applicationDetail: {},
      // 可用车辆列表
      availableVehicleList: [],
      // 过滤后的车辆列表
      filteredVehicleList: [],
      // 选中的车辆
      selectedVehicle: null,
      // 车辆筛选条件
      vehicleFilter: {
        brand: null,
        location: null
      },
      // 品牌选项
      brandOptions: [],
      // 位置选项
      locationOptions: [],
      // 钥匙配置
      keyConfig: {
        validPeriod: [],
        remark: ''
      },
      // 字典选项
      applicationStatusOptions: dictToArray(APPLICATION_STATUS),
      vehicleStatusOptions: dictToArray(VEHICLE_STATUS)
    };
  },
  created() {
    this.getList();
  },
  methods: {
    /** 查询申请列表 */
    getList() {
      this.loading = true;
      listUserApplication(this.queryParams).then(response => {
        this.applicationList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.applicationId)
      this.single = selection.length !== 1
      this.multiple = !selection.length
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    /** 分配钥匙 */
    handleAssignKey(row) {
      this.currentApplication = row;
      this.selectedVehicle = null;
      this.keyConfig = {
        validPeriod: [],
        remark: ''
      };
      this.loadAvailableVehicles();
      this.assignDialogVisible = true;
    },
    /** 查看申请详情 */
    handleViewApplication(row) {
      getUserApplication(row.applicationId).then(response => {
        this.applicationDetail = response.data;
        this.detailDialogVisible = true;
      });
    },
    /** 加载可用车辆 */
    loadAvailableVehicles() {
      listAvailableVehicles().then(response => {
        this.availableVehicleList = response.data;
        this.filteredVehicleList = response.data;
        this.extractFilterOptions();
      });
    },
    /** 提取筛选选项 */
    extractFilterOptions() {
      this.brandOptions = [...new Set(this.availableVehicleList.map(v => v.brand))];
      this.locationOptions = [...new Set(this.availableVehicleList.map(v => v.location))];
    },
    /** 筛选车辆 */
    filterVehicles() {
      this.filteredVehicleList = this.availableVehicleList.filter(vehicle => {
        return (!this.vehicleFilter.brand || vehicle.brand === this.vehicleFilter.brand) &&
               (!this.vehicleFilter.location || vehicle.location === this.vehicleFilter.location);
      });
    },
    /** 选择车辆 */
    handleVehicleSelect(currentRow) {
      this.selectedVehicle = currentRow;
    },
    /** 刷新车辆列表 */
    refreshVehicleList() {
      this.loadAvailableVehicles();
    },
    /** 确认分配钥匙 */
    confirmAssignKey() {
      if (!this.selectedVehicle) {
        this.$modal.msgError("请选择车辆");
        return;
      }
      
      const assignData = {
        applicationId: this.currentApplication.applicationId,
        userId: this.currentApplication.userId,
        vehicleId: this.selectedVehicle.vehicleId,
        vinCode: this.selectedVehicle.vinCode,
        validStartTime: this.keyConfig.validPeriod[0],
        validEndTime: this.keyConfig.validPeriod[1],
        remark: this.keyConfig.remark
      };

      assignDigitalKey(assignData).then(() => {
        this.$modal.msgSuccess("钥匙分配成功");
        this.assignDialogVisible = false;
        this.getList();
      });
    },
    /** 获取燃油颜色 */
    getFuelColor(percentage) {
      if (percentage > 50) return '#67c23a';
      if (percentage > 20) return '#e6a23c';
      return '#f56c6c';
    }
  }
};
</script>

<style scoped>
.assign-container {
  max-height: 600px;
  overflow-y: auto;
}

.vehicle-info {
  line-height: 1.5;
}

.vehicle-name {
  font-weight: bold;
  color: #303133;
}

.vehicle-detail {
  font-size: 12px;
  color: #909399;
}

.image-item {
  text-align: center;
}

.image-title {
  margin-bottom: 8px;
  font-size: 14px;
  color: #606266;
}

.user-info-card, .vehicle-select-card, .selected-vehicle-card, .key-config-card {
  margin-bottom: 0;
}
</style>

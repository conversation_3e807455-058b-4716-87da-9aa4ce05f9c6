@startuml
skinparam monochrome true

participant "APP" as Mobile
participant "车端（BLE & TBOX）" as CarBT
participant "钥匙云平台" as Cloud

== 蓝牙配对流程 ==

Mobile -> Mobile : 1. 在APP中选择添加数字钥匙
Mobile -> Mobile : 2. 扫描车辆二维码或输入VIN码识别车辆
Mobile -> Cloud : 3. 发送车辆VIN码，请求获取数字钥匙
Cloud -> Cloud : 4. 根据VIN码生成虚拟密钥（VIRKEY）
Cloud -> Mobile : 5. 下发虚拟密钥到手机
Mobile -> Mobile : 6. 保存虚拟密钥到安全存储区

note over Cloud, CarBT
  钥匙云平台通过TSP平台或OEM平台查询VIN码对应的车辆TBOX信息
  (用于云平台主动和TBOX通讯)
end note

Cloud -> Cloud : 7. 查询VIN码对应的TBOX信息
Cloud -> CarBT : 8. 通过蜂窝网络连接到车辆TBOX
CarBT -> Cloud : 9. TBOX身份验证和连接确认
Cloud -> CarBT : 10. 生成根密钥并下发到汽车
CarBT -> CarBT : 11. 存储根密钥
CarBT -> Mobile : 12. 发送蓝牙广播（包含MAC地址等信息）
Mobile -> CarBT : 13. 扫描并连接到汽车蓝牙模块
Mobile -> CarBT : 14. 发送蓝牙配对请求
CarBT -> Mobile : 15. 发起蓝牙安全请求
CarBT -> CarBT : 16. 生成配对码
Mobile -> Mobile : 17. 生成或输入配对码
Mobile -> CarBT : 18. 发送配对码进行校验
CarBT -> Mobile : 19. 返回配对码校验结果
CarBT -> CarBT : 20. 配对成功，绑定设备信息
Mobile -> Mobile : 21. 配对成功，绑定设备信息
Mobile -> CarBT : 22. 发送加密的虚拟密钥和VIN码
CarBT -> CarBT : 23. 验证虚拟密钥和VIN码匹配
CarBT -> Mobile : 24. 返回验证结果
Mobile -> Cloud : 25. 上报配对结果
Cloud -> Cloud : 26. 记录钥匙绑定状态
@enduml
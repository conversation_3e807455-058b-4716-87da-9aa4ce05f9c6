/**
 * 内容加载处理脚本
 * 用于处理带有data-html-src属性的图片区域，直接加载并显示HTML内容
 */
document.addEventListener("DOMContentLoaded", function() {
    // 选择所有带有data-html-src属性的图片
    var imageContainers = document.querySelectorAll("img[data-html-src]");
    
    // 处理每个图片区域
    imageContainers.forEach(function(img) {
        // 获取HTML内容源
        var htmlSrc = img.getAttribute("data-html-src");
        
        // 创建内容容器
        var contentContainer = document.createElement('div');
        contentContainer.className = 'included-content';
        contentContainer.style.width = '100%';
        contentContainer.style.marginTop = '20px';
        contentContainer.style.marginBottom = '30px';
        
        // 加载HTML内容
        fetch(htmlSrc)
            .then(response => response.text())
            .then(html => {
                // 提取HTML内容的body部分
                var bodyContent = html.match(/<body[^>]*>([\s\S]*)<\/body>/i);
                
                if (bodyContent && bodyContent[1]) {
                    // 将提取的内容放入容器
                    contentContainer.innerHTML = bodyContent[1];
                    
                    // 确保所有样式正确应用
                    var styles = contentContainer.querySelectorAll('style');
                    styles.forEach(function(style) {
                        document.head.appendChild(style.cloneNode(true));
                    });
                    
                    // 替换图片元素为内容容器
                    img.parentNode.replaceChild(contentContainer, img);
                } else {
                    console.error('无法从' + htmlSrc + '提取内容');
                    // 保留原始图片，但添加错误提示
                    img.title = '无法加载内容';
                }
            })
            .catch(error => {
                console.error('加载' + htmlSrc + '时出错:', error);
                
                // 显示错误信息
                var errorMessage = document.createElement('div');
                errorMessage.style.padding = '20px';
                errorMessage.style.color = '#e74c3c';
                errorMessage.style.textAlign = 'center';
                errorMessage.innerHTML = '<strong>内容加载失败</strong><p>无法加载 ' + htmlSrc + '</p>';
                
                img.parentNode.replaceChild(errorMessage, img);
            });
    });
    
    // 淡入动画效果
    var fadeInElements = document.querySelectorAll(".fade-in");
    
    function checkVisibility() {
        fadeInElements.forEach(function(element) {
            var rect = element.getBoundingClientRect();
            var windowHeight = window.innerHeight;
            
            // 当元素进入视口时应用动画
            if (rect.top < windowHeight * 0.9) {
                element.classList.add("visible");
            }
        });
    }
    
    // 确保初始内容可见
    setTimeout(function() {
        // 将所有元素设置为可见
        fadeInElements.forEach(function(element) {
            element.classList.add("visible");
        });
    }, 100);
    
    // 添加可见时的类
    document.addEventListener("scroll", checkVisibility);
    window.addEventListener("resize", checkVisibility);
    
    // 立即检查初始可见性
    checkVisibility();
}); 
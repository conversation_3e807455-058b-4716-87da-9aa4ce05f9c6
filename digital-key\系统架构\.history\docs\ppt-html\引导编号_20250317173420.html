<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>钥匙云平台</title>
    <!-- 引入html2canvas库 -->
    <script src="https://html2canvas.hertzen.com/dist/html2canvas.min.js"></script>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
            font-family: 'PingFang SC', 'Microsoft YaHei', sans-serif;
        }
        
        body {
            background: #f0f0f0;
            display: flex;
            justify-content: center;
            align-items: center;
            min-height: 100vh;
            color: #333;
            padding: 20px;
        }
        
        /* PPT边框指示器 */
        .ppt-container {
            position: relative;
            width: 1920px; /* 标准PPT宽度 */
            height: 1080px; /* 标准PPT高度 */
            box-shadow: 0 0 30px rgba(0, 0, 0, 0.2);
            border: 2px dashed #ccc;
            background-image: linear-gradient(45deg, #ccc 25%, transparent 25%), 
                              linear-gradient(-45deg, #ccc 25%, transparent 25%), 
                              linear-gradient(45deg, transparent 75%, #ccc 75%), 
                              linear-gradient(-45deg, transparent 75%, #ccc 75%);
            background-size: 20px 20px;
            background-position: 0 0, 0 10px, 10px -10px, -10px 0px;
        }
        
        .ppt-label {
            position: absolute;
            top: -40px;
            left: 0;
            background-color: #555;
            color: white;
            padding: 5px 15px;
            border-radius: 4px;
            font-size: 16px;
        }
        
        /* 复制按钮 */
        .copy-button {
            position: absolute;
            top: 20px;
            right: 20px;
            background: linear-gradient(135deg, #3498db, #2980b9);
            color: white;
            border: none;
            padding: 12px 25px;
            border-radius: 30px;
            font-size: 18px;
            cursor: pointer;
            z-index: 1000;
            box-shadow: 0 4px 10px rgba(0, 0, 0, 0.2);
            display: flex;
            align-items: center;
            gap: 10px;
            transition: all 0.3s ease;
        }
        
        /* 下载按钮 */
        .download-button {
            position: absolute;
            top: 20px;
            right: 230px;
            background: linear-gradient(135deg, #2ecc71, #27ae60);
            color: white;
            border: none;
            padding: 12px 25px;
            border-radius: 30px;
            font-size: 18px;
            cursor: pointer;
            z-index: 1000;
            box-shadow: 0 4px 10px rgba(0, 0, 0, 0.2);
            display: flex;
            align-items: center;
            gap: 10px;
            transition: all 0.3s ease;
        }
        
        .copy-button:hover, .download-button:hover {
            transform: translateY(-3px);
            box-shadow: 0 6px 15px rgba(0, 0, 0, 0.25);
        }
        
        .copy-button:active, .download-button:active {
            transform: translateY(0);
        }
        
        .copy-icon {
            width: 20px;
            height: 20px;
            background-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="white"><path d="M19,21H8V7H19M19,5H8A2,2 0 0,0 6,7V21A2,2 0 0,0 8,23H19A2,2 0 0,0 21,21V7A2,2 0 0,0 19,5M16,1H4A2,2 0 0,0 2,3V17H4V3H16V1Z" /></svg>');
            background-size: contain;
            background-repeat: no-repeat;
        }
        
        .download-icon {
            width: 20px;
            height: 20px;
            background-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="white"><path d="M5,20H19V18H5M19,9H15V3H9V9H5L12,16L19,9Z" /></svg>');
            background-size: contain;
            background-repeat: no-repeat;
        }
        
        /* 清晰度选择器样式 */
        .resolution-selector {
            position: absolute;
            top: 70px;
            right: 230px;
            display: flex;
            align-items: center;
            background: rgba(255, 255, 255, 0.9);
            padding: 8px 15px;
            border-radius: 30px;
            box-shadow: 0 4px 10px rgba(0, 0, 0, 0.1);
            z-index: 1000;
        }
        
        .resolution-label {
            margin-right: 10px;
            font-size: 14px;
            color: #555;
        }
        
        .resolution-btn {
            border: none;
            background: #f0f0f0;
            padding: 5px 10px;
            margin: 0 3px;
            border-radius: 15px;
            font-size: 14px;
            cursor: pointer;
            transition: all 0.2s ease;
            color: #555;
        }
        
        .resolution-btn:hover {
            background: #e0e0e0;
        }
        
        .resolution-btn.active {
            background: linear-gradient(135deg, #3498db, #2980b9);
            color: white;
        }
        
        /* 修改整体布局为左右结构 */
        .slide {
            width: 100%;
            height: 100%;
            background-color: transparent; /* 完全透明背景 */
            padding: 60px;
            display: flex;
            flex-direction: column;
            overflow: hidden;
            position: relative;
        }
        
        /* 主内容区域左右分栏 */
        .main-content {
            display: flex;
            width: 100%;
            height: calc(100% - 100px); /* 减少高度，因为移除了底部版权信息 */
            margin-top: 20px;
        }
        
        /* 左侧内容列表区域 - 添加垂直居中 */
        .content-overview {
            width: 65%; /* 增加宽度从45%到65% */
            display: flex;
            flex-direction: column;
            padding-right: 40px;
            justify-content: center; /* 添加垂直居中 */
        }
        
        /* 右侧架构图区域 */
        .diagram-container {
            width: 35%; /* 减少宽度从55%到35% */
            display: flex;
            justify-content: center;
            align-items: center;
        }
        
        /* 目录卡片样式调整 */
        .content-card {
            background: white;
            border-radius: 20px;
            padding: 25px 35px;
            margin-bottom: 25px;
            width: 100%;
            display: flex;
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
            box-shadow: 0 8px 20px rgba(0, 0, 0, 0.07);
            cursor: pointer; /* 添加鼠标指针样式 */
        }
        
        .content-card:hover {
            transform: translateY(-3px);
            box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
        }
        
        /* 点击效果 */
        .content-card:active {
            transform: translateY(0px);
            box-shadow: 0 4px 10px rgba(0, 0, 0, 0.1);
        }
        
        .content-number {
            font-size: 52px;
            font-weight: 700;
            color: rgba(52, 152, 219, 0.2);
            margin-right: 30px;
            align-self: center;
            min-width: 60px;
            text-align: center;
        }
        
        .content-details {
            display: flex;
            flex-direction: column;
        }
        
        .content-title {
            font-size: 24px;
            font-weight: 600;
            color: #2c3e50;
            margin-bottom: 10px;
        }
        
        .content-desc {
            font-size: 16px;
            color: #7f8c8d;
            line-height: 1.5;
        }
        
        /* 当前内容高亮 */
        .content-card.active {
            background: linear-gradient(to right, #f0f7ff, #e6f0ff);
            border-left: 6px solid #3498db;
            transform: translateX(5px);
        }
        
        .content-card.active .content-number {
            color: rgba(52, 152, 219, 0.7);
        }
        
        /* 架构图容器优化 - 完全透明背景 */
        .architecture-diagram {
            width: 100%;
            height: 100%;
            background: transparent; /* 改为完全透明 */
            border-radius: 16px;
            padding: 30px;
            position: relative;
            display: flex;
            justify-content: center;
            align-items: center;
            border: none; /* 移除边框 */
            box-shadow: none; /* 移除阴影 */
        }
        
        .diagram-title {
            position: absolute;
            top: 15px;
            left: 50%;
            transform: translateX(-50%);
            font-size: 18px;
            font-weight: 600;
            color: #2c3e50;
            padding: 5px 15px;
            border-radius: 20px;
            background-color: rgba(255, 255, 255, 0.7);
        }
        
        /* 三角形布局优化 */
        .triangle-layout {
            position: relative;
            width: 100%;
            height: 100%;
        }
        
        /* 平台盒子样式 */
        .platform-box {
            position: absolute;
            width: 120px;
            height: 120px;
            border-radius: 12px;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            color: white;
            text-align: center;
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.2);
            z-index: 10;
        }
        
        /* 手机端定位 */
        .platform-mobile {
            background: linear-gradient(135deg, #2ecc71, #27ae60);
            top: 60px;
            left: 50%;
            transform: translateX(-50%);
        }
        
        /* A车端定位 */
        .platform-car {
            background: linear-gradient(135deg, #3498db, #2980b9);
            bottom: 120px;
            left: 20%;
        }
        
        /* 云平台定位 */
        .platform-cloud {
            background: linear-gradient(135deg, #9b59b6, #8e44ad);
            bottom: 120px;
            right: 20%;
        }
        
        /* 连接线样式优化 */
        .connection-line {
            position: absolute;
            background-color: #f1c40f;
            height: 3px;
            z-index: 5;
        }
        
        /* 手机到车的连线 */
        .line-mobile-car {
            width: 240px;
            top: 120px;
            left: 31%;
            transform: rotate(45deg);
            transform-origin: left center;
        }
        
        /* 手机到云的连线 */
        .line-mobile-cloud {
            width: 240px;
            top: 120px;
            right: 31%;
            transform: rotate(-45deg);
            transform-origin: right center;
        }
        
        /* 车到云的连线 */
        .line-car-cloud {
            width: 58%;
            bottom: 180px;
            left: 21%;
        }
        
        /* 连线标签样式 */
        .connection-label {
            position: absolute;
            background-color: white;
            padding: 5px 10px;
            border-radius: 12px;
            font-size: 12px;
            color: #555;
            white-space: nowrap;
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
            z-index: 6;
        }
        
        .label-mobile-car {
            top: 160px;
            left: 35%;
            transform: rotate(0deg);
        }
        
        .label-mobile-cloud {
            top: 160px;
            right: 35%;
            transform: rotate(0deg);
        }
        
        .label-car-cloud {
            bottom: 200px;
            left: 50%;
            transform: translateX(-50%);
        }
        
        /* 底部装饰和版权 */
        .footer {
            position: absolute;
            bottom: 20px;
            width: 100%;
            text-align: center;
            font-size: 14px;
            color: #95a5a6;
            left: 0;
            z-index: 10;
            display: none;
        }
        
        /* 侧边装饰 */
        .side-decoration {
            position: absolute;
            bottom: 40px;
            right: 40px;
            display: flex;
            gap: 15px;
            z-index: 10;
        }
        
        .side-icon {
            width: 50px;
            height: 50px;
            opacity: 0.7;
            transition: all 0.3s ease;
        }
        
        .side-icon:hover {
            transform: scale(1.1);
            opacity: 1;
        }
        
        /* 消息提示 */
        .toast {
            position: fixed;
            top: 50px;
            left: 50%;
            transform: translateX(-50%);
            background-color: rgba(0, 0, 0, 0.8);
            color: white;
            padding: 12px 25px;
            border-radius: 30px;
            font-size: 16px;
            z-index: 2000;
            display: none;
            animation: fadeInOut 2s ease;
        }
        
        @keyframes fadeInOut {
            0% { opacity: 0; }
            20% { opacity: 1; }
            80% { opacity: 1; }
            100% { opacity: 0; }
        }
        
        /* 简化架构图区域 - 使用更简单的实现 */
        .simplified-diagram {
            width: 100%;
            height: 300px;
            position: relative;
            margin-top: 40px;
        }
        
        /* 简化的平台样式 */
        .simple-platform {
            position: absolute;
            width: 100px;
            height: 100px;
            border-radius: 12px;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            color: white;
            text-align: center;
            box-shadow: 0 6px 15px rgba(0, 0, 0, 0.15);
            z-index: 10;
        }
        
        .simple-platform .icon {
            font-size: 32px;
            margin-bottom: 8px;
        }
        
        .simple-platform .name {
            font-size: 14px;
            font-weight: 500;
        }
        
        /* 各平台位置和颜色 */
        .phone-platform {
            background-color: #2ecc71;
            top: 0;
            left: 50%;
            transform: translateX(-50%);
        }
        
        .car-platform {
            background-color: #3498db;
            bottom: 0;
            left: 0;
        }
        
        .cloud-platform {
            background-color: #9b59b6;
            bottom: 0;
            right: 0;
        }
        
        /* 简化的连接线 */
        .simple-line {
            position: absolute;
            background-color: #f1c40f;
            height: 2px;
            z-index: 5;
        }
        
        /* 位置调整 */
        .line-phone-car {
            width: 120px;
            top: 50px;
            left: 40px;
            transform: rotate(45deg);
        }
        
        .line-phone-cloud {
            width: 120px;
            top: 50px;
            right: 40px;
            transform: rotate(-45deg);
        }
        
        .line-car-cloud {
            width: 170px;
            bottom: 50px;
            left: 100px;
        }
        
        /* 新的架构图样式 */
        .new-diagram {
            width: 100%;
            height: 100%;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            position: relative;
        }
        
        .diagram-title {
            font-size: 18px;
            font-weight: 600;
            color: #2c3e50;
            margin-bottom: 30px;
            text-align: center;
        }
        
        /* 三角形表格布局 */
        .triangle-table {
            width: 100%;
            height: 300px;
            border-collapse: collapse;
        }
        
        .triangle-table td {
            text-align: center;
            vertical-align: middle;
            padding: 0;
            position: relative;
        }
        
        .center-cell {
            height: 140px;
        }
        
        .bottom-cell {
            height: 140px;
        }
        
        .line-cell {
            height: 80px;
        }
        
        /* 平台方块样式 */
        .platform-item {
            width: 100px;
            height: 100px;
            border-radius: 12px;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            margin: 0 auto;
            box-shadow: 0 6px 16px rgba(0, 0, 0, 0.1);
        }
        
        .platform-item img {
            width: 40px;
            height: 40px;
            margin-bottom: 10px;
        }
        
        .platform-name {
            font-size: 14px;
            font-weight: 500;
            color: white;
        }
        
        /* 三个平台的颜色 */
        .mobile-item {
            background-color: #2ecc71;
        }
        
        .car-item {
            background-color: #3498db;
        }
        
        .cloud-item {
            background-color: #9b59b6;
        }
        
        /* 连接线样式 */
        .line {
            background-color: #f1c40f;
            height: 3px;
            position: relative;
        }
        
        .line-left {
            width: 120px;
            transform: rotate(45deg);
            transform-origin: right center;
            position: absolute;
            top: 40px;
            right: 20px;
        }
        
        .line-right {
            width: 120px;
            transform: rotate(-45deg);
            transform-origin: left center;
            position: absolute;
            top: 40px;
            left: 20px;
        }
        
        .line-bottom {
            width: 100%;
            position: absolute;
            top: 50%;
            left: 0;
        }
        
        .line-text {
            position: absolute;
            background: white;
            padding: 3px 8px;
            border-radius: 10px;
            font-size: 12px;
            color: #555;
            white-space: nowrap;
            top: -18px;
            left: 50%;
            transform: translateX(-50%);
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
        }
        
        /* 添加SVG图表容器样式 */
        .svg-diagram {
            width: 100%;
            height: 100%;
            display: flex;
            justify-content: center;
            align-items: center;
        }
        
        /* 确保SVG在容器中居中显示 */
        .svg-diagram svg {
            max-width: 100%;
            max-height: 100%;
        }
        
        /* 平台组件的阴影效果 */
        .platform rect {
            filter: drop-shadow(0px 4px 8px rgba(0, 0, 0, 0.2));
        }
    </style>
</head>
<body>
    <div class="ppt-container" id="pptContainer">
        <div class="ppt-label">PPT尺寸(1920×1080) - 透明背景</div>
        <button class="copy-button" id="copyButton">
            <span class="copy-icon"></span>复制为PPT图片
        </button>
        <button class="download-button" id="downloadButton">
            <span class="download-icon"></span>下载透明PNG
        </button>
        
        <!-- 添加清晰度选择按钮组 -->
        <div class="resolution-selector" id="resolutionSelector">
            <span class="resolution-label">清晰度:</span>
            <button class="resolution-btn" data-scale="1">标准</button>
            <button class="resolution-btn active" data-scale="2">高清</button>
            <button class="resolution-btn" data-scale="3">超清</button>
        </div>
        
        <div class="slide" id="slide">
            <!-- 顶部标题 -->
            <div class="header">
                <h1>车载智能钥匙系统</h1>
                <div class="subtitle">安全、高效的数字钥匙解决方案</div>
            </div>
            
            <!-- 主内容区域（左右布局） -->
            <div class="main-content">
                <!-- 左侧内容概述 -->
                <div class="content-overview">
                    <!-- 目录卡片 -->
                    <div class="content-card active" data-index="1">
                        <div class="content-number">1</div>
                        <div class="content-details">
                            <div class="content-title">整体架构与组成</div>
                            <div class="content-desc">详解车端、手机端与云平台之间的交互关系及数据流，阐述系统架构设计原理</div>
                        </div>
                    </div>
                    
                    <div class="content-card" data-index="2">
                        <div class="content-number">2</div>
                        <div class="content-details">
                            <div class="content-title">三端详细构成</div>
                            <div class="content-desc">剖析三端各自的模块构成、安全机制及功能覆盖，展示系统各组件的设计理念</div>
                        </div>
                    </div>
                    
                    <div class="content-card" data-index="3">
                        <div class="content-number">3</div>
                        <div class="content-details">
                            <div class="content-title">用户操作流程</div>
                            <div class="content-desc">演示各项核心功能的操作方式，包括设备配对、无感控车、钥匙授权与分享等使用场景</div>
                        </div>
                    </div>
                    
                    <div class="content-card" data-index="4">
                        <div class="content-number">4</div>
                        <div class="content-details">
                            <div class="content-title">常见问题解答</div>
                            <div class="content-desc">以问答形式呈现系统使用过程中的常见疑问，提供清晰易懂的解决方案</div>
                        </div>
                    </div>
                </div>
                
                <!-- 右侧架构图 -->
                <div class="diagram-container">
                    <!-- SVG架构图 - 全新实现方式 -->
                    <div class="svg-diagram">
                        <svg width="100%" height="100%" viewBox="0 0 400 350" xmlns="http://www.w3.org/2000/svg">
                            <!-- 标题 -->
                            <text x="200" y="30" font-size="18" text-anchor="middle" fill="#2c3e50" font-weight="600">三端通讯</text>
                            
                            <!-- 手机平台 -->
                            <g class="platform mobile-platform">
                                <rect x="150" y="60" width="100" height="100" rx="12" fill="#2ecc71"></rect>
                                <text x="200" y="140" font-size="16" text-anchor="middle" fill="white" font-weight="500">手机端</text>
                                <image x="170" y="80" width="60" height="40" href="data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHZpZXdCb3g9IjAgMCAyNCAyNCIgZmlsbD0id2hpdGUiPjxwYXRoIGQ9Ik0xNywxOUg3VjVIMTdNMTcsMUg3QzUuODksMSA1LDEuODkgNSwzVjIxQTIsMiAwIDAsMCA3LDIzSDE3QTIsMiAwIDAsMCAxOSwyMVYzQzE5LDEuODkgMTguMSwxIDE3LDFaIiAvPjwvc3ZnPg=="/>
                            </g>
                            
                            <!-- 车端平台 -->
                            <g class="platform car-platform">
                                <rect x="50" y="220" width="100" height="100" rx="12" fill="#3498db"></rect>
                                <text x="100" y="295" font-size="16" text-anchor="middle" fill="white" font-weight="500">车端</text>
                                <image x="70" y="240" width="60" height="40" href="data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHZpZXdCb3g9IjAgMCAyNCAyNCIgZmlsbD0id2hpdGUiPjxwYXRoIGQ9Ik01LDExTDYuNSw2LjVIMTcuNUwxOSwxMU0xNy41LDE2QTEuNSwxLjUgMCAwLDEgMTYsMTQuNUExLjUsMS41IDAgMCwxIDE3LjUsMTNBMS41LDEuNSAwIDAsMSAxOSwxNC41QTEuNSwxLjUgMCAwLDEgMTcuNSwxNk02LjUsMTZBMS41LDEuNSAwIDAsMSA1LDE0LjVBMS41LDEuNSAwIDAsMSA2LjUsMTNBMS41LDEuNSAwIDAsMSA4LDE0LjVBMS41LDEuNSAwIDAsMSA2LjUsMTZNMTguOTIsNkMxOC43Miw1LjQyIDE4LjE2LDUgMTcuNSw1SDYuNUM1Ljg0LDUgNS4yOCw1LjQyIDUuMDgsNkwzLDEyVjIwQTEsMSAwIDAsMCA0LDIxSDVBMSwxIDAgMCwwIDYsMjBWMTlIMThWMjBBMSwxIDAgMCwwIDE5LDIxSDIwQTEsMSAwIDAsMCAyMSwyMFYxMkwxOC45Miw2WiIgLz48L3N2Zz4="/>
                            </g>
                            
                            <!-- 云平台 -->
                            <g class="platform cloud-platform">
                                <rect x="250" y="220" width="100" height="100" rx="12" fill="#9b59b6"></rect>
                                <text x="300" y="295" font-size="16" text-anchor="middle" fill="white" font-weight="500">云平台</text>
                                <image x="270" y="240" width="60" height="40" href="data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHZpZXdCb3g9IjAgMCAyNCAyNCIgZmlsbD0id2hpdGUiPjxwYXRoIGQ9Ik0xOS4zNSwxMC4wM0MxOC42Nyw2LjU5IDE1LjY0LDQgMTIsNEM5LjExLDQgNi42LDUuNjQgNS4zNSw4LjAzQzIuMzQsOC4zNiAwLDEwLjkgMCwxNEE2LDYgMCAwLDAgNiwyMEgxOUE1LDUgMCAwLDAgMjQsMTVDMjQsMTIuMzYgMjEuOTUsMTAuMjIgMTkuMzUsMTAuMDNaIiAvPjwvc3ZnPg=="/>
                            </g>
                            
                            <!-- 连接线 - 手机到车端 -->
                            <line x1="150" y1="130" x2="100" y2="220" stroke="#f1c40f" stroke-width="3"/>
                            <rect x="115" y="180" width="90" height="20" rx="10" fill="white"/>
                            <text x="160" y="195" font-size="10" text-anchor="middle" fill="#555">蓝牙近场通信</text>
                            
                            <!-- 连接线 - 手机到云平台 -->
                            <line x1="250" y1="130" x2="300" y2="220" stroke="#f1c40f" stroke-width="3"/>
                            <rect x="230" y="180" width="90" height="20" rx="10" fill="white"/>
                            <text x="275" y="195" font-size="10" text-anchor="middle" fill="#555">互联网通信</text>
                            <!-- 连接线 - 车端到云平台 -->
                            <line x1="150" y1="270" x2="250" y2="270" stroke="#f1c40f" stroke-width="3"/>
                            <rect x="170" y="250" width="60" height="20" rx="10" fill="white"/>
                            <text x="200" y="265" font-size="10" text-anchor="middle" fill="#555">互联网通信</text>
                        </svg>
                    </div>
                </div>
            </div>
            
            <!-- 底部版权信息 -->
            <div class="footer">
                <!-- 已移除 -->
            </div>
        </div>
    </div>
    
    <div class="toast" id="toast">操作成功！</div>
    
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const copyButton = document.getElementById('copyButton');
            const downloadButton = document.getElementById('downloadButton');
            const toast = document.getElementById('toast');
            const slide = document.getElementById('slide');
            const resolutionBtns = document.querySelectorAll('.resolution-btn');
            const contentCards = document.querySelectorAll('.content-card');
            
            // 为内容卡片添加点击事件
            contentCards.forEach(card => {
                card.addEventListener('click', function() {
                    // 移除所有卡片的active类
                    contentCards.forEach(c => c.classList.remove('active'));
                    // 为当前卡片添加active类
                    this.classList.add('active');
                    
                    // 可以选择显示相应的提示（可选）
                    showToast(`已选择: ${this.querySelector('.content-title').textContent}`);
                });
            });
            
            // 默认清晰度为2x (高清)
            let currentScale = 2;
            
            // 清晰度按钮点击事件
            resolutionBtns.forEach(btn => {
                btn.addEventListener('click', function() {
                    // 移除所有按钮的active类
                    resolutionBtns.forEach(b => b.classList.remove('active'));
                    // 给当前按钮添加active类
                    this.classList.add('active');
                    // 更新当前清晰度
                    currentScale = parseFloat(this.getAttribute('data-scale'));
                    
                    // 显示提示
                    showToast(`清晰度已设置为${this.textContent}模式`);
                });
            });
            
            // 显示提示消息
            function showToast(message) {
                toast.textContent = message;
                toast.style.display = 'block';
                setTimeout(function() {
                    toast.style.display = 'none';
                }, 2000);
            }
            
            // 生成透明背景图片
            function generateImage() {
                return html2canvas(slide, {
                    scale: currentScale, // 使用用户选择的清晰度
                    useCORS: true,
                    allowTaint: true,
                    backgroundColor: null // 关键：设置为null以支持透明背景
                });
            }
            
            // 复制按钮功能
            copyButton.addEventListener('click', function() {
                showToast("正在处理图片，请稍候...");
                
                generateImage().then(function(canvas) {
                    try {
                        // 使用现代剪贴板API
                        canvas.toBlob(function(blob) {
                            try {
                                const item = new ClipboardItem({ 'image/png': blob });
                                navigator.clipboard.write([item]).then(function() {
                                    showToast("已复制到剪贴板！现在可以粘贴到PPT中");
                                }).catch(function(err) {
                                    showToast("自动复制失败，请使用下载按钮获取图片");
                                    console.error("剪贴板API错误:", err);
                                });
                            } catch (e) {
                                showToast("您的浏览器不支持复制图片，请使用下载按钮");
                                console.error("ClipboardItem错误:", e);
                            }
                        });
                    } catch (e) {
                        showToast("复制功能不可用，请使用下载图片功能");
                        console.error("toBlob错误:", e);
                    }
                }).catch(function(err) {
                    showToast("生成图片失败，请尝试下载或截图");
                    console.error("html2canvas错误:", err);
                });
            });
            
            downloadButton.addEventListener('click', function () {
                showToast(`正在生成${currentScale}x清晰度透明PNG图片，请稍候...`);

                generateImage().then(function (canvas) {
                    // 将Canvas转换为PNG并下载
                    const imageUrl = canvas.toDataURL('image/png');
                    const link = document.createElement('a');

                    // 首先从URL路径获取文件名
                    let fileName = "";
                    const pathParts = window.location.pathname.split('/');
                    const htmlFileName = pathParts[pathParts.length - 1];
                    // 移除.html后缀并进行URL解码以支持中文文件名
                    fileName = decodeURIComponent(htmlFileName.replace('.html', ''));

                    // 如果从URL获取的文件名为空，尝试从标题获取
                    if (!fileName) {
                        const titleElement = document.querySelector('.header h1');
                        if (titleElement && titleElement.textContent) {
                            fileName = titleElement.textContent.trim();
                        }
                    }

                    // 确保文件名不为空
                    if (!fileName) {
                        fileName = "测试";
                    }

                    link.download = `${fileName}-透明-${currentScale}x清晰度.png`;
                    link.href = imageUrl;
                    link.click();

                    showToast(`${currentScale}x清晰度透明背景PNG已下载`);
                }).catch(function (err) {
                    showToast("生成图片失败，请尝试截图");
                    console.error("Error:", err);
                });
            });
        });
    </script>
</body>
</html> 
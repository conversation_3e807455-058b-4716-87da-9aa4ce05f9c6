@startuml
skinparam monochrome true

participant "APP" as Mobile
participant "车端（BLE & TBOX）" as CarBT
participant "钥匙云平台" as Cloud
participant "TSP/OEM平台" as ExternalSystem

== 蓝牙配对流程 ==

Mobile -> Mobile : 1. 在APP中选择添加数字钥匙
Mobile -> Mobile : 2. 扫描车辆二维码或输入VIN码识别车辆
Mobile -> Cloud : 3. 发送车辆VIN码，请求获取数字钥匙
Cloud -> Cloud : 4. 根据VIN码生成虚拟密钥（VIRKEY）和唯一配对令牌
Cloud -> Mobile : 5. 下发虚拟密钥和配对令牌到手机
Mobile -> Mobile : 6. 保存虚拟密钥和配对令牌到安全存储区

note over Cloud, ExternalSystem
  钥匙云平台通过安全通道与TSP/OEM平台交互
  获取车辆TBOX信息并建立安全连接
end note

Cloud -> ExternalSystem : 7. 通过统一接口适配层查询VIN码对应的TBOX信息
ExternalSystem -> Cloud : 8. 返回TBOX信息
Cloud -> CarBT : 9. 通过4G连接到车辆TBOX并生成根密钥下发到汽车

note over Cloud, CarBT
  根密钥(Root Key)是整个安全体系的基础
  用于后续蓝牙通信的加密和认证
end note

CarBT -> CarBT : 10. 存储根密钥和配对令牌
CarBT -> Mobile : 11. 发送蓝牙广播（包含MAC、加密VIN码和挑战码等信息）

note over Mobile, CarBT
  蓝牙广播中的加密VIN码只能被持有对应虚拟密钥的手机解密
  确保只有授权手机能识别并连接到正确的车辆
end note

Mobile -> Mobile : 12a. 解密广播中的VIN码并与本地存储的VIN码比对
Mobile -> CarBT : 12b. 根据广播中的加密VIN码，筛出配对车辆并连接到汽车蓝牙模块
Mobile -> CarBT : 13. 发送蓝牙配对请求（包含配对令牌和虚拟密钥派生的证明）
CarBT -> CarBT : 14a. 验证配对令牌的有效性
CarBT -> Mobile : 14b. 发起蓝牙安全请求（包含基于根密钥生成的挑战）

CarBT -> CarBT : 15. 生成配对码（基于根密钥和配对令牌）
Mobile -> Mobile : 16. 生成配对码（基于虚拟密钥和配对令牌）
Mobile -> CarBT : 17. 发送配对码和挑战响应进行双向验证
CarBT -> CarBT : 18a. 验证挑战响应和配对码
CarBT -> Mobile : 18b. 返回验证结果和车端证明

note over Mobile, CarBT
  双向验证确保:
  1. 手机持有正确的虚拟密钥和配对令牌
  2. 车辆持有正确的根密钥和配对令牌
  只有双方都通过验证才能完成配对
end note

== 配对完成与安全通道建立 ==

CarBT -> CarBT : 19. 验证成功，存储以下信息到安全存储区：
              \n- 会话密钥（Session Key）
              \n- 绑定的手机设备ID
              \n- 密钥有效期限
              \n- 授权等级（主/副用户）
Mobile -> Mobile : 20. 验证成功，存储以下信息到手机安全区：
               \n- 会话密钥（Session Key）
               \n- 车辆标识（VIN）
               \n- 蓝牙MAC地址
               \n- 密钥有效期限
               \n- 钥匙权限等级
CarBT <-> Mobile : 21. 建立加密安全通道（基于会话密钥）

note over Mobile, CarBT
  安全通道建立后，所有后续通信都将被加密保护
  确保数据传输的机密性和完整性
end note

Mobile -> Cloud : 22. 上报配对成功状态，包含：设备ID、车辆VIN、配对时间戳
Cloud -> Cloud : 23. 在钥匙管理系统中记录钥匙绑定关系：
             \n- 用户账号与车辆VIN绑定
             \n- 设备标识与钥匙ID绑定
             \n- 钥匙权限等级
             \n- 生效和过期时间
Cloud -> Mobile : 24. 返回数字钥匙ID和完整权限配置信息
Mobile -> Mobile : 25. 显示数字钥匙添加成功，可用于解锁和启动车辆

note over Cloud, CarBT
  完整配对流程结束后各方存储的核心信息:
  1. 手机端: 会话密钥、车辆信息、钥匙ID、权限配置
  2. 车端: 会话密钥、授权设备ID、权限等级
  3. 云平台: 用户-车辆-设备三者关系、钥匙状态管理信息
  
  这些信息将用于:
  - 后续蓝牙重连验证
  - 功能权限控制
  - 远程管理与授权
end note

@enduml
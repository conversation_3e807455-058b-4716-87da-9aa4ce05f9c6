@startuml
skinparam componentStyle rectangle

package "数字钥匙" {
    package "手机端" {
        component "钥匙管理模块" as KeyManage
        component "车辆控制模块" as VehicleCtrl
        component "蓝牙通信模块" as BLEComm
        component "安全存储模块" as SecStorage
        component "时间同步模块" as TimeSync
    }
    
    package "车端" {
        component "BLE通信模块" as CarBLE
        component "钥匙验证模块" as KeyVerify
        component "指令执行模块" as CmdExec
        component "安全存储模块" as CarSecStorage
        component "T-Box通信模块" as TBoxComm
        component "时间同步模块" as CarTimeSync
    }
    
    package "钥匙云平台" {
        component "钥匙生命周期管理" as KeyLifecycle
        note right of KeyLifecycle
          负责钥匙创建、授权、撤销的全生命周期管理；
          包含配对状态管理与事件记录；
          实现配对限制与异常监控机制
        end note
        
        component "VIN码关联服务" as VINService
        component "安全认证中心" as AuthCenter
        note right of AuthCenter
          提供身份认证、证书管理与验证；
          管理配对过程中的挑战-响应验证；
          处理安全事件响应与异常检测
        end note
        
        component "密钥管理系统" as KeyMgmt
        note right of KeyMgmt
          生成并管理虚拟密钥、根密钥与配对令牌；
          实现密钥轮换与版本控制；
          确保密钥材料的安全存储与备份
        end note
        
        component "时间服务器" as TimeServer
        component "统一接口服务" as UnifiedAPI
        note right of UnifiedAPI
          提供标准API接口；
          建立端到端加密通信通道；
          确保消息完整性与防重放保护
        end note
    }
    
    ' 简化外部平台设计
    component "外部平台(TSP/OEM等)" as ExternalPlatforms
    
    database "数据存储接口" {
        database "钥匙数据" as KeysDB
        database "密钥材料" as KeyMaterialsDB
    }
    
    database "安全基础设施" {
        database "密码机/HSM" as HSM
        note right of HSM
          执行所有密钥生成与密码学操作；
          确保密钥永远不会明文离开设备；
          提供物理防篡改保护
        end note
        
        database "PKI/CA系统" as PKICA
        database "密钥材料备份系统" as SecureBackup
        note right of SecureBackup
          安全备份关键密钥材料；
          采用分片存储与多重验证；
          防止单点故障风险
        end note
    }
}

' 使用标记替代直接连线
' 手机端内部关系
KeyManage -[#blue]-> VehicleCtrl : A1
VehicleCtrl -[#blue]-> BLEComm : A2
BLEComm -[#blue]-> SecStorage : A3
SecStorage -[#blue]-> TimeSync : A4

' 车端内部关系
CarBLE -[#green]-> KeyVerify : B1
KeyVerify -[#green]-> CmdExec : B2
CmdExec -[#green]-> CarSecStorage : B3
CarSecStorage -[#green]-> TBoxComm : B4
TBoxComm -[#green]-> CarTimeSync : B5

' 钥匙服务内部关系
KeyLifecycle -[#red]-> VINService : C1
VINService -[#red]-> AuthCenter : C2
AuthCenter -[#red]-> KeyMgmt : C3
KeyMgmt -[#red]-> TimeServer : C4
TimeServer -[#red]-> UnifiedAPI : C5

' 跨系统关系
BLEComm <-[#purple]-> CarBLE : D1
UnifiedAPI <-[#purple]-> TBoxComm : D2
UnifiedAPI <-[#purple]-> KeyManage : D3
TimeServer -[#purple]-> TimeSync : D4
TimeServer -[#purple]-> CarTimeSync : D5

' 数据库关系
KeyLifecycle -[#orange]-> KeysDB : E1
KeyMgmt -[#orange]-> KeyMaterialsDB : E2
AuthCenter -[#orange]-> PKICA : E3
KeyMgmt -[#orange]-> HSM : E4
KeyMgmt -[#orange]-> SecureBackup : E5

' 外部平台与钥匙云平台的关系 - 简化连接
UnifiedAPI <-[#brown]-> ExternalPlatforms : F1

note right of UnifiedAPI
  统一接口服务负责对内与车端TBOX通信，
  下发证书、密钥和控制指令；
  对外与TSP/OEM等平台交互，
  获取车辆信息、用户授权等数据
end note

' 表示关键的关系
KeyMgmt <-[#magenta]-> HSM : G1
AuthCenter <-[#magenta]-> PKICA : G2
KeyLifecycle -[#magenta]-> KeyMgmt : G3
UnifiedAPI -[#magenta]-> AuthCenter : G4

legend
  <b>连接关系说明</b>
  
  <b>A系列</b>：手机端内部模块关系
  A1: 钥匙管理模块向车辆控制模块提供钥匙信息
  A2: 车辆控制模块通过蓝牙通信模块发送控制指令
  A3: 蓝牙通信模块使用安全存储模块中的密钥进行加密通信
  A4: 安全存储模块使用时间同步模块提供的时间戳进行安全验证
  
  <b>B系列</b>：车端内部模块关系
  B1: BLE通信模块接收指令并传递给钥匙验证模块
  B2: 钥匙验证模块验证通过后，将指令传递给指令执行模块
  B3: 指令执行模块执行指令，并将结果存储在安全存储模块
  B4: 安全存储模块与T-Box通信模块交互，上报状态
  B5: T-Box通信模块使用时间同步模块提供的时间戳进行安全验证
  
  <b>C系列</b>：钥匙云平台内部模块关系
  C1: 钥匙生命周期管理向VIN码关联服务提供钥匙信息
  C2: VIN码关联服务向安全认证中心提供车辆信息
  C3: 安全认证中心向密钥管理系统请求密钥材料
  C4: 密钥管理系统向时间服务器请求时间戳
  C5: 时间服务器向统一接口服务提供时间同步服务
  
  <b>D系列</b>：跨系统通信关系
  D1: 手机端蓝牙通信模块与车端BLE通信模块进行蓝牙通信
  D2: 云平台统一接口服务与车端T-Box通信模块进行远程通信
  D3: 云平台统一接口服务与手机端钥匙管理模块进行HTTPS通信
  D4: 云平台时间服务器与手机端时间同步模块进行时间同步
  D5: 云平台时间服务器与车端时间同步模块进行时间同步
  
  <b>E系列</b>：数据存储关系
  E1: 钥匙生命周期管理模块访问钥匙数据库
  E2: 密钥管理系统访问密钥材料数据库
  E3: 安全认证中心访问PKI/CA系统
  E4: 密钥管理系统访问HSM进行密钥操作
  E5: 密钥管理系统将密钥材料备份到备份系统
  
  <b>F系列</b>：外部平台交互
  F1: 统一接口服务与外部平台(TSP/OEM等)进行数据交互和API调用
  
  <b>G系列</b>：关键安全关系
  G1: 密钥管理系统与HSM交互进行密钥生成与操作
  G2: 安全认证中心与PKI/CA系统交互进行证书管理
  G3: 钥匙生命周期管理向密钥管理系统发送钥匙生命周期事件
  G4: 统一接口服务向安全认证中心发送安全验证请求
end legend

@enduml
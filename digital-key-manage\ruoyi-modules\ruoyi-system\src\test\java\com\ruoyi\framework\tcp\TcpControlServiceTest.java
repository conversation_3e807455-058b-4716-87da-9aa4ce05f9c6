package com.ruoyi.framework.tcp;

import org.junit.jupiter.api.Test;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * TCP控制服务单元测试
 * 
 * <AUTHOR>
 */
public class TcpControlServiceTest {
    
    private static final Logger LOGGER = LoggerFactory.getLogger(TcpControlServiceTest.class);
    
    @Test
    public void testProtocolGeneration() {
        LOGGER.info("🧪 开始测试协议生成");
        
        TcpControlService service = new TcpControlService();
        
        // 测试解锁指令生成
        String unlockCommand = service.generateUnlockCommand();
        assert unlockCommand != null : "解锁指令不能为空";
        assert unlockCommand.startsWith("7E") : "解锁指令应以7E开头";
        assert unlockCommand.endsWith("7E") : "解锁指令应以7E结尾";
        assert unlockCommand.contains("04") : "解锁指令应包含状态码04";
        
        LOGGER.info("✅ 解锁指令生成正确: {}", unlockCommand);
        
        // 测试闭锁指令生成
        String lockCommand = service.generateLockCommand();
        assert lockCommand != null : "闭锁指令不能为空";
        assert lockCommand.startsWith("7E") : "闭锁指令应以7E开头";
        assert lockCommand.endsWith("7E") : "闭锁指令应以7E结尾";
        assert lockCommand.contains("03") : "闭锁指令应包含状态码03";
        
        LOGGER.info("✅ 闭锁指令生成正确: {}", lockCommand);
        
        LOGGER.info("✅ 协议生成测试通过");
    }
    
    @Test
    public void testProtocolParsing() {
        LOGGER.info("🧪 开始测试协议解析");
        
        TcpControlService service = new TcpControlService();
        
        // 测试解锁状态解析
        String unlockMessage = "7E000A000A000102030405060708090A000030333032303030313034007E";
        TcpControlService.TboxStatusResult unlockResult = service.parseTboxMessage(unlockMessage);
        
        assert unlockResult.isValid() : "解锁消息应该解析成功";
        assert "unlocked".equals(unlockResult.getLockStatus()) : "解锁状态应该是unlocked";
        assert unlockResult.getStatusDescription().contains("解锁") : "状态描述应该包含解锁";
        
        LOGGER.info("✅ 解锁状态解析正确: {}", unlockResult.getStatusDescription());
        
        // 测试闭锁状态解析
        String lockMessage = "7E000A000A000102030405060708090A000030333032303030313033007E";
        TcpControlService.TboxStatusResult lockResult = service.parseTboxMessage(lockMessage);
        
        assert lockResult.isValid() : "闭锁消息应该解析成功";
        assert "locked".equals(lockResult.getLockStatus()) : "闭锁状态应该是locked";
        assert lockResult.getStatusDescription().contains("闭锁") : "状态描述应该包含闭锁";
        
        LOGGER.info("✅ 闭锁状态解析正确: {}", lockResult.getStatusDescription());
        
        LOGGER.info("✅ 协议解析测试通过");
    }
    
    @Test
    public void testProtocolValidation() {
        LOGGER.info("🧪 开始测试协议验证");
        
        TcpControlService service = new TcpControlService();
        
        // 测试有效协议
        String validProtocol = "7E000A000A000102030405060708090A000030333032303030313034007E";
        assert service.isValidProtocolFormat(validProtocol) : "有效协议应该验证通过";
        
        // 测试无效协议
        assert !service.isValidProtocolFormat("") : "空字符串应该验证失败";
        assert !service.isValidProtocolFormat(null) : "null应该验证失败";
        assert !service.isValidProtocolFormat("invalid") : "无效格式应该验证失败";
        assert !service.isValidProtocolFormat("7E123") : "长度不足应该验证失败";
        
        LOGGER.info("✅ 协议验证测试通过");
    }
    
    @Test
    public void testErrorHandling() {
        LOGGER.info("🧪 开始测试错误处理");
        
        TcpControlService service = new TcpControlService();
        
        // 测试空消息
        TcpControlService.TboxStatusResult emptyResult = service.parseTboxMessage("");
        assert !emptyResult.isValid() : "空消息应该处理失败";
        assert emptyResult.getErrorMessage() != null : "应该有错误信息";
        
        // 测试null消息
        TcpControlService.TboxStatusResult nullResult = service.parseTboxMessage(null);
        assert !nullResult.isValid() : "null消息应该处理失败";
        assert nullResult.getErrorMessage() != null : "应该有错误信息";
        
        // 测试格式错误消息
        TcpControlService.TboxStatusResult formatErrorResult = service.parseTboxMessage("invalid_format");
        assert !formatErrorResult.isValid() : "格式错误消息应该处理失败";
        assert formatErrorResult.getErrorMessage() != null : "应该有错误信息";
        
        LOGGER.info("✅ 错误处理测试通过");
    }
}

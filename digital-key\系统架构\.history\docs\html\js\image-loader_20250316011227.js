/**
 * 图片加载处理脚本
 * 用于处理带有data-html-src属性的图片，实现点击图片可以查看详细内容
 */
document.addEventListener("DOMContentLoaded", function() {
    // 添加模态窗口到页面
    var modal = document.createElement('div');
    modal.className = 'content-modal';
    modal.innerHTML = `
        <div class="modal-content">
            <span class="close-modal">&times;</span>
            <div class="modal-body">
                <iframe id="content-frame" src="" width="100%" height="100%"></iframe>
            </div>
        </div>
    `;
    document.body.appendChild(modal);
    
    // 添加模态窗口样式
    var style = document.createElement('style');
    style.textContent = `
        .content-modal {
            display: none;
            position: fixed;
            z-index: 1001;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            overflow: auto;
            background-color: rgba(0,0,0,0.7);
        }
        .modal-content {
            position: relative;
            background-color: #fefefe;
            margin: 2% auto;
            padding: 20px;
            border-radius: 10px;
            width: 90%;
            height: 90%;
            box-shadow: 0 4px 8px rgba(0,0,0,0.2);
            animation: modalopen 0.4s;
        }
        @keyframes modalopen {
            from {opacity: 0; transform: translateY(-30px);}
            to {opacity: 1; transform: translateY(0);}
        }
        .close-modal {
            color: #aaa;
            float: right;
            font-size: 28px;
            font-weight: bold;
            cursor: pointer;
            position: absolute;
            right: 20px;
            top: 10px;
            z-index: 1002;
        }
        .close-modal:hover {
            color: #000;
        }
        .modal-body {
            height: calc(100% - 40px);
            padding-top: 30px;
        }
        #content-frame {
            border: none;
            border-radius: 5px;
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
        }
    `;
    document.head.appendChild(style);
    
    // 关闭按钮事件
    var closeBtn = document.querySelector('.close-modal');
    closeBtn.addEventListener('click', function() {
        document.querySelector('.content-modal').style.display = 'none';
        document.getElementById('content-frame').src = '';
    });
    
    // 点击模态窗口外部关闭
    window.addEventListener('click', function(event) {
        if (event.target == modal) {
            modal.style.display = 'none';
            document.getElementById('content-frame').src = '';
        }
    });
    
    // 选择所有带有data-html-src属性的图片
    var images = document.querySelectorAll("img[data-html-src]");
    
    // 为每个图片设置点击事件
    images.forEach(function(img) {
        // 创建一个占位SVG图
        var title = img.alt || "点击查看详细内容";
        var bgColor = "#f8f9fa";
        var textColor = "#3498db";
        
        // 设置不同类型图表的颜色
        if (img.alt.includes("配对")) {
            bgColor = "#eefbf5";
            textColor = "#2ecc71";
        } else if (img.alt.includes("无感")) {
            bgColor = "#f5eef8";
            textColor = "#9b59b6";
        }
        
        var svgImage = 'data:image/svg+xml;charset=utf-8,' + encodeURIComponent('<svg xmlns="http://www.w3.org/2000/svg" width="800" height="400" viewBox="0 0 800 400"><rect width="800" height="400" fill="' + bgColor + '"/><text x="400" y="200" text-anchor="middle" font-family="Arial, sans-serif" font-size="20" fill="' + textColor + '">' + title + ' - 点击查看详细内容</text></svg>');
        
        // 设置图片源为SVG
        img.src = svgImage;
        
        // 添加鼠标悬停时的提示
        img.title = "点击查看详细内容";
        
        // 设置鼠标样式为手型，表示可点击
        img.style.cursor = "pointer";
        
        // 添加点击事件处理器
        img.addEventListener("click", function() {
            // 在模态窗口中显示内容
            var contentFrame = document.getElementById('content-frame');
            contentFrame.src = this.getAttribute("data-html-src");
            document.querySelector('.content-modal').style.display = 'block';
        });
    });
    
    // 淡入动画效果
    var fadeInElements = document.querySelectorAll(".fade-in");
    
    function checkVisibility() {
        fadeInElements.forEach(function(element) {
            var rect = element.getBoundingClientRect();
            var windowHeight = window.innerHeight;
            
            // 当元素进入视口时应用动画
            if (rect.top < windowHeight * 0.9) {
                element.classList.add("visible");
            }
        });
    }
    
    // 确保初始内容可见
    setTimeout(function() {
        // 将所有元素设置为可见
        fadeInElements.forEach(function(element) {
            element.classList.add("visible");
        });
    }, 100);
    
    // 添加可见时的类
    document.addEventListener("scroll", checkVisibility);
    window.addEventListener("resize", checkVisibility);
    
    // 立即检查初始可见性
    checkVisibility();
}); 
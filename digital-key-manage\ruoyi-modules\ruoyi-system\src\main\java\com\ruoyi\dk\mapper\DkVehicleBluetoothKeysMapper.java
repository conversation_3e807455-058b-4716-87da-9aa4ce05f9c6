package com.ruoyi.dk.mapper;

import java.util.List;
import com.ruoyi.dk.domain.DkVehicleBluetoothKeys;
import org.apache.ibatis.annotations.Param;

/**
 * 蓝牙密钥Mapper接口
 * 
 * <AUTHOR>
 * @date 2024-05-14
 */
public interface DkVehicleBluetoothKeysMapper 
{
    /**
     * 查询蓝牙密钥
     * 
     * @param userId 蓝牙密钥主键
     * @return 蓝牙密钥
     */
    public DkVehicleBluetoothKeys selectDkVehicleBluetoothKeysByUserId(Long userId);

    /**
     * 查询蓝牙密钥列表
     * 
     * @param dkVehicleBluetoothKeys 蓝牙密钥
     * @return 蓝牙密钥集合
     */
    public List<DkVehicleBluetoothKeys> selectDkVehicleBluetoothKeysList(DkVehicleBluetoothKeys dkVehicleBluetoothKeys);

    /**
     * 新增蓝牙密钥
     * 
     * @param dkVehicleBluetoothKeys 蓝牙密钥
     * @return 结果
     */
    public int insertDkVehicleBluetoothKeys(DkVehicleBluetoothKeys dkVehicleBluetoothKeys);

    /**
     * 修改蓝牙密钥
     * 
     * @param dkVehicleBluetoothKeys 蓝牙密钥
     * @return 结果
     */
    public int updateDkVehicleBluetoothKeys(DkVehicleBluetoothKeys dkVehicleBluetoothKeys);

    /**
     * 删除蓝牙密钥
     * 
     * @param userId 蓝牙密钥主键
     * @return 结果
     */
    public int deleteDkVehicleBluetoothKeysByUserId(Long userId);

    /**
     * 批量删除蓝牙密钥
     * 
     * @param userIds 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteDkVehicleBluetoothKeysByUserIds(Long[] userIds);

    /**
     * 解绑用户的VIN码
     * 
     * @param userId 用户ID
     * @param vinCodes 需要解绑的VIN码集合
     * @return 结果
     */
    public int unbindVinCodes(@Param("userId") Long userId, @Param("vinCodes") String[] vinCodes);
}

<template>
  <view class="container bg-cls">
    <!-- 自定义导航栏 -->
    <view class="custom-navbar">
      <text class="navbar-title">个人信息</text>
    </view>

    <!-- 页面内容 -->
    <view class="userInfo-container">
      <uni-list>
        <uni-list-item showExtraIcon="true" :extraIcon="{type: 'person-filled'}" title="昵称" :rightText="user.nickName" />
        <!-- <uni-list-item showExtraIcon="true" :extraIcon="{type: 'phone-filled'}" title="手机号码" :rightText="user.phonenumber" /> -->
        <uni-list-item showExtraIcon="true" :extraIcon="{type: 'email-filled'}" title="邮箱" :rightText="user.email" />
        <!-- <uni-list-item showExtraIcon="true" :extraIcon="{type: 'auth-filled'}" title="岗位" :rightText="postGroup" /> -->
        <!-- <uni-list-item showExtraIcon="true" :extraIcon="{type: 'staff-filled'}" title="角色" :rightText="roleGroup" /> -->
        <uni-list-item showExtraIcon="true" :extraIcon="{type: 'calendar-filled'}" title="注册日期" :rightText="user.createTime" />
      </uni-list>
    </view>
  </view>
</template>

<script>
  import { getUserProfile } from "@/api/system/user"

  export default {
    data() {
      return {
        user: {},
        roleGroup: "",
        postGroup: ""
      }
    },
    onLoad() {
      this.getUser()
    },
    methods: {
      getUser() {
        getUserProfile().then(response => {
          this.user = response.data
          this.roleGroup = response.roleGroup
          this.postGroup = response.postGroup
        })
      }
    }
  }
</script>

<style lang="scss">
  page {
    /* 设置背景固定，不随页面滚动 */
    background-attachment: fixed;

    /* 其他样式，比如设置高度 */
    height: 70vh;
    /* 设置高度为视口高度 */
  }


  .uni-list, .uni-list-item {
    background-color: #ff000000 !important;
  }

  .uni-list-item {
    border-bottom-style: solid;
    border-bottom-width: 1px;
    border-bottom-color: #b6b6b76b;
  }

  .custom-navbar {
    display: flex;
    justify-content: center;
    align-items: center;
    height: 184rpx; /* 导航栏高度 */
    border-radius: 0rpx 0rpx 0rpx 0rpx;
    background-color: rgba(0, 0, 0, 0); /* 自定义背景色 */
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    z-index: 999;
  }

  .navbar-title {
    margin-top: 75rpx;
    color: #121010; /* 标题颜色 */
  }

  .userInfo-container {
    padding-top: 184rpx;
    width: 100%;
    height: 100%;
  }
</style>

<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>数字车钥匙分享功能</title>
    <!-- 引入html2canvas库 -->
    <script src="https://html2canvas.hertzen.com/dist/html2canvas.min.js"></script>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
            font-family: 'PingFang SC', 'Microsoft YaHei', sans-serif;
        }
        
        body {
            background: #f0f0f0;
            display: flex;
            justify-content: center;
            align-items: center;
            min-height: 100vh;
            color: #333;
            padding: 20px;
        }
        
        /* PPT边框指示器 */
        .ppt-container {
            position: relative;
            width: 1920px;
            height: 1080px;
            box-shadow: 0 0 30px rgba(0, 0, 0, 0.2);
            border: 2px dashed #ccc;
            /* 添加棋盘格背景以显示透明度 */
            background-image: linear-gradient(45deg, #ccc 25%, transparent 25%), 
                              linear-gradient(-45deg, #ccc 25%, transparent 25%), 
                              linear-gradient(45deg, transparent 75%, #ccc 75%), 
                              linear-gradient(-45deg, transparent 75%, #ccc 75%);
            background-size: 20px 20px;
            background-position: 0 0, 0 10px, 10px -10px, -10px 0px;
        }
        
        .ppt-label {
            position: absolute;
            top: -40px;
            left: 0;
            background-color: #555;
            color: white;
            padding: 5px 15px;
            border-radius: 4px;
            font-size: 16px;
        }
        
        /* 复制按钮 */
        .copy-button {
            position: absolute;
            top: 20px;
            right: 20px;
            background: linear-gradient(135deg, #3498db, #2980b9);
            color: white;
            border: none;
            padding: 12px 25px;
            border-radius: 30px;
            font-size: 18px;
            cursor: pointer;
            z-index: 1000;
            box-shadow: 0 4px 10px rgba(0, 0, 0, 0.2);
            display: flex;
            align-items: center;
            gap: 10px;
            transition: all 0.3s ease;
        }
        
        /* 下载按钮 */
        .download-button {
            position: absolute;
            top: 20px;
            right: 230px;
            background: linear-gradient(135deg, #2ecc71, #27ae60);
            color: white;
            border: none;
            padding: 12px 25px;
            border-radius: 30px;
            font-size: 18px;
            cursor: pointer;
            z-index: 1000;
            box-shadow: 0 4px 10px rgba(0, 0, 0, 0.2);
            display: flex;
            align-items: center;
            gap: 10px;
            transition: all 0.3s ease;
        }
        
        .copy-button:hover, .download-button:hover {
            transform: translateY(-3px);
            box-shadow: 0 6px 15px rgba(0, 0, 0, 0.25);
        }
        
        .copy-button:active, .download-button:active {
            transform: translateY(0);
        }
        
        .copy-icon {
            width: 20px;
            height: 20px;
            background-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="white"><path d="M19,21H8V7H19M19,5H8A2,2 0 0,0 6,7V21A2,2 0 0,0 8,23H19A2,2 0 0,0 21,21V7A2,2 0 0,0 19,5M16,1H4A2,2 0 0,0 2,3V17H4V3H16V1Z" /></svg>');
            background-size: contain;
            background-repeat: no-repeat;
        }
        
        .download-icon {
            width: 20px;
            height: 20px;
            background-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="white"><path d="M5,20H19V18H5M19,9H15V3H9V9H5L12,16L19,9Z" /></svg>');
            background-size: contain;
            background-repeat: no-repeat;
        }
        
        .slide {
            width: 100%;
            height: 100%;
            background-color: transparent; /* 透明背景 */
            padding: 60px;
            display: flex;
            flex-direction: column;
            overflow: hidden;
            position: relative;
        }
        
        .slide-header {
            text-align: center;
            margin-bottom: 60px;
            position: relative;
        }
        
        .slide-header h1 {
            font-size: 64px;
            color: #2c3e50;
            margin-bottom: 20px;
            font-weight: 600;
            position: relative;
            display: inline-block;
        }
        
        .slide-header h1::after {
            content: '';
            position: absolute;
            bottom: -10px;
            left: 50%;
            transform: translateX(-50%);
            width: 100px;
            height: 4px;
            background: linear-gradient(90deg, #3498db, #2980b9);
            border-radius: 2px;
        }
        
        .slide-header h3 {
            font-size: 32px;
            color: #7f8c8d;
            font-weight: 400;
        }
        
        .slide-content {
            display: flex;
            flex: 1;
            gap: 60px;
        }
        
        .section {
            flex: 1;
            border-radius: 12px;
            padding: 40px;
            display: flex;
            flex-direction: column;
            position: relative;
            overflow: hidden;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.08);
        }
        
        .section-left {
            background-color: rgba(240, 247, 255, 0.9); /* 调整透明度 */
        }
        
        .section-right {
            background-color: rgba(255, 248, 240, 0.9); /* 调整透明度 */
        }
        
        .section::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 6px;
        }
        
        .section-left::before {
            background: linear-gradient(90deg, #3498db, #2980b9);
        }
        
        .section-right::before {
            background: linear-gradient(90deg, #e67e22, #d35400);
        }
        
        .section h2 {
            font-size: 42px;
            color: #2c3e50;
            margin-bottom: 40px;
            padding-bottom: 15px;
            position: relative;
            display: inline-block;
        }
        
        .section-left h2 {
            color: #2980b9;
        }
        
        .section-right h2 {
            color: #d35400;
        }
        
        .section h2::after {
            content: '';
            position: absolute;
            bottom: 0;
            left: 0;
            width: 60px;
            height: 3px;
            border-radius: 2px;
        }
        
        .section-left h2::after {
            background-color: #3498db;
        }
        
        .section-right h2::after {
            background-color: #e67e22;
        }
        
        .section-content {
            flex: 1;
            display: flex;
            flex-direction: column;
        }
        
        /* 改进流程步骤连线样式 */
        .flow-steps {
            list-style: none;
            position: relative;
            display: flex;
            flex-direction: column;
            height: 100%;
            padding-left: 30px;
        }
        
        .flow-steps::before {
            content: '';
            position: absolute;
            left: 19px;
            top: 25px;
            width: 4px;
            height: calc(100% - 80px);
            background: linear-gradient(to bottom, #3498db, #2980b9);
            border-radius: 2px;
            z-index: 1;
        }
        
        .flow-steps li {
            margin-bottom: 35px;
            padding-left: 60px;
            position: relative;
            line-height: 1.4;
            font-size: 28px;
            z-index: 2;
            color: #34495e;
            display: flex;
            align-items: center;
        }
        
        .flow-steps li::before {
            content: attr(data-step);
            position: absolute;
            left: 0;
            top: 50%;
            transform: translateY(-50%);
            display: flex;
            justify-content: center;
            align-items: center;
            width: 42px;
            height: 42px;
            background: linear-gradient(135deg, #3498db, #2980b9);
            color: white;
            border-radius: 50%;
            font-size: 22px;
            font-weight: bold;
            z-index: 3;
            box-shadow: 0 4px 10px rgba(52, 152, 219, 0.3);
        }
        
        .flow-steps li::after {
            content: '';
            position: absolute;
            left: 21px;
            top: 50%;
            transform: translateY(-50%);
            width: 80px;
            height: 2px;
            background-color: rgba(52, 152, 219, 0.2);
            z-index: 0;
        }
        
        /* 改进安全要点块状样式 */
        .security-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 30px;
            height: 100%;
        }
        
        .security-item {
            background-color: white;
            border-radius: 10px;
            padding: 25px 30px;
            font-size: 28px;
            display: flex;
            align-items: center;
            position: relative;
            box-shadow: 0 8px 20px rgba(0, 0, 0, 0.05);
            color: #34495e;
            transition: all 0.3s ease;
            border-left: 0;
            overflow: hidden;
        }
        
        .security-item::before {
            content: '';
            position: absolute;
            left: 0;
            top: 0;
            height: 100%;
            width: 6px;
            background: linear-gradient(to bottom, #e67e22, #d35400);
            border-radius: 3px 0 0 3px;
        }
        
        .security-item::after {
            content: "•";
            color: #e67e22;
            font-size: 40px;
            margin-right: 15px;
            line-height: 0;
        }
        
        .security-item:hover {
            transform: translateY(-5px);
            box-shadow: 0 12px 25px rgba(0, 0, 0, 0.08);
        }
        
        /* 底部图标优化 */
        .icon-container {
            position: absolute;
            bottom: 60px;
            right: 60px;
            display: flex;
            gap: 25px;
        }
        
        .icon {
            width: 80px;
            height: 80px;
            background-size: contain;
            background-repeat: no-repeat;
            background-position: center;
            opacity: 0.6;
            transition: opacity 0.3s, transform 0.3s;
        }
        
        .icon:hover {
            opacity: 0.8;
            transform: scale(1.1);
        }
        
        .icon-car {
            width: 100px;
            height: 100px;
            background-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="%232c3e50"><path d="M5,11L6.5,6.5H17.5L19,11M17.5,16A1.5,1.5 0 0,1 16,14.5A1.5,1.5 0 0,1 17.5,13A1.5,1.5 0 0,1 19,14.5A1.5,1.5 0 0,1 17.5,16M6.5,16A1.5,1.5 0 0,1 5,14.5A1.5,1.5 0 0,1 6.5,13A1.5,1.5 0 0,1 8,14.5A1.5,1.5 0 0,1 6.5,16M18.92,6C18.72,5.42 18.16,5 17.5,5H6.5C5.84,5 5.28,5.42 5.08,6L3,12V20A1,1 0 0,0 4,21H5A1,1 0 0,0 6,20V19H18V20A1,1 0 0,0 19,21H20A1,1 0 0,0 21,20V12L18.92,6Z" /></svg>');
        }
        
        .icon-key {
            background-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="%23e67e22"><path d="M7,14A2,2 0 0,1 5,12A2,2 0 0,1 7,10A2,2 0 0,1 9,12A2,2 0 0,1 7,14M12.65,10C11.83,7.67 9.61,6 7,6A6,6 0 0,0 1,12A6,6 0 0,0 7,18C9.61,18 11.83,16.33 12.65,14H17V18H21V14H23V10H12.65Z" /></svg>');
        }
        
        .icon-security {
            background-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="%233498db"><path d="M12,1L3,5V11C3,16.55 6.84,21.74 12,23C17.16,21.74 21,16.55 21,11V5L12,1M12,5A3,3 0 0,1 15,8A3,3 0 0,1 12,11A3,3 0 0,1 9,8A3,3 0 0,1 12,5M17.13,17C15.92,18.85 14.11,20.24 12,20.92C9.89,20.24 8.08,18.85 6.87,17C6.53,16.5 6.24,16 6,15.47C6,13.82 8.71,12.47 12,12.47C15.29,12.47 18,13.79 18,15.47C17.76,16 17.47,16.5 17.13,17Z" /></svg>');
        }
        
        /* 装饰元素 */
        .decoration {
            position: absolute;
            opacity: 0.03;
            z-index: 0;
        }
        
        .decoration-left {
            width: 300px;
            height: 300px;
            background-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="%233498db"><path d="M7,14A2,2 0 0,1 5,12A2,2 0 0,1 7,10A2,2 0 0,1 9,12A2,2 0 0,1 7,14M12.65,10C11.83,7.67 9.61,6 7,6A6,6 0 0,0 1,12A6,6 0 0,0 7,18C9.61,18 11.83,16.33 12.65,14H17V18H21V14H23V10H12.65Z" /></svg>');
            bottom: -50px;
            left: -50px;
            transform: rotate(-20deg);
        }
        
        .decoration-right {
            width: 300px;
            height: 300px;
            background-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="%23e67e22"><path d="M12,1L3,5V11C3,16.55 6.84,21.74 12,23C17.16,21.74 21,16.55 21,11V5L12,1M12,5A3,3 0 0,1 15,8A3,3 0 0,1 12,11A3,3 0 0,1 9,8A3,3 0 0,1 12,5M17.13,17C15.92,18.85 14.11,20.24 12,20.92C9.89,20.24 8.08,18.85 6.87,17C6.53,16.5 6.24,16 6,15.47C6,13.82 8.71,12.47 12,12.47C15.29,12.47 18,13.79 18,15.47C17.76,16 17.47,16.5 17.13,17Z" /></svg>');
            top: -50px;
            right: -50px;
            transform: rotate(20deg);
        }
        
        /* 消息提示 */
        .toast {
            position: fixed;
            top: 50px;
            left: 50%;
            transform: translateX(-50%);
            background-color: rgba(0, 0, 0, 0.8);
            color: white;
            padding: 12px 25px;
            border-radius: 30px;
            font-size: 16px;
            z-index: 2000;
            display: none;
            animation: fadeInOut 2s ease;
        }
        
        @keyframes fadeInOut {
            0% { opacity: 0; }
            20% { opacity: 1; }
            80% { opacity: 1; }
            100% { opacity: 0; }
        }
    </style>
</head>
<body>
    <div class="ppt-container" id="pptContainer">
        <div class="ppt-label">PPT尺寸(1920×1080) - 透明背景</div>
        <button class="copy-button" id="copyButton">
            <span class="copy-icon"></span>复制为PPT图片
        </button>
        <button class="download-button" id="downloadButton">
            <span class="download-icon"></span>下载透明PNG
        </button>
        
        <div class="slide" id="slide">
            <div class="decoration decoration-left"></div>
            <div class="decoration decoration-right"></div>
            
            <div class="slide-header">
                <h1>数字车钥匙分享功能</h1>
                <h3>安全、便捷的智能车辆使用权限管理</h3>
            </div>
            
            <div class="slide-content">
                <div class="section section-left">
                    <h2>用户操作流程</h2>
                    <div class="section-content">
                        <ul class="flow-steps">
                            <li data-step="1">车主打开APP，点击"钥匙分享"</li>
                            <li data-step="2">设置权限和有效期</li>
                            <li data-step="3">生成分享二维码</li>
                            <li data-step="4">接收方打开APP，扫描二维码</li>
                            <li data-step="5">车主确认分享请求</li>
                            <li data-step="6">接收方获得数字钥匙</li>
                            <li data-step="7">接收方使用钥匙控制车辆</li>
                            <li data-step="8">车主可随时查看或撤销分享</li>
                        </ul>
                    </div>
                </div>
                
                <div class="section section-right">
                    <h2>安全设计要点</h2>
                    <div class="section-content">
                        <div class="security-grid">
                            <div class="security-item">分享前需身份二次验证</div>
                            <div class="security-item">二维码加密且定时刷新</div>
                            <div class="security-item">严格的权限隔离机制</div>
                            <div class="security-item">完整的使用记录追踪</div>
                            <div class="security-item">一键紧急撤销功能</div>
                            <div class="security-item">离线状态下权限仍有效</div>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="icon-container">
                <div class="icon icon-car"></div>
                <div class="icon icon-key"></div>
                <div class="icon icon-security"></div>
            </div>
        </div>
    </div>
    
    <div class="toast" id="toast">操作成功！</div>
    
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const copyButton = document.getElementById('copyButton');
            const downloadButton = document.getElementById('downloadButton');
            const toast = document.getElementById('toast');
            const slide = document.getElementById('slide');
            
            // 显示提示消息
            function showToast(message) {
                toast.textContent = message;
                toast.style.display = 'block';
                setTimeout(function() {
                    toast.style.display = 'none';
                }, 2000);
            }
            
            // 生成透明背景图片
            function generateImage() {
                return html2canvas(slide, {
                    scale: 2, // 提高分辨率
                    useCORS: true,
                    allowTaint: true,
                    backgroundColor: null // 关键：设置为null以支持透明背景
                });
            }
            
            // 复制按钮功能
            copyButton.addEventListener('click', function() {
                showToast("正在处理图片，请稍候...");
                
                generateImage().then(function(canvas) {
                    try {
                        // 使用现代剪贴板API
                        canvas.toBlob(function(blob) {
                            try {
                                const item = new ClipboardItem({ 'image/png': blob });
                                navigator.clipboard.write([item]).then(function() {
                                    showToast("已复制到剪贴板！现在可以粘贴到PPT中");
                                }).catch(function(err) {
                                    showToast("自动复制失败，请使用下载按钮获取图片");
                                    console.error("剪贴板API错误:", err);
                                });
                            } catch (e) {
                                showToast("您的浏览器不支持复制图片，请使用下载按钮");
                                console.error("ClipboardItem错误:", e);
                            }
                        });
                    } catch (e) {
                        showToast("复制功能不可用，请使用下载图片功能");
                        console.error("toBlob错误:", e);
                    }
                }).catch(function(err) {
                    showToast("生成图片失败，请尝试下载或截图");
                    console.error("html2canvas错误:", err);
                });
            });
            
            // 下载按钮功能
            downloadButton.addEventListener('click', function() {
                showToast("正在生成透明PNG图片，请稍候...");
                
                generateImage().then(function(canvas) {
                    // 将Canvas转换为PNG并下载
                    const imageUrl = canvas.toDataURL('image/png');
                    const link = document.createElement('a');
                    link.download = '数字车钥匙分享功能-透明.png';
                    link.href = imageUrl;
                    link.click();
                    
                    showToast("透明背景PNG已下载，可直接插入PPT");
                }).catch(function(err) {
                    showToast("生成图片失败，请尝试截图");
                    console.error("Error:", err);
                });
            });
        });
    </script>
</body>
</html> 
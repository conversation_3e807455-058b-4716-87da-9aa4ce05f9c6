<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>钥匙云平台</title>
    <!-- 引入html2canvas库 -->
    <script src="https://html2canvas.hertzen.com/dist/html2canvas.min.js"></script>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
            font-family: 'PingFang SC', 'Microsoft YaHei', sans-serif;
        }
        
        body {
            background: #f0f0f0;
            display: flex;
            justify-content: center;
            align-items: center;
            min-height: 100vh;
            color: #333;
            padding: 20px;
        }
        
        .ppt-container {
            position: relative;
            width: 1920px; /* 标准PPT宽度 */
            height: 1080px; /* 标准PPT高度 */
            box-shadow: 0 0 30px rgba(0, 0, 0, 0.2);
            border: 2px dashed #ccc;
            background-color: white;
        }
        
        .ppt-label {
            position: absolute;
            top: -40px;
            left: 0;
            background-color: #555;
            color: white;
            padding: 5px 15px;
            border-radius: 4px;
            font-size: 16px;
        }
        
        /* 按钮样式 */
        .copy-button {
            position: absolute;
            top: 20px;
            right: 20px;
            background: linear-gradient(135deg, #3498db, #2980b9);
            color: white;
            border: none;
            padding: 12px 25px;
            border-radius: 30px;
            font-size: 18px;
            cursor: pointer;
            z-index: 1000;
            box-shadow: 0 4px 10px rgba(0, 0, 0, 0.2);
            display: flex;
            align-items: center;
            gap: 10px;
            transition: all 0.3s ease;
        }
        
        .download-button {
            position: absolute;
            top: 20px;
            right: 230px;
            background: linear-gradient(135deg, #2ecc71, #27ae60);
            color: white;
            border: none;
            padding: 12px 25px;
            border-radius: 30px;
            font-size: 18px;
            cursor: pointer;
            z-index: 1000;
            box-shadow: 0 4px 10px rgba(0, 0, 0, 0.2);
            display: flex;
            align-items: center;
            gap: 10px;
            transition: all 0.3s ease;
        }
        
        .resolution-selector {
            position: absolute;
            top: 70px;
            right: 230px;
            display: flex;
            align-items: center;
            background: rgba(255, 255, 255, 0.9);
            padding: 8px 15px;
            border-radius: 30px;
            box-shadow: 0 4px 10px rgba(0, 0, 0, 0.1);
            z-index: 1000;
        }
        
        .copy-icon {
            width: 20px;
            height: 20px;
            background-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="white"><path d="M19,21H8V7H19M19,5H8A2,2 0 0,0 6,7V21A2,2 0 0,0 8,23H19A2,2 0 0,0 21,21V7A2,2 0 0,0 19,5M16,1H4A2,2 0 0,0 2,3V17H4V3H16V1Z" /></svg>');
            background-size: contain;
            background-repeat: no-repeat;
        }
        
        .download-icon {
            width: 20px;
            height: 20px;
            background-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="white"><path d="M5,20H19V18H5M19,9H15V3H9V9H5L12,16L19,9Z" /></svg>');
            background-size: contain;
            background-repeat: no-repeat;
        }
        
        .resolution-label {
            margin-right: 10px;
            font-size: 14px;
            color: #555;
        }
        
        .resolution-btn {
            border: none;
            background: #f0f0f0;
            padding: 5px 10px;
            margin: 0 3px;
            border-radius: 15px;
            font-size: 14px;
            cursor: pointer;
            transition: all 0.2s ease;
            color: #555;
        }
        
        .resolution-btn:hover {
            background: #e0e0e0;
        }
        
        .resolution-btn.active {
            background: linear-gradient(135deg, #3498db, #2980b9);
            color: white;
        }
        
        /* 滑动内容区 */
        .slide {
            width: 100%;
            height: 100%;
            background-color: transparent;
            padding: 40px;
            position: relative;
        }
        
        /* 标题样式 */
        .header {
            text-align: center;
            margin-bottom: 40px;
        }
        
        .header h1 {
            font-size: 48px;
            color: #2c3e50;
            margin-bottom: 15px;
            font-weight: 700;
        }
        
        .header .subtitle {
            font-size: 24px;
            color: #7f8c8d;
            font-weight: 400;
        }
        
        /* 架构图容器 */
        #architecture-svg {
            width: 100%;
            height: 750px;
            display: block;
            margin: 0 auto;
        }
        
        /* 使SVG文本可选择 */
        .svg-text {
            -webkit-user-select: none;
            -moz-user-select: none;
            -ms-user-select: none;
            user-select: none;
        }
        
        /* 底部说明 */
        .footer {
            position: absolute;
            bottom: 20px;
            width: 100%;
            text-align: center;
            font-size: 16px;
            color: #95a5a6;
            left: 0;
        }
        
        .toast {
            position: fixed;
            top: 50px;
            left: 50%;
            transform: translateX(-50%);
            background-color: rgba(0, 0, 0, 0.8);
            color: white;
            padding: 12px 25px;
            border-radius: 30px;
            font-size: 16px;
            z-index: 2000;
            display: none;
            animation: fadeInOut 2s ease;
        }
        
        @keyframes fadeInOut {
            0% { opacity: 0; }
            20% { opacity: 1; }
            80% { opacity: 1; }
            100% { opacity: 0; }
        }
    </style>
</head>
<body>
    <div class="ppt-container" id="pptContainer">
        <div class="ppt-label">PPT尺寸(1920×1080) - 透明背景</div>
        <button class="copy-button" id="copyButton">
            <span class="copy-icon"></span>复制为PPT图片
        </button>
        <button class="download-button" id="downloadButton">
            <span class="download-icon"></span>下载透明PNG
        </button>
        
        <!-- 添加清晰度选择按钮组 -->
        <div class="resolution-selector" id="resolutionSelector">
            <span class="resolution-label">清晰度:</span>
            <button class="resolution-btn" data-scale="1">标准</button>
            <button class="resolution-btn active" data-scale="2">高清</button>
            <button class="resolution-btn" data-scale="3">超清</button>
        </div>
        
        <div class="slide" id="slide">
            <!-- 顶部标题 -->
            <div class="header">
                <h1>数字钥匙三端架构图</h1>
                <div class="subtitle">车辆数字钥匙系统技术架构与通信流程</div>
            </div>
            
            <!-- 使用SVG绘制架构图 -->
            <svg id="architecture-svg" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1800 750">
                <!-- 定义箭头标记 -->
                <defs>
                    <marker id="arrow-blue" markerWidth="10" markerHeight="7" refX="0" refY="3.5" orient="auto">
                        <polygon points="0 0, 10 3.5, 0 7" fill="#2196F3"/>
                    </marker>
                    <marker id="arrow-green" markerWidth="10" markerHeight="7" refX="0" refY="3.5" orient="auto">
                        <polygon points="0 0, 10 3.5, 0 7" fill="#4CAF50"/>
                    </marker>
                    <marker id="arrow-red" markerWidth="10" markerHeight="7" refX="0" refY="3.5" orient="auto">
                        <polygon points="0 0, 10 3.5, 0 7" fill="#F44336"/>
                    </marker>
                </defs>
                
                <!-- 云平台区域 -->
                <rect x="100" y="50" width="1600" height="150" rx="8" fill="#E3F2FD" stroke="#2196F3" stroke-width="2"/>
                <rect x="100" y="50" width="1600" height="50" rx="8" fill="#2196F3"/>
                <text x="900" y="82" text-anchor="middle" fill="white" font-size="24" font-weight="bold" class="svg-text">钥匙云平台</text>
                
                <!-- 云平台模块 -->
                <rect x="180" y="120" width="200" height="60" rx="5" fill="white" stroke="#BBDEFB" stroke-width="1"/>
                <text x="280" y="155" text-anchor="middle" fill="#1565C0" font-size="16" class="svg-text">钥匙生命周期管理</text>
                
                <rect x="400" y="120" width="200" height="60" rx="5" fill="white" stroke="#BBDEFB" stroke-width="1"/>
                <text x="500" y="155" text-anchor="middle" fill="#1565C0" font-size="16" class="svg-text">安全认证中心</text>
                
                <rect x="620" y="120" width="200" height="60" rx="5" fill="white" stroke="#BBDEFB" stroke-width="1"/>
                <text x="720" y="155" text-anchor="middle" fill="#1565C0" font-size="16" class="svg-text">密钥管理系统</text>
                
                <rect x="840" y="120" width="200" height="60" rx="5" fill="white" stroke="#BBDEFB" stroke-width="1"/>
                <text x="940" y="155" text-anchor="middle" fill="#1565C0" font-size="16" class="svg-text">统一接口服务</text>
                
                <rect x="1060" y="120" width="200" height="60" rx="5" fill="white" stroke="#BBDEFB" stroke-width="1"/>
                <text x="1160" y="155" text-anchor="middle" fill="#1565C0" font-size="16" class="svg-text">安全通信通道</text>
                
                <rect x="1280" y="120" width="200" height="60" rx="5" fill="white" stroke="#BBDEFB" stroke-width="1"/>
                <text x="1380" y="155" text-anchor="middle" fill="#1565C0" font-size="16" class="svg-text">时间同步服务</text>
                
                <!-- 手机端区域 -->
                <rect x="625" y="300" width="550" height="300" rx="8" fill="#E8F5E9" stroke="#4CAF50" stroke-width="2"/>
                <rect x="625" y="300" width="550" height="50" rx="8" fill="#4CAF50"/>
                <text x="900" y="332" text-anchor="middle" fill="white" font-size="24" font-weight="bold" class="svg-text">手机端</text>
                
                <!-- 手机端模块 -->
                <rect x="680" y="380" width="220" height="60" rx="5" fill="white" stroke="#C8E6C9" stroke-width="1"/>
                <text x="790" y="415" text-anchor="middle" fill="#2E7D32" font-size="16" class="svg-text">钥匙与车辆管理</text>
                
                <rect x="900" y="380" width="220" height="60" rx="5" fill="white" stroke="#C8E6C9" stroke-width="1"/>
                <text x="1010" y="415" text-anchor="middle" fill="#2E7D32" font-size="16" class="svg-text">蓝牙通信模块</text>
                
                <rect x="680" y="460" width="220" height="60" rx="5" fill="white" stroke="#C8E6C9" stroke-width="1"/>
                <text x="790" y="495" text-anchor="middle" fill="#2E7D32" font-size="16" class="svg-text">安全存储模块</text>
                
                <rect x="900" y="460" width="220" height="60" rx="5" fill="white" stroke="#C8E6C9" stroke-width="1"/>
                <text x="1010" y="495" text-anchor="middle" fill="#2E7D32" font-size="16" class="svg-text">暗号交换模块</text>
                
                <!-- 车端区域 -->
                <rect x="100" y="650" width="1600" height="150" rx="8" fill="#FFEBEE" stroke="#F44336" stroke-width="2"/>
                <rect x="100" y="650" width="1600" height="50" rx="8" fill="#F44336"/>
                <text x="900" y="682" text-anchor="middle" fill="white" font-size="24" font-weight="bold" class="svg-text">车端</text>
                
                <!-- 车端模块 -->
                <rect x="180" y="720" width="200" height="60" rx="5" fill="white" stroke="#FFCDD2" stroke-width="1"/>
                <text x="280" y="755" text-anchor="middle" fill="#C62828" font-size="16" class="svg-text">钥匙验证模块</text>
                
                <rect x="400" y="720" width="200" height="60" rx="5" fill="white" stroke="#FFCDD2" stroke-width="1"/>
                <text x="500" y="755" text-anchor="middle" fill="#C62828" font-size="16" class="svg-text">蓝牙通信模块</text>
                
                <rect x="620" y="720" width="200" height="60" rx="5" fill="white" stroke="#FFCDD2" stroke-width="1"/>
                <text x="720" y="755" text-anchor="middle" fill="#C62828" font-size="16" class="svg-text">指令执行模块</text>
                
                <rect x="840" y="720" width="200" height="60" rx="5" fill="white" stroke="#FFCDD2" stroke-width="1"/>
                <text x="940" y="755" text-anchor="middle" fill="#C62828" font-size="16" class="svg-text">暗号交换模块</text>
                
                <rect x="1060" y="720" width="200" height="60" rx="5" fill="white" stroke="#FFCDD2" stroke-width="1"/>
                <text x="1160" y="755" text-anchor="middle" fill="#C62828" font-size="16" class="svg-text">安全存储模块</text>
                
                <rect x="1280" y="720" width="200" height="60" rx="5" fill="white" stroke="#FFCDD2" stroke-width="1"/>
                <text x="1380" y="755" text-anchor="middle" fill="#C62828" font-size="16" class="svg-text">远程通信模块</text>
                
                <!-- 云平台到手机端连接线 -->
                <path d="M 800 200 L 800 300" stroke="#2196F3" stroke-width="3" stroke-dasharray="0" marker-end="url(#arrow-blue)" fill="none"/>
                <path d="M 1000 300 L 1000 200" stroke="#4CAF50" stroke-width="3" stroke-dasharray="0" marker-end="url(#arrow-green)" fill="none"/>
                
                <!-- 手机端到车端连接线 -->
                <path d="M 800 600 L 800 650" stroke="#4CAF50" stroke-width="3" stroke-dasharray="0" marker-end="url(#arrow-green)" fill="none"/>
                <path d="M 1000 650 L 1000 600" stroke="#F44336" stroke-width="3" stroke-dasharray="0" marker-end="url(#arrow-red)" fill="none"/>
                
                <!-- 云平台到车端连接线 (虚线) -->
                <path d="M 300 200 C 250 350, 250 500, 300 650" stroke="#2196F3" stroke-width="2.5" stroke-dasharray="10,5" marker-end="url(#arrow-blue)" fill="none"/>
                <path d="M 400 650 C 450 500, 450 350, 400 200" stroke="#F44336" stroke-width="2.5" stroke-dasharray="10,5" marker-end="url(#arrow-red)" fill="none"/>
                
                <!-- 连接标签 -->
                <!-- 云平台到手机端标签 -->
                <rect x="720" y="230" width="120" height="30" rx="5" fill="white" stroke="#BBDEFB" stroke-width="1"/>
                <text x="780" y="250" text-anchor="middle" fill="#1565C0" font-size="14" class="svg-text">钥匙授权/认证</text>
                
                <rect x="960" y="230" width="120" height="30" rx="5" fill="white" stroke="#C8E6C9" stroke-width="1"/>
                <text x="1020" y="250" text-anchor="middle" fill="#2E7D32" font-size="14" class="svg-text">状态同步/更新</text>
                
                <!-- 手机端到车端标签 -->
                <rect x="720" y="610" width="120" height="30" rx="5" fill="white" stroke="#C8E6C9" stroke-width="1"/>
                <text x="780" y="630" text-anchor="middle" fill="#2E7D32" font-size="14" class="svg-text">蓝牙控制指令</text>
                
                <rect x="960" y="610" width="120" height="30" rx="5" fill="white" stroke="#FFCDD2" stroke-width="1"/>
                <text x="1020" y="630" text-anchor="middle" fill="#C62828" font-size="14" class="svg-text">状态反馈/验证</text>
                
                <!-- 远程连接标签 -->
                <g transform="translate(250, 400) rotate(-30)">
                    <rect x="0" y="0" width="150" height="30" rx="5" fill="white" stroke="#BBDEFB" stroke-width="1"/>
                    <text x="75" y="20" text-anchor="middle" fill="#1565C0" font-size="14" class="svg-text">远程控制/配置更新</text>
                </g>
                
                <g transform="translate(450, 400) rotate(30)">
                    <rect x="0" y="0" width="150" height="30" rx="5" fill="white" stroke="#FFCDD2" stroke-width="1"/>
                    <text x="75" y="20" text-anchor="middle" fill="#C62828" font-size="14" class="svg-text">状态上报/安全校验</text>
                </g>
                
                <!-- 近远标签 -->
                <circle cx="570" cy="450" r="15" fill="#4CAF50"/>
                <text x="570" y="455" text-anchor="middle" fill="white" font-size="14" font-weight="bold" class="svg-text">近</text>
                
                <circle cx="350" cy="400" r="15" fill="#2196F3"/>
                <text x="350" y="405" text-anchor="middle" fill="white" font-size="14" font-weight="bold" class="svg-text">远</text>
            </svg>
            
            <!-- 底部说明 -->
            <div class="footer">
                数字钥匙三端架构 - 安全高效的车辆接入控制系统
            </div>
        </div>
    </div>
    
    <div class="toast" id="toast">操作成功！</div>
    
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const copyButton = document.getElementById('copyButton');
            const downloadButton = document.getElementById('downloadButton');
            const toast = document.getElementById('toast');
            const slide = document.getElementById('slide');
            const resolutionBtns = document.querySelectorAll('.resolution-btn');
            
            // 默认清晰度为2x (高清)
            let currentScale = 2;
            
            // 清晰度按钮点击事件
            resolutionBtns.forEach(btn => {
                btn.addEventListener('click', function() {
                    // 移除所有按钮的active类
                    resolutionBtns.forEach(b => b.classList.remove('active'));
                    // 给当前按钮添加active类
                    this.classList.add('active');
                    // 更新当前清晰度
                    currentScale = parseFloat(this.getAttribute('data-scale'));
                    
                    // 显示提示
                    showToast(`清晰度已设置为${this.textContent}模式`);
                });
            });
            
            // 显示提示消息
            function showToast(message) {
                toast.textContent = message;
                toast.style.display = 'block';
                setTimeout(function() {
                    toast.style.display = 'none';
                }, 2000);
            }
            
            // 生成透明背景图片
            function generateImage() {
                return html2canvas(slide, {
                    scale: currentScale, // 使用用户选择的清晰度
                    useCORS: true,
                    allowTaint: true,
                    backgroundColor: null // 关键：设置为null以支持透明背景
                });
            }
            
            // 复制按钮功能
            copyButton.addEventListener('click', function() {
                showToast("正在处理图片，请稍候...");
                
                generateImage().then(function(canvas) {
                    try {
                        // 使用现代剪贴板API
                        canvas.toBlob(function(blob) {
                            try {
                                const item = new ClipboardItem({ 'image/png': blob });
                                navigator.clipboard.write([item]).then(function() {
                                    showToast("已复制到剪贴板！现在可以粘贴到PPT中");
                                }).catch(function(err) {
                                    showToast("自动复制失败，请使用下载按钮获取图片");
                                    console.error("剪贴板API错误:", err);
                                });
                            } catch (e) {
                                showToast("您的浏览器不支持复制图片，请使用下载按钮");
                                console.error("ClipboardItem错误:", e);
                            }
                        });
                    } catch (e) {
                        showToast("复制功能不可用，请使用下载图片功能");
                        console.error("toBlob错误:", e);
                    }
                }).catch(function(err) {
                    showToast("生成图片失败，请尝试下载或截图");
                    console.error("html2canvas错误:", err);
                });
            });
            
            // 下载按钮功能
            downloadButton.addEventListener('click', function () {
                showToast(`正在生成${currentScale}x清晰度透明PNG图片，请稍候...`);

                generateImage().then(function (canvas) {
                    // 将Canvas转换为PNG并下载
                    const imageUrl = canvas.toDataURL('image/png');
                    const link = document.createElement('a');

                    // 首先从URL路径获取文件名
                    let fileName = "";
                    const pathParts = window.location.pathname.split('/');
                    const htmlFileName = pathParts[pathParts.length - 1];
                    // 移除.html后缀并进行URL解码以支持中文文件名
                    fileName = decodeURIComponent(htmlFileName.replace('.html', ''));

                    // 如果从URL获取的文件名为空，尝试从标题获取
                    if (!fileName) {
                        const titleElement = document.querySelector('.header h1');
                        if (titleElement && titleElement.textContent) {
                            fileName = titleElement.textContent.trim();
                        }
                    }

                    // 确保文件名不为空
                    if (!fileName) {
                        fileName = "测试";
                    }

                    link.download = `${fileName}-透明-${currentScale}x清晰度.png`;
                    link.href = imageUrl;
                    link.click();

                    showToast(`${currentScale}x清晰度透明背景PNG已下载`);
                }).catch(function (err) {
                    showToast("生成图片失败，请尝试截图");
                    console.error("Error:", err);
                });
            });
        });
    </script>
</body>
</html> 
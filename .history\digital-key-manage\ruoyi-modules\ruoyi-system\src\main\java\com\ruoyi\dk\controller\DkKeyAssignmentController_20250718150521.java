package com.ruoyi.dk.controller;

import com.ruoyi.common.core.web.controller.BaseController;
import com.ruoyi.common.core.web.domain.AjaxResult;
import com.ruoyi.common.core.web.page.TableDataInfo;
import com.ruoyi.dk.domain.DkKeyAssignment;
import com.ruoyi.dk.service.IDkKeyAssignmentService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.Map;

/**
 * 钥匙分配管理Controller
 * 
 * <AUTHOR>
 * @date 2024-01-15
 */
@RestController
@RequestMapping("/dk/keyAssignment")
public class DkKeyAssignmentController extends BaseController {

    @Autowired(required = false)
    private IDkKeyAssignmentService dkKeyAssignmentService;

    /**
     * 查询钥匙分配列表
     */
    @GetMapping("/list")
    public TableDataInfo list(DkKeyAssignment dkKeyAssignment) {
        try {
            if (dkKeyAssignmentService != null) {
                startPage();
                List<DkKeyAssignment> list = dkKeyAssignmentService.selectDkKeyAssignmentList(dkKeyAssignment);
                return getDataTable(list);
            }
        } catch (Exception e) {
            logger.error("查询钥匙分配列表失败", e);
        }

        // 返回模拟数据
        List<Map<String, Object>> mockList = new ArrayList<>();

        Map<String, Object> assignment1 = new HashMap<>();
        assignment1.put("assignmentId", 1L);
        assignment1.put("assignmentNo", "ASG20240115001");
        assignment1.put("userId", 1001L);
        assignment1.put("userName", "张三");
        assignment1.put("vehicleId", 1L);
        assignment1.put("vehicleName", "比亚迪秦PLUS DM-i");
        assignment1.put("licensePlate", "京A12345");
        assignment1.put("keyId", "DK001");
        assignment1.put("status", "1");
        assignment1.put("assignTime", "2024-01-15 09:00:00");
        assignment1.put("expectedReturnTime", "2024-01-15 18:00:00");
        assignment1.put("actualReturnTime", "");
        assignment1.put("remark", "商务出行");
        mockList.add(assignment1);

        Map<String, Object> assignment2 = new HashMap<>();
        assignment2.put("assignmentId", 2L);
        assignment2.put("assignmentNo", "ASG20240115002");
        assignment2.put("userId", 1002L);
        assignment2.put("userName", "李四");
        assignment2.put("vehicleId", 2L);
        assignment2.put("vehicleName", "特斯拉Model 3");
        assignment2.put("licensePlate", "京A67890");
        assignment2.put("keyId", "DK002");
        assignment2.put("status", "2");
        assignment2.put("assignTime", "2024-01-14 14:00:00");
        assignment2.put("expectedReturnTime", "2024-01-14 20:00:00");
        assignment2.put("actualReturnTime", "2024-01-14 19:30:00");
        assignment2.put("remark", "日常通勤");
        mockList.add(assignment2);

        Map<String, Object> assignment3 = new HashMap<>();
        assignment3.put("assignmentId", 3L);
        assignment3.put("assignmentNo", "ASG20240115003");
        assignment3.put("userId", 1003L);
        assignment3.put("userName", "王五");
        assignment3.put("vehicleId", 3L);
        assignment3.put("vehicleName", "本田雅阁");
        assignment3.put("licensePlate", "京A11111");
        assignment3.put("keyId", "DK003");
        assignment3.put("status", "3");
        assignment3.put("assignTime", "2024-01-13 10:00:00");
        assignment3.put("expectedReturnTime", "2024-01-13 18:00:00");
        assignment3.put("actualReturnTime", "");
        assignment3.put("remark", "周末出游");
        mockList.add(assignment3);

        TableDataInfo dataTable = new TableDataInfo();
        dataTable.setRows(mockList);
        dataTable.setTotal(mockList.size());
        return dataTable;
    }

    /**
     * 获取钥匙分配详细信息
     */
    @GetMapping(value = "/{assignmentId}")
    public AjaxResult getInfo(@PathVariable("assignmentId") Long assignmentId) {
        try {
            if (dkKeyAssignmentService != null) {
                return AjaxResult.success(dkKeyAssignmentService.selectDkKeyAssignmentByAssignmentId(assignmentId));
            }
        } catch (Exception e) {
            logger.error("获取钥匙分配详细信息失败", e);
        }

        // 返回模拟数据
        Map<String, Object> mockData = new HashMap<>();
        mockData.put("assignmentId", assignmentId);
        mockData.put("assignmentNo", "ASG20240115" + String.format("%03d", assignmentId));
        mockData.put("userId", 1000L + assignmentId);
        mockData.put("userName", "测试用户" + assignmentId);
        mockData.put("vehicleId", assignmentId);
        mockData.put("vehicleName", "测试车辆" + assignmentId);
        mockData.put("licensePlate", "京A1234" + assignmentId);
        mockData.put("keyId", "DK" + String.format("%03d", assignmentId));
        mockData.put("status", "1");
        mockData.put("assignTime", "2024-01-15 09:00:00");
        mockData.put("expectedReturnTime", "2024-01-15 18:00:00");
        mockData.put("remark", "测试分配");

        return AjaxResult.success(mockData);
    }

    /**
     * 分配数字钥匙
     */
    @PostMapping("/assign")
    public AjaxResult assignDigitalKey(@RequestBody Map<String, Object> assignData) {
        try {
            if (dkKeyAssignmentService != null) {
                Long userId = Long.valueOf(assignData.get("userId").toString());
                Long vehicleId = Long.valueOf(assignData.get("vehicleId").toString());
                String expectedReturnTime = assignData.get("expectedReturnTime").toString();
                String remark = assignData.get("remark") != null ? assignData.get("remark").toString() : "";
                return toAjax(dkKeyAssignmentService.assignDigitalKey(userId, vehicleId, expectedReturnTime, remark));
            }
        } catch (Exception e) {
            logger.error("分配数字钥匙失败", e);
        }

        // 模拟成功
        return AjaxResult.success("钥匙分配成功");
    }

    /**
     * 回收数字钥匙
     */
    @PutMapping("/revoke/{assignmentId}")
    public AjaxResult revokeDigitalKey(@PathVariable Long assignmentId, @RequestBody Map<String, Object> revokeData) {
        try {
            if (dkKeyAssignmentService != null) {
                String remark = revokeData.get("remark") != null ? revokeData.get("remark").toString() : "";
                return toAjax(dkKeyAssignmentService.revokeDigitalKey(assignmentId, remark));
            }
        } catch (Exception e) {
            logger.error("回收数字钥匙失败", e);
        }

        // 模拟成功
        return AjaxResult.success("钥匙回收成功");
    }

    /**
     * 批量分配钥匙
     */
    @PostMapping("/batchAssign")
    public AjaxResult batchAssignKeys(@RequestBody Map<String, Object> batchData) {
        try {
            if (dkKeyAssignmentService != null) {
                // 处理批量分配逻辑
                return AjaxResult.success("批量分配成功");
            }
        } catch (Exception e) {
            logger.error("批量分配钥匙失败", e);
        }

        // 模拟成功
        return AjaxResult.success("批量分配成功");
    }

    /**
     * 批量回收钥匙
     */
    @PutMapping("/batchRevoke")
    public AjaxResult batchRevokeKeys(@RequestBody Map<String, Object> batchData) {
        try {
            if (dkKeyAssignmentService != null) {
                Long[] assignmentIds = (Long[]) batchData.get("assignmentIds");
                String remark = batchData.get("remark") != null ? batchData.get("remark").toString() : "";
                return toAjax(dkKeyAssignmentService.batchRevokeKeys(assignmentIds, remark));
            }
        } catch (Exception e) {
            logger.error("批量回收钥匙失败", e);
        }

        // 模拟成功
        return AjaxResult.success("批量回收成功");
    }

    /**
     * 获取待分配的申请列表
     */
    @PreAuthorize("@ss.hasPermi('dk:assignment:query')")
    @GetMapping("/pending")
    public TableDataInfo getPendingAssignments() {
        try {
            if (dkKeyAssignmentService != null) {
                startPage();
                List<Map<String, Object>> list = dkKeyAssignmentService.getPendingAssignments();
                return getDataTable(list);
            }
        } catch (Exception e) {
            logger.error("获取待分配申请列表失败", e);
        }

        // 返回模拟数据
        List<Map<String, Object>> mockList = new ArrayList<>();

        Map<String, Object> pending1 = new HashMap<>();
        pending1.put("applicationId", 1L);
        pending1.put("applicationNo", "APP20240115001");
        pending1.put("realName", "张三");
        pending1.put("phone", "13800138001");
        pending1.put("applicationReason", "商务出行需要");
        pending1.put("createTime", "2024-01-15 09:30:00");
        mockList.add(pending1);

        Map<String, Object> pending2 = new HashMap<>();
        pending2.put("applicationId", 4L);
        pending2.put("applicationNo", "APP20240115004");
        pending2.put("realName", "赵六");
        pending2.put("phone", "13800138004");
        pending2.put("applicationReason", "周末出游");
        pending2.put("createTime", "2024-01-15 11:45:00");
        mockList.add(pending2);

        TableDataInfo dataTable = new TableDataInfo();
        dataTable.setRows(mockList);
        dataTable.setTotal(mockList.size());
        return dataTable;
    }

    /**
     * 更新分配状态
     */
    @PreAuthorize("@ss.hasPermi('dk:assignment:status')")
    @PutMapping("/status/{assignmentId}")
    public AjaxResult updateAssignmentStatus(@PathVariable Long assignmentId,
            @RequestBody Map<String, Object> statusData) {
        try {
            if (dkKeyAssignmentService != null) {
                String status = statusData.get("status").toString();
                String remark = statusData.get("remark") != null ? statusData.get("remark").toString() : "";
                return toAjax(dkKeyAssignmentService.updateAssignmentStatus(assignmentId, status, remark));
            }
        } catch (Exception e) {
            logger.error("更新分配状态失败", e);
        }

        // 模拟成功
        return AjaxResult.success("状态更新成功");
    }

    /**
     * 获取分配统计信息
     */
    @PreAuthorize("@ss.hasPermi('dk:assignment:query')")
    @GetMapping("/statistics")
    public AjaxResult getAssignmentStatistics() {
        try {
            if (dkKeyAssignmentService != null) {
                Map<String, Object> statistics = dkKeyAssignmentService.getAssignmentStatistics();
                return AjaxResult.success(statistics);
            }
        } catch (Exception e) {
            logger.error("获取分配统计信息失败", e);
        }

        // 返回模拟数据
        Map<String, Object> mockStats = new HashMap<>();
        mockStats.put("totalAssignments", 150);
        mockStats.put("inUse", 65);
        mockStats.put("returned", 80);
        mockStats.put("overdue", 3);
        mockStats.put("abnormal", 2);
        mockStats.put("todayAssignments", 15);
        mockStats.put("todayReturns", 12);

        return AjaxResult.success(mockStats);
    }
}

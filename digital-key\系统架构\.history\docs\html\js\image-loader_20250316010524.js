/**
 * 图片加载处理脚本
 * 用于处理带有data-html-src属性的图片，实现点击图片可以查看详细内容
 */
document.addEventListener("DOMContentLoaded", function() {
    // 选择所有带有data-html-src属性的图片
    var images = document.querySelectorAll("img[data-html-src]");
    
    // 为每个图片设置加载和点击事件
    images.forEach(function(img) {
        // 设置图片源为HTML文件路径
        img.src = img.getAttribute("data-html-src");
        
        // 添加鼠标悬停时的提示
        img.title = "点击查看详细内容";
        
        // 设置鼠标样式为手型，表示可点击
        img.style.cursor = "pointer";
        
        // 添加点击事件处理器
        img.addEventListener("click", function() {
            // 在新窗口打开HTML文件
            window.open(this.getAttribute("data-html-src"), "_blank");
        });
        
        // 添加加载错误处理
        img.addEventListener("error", function() {
            console.error("图片加载失败：" + this.getAttribute("data-html-src"));
            // 设置默认的错误图片
            this.src = "images/error-placeholder.svg";
            this.alt = "图片加载失败";
            // 移除点击事件
            this.style.cursor = "default";
            this.title = "图片加载失败，请检查路径或刷新页面";
            // 克隆节点以移除所有事件监听器
            var newImg = this.cloneNode(true);
            this.parentNode.replaceChild(newImg, this);
        });
    });
    
    // 淡入动画效果
    var fadeInElements = document.querySelectorAll(".fade-in");
    
    function checkVisibility() {
        fadeInElements.forEach(function(element) {
            var rect = element.getBoundingClientRect();
            var windowHeight = window.innerHeight;
            
            // 当元素进入视口时应用动画
            if (rect.top < windowHeight * 0.9) {
                element.classList.add("visible");
            }
        });
    }
    
    // 设置初始状态
    fadeInElements.forEach(function(element) {
        element.style.opacity = "0";
        element.style.transform = "translateY(20px)";
        element.style.transition = "opacity 0.8s ease, transform 0.8s ease";
    });
    
    // 添加可见时的类
    document.addEventListener("scroll", checkVisibility);
    window.addEventListener("resize", checkVisibility);
    
    // 检查初始可见性
    checkVisibility();
}); 
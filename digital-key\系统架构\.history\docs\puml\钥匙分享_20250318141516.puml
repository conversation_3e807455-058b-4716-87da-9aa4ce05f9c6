@startuml
skinparam monochrome true

actor "车主(Owner)" as Owner
actor "被分享者(Recipient)" as Recipient
participant "车主APP" as OwnerApp
participant "被分享者APP" as RecipientApp
participant "钥匙云平台" as Cloud
participant "车辆(BLE & TBOX)" as Car
database "安全存储区域" as SecureStorage

== 钥匙分享发起 ==

Owner -> OwnerApp : 1. 选择"分享我的钥匙"功能
OwnerApp -> OwnerApp : 2. 显示可分享的车辆列表
Owner -> OwnerApp : 3. 选择要分享的车辆
OwnerApp -> OwnerApp : 4. 展示分享选项界面
Owner -> OwnerApp : 5. 设置：\n- 接收者信息(手机号/邮箱)\n- 权限级别(完全/有限/仅访问)\n- 有效期限(临时/周期/长期)\n- 使用限制(时间/地点/功能)
OwnerApp -> Cloud : 6. 发送分享请求(分享参数+车主身份验证)

note right of Cloud
  钥匙分享的关键安全保障：
  1. 原始拥有者身份必须经过严格验证
  2. 分享权限必须低于车主权限
  3. 分享记录要完整保存以便审计
  4. 云平台作为信任中心确保流程安全
end note

Cloud -> Cloud : 7. 验证请求：\n- 验证车主身份和授权\n- 检查分享策略合规性\n- 验证接收者账号存在
Cloud -> SecureStorage : 8. 记录分享意向(状态：待接受)
Cloud -> OwnerApp : 9. 返回分享创建成功
OwnerApp -> Owner : 10. 显示分享创建成功
Cloud -> RecipientApp : 11. 推送钥匙分享邀请通知

== 被分享者接收流程 ==

RecipientApp -> Recipient : 12. 推送通知："[车主姓名]向您分享了[车辆]的数字钥匙"
Recipient -> RecipientApp : 13. 打开通知，查看邀请详情
RecipientApp -> RecipientApp : 14. 显示：\n- 分享者信息\n- 车辆信息\n- 权限范围\n- 有效期限
Recipient -> RecipientApp : 15. 确认接受分享
RecipientApp -> Cloud : 16. 发送接受请求(含身份验证信息)

Cloud -> Cloud : 17. 创建分享密钥：\n- 根据权限级别派生子密钥\n- 生成唯一分享ID\n- 设置有效期和限制条件\n- 关联车主和接收者身份
Cloud -> SecureStorage : 18. 更新分享记录(状态：已接受)
Cloud -> RecipientApp : 19. 下发加密的分享钥匙包

note right of RecipientApp
  分享钥匙包包含：
  - 派生的子密钥(权限受限)
  - 授权令牌
  - 使用策略
  - 车辆连接信息
  - 有效期限
  - 临时身份标识
  所有信息均经过加密保护
end note

RecipientApp -> RecipientApp : 20. 将钥匙包存入设备安全区域(TEE/SE)
RecipientApp -> Recipient : 21. 显示"钥匙接收成功，可以使用"
Cloud -> OwnerApp : 22. 通知"[接收者]已接受您的钥匙分享"
Cloud -> Car : 23. 通过TSP下发授权信息：\n- 添加被授权用户ID\n- 设置权限级别\n- 配置有效期\n- 同步使用限制

== 被分享钥匙使用流程 ==

Recipient -> RecipientApp : 24. 选择使用分享的车钥匙
RecipientApp -> RecipientApp : 25. 从安全区域读取钥匙信息
RecipientApp -> Car : 26. 发现并连接车辆蓝牙

note right of Car
  车辆通过以下机制验证分享钥匙：
  1. 验证钥匙是否在授权列表中
  2. 检查钥匙是否在有效期内
  3. 验证当前时间/位置是否符合使用限制
  4. 根据权限级别决定可执行的功能
end note

Car -> Car : 27. 验证临时授权是否有效
RecipientApp <-> Car : 28. 执行双向认证
RecipientApp <-> Car : 29. 建立加密安全通道

RecipientApp -> Car : 30. 发送控车指令(带权限级别标识)
Car -> Car : 31. 验证指令权限并执行
Car -> RecipientApp : 32. 返回执行结果
RecipientApp -> Recipient : 33. 显示操作成功/失败

Cloud <- Car : 34. 上报使用记录(可选，取决于配置)
Cloud -> OwnerApp : 35. 可选：通知车主"您的车被[接收者]使用"

== 钥匙分享管理 ==

Owner -> OwnerApp : 36. 查看"我的分享管理"
OwnerApp -> Cloud : 37. 获取所有分享记录
Cloud -> OwnerApp : 38. 返回分享列表和状态
OwnerApp -> Owner : 39. 显示所有分享记录：\n- 接收者\n- 分享状态\n- 最后使用时间\n- 剩余有效期

Owner -> OwnerApp : 40. 选择"撤销分享"
OwnerApp -> Cloud : 41. 发送撤销请求
Cloud -> Cloud : 42. 标记分享为已撤销
Cloud -> Car : 43. 通知车辆移除授权
Cloud -> RecipientApp : 44. 推送通知："您的钥匙已被撤销"
RecipientApp -> RecipientApp : 45. 删除本地钥匙数据
RecipientApp -> Recipient : 46. 显示"钥匙已失效"

== 自动过期处理 ==

note over Cloud
  当分享钥匙到达设定的过期时间
end note

Cloud -> Cloud : 47. 定时任务检测过期钥匙
Cloud -> Cloud : 48. 标记已过期的分享
Cloud -> Car : 49. 通知车辆移除过期授权
Cloud -> RecipientApp : 50. 推送通知："您的临时钥匙已过期"
Cloud -> OwnerApp : 51. 通知："分享给[接收者]的钥匙已过期"

RecipientApp -> RecipientApp : 52. 删除或归档过期钥匙
RecipientApp -> Recipient : 53. 显示"钥匙已过期"

== 异常处理 ==

alt 网络连接异常
    Cloud -> RecipientApp : 推送失败
    note over Cloud, RecipientApp
      系统会在网络恢复后重新尝试推送
      或在接收者下次登录时展示
    end note
else 车辆离线
    Cloud -> Car : 授权信息下发失败
    note over Cloud, Car
      系统会在车辆上线后自动同步
      同时在云端记录授权状态，确保一致性
    end note
else 安全异常
    Cloud -> Cloud : 检测到异常使用模式
    Cloud -> Owner : 发送安全预警
    Cloud -> SecureStorage : 记录安全事件
end

@enduml
package com.ruoyi.dk.controller;

import com.ruoyi.common.core.web.controller.BaseController;
import com.ruoyi.dk.utils.VinKeyGenerator;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

/**
 * 控制器类，用于处理与VIN密钥生成相关的HTTP请求。
 */
@RestController
@RequestMapping("/api/vin")
public class VinKeyController extends BaseController {

    // 自动注入VinKeyGenerator工具类
    @Autowired
    private VinKeyGenerator vinKeyGenerator;

    /**
     * 根据提供的VIN码生成密钥，并以字节数组形式返回。
     * 
     * @param vin 车辆识别号码
     * @return 密钥字节数组
     */
    @GetMapping("/generate-key")
    public byte[] generateKey(@RequestParam("vin") String vin) {
        // 调用VinKeyGenerator的方法来生成密钥
        return vinKeyGenerator.generateKeyFromVin(vin);
    }
}

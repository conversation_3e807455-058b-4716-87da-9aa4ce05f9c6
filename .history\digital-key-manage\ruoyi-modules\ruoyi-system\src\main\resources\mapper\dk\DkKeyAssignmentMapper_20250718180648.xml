<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.dk.mapper.DkKeyAssignmentMapper">
    
    <resultMap type="DkKeyAssignment" id="DkKeyAssignmentResult">
        <result property="assignmentId"    column="assignment_id"    />
        <result property="userId"    column="user_id"    />
        <result property="userName"    column="user_name"    />
        <result property="vehicleId"    column="vehicle_id"    />
        <result property="vehicleName"    column="vehicle_name"    />
        <result property="keyType"    column="key_type"    />
        <result property="keyValue"    column="key_value"    />
        <result property="status"    column="status"    />
        <result property="assignTime"    column="assign_time"    />
        <result property="expectedReturnTime"    column="expected_return_time"    />
        <result property="actualReturnTime"    column="actual_return_time"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="remark"    column="remark"    />
    </resultMap>

    <sql id="selectDkKeyAssignmentVo">
        select assignment_id, user_id, user_name, vehicle_id, vehicle_name, key_type, key_value, status, assign_time, expected_return_time, actual_return_time, create_by, create_time, update_by, update_time, remark from dk_key_assignment
    </sql>

    <select id="selectDkKeyAssignmentList" parameterType="DkKeyAssignment" resultMap="DkKeyAssignmentResult">
        <include refid="selectDkKeyAssignmentVo"/>
        <where>  
            <if test="userId != null "> and user_id = #{userId}</if>
            <if test="userName != null  and userName != ''"> and user_name like concat('%', #{userName}, '%')</if>
            <if test="vehicleId != null "> and vehicle_id = #{vehicleId}</if>
            <if test="vehicleName != null  and vehicleName != ''"> and vehicle_name like concat('%', #{vehicleName}, '%')</if>
            <if test="keyType != null  and keyType != ''"> and key_type = #{keyType}</if>
            <if test="status != null  and status != ''"> and status = #{status}</if>
            <if test="assignTime != null "> and assign_time = #{assignTime}</if>
            <if test="expectedReturnTime != null "> and expected_return_time = #{expectedReturnTime}</if>
        </where>
        order by create_time desc
    </select>
    
    <select id="selectDkKeyAssignmentByAssignmentId" parameterType="Long" resultMap="DkKeyAssignmentResult">
        <include refid="selectDkKeyAssignmentVo"/>
        where assignment_id = #{assignmentId}
    </select>
    
    <select id="selectDkKeyAssignmentByUserId" parameterType="Long" resultMap="DkKeyAssignmentResult">
        <include refid="selectDkKeyAssignmentVo"/>
        where user_id = #{userId}
        order by create_time desc
    </select>
    
    <select id="selectDkKeyAssignmentByVehicleId" parameterType="Long" resultMap="DkKeyAssignmentResult">
        <include refid="selectDkKeyAssignmentVo"/>
        where vehicle_id = #{vehicleId}
        order by create_time desc
    </select>
    
    <select id="selectPendingAssignments" resultType="java.util.Map">
        select 
            ka.assignment_id,
            ka.user_name,
            ka.vehicle_name,
            ka.expected_return_time,
            ka.create_time,
            ka.remark
        from dk_key_assignment ka
        where ka.status = '0'
        order by ka.create_time desc
    </select>
    
    <select id="countAssignmentsByStatus" parameterType="String" resultType="int">
        select count(*) from dk_key_assignment where status = #{status}
    </select>

    <insert id="insertDkKeyAssignment" parameterType="DkKeyAssignment" useGeneratedKeys="true" keyProperty="assignmentId">
        insert into dk_key_assignment
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="userId != null">user_id,</if>
            <if test="userName != null">user_name,</if>
            <if test="vehicleId != null">vehicle_id,</if>
            <if test="vehicleName != null">vehicle_name,</if>
            <if test="keyType != null">key_type,</if>
            <if test="keyValue != null">key_value,</if>
            <if test="status != null">status,</if>
            <if test="assignTime != null">assign_time,</if>
            <if test="expectedReturnTime != null">expected_return_time,</if>
            <if test="actualReturnTime != null">actual_return_time,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="remark != null">remark,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="userId != null">#{userId},</if>
            <if test="userName != null">#{userName},</if>
            <if test="vehicleId != null">#{vehicleId},</if>
            <if test="vehicleName != null">#{vehicleName},</if>
            <if test="keyType != null">#{keyType},</if>
            <if test="keyValue != null">#{keyValue},</if>
            <if test="status != null">#{status},</if>
            <if test="assignTime != null">#{assignTime},</if>
            <if test="expectedReturnTime != null">#{expectedReturnTime},</if>
            <if test="actualReturnTime != null">#{actualReturnTime},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="remark != null">#{remark},</if>
         </trim>
    </insert>

    <update id="updateDkKeyAssignment" parameterType="DkKeyAssignment">
        update dk_key_assignment
        <trim prefix="SET" suffixOverrides=",">
            <if test="userId != null">user_id = #{userId},</if>
            <if test="userName != null">user_name = #{userName},</if>
            <if test="vehicleId != null">vehicle_id = #{vehicleId},</if>
            <if test="vehicleName != null">vehicle_name = #{vehicleName},</if>
            <if test="keyType != null">key_type = #{keyType},</if>
            <if test="keyValue != null">key_value = #{keyValue},</if>
            <if test="status != null">status = #{status},</if>
            <if test="assignTime != null">assign_time = #{assignTime},</if>
            <if test="expectedReturnTime != null">expected_return_time = #{expectedReturnTime},</if>
            <if test="actualReturnTime != null">actual_return_time = #{actualReturnTime},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="remark != null">remark = #{remark},</if>
        </trim>
        where assignment_id = #{assignmentId}
    </update>
    
    <update id="updateAssignmentStatus">
        update dk_key_assignment 
        set status = #{status}, remark = #{remark}, update_time = now()
        where assignment_id = #{assignmentId}
    </update>

    <delete id="deleteDkKeyAssignmentByAssignmentId" parameterType="Long">
        delete from dk_key_assignment where assignment_id = #{assignmentId}
    </delete>

    <delete id="deleteDkKeyAssignmentByAssignmentIds" parameterType="String">
        delete from dk_key_assignment where assignment_id in 
        <foreach item="assignmentId" collection="array" open="(" separator="," close=")">
            #{assignmentId}
        </foreach>
    </delete>
</mapper>

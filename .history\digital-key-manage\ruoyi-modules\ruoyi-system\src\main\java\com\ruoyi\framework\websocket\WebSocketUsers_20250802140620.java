package com.ruoyi.framework.websocket;

import java.io.IOException;
import java.util.Collection;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.ConcurrentHashMap;
import javax.websocket.Session;

import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * websocket 客户端用户集
 * 
 * <AUTHOR>
 */
public class WebSocketUsers {
    /**
     * WebSocketUsers 日志控制器
     */
    private static final Logger LOGGER = LoggerFactory.getLogger(WebSocketUsers.class);

    /**
     * 用户集
     */
    private static Map<String, Session> USERS = new ConcurrentHashMap<String, Session>();

    /**
     * 用户及Session映射
     */
    private static Map<Long, String> USERSESSIONS = new ConcurrentHashMap<Long, String>();

    /**
     * 存储用户
     *
     * @param key     唯一键
     * @param session 用户信息
     */
    public static void put(String key, Session session) {
        USERS.put(key, session);
    }

    /**
     * 存储用户Session映射
     *
     * @param userId    用户id（唯一键）
     * @param sessionId 用户会话信息id
     */
    public static void putUserSessions(Long userId, String sessionId) {
        USERSESSIONS.put(userId, sessionId);
    }

    /**
     * 移除用户
     *
     * @param session 用户信息
     *
     * @return 移除结果
     */
    public static boolean remove(Session session) {
        String key = null;
        boolean flag = USERS.containsValue(session);
        if (flag) {
            Set<Map.Entry<String, Session>> entries = USERS.entrySet();
            for (Map.Entry<String, Session> entry : entries) {
                Session value = entry.getValue();
                if (value.equals(session)) {
                    key = entry.getKey();
                    break;
                }
            }
        } else {
            return true;
        }
        return remove(key);
    }

    /**
     * 移出用户Session映射
     *
     * @param key 键
     */
    public static boolean removeUserSessions(String key) {
        LOGGER.info("\n 正在移出用户Session映射 - {}", key);
        try {
            Long userId = Long.valueOf(key);
            String remove = USERSESSIONS.remove(userId);
            if (StringUtils.isNotBlank(remove)) {
                boolean containsValue = USERSESSIONS.containsValue(remove);
                LOGGER.info("\n 移出用户Session映射结果 - {}", containsValue ? "失败" : "成功");
                return containsValue;
            } else {
                return true;
            }
        } catch (NumberFormatException e) {
            LOGGER.warn("\n 移出用户Session映射失败 - 无效的用户ID: {}", key);
            return true;
        }
    }

    /**
     * 根据sessionId移出用户Session映射
     *
     * @param sessionId 会话ID
     */
    public static boolean removeUserSessionsBySessionId(String sessionId) {
        LOGGER.info("\n 正在根据sessionId移出用户Session映射 - {}", sessionId);

        // 查找对应的userId
        Long userIdToRemove = null;
        for (Map.Entry<Long, String> entry : USERSESSIONS.entrySet()) {
            if (sessionId.equals(entry.getValue())) {
                userIdToRemove = entry.getKey();
                break;
            }
        }

        if (userIdToRemove != null) {
            String removed = USERSESSIONS.remove(userIdToRemove);
            LOGGER.info("\n 移出用户Session映射结果 - {}", removed != null ? "成功" : "失败");
            return removed != null;
        } else {
            LOGGER.info("\n 未找到对应的用户Session映射 - {}", sessionId);
            return true;
        }
    }

    /**
     * 移出用户
     *
     * @param key 键
     */
    public static boolean remove(String key) {
        LOGGER.info("\n 正在移出用户 - {}", key);
        Session remove = USERS.remove(key);
        if (remove != null) {
            boolean containsValue = USERS.containsValue(remove);
            LOGGER.info("\n 移出结果 - {}", containsValue ? "失败" : "成功");
            return containsValue;
        } else {
            return true;
        }
    }

    /**
     * 获取在线用户列表
     *
     * @return 返回用户集合
     */
    public static Map<String, Session> getUsers() {
        return USERS;
    }

    /**
     * 通过用户ID获取在线用户会话信息
     *
     * @return 返回用户会话信息
     */
    public static Session getSessionByUserId(Long userId) {
        // 防止userId为null导致的NullPointerException
        if (userId == null) {
            LOGGER.warn("\n 获取用户会话信息失败 - userId为null");
            return null;
        }

        String sessionId = USERSESSIONS.get(userId);
        if (StringUtils.isBlank(sessionId)) {
            return null;
        }

        return USERS.get(sessionId);
    }

    /**
     * 群发消息文本消息
     *
     * @param message 消息内容
     */
    public static void sendMessageToUsersByText(String message) {
        Collection<Session> values = USERS.values();
        for (Session value : values) {
            sendMessageToUserByText(value, message);
        }
    }

    /**
     * 发送文本消息
     *
     * @param session 用户信息
     * @param message 消息内容
     */
    public static void sendMessageToUserByText(Session session, String message) {
        if (session != null) {
            try {
                session.getBasicRemote().sendText(message);
            } catch (IOException e) {
                LOGGER.error("\n[发送消息异常]", e);
            }
        } else {
            LOGGER.info("\n[你已离线]");
        }
    }
}

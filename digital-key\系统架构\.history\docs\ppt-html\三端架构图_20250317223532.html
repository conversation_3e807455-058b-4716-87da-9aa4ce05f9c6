<!DOCTYPE html>
<html lang="zh-CN">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>系统整体架构</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
            font-family: 'PingFang SC', 'Microsoft YaHei', sans-serif;
        }

        body {
            display: flex;
            justify-content: center;
            align-items: center;
            min-height: 100vh;
            background: white;
            padding: 20px;
        }

        .architecture-container {
            width: 1200px;
            padding: 40px;
        }

        .title {
            font-size: 32px;
            color: #333;
            margin-bottom: 30px;
            font-weight: bold;
        }

        .subtitle {
            font-size: 18px;
            color: #666;
            margin-bottom: 50px;
        }

        .system-layout {
            display: flex;
            flex-direction: column;
            gap: 30px;
        }

        /* 云端部分 */
        .cloud-section {
            border: 2px dashed #6c5ce7;
            padding: 20px;
            border-radius: 10px;
            position: relative;
        }

        .section-label {
            position: absolute;
            top: -12px;
            left: 20px;
            background: white;
            padding: 0 10px;
            color: #6c5ce7;
            font-weight: bold;
        }

        .cloud-components {
            display: flex;
            justify-content: space-around;
            align-items: center;
            gap: 20px;
        }

        .component {
            background: #f0f2f5;
            padding: 15px 30px;
            border-radius: 8px;
            text-align: center;
            min-width: 150px;
        }

        /* 移动端和车辆端部分 */
        .client-sections {
            display: flex;
            justify-content: space-between;
            gap: 30px;
        }

        .mobile-section,
        .vehicle-section {
            flex: 1;
            border: 2px dashed #00b894;
            padding: 20px;
            border-radius: 10px;
            position: relative;
        }

        .component-list {
            display: flex;
            flex-direction: column;
            gap: 15px;
        }

        /* 连接线 */
        .connection-line {
            border-left: 2px dashed #a8a8a8;
            height: 30px;
            margin: 0 auto;
        }

        /* 模块组件样式 */
        .module {
            background: #e8f5e9;
            padding: 10px;
            border-radius: 6px;
            margin: 5px 0;
        }

        /* 产线前装部分 */
        .production-section {
            border: 2px dashed #e17055;
            padding: 20px;
            border-radius: 10px;
            position: relative;
            margin-top: 30px;
        }
    </style>
</head>

<body>
    <div class="architecture-container">
        <h1 class="title">系统整体架构</h1>
        <p class="subtitle">数字车钥匙系统主要包括三个实体，即移动智能终端（移动终端或手机）、云端服务器与车辆端。</p>

        <div class="system-layout">
            <!-- 云端部分 -->
            <div class="cloud-section">
                <span class="section-label">云端</span>
                <div class="cloud-components">
                    <div class="component">DK Server</div>
                    <div class="component">TSM</div>
                    <div class="component">OEM TSP</div>
                </div>
            </div>

            <!-- 移动端和车辆端 -->
            <div class="client-sections">
                <!-- 移动端 -->
                <div class="mobile-section">
                    <span class="section-label">移动端</span>
                    <div class="component-list">
                        <div class="component">NFC</div>
                        <div class="component">BLE</div>
                        <div class="module">钥匙业务模块</div>
                        <div class="module">钥匙标准基础模块SDK</div>
                    </div>
                </div>

                <!-- 车辆端 -->
                <div class="vehicle-section">
                    <span class="section-label">车辆端</span>
                    <div class="component-list">
                        <div class="module">数字钥匙NFC模组</div>
                        <div class="component">射频天线</div>
                        <div class="component">CLF</div>
                        <div class="module">数字钥匙BLE模组</div>
                        <div class="component">MCU</div>
                        <div class="component">BLE</div>
                        <div class="component">eSE</div>
                    </div>
                </div>
            </div>

            <!-- 产线前装部分 -->
            <div class="production-section">
                <span class="section-label">产线前装</span>
                <div class="component">产线安装、检测</div>
            </div>
        </div>
    </div>
</body>

</html>
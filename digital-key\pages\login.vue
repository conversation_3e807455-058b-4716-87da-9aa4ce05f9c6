<template>
  <view class="container">
    <!-- 自定义导航栏 -->
    <view class="custom-navbar">
      <text class="navbar-title">登录</text>
    </view>
    
    <!-- 页面内容 -->
    <view class="normal-login-container">
      <view class="grid col-1 logo-img">
        <view class="text-center">
          <image style="width: 158rpx;height: 158rpx;" :src="globalConfig.appInfo.logo" mode="widthFix">
          </image>
        </view>
      </view>
      <view class="grid col-1">
        <view class="text-center">
          <text class="title">数字钥匙</text>
        </view>
      </view>
      <view class="login-form-content">
        <view class="input-item flex align-center">
          <view class="iconfont icon-user icon"></view>
          <input v-model="loginForm.username" class="input" type="text" placeholder="请输入账号" maxlength="30" />
        </view>
        <view class="input-item flex align-center">
          <view class="iconfont icon-password icon"></view>
          <input v-model="loginForm.password" type="password" class="input" placeholder="请输入密码" maxlength="20" />
        </view>
        <view class="action-btn">
          <button @click="handleLogin" class="login-btn cu-btn block login-btn-cls lg round">登录</button>
        </view>
        <view class="reg text-center" v-if="register">
          <text class="text-grey1">没有账号？</text>
          <text @click="handleUserRegister" class="text-gray">立即注册</text>
        </view>
        <!-- <view class="xieyi text-center">
          <text class="text-grey1">登录即代表同意</text>
          <text @click="handleUserAgrement" class="text-blue">《用户协议》</text>
          <text @click="handlePrivacy" class="text-blue">《隐私协议》</text>
        </view> -->
      </view>
       
    </view>
  </view>
</template>

<script>
  import { getCodeImg } from '@/api/login'

  export default {
    data() {
      return {
        codeUrl: "",
        captchaEnabled: true,
        // 用户注册开关
        register: true,
        globalConfig: getApp().globalData.config,
        loginForm: {
          username: "",
          password: "",
          code: "",
          uuid: '',
          // 自动登录标记
          autoLogonSign: true
        }
      }
    },

    // onLoad() {
    //   // 从缓存里获取登录相关信息
    //   let loginForm = uni.getStorageSync("loginForm");

    //   // 如果开启了自动登录，则执行自动登录逻辑
    //   if (loginForm?.autoLogonSign) {
    //     this.loginForm = loginForm;
    //     // 自动登录
    //     this.pwdLogin();
    //   }
    // },

    created() {
      // this.getCode()
    },
    methods: {
      // 用户注册
      handleUserRegister() {
        this.$tab.redirectTo(`/pages/register`)
      },
      // 隐私协议
      handlePrivacy() {
        let site = this.globalConfig.appInfo.agreements[0]
        this.$tab.navigateTo(`/pages/common/webview/index?title=${site.title}&url=${site.url}`)
      },
      // 用户协议
      handleUserAgrement() {
        let site = this.globalConfig.appInfo.agreements[1]
        this.$tab.navigateTo(`/pages/common/webview/index?title=${site.title}&url=${site.url}`)
      },
      // 获取图形验证码
      getCode() {
        // getCodeImg().then(res => {
        //   this.captchaEnabled = res.captchaEnabled === undefined ? true : res.captchaEnabled
        //   if (this.captchaEnabled) {
        //     this.codeUrl = 'data:image/gif;base64,' + res.img
        //     this.loginForm.uuid = res.uuid
        //   }
        // })
      },
      // 登录方法
      async handleLogin() {
        if (this.loginForm.username === "") {
          this.$modal.msgError("请输入您的账号")
        } else if (this.loginForm.password === "") {
          this.$modal.msgError("请输入您的密码")
        } else {
          this.$modal.loading("登录中，请耐心等待...")
          this.pwdLogin()
        }
      },
      // 密码登录
      async pwdLogin() {
        this.$store.dispatch('Login', this.loginForm).then(() => {
          this.$modal.closeLoading()
          this.loginSuccess()
        }).catch(() => {
          if (this.captchaEnabled) {
            this.getCode()
          }
        })
      },
      // 登录成功后，处理函数
      loginSuccess(result) {
        // 开启自动登录
        this.loginForm.autoLogonSign = true;
        // 缓存登录相关信息
        uni.setStorageSync("loginForm", this.loginForm);

        // 设置用户信息
        this.$store.dispatch('GetInfo').then(res => {
          this.$tab.reLaunch('/pages/work/digitalKey/index')
        })
      }
    }
  }
</script>

<style lang="scss">
  page {
    background-color: #ffffff;
  }

  .custom-navbar {
    display: flex;
    justify-content: center;
    align-items: center;
    height: 184rpx; /* 导航栏高度 */
    border-radius: 0rpx 0rpx 0rpx 0rpx;
    background-color: rgba(0, 0, 0, 0); /* 自定义背景色 */
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    z-index: 999;
  }

  .navbar-title {
    margin-top: 75rpx;
    color: #121010; /* 标题颜色 */
  }

  .normal-login-container {
    width: 100%;

    .logo-content {
      width: 100%;
      font-size: 21px;
      text-align: center;
      padding-top: 15%;

      image {
        border-radius: 4px;
      }

    }
    .title {
      margin-left: 10px;
      color: #060708;
      font-size: 42rpx;
    }
    .logo-img {
      margin-top: 220rpx;
    }

    .login-form-content {
      text-align: center;
      margin: 20px auto;
      margin-top: 15%;
      width: 80%;

      .login-btn {
        margin-top: 40px;
        height: 45px;
      }
      
      .reg {
        margin-top: 15px;
      }
      
      .xieyi {
        color: #333;
        margin-top: 20px;
      }
      
      .login-code {
        height: 38px;
        float: right;
      
        .login-code-img {
          height: 38px;
          position: absolute;
          margin-left: 10px;
          width: 200rpx;
        }
      }

      .login-btn-cls {
        background-color: #4D7EFC;
        color: #FFFFFF;
      }
    }
  }

</style>

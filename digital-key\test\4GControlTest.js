/**
 * 4G控车功能测试脚本
 * 用于验证4G控车WebSocket通信功能
 */

// 模拟uni对象（用于Node.js环境测试）
const mockUni = {
  connectSocket: (options) => {
    console.log('🔗 模拟连接WebSocket:', options.url)
    return { socketTask: 'mock_socket' }
  },
  
  onSocketOpen: (callback) => {
    console.log('📡 模拟WebSocket连接打开')
    setTimeout(() => callback({ data: 'connected' }), 1000)
  },
  
  onSocketMessage: (callback) => {
    console.log('📨 模拟WebSocket消息监听')
    // 模拟接收消息
    setTimeout(() => {
      callback({
        data: JSON.stringify({
          senderName: '4G控车系统',
          message: '连接成功',
          messageType: 0,
          timestamp: Date.now()
        })
      })
    }, 2000)
  },
  
  onSocketClose: (callback) => {
    console.log('❌ 模拟WebSocket连接关闭监听')
  },
  
  onSocketError: (callback) => {
    console.log('⚠️ 模拟WebSocket错误监听')
  },
  
  sendSocketMessage: (options) => {
    console.log('📤 模拟发送WebSocket消息:', options.data)
    if (options.success) {
      setTimeout(() => options.success(), 500)
    }
  },
  
  closeSocket: () => {
    console.log('🔌 模拟关闭WebSocket连接')
  },
  
  showToast: (options) => {
    console.log('🍞 模拟显示Toast:', options.title)
  },
  
  showLoading: (options) => {
    console.log('⏳ 模拟显示Loading:', options.title)
  },
  
  hideLoading: () => {
    console.log('✅ 模拟隐藏Loading')
  }
}

// 模拟store对象
const mockStore = {
  state: {
    user: {
      id: 1,
      name: '测试用户'
    }
  }
}

// 模拟getToken函数
const mockGetToken = () => 'mock_token_123456'

// 模拟配置
const mockConfig = {
  websocketUrl: 'ws://localhost:9201',
  enabled: true,
  reconnect: {
    maxAttempts: 5,
    interval: 5000
  },
  timeout: {
    connect: 10000,
    command: 5000
  }
}

// 4G控车WebSocket类（简化版，用于测试）
class FourGControlWebSocketTest {
  constructor() {
    this.ws = null
    this.isConnected = false
    this.reconnectTimer = null
    this.reconnectCount = 0
    this.maxReconnectCount = mockConfig.reconnect.maxAttempts
    this.reconnectInterval = mockConfig.reconnect.interval
    this.messageHandlers = new Map()
    this.vehicleStatusCallback = null
    this.connectionStatusCallback = null
    this.config = mockConfig
  }

  connect(serverUrl = null) {
    if (this.isConnected) {
      console.log('4G控车WebSocket已连接，无需重复连接')
      return
    }

    const baseUrl = serverUrl || this.config.websocketUrl
    const userId = mockStore.state.user?.id || 1
    const wsUrl = `${baseUrl}/websocket/message?userId=${userId}`

    console.log('🚗 正在连接4G控车WebSocket:', wsUrl)

    try {
      this.ws = mockUni.connectSocket({
        url: wsUrl,
        header: {
          Authorization: mockGetToken()
        },
        success: () => {
          console.log('4G控车WebSocket连接请求发送成功')
        },
        fail: (err) => {
          console.error('4G控车WebSocket连接失败:', err)
          this.handleConnectionError()
        }
      })

      // 监听连接打开
      mockUni.onSocketOpen((res) => {
        console.log('✅ 4G控车WebSocket连接成功:', res)
        this.isConnected = true
        this.reconnectCount = 0
        
        if (this.connectionStatusCallback) {
          this.connectionStatusCallback(true)
        }
      })

      // 监听消息
      mockUni.onSocketMessage((res) => {
        this.handleMessage(res.data)
      })

      // 监听连接关闭
      mockUni.onSocketClose((res) => {
        console.log('❌ 4G控车WebSocket连接关闭:', res)
        this.isConnected = false
        
        if (this.connectionStatusCallback) {
          this.connectionStatusCallback(false)
        }
      })

      // 监听连接错误
      mockUni.onSocketError((res) => {
        console.error('❌ 4G控车WebSocket连接错误:', res)
        this.isConnected = false
        
        if (this.connectionStatusCallback) {
          this.connectionStatusCallback(false)
        }
      })

    } catch (error) {
      console.error('4G控车WebSocket初始化失败:', error)
      this.handleConnectionError()
    }
  }

  sendControlCommand(command, callback = null) {
    if (!this.isConnected) {
      const error = '4G控车WebSocket未连接，无法发送指令'
      console.error(error)
      if (callback) callback({ success: false, message: error })
      return
    }

    const message = {
      messageType: 4,
      message: command,
      senderName: mockStore.state.user?.name || '手机用户',
      senderId: mockStore.state.user?.id || 1,
      timestamp: Date.now()
    }

    console.log('🚗 发送4G控车指令:', message)

    try {
      mockUni.sendSocketMessage({
        data: JSON.stringify(message),
        success: () => {
          console.log('✅ 4G控车指令发送成功')
          if (callback) callback({ success: true, message: '指令发送成功' })
        },
        fail: (err) => {
          console.error('❌ 4G控车指令发送失败:', err)
          if (callback) callback({ success: false, message: '指令发送失败' })
        }
      })
    } catch (error) {
      console.error('4G控车指令发送异常:', error)
      if (callback) callback({ success: false, message: '指令发送异常' })
    }
  }

  sendUnlockCommand(callback = null) {
    this.sendControlCommand('unlock', callback)
  }

  sendLockCommand(callback = null) {
    this.sendControlCommand('lock', callback)
  }

  handleMessage(data) {
    try {
      const message = JSON.parse(data)
      console.log('📨 收到4G控车消息:', message)

      if (message.senderName === '4G控车系统') {
        console.log('🚗 控车系统消息:', message.message)
      } else if (message.senderName === 'TBOX') {
        console.log('📡 TBOX设备消息:', message.message)
        
        if (message.message.includes('解锁') || message.message.includes('闭锁')) {
          const isLocked = message.message.includes('闭锁')
          
          if (this.vehicleStatusCallback) {
            this.vehicleStatusCallback({
              locked: isLocked,
              message: message.message,
              timestamp: message.timestamp || Date.now()
            })
          }
        }
      }

    } catch (error) {
      console.error('解析4G控车消息失败:', error, data)
    }
  }

  handleConnectionError() {
    this.isConnected = false
    
    if (this.connectionStatusCallback) {
      this.connectionStatusCallback(false)
    }
  }

  setVehicleStatusCallback(callback) {
    this.vehicleStatusCallback = callback
  }

  setConnectionStatusCallback(callback) {
    this.connectionStatusCallback = callback
  }

  disconnect() {
    if (this.reconnectTimer) {
      clearTimeout(this.reconnectTimer)
      this.reconnectTimer = null
    }
    
    if (this.ws) {
      mockUni.closeSocket()
      this.ws = null
    }
    
    this.isConnected = false
    this.reconnectCount = 0
  }

  getConnectionStatus() {
    return this.isConnected
  }
}

// 测试函数
async function test4GControl() {
  console.log('🧪 开始4G控车功能测试...\n')

  // 创建4G控车实例
  const fourGControl = new FourGControlWebSocketTest()

  // 设置回调函数
  fourGControl.setConnectionStatusCallback((connected) => {
    console.log(`📡 连接状态变化: ${connected ? '已连接' : '已断开'}`)
  })

  fourGControl.setVehicleStatusCallback((status) => {
    console.log('🚙 车辆状态更新:', status)
  })

  // 测试连接
  console.log('1️⃣ 测试连接功能...')
  fourGControl.connect()

  // 等待连接建立
  await new Promise(resolve => setTimeout(resolve, 3000))

  // 测试解锁指令
  console.log('\n2️⃣ 测试解锁指令...')
  fourGControl.sendUnlockCommand((result) => {
    console.log('解锁结果:', result)
  })

  // 等待指令处理
  await new Promise(resolve => setTimeout(resolve, 2000))

  // 测试闭锁指令
  console.log('\n3️⃣ 测试闭锁指令...')
  fourGControl.sendLockCommand((result) => {
    console.log('闭锁结果:', result)
  })

  // 等待指令处理
  await new Promise(resolve => setTimeout(resolve, 2000))

  // 测试断开连接
  console.log('\n4️⃣ 测试断开连接...')
  fourGControl.disconnect()

  console.log('\n✅ 4G控车功能测试完成!')
}

// 如果是在Node.js环境中运行
if (typeof module !== 'undefined' && module.exports) {
  module.exports = {
    FourGControlWebSocketTest,
    test4GControl,
    mockUni,
    mockStore,
    mockConfig
  }
  
  // 直接运行测试
  test4GControl().catch(console.error)
} else {
  // 在浏览器环境中，将测试函数暴露到全局
  window.test4GControl = test4GControl
  console.log('🌐 4G控车测试脚本已加载，请调用 test4GControl() 开始测试')
}

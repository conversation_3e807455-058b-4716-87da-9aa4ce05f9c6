<mxfile host="app.diagrams.net" modified="2023-11-15T08:30:00.000Z" agent="Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36" etag="abc123" version="21.6.6" type="device">
  <diagram id="digital-key-architecture" name="数字钥匙三端架构图">
    <mxGraphModel dx="1422" dy="798" grid="1" gridSize="10" guides="1" tooltips="1" connect="1" arrows="1" fold="1" page="1" pageScale="1" pageWidth="1169" pageHeight="827" math="0" shadow="0">
      <root>
        <mxCell id="0" />
        <mxCell id="1" parent="0" />
        
        <!-- 标题 -->
        <mxCell id="title" value="数字钥匙三端架构" style="text;html=1;strokeColor=none;fillColor=none;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=24;fontStyle=1;fontColor=#0066CC;" vertex="1" parent="1">
          <mxGeometry x="400" y="40" width="370" height="40" as="geometry" />
        </mxCell>
        
        <!-- 云端服务 -->
        <mxCell id="cloud" value="云端服务" style="swimlane;fontStyle=1;align=center;verticalAlign=top;childLayout=stackLayout;horizontal=1;startSize=30;horizontalStack=0;resizeParent=1;resizeParentMax=0;resizeLast=0;collapsible=0;marginBottom=0;html=1;fillColor=#dae8fc;strokeColor=#6c8ebf;gradientColor=#7ea6e0;fontSize=16;" vertex="1" parent="1">
          <mxGeometry x="400" y="120" width="370" height="150" as="geometry" />
        </mxCell>
        
        <mxCell id="cloud-content" value="&lt;div&gt;- 用户认证与授权&lt;/div&gt;&lt;div&gt;- 钥匙权限管理&lt;/div&gt;&lt;div&gt;- 数据同步服务&lt;/div&gt;&lt;div&gt;- 安全加密通信&lt;/div&gt;&lt;div&gt;- 日志与监控系统&lt;/div&gt;" style="text;html=1;strokeColor=none;fillColor=none;align=left;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=14;spacing=5;spacingLeft=10;" vertex="1" parent="cloud">
          <mxGeometry y="30" width="370" height="120" as="geometry" />
        </mxCell>
        
        <!-- 移动端 -->
        <mxCell id="mobile" value="移动端应用" style="swimlane;fontStyle=1;align=center;verticalAlign=top;childLayout=stackLayout;horizontal=1;startSize=30;horizontalStack=0;resizeParent=1;resizeParentMax=0;resizeLast=0;collapsible=0;marginBottom=0;html=1;fillColor=#d5e8d4;strokeColor=#82b366;gradientColor=#97d077;fontSize=16;" vertex="1" parent="1">
          <mxGeometry x="120" y="340" width="280" height="150" as="geometry" />
        </mxCell>
        
        <mxCell id="mobile-content" value="&lt;div&gt;- 用户界面&lt;/div&gt;&lt;div&gt;- 蓝牙/NFC通信模块&lt;/div&gt;&lt;div&gt;- 生物识别验证&lt;/div&gt;&lt;div&gt;- 本地安全存储&lt;/div&gt;&lt;div&gt;- 离线操作功能&lt;/div&gt;" style="text;html=1;strokeColor=none;fillColor=none;align=left;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=14;spacing=5;spacingLeft=10;" vertex="1" parent="mobile">
          <mxGeometry y="30" width="280" height="120" as="geometry" />
        </mxCell>
        
        <!-- 智能锁端 -->
        <mxCell id="lock" value="智能锁端" style="swimlane;fontStyle=1;align=center;verticalAlign=top;childLayout=stackLayout;horizontal=1;startSize=30;horizontalStack=0;resizeParent=1;resizeParentMax=0;resizeLast=0;collapsible=0;marginBottom=0;html=1;fillColor=#ffe6cc;strokeColor=#d79b00;gradientColor=#ffce9f;fontSize=16;" vertex="1" parent="1">
          <mxGeometry x="450" y="340" width="280" height="150" as="geometry" />
        </mxCell>
        
        <mxCell id="lock-content" value="&lt;div&gt;- 低功耗蓝牙/NFC接收器&lt;/div&gt;&lt;div&gt;- 安全认证模块&lt;/div&gt;&lt;div&gt;- 电子锁控制系统&lt;/div&gt;&lt;div&gt;- 电池管理&lt;/div&gt;&lt;div&gt;- 紧急解锁机制&lt;/div&gt;" style="text;html=1;strokeColor=none;fillColor=none;align=left;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=14;spacing=5;spacingLeft=10;" vertex="1" parent="lock">
          <mxGeometry y="30" width="280" height="120" as="geometry" />
        </mxCell>
        
        <!-- 管理端 -->
        <mxCell id="admin" value="管理端" style="swimlane;fontStyle=1;align=center;verticalAlign=top;childLayout=stackLayout;horizontal=1;startSize=30;horizontalStack=0;resizeParent=1;resizeParentMax=0;resizeLast=0;collapsible=0;marginBottom=0;html=1;fillColor=#e1d5e7;strokeColor=#9673a6;gradientColor=#d5b9d5;fontSize=16;" vertex="1" parent="1">
          <mxGeometry x="780" y="340" width="280" height="150" as="geometry" />
        </mxCell>
        
        <mxCell id="admin-content" value="&lt;div&gt;- 用户管理界面&lt;/div&gt;&lt;div&gt;- 权限分配系统&lt;/div&gt;&lt;div&gt;- 使用记录查询&lt;/div&gt;&lt;div&gt;- 远程控制功能&lt;/div&gt;&lt;div&gt;- 系统配置工具&lt;/div&gt;" style="text;html=1;strokeColor=none;fillColor=none;align=left;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=14;spacing=5;spacingLeft=10;" vertex="1" parent="admin">
          <mxGeometry y="30" width="280" height="120" as="geometry" />
        </mxCell>
        
        <!-- 连接线 -->
        <mxCell id="cloud-to-mobile" value="HTTPS/API" style="endArrow=classic;startArrow=classic;html=1;rounded=0;exitX=0.25;exitY=1;exitDx=0;exitDy=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;strokeWidth=2;fontSize=12;fontColor=#0066CC;edgeStyle=orthogonalEdgeStyle;curved=1;" edge="1" parent="1" source="cloud-content" target="mobile">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="560" y="400" as="sourcePoint" />
            <mxPoint x="610" y="350" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        
        <mxCell id="cloud-to-lock" value="MQTT/WebSocket" style="endArrow=classic;startArrow=classic;html=1;rounded=0;exitX=0.5;exitY=1;exitDx=0;exitDy=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;strokeWidth=2;fontSize=12;fontColor=#0066CC;" edge="1" parent="1" source="cloud-content" target="lock">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="560" y="400" as="sourcePoint" />
            <mxPoint x="610" y="350" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        
        <mxCell id="cloud-to-admin" value="HTTPS/API" style="endArrow=classic;startArrow=classic;html=1;rounded=0;exitX=0.75;exitY=1;exitDx=0;exitDy=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;strokeWidth=2;fontSize=12;fontColor=#0066CC;edgeStyle=orthogonalEdgeStyle;curved=1;" edge="1" parent="1" source="cloud-content" target="admin">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="560" y="400" as="sourcePoint" />
            <mxPoint x="610" y="350" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        
        <mxCell id="mobile-to-lock" value="蓝牙/NFC" style="endArrow=classic;startArrow=classic;html=1;rounded=0;exitX=1;exitY=0.5;exitDx=0;exitDy=0;entryX=0;entryY=0.5;entryDx=0;entryDy=0;strokeWidth=2;fontSize=12;fontColor=#0066CC;dashed=1;" edge="1" parent="1" source="mobile-content" target="lock-content">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="560" y="400" as="sourcePoint" />
            <mxPoint x="610" y="350" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        
        <!-- 安全层 -->
        <mxCell id="security" value="安全层" style="swimlane;fontStyle=1;align=center;verticalAlign=top;childLayout=stackLayout;horizontal=1;startSize=30;horizontalStack=0;resizeParent=1;resizeParentMax=0;resizeLast=0;collapsible=0;marginBottom=0;html=1;fillColor=#f8cecc;strokeColor=#b85450;gradientColor=#ea6b66;fontSize=16;" vertex="1" parent="1">
          <mxGeometry x="400" y="540" width="370" height="120" as="geometry" />
        </mxCell>
        
        <mxCell id="security-content" value="&lt;div&gt;- 端到端加密&lt;/div&gt;&lt;div&gt;- 多因素认证&lt;/div&gt;&lt;div&gt;- 安全令牌&lt;/div&gt;&lt;div&gt;- 入侵检测&lt;/div&gt;" style="text;html=1;strokeColor=none;fillColor=none;align=left;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=14;spacing=5;spacingLeft=10;" vertex="1" parent="security">
          <mxGeometry y="30" width="370" height="90" as="geometry" />
        </mxCell>
        
        <!-- 安全层连接 -->
        <mxCell id="security-connection" value="" style="shape=curlyBracket;whiteSpace=wrap;html=1;rounded=1;flipH=1;labelPosition=right;verticalLabelPosition=middle;align=left;verticalAlign=middle;rotation=90;size=0.5;strokeWidth=2;fillColor=none;" vertex="1" parent="1">
          <mxGeometry x="575" y="440" width="20" height="370" as="geometry" />
        </mxCell>
        
        <!-- 图例 -->
        <mxCell id="legend" value="图例" style="swimlane;fontStyle=1;align=center;verticalAlign=top;childLayout=stackLayout;horizontal=1;startSize=30;horizontalStack=0;resizeParent=1;resizeParentMax=0;resizeLast=0;collapsible=0;marginBottom=0;html=1;fillColor=#f5f5f5;strokeColor=#666666;gradientColor=#b3b3b3;fontSize=14;" vertex="1" parent="1">
          <mxGeometry x="850" y="540" width="210" height="120" as="geometry" />
        </mxCell>
        
        <mxCell id="legend-content" value="&lt;div style=&quot;display: flex; align-items: center;&quot;&gt;&lt;span style=&quot;display: inline-block; width: 30px; height: 2px; background-color: #000; margin-right: 10px;&quot;&gt;&lt;/span&gt; 稳定连接&lt;/div&gt;&lt;div style=&quot;display: flex; align-items: center; margin-top: 10px;&quot;&gt;&lt;span style=&quot;display: inline-block; width: 30px; height: 2px; background-color: #000; margin-right: 10px; border-top: 2px dashed #000;&quot;&gt;&lt;/span&gt; 临时连接&lt;/div&gt;" style="text;html=1;strokeColor=none;fillColor=none;align=left;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=14;spacing=5;spacingLeft=10;" vertex="1" parent="legend">
          <mxGeometry y="30" width="210" height="90" as="geometry" />
        </mxCell>
      </root>
    </mxGraphModel>
  </diagram>
</mxfile>

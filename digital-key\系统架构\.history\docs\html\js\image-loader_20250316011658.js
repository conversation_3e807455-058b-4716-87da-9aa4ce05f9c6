/**
 * 图片加载处理脚本
 * 用于处理带有data-html-src属性的图片，实现点击图片可以直接查看详细内容
 */
document.addEventListener("DOMContentLoaded", function() {
    // 选择所有带有data-html-src属性的图片
    var images = document.querySelectorAll("img[data-html-src]");
    
    // 为每个图片设置点击事件和内容区域
    images.forEach(function(img) {
        // 创建内容容器
        var contentContainer = document.createElement('div');
        contentContainer.className = 'detail-content';
        contentContainer.style.display = 'none';
        contentContainer.style.width = '100%';
        contentContainer.style.marginTop = '20px';
        contentContainer.style.border = '1px solid #e0e0e0';
        contentContainer.style.borderRadius = '8px';
        contentContainer.style.overflow = 'hidden';
        contentContainer.style.transition = 'all 0.3s ease';
        contentContainer.style.boxShadow = '0 4px 8px rgba(0,0,0,0.1)';
        
        // 创建iframe来加载内容
        var contentFrame = document.createElement('iframe');
        contentFrame.style.width = '100%';
        contentFrame.style.height = '600px';
        contentFrame.style.border = 'none';
        contentFrame.style.display = 'block';
        
        contentContainer.appendChild(contentFrame);
        
        // 将内容容器插入到图片的父元素中，紧跟在图片后面
        img.parentNode.appendChild(contentContainer);
        
        // 创建一个占位SVG图
        var title = img.alt || "点击查看详细内容";
        var bgColor = "#f8f9fa";
        var textColor = "#3498db";
        
        // 设置不同类型图表的颜色
        if (img.alt.includes("配对")) {
            bgColor = "#eefbf5";
            textColor = "#2ecc71";
        } else if (img.alt.includes("无感")) {
            bgColor = "#f5eef8";
            textColor = "#9b59b6";
        }
        
        var svgImage = 'data:image/svg+xml;charset=utf-8,' + encodeURIComponent('<svg xmlns="http://www.w3.org/2000/svg" width="800" height="400" viewBox="0 0 800 400"><rect width="800" height="400" fill="' + bgColor + '"/><text x="400" y="200" text-anchor="middle" font-family="Arial, sans-serif" font-size="20" fill="' + textColor + '">' + title + ' - 点击查看详细内容</text></svg>');
        
        // 设置图片源为SVG
        img.src = svgImage;
        
        // 添加鼠标悬停时的提示
        img.title = "点击查看详细内容";
        
        // 设置鼠标样式为手型，表示可点击
        img.style.cursor = "pointer";
        
        // 添加点击事件处理器
        img.addEventListener("click", function() {
            // 获取所有内容容器并隐藏
            var allContainers = document.querySelectorAll('.detail-content');
            allContainers.forEach(function(container) {
                container.style.display = 'none';
            });
            
            // 加载并显示当前内容
            contentFrame.src = this.getAttribute("data-html-src");
            contentContainer.style.display = 'block';
            
            // 平滑滚动到内容区域
            setTimeout(function() {
                contentContainer.scrollIntoView({
                    behavior: 'smooth',
                    block: 'start'
                });
            }, 100);
            
            // 添加按钮来收起内容
            var closeButton = document.createElement('button');
            closeButton.textContent = '收起内容';
            closeButton.style.position = 'absolute';
            closeButton.style.right = '10px';
            closeButton.style.top = '10px';
            closeButton.style.padding = '5px 10px';
            closeButton.style.background = '#f8f9fa';
            closeButton.style.border = '1px solid #ddd';
            closeButton.style.borderRadius = '4px';
            closeButton.style.cursor = 'pointer';
            closeButton.onclick = function(e) {
                e.stopPropagation();
                contentContainer.style.display = 'none';
            };
            
            if (!contentContainer.querySelector('button')) {
                contentContainer.appendChild(closeButton);
            }
        });
        
        // 添加错误处理
        img.addEventListener("error", function() {
            console.error("图片加载失败：" + this.src);
            // 设置默认的错误图片
            this.src = "diagrams/error-placeholder.svg";
        });
    });
    
    // 淡入动画效果
    var fadeInElements = document.querySelectorAll(".fade-in");
    
    function checkVisibility() {
        fadeInElements.forEach(function(element) {
            var rect = element.getBoundingClientRect();
            var windowHeight = window.innerHeight;
            
            // 当元素进入视口时应用动画
            if (rect.top < windowHeight * 0.9) {
                element.classList.add("visible");
            }
        });
    }
    
    // 确保初始内容可见
    setTimeout(function() {
        // 将所有元素设置为可见
        fadeInElements.forEach(function(element) {
            element.classList.add("visible");
        });
    }, 100);
    
    // 添加可见时的类
    document.addEventListener("scroll", checkVisibility);
    window.addEventListener("resize", checkVisibility);
    
    // 立即检查初始可见性
    checkVisibility();
}); 
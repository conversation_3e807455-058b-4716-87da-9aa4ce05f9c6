<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>数字钥匙技术架构</title>
    <link rel="stylesheet" href="css/style.css">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/reveal.js@4.3.1/dist/reveal.min.css">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/reveal.js@4.3.1/dist/theme/white.min.css">
</head>
<body>
    <div class="reveal">
        <div class="slides">
            <!-- 封面页 -->
            <section>
                <h1>数字钥匙技术架构</h1>
                <h3>智能汽车数字钥匙解决方案</h3>
                <p>
                    <small>点击箭头或使用键盘方向键浏览</small>
                </p>
            </section>

            <!-- 目录页 -->
            <section>
                <h2>目录</h2>
                <ul>
                    <li><a href="#/overview">整体架构概述</a></li>
                    <li><a href="#/components">系统组件详解</a></li>
                    <li><a href="#/pairing">首次配对流程</a></li>
                    <li><a href="#/communication">蓝牙通讯过程</a></li>
                    <li><a href="#/security">安全设计</a></li>
                    <li><a href="#/key-management">密钥管理机制</a></li>
                    <li><a href="#/time-sync">时间同步机制</a></li>
                    <li><a href="#/passive-control">无感控车方案</a></li>
                    <li><a href="#/emergency">故障应急处理</a></li>
                </ul>
            </section>

            <!-- 整体架构概述 -->
            <section id="overview">
                <section>
                    <h2>整体架构概述</h2>
                    <p>数字钥匙系统由三大核心模块组成</p>
                    <div class="architecture-diagram">
                        <div class="component mobile">
                            <h3>手机端</h3>
                            <p>用户交互界面与核心SDK</p>
                        </div>
                        <div class="component car">
                            <h3>车端</h3>
                            <p>BLE模块与TBOX</p>
                        </div>
                        <div class="component cloud">
                            <h3>钥匙云平台</h3>
                            <p>密钥管理与安全认证</p>
                        </div>
                    </div>
                </section>
                
                <section>
                    <h2>系统架构图</h2>
                    <img src="images/overall-architecture.png" alt="整体架构图">
                    <p><small>系统各模块间的交互关系</small></p>
                </section>
            </section>

            <!-- 系统组件详解 -->
            <section id="components">
                <section>
                    <h2>系统组件详解</h2>
                    <p>点击查看各模块详细功能</p>
                    <div class="component-links">
                        <a href="#/components/mobile">手机端组件</a>
                        <a href="#/components/car">车端组件</a>
                        <a href="#/components/cloud">云平台组件</a>
                    </div>
                </section>
                
                <section id="components/mobile">
                    <h2>手机端组件</h2>
                    <ul>
                        <li><strong>钥匙管理模块</strong>：管理数字钥匙的生命周期</li>
                        <li><strong>车辆控制模块</strong>：执行车辆控制指令</li>
                        <li><strong>蓝牙通信模块</strong>：与车端建立安全连接</li>
                        <li><strong>安全存储模块</strong>：安全存储密钥和配置</li>
                        <li><strong>时间同步模块</strong>：确保精确时间基准</li>
                        <li><strong>暗号交换模块</strong>：与车端协商生成临时暗号</li>
                        <li><strong>智能场景管理模块</strong>：管理无感连接触发条件</li>
                        <li><strong>异常处理模块</strong>：处理通信异常和认证失败</li>
                    </ul>
                </section>
                
                <section id="components/car">
                    <h2>车端组件</h2>
                    <ul>
                        <li><strong>蓝牙通信模块</strong>：与手机建立安全连接</li>
                        <li><strong>钥匙验证模块</strong>：验证数字钥匙的有效性</li>
                        <li><strong>指令执行模块</strong>：执行控车指令</li>
                        <li><strong>安全存储模块</strong>：安全存储密钥和配置</li>
                        <li><strong>远程通信模块</strong>：与云平台通信</li>
                        <li><strong>时间同步模块</strong>：确保精确时间基准</li>
                        <li><strong>暗号交换模块</strong>：与手机协商生成临时暗号</li>
                        <li><strong>用户行为分析模块</strong>：核心无感控车算法</li>
                        <li><strong>异常处理模块</strong>：处理异常连接和操作</li>
                    </ul>
                </section>
                
                <section id="components/cloud">
                    <h2>云平台组件</h2>
                    <ul>
                        <li><strong>钥匙生命周期管理</strong>：管理钥匙的创建、授权和撤销</li>
                        <li><strong>车辆关联服务</strong>：管理车辆信息和绑定关系</li>
                        <li><strong>安全认证中心</strong>：负责身份验证和安全事件处理</li>
                        <li><strong>密钥管理系统</strong>：生成和管理各种密钥</li>
                        <li><strong>时间服务器</strong>：提供精确时间基准</li>
                        <li><strong>统一接口服务</strong>：提供标准接口和安全通信</li>
                        <li><strong>安全通信通道</strong>：提供加密通信和防篡改措施</li>
                        <li><strong>异常监控与处理</strong>：监控异常登录和使用模式</li>
                    </ul>
                </section>
            </section>

            <!-- 首次配对流程 -->
            <section id="pairing">
                <section>
                    <h2>首次配对流程</h2>
                    <p>数字钥匙首次配对的完整流程</p>
                </section>
                
                <section>
                    <h3>配对流程概述</h3>
                    <ol>
                        <li>用户在APP中选择添加数字钥匙</li>
                        <li>扫描车辆二维码或输入VIN码识别车辆</li>
                        <li>云平台生成虚拟密钥和配对令牌</li>
                        <li>手机与车辆建立蓝牙连接</li>
                        <li>双方进行安全验证</li>
                        <li>建立加密安全通道</li>
                        <li>完成配对并记录绑定关系</li>
                    </ol>
                </section>
                
                <section>
                    <h3>配对流程泳道图</h3>
                    <img src="images/pairing-swimlane.png" alt="首次配对泳道图">
                    <p><small>手机、车辆和云平台之间的交互过程</small></p>
                </section>
                
                <section>
                    <h3>配对安全保障</h3>
                    <ul>
                        <li>双向验证确保设备身份</li>
                        <li>一次性配对码防止重放攻击</li>
                        <li>临时暗号定期更换</li>
                        <li>密钥存储在特殊安全区域</li>
                        <li>使用蓝牙4.2+安全特性</li>
                        <li>距离测量防止中继攻击</li>
                    </ul>
                </section>
            </section>

            <!-- 蓝牙通讯过程 -->
            <section id="communication">
                <section>
                    <h2>蓝牙通讯过程</h2>
                    <p>手机与车辆之间的蓝牙通讯机制</p>
                </section>
                
                <section>
                    <h3>通讯流程概述</h3>
                    <ol>
                        <li>手机识别车辆蓝牙广播信号</li>
                        <li>建立蓝牙连接</li>
                        <li>进行身份验证</li>
                        <li>建立安全通道</li>
                        <li>发送控车指令</li>
                        <li>执行操作并返回结果</li>
                    </ol>
                </section>
                
                <section>
                    <h3>通讯安全机制</h3>
                    <ul>
                        <li>会话密钥加密所有通信内容</li>
                        <li>每条消息都有唯一标识，防止重放</li>
                        <li>消息完整性校验，防止篡改</li>
                        <li>会话密钥定期更新</li>
                        <li>通信异常自动处理</li>
                    </ul>
                </section>
            </section>

            <!-- 安全设计 -->
            <section id="security">
                <section>
                    <h2>安全设计</h2>
                    <p>数字钥匙系统的安全保障机制</p>
                </section>
                
                <section>
                    <h3>认证与加密</h3>
                    <ul>
                        <li>基于挑战-响应的双向身份认证</li>
                        <li>非对称加密技术保护密钥交换</li>
                        <li>对称加密保护通信内容</li>
                        <li>数字签名确保指令完整性</li>
                        <li>随机挑战数防止重放攻击</li>
                    </ul>
                </section>
                
                <section>
                    <h3>安全基础设施</h3>
                    <ul>
                        <li><strong>密码机(HSM)</strong>：执行密钥操作，确保密钥安全</li>
                        <li><strong>证书系统(PKI/CA)</strong>：提供设备身份证书，支持证书验证和撤销</li>
                        <li><strong>密钥备份系统</strong>：安全备份关键信息，防止数据丢失</li>
                        <li><strong>安全监控</strong>：监控系统使用情况，检测异常行为</li>
                    </ul>
                </section>
                
                <section>
                    <h3>防攻击措施</h3>
                    <ul>
                        <li>防中继攻击：基于距离测量和时间窗口验证</li>
                        <li>防重放攻击：使用随机挑战和序列号</li>
                        <li>防篡改：消息完整性校验和数字签名</li>
                        <li>防窃听：端到端加密通信</li>
                        <li>防暴力破解：多次失败自动锁定</li>
                    </ul>
                </section>
            </section>

            <!-- 密钥管理机制 -->
            <section id="key-management">
                <section>
                    <h2>密钥管理机制</h2>
                    <p>密钥的存储、更新、撤销机制</p>
                </section>
                
                <section>
                    <h3>密钥体系</h3>
                    <ul>
                        <li><strong>根密钥</strong>：车端保存的主密钥，是安全通信的基础</li>
                        <li><strong>虚拟密钥</strong>：手机端保存的密钥，用于识别和连接车辆</li>
                        <li><strong>配对令牌</strong>：双方验证身份的凭证</li>
                        <li><strong>会话密钥</strong>：临时通信密钥，定期更新</li>
                    </ul>
                </section>
                
                <section>
                    <h3>密钥存储</h3>
                    <ul>
                        <li>手机端：存储在TEE/SE等安全区域</li>
                        <li>车端：存储在HSM或安全芯片中</li>
                        <li>云平台：使用密码机和分布式存储</li>
                    </ul>
                </section>
                
                <section>
                    <h3>密钥更新机制</h3>
                    <ul>
                        <li>定期自动更新会话密钥</li>
                        <li>根据安全策略更新虚拟密钥</li>
                        <li>异常情况触发紧急更新</li>
                        <li>更新过程不影响用户体验</li>
                    </ul>
                </section>
                
                <section>
                    <h3>密钥撤销机制</h3>
                    <ul>
                        <li><strong>主动撤销</strong>：用户主动删除或管理员撤销</li>
                        <li><strong>被动撤销</strong>：异常行为触发自动撤销</li>
                        <li><strong>紧急撤销</strong>：安全威胁触发立即撤销</li>
                        <li><strong>撤销传播</strong>：确保所有端同步撤销状态</li>
                    </ul>
                </section>
            </section>

            <!-- 时间同步机制 -->
            <section id="time-sync">
                <section>
                    <h2>时间同步机制</h2>
                    <p>确保系统各端时间一致性的机制</p>
                </section>
                
                <section>
                    <h3>时间同步重要性</h3>
                    <ul>
                        <li>确保安全验证的时效性</li>
                        <li>防止重放攻击</li>
                        <li>保证操作记录的准确性</li>
                        <li>支持基于时间的权限控制</li>
                    </ul>
                </section>
                
                <section>
                    <h3>同步机制设计</h3>
                    <ul>
                        <li>云平台作为时间权威源</li>
                        <li>支持NTP/PTP时间同步协议</li>
                        <li>车端与云端定期同步</li>
                        <li>手机端与网络时间服务器同步</li>
                        <li>通信过程中的时间偏差校正</li>
                        <li>离线状态下的时钟漂移补偿</li>
                    </ul>
                </section>
            </section>

            <!-- 无感控车方案 -->
            <section id="passive-control">
                <section>
                    <h2>无感控车方案</h2>
                    <p>实现用户无需主动操作的自动控车功能</p>
                </section>
                
                <section>
                    <h3>无感控车流程</h3>
                    <ol>
                        <li>APP后台检测用户状态和环境</li>
                        <li>满足条件时自动发起蓝牙连接</li>
                        <li>快速建立安全通道</li>
                        <li>车端计算与手机的距离</li>
                        <li>距离在安全范围内时执行预设操作</li>
                        <li>用户离开时自动断开连接并锁车</li>
                    </ol>
                </section>
                
                <section>
                    <h3>无感控车泳道图</h3>
                    <img src="images/passive-control-swimlane.png" alt="无感控车泳道图">
                    <p><small>无感控车的详细交互流程</small></p>
                </section>
                
                <section>
                    <h3>距离计算算法</h3>
                    <ul>
                        <li>基于RSSI信号强度计算距离</li>
                        <li>考虑环境干扰因素</li>
                        <li>应用机器学习算法过滤干扰</li>
                        <li>多节点定位提高精度</li>
                        <li>根据距离执行分级权限控制</li>
                    </ul>
                </section>
                
                <section>
                    <h3>省电策略</h3>
                    <ul>
                        <li>基于用户行为智能唤醒</li>
                        <li>动态调整扫描频率</li>
                        <li>优化连接参数</li>
                        <li>低电量模式自动调整</li>
                        <li>智能场景识别减少不必要的连接</li>
                    </ul>
                </section>
            </section>

            <!-- 故障应急处理 -->
            <section id="emergency">
                <section>
                    <h2>故障应急处理机制</h2>
                    <p>应对各种异常情况的处理方案</p>
                </section>
                
                <section>
                    <h3>丢失手机处理</h3>
                    <ul>
                        <li>用户通过云平台远程撤销钥匙</li>
                        <li>支持多设备登录，在其他设备上操作</li>
                        <li>紧急联系人协助处理</li>
                        <li>备用物理钥匙应急</li>
                    </ul>
                </section>
                
                <section>
                    <h3>换手机处理</h3>
                    <ul>
                        <li>在新手机上登录账号</li>
                        <li>云平台验证身份后恢复钥匙</li>
                        <li>旧手机钥匙自动失效</li>
                        <li>新手机重新配对或直接恢复</li>
                    </ul>
                </section>
                
                <section>
                    <h3>通信异常处理</h3>
                    <ul>
                        <li>自动重连机制</li>
                        <li>备用通信通道</li>
                        <li>离线操作缓存</li>
                        <li>网络恢复后自动同步</li>
                    </ul>
                </section>
                
                <section>
                    <h3>安全威胁处理</h3>
                    <ul>
                        <li>异常行为检测与阻断</li>
                        <li>紧急锁定机制</li>
                        <li>安全事件上报与分析</li>
                        <li>远程安全干预</li>
                    </ul>
                </section>
            </section>

            <!-- 结束页 -->
            <section>
                <h2>谢谢观看</h2>
                <p>数字钥匙技术架构展示</p>
            </section>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/reveal.js@4.3.1/dist/reveal.min.js"></script>
    <script>
        Reveal.initialize({
            hash: true,
            slideNumber: true,
            transition: 'slide',
            controls: true,
            progress: true
        });
    </script>
</body>
</html>

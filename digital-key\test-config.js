/**
 * 项目配置测试脚本
 * 用于验证项目配置是否正确
 */

const fs = require('fs')
const path = require('path')

console.log('🧪 开始检查项目配置...\n')

// 检查必要文件
const requiredFiles = [
  'pages.json',
  'manifest.json',
  'main.js',
  'App.vue',
  'package.json'
]

console.log('📁 检查必要文件:')
requiredFiles.forEach(file => {
  const exists = fs.existsSync(path.join(__dirname, file))
  console.log(`  ${exists ? '✅' : '❌'} ${file}`)
})

// 检查pages.json配置
console.log('\n📄 检查pages.json配置:')
try {
  const pagesConfig = JSON.parse(fs.readFileSync(path.join(__dirname, 'pages.json'), 'utf8'))
  
  console.log(`  ✅ pages.json格式正确`)
  console.log(`  📊 页面数量: ${pagesConfig.pages.length}`)
  
  // 检查页面文件是否存在
  let missingPages = 0
  pagesConfig.pages.forEach(page => {
    const pagePath = path.join(__dirname, page.path + '.vue')
    const exists = fs.existsSync(pagePath)
    if (!exists) {
      console.log(`  ❌ 缺失页面: ${page.path}.vue`)
      missingPages++
    }
  })
  
  if (missingPages === 0) {
    console.log(`  ✅ 所有页面文件都存在`)
  } else {
    console.log(`  ⚠️  发现 ${missingPages} 个缺失页面`)
  }
  
} catch (error) {
  console.log(`  ❌ pages.json格式错误: ${error.message}`)
}

// 检查4G控车相关文件
console.log('\n🚗 检查4G控车功能文件:')
const fourGFiles = [
  'utils/websocket/4GControlWebSocket.js',
  'config/4GControlConfig.js',
  'pages/test/4GControlTest.vue',
  'docs/4G控车功能说明.md'
]

fourGFiles.forEach(file => {
  const exists = fs.existsSync(path.join(__dirname, file))
  console.log(`  ${exists ? '✅' : '❌'} ${file}`)
})

// 检查package.json配置
console.log('\n📦 检查package.json配置:')
try {
  const packageConfig = JSON.parse(fs.readFileSync(path.join(__dirname, 'package.json'), 'utf8'))
  
  console.log(`  ✅ package.json格式正确`)
  console.log(`  📊 项目名称: ${packageConfig.name}`)
  console.log(`  📊 版本号: ${packageConfig.version}`)
  
  // 检查脚本
  if (packageConfig.scripts && packageConfig.scripts['dev:mp-weixin']) {
    console.log(`  ✅ 微信小程序开发脚本已配置`)
  } else {
    console.log(`  ❌ 缺少微信小程序开发脚本`)
  }
  
} catch (error) {
  console.log(`  ❌ package.json格式错误: ${error.message}`)
}

// 检查依赖
console.log('\n📚 检查依赖安装:')
const nodeModulesExists = fs.existsSync(path.join(__dirname, 'node_modules'))
console.log(`  ${nodeModulesExists ? '✅' : '❌'} node_modules目录`)

if (nodeModulesExists) {
  const importantDeps = [
    '@dcloudio/uni-ui',
    'crypto-js',
    'js-md5'
  ]
  
  importantDeps.forEach(dep => {
    const depPath = path.join(__dirname, 'node_modules', dep)
    const exists = fs.existsSync(depPath)
    console.log(`  ${exists ? '✅' : '❌'} ${dep}`)
  })
}

console.log('\n🎯 配置检查完成!')
console.log('\n💡 启动建议:')
console.log('  1. 使用HBuilderX IDE打开项目（推荐）')
console.log('  2. 或安装CLI工具: npm install -g @dcloudio/cli')
console.log('  3. 然后运行: npm run dev:mp-weixin')
console.log('\n📖 详细说明请查看: 启动说明.md')

package com.ruoyi.dk.controller;

import com.ruoyi.common.core.web.controller.BaseController;
import com.ruoyi.common.core.web.domain.AjaxResult;
import com.ruoyi.dk.service.IDkDashboardService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 数字钥匙运营工作台Controller
 * 
 * <AUTHOR>
 * @date 2024-01-15
 */
@RestController
@RequestMapping("/dashboard")
public class DkDashboardController extends BaseController {

    @Autowired
    private IDkDashboardService dkDashboardService;

    /**
     * 获取仪表板数据
     */
    @GetMapping("/data")
    public AjaxResult getDashboardData() {
        Map<String, Object> data = dkDashboardService.getDashboardData();
        return AjaxResult.success(data);
    }

    /**
     * 获取待处理事项列表
     */
    @GetMapping("/todoList")
    public AjaxResult getTodoList() {
        List<Map<String, Object>> todoList = dkDashboardService.getTodoList();
        return AjaxResult.success(todoList);
    }

    /**
     * 获取车辆统计数据
     */
    @GetMapping("/vehicleStats")
    public AjaxResult getVehicleStats() {
        Map<String, Object> vehicleStats = dkDashboardService.getVehicleStats();
        return AjaxResult.success(vehicleStats);
    }

    /**
     * 获取最近活动列表
     */
    @GetMapping("/activityList")
    public AjaxResult getActivityList() {
        List<Map<String, Object>> activityList = dkDashboardService.getActivityList();
        return AjaxResult.success(activityList);
    }

    /**
     * 获取运营统计数据
     */
    @GetMapping("/operationStats")
    public AjaxResult getOperationStats() {
        Map<String, Object> operationStats = dkDashboardService.getOperationStats();
        return AjaxResult.success(operationStats);
    }
}

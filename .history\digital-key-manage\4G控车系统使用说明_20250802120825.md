# 4G控车系统使用说明

## 系统概述

4G控车系统是一个基于SpringBoot的云平台应用，实现了完整的4G车辆控制通信流程。系统支持手机APP通过WebSocket发送控车指令，服务器处理指令并通过TCP协议与TBOX设备通信，实现车辆的远程解锁/闭锁控制。

## 系统架构

```
手机APP (WebSocket) ↔ SpringBoot服务器 (TCP) ↔ TBOX设备
```

### 核心组件

1. **TCP服务器** (`TcpServer.java`) - 监听9999端口，接收TBOX客户端连接
2. **TCP客户端处理器** (`TcpClientHandler.java`) - 管理单个TBOX连接和消息处理
3. **4G控车协议服务** (`TcpControlService.java`) - 处理协议解析和指令生成
4. **WebSocket服务器** (`WebSocketServer.java`) - 处理手机端WebSocket连接和4G控车请求
5. **控车API接口** (`VehicleControlController.java`) - 提供RESTful API接口
6. **单页HTML前端** (`4g-vehicle-control.html`) - 可视化控车界面
7. **TBOX模拟器** (`TboxSimulator.java`) - 用于测试的TBOX设备模拟器

## 协议说明

### 4G控车协议格式

- **协议头**: `7E000A000A000102030405060708090A00003033303230303031`
- **状态码**: `04` (解锁) / `03` (闭锁)
- **协议尾**: `007E`

### 完整指令示例

- **解锁指令**: `7E000A000A000102030405060708090A000030333032303030313034007E`
- **闭锁指令**: `7E000A000A000102030405060708090A000030333032303030313033007E`

## 快速启动

### 1. 启动SpringBoot应用

```bash
cd digital-key-manage/ruoyi-modules/ruoyi-system
mvn spring-boot:run
```

应用启动后：
- HTTP服务端口: 9201
- TCP服务端口: 9999
- WebSocket端点: `/websocket/message`

### 2. 启动TBOX模拟器

```bash
# 编译并运行TBOX模拟器
cd digital-key-manage/ruoyi-modules/ruoyi-system
mvn compile exec:java -Dexec.mainClass="com.ruoyi.framework.tcp.TboxSimulator"
```

模拟器命令：
- `unlock` - 发送解锁状态
- `lock` - 发送闭锁状态
- `auto` - 开启自动心跳
- `stop` - 停止自动心跳
- `quit` - 退出程序

### 3. 访问界面

启动完成后可访问以下界面：

- **TBOX模拟器**: http://localhost:9201/tbox-simulator.html
- **手机控车APP**: http://localhost:9201/mobile-control.html
- **原始控车界面**: http://localhost:9201/4g-vehicle-control.html

## 使用方式

### 方式一：完整流程测试（推荐）

1. **启动TBOX模拟器**
   - 打开 `http://localhost:9201/tbox-simulator.html`
   - 点击"连接云平台"建立TCP连接
   - 观察连接状态变为"已连接"

2. **启动手机控车APP**
   - 打开 `http://localhost:9201/mobile-control.html`
   - 等待WebSocket连接建立（绿色指示灯）
   - 点击"解锁车辆"或"闭锁车辆"按钮

3. **观察通信过程**
   - 在手机APP中查看操作结果
   - 在TBOX模拟器中查看收到的控车指令
   - 系统实现了完整的4G控车通信流程

### 方式二：使用Java TBOX模拟器

1. 启动Java TBOX模拟器：
   ```bash
   cd digital-key-manage/ruoyi-modules/ruoyi-system
   mvn compile exec:java -Dexec.mainClass="com.ruoyi.framework.tcp.TboxSimulator"
   ```

2. 使用手机控车APP发送指令：
   - 打开 `http://localhost:9201/mobile-control.html`
   - 发送控车指令

### 方式三：传统Web界面控车

1. 打开控车界面 `http://localhost:9201/4g-vehicle-control.html`
2. 等待WebSocket连接建立（绿色指示灯）
3. 点击"解锁车辆"或"闭锁车辆"按钮
4. 查看消息日志中的操作结果

### 方式二：WebSocket API

发送JSON消息到WebSocket端点 `/websocket/message`：

```json
{
  "messageType": 4,
  "message": "unlock",
  "senderName": "用户",
  "senderId": 1
}
```

支持的控车指令：
- `unlock` / `解锁` - 车辆解锁
- `lock` / `闭锁` - 车辆闭锁

### 方式三：REST API

#### 车辆解锁
```bash
curl -X POST "http://localhost:9201/api/vehicle/unlock"
```

#### 车辆闭锁
```bash
curl -X POST "http://localhost:9201/api/vehicle/lock"
```

#### 查询状态
```bash
curl -X GET "http://localhost:9201/api/vehicle/status"
```

#### 通用控车接口
```bash
curl -X POST "http://localhost:9201/api/vehicle/control" \
  -H "Content-Type: application/json" \
  -d '{"action": "unlock"}'
```

## 测试流程

### 完整测试步骤

1. **启动服务器**
   ```bash
   mvn spring-boot:run
   ```

2. **启动TBOX模拟器**
   ```bash
   mvn compile exec:java -Dexec.mainClass="com.ruoyi.framework.tcp.TboxSimulator"
   ```

3. **测试WebSocket控车**
   - 打开 `http://localhost:9201/4g-vehicle-control.html`
   - 点击解锁/闭锁按钮
   - 观察TBOX模拟器收到指令并响应

4. **测试REST API**
   ```bash
   # 解锁测试
   curl -X POST "http://localhost:9201/api/vehicle/unlock"
   
   # 闭锁测试
   curl -X POST "http://localhost:9201/api/vehicle/lock"
   
   # 状态查询
   curl -X GET "http://localhost:9201/api/vehicle/status"
   ```

5. **运行集成测试**
   ```bash
   mvn test -Dtest=VehicleControlIntegrationTest
   ```

### 预期结果

- ✅ TCP服务器成功启动并监听9999端口
- ✅ TBOX模拟器成功连接到TCP服务器
- ✅ WebSocket连接建立成功
- ✅ 控车指令正确发送和接收
- ✅ 协议解析和状态反馈正常
- ✅ 前端界面实时显示操作结果

## 配置说明

### TCP服务器配置 (bootstrap.yml)

```yaml
tcp:
  server:
    port: 9999        # TCP服务器端口
    enabled: true     # 是否启用TCP服务器
```

### 日志配置

系统使用SLF4J + Logback进行日志记录，关键日志包括：
- 🚀 TCP服务器启动
- 📱 TBOX客户端连接
- 🚗 控车指令处理
- 📤 状态反馈发送
- ❌ 错误信息记录

## 故障排除

### 常见问题

1. **TCP服务器启动失败**
   - 检查端口9999是否被占用
   - 确认配置文件中TCP服务器已启用

2. **TBOX连接失败**
   - 确认TCP服务器已启动
   - 检查防火墙设置
   - 验证IP地址和端口配置

3. **WebSocket连接失败**
   - 检查SpringBoot应用是否正常启动
   - 确认端口9201可访问
   - 查看浏览器控制台错误信息

4. **控车指令无响应**
   - 确认TBOX设备已连接
   - 检查协议格式是否正确
   - 查看服务器日志排查问题

### 调试建议

1. 启用详细日志记录
2. 使用TBOX模拟器进行本地测试
3. 通过REST API验证服务器功能
4. 检查WebSocket连接状态
5. 运行集成测试验证系统功能

## 扩展功能

系统支持以下扩展：

1. **多TBOX管理** - 支持同时连接多个TBOX设备
2. **指定设备控车** - 可向特定TBOX发送控车指令
3. **状态监控** - 实时监控TBOX连接状态
4. **协议扩展** - 支持添加新的控车指令
5. **安全认证** - 可集成用户认证和权限控制

## 技术支持

如有问题，请查看：
1. 系统日志文件
2. 集成测试结果
3. API接口文档
4. 协议规范说明

---

**注意**: 本系统仅用于开发和测试环境，生产环境部署需要额外的安全配置和性能优化。

<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>📱 手机控车APP</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }

        .container {
            max-width: 400px;
            margin: 0 auto;
            background: rgba(255, 255, 255, 0.95);
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            overflow: hidden;
        }

        .header {
            background: linear-gradient(135deg, #007bff, #0056b3);
            color: white;
            padding: 30px;
            text-align: center;
        }

        .header h1 {
            font-size: 2em;
            margin-bottom: 10px;
            text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
        }

        .header p {
            font-size: 1em;
            opacity: 0.9;
        }

        .status-panel {
            padding: 20px;
            background: #f8f9fa;
            border-bottom: 1px solid #e9ecef;
        }

        .status-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 15px;
            padding: 10px;
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        }

        .status-label {
            font-weight: 600;
            color: #495057;
        }

        .status-value {
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .connection-status {
            width: 12px;
            height: 12px;
            border-radius: 50%;
            display: inline-block;
        }

        .connection-status.connected {
            background-color: #28a745;
            box-shadow: 0 0 10px rgba(40, 167, 69, 0.5);
        }

        .connection-status.disconnected {
            background-color: #dc3545;
            box-shadow: 0 0 10px rgba(220, 53, 69, 0.5);
        }

        .control-panel {
            padding: 30px 20px;
            display: flex;
            flex-direction: column;
            gap: 20px;
        }

        .control-btn {
            padding: 20px;
            border: none;
            border-radius: 15px;
            font-size: 1.3em;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 10px;
        }

        .control-btn:hover:not(:disabled) {
            transform: translateY(-2px);
            box-shadow: 0 6px 12px rgba(0, 0, 0, 0.15);
        }

        .control-btn:disabled {
            opacity: 0.6;
            cursor: not-allowed;
        }

        .unlock-btn {
            background: linear-gradient(135deg, #28a745, #20c997);
            color: white;
        }

        .lock-btn {
            background: linear-gradient(135deg, #dc3545, #c82333);
            color: white;
        }

        .message-panel {
            padding: 20px;
            max-height: 300px;
            overflow-y: auto;
        }

        .message-item {
            margin-bottom: 10px;
            padding: 12px;
            border-radius: 8px;
            border-left: 4px solid;
        }

        .message-item.info {
            background-color: #d1ecf1;
            border-left-color: #17a2b8;
            color: #0c5460;
        }

        .message-item.success {
            background-color: #d4edda;
            border-left-color: #28a745;
            color: #155724;
        }

        .message-item.error {
            background-color: #f8d7da;
            border-left-color: #dc3545;
            color: #721c24;
        }

        .message-time {
            font-size: 0.8em;
            opacity: 0.7;
            margin-bottom: 5px;
        }

        .message-content {
            font-weight: 500;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>📱 手机控车APP</h1>
            <p>4G远程车辆控制</p>
        </div>

        <div class="status-panel">
            <div class="status-item">
                <span class="status-label">连接状态:</span>
                <span class="status-value">
                    <span id="connectionStatus" class="connection-status disconnected"></span>
                    <span id="connectionText">未连接</span>
                </span>
            </div>
            <div class="status-item">
                <span class="status-label">在线TBOX:</span>
                <span class="status-value" id="tboxCount">0</span>
            </div>
        </div>

        <div class="control-panel">
            <button id="unlockBtn" class="control-btn unlock-btn" onclick="sendUnlockCommand()" disabled>
                🔓 解锁车辆
            </button>
            <button id="lockBtn" class="control-btn lock-btn" onclick="sendLockCommand()" disabled>
                🔒 闭锁车辆
            </button>
        </div>

        <div class="message-panel">
            <h3 style="margin-bottom: 15px; color: #333;">📨 操作日志</h3>
            <div id="messageContainer">
                <div class="message-item info">
                    <div class="message-time">系统启动</div>
                    <div class="message-content">手机控车APP已初始化，正在连接云平台...</div>
                </div>
            </div>
        </div>
    </div>

    <script>
        let websocket = null;
        let isConnected = false;

        // 初始化WebSocket连接
        function initWebSocket() {
            const protocol = window.location.protocol === 'https:' ? 'wss:' : 'ws:';
            const wsUrl = `${protocol}//${window.location.host}/websocket/message?userId=1`;
            
            try {
                websocket = new WebSocket(wsUrl);
                
                websocket.onopen = function(event) {
                    isConnected = true;
                    updateConnectionStatus(true);
                    addMessage('success', '云平台连接成功');
                    refreshStatus();
                };
                
                websocket.onmessage = function(event) {
                    try {
                        const message = JSON.parse(event.data);
                        handleWebSocketMessage(message);
                    } catch (e) {
                        addMessage('info', '收到消息: ' + event.data);
                    }
                };
                
                websocket.onclose = function(event) {
                    isConnected = false;
                    updateConnectionStatus(false);
                    addMessage('error', '云平台连接已断开');
                    
                    // 5秒后尝试重连
                    setTimeout(initWebSocket, 5000);
                };
                
                websocket.onerror = function(error) {
                    addMessage('error', '云平台连接错误');
                    console.error('WebSocket error:', error);
                };
                
            } catch (e) {
                addMessage('error', '云平台连接初始化失败: ' + e.message);
                console.error('WebSocket init error:', e);
            }
        }

        // 处理WebSocket消息
        function handleWebSocketMessage(message) {
            if (message.senderName === '4G控车系统') {
                if (message.message.includes('错误')) {
                    addMessage('error', message.message);
                } else {
                    addMessage('success', message.message);
                }
            } else if (message.senderName === 'TBOX') {
                addMessage('info', message.message);
            } else {
                addMessage('info', message.message || '收到系统消息');
            }
        }

        // 发送解锁指令
        function sendUnlockCommand() {
            if (!isConnected) {
                addMessage('error', '请先连接云平台');
                return;
            }
            
            const message = {
                messageType: 4,
                message: 'unlock',
                senderName: '手机用户',
                senderId: 1
            };
            
            websocket.send(JSON.stringify(message));
            addMessage('info', '发送解锁指令...');
        }

        // 发送闭锁指令
        function sendLockCommand() {
            if (!isConnected) {
                addMessage('error', '请先连接云平台');
                return;
            }
            
            const message = {
                messageType: 4,
                message: 'lock',
                senderName: '手机用户',
                senderId: 1
            };
            
            websocket.send(JSON.stringify(message));
            addMessage('info', '发送闭锁指令...');
        }

        // 刷新状态
        function refreshStatus() {
            if (!isConnected) return;
            
            fetch('/api/vehicle/status')
                .then(response => response.json())
                .then(data => {
                    if (data.code === 200) {
                        document.getElementById('tboxCount').textContent = data.data.connectedTboxCount || 0;
                    }
                })
                .catch(error => {
                    console.error('刷新状态失败:', error);
                });
        }

        // 更新连接状态
        function updateConnectionStatus(connected) {
            const statusElement = document.getElementById('connectionStatus');
            const textElement = document.getElementById('connectionText');
            const unlockBtn = document.getElementById('unlockBtn');
            const lockBtn = document.getElementById('lockBtn');
            
            if (connected) {
                statusElement.className = 'connection-status connected';
                textElement.textContent = '已连接';
                unlockBtn.disabled = false;
                lockBtn.disabled = false;
            } else {
                statusElement.className = 'connection-status disconnected';
                textElement.textContent = '未连接';
                unlockBtn.disabled = true;
                lockBtn.disabled = true;
            }
        }

        // 添加消息
        function addMessage(type, content) {
            const container = document.getElementById('messageContainer');
            const messageDiv = document.createElement('div');
            messageDiv.className = `message-item ${type}`;
            
            const time = new Date().toLocaleTimeString();
            messageDiv.innerHTML = `
                <div class="message-time">${time}</div>
                <div class="message-content">${content}</div>
            `;
            
            container.appendChild(messageDiv);
            container.scrollTop = container.scrollHeight;
        }

        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', function() {
            initWebSocket();
            
            // 定期刷新状态
            setInterval(refreshStatus, 30000); // 每30秒刷新一次
        });
    </script>
</body>
</html>

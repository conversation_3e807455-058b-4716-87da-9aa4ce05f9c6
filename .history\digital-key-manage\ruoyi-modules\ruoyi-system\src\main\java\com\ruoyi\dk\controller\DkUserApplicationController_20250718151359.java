package com.ruoyi.dk.controller;

import com.ruoyi.common.core.web.controller.BaseController;
import com.ruoyi.common.core.web.domain.AjaxResult;
import com.ruoyi.common.core.web.page.TableDataInfo;
import com.ruoyi.dk.domain.DkUserApplication;
import com.ruoyi.dk.service.IDkUserApplicationService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.Map;

/**
 * 用户申请管理Controller
 * 
 * <AUTHOR>
 * @date 2024-01-15
 */
@RestController
@RequestMapping("/dk/userApplication")
public class DkUserApplicationController extends BaseController {

    @Autowired(required = false)
    private IDkUserApplicationService dkUserApplicationService;

    /**
     * 查询用户申请列表
     */
    @GetMapping("/list")
    public TableDataInfo list(DkUserApplication dkUserApplication) {
        try {
            if (dkUserApplicationService != null) {
                startPage();
                List<DkUserApplication> list = dkUserApplicationService.selectDkUserApplicationList(dkUserApplication);
                return getDataTable(list);
            }
        } catch (Exception e) {
            logger.error("查询用户申请列表失败", e);
        }

        // 返回模拟数据
        List<Map<String, Object>> mockList = new ArrayList<>();

        Map<String, Object> app1 = new HashMap<>();
        app1.put("applicationId", 1L);
        app1.put("applicationNo", "APP20240115001");
        app1.put("realName", "张三");
        app1.put("phone", "**********1");
        app1.put("idCard", "110101199001011234");
        app1.put("drivingLicense", "110101199001011234");
        app1.put("status", "0");
        app1.put("applicationReason", "商务出行需要");
        app1.put("createTime", "2024-01-15 09:30:00");
        app1.put("auditUserName", "");
        mockList.add(app1);

        Map<String, Object> app2 = new HashMap<>();
        app2.put("applicationId", 2L);
        app2.put("applicationNo", "APP20240115002");
        app2.put("realName", "李四");
        app2.put("phone", "**********2");
        app2.put("idCard", "110101199002021234");
        app2.put("drivingLicense", "110101199002021234");
        app2.put("status", "1");
        app2.put("applicationReason", "日常通勤使用");
        app2.put("createTime", "2024-01-15 10:15:00");
        app2.put("auditUserName", "管理员");
        mockList.add(app2);

        Map<String, Object> app3 = new HashMap<>();
        app3.put("applicationId", 3L);
        app3.put("applicationNo", "APP20240115003");
        app3.put("realName", "王五");
        app3.put("phone", "**********3");
        app3.put("idCard", "110101199003031234");
        app3.put("drivingLicense", "110101199003031234");
        app3.put("status", "2");
        app3.put("applicationReason", "周末出游");
        app3.put("createTime", "2024-01-15 11:00:00");
        app3.put("auditUserName", "管理员");
        mockList.add(app3);

        TableDataInfo dataTable = new TableDataInfo();
        dataTable.setRows(mockList);
        dataTable.setTotal(mockList.size());
        return dataTable;
    }

    /**
     * 获取用户申请详细信息
     */
    @GetMapping(value = "/{applicationId}")
    public AjaxResult getInfo(@PathVariable("applicationId") Long applicationId) {
        try {
            if (dkUserApplicationService != null) {
                return AjaxResult
                        .success(dkUserApplicationService.selectDkUserApplicationByApplicationId(applicationId));
            }
        } catch (Exception e) {
            logger.error("获取用户申请详细信息失败", e);
        }

        // 返回模拟数据
        Map<String, Object> mockData = new HashMap<>();
        mockData.put("applicationId", applicationId);
        mockData.put("applicationNo", "APP20240115" + String.format("%03d", applicationId));
        mockData.put("realName", "测试用户" + applicationId);
        mockData.put("phone", "**********" + applicationId);
        mockData.put("idCard", "11010119900101123" + applicationId);
        mockData.put("drivingLicense", "11010119900101123" + applicationId);
        mockData.put("status", "0");
        mockData.put("applicationReason", "测试申请原因");
        mockData.put("createTime", "2024-01-15 09:30:00");

        return AjaxResult.success(mockData);
    }

    /**
     * 新增用户申请
     */
    @PostMapping
    public AjaxResult add(@RequestBody DkUserApplication dkUserApplication) {
        try {
            if (dkUserApplicationService != null) {
                return toAjax(dkUserApplicationService.insertDkUserApplication(dkUserApplication));
            }
        } catch (Exception e) {
            logger.error("新增用户申请失败", e);
        }

        // 模拟成功
        return AjaxResult.success("新增成功");
    }

    /**
     * 修改用户申请
     */
    @PutMapping
    public AjaxResult edit(@RequestBody DkUserApplication dkUserApplication) {
        try {
            if (dkUserApplicationService != null) {
                return toAjax(dkUserApplicationService.updateDkUserApplication(dkUserApplication));
            }
        } catch (Exception e) {
            logger.error("修改用户申请失败", e);
        }

        // 模拟成功
        return AjaxResult.success("修改成功");
    }

    /**
     * 删除用户申请
     */
    @DeleteMapping("/{applicationIds}")
    public AjaxResult remove(@PathVariable Long[] applicationIds) {
        try {
            if (dkUserApplicationService != null) {
                return toAjax(dkUserApplicationService.deleteDkUserApplicationByApplicationIds(applicationIds));
            }
        } catch (Exception e) {
            logger.error("删除用户申请失败", e);
        }

        // 模拟成功
        return AjaxResult.success("删除成功");
    }

    /**
     * 审核用户申请
     */
    @PutMapping("/audit")
    public AjaxResult audit(@RequestBody Map<String, Object> auditData) {
        try {
            if (dkUserApplicationService != null) {
                Long applicationId = Long.valueOf(auditData.get("applicationId").toString());
                String status = auditData.get("status").toString();
                String auditRemark = auditData.get("auditRemark").toString();
                return toAjax(dkUserApplicationService.auditApplication(applicationId, status, auditRemark));
            }
        } catch (Exception e) {
            logger.error("审核用户申请失败", e);
        }

        // 模拟成功
        return AjaxResult.success("审核成功");
    }

    /**
     * 批量审核用户申请
     */
    @PreAuthorize("@ss.hasPermi('dk:application:audit')")
    @PutMapping("/batchAudit")
    public AjaxResult batchAudit(@RequestBody Map<String, Object> auditData) {
        try {
            if (dkUserApplicationService != null) {
                Long[] applicationIds = (Long[]) auditData.get("applicationIds");
                String status = auditData.get("status").toString();
                String auditRemark = auditData.get("auditRemark").toString();
                return toAjax(dkUserApplicationService.batchAuditApplication(applicationIds, status, auditRemark));
            }
        } catch (Exception e) {
            logger.error("批量审核用户申请失败", e);
        }

        // 模拟成功
        return AjaxResult.success("批量审核成功");
    }

    /**
     * 获取待审核申请数量
     */
    @PreAuthorize("@ss.hasPermi('dk:application:query')")
    @GetMapping("/pendingCount")
    public AjaxResult getPendingApplicationCount() {
        try {
            if (dkUserApplicationService != null) {
                int count = dkUserApplicationService.getPendingApplicationCount();
                return AjaxResult.success(count);
            }
        } catch (Exception e) {
            logger.error("获取待审核申请数量失败", e);
        }

        // 返回模拟数据
        return AjaxResult.success(8);
    }
}

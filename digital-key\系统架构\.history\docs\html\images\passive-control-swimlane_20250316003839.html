<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>无感控车流程泳道图</title>
    <style>
        body {
            font-family: 'Microsoft YaHei', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .swimlane-container {
            max-width: 1000px;
            margin: 0 auto;
            background-color: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
        }
        h1 {
            color: #2874a6;
            text-align: center;
            margin-bottom: 30px;
        }
        .swimlane {
            display: flex;
            margin-bottom: 40px;
        }
        .lane-title {
            width: 120px;
            padding: 10px;
            text-align: center;
            font-weight: bold;
            border-radius: 5px 0 0 5px;
        }
        .mobile-lane .lane-title {
            background-color: #d4e6f1;
            border: 2px solid #3498db;
        }
        .car-lane .lane-title {
            background-color: #d5f5e3;
            border: 2px solid #2ecc71;
        }
        .cloud-lane .lane-title {
            background-color: #ebdef0;
            border: 2px solid #9b59b6;
        }
        .external-lane .lane-title {
            background-color: #fdebd0;
            border: 2px solid #f39c12;
        }
        .lane-content {
            flex: 1;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 0 5px 5px 0;
        }
        .step {
            margin-bottom: 15px;
            padding: 10px;
            background-color: #f8f9f9;
            border-radius: 5px;
            position: relative;
        }
        .step-number {
            font-weight: bold;
            color: #3498db;
            margin-right: 5px;
        }
        .arrow {
            position: absolute;
            bottom: -15px;
            left: 50%;
            transform: translateX(-50%);
            color: #7f8c8d;
            font-size: 20px;
        }
        .note {
            margin: 20px 0;
            padding: 10px;
            background-color: #f9ebea;
            border-left: 4px solid #e74c3c;
            border-radius: 0 5px 5px 0;
        }
        .note-title {
            font-weight: bold;
            color: #c0392b;
            margin-bottom: 5px;
        }
        .phase {
            margin: 30px 0;
            padding: 10px;
            background-color: #eaeded;
            text-align: center;
            font-weight: bold;
            color: #2c3e50;
            border-radius: 5px;
        }
        .condition {
            margin: 20px 0;
            padding: 10px;
            background-color: #eafaf1;
            border-left: 4px solid #27ae60;
            border-radius: 0 5px 5px 0;
        }
        .condition-title {
            font-weight: bold;
            color: #27ae60;
            margin-bottom: 5px;
        }
    </style>
</head>
<body>
    <div class="swimlane-container">
        <h1>数字钥匙无感控车流程</h1>
        
        <div class="note">
            <div class="note-title">无感控车概述：</div>
            <p>无感控车是指手机在不需要用户主动操作的情况下，自动与车辆建立蓝牙连接并执行预设操作的功能。前提是APP已在手机后台运行，且已获得必要权限。</p>
        </div>
        
        <div class="phase">无感连接建立</div>
        
        <div class="swimlane mobile-lane">
            <div class="lane-title">APP(后台运行)</div>
            <div class="lane-content">
                <div class="step">
                    <span class="step-number">1a.</span> 系统唤醒后台APP
                    <div class="arrow">↓</div>
                </div>
                <div class="step">
                    <span class="step-number">1b.</span> 检查是否满足连接条件
                </div>
            </div>
        </div>
        
        <div class="note">
            <div class="note-title">连接条件包括：</div>
            <ul>
                <li>用户已启用无感连接</li>
                <li>手机电量充足</li>
                <li>检测到用户走路状态（省电策略）</li>
            </ul>
        </div>
        
        <div class="swimlane mobile-lane">
            <div class="lane-title">APP(后台运行)</div>
            <div class="lane-content">
                <div class="step">
                    <span class="step-number">2.</span> 从安全区域读取已存储的车辆连接信息
                    <div class="arrow">↓</div>
                </div>
                <div class="step">
                    <span class="step-number">3.</span> 直接发起蓝牙连接请求
                    <div class="arrow">→</div>
                </div>
            </div>
        </div>
        
        <div class="note">
            <div class="note-title">连接信息包含：</div>
            <ul>
                <li>BLE无感连接必要信息（设备UUID等）</li>
                <li>配对密钥</li>
            </ul>
        </div>
        
        <div class="swimlane car-lane">
            <div class="lane-title">车端</div>
            <div class="lane-content">
                <div class="step">
                    <span class="step-number">4a.</span> 验证连接请求来源
                    <div class="arrow">↓</div>
                </div>
                <div class="step">
                    <span class="step-number">4b.</span> 检查连接信息有效性
                    <div class="arrow">↓</div>
                </div>
                <div class="step">
                    <span class="step-number">4c.</span> 检查是否满足自动接受条件
                    <div class="arrow">↓</div>
                </div>
                <div class="step">
                    <span class="step-number">4d.</span> 接受连接请求
                    <div class="arrow">←</div>
                </div>
            </div>
        </div>
        
        <div class="note">
            <div class="note-title">自动接受条件：</div>
            <ul>
                <li>连接信息有效</li>
                <li>车辆处于安全状态</li>
                <li>未触发安全警报</li>
                <li>电量状态正常</li>
            </ul>
        </div>
        
        <div class="phase">安全认证过程</div>
        
        <div class="swimlane mobile-lane">
            <div class="lane-title">APP(后台运行)</div>
            <div class="lane-content">
                <div class="step">
                    <span class="step-number">8.</span> 发送身份验证请求（包含设备ID和时间戳）
                    <div class="arrow">→</div>
                </div>
            </div>
        </div>
        
        <div class="swimlane car-lane">
            <div class="lane-title">车端</div>
            <div class="lane-content">
                <div class="step">
                    <span class="step-number">9a.</span> 从安全区域读取该设备的会话密钥和配对信息
                    <div class="arrow">↓</div>
                </div>
                <div class="step">
                    <span class="step-number">9b.</span> 发送认证挑战（随机数和车辆会话标识）
                    <div class="arrow">←</div>
                </div>
            </div>
        </div>
        
        <div class="swimlane mobile-lane">
            <div class="lane-title">APP(后台运行)</div>
            <div class="lane-content">
                <div class="step">
                    <span class="step-number">10a.</span> 根据随机数、会话密钥和时间戳计算认证响应
                    <div class="arrow">↓</div>
                </div>
                <div class="step">
                    <span class="step-number">10b.</span> 添加防重放保护（序列号）
                    <div class="arrow">↓</div>
                </div>
                <div class="step">
                    <span class="step-number">10c.</span> 发送认证响应和挑战（包含对车辆的随机挑战）
                    <div class="arrow">→</div>
                </div>
            </div>
        </div>
        
        <div class="swimlane car-lane">
            <div class="lane-title">车端</div>
            <div class="lane-content">
                <div class="step">
                    <span class="step-number">11a.</span> 验证认证响应和防重放信息
                    <div class="arrow">↓</div>
                </div>
                <div class="step">
                    <span class="step-number">11b.</span> 计算对手机挑战的响应
                    <div class="arrow">↓</div>
                </div>
                <div class="step">
                    <span class="step-number">11c.</span> 发送认证结果和挑战响应
                    <div class="arrow">←</div>
                </div>
            </div>
        </div>
        
        <div class="swimlane mobile-lane">
            <div class="lane-title">APP(后台运行)</div>
            <div class="lane-content">
                <div class="step">
                    <span class="step-number">11d.</span> 验证车辆的挑战响应
                </div>
            </div>
        </div>
        
        <div class="note">
            <div class="note-title">完整双向认证确保：</div>
            <ol>
                <li>手机确实是已配对的授权设备</li>
                <li>车辆确实是用户的车辆</li>
                <li>每次认证使用唯一随机数</li>
                <li>添加序列号防止重放攻击</li>
            </ol>
        </div>
        
        <div class="phase">快速安全通道建立</div>
        
        <div class="swimlane mobile-lane">
            <div class="lane-title">APP(后台运行)</div>
            <div class="lane-content">
                <div class="step">
                    <span class="step-number">12a.</span> 从安全区域获取会话密钥
                    <div class="arrow">↓</div>
                </div>
                <div class="step">
                    <span class="step-number">12b.</span> 验证会话密钥有效期
                </div>
            </div>
        </div>
        
        <div class="condition">
            <div class="condition-title">条件判断：会话密钥是否有效</div>
        </div>
        
        <div class="swimlane mobile-lane">
            <div class="lane-title">APP(后台运行)</div>
            <div class="lane-content">
                <div class="step">
                    <span class="step-number">12c.</span> 使用有效会话密钥快速建立安全通道
                    <div class="arrow">↔</div>
                </div>
            </div>
        </div>
        
        <div class="swimlane car-lane">
            <div class="lane-title">车端</div>
            <div class="lane-content">
                <div class="step">
                    <span class="step-number">12c.</span> 使用有效会话密钥快速建立安全通道
                    <div class="arrow">↔</div>
                </div>
            </div>
        </div>
        
        <div class="note">
            <div class="note-title">会话密钥无效时的处理：</div>
            <ol>
                <li>发起完整密钥协商请求</li>
                <li>交换密钥协商参数</li>
                <li>生成新的会话密钥材料</li>
                <li>验证新会话密钥并建立安全通道</li>
                <li>在安全区域更新会话密钥</li>
            </ol>
            <p>会话密钥会定期更新以保证安全性</p>
        </div>
        
        <div class="phase">距离计算与状态同步</div>
        
        <div class="swimlane car-lane">
            <div class="lane-title">车端</div>
            <div class="lane-content">
                <div class="step">
                    <span class="step-number">13a.</span> 从连接中获取RSSI值
                    <div class="arrow">↓</div>
                </div>
                <div class="step">
                    <span class="step-number">13b.</span> 计算与手机的距离（多节点则是成对RSSI值进入算法）
                    <div class="arrow">↓</div>
                </div>
                <div class="step">
                    <span class="step-number">14.</span> 发送车辆当前状态信息
                    <div class="arrow">←</div>
                </div>
            </div>
        </div>
        
        <div class="note">
            <div class="note-title">距离计算考虑因素：</div>
            <ul>
                <li>RSSI信号强度</li>
                <li>环境干扰因素</li>
                <li>信号反射影响</li>
                <li>历史数据校准</li>
            </ul>
        </div>
        
        <div class="note">
            <div class="note-title">车辆状态信息包含：</div>
            <ul>
                <li>车门锁状态</li>
                <li>车窗状态</li>
                <li>发动机状态</li>
                <li>电池电量</li>
                <li>其他车辆信息</li>
                <li>计算得到的距离信息</li>
            </ul>
        </div>
        
        <div class="swimlane mobile-lane">
            <div class="lane-title">APP(后台运行)</div>
            <div class="lane-content">
                <div class="step">
                    <span class="step-number">15.</span> 后台更新车辆状态缓存
                </div>
            </div>
        </div>
        
        <div class="condition">
            <div class="condition-title">条件判断：距离是否在安全范围内</div>
        </div>
        
        <div class="phase">自动控车执行</div>
        
        <div class="swimlane mobile-lane">
            <div class="lane-title">APP(后台运行)</div>
            <div class="lane-content">
                <div class="step">
                    <span class="step-number">16a.</span> 根据用户预设策略准备自动控车指令
                    <div class="arrow">↓</div>
                </div>
                <div class="step">
                    <span class="step-number">16b.</span> 发送控车请求，请求挑战信息
                    <div class="arrow">→</div>
                </div>
            </div>
        </div>
        
        <div class="note">
            <div class="note-title">可能的自动控车指令：</div>
            <ul>
                <li>车门解锁</li>
                <li>迎宾灯控制等</li>
            </ul>
        </div>
        
        <div class="swimlane car-lane">
            <div class="lane-title">车端</div>
            <div class="lane-content">
                <div class="step">
                    <span class="step-number">16c.</span> 返回随机挑战数
                    <div class="arrow">←</div>
                </div>
            </div>
        </div>
        
        <div class="swimlane mobile-lane">
            <div class="lane-title">APP(后台运行)</div>
            <div class="lane-content">
                <div class="step">
                    <span class="step-number">16d.</span> 使用收到的随机挑战数生成控车指令并签名
                    <div class="arrow">↓</div>
                </div>
                <div class="step">
                    <span class="step-number">16e.</span> 使用会话密钥加密指令
                    <div class="arrow">↓</div>
                </div>
                <div class="step">
                    <span class="step-number">17.</span> 发送加密的控车指令
                    <div class="arrow">→</div>
                </div>
            </div>
        </div>
        
        <div class="note">
            <div class="note-title">控车指令包含：</div>
            <ul>
                <li>操作类型（如解锁、上锁等）</li>
                <li>时间戳（辅助信息）</li>
                <li>车端提供的随机挑战数（确保唯一性）</li>
                <li>数字签名（验证指令完整性）</li>
            </ul>
        </div>
        
        <div class="swimlane car-lane">
            <div class="lane-title">车端</div>
            <div class="lane-content">
                <div class="step">
                    <span class="step-number">18a.</span> 使用会话密钥解密指令
                    <div class="arrow">↓</div>
                </div>
                <div class="step">
                    <span class="step-number">18b.</span> 验证数字签名
                    <div class="arrow">↓</div>
                </div>
                <div class="step">
                    <span class="step-number">18c.</span> 检查用户权限
                    <div class="arrow">↓</div>
                </div>
                <div class="step">
                    <span class="step-number">18d.</span> 验证指令中的随机挑战数是否为刚刚发出且未使用过的
                </div>
            </div>
        </div>
        
        <div class="note">
            <div class="note-title">权限检查包括：</div>
            <ul>
                <li>验证用户身份（车主/临时用户）</li>
                <li>检查操作权限级别</li>
                <li>验证操作授权状态</li>
                <li>检查安全限制条件</li>
            </ul>
        </div>
        
        <div class="note">
            <div class="note-title">随机挑战验证：</div>
            <ul>
                <li>确认是否为本次会话刚刚生成的随机数</li>
                <li>确保每个随机挑战数只被使用一次</li>
                <li>比传统时效性检查更安全，完全防止重放攻击</li>
            </ul>
        </div>
        
        <div class="condition">
            <div class="condition-title">条件判断：指令验证是否通过</div>
        </div>
        
        <div class="swimlane car-lane">
            <div class="lane-title">车端</div>
            <div class="lane-content">
                <div class="step">
                    <span class="step-number">19a.</span> 执行控车操作
                    <div class="arrow">↓</div>
                </div>
                <div class="step">
                    <span class="step-number">19b.</span> 返回操作执行结果
                    <div class="arrow">←</div>
                </div>
                <div class="step">
                    <span class="step-number">19c.</span> 上报操作记录（可选，根据网络状态）
                    <div class="arrow">→</div>
                </div>
            </div>
        </div>
        
        <div class="swimlane cloud-lane">
            <div class="lane-title">钥匙云平台</div>
            <div class="lane-content">
                <div class="step">
                    <span class="step-number">19c.</span> 接收并记录操作记录
                </div>
            </div>
        </div>
        
        <div class="swimlane mobile-lane">
            <div class="lane-title">APP(后台运行)</div>
            <div class="lane-content">
                <div class="step">
                    <span class="step-number">19d.</span> 生成系统通知提醒用户操作已执行
                </div>
            </div>
        </div>
        
        <div class="note">
            <div class="note-title">指令验证失败的处理：</div>
            <ul>
                <li>车端返回验证失败信息</li>
                <li>APP记录失败原因，不打扰用户</li>
                <li>失败原因可能是：解密失败、签名验证失败、权限不足、指令过期、重复执行</li>
            </ul>
        </div>
        
        <div class="phase">断开连接处理</div>
        
        <div class="note">
            <div class="note-title">距离超出安全范围的处理分为两种情况：</div>
            <ol>
                <li>用户正常离开 - 距离逐渐增大，有足够时间执行完整断开流程</li>
                <li>用户快速离开 - 距离快速增大，需要紧急处理</li>
            </ol>
        </div>
        
        <div class="condition">
            <div class="condition-title">条件判断：用户正常离开还是快速离开</div>
        </div>
        
        <div class="swimlane car-lane">
            <div class="lane-title">车端</div>
            <div class="lane-content">
                <div class="step">
                    <span class="step-number">21a.</span> 发送距离预警通知
                    <div class="arrow">←</div>
                </div>
            </div>
        </div>
        
        <div class="swimlane mobile-lane">
            <div class="lane-title">APP(后台运行)</div>
            <div class="lane-content">
                <div class="step">
                    <span class="step-number">21b.</span> 准备断开连接前的操作
                    <div class="arrow">↓</div>
                </div>
                <div class="step">
                    <span class="step-number">21c.</span> 发送最终控车指令(如上锁)
                    <div class="arrow">→</div>
                </div>
            </div>
        </div>
        
        <div class="note">
            <div class="note-title">断开前操作可能包括：</div>
            <ul>
                <li>发送车门上锁指令</li>
                <li>关闭车窗</li>
                <li>其他安全措施</li>
            </ul>
        </div>
        
        <div class="swimlane car-lane">
            <div class="lane-title">车端</div>
            <div class="lane-content">
                <div class="step">
                    <span class="step-number">21d.</span> 执行最终控车操作
                    <div class="arrow">↓</div>
                </div>
                <div class="step">
                    <span class="step-number">21e.</span> 返回操作结果
                    <div class="arrow">←</div>
                </div>
            </div>
        </div>
        
        <div class="swimlane mobile-lane">
            <div class="lane-title">APP(后台运行)</div>
            <div class="lane-content">
                <div class="step">
                    <span class="step-number">21f.</span> 发送断开连接请求
                    <div class="arrow">→</div>
                </div>
            </div>
        </div>
        
        <div class="swimlane car-lane">
            <div class="lane-title">车端</div>
            <div class="lane-content">
                <div class="step">
                    <span class="step-number">21g.</span> 清理临时会话资源
                </div>
            </div>
        </div>
        
        <div class="swimlane mobile-lane">
            <div class="lane-title">APP(后台运行)</div>
            <div class="lane-content">
                <div class="step">
                    <span class="step-number">21h.</span> 清理临时会话资源
                    <div class="arrow">↓</div>
                </div>
                <div class="step">
                    <span class="step-number">21i.</span> 生成系统通知提醒用户车辆已锁定
                </div>
            </div>
        </div>
        
        <div class="note">
            <div class="note-title">用户快速离开的紧急处理：</div>
            <ol>
                <li>车端检测到距离快速增大或信号急剧减弱</li>
                <li>触发紧急安全措施（自动执行车辆上锁、关闭可能的危险设备等）</li>
                <li>设置安全确认标志，等待下次连接确认</li>
                <li>上报紧急安全措施执行结果</li>
                <li>强制清理会话资源</li>
                <li>手机检测到连接丢失，执行本地紧急处理</li>
                <li>向用户推送高优先级警告通知（如果安全措施执行失败）</li>
            </ol>
        </div>
    </div>
</body>
</html> 
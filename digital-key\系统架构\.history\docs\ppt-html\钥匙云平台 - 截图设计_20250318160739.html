<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>钥匙云平台</title>
    <!-- 引入html2canvas库 -->
    <script src="https://html2canvas.hertzen.com/dist/html2canvas.min.js"></script>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
            font-family: 'PingFang SC', 'Microsoft YaHei', sans-serif;
        }
        
        body {
            background: #f0f0f0;
            display: flex;
            justify-content: center;
            align-items: center;
            min-height: 100vh;
            color: #333;
            padding: 20px;
        }
        
        /* PPT边框指示器 */
        .ppt-container {
            position: relative;
            width: 1920px; /* 标准PPT宽度 */
            height: 1080px; /* 标准PPT高度 */
            box-shadow: 0 0 30px rgba(0, 0, 0, 0.2);
            border: 2px dashed #ccc;
            background-image: linear-gradient(45deg, #ccc 25%, transparent 25%), 
                              linear-gradient(-45deg, #ccc 25%, transparent 25%), 
                              linear-gradient(45deg, transparent 75%, #ccc 75%), 
                              linear-gradient(-45deg, transparent 75%, #ccc 75%);
            background-size: 20px 20px;
            background-position: 0 0, 0 10px, 10px -10px, -10px 0px;
        }
        
        .ppt-label {
            position: absolute;
            top: -40px;
            left: 0;
            background-color: #555;
            color: white;
            padding: 5px 15px;
            border-radius: 4px;
            font-size: 16px;
        }
        
        /* 复制按钮 */
        .copy-button {
            position: absolute;
            top: 20px;
            right: 20px;
            background: linear-gradient(135deg, #3498db, #2980b9);
            color: white;
            border: none;
            padding: 12px 25px;
            border-radius: 30px;
            font-size: 18px;
            cursor: pointer;
            z-index: 1000;
            box-shadow: 0 4px 10px rgba(0, 0, 0, 0.2);
            display: flex;
            align-items: center;
            gap: 10px;
            transition: all 0.3s ease;
        }
        
        /* 下载按钮 */
        .download-button {
            position: absolute;
            top: 20px;
            right: 230px;
            background: linear-gradient(135deg, #2ecc71, #27ae60);
            color: white;
            border: none;
            padding: 12px 25px;
            border-radius: 30px;
            font-size: 18px;
            cursor: pointer;
            z-index: 1000;
            box-shadow: 0 4px 10px rgba(0, 0, 0, 0.2);
            display: flex;
            align-items: center;
            gap: 10px;
            transition: all 0.3s ease;
        }
        
        .copy-button:hover, .download-button:hover {
            transform: translateY(-3px);
            box-shadow: 0 6px 15px rgba(0, 0, 0, 0.25);
        }
        
        .copy-button:active, .download-button:active {
            transform: translateY(0);
        }
        
        .copy-icon {
            width: 20px;
            height: 20px;
            background-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="white"><path d="M19,21H8V7H19M19,5H8A2,2 0 0,0 6,7V21A2,2 0 0,0 8,23H19A2,2 0 0,0 21,21V7A2,2 0 0,0 19,5M16,1H4A2,2 0 0,0 2,3V17H4V3H16V1Z" /></svg>');
            background-size: contain;
            background-repeat: no-repeat;
        }
        
        .download-icon {
            width: 20px;
            height: 20px;
            background-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="white"><path d="M5,20H19V18H5M19,9H15V3H9V9H5L12,16L19,9Z" /></svg>');
            background-size: contain;
            background-repeat: no-repeat;
        }
        
        /* 清晰度选择器样式 */
        .resolution-selector {
            position: absolute;
            top: 70px;
            right: 230px;
            display: flex;
            align-items: center;
            background: rgba(255, 255, 255, 0.9);
            padding: 8px 15px;
            border-radius: 30px;
            box-shadow: 0 4px 10px rgba(0, 0, 0, 0.1);
            z-index: 1000;
        }
        
        .resolution-label {
            margin-right: 10px;
            font-size: 14px;
            color: #555;
        }
        
        .resolution-btn {
            border: none;
            background: #f0f0f0;
            padding: 5px 10px;
            margin: 0 3px;
            border-radius: 15px;
            font-size: 14px;
            cursor: pointer;
            transition: all 0.2s ease;
            color: #555;
        }
        
        .resolution-btn:hover {
            background: #e0e0e0;
        }
        
        .resolution-btn.active {
            background: linear-gradient(135deg, #3498db, #2980b9);
            color: white;
        }
        
        /* 新设计开始 - 完全透明背景 */
        .slide {
            width: 100%;
            height: 100%;
            background-color: transparent; /* 完全透明背景 */
            padding: 60px;
            display: flex;
            flex-direction: column;
            overflow: hidden;
            position: relative;
        }
        
        /* 顶部标题区域 - 修改了标题样式，删除背景渐变 */
        .header {
            text-align: center;
            margin-bottom: 30px;
            position: relative;
            z-index: 10;
        }
        
        .header h1 {
            font-size: 54px;
            color: #2c3e50;
            margin-bottom: 10px;
            position: relative;
            display: inline-block;
            font-weight: 700;
            background: none;
        }
        
        .header .subtitle {
            font-size: 24px;
            color: #7f8c8d;
            font-weight: 400;
            max-width: 800px;
            margin: 0 auto;
        }
        
        /* 功能截图网格布局 - 修改为2行4列 */
        .screenshots-grid {
            display: grid;
            grid-template-columns: repeat(4, 1fr);
            grid-template-rows: repeat(2, 1fr);
            gap: 20px;
            padding: 0 30px;
            position: relative;
            z-index: 10;
            height: 780px; /* 控制截图区域高度 */
        }
        
        /* 截图容器样式 */
        .screenshot-container {
            background: white;
            border-radius: 12px;
            padding: 5px;
            display: flex;
            flex-direction: column;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.08);
            overflow: hidden;
            height: 100%;
        }
        
        /* 截图标题栏 */
        .screenshot-title-bar {
            display: flex;
            align-items: center;
            padding: 8px 12px;
            background: linear-gradient(to right, #f8f9fa, #e9ecef);
            border-top-left-radius: 8px;
            border-top-right-radius: 8px;
            border-bottom: 1px solid #dee2e6;
        }
        
        .window-controls {
            display: flex;
            gap: 6px;
            margin-right: 10px;
        }
        
        .window-circle {
            width: 12px;
            height: 12px;
            border-radius: 50%;
        }
        
        .window-close { background-color: #ff5f56; }
        .window-minimize { background-color: #ffbd2e; }
        .window-maximize { background-color: #27c93f; }
        
        .screenshot-title {
            font-size: 14px;
            font-weight: 600;
            color: #495057;
            text-align: center;
            flex-grow: 1;
        }
        
        /* 截图内容区域 */
        .screenshot-content {
            flex-grow: 1;
            overflow: hidden;
            position: relative;
            background: #f8f9fa;
            border-bottom-left-radius: 8px;
            border-bottom-right-radius: 8px;
        }
        
        .screenshot-image {
            width: 100%;
            height: 100%;
            background-size: cover;
            background-position: center;
            background-repeat: no-repeat;
        }
        
        /* 截图下方的功能说明 */
        .screenshot-caption {
            position: absolute;
            bottom: 0;
            left: 0;
            right: 0;
            background: rgba(0, 0, 0, 0.7);
            color: white;
            padding: 8px 12px;
            font-size: 14px;
            font-weight: 500;
        }
        
        /* 各个功能的截图背景颜色和图片设置 */
        #key-lifecycle .screenshot-image {
            background-color: #e3f2fd; /* 浅蓝色背景 */
            background-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" width="100" height="100" fill="%233498db" opacity="0.2"><path d="M7,14A2,2 0 0,1 5,12A2,2 0 0,1 7,10A2,2 0 0,1 9,12A2,2 0 0,1 7,14M12.65,10C11.83,7.67 9.61,6 7,6A6,6 0 0,0 1,12A6,6 0 0,0 7,18C9.61,18 11.83,16.33 12.65,14H17V18H21V14H23V10H12.65Z" /></svg>');
        }
        
        #vehicle-service .screenshot-image {
            background-color: #e8f5e9; /* 浅绿色背景 */
            background-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" width="100" height="100" fill="%232ecc71" opacity="0.2"><path d="M18,18.5A1.5,1.5 0 0,1 16.5,17A1.5,1.5 0 0,1 18,15.5A1.5,1.5 0 0,1 19.5,17A1.5,1.5 0 0,1 18,18.5M19.5,9.5L21.46,12H17V9.5M6,18.5A1.5,1.5 0 0,1 4.5,17A1.5,1.5 0 0,1 6,15.5A1.5,1.5 0 0,1 7.5,17A1.5,1.5 0 0,1 6,18.5M20,8L23,12V17H21A3,3 0 0,1 18,20A3,3 0 0,1 15,17H9A3,3 0 0,1 6,20A3,3 0 0,1 3,17H1V6C1,4.89 1.89,4 3,4H17V8H20Z" /></svg>');
        }
        
        #auth-center .screenshot-image {
            background-color: #f3e5f5; /* 浅紫色背景 */
            background-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" width="100" height="100" fill="%239b59b6" opacity="0.2"><path d="M12,1L3,5V11C3,16.55 6.84,21.74 12,23C17.16,21.74 21,16.55 21,11V5L12,1M12,5A3,3 0 0,1 15,8A3,3 0 0,1 12,11A3,3 0 0,1 9,8A3,3 0 0,1 12,5M17.13,17C15.92,18.85 14.11,20.24 12,20.92C9.89,20.24 8.08,18.85 6.87,17C6.53,16.5 6.24,16 6,15.47C6,13.82 8.71,12.47 12,12.47C15.29,12.47 18,13.79 18,15.47C17.76,16 17.47,16.5 17.13,17Z" /></svg>');
        }
        
        #key-management .screenshot-image {
            background-color: #fff3e0; /* 浅橙色背景 */
            background-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" width="100" height="100" fill="%23e67e22" opacity="0.2"><path d="M22,18V22H18V19H15V16H12L9.74,13.74C9.19,13.91 8.61,14 8,14A6,6 0 0,1 2,8A6,6 0 0,1 8,2A6,6 0 0,1 14,8C14,8.61 13.91,9.19 13.74,9.74L22,18M7,5A2,2 0 0,0 5,7A2,2 0 0,0 7,9A2,2 0 0,0 9,7A2,2 0 0,0 7,5Z" /></svg>');
        }
        
        #api-service .screenshot-image {
            background-color: #eceff1; /* 浅灰色背景 */
            background-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" width="100" height="100" fill="%2334495e" opacity="0.2"><path d="M20,19V7H4V19H20M20,3A2,2 0 0,1 22,5V19A2,2 0 0,1 20,21H4A2,2 0 0,1 2,19V5A2,2 0 0,1 4,3H20M13,17V15H18V17H13M9.58,13L5.57,9H8.4L11.7,12.3C12.09,12.69 12.09,13.33 11.7,13.72L8.42,17H5.59L9.58,13Z" /></svg>');
        }
        
        #secure-channel .screenshot-image {
            background-color: #fffde7; /* 浅黄色背景 */
            background-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" width="100" height="100" fill="%23f1c40f" opacity="0.2"><path d="M12,1L3,5V11C3,16.55 6.84,21.74 12,23C17.16,21.74 21,16.55 21,11V5L12,1M12,5.68C12.5,5.68 12.95,6.11 12.95,6.63V10.11L18,13.26V14.53L12.95,12.95V16.42L14.21,17.37V18.32L12,17.68L9.79,18.32V17.37L11.05,16.42V12.95L6,14.53V13.26L11.05,10.11V6.63C11.05,6.11 11.5,5.68 12,5.68Z" /></svg>');
        }
        
        #monitoring .screenshot-image {
            background-color: #ffebee; /* 浅红色背景 */
            background-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" width="100" height="100" fill="%23e74c3c" opacity="0.2"><path d="M12,9A3,3 0 0,1 15,12A3,3 0 0,1 12,15A3,3 0 0,1 9,12A3,3 0 0,1 12,9M12,4.5C17,4.5 21.27,7.61 23,12C21.27,16.39 17,19.5 12,19.5C7,19.5 2.73,16.39 1,12C2.73,7.61 7,4.5 12,4.5M3.18,12C4.83,15.36 8.24,17.5 12,17.5C15.76,17.5 19.17,15.36 20.82,12C19.17,8.64 15.76,6.5 12,6.5C8.24,6.5 4.83,8.64 3.18,12Z" /></svg>');
        }
        
        #time-server .screenshot-image {
            background-color: #e0f2f1; /* 浅青色背景 */
            background-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" width="100" height="100" fill="%231abc9c" opacity="0.2"><path d="M12,20A8,8 0 0,0 20,12A8,8 0 0,0 12,4A8,8 0 0,0 4,12A8,8 0 0,0 12,20M12,2A10,10 0 0,1 22,12A10,10 0 0,1 12,22C6.47,22 2,17.5 2,12A10,10 0 0,1 12,2M12.5,7V12.25L17,14.92L16.25,16.15L11,13V7H12.5Z" /></svg>');
        }
        
        /* 添加界面元素样式 */
        .ui-table {
            width: 90%;
            margin: 20px auto;
            border-collapse: collapse;
            font-size: 12px;
        }
        
        .ui-table th, .ui-table td {
            border: 1px solid #dee2e6;
            padding: 8px;
            text-align: left;
        }
        
        .ui-table th {
            background-color: #f8f9fa;
        }
        
        .ui-button {
            display: inline-block;
            padding: 6px 12px;
            background: linear-gradient(to bottom, #007bff, #0069d9);
            color: white;
            border-radius: 4px;
            font-size: 12px;
            margin: 5px;
        }
        
        .ui-search {
            display: flex;
            width: 90%;
            margin: 15px auto;
        }
        
        .ui-search input {
            flex-grow: 1;
            padding: 8px;
            border: 1px solid #ced4da;
            border-radius: 4px 0 0 4px;
        }
        
        .ui-search button {
            padding: 8px 15px;
            background: #007bff;
            color: white;
            border: 1px solid #007bff;
            border-radius: 0 4px 4px 0;
        }
        
        .ui-tabs {
            display: flex;
            width: 90%;
            margin: 10px auto;
            border-bottom: 1px solid #dee2e6;
        }
        
        .ui-tab {
            padding: 8px 16px;
            border: 1px solid transparent;
            border-top-left-radius: 4px;
            border-top-right-radius: 4px;
            margin-right: 5px;
            font-size: 14px;
            cursor: pointer;
        }
        
        .ui-tab.active {
            border-color: #dee2e6 #dee2e6 #fff;
            background-color: #fff;
        }
        
        .ui-panel {
            background-color: #fff;
            border-radius: 4px;
            box-shadow: 0 2px 5px rgba(0,0,0,0.05);
            margin: 10px auto;
            width: 90%;
            padding: 10px;
        }
        
        .ui-chart {
            width: 90%;
            height: 150px;
            margin: 10px auto;
            background: linear-gradient(to right, rgba(41,128,185,0.1), rgba(52,152,219,0.1));
            border-radius: 4px;
            position: relative;
            overflow: hidden;
        }
        
        .ui-chart::before {
            content: '';
            position: absolute;
            bottom: 0;
            left: 0;
            right: 0;
            height: 50px;
            background: linear-gradient(to right, rgba(41,128,185,0.3), rgba(52,152,219,0.3));
            clip-path: polygon(0 100%, 10% 70%, 20% 90%, 30% 50%, 40% 60%, 50% 30%, 60% 40%, 70% 20%, 80% 30%, 90% 10%, 100% 40%, 100% 100%);
        }
        
        /* 底部装饰和版权 */
        .footer {
            position: absolute;
            bottom: 20px;
            width: 100%;
            text-align: center;
            font-size: 14px;
            color: #95a5a6;
            left: 0;
            z-index: 10;
        }
        
        /* 侧边装饰 */
        .side-decoration {
            position: absolute;
            bottom: 40px;
            right: 40px;
            display: flex;
            gap: 15px;
            z-index: 10;
        }
        
        .side-icon {
            width: 50px;
            height: 50px;
            opacity: 0.7;
            transition: all 0.3s ease;
        }
        
        .side-icon:hover {
            transform: scale(1.1);
            opacity: 1;
        }
        
        /* 消息提示 */
        .toast {
            position: fixed;
            top: 50px;
            left: 50%;
            transform: translateX(-50%);
            background-color: rgba(0, 0, 0, 0.8);
            color: white;
            padding: 12px 25px;
            border-radius: 30px;
            font-size: 16px;
            z-index: 2000;
            display: none;
            animation: fadeInOut 2s ease;
        }
        
        @keyframes fadeInOut {
            0% { opacity: 0; }
            20% { opacity: 1; }
            80% { opacity: 1; }
            100% { opacity: 0; }
        }
        
        /* 美化表格样式 */
        .ui-table {
            width: 100%;
            border-collapse: collapse;
            font-size: 12px;
            background-color: white;
        }
        
        .ui-table th, .ui-table td {
            border: 1px solid #e9ecef;
            padding: 8px;
            text-align: left;
        }
        
        .ui-table th {
            background-color: #f8f9fa;
            font-weight: 600;
            color: #495057;
        }
        
        .ui-table tr:nth-child(even) {
            background-color: #f9f9f9;
        }
        
        .ui-table tr:hover {
            background-color: #f1f1f1;
        }
        
        /* 美化按钮样式 */
        .ui-button {
            display: inline-block;
            padding: 6px 12px;
            background: linear-gradient(to bottom, #007bff, #0069d9);
            color: white;
            border-radius: 4px;
            font-size: 12px;
            margin: 5px;
            cursor: pointer;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            text-align: center;
        }
        
        .ui-button:hover {
            transform: translateY(-1px);
            box-shadow: 0 4px 6px rgba(0,0,0,0.15);
        }
        
        /* 美化搜索框样式 */
        .ui-search {
            display: flex;
            width: 100%;
            margin: 15px 0;
        }
        
        .ui-search input {
            flex-grow: 1;
            padding: 8px 12px;
            border: 1px solid #ced4da;
            border-radius: 4px 0 0 4px;
            font-size: 12px;
        }
        
        .ui-search button {
            padding: 8px 15px;
            background: linear-gradient(to bottom, #007bff, #0069d9);
            color: white;
            border: none;
            border-radius: 0 4px 4px 0;
            cursor: pointer;
            font-size: 12px;
        }
        
        /* 美化标签页样式 */
        .ui-tabs {
            display: flex;
            width: 100%;
            margin: 10px 0;
            border-bottom: 1px solid #dee2e6;
        }
        
        .ui-tab {
            padding: 8px 16px;
            border: 1px solid transparent;
            border-top-left-radius: 4px;
            border-top-right-radius: 4px;
            margin-right: 5px;
            font-size: 14px;
            cursor: pointer;
            color: #6c757d;
        }
        
        .ui-tab.active {
            border-color: #dee2e6 #dee2e6 #fff;
            background-color: #fff;
            color: #495057;
            font-weight: 600;
        }
        
        .ui-tab:hover:not(.active) {
            background-color: #f8f9fa;
            color: #495057;
        }
        
        /* 修复截图容器内的样式 */
        .screenshot-image {
            padding: 0;
            background-color: white !important;
            background-image: none !important;
        }
        
        #key-lifecycle, #auth-center, #api-service, #monitoring {
            height: 100%;
            width: 100%;
            background-color: white !important;
            background-image: none !important;
        }
        
        /* 美化面板样式 */
        .ui-panel {
            background-color: #fff;
            border-radius: 4px;
            box-shadow: 0 2px 5px rgba(0,0,0,0.05);
            margin: 0;
            width: 100%;
            height: 100%;
            padding: 10px;
            box-sizing: border-box;
            overflow: auto;
        }
    </style>
</head>
<body>
    <div class="ppt-container" id="pptContainer">
        <div class="ppt-label">PPT尺寸(1920×1080) - 透明背景</div>
        <button class="copy-button" id="copyButton">
            <span class="copy-icon"></span>复制为PPT图片
        </button>
        <button class="download-button" id="downloadButton">
            <span class="download-icon"></span>下载透明PNG
        </button>
        
        <!-- 添加清晰度选择按钮组 -->
        <div class="resolution-selector" id="resolutionSelector">
            <span class="resolution-label">清晰度:</span>
            <button class="resolution-btn" data-scale="1">标准</button>
            <button class="resolution-btn active" data-scale="2">高清</button>
            <button class="resolution-btn" data-scale="3">超清</button>
        </div>
        
        <div class="slide" id="slide">
            <!-- 顶部标题 -->
            <div class="header">
                <h1>钥匙云平台</h1>
                <div class="subtitle">安全、高效的智能车辆数字钥匙全生命周期管理系统</div>
            </div>
            
            <!-- 功能截图网格 -->
            <div class="screenshots-grid">
                <!-- 功能1: 钥匙生命周期管理 -->
                <div class="screenshot-container feature-1">
                    <div class="screenshot-title-bar">
                        <div class="window-controls">
                            <div class="window-circle window-close"></div>
                            <div class="window-circle window-minimize"></div>
                            <div class="window-circle window-maximize"></div>
                        </div>
                        <div class="screenshot-title">钥匙生命周期管理</div>
                    </div>
                    <div class="screenshot-content">
                        <div class="screenshot-image" id="key-lifecycle">
                            <!-- 添加实际的界面内容 -->
                            <div class="ui-panel" style="height: 100%; padding: 15px; box-sizing: border-box;">
                                <div class="ui-tabs">
                                    <div class="ui-tab active">全部钥匙</div>
                                    <div class="ui-tab">已授权</div>
                                    <div class="ui-tab">待授权</div>
                                    <div class="ui-tab">已撤销</div>
                                </div>
                                <div class="ui-search">
                                    <input type="text" placeholder="搜索钥匙ID或用户名...">
                                    <button>搜索</button>
                                </div>
                                <table class="ui-table">
                                    <thead>
                                        <tr>
                                            <th>钥匙ID</th>
                                            <th>用户</th>
                                            <th>车辆</th>
                                            <th>授权时间</th>
                                            <th>有效期至</th>
                                            <th>状态</th>
                                            <th>操作</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                            <td>KY-2023001</td>
                                            <td>张三</td>
                                            <td>宝马X5</td>
                                            <td>2023-10-01</td>
                                            <td>2024-10-01</td>
                                            <td><span style="color: green;">有效</span></td>
                                            <td>
                                                <span class="ui-button">管理</span>
                                            </td>
                                        </tr>
                                        <tr>
                                            <td>KY-2023002</td>
                                            <td>李四</td>
                                            <td>奔驰C级</td>
                                            <td>2023-09-15</td>
                                            <td>2023-12-15</td>
                                            <td><span style="color: green;">有效</span></td>
                                            <td>
                                                <span class="ui-button">管理</span>
                                            </td>
                                        </tr>
                                        <tr>
                                            <td>KY-2023003</td>
                                            <td>王五</td>
                                            <td>丰田凯美瑞</td>
                                            <td>2023-08-20</td>
                                            <td>2023-11-20</td>
                                            <td><span style="color: orange;">即将过期</span></td>
                                            <td>
                                                <span class="ui-button">管理</span>
                                            </td>
                                        </tr>
                                    </tbody>
                                </table>
                                <div style="text-align: right; margin-top: 15px;">
                                    <span class="ui-button" style="background: linear-gradient(to bottom, #28a745, #218838);">创建钥匙</span>
                                    <span class="ui-button" style="background: linear-gradient(to bottom, #dc3545, #c82333);">批量撤销</span>
                                </div>
                            </div>
                        </div>
                        <div class="screenshot-caption">创建、授权、撤销数字钥匙，精确控制钥匙权限与有效期，实现全流程智能管理</div>
                    </div>
                </div>
                
                <!-- 功能2: 车辆关联服务 -->
                <div class="screenshot-container feature-2">
                    <div class="screenshot-title-bar">
                        <div class="window-controls">
                            <div class="window-circle window-close"></div>
                            <div class="window-circle window-minimize"></div>
                            <div class="window-circle window-maximize"></div>
                        </div>
                        <div class="screenshot-title">车辆关联服务</div>
                    </div>
                    <div class="screenshot-content">
                        <div class="screenshot-image" id="vehicle-service"></div>
                        <div class="screenshot-caption">验证车辆信息，建立车辆与钥匙的安全绑定关系，确保数字凭证与实体车辆一一对应</div>
                    </div>
                </div>
                
                <!-- 功能3: 安全认证中心 -->
                <div class="screenshot-container feature-3">
                    <div class="screenshot-title-bar">
                        <div class="window-controls">
                            <div class="window-circle window-close"></div>
                            <div class="window-circle window-minimize"></div>
                            <div class="window-circle window-maximize"></div>
                        </div>
                        <div class="screenshot-title">安全认证中心</div>
                    </div>
                    <div class="screenshot-content">
                        <div class="screenshot-image" id="auth-center">
                            <!-- 添加实际的界面内容 -->
                            <div class="ui-panel" style="height: 100%; display: flex; flex-direction: column; justify-content: center; align-items: center; padding: 20px;">
                                <div style="text-align: center; margin-bottom: 20px;">
                                    <svg viewBox="0 0 24 24" width="80" height="80" style="fill: #9b59b6;">
                                        <path d="M12,1L3,5V11C3,16.55 6.84,21.74 12,23C17.16,21.74 21,16.55 21,11V5L12,1M12,5A3,3 0 0,1 15,8A3,3 0 0,1 12,11A3,3 0 0,1 9,8A3,3 0 0,1 12,5M17.13,17C15.92,18.85 14.11,20.24 12,20.92C9.89,20.24 8.08,18.85 6.87,17C6.53,16.5 6.24,16 6,15.47C6,13.82 8.71,12.47 12,12.47C15.29,12.47 18,13.79 18,15.47C17.76,16 17.47,16.5 17.13,17Z" />
                                    </svg>
                                </div>
                                <h2 style="margin-bottom: 20px; color: #333; font-size: 24px;">身份验证</h2>
                                <p style="margin-bottom: 20px; color: #666; font-size: 16px;">请选择一种身份验证方式继续操作</p>
                                <div style="display: flex; gap: 15px; margin-bottom: 30px;">
                                    <div class="ui-button" style="display: flex; align-items: center; gap: 8px; padding: 10px 20px;">
                                        <svg viewBox="0 0 24 24" width="20" height="20" style="fill: white;">
                                            <path d="M9,11.75A1.25,1.25 0 0,0 7.75,13A1.25,1.25 0 0,0 9,14.25A1.25,1.25 0 0,0 10.25,13A1.25,1.25 0 0,0 9,11.75M15,11.75A1.25,1.25 0 0,0 13.75,13A1.25,1.25 0 0,0 15,14.25A1.25,1.25 0 0,0 16.25,13A1.25,1.25 0 0,0 15,11.75M12,2A10,10 0 0,0 2,12A10,10 0 0,0 12,22A10,10 0 0,0 22,12A10,10 0 0,0 12,2M12,20C7.59,20 4,16.41 4,12C4,11.71 4.03,11.43 4.07,11.15C5.02,10.45 6.23,10 7.5,10C9.09,10 10.5,10.63 11.54,11.61C12.63,10.63 14.09,10 15.75,10C17.03,10 18.26,10.44 19.22,11.15C19.26,11.43 19.29,11.71 19.29,12C19.29,16.41 15.7,20 12,20Z" />
                                        </svg>
                                        人脸识别
                                    </div>
                                    <div class="ui-button" style="display: flex; align-items: center; gap: 8px; padding: 10px 20px;">
                                        <svg viewBox="0 0 24 24" width="20" height="20" style="fill: white;">
                                            <path d="M11.83,1.73C8.43,1.79 6.23,3.32 6.23,3.32C5.95,3.5 5.88,3.91 6.07,4.19C6.27,4.5 6.66,4.55 6.96,4.34C6.96,4.34 11.27,1.15 17.46,4.38C17.75,4.55 18.14,4.45 18.31,4.15C18.5,3.85 18.37,3.47 18.03,3.28C16.36,2.4 14.78,1.96 13.36,1.8C12.83,1.74 12.32,1.72 11.83,1.73M12.22,4.34C6.26,4.26 3.41,9.05 3.41,9.05C3.22,9.34 3.3,9.72 3.58,9.91C3.87,10.1 4.26,10 4.5,9.68C4.5,9.68 6.92,5.5 12.2,5.59C17.5,5.66 19.82,9.65 19.82,9.65C20,9.94 20.38,10.04 20.68,9.87C21,9.69 21.07,9.31 20.9,9C20.9,9 18.15,4.42 12.22,4.34M11.5,6.82C9.82,6.94 8.21,7.55 7,8.56C4.62,10.53 3.1,14.14 4.77,19C4.88,19.33 5.24,19.5 5.57,19.39C5.89,19.28 6.07,18.92 5.95,18.6V18.6C4.41,14.13 5.78,11.2 7.8,9.5C9.77,7.89 13.25,7.5 15.84,9.1C17.11,9.9 18.1,11.28 18.6,12.64C19.11,14 19.08,15.32 18.67,15.94C18.25,16.59 17.4,16.83 16.65,16.64C15.9,16.45 15.29,15.91 15.26,14.77C15.23,13.06 13.89,12 12.5,11.84C11.16,11.68 9.61,12.4 9.21,14C8.45,16.92 10.36,21.07 14.78,22.45C15.11,22.55 15.46,22.37 15.57,22.04C15.67,21.71 15.5,21.35 15.15,21.25C11.32,20.06 9.87,16.43 10.42,14.29C10.66,13.33 11.5,13 12.38,13.08C13.25,13.18 14,13.7 14,14.79C14.05,16.43 15.12,17.54 16.34,17.85C17.56,18.16 18.97,17.77 19.72,16.62C20.5,15.45 20.37,13.8 19.78,12.21C19.18,10.61 18.07,9.03 16.5,8.04C14.96,7.08 13.19,6.7 11.5,6.82M11.86,9.25V9.26C10.08,9.32 8.3,10.24 7.28,12.18C5.96,14.67 6.56,17.21 7.44,19.04C8.33,20.88 9.54,22.1 9.54,22.1C9.78,22.35 10.17,22.35 10.42,22.11C10.67,21.87 10.67,21.5 10.43,21.23C10.43,21.23 9.36,20.13 8.57,18.5C7.78,16.87 7.3,14.81 8.38,12.77C9.5,10.67 11.5,10.16 13.26,10.67C15.04,11.19 16.53,12.74 16.5,15.03C16.46,15.38 16.71,15.68 17.06,15.7C17.4,15.73 17.7,15.47 17.73,15.06C17.79,12.2 15.87,10.13 13.61,9.47C13.04,9.31 12.45,9.23 11.86,9.25M12.08,14.25C11.73,14.26 11.46,14.55 11.47,14.89C11.47,14.89 11.5,16.37 12.31,17.8C13.15,19.23 14.93,20.59 18.03,20.3C18.37,20.28 18.64,20 18.62,19.64C18.6,19.29 18.3,19.03 17.91,19.06C15.19,19.31 14.04,18.28 13.39,17.17C12.74,16.07 12.72,14.88 12.72,14.88C12.72,14.53 12.44,14.25 12.08,14.25Z" />
                                        </svg>
                                        指纹识别
                                    </div>
                                    <div class="ui-button" style="display: flex; align-items: center; gap: 8px; padding: 10px 20px;">
                                        <svg viewBox="0 0 24 24" width="20" height="20" style="fill: white;">
                                            <path d="M20,4H4A2,2 0 0,0 2,6V18A2,2 0 0,0 4,20H20A2,2 0 0,0 22,18V6A2,2 0 0,0 20,4M20,18H4V8L12,13L20,8V18M20,6L12,11L4,6V6H20V6Z" />
                                        </svg>
                                        验证码登录
                                    </div>
                                </div>
                                <div style="width: 200px; height: 200px; border: 2px dashed #9b59b6; border-radius: 10px; display: flex; align-items: center; justify-content: center; margin-top: 10px;">
                                    <span style="color: #9b59b6; font-size: 14px;">请对准摄像头</span>
                                </div>
                            </div>
                        </div>
                        <div class="screenshot-caption">确保用户身份可信，执行多层次权限验证，有效防止未授权访问和身份冒用行为</div>
                    </div>
                </div>
                
                <!-- 功能4: 密钥管理系统 -->
                <div class="screenshot-container feature-4">
                    <div class="screenshot-title-bar">
                        <div class="window-controls">
                            <div class="window-circle window-close"></div>
                            <div class="window-circle window-minimize"></div>
                            <div class="window-circle window-maximize"></div>
                        </div>
                        <div class="screenshot-title">密钥管理系统</div>
                    </div>
                    <div class="screenshot-content">
                        <div class="screenshot-image" id="key-management"></div>
                        <div class="screenshot-caption">生成加密密钥，确保密钥安全存储与分发，支持密钥更新与版本管理，构建立体防护</div>
                    </div>
                </div>
                
                <!-- 功能5: 统一接口服务 -->
                <div class="screenshot-container feature-5">
                    <div class="screenshot-title-bar">
                        <div class="window-controls">
                            <div class="window-circle window-close"></div>
                            <div class="window-circle window-minimize"></div>
                            <div class="window-circle window-maximize"></div>
                        </div>
                        <div class="screenshot-title">统一接口服务</div>
                    </div>
                    <div class="screenshot-content">
                        <div class="screenshot-image" id="api-service">
                            <!-- 添加实际的界面内容 -->
                            <div class="ui-panel" style="height: 100%; padding: 15px; box-sizing: border-box;">
                                <div class="ui-tabs">
                                    <div class="ui-tab active">接口列表</div>
                                    <div class="ui-tab">授权管理</div>
                                    <div class="ui-tab">接口文档</div>
                                    <div class="ui-tab">接口监控</div>
                                </div>
                                <div class="ui-search">
                                    <input type="text" placeholder="搜索API接口...">
                                    <button>搜索</button>
                                </div>
                                <table class="ui-table">
                                    <thead>
                                        <tr>
                                            <th>接口名称</th>
                                            <th>接口路径</th>
                                            <th>版本</th>
                                            <th>状态</th>
                                            <th>调用次数</th>
                                            <th>操作</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                            <td>创建数字钥匙</td>
                                            <td>/api/v1/keys/create</td>
                                            <td>v1.0</td>
                                            <td><span style="color: green;">正常</span></td>
                                            <td>1,243</td>
                                            <td>
                                                <span class="ui-button">测试</span>
                                                <span class="ui-button">文档</span>
                                            </td>
                                        </tr>
                                        <tr>
                                            <td>验证钥匙权限</td>
                                            <td>/api/v1/keys/verify</td>
                                            <td>v1.0</td>
                                            <td><span style="color: green;">正常</span></td>
                                            <td>3,578</td>
                                            <td>
                                                <span class="ui-button">测试</span>
                                                <span class="ui-button">文档</span>
                                            </td>
                                        </tr>
                                        <tr>
                                            <td>撤销钥匙访问</td>
                                            <td>/api/v1/keys/revoke</td>
                                            <td>v1.1</td>
                                            <td><span style="color: orange;">维护中</span></td>
                                            <td>856</td>
                                            <td>
                                                <span class="ui-button">测试</span>
                                                <span class="ui-button">文档</span>
                                            </td>
                                        </tr>
                                    </tbody>
                                </table>
                                <div style="margin-top: 15px; display: flex; justify-content: space-between;">
                                    <div>
                                        <span class="ui-button" style="background: linear-gradient(to bottom, #28a745, #218838);">创建接口</span>
                                        <span class="ui-button">刷新状态</span>
                                    </div>
                                    <div>
                                        共 <b>18</b> 个接口，<span style="color: green;">16</span> 个正常，<span style="color: orange;">2</span> 个维护中
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="screenshot-caption">提供标准化API，连接外部系统，确保接口安全，支持跨平台无缝对接与扩展</div>
                    </div>
                </div>
                
                <!-- 功能6: 安全通信通道 -->
                <div class="screenshot-container feature-6">
                    <div class="screenshot-title-bar">
                        <div class="window-controls">
                            <div class="window-circle window-close"></div>
                            <div class="window-circle window-minimize"></div>
                            <div class="window-circle window-maximize"></div>
                        </div>
                        <div class="screenshot-title">安全通信通道</div>
                    </div>
                    <div class="screenshot-content">
                        <div class="screenshot-image" id="secure-channel"></div>
                        <div class="screenshot-caption">实现端到端加密通信，防止数据被窃听或篡改，保障传输过程中的数据安全</div>
                    </div>
                </div>
                
                <!-- 功能7: 异常监控与处理 -->
                <div class="screenshot-container feature-7">
                    <div class="screenshot-title-bar">
                        <div class="window-controls">
                            <div class="window-circle window-close"></div>
                            <div class="window-circle window-minimize"></div>
                            <div class="window-circle window-maximize"></div>
                        </div>
                        <div class="screenshot-title">异常监控与处理</div>
                    </div>
                    <div class="screenshot-content">
                        <div class="screenshot-image" id="monitoring">
                            <!-- 添加实际的界面内容 -->
                            <div class="ui-panel" style="height: 100%; padding: 15px; box-sizing: border-box;">
                                <div style="display: flex; justify-content: space-between; margin-bottom: 15px;">
                                    <div style="font-weight: bold; font-size: 16px;">系统安全状态</div>
                                    <div>
                                        <span style="padding: 4px 8px; background-color: #4CAF50; color: white; border-radius: 4px; font-size: 12px; margin-right: 10px;">正常</span>
                                        <span style="color: #666; font-size: 12px;">最后更新: 2023-11-15 14:30:22</span>
                                    </div>
                                </div>
                                
                                <!-- 图表区域 -->
                                <div style="display: flex; gap: 15px; margin-bottom: 15px;">
                                    <div style="flex: 1; background-color: #f8f9fa; border-radius: 4px; padding: 10px; height: 120px; position: relative;">
                                        <div style="font-size: 14px; font-weight: 500; margin-bottom: 5px;">访问请求监控</div>
                                        <div style="position: absolute; bottom: 0; left: 0; right: 0; height: 70px;">
                                            <!-- 简单的柱状图 -->
                                            <div style="display: flex; align-items: flex-end; height: 100%; padding: 0 10px; justify-content: space-between;">
                                                <div style="width: 8px; height: 30%; background-color: #007bff;"></div>
                                                <div style="width: 8px; height: 50%; background-color: #007bff;"></div>
                                                <div style="width: 8px; height: 40%; background-color: #007bff;"></div>
                                                <div style="width: 8px; height: 70%; background-color: #007bff;"></div>
                                                <div style="width: 8px; height: 45%; background-color: #007bff;"></div>
                                                <div style="width: 8px; height: 60%; background-color: #007bff;"></div>
                                                <div style="width: 8px; height: 35%; background-color: #007bff;"></div>
                                                <div style="width: 8px; height: 55%; background-color: #007bff;"></div>
                                                <div style="width: 8px; height: 65%; background-color: #007bff;"></div>
                                                <div style="width: 8px; height: 40%; background-color: #007bff;"></div>
                                                <div style="width: 8px; height: 80%; background-color: #007bff;"></div>
                                                <div style="width: 8px; height: 60%; background-color: #007bff;"></div>
                                            </div>
                                        </div>
                                    </div>
                                    <div style="flex: 1; background-color: #f8f9fa; border-radius: 4px; padding: 10px; height: 120px;">
                                        <div style="font-size: 14px; font-weight: 500; margin-bottom: 15px;">安全事件统计</div>
                                        <div style="display: flex; justify-content: space-around; text-align: center;">
                                            <div>
                                                <div style="font-size: 24px; font-weight: bold; color: #28a745;">0</div>
                                                <div style="font-size: 12px; color: #666;">高危事件</div>
                                            </div>
                                            <div>
                                                <div style="font-size: 24px; font-weight: bold; color: #ffc107;">2</div>
                                                <div style="font-size: 12px; color: #666;">中危事件</div>
                                            </div>
                                            <div>
                                                <div style="font-size: 24px; font-weight: bold; color: #17a2b8;">14</div>
                                                <div style="font-size: 12px; color: #666;">低危事件</div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                
                                <table class="ui-table">
                                    <thead>
                                        <tr>
                                            <th>事件ID</th>
                                            <th>事件类型</th>
                                            <th>触发时间</th>
                                            <th>源IP</th>
                                            <th>风险等级</th>
                                            <th>处理状态</th>
                                            <th>操作</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                            <td>SEC-2023111501</td>
                                            <td>未授权访问尝试</td>
                                            <td>2023-11-15 10:23:45</td>
                                            <td>*************</td>
                                            <td><span style="color: orange;">中危</span></td>
                                            <td>已自动阻断</td>
                                            <td>
                                                <span class="ui-button">详情</span>
                                            </td>
                                        </tr>
                                        <tr>
                                            <td>SEC-2023111502</td>
                                            <td>暴力破解尝试</td>
                                            <td>2023-11-15 11:45:12</td>
                                            <td>192.168.1.106</td>
                                            <td><span style="color: orange;">中危</span></td>
                                            <td>已自动阻断</td>
                                            <td>
                                                <span class="ui-button">详情</span>
                                            </td>
                                        </tr>
                                        <tr>
                                            <td>SEC-2023111503</td>
                                            <td>权限提升尝试</td>
                                            <td>2023-11-15 13:17:32</td>
                                            <td>192.168.1.107</td>
                                            <td><span style="color: #17a2b8;">低危</span></td>
                                            <td>已记录</td>
                                            <td>
                                                <span class="ui-button">详情</span>
                                            </td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                        <div class="screenshot-caption">智能检测异常行为，执行安全响应，记录安全事件，建立完整事件追溯机制</div>
                    </div>
                </div>
                
                <!-- 功能8: 时间服务器 -->
                <div class="screenshot-container feature-8">
                    <div class="screenshot-title-bar">
                        <div class="window-controls">
                            <div class="window-circle window-close"></div>
                            <div class="window-circle window-minimize"></div>
                            <div class="window-circle window-maximize"></div>
                        </div>
                        <div class="screenshot-title">时间服务器</div>
                    </div>
                    <div class="screenshot-content">
                        <div class="screenshot-image" id="time-server"></div>
                        <div class="screenshot-caption">提供精确时间基准，支持NTP/PTP时间同步，有效防止重放攻击，确保系统操作的时序准确性</div>
                    </div>
                </div>
            </div>
            
            <!-- 侧边装饰图标 -->
            <div class="side-decoration">
                <svg class="side-icon" viewBox="0 0 24 24" fill="#3498db">
                    <path d="M12,17A2,2 0 0,0 14,15C14,13.89 13.1,13 12,13A2,2 0 0,0 10,15A2,2 0 0,0 12,17M18,8A2,2 0 0,1 20,10V20A2,2 0 0,1 18,22H6A2,2 0 0,1 4,20V10C4,8.89 4.9,8 6,8H7V6A5,5 0 0,1 12,1A5,5 0 0,1 17,6V8H18M12,3A3,3 0 0,0 9,6V8H15V6A3,3 0 0,0 12,3Z" />
                </svg>
                <svg class="side-icon" viewBox="0 0 24 24" fill="#2ecc71">
                    <path d="M21,11C21,16.55 17.16,21.74 12,23C6.84,21.74 3,16.55 3,11V5L12,1L21,5V11M12,21C15.75,20 19,15.54 19,11.22V6.3L12,3.18L5,6.3V11.22C5,15.54 8.25,20 12,21Z" />
                </svg>
                <svg class="side-icon" viewBox="0 0 24 24" fill="#e67e22">
                    <path d="M12,12H19C18.47,16.11 15.72,19.78 12,20.92V12H5V6.3L12,3.19M12,1L3,5V11C3,16.55 6.84,21.73 12,23C17.16,21.73 21,16.55 21,11V5L12,1Z" />
                </svg>
            </div>
        </div>
    </div>
    
    <div class="toast" id="toast">操作成功！</div>
    
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const copyButton = document.getElementById('copyButton');
            const downloadButton = document.getElementById('downloadButton');
            const toast = document.getElementById('toast');
            const slide = document.getElementById('slide');
            const resolutionBtns = document.querySelectorAll('.resolution-btn');
            
            // 默认清晰度为2x (高清)
            let currentScale = 2;
            
            // 清晰度按钮点击事件
            resolutionBtns.forEach(btn => {
                btn.addEventListener('click', function() {
                    // 移除所有按钮的active类
                    resolutionBtns.forEach(b => b.classList.remove('active'));
                    // 给当前按钮添加active类
                    this.classList.add('active');
                    // 更新当前清晰度
                    currentScale = parseFloat(this.getAttribute('data-scale'));
                    
                    // 显示提示
                    showToast(`清晰度已设置为${this.textContent}模式`);
                });
            });
            
            // 显示提示消息
            function showToast(message) {
                toast.textContent = message;
                toast.style.display = 'block';
                setTimeout(function() {
                    toast.style.display = 'none';
                }, 2000);
            }
            
            // 生成透明背景图片
            function generateImage() {
                return html2canvas(slide, {
                    scale: currentScale, // 使用用户选择的清晰度
                    useCORS: true,
                    allowTaint: true,
                    backgroundColor: null // 关键：设置为null以支持透明背景
                });
            }
            
            // 复制按钮功能
            copyButton.addEventListener('click', function() {
                showToast("正在处理图片，请稍候...");
                
                generateImage().then(function(canvas) {
                    try {
                        // 使用现代剪贴板API
                        canvas.toBlob(function(blob) {
                            try {
                                const item = new ClipboardItem({ 'image/png': blob });
                                navigator.clipboard.write([item]).then(function() {
                                    showToast("已复制到剪贴板！现在可以粘贴到PPT中");
                                }).catch(function(err) {
                                    showToast("自动复制失败，请使用下载按钮获取图片");
                                    console.error("剪贴板API错误:", err);
                                });
                            } catch (e) {
                                showToast("您的浏览器不支持复制图片，请使用下载按钮");
                                console.error("ClipboardItem错误:", e);
                            }
                        });
                    } catch (e) {
                        showToast("复制功能不可用，请使用下载图片功能");
                        console.error("toBlob错误:", e);
                    }
                }).catch(function(err) {
                    showToast("生成图片失败，请尝试下载或截图");
                    console.error("html2canvas错误:", err);
                });
            });
            
            // 下载按钮功能
           downloadButton.addEventListener('click', function () {
                showToast(`正在生成${currentScale}x清晰度透明PNG图片，请稍候...`);

                generateImage().then(function (canvas) {
                    // 将Canvas转换为PNG并下载
                    const imageUrl = canvas.toDataURL('image/png');
                    const link = document.createElement('a');

                    // 首先从URL路径获取文件名
                    let fileName = "";
                    const pathParts = window.location.pathname.split('/');
                    const htmlFileName = pathParts[pathParts.length - 1];
                    // 移除.html后缀并进行URL解码以支持中文文件名
                    fileName = decodeURIComponent(htmlFileName.replace('.html', ''));

                    // 如果从URL获取的文件名为空，尝试从标题获取
                    if (!fileName) {
                        const titleElement = document.querySelector('.header h1');
                        if (titleElement && titleElement.textContent) {
                            fileName = titleElement.textContent.trim();
                        }
                    }
                    }

                    // 确保文件名不为空
                    if (!fileName) {
                        fileName = "测试";
                    }

                    link.download = `${fileName}-透明-${currentScale}x清晰度.png`;
                    link.href = imageUrl;
                    link.click();

                    showToast(`${currentScale}x清晰度透明背景PNG已下载`);
                }).catch(function (err) {
                    showToast("生成图片失败，请尝试截图");
                    console.error("Error:", err);
                });
            });
        });
    </script>
</body>
</html> 
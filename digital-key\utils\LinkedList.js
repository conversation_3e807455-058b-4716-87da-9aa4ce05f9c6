/**
 * LinkedList 1.0
 * @Class LinkedList
 * @description 链表
 */
// 链表节点类
class ListNode {
  constructor(data) {
    this.data = data;
    this.next = null;
  }
}

// 链表类
export class LinkedList {
  constructor() {
    this.head = null;
  }

  // 在链表尾部批量添加节点
  batchAppend(dataList) {
    if (!dataList?.length) {
      return;
    }
    dataList.forEach((data) => {
      this.append(data);
    });
  }

  // 在链表尾部添加节点
  append(data) {
    const newNode = new ListNode(data);
    if (!this.head) {
      this.head = newNode;
    } else {
      let current = this.head;
      while (current.next) {
        current = current.next;
      }
      current.next = newNode;
    }
  }

  // 返回链表中的第一个值，并移除这个节点
  getFirstValue() {
    // 检查链表是否为空
    if (!this.head) {
      return null; // 链表为空，无法移除值
    }

    // 获取并移除头节点的数据值
    const firstValue = this.head.data;
    this.head = this.head.next;

    // 返回被移除的节点的数据值
    return firstValue;
  }

  // 删除指定数据的节点
  delete(data) {
    if (!this.head) return;

    if (this.head.data === data) {
      this.head = this.head.next;
      return;
    }

    let current = this.head;
    while (current.next && current.next.data !== data) {
      current = current.next;
    }

    if (current.next) {
      current.next = current.next.next;
    }
  }

  // 清空链表
  clear() {
    this.head = null;
  }

  // 获取链表的长度
  getLength() {
    let length = 0;
    let current = this.head;

    // 遍历链表直到达到尾节点
    while (current) {
      length++;
      current = current.next;
    }

    return length;
  }
}

page {
	/* 设置背景颜色为深蓝色 */
	// background-color: #004080;
	/* 或者使用 rgb(0, 64, 128) */

	/* 创建线性渐变，从上到下渐变，从深蓝色到浅蓝色 */
	// background-image: linear-gradient(to bottom, #004080, #66b3ff);
	/* 或者使用 rgb(0, 64, 128) 到 rgb(102, 179, 255) */

	/* 设置背景固定，不随页面滚动 */
	background-attachment: fixed;

	/* 其他样式，比如设置高度 */
	height: 100vh;
	/* 设置高度为视口高度 */
}
.bg_img {
	width: 100%;
	height: 100%;
	top: 0;
	position: fixed;
}
.page-content::before {
	content: '';
	position: absolute;
	top: 0;
	left: 50%;
	width: 100%;
	height: 1px;
	background: rgba(255, 255, 255, 0.3); /* 白色系 */
	transform: translateX(-50%);
	z-index: -1;
	filter: blur(2px);
  }
.page-content {
	position: relative;
	touch-action: none;
	padding-top: 8upx;
	height: 100%;
	.imgBox {
		height: 278upx;
		width: 278upx;
		margin: auto;
		margin-bottom: 100upx;
		image {
			width: 100%;
			height: 100%;
		}
	}
	.input {
		margin: 0 58upx;
		input {
			padding: 29upx 0;
			padding-left: 12upx;
			border-bottom: 1upx solid #fff;
			font-size: 30upx;
			color: #fff;
		}
		.changePwd {
			width: 100%;
			text-align: right;
			margin-top: 35upx;
			color: #fff;
			font-size:30upx;
			margin-bottom: 61upx;
		}
	}
}

.login-but {
	font-size: 30rpx;
	width:200rpx;
	height:65rpx;
	margin: 0 auto;
	border: 1px solid #ffffff;
	border-radius:45upx;
	line-height: 65rpx;
	color: #fff;
	& + .login-but {
		// margin-top: 40rpx;
	}
}

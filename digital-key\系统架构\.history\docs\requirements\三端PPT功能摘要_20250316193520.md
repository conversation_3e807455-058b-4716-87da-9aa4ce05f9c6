## 钥匙云平台核心架构

- **钥匙生命周期管理**：创建、授权、撤销数字钥匙，管理钥匙权限与有效期
  
- **车辆关联服务**：验证车辆信息，建立车辆与钥匙的安全绑定关系
  
- **安全认证中心**：确保用户身份可信，执行权限验证，防止未授权访问
  
- **密钥管理系统**：生成加密密钥，确保密钥安全存储与分发，支持密钥更新
  
- **统一接口服务**：提供标准化API，连接外部系统，确保接口安全
  
- **安全通信通道**：实现端到端加密通信，防止数据被窃听或篡改
  
- **异常监控与处理**：检测异常行为，执行安全响应，记录安全事件
  
- **时间服务器**：提供精确时间基准，支持NTP/PTP时间同步，防止重放攻击

## 手机端核心功能

- **钥匙管理模块**：查看、使用和管理个人数字钥匙，包括钥匙状态监控和权限查看
  
- **车辆控制模块**：发送控制指令，接收车辆状态反馈
  
- **安全存储模块**：在手机安全区域存储密钥、配对令牌和车辆连接信息
  
- **蓝牙通信模块**：扫描识别车辆广播，建立和维护与车辆的蓝牙连接
  
- **时间同步模块**：与云端时间服务器同步，提供可信时间戳，防止重放攻击
  
- **暗号交换模块**：与车端协商生成临时暗号，确保通信安全
  
- **智能场景管理模块**：根据位置、时间和活动状态调整扫描频率，优化电量使用
  
- **异常处理模块**：检测并处理连接异常、认证失败等问题，确保使用安全
  
- **标定模块**：用于校准RSSI信号与实际距离的对应关系，提高无感控车精度

## 车端核心功能

- **蓝牙通信模块**：发送加密广播信号，接收并处理连接请求
  
- **钥匙验证模块**：验证手机身份，确保只有授权设备能够连接
  
- **指令执行模块**：接收并执行来自手机的控制指令，反馈执行结果
  
- **安全存储模块**：在安全区域存储根密钥、会话密钥和授权设备信息
  
- **远程通信模块**：与云平台建立安全连接，同步状态和配置
  
- **时间同步模块**：与云端时间服务器同步，提供精确时间基准
  
- **暗号交换模块**：与手机端协商生成临时暗号，确保只有配对的双方能通信
  
- **用户行为分析模块**：基于RSSI信号强度计算与手机的距离，支持距离感知操作
  
- **异常处理模块**：处理异常连接请求，监控异常操作，执行安全措施
  
- **标定配置模块**：存储和管理RSSI信号标定数据，支持不同环境下的精确距离计算

## 钥匙分享功能

- **步骤一：发起分享**：车主在APP中选择"钥匙分享"功能，选择分享钥匙给他人
  
- **步骤二：设置权限**：设定接收方可使用的功能（如仅解锁/全部功能），使用时段和有效期限
  
- **步骤三：生成二维码**：系统根据设置的权限信息生成专属二维码，显示在车主手机屏幕上
  
- **步骤四：接收方扫码**：接收方在APP中选择"接受他人分享"功能，使用手机扫描车主展示的二维码，系统自动处理分享请求
  
- **步骤五：分享确认**：车主收到分享确认请求，确认后系统完成钥匙分享流程
  
- **步骤六：钥匙激活**：确认完成后，接收方的数字钥匙自动激活，可在授权范围内使用
  
- **步骤七：使用钥匙**：接收方在授权时段内使用APP控制车辆，权限范围内操作
  
- **步骤八：管理与撤销**：车主可随时在APP中查看分享状态，必要时调整权限或撤销钥匙

## 钥匙配对功能

- **步骤一：初始化请求**：用户在APP中选择添加数字钥匙，通过扫描车辆二维码或输入VIN码识别车辆

- **步骤二：云端授权**：APP向云平台发送车辆VIN码和手机设备唯一标识，云平台生成虚拟密钥和配对令牌并下发到手机

- **步骤三：车辆准备**：云平台通过4G连接将根密钥和授权手机标识码下发到车辆，车端存储这些信息

- **步骤四：蓝牙发现**：车辆发送加密的蓝牙广播信号，只有持有正确虚拟密钥的手机才能识别并连接

- **步骤五：身份验证**：手机连接车辆蓝牙并发送配对请求，车端验证手机身份是否匹配授权标识码

- **步骤六：双向验证**：车端和手机基于相同的参数（VIN码、标识码和时间戳）各自生成一次性配对码进行验证

- **步骤七：暗号交换**：双方交换部分暗号材料，各自计算出相同的会话密钥（临时暗号）

- **步骤八：安全存储**：车端和手机分别将会话密钥、绑定信息和权限等级存储到各自的安全区域

- **步骤九：建立通道**：基于会话密钥建立加密安全通道，所有通信内容均经过加密保护

- **步骤十：配对确认**：手机和车端分别向云平台上报配对成功，云平台记录钥匙绑定关系

- **步骤十一：完成配对**：云平台返回数字钥匙信息和权限配置，APP显示钥匙添加成功

- **异常处理机制**：系统能够检测并处理配对超时、验证失败和暗号生成失败等异常情况

## 无感控车功能

- **功能概述**：无感控车是指手机在不需要用户主动操作的情况下，自动与车辆建立蓝牙连接并执行预设操作的功能。前提是APP已在手机后台运行，且已获得必要权限。

- **步骤一：自动唤醒**：系统唤醒后台APP，检查是否满足连接条件（用户已启用无感连接、手机电量充足、检测到用户走路状态）

- **步骤二：连接建立**：手机从安全区域读取已存储的车辆连接信息，直接发起蓝牙连接请求，车端验证后接受连接

- **步骤三：安全认证**：手机与车端进行双向认证，确保手机是已配对的授权设备，车辆是用户的车辆，并防止重放攻击

- **步骤四：安全通道**：基于会话密钥建立安全通道，若密钥过期则进行完整密钥协商，生成新的会话密钥

- **步骤五：距离计算**：车端从连接中获取RSSI值，计算与手机的距离，考虑信号强度、环境干扰等因素

- **步骤六：状态同步**：车端向手机发送当前状态信息（车门锁状态、车窗状态、发动机状态等），手机后台更新缓存

- **步骤七：自动控车**：当距离在安全范围内，手机根据用户预设策略准备自动控车指令，经过安全验证后发送至车端执行

- **步骤八：断开处理**：
  - 正常离开：距离逐渐增大，执行断开前操作（如车门上锁），清理会话资源
  - 快速离开：距离快速增大，触发紧急安全措施，自动执行车辆上锁等操作
  - 信号丢失：检测到信号丢失，执行安全措施并清理会话资源

- **步骤九：持续监控**：车端持续监控车辆状态变化，更新距离信息，状态变化时推送通知

- **步骤十：心跳机制**：手机定期发送带有递增序列号的加密心跳包，车端验证后返回响应，确保连接安全

- **优化策略**：
  - 手机端：系统级唤醒、上下文感知扫描、电量自适应、学习优化、多因素触发、防误触机制
  - 车端：智能广播、安全广播、安全优先、状态感知、异常防护、权限分级、距离计算优化

- **安全保障**：所有通信均经过加密，指令包含时间戳和随机数防重放，关键操作需额外验证，定期更新会话密钥，距离阈值严格控制，多因素验证机制





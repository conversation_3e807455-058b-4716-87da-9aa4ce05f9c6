# 数字钥匙系统架构设计

## 项目概述

数字钥匙系统是一套完整的汽车数字钥匙解决方案，包含手机APP对应的SDK、云平台SDK以及完整的车端软硬件开发的集成产品。该系统允许用户通过手机APP远程或近场控制车辆，实现传统物理钥匙的数字化替代。

## 系统架构

数字钥匙系统由以下几个主要部分组成：

1. **手机端**：用户通过手机APP管理数字钥匙、控制车辆
2. **车端**：车辆上的硬件和软件系统，接收并执行控制指令
3. **钥匙云平台**：管理数字钥匙的生命周期、提供安全认证等服务
4. **外部平台**：包括TSP平台、OEM平台等，提供车辆信息、远程控制等服务

### 架构图

系统整体架构如下图所示：

![整体架构图](整体架构图.png)

## 主要功能模块

### 手机端

- **钥匙管理模块**：管理用户的数字钥匙，包括添加、删除、共享等功能
- **车辆控制模块**：提供车辆控制功能，如开锁、关锁、启动等
- **蓝牙通信模块**：负责与车端进行蓝牙通信
- **安全存储模块**：安全存储数字钥匙和相关密钥材料
- **时间同步模块**：与服务器进行时间同步，确保安全认证的时间准确性
- **暗号交换模块**：负责与车端协商生成临时暗号，确保只有配对的双方能通信

### 车端

- **BLE通信模块**：负责与手机端进行蓝牙通信
- **钥匙验证模块**：验证数字钥匙的有效性和权限
- **指令执行模块**：执行控制指令，如开锁、关锁、启动等
- **安全存储模块**：安全存储密钥材料和相关配置
- **T-Box通信模块**：与TSP平台进行通信，接收远程控制指令
- **时间同步模块**：与服务器进行时间同步，确保安全认证的时间准确性
- **暗号交换模块**：负责与手机端协商生成临时暗号，确保只有配对的双方能通信

### 钥匙云平台

- **钥匙生命周期管理**：管理数字钥匙的创建、更新、撤销等
- **VIN码关联服务**：将VIN码与车辆信息关联
- **安全认证中心**：提供安全认证服务
- **密钥管理系统**：管理系统中的各类密钥
- **时间服务器**：提供标准时间服务
- **API接口层**：提供对外接口服务
- **外部平台集成服务**：与TSP平台、OEM平台等外部系统进行集成
- **安全通信通道**：提供加密通信，防止消息被窃听或篡改

## 与外部平台的交互

数字钥匙系统需要与多个外部平台进行交互，主要包括：

### TSP平台交互

TSP平台（车联网服务提供商平台）是车联网服务提供商的核心系统，负责车辆远程监控、远程控制、用户管理等功能。数字钥匙系统与TSP平台的主要交互包括：

1. **车辆信息查询**：查询车辆基本信息、状态信息、位置信息等
2. **远程控制指令下发**：下发远程控制指令（如远程开锁、远程启动等）
3. **用户授权验证**：验证用户对车辆的控制权限
4. **事件通知**：接收车辆事件通知（如车门状态变化、车辆启动等）

### OEM平台交互

OEM平台（汽车制造商平台）是汽车制造商的核心系统，负责车辆生产管理、配置管理、售后服务等功能。数字钥匙系统与OEM平台的主要交互包括：

1. **车辆生产信息查询**：查询车辆生产信息、配置信息等
2. **车辆诊断**：获取车辆诊断信息、故障码等
3. **售后服务**：获取车辆保修信息、维修记录等

## 主要流程

### 首次蓝牙配对流程

首次蓝牙配对流程是用户首次使用数字钥匙与车辆建立连接的过程，详见[首次配对 - 泳道图.puml](首次配对%20-%20泳道图.puml)。主要步骤包括：

1. 用户在APP中选择添加数字钥匙
2. 扫描车辆二维码或输入VIN码识别车辆
3. 云平台生成虚拟密钥和配对令牌
4. 手机与车辆建立蓝牙连接
5. 双方进行身份验证和安全通道建立
6. 存储配对信息到各自的安全区域

### 蓝牙控车流程

蓝牙控车流程是用户通过蓝牙连接控制车辆的过程，详见[蓝牙控车-泳道图.puml](蓝牙控车-泳道图.puml)。

### 无感控车流程

无感控车流程是手机在不需要用户主动操作的情况下，自动与车辆建立蓝牙连接并执行预设操作的功能，详见[无感控车 - 泳道图.puml](无感控车%20-%20泳道图.puml)。主要步骤包括：

1. 系统唤醒后台APP并检查连接条件
2. 手机自动发起蓝牙连接请求
3. 车端验证连接请求并接受连接
4. 双方进行安全认证并建立安全通道
5. 车端计算与手机的距离并同步状态
6. 根据距离和预设策略执行自动控车操作
7. 持续监控距离变化，在用户离开时执行安全措施

无感控车具有以下优化策略：
- 系统级唤醒 - 利用操作系统提供的后台唤醒机制
- 上下文感知扫描 - 根据位置、时间、活动状态调整扫描频率
- 电量自适应 - 根据手机电量调整扫描策略
- 学习优化 - 记录用户习惯，优化扫描策略
- 多因素触发 - 综合多种因素决定是否发起连接
- 防误触机制 - 避免意外连接

### 外部平台交互流程

外部平台交互流程展示了数字钥匙系统与TSP平台、OEM平台等外部系统的交互过程，详见[外部平台交互-泳道图.puml](外部平台交互-泳道图.puml)。

## 安全设计

数字钥匙系统的安全设计包括以下几个方面：

1. **通信安全**：所有通信均采用TLS 1.3或更高版本加密，支持双向TLS认证
2. **认证与授权**：基于PKI的证书认证、API Key + 签名认证、OAuth 2.0认证等
3. **数据安全**：敏感数据传输和存储时加密，数据签名验证等
4. **密钥管理**：密钥生成、分发、存储、更新、撤销等全生命周期管理
5. **时间同步**：确保系统各组件的时间同步，防止重放攻击
6. **安全存储**：关键数据存储在安全区域，防止未授权访问
7. **异常检测**：监控系统使用情况，检测异常行为并触发安全响应

## 密钥存储与管理

系统中的密钥存储与管理遵循以下原则：

1. **分层密钥架构**：采用根密钥、中间密钥和会话密钥的分层架构
2. **安全存储**：密钥材料存储在硬件安全模块(HSM)或可信执行环境(TEE)中
3. **定期更新**：会话密钥定期更新，确保即使密钥泄露也只影响有限时间
4. **密钥备份**：关键密钥材料进行安全备份，防止数据丢失
5. **撤销机制**：支持密钥紧急撤销，应对安全事件

## 时间同步机制

时间同步机制确保系统各组件的时间一致性，主要包括：

1. **NTP同步**：与标准时间服务器进行NTP同步
2. **时间戳验证**：所有安全操作包含时间戳，防止重放攻击
3. **容错机制**：考虑网络延迟和时钟漂移，设置合理的时间容错范围
4. **离线处理**：在网络不可用时，采用本地时钟并记录时间偏差

## 文档索引

- [整体架构图.puml](整体架构图.puml)：系统整体架构图
- [首次配对 - 泳道图.puml](首次配对%20-%20泳道图.puml)：首次蓝牙配对流程
- [蓝牙控车-泳道图.puml](蓝牙控车-泳道图.puml)：蓝牙控车流程
- [无感控车 - 泳道图.puml](无感控车%20-%20泳道图.puml)：无感控车流程
- [外部平台接口设计.md](外部平台接口设计.md)：与外部平台的接口交互设计
- [外部平台交互-泳道图.puml](外部平台交互-泳道图.puml)：与外部平台的交互流程

## 注意事项

1. 本系统是一个完整的集成产品，包含手机APP对应的SDK、云平台SDK以及完整的车端软硬件开发
2. 在每一块设计时，都需要考虑到与其它模块的关联，以及如何集成到一起
3. 系统设计需要考虑安全性、可靠性、可扩展性等多方面因素
4. 实际部署时需根据具体车型和用户需求进行定制化配置
5. 系统应具备故障应急处理机制，确保在异常情况下仍能保障基本功能

## 后续开发计划

1. **故障应急处理机制**：设计在各种异常情况下的应急处理流程
2. **安全威胁处理**：完善对各类安全威胁的防御和响应机制
3. **平台兼容方案**：提高系统与不同车型和平台的兼容性
4. **1对N配对方案**：完善车端根密钥、手机端虚拟密钥以及配对令牌存储策略
5. **用户系统对接方案**：设计与TSP等平台的用户管理和权限管理对接方案
6. **密钥更新策略**：优化密钥定期更新和紧急更新的策略
7. **操作记录处理**：完善TBOX上报操作记录的处理策略，兼顾网络环境差异 
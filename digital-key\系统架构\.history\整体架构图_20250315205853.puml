@startuml
skinparam componentStyle rectangle

package "数字钥匙" {
    package "手机端" {
        component "钥匙管理模块" as KeyManage
        component "车辆控制模块" as VehicleCtrl
        component "蓝牙通信模块" as BLEComm
        component "安全存储模块" as SecStorage
        component "时间同步模块" as TimeSync
        note right of TimeSync
          提供精确时间基准；
          支持NTP/PTP时间同步协议；
          防篡改时间源验证；
          为安全验证提供可信时间戳；
          防止重放攻击的关键基础
        end note
        component "暗号交换模块" as MobileKeyExchange
        component "智能场景管理模块" as SmartSceneManager
        note right of SmartSceneManager
          负责检测用户状态与环境；
          决定无感连接触发条件；
          优化电量与连接策略
        end note
        note right of MobileKeyExchange
          负责与车端协商生成临时暗号；
          确保只有配对的双方能通信；
          支持对称与非对称密钥交换算法；
          实现临时会话密钥的定期更新；
          确保通信隧道安全性
        end note
        component "异常处理模块" as MobileErrorHandler
        note right of MobileErrorHandler
          监控通信异常；
          处理认证失败；
          实现自动重连；
          记录异常日志；
          支持指数退避重连策略；
          检测连接超时和心跳异常
        end note
    }
    
    package "车端" {
        component "蓝牙通信模块" as CarBLE
        component "钥匙验证模块" as KeyVerify
        component "指令执行模块" as CmdExec
        component "安全存储模块" as CarSecStorage
        component "远程通信模块" as TBoxComm
        component "时间同步模块" as CarTimeSync
        note right of CarTimeSync
          与云端时间服务器定期同步；
          提供车端精确时间基准；
          支持离线时的时钟漂移补偿；
          为所有安全操作提供可信时间戳
        end note
        component "暗号交换模块" as CarKeyExchange
        note right of CarKeyExchange
          负责与手机端协商生成临时暗号；
          确保只有配对的双方能通信；
          支持挑战-响应认证机制；
          使用根密钥保护暗号交换过程；
          实现会话密钥生命周期管理
        end note
        component "用户行为分析模块（核心无感控车算法）" as DistanceCalc
        note right of DistanceCalc
          分析RSSI信号强度；
          执行高精度距离计算；
          支持多节点定位；
          提供距离安全策略；
          实现防中继攻击保护；
          应用机器学习算法过滤环境干扰；
          根据距离执行分级权限控制；
          支持心跳机制监测连接状态
        end note
        component "异常处理模块" as CarErrorHandler
        note right of CarErrorHandler
          处理异常连接请求；
          监控异常操作；
          执行安全措施；
          记录安全事件；
          应对断连场景自动执行安全策略；
          监测并阻止重放攻击；
          识别并处理暗号交换失败
        end note
    }
    
    package "钥匙云平台" {
        component "钥匙生命周期管理" as KeyLifecycle
        note right of KeyLifecycle
          负责钥匙的创建、授权和撤销；
          管理配对状态；
          监控异常使用情况；
          支持临时钥匙授权；
          提供数字钥匙委托与收回；
          追踪钥匙使用历史
        end note
        
        component "车辆关联服务" as VINService
        note right of VINService
          验证VIN码有效性；
          管理车辆信息；
          处理车辆绑定请求；
          查询车辆配置和功能；
          确认车辆支持的数字钥匙版本
        end note
        
        component "安全认证中心" as AuthCenter
        note right of AuthCenter
          负责身份验证；
          确保只有授权用户能使用钥匙；
          处理安全事件
        end note
        
        component "密钥管理系统" as KeyMgmt
        note right of KeyMgmt
          生成和管理各种密钥；
          确保密钥安全存储；
          提供暗号交换基础；
          监控密钥使用情况；
          管理根密钥下发流程；
          执行密钥周期性更新；
          生成虚拟密钥和配对令牌；
          管理授权手机标识码；
          支持紧急密钥撤销
        end note
        
        component "时间服务器" as TimeServer
        component "统一接口服务" as UnifiedAPI
        note right of UnifiedAPI
          提供标准接口；
          建立安全通信通道；
          防止消息被篡改或重放
        end note
        
        component "安全通信通道" as SecureChannel
        note right of SecureChannel
          提供加密通信；
          防止消息被窃听或篡改；
          支持暗号更新；
          监控通信异常；
          实现防重放和防中继措施；
          确保双向身份验证；
          支持会话恢复和断点续传；
          实现数据完整性保护
        end note
        
        component "异常监控与处理" as CloudErrorHandler
        note right of CloudErrorHandler
          监控异常登录；
          分析异常使用模式；
          实施安全风控；
          触发安全预警；
          执行数据脱敏处理
        end note
    }
    
    ' 简化外部平台设计
    component "外部平台(TSP/OEM等)" as ExternalPlatforms
    note right of ExternalPlatforms
      提供车辆远程服务；
      与钥匙云平台安全对接；
      支持远程操作和状态查询；
      提供车辆特定功能支持；
      结合厂商专有安全协议
    end note
    
    database "数据存储" {
        database "钥匙数据" as KeysDB
        database "密钥材料" as KeyMaterialsDB
        database "通信记录" as SessionDB
        note right of SessionDB
          记录通信事件；
          支持异常检测；
          不存储密钥本身
        end note
        database "操作日志" as OperationLogDB
        note right of OperationLogDB
          记录所有钥匙操作；
          支持安全审计；
          保存异常记录
        end note
    }
    
    database "安全基础设施" {
        database "密码机" as HSM
        note right of HSM
          执行密钥操作；
          确保密钥安全；
          提供物理防护
        end note
        
        database "证书系统" as PKICA
        note right of PKICA
          提供设备身份证书；
          支持证书验证和撤销；
          为加密通信提供密钥基础；
          管理信任链和根证书；
          支持证书生命周期管理
        end note
        
        database "密钥备份系统" as SecureBackup
        note right of SecureBackup
          安全备份关键信息；
          防止数据丢失；
          多重保护机制；
          支持灾难恢复；
          采用阈值密钥分割技术
        end note
        
        database "安全监控" as SecMonitor
        note right of SecMonitor
          监控系统使用情况；
          检测异常行为；
          触发安全响应；
          提供安全报告
        end note
    }
}

' 使用标记替代直接连线
' 手机端内部关系
KeyManage -[#blue]-> VehicleCtrl : A1
VehicleCtrl -[#blue]-> BLEComm : A2
BLEComm -[#blue]-> SecStorage : A3
SecStorage -[#blue]-> TimeSync : A4
BLEComm -[#blue]-> MobileKeyExchange : A5
MobileKeyExchange -[#blue]-> SecStorage : A6
SmartSceneManager -[#blue]-> BLEComm : A7
SmartSceneManager -[#blue]-> VehicleCtrl : A8
BLEComm -[#blue]-> MobileErrorHandler : A9
MobileErrorHandler -[#blue]-> KeyManage : A10

' 车端内部关系
CarBLE -[#green]-> KeyVerify : B1
KeyVerify -[#green]-> CmdExec : B2
CmdExec -[#green]-> CarSecStorage : B3
CarSecStorage -[#green]-> TBoxComm : B4
TBoxComm -[#green]-> CarTimeSync : B5
CarBLE -[#green]-> CarKeyExchange : B6
CarKeyExchange -[#green]-> CarSecStorage : B7
CarBLE -[#green]-> DistanceCalc : B8
DistanceCalc -[#green]-> CmdExec : B9
CarBLE -[#green]-> CarErrorHandler : B10
CarErrorHandler -[#green]-> CmdExec : B11

' 钥匙服务内部关系
KeyLifecycle -[#red]-> VINService : C1
VINService -[#red]-> AuthCenter : C2
AuthCenter -[#red]-> KeyMgmt : C3
KeyMgmt -[#red]-> TimeServer : C4
TimeServer -[#red]-> UnifiedAPI : C5
KeyMgmt -[#red]-> SecureChannel : C6
SecureChannel -[#red]-> UnifiedAPI : C7
KeyLifecycle -[#red]-> CloudErrorHandler : C8
CloudErrorHandler -[#red]-> AuthCenter : C9

' 跨系统关系
BLEComm <-[#purple]-> CarBLE : D1
UnifiedAPI <-[#purple]-> TBoxComm : D2
UnifiedAPI <-[#purple]-> KeyManage : D3
TimeServer -[#purple]-> TimeSync : D4
TimeServer -[#purple]-> CarTimeSync : D5
MobileKeyExchange <-[#purple]-> CarKeyExchange : D6
MobileErrorHandler <-[#purple]-> CarErrorHandler : D7
CloudErrorHandler <-[#purple]-> MobileErrorHandler : D8

' 数据库关系
KeyLifecycle -[#orange]-> KeysDB : E1
KeyMgmt -[#orange]-> KeyMaterialsDB : E2
AuthCenter -[#orange]-> PKICA : E3
KeyMgmt -[#orange]-> HSM : E4
KeyMgmt -[#orange]-> SecureBackup : E5
SecureChannel -[#orange]-> SessionDB : E6
KeyMgmt -[#orange]-> SecMonitor : E7
CloudErrorHandler -[#orange]-> OperationLogDB : E8
MobileErrorHandler -[#orange]-> OperationLogDB : E9
CarErrorHandler -[#orange]-> OperationLogDB : E10

' 外部平台与钥匙云平台的关系 - 简化连接
UnifiedAPI <-[#brown]-> ExternalPlatforms : F1

note right of UnifiedAPI
  统一接口服务负责与车辆和外部系统通信，
  确保信息安全传递
end note

' 表示关键的关系
KeyMgmt <-[#magenta]-> HSM : G1
AuthCenter <-[#magenta]-> PKICA : G2
KeyLifecycle -[#magenta]-> KeyMgmt : G3
UnifiedAPI -[#magenta]-> AuthCenter : G4
SecureChannel <-[#magenta]-> SecMonitor : G5

legend
  <b>连接关系说明</b>
  
  <b>A系列</b>：手机端内部模块关系
  A1: 钥匙管理模块向车辆控制模块提供钥匙信息和授权令牌，包含权限级别和有效期
  A2: 车辆控制模块构建安全控制指令，通过蓝牙通信模块发送带有数字签名的控制命令
  A3: 蓝牙通信模块调用安全存储模块获取会话密钥和安全参数，实现数据加密和完整性校验
  A4: 安全存储模块利用时间同步模块的精确时间戳生成防重放安全令牌，实现指令唯一性
  A5: 蓝牙通信模块激活暗号交换模块执行ECDH密钥协商算法，支持P-256曲线的密钥交换
  A6: 暗号交换模块将协商生成的临时会话密钥通过硬件绑定密钥加密后存入TEE/SE安全区域
  A7: 智能场景管理模块根据用户行为模式和环境数据动态调整蓝牙扫描策略和广播参数
  A8: 智能场景管理模块为车辆控制模块提供上下文感知数据，优化无感控车决策算法
  A9: 蓝牙通信模块实时监控连接质量，向异常处理模块报告信号异常和认证失败事件
  A10: 异常处理模块实施指数退避重连策略，同时向钥匙管理模块上报需要云端干预的安全事件
  
  <b>B系列</b>：车端内部模块关系
  B1: 蓝牙通信模块接收指令后进行初步解密，传递给钥匙验证模块执行多因素安全验证
  B2: 钥匙验证模块完成挑战-响应认证和权限检查后，将带有安全上下文的指令传递给执行模块
  B3: 指令执行模块执行操作并生成包含操作结果和车辆状态的事件日志，存入安全存储区域
  B4: 安全存储模块对关键状态变更加密后通过远程通信模块上报到云端，确保状态一致性
  B5: 远程通信模块使用时间同步模块提供的NTP/PTP同步时间戳，防止中间人和重放攻击
  B6: 蓝牙通信模块检测到连接请求后，调用暗号交换模块生成一次性随机数并启动密钥协商
  B7: 暗号交换模块将协商密钥使用HSM管理的车辆根密钥保护后，存入防篡改硬件安全区域
  B8: 蓝牙通信模块将多点采集的RSSI原始数据传输给距离计算模块进行环境噪声过滤
  B9: 距离计算模块应用机器学习算法计算精确距离并实施基于距离的分级权限控制策略
  B10: 蓝牙通信模块通过心跳包序列号检测，识别潜在重放攻击并报告给异常处理模块
  B11: 异常处理模块根据安全风险等级触发相应安全措施，包括密钥刷新和安全记录上报
  
  <b>C系列</b>：钥匙云平台内部模块关系
  C1: 钥匙生命周期管理模块通过密钥派生函数(KDF)生成钥匙材料，传递给车辆关联服务
  C2: 车辆关联服务验证车辆数字签名并执行OEM权限检查，向安全认证中心提供车辆元数据
  C3: 安全认证中心执行多层次身份验证后，请求密钥管理系统生成符合FIPS 140-2的密钥材料
  C4: 密钥管理系统向时间服务器请求带有抗量子签名的可信时间戳，用于密钥生成熵源增强
  C5: 时间服务器向统一接口服务提供RFC3161兼容的时间同步服务，支持时间证明和验证
  C6: 密钥管理系统为安全通信通道提供会话密钥和对称加密算法套件，支持AES-GCM和ChaCha20
  C7: 安全通信通道实现TLS 1.3和自定义密钥扩展协议，为统一接口服务提供端到端加密
  C8: 钥匙生命周期管理模块检测钥匙使用异常模式，激活安全风控并通知异常监控处理
  C9: 异常监控与处理模块执行行为分析和威胁建模，向安全认证中心提供风险评分和建议
  
  <b>D系列</b>：跨系统通信关系
  D1: 手机端与车端蓝牙模块建立BLE 5.0安全通道，实现基于ECDHE的会话协商和双向认证
  D2: 云平台接口服务与车端通信模块通过4G/5G加密通道交换车辆状态和远程指令，支持断点续传
  D3: 云平台接口服务与手机端钥匙管理模块通过mTLS建立安全通道，实现钥匙信息同步和授权
  D4: 云平台时间服务器与手机端时间同步模块定期执行NTP安全同步，并验证时间源的真实性
  D5: 云平台时间服务器为车端提供PTP高精度时间同步，支持亚毫秒级精度和防篡改验证
  D6: 手机端与车端暗号交换模块执行基于NIST SP 800-56A的密钥协商协议，支持前向安全性
  D7: 手机端与车端异常处理模块交换安全事件和异常特征，协同实施安全防护和分级响应
  D8: 云平台异常监控模块收集手机端行为特征和异常事件，进行集中式安全分析和威胁响应
  
  <b>E系列</b>：数据存储关系
  E1: 钥匙生命周期管理模块对钥匙数据库执行强一致性操作，确保授权关系的原子性更新
  E2: 密钥管理系统通过HSM硬件接口访问离线密钥库，采用分片存储和阈值解密保护密钥材料
  E3: 安全认证中心访问X.509 v3证书系统，执行OCSP证书有效性检查和CRL撤销状态验证
  E4: 密钥管理系统调用FIPS 140-2 Level 3及以上密码机执行密钥生成和签名操作，防止密钥泄露
  E5: 密钥管理系统使用Shamir密钥分割算法将密钥材料备份到多区域异地灾备系统
  E6: 安全通信通道将通信元数据（不含密钥本身）存入会话数据库，支持异常检测和审计
  E7: 密钥管理系统向安全监控系统提供脱敏的密钥使用统计，用于异常行为分析和审计
  E8: 云平台异常处理模块将安全事件以W3C标准格式记录到防篡改的区块链操作日志
  E9: 手机端异常处理将加密的异常事件上报到集中式日志系统，支持离线分析和故障诊断
  E10: 车端异常处理模块将关键安全事件写入防篡改EEPROM，并同步至云端日志系统
  
  <b>F系列</b>：外部平台交互
  F1: 统一接口服务通过OAuth 2.0和JWT令牌与外部平台建立身份联盟，支持跨域授权和API安全调用
  
  <b>G系列</b>：关键安全关系
  G1: 密钥管理系统通过PKCS#11安全接口与硬件密码机交互，执行高敏感度的密钥操作
  G2: 安全认证中心实施OIDC协议与证书系统交互，管理PKI信任链和证书生命周期
  G3: 钥匙生命周期管理向密钥管理系统发送符合KMIP标准的钥匙生命周期事件，触发密钥操作
  G4: 统一接口服务通过零信任架构向安全认证中心持续验证每个请求的身份和权限
  G5: 安全通信通道向安全监控系统提供实时通信行为分析，支持AI驱动的威胁检测和响应
end legend

@enduml
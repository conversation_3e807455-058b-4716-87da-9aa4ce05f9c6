<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="100px">
      <el-form-item label="分享人姓名" prop="sharerName">
        <el-input
          v-model="queryParams.sharerName"
          placeholder="请输入分享人姓名"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="被分享人姓名" prop="shareeName">
        <el-input
          v-model="queryParams.shareeName"
          placeholder="请输入被分享人姓名"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="车辆VIN" prop="vehicleVinOfFuzzyMatch">
        <el-input
          v-model="queryParams.vehicleVinOfFuzzyMatch"
          placeholder="请输入车辆VIN"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <!-- <el-form-item label="分享开始时间" prop="shareStartTime">
        <el-date-picker clearable
          v-model="queryParams.shareStartTime"
          type="date"
          value-format="yyyy-MM-dd"
          placeholder="请选择分享开始时间">
        </el-date-picker>
      </el-form-item>
      <el-form-item label="分享结束时间" prop="shareEndTime">
        <el-date-picker clearable
          v-model="queryParams.shareEndTime"
          type="date"
          value-format="yyyy-MM-dd"
          placeholder="请选择分享结束时间">
        </el-date-picker>
      </el-form-item> -->
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <!-- <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="el-icon-plus"
          size="mini"
          @click="handleAdd"
          v-hasPermi="['dk:digitalKeyShareDetail:add']"
        >新增</el-button>
      </el-col> -->
      <el-col :span="1.5">
        <el-button
          type="success"
          plain
          icon="el-icon-edit"
          size="mini"
          :disabled="single"
          @click="handleUpdate"
          v-hasPermi="['dk:digitalKeyShareDetail:edit']"
        >修改</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="danger"
          plain
          icon="el-icon-delete"
          size="mini"
          :disabled="multiple"
          @click="handleDelete"
          v-hasPermi="['dk:digitalKeyShareDetail:remove']"
        >删除</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="el-icon-download"
          size="mini"
          @click="handleExport"
          v-hasPermi="['dk:digitalKeyShareDetail:export']"
        >导出</el-button>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="digitalKeyShareDetailList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="分享人" align="center" prop="sharerName" />
      <el-table-column label="被分享人" align="center" prop="shareeName" />
      <el-table-column label="分享车辆VIN码" align="center" prop="shareVehicleVin" />
      <el-table-column label="分享开始时间" align="center" prop="shareStartTime" width="180">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.shareStartTime) }}</span>
        </template>
      </el-table-column>
      <el-table-column label="分享结束时间" align="center" prop="shareEndTime" width="180">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.shareEndTime) }}</span>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="text"
            icon="el-icon-edit"
            @click="handleUpdate(scope.row)"
            v-hasPermi="['dk:digitalKeyShareDetail:edit']"
          >修改</el-button>
          <el-button
            size="mini"
            type="text"
            icon="el-icon-delete"
            @click="handleDelete(scope.row)"
            v-hasPermi="['dk:digitalKeyShareDetail:remove']"
          >删除</el-button>
        </template>
      </el-table-column>
    </el-table>
    
    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加或修改车辆数字钥匙分享详情对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="500px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="100px">
        <!-- <el-form-item label="分享开始时间" prop="shareStartTime">
          <el-date-picker clearable
            v-model="form.shareStartTime"
            type="date"
            value-format="yyyy-MM-dd"
            placeholder="请选择分享开始时间">
          </el-date-picker>
        </el-form-item> -->
        <el-form-item label="分享结束时间" prop="shareEndTime">
          <el-date-picker clearable
            v-model="form.shareEndTime"
            type="date"
            value-format="yyyy-MM-dd"
            placeholder="请选择分享结束时间">
          </el-date-picker>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { listDigitalKeyShareDetail, getDigitalKeyShareDetail, delDigitalKeyShareDetail, addDigitalKeyShareDetail, updateDigitalKeyShareDetail } from "@/api/dk/digitalKeyShareDetail";

export default {
  name: "DigitalKeyShareDetail",
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 车辆数字钥匙分享详情表格数据
      digitalKeyShareDetailList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        sharerName: null,
        shareeName: null,
        vehicleVinOfFuzzyMatch: null,
        shareStartTime: null,
        shareEndTime: null
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
      }
    };
  },
  created() {
    this.getList();
  },
  methods: {
    /** 查询车辆数字钥匙分享详情列表 */
    getList() {
      this.loading = true;
      listDigitalKeyShareDetail(this.queryParams).then(response => {
        this.digitalKeyShareDetailList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        detailId: null,
        sharerId: null,
        shareeId: null,
        shareVehicleVin: null,
        shareStartTime: null,
        shareEndTime: null
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.detailId)
      this.single = selection.length!==1
      this.multiple = !selection.length
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "添加车辆数字钥匙分享详情";
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const detailId = row.detailId || this.ids
      getDigitalKeyShareDetail(detailId).then(response => {
        this.form = response.data;
        this.open = true;
        this.title = "修改车辆数字钥匙分享详情";
      });
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate(valid => {
        if (valid) {
          if (this.form.sharerId != null) {
            updateDigitalKeyShareDetail(this.form).then(response => {
              this.$modal.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            });
          } else {
            addDigitalKeyShareDetail(this.form).then(response => {
              this.$modal.msgSuccess("新增成功");
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const detailIds = row.detailId || this.ids;
      this.$modal.confirm('是否确认删除当前的选择的数据项？').then(function() {
        return delDigitalKeyShareDetail(detailIds);
      }).then(() => {
        this.getList();
        this.$modal.msgSuccess("删除成功");
      }).catch(() => {});
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download('dk/digitalKeyShareDetail/export', {
        ...this.queryParams
      }, `digitalKeyShareDetail_${new Date().getTime()}.xlsx`)
    }
  }
};
</script>

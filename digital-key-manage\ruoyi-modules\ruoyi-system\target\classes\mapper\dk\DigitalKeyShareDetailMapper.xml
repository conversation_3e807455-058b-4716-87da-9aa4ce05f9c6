<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.dk.mapper.DigitalKeyShareDetailMapper">
    
    <resultMap type="DigitalKeyShareDetail" id="DigitalKeyShareDetailResult">
        <result property="detailId"    column="detail_id"    />
        <result property="sharerId"    column="sharer_id"    />
        <result property="shareeId"    column="sharee_id"    />
        <result property="shareVehicleVin"    column="share_vehicle_vin"    />
        <result property="shareStartTime"    column="share_start_time"    />
        <result property="shareEndTime"    column="share_end_time"    />
    </resultMap>

    <sql id="selectDigitalKeyShareDetailVo">
        select
           t1.detail_id, t1.sharer_id, t2.user_name AS sharerName, t3.user_name AS shareeName,
           t1.sharee_id, t1.share_vehicle_vin, t1.share_start_time, t1.share_end_time, t4.bluetooth_perm_key AS bluetoothPermKey
        from digital_key_share_detail t1
        LEFT JOIN sys_user t2 ON t2.user_id = t1.sharer_id
        LEFT JOIN sys_user t3 ON t3.user_id = t1.sharee_id
        LEFT JOIN dk_vehicle_bluetooth_keys t4 ON t4.user_id = t1.sharer_id AND t4.vehicle_vin = t1.share_vehicle_vin
    </sql>

    <select id="selectDigitalKeyShareDetailList" parameterType="DigitalKeyShareDetail" resultMap="DigitalKeyShareDetailResult">
        <include refid="selectDigitalKeyShareDetailVo"/>
        <where>  
            <if test="sharerName != null and sharerName != ''"> and t2.user_name like concat('%', #{sharerName}, '%')</if>
            <if test="shareeName != null and shareeName != ''"> and t3.user_name like concat('%', #{shareeName}, '%')</if>
            <if test="sharerId != null or shareeId != null">
                AND (
                    <if test="sharerId != null">
                        t1.sharer_id = #{sharerId}
                    </if>
                    <if test="sharerId != null and shareeId != null">
                        OR
                    </if>
                    <if test="shareeId != null">
                        t1.sharee_id = #{shareeId}
                    </if>
                )
            </if>
            <if test="vehicleVinOfFuzzyMatch != null and vehicleVinOfFuzzyMatch != ''"> and t1.share_vehicle_vin like concat('%', #{vehicleVinOfFuzzyMatch}, '%')</if>
            <if test="shareVehicleVin != null and shareVehicleVin != ''"> and t1.share_vehicle_vin = #{shareVehicleVin}</if>
            <!-- 移除了对当前时间的限制，以获取所有数据，包括过期的 -->
            <if test="shareStartTime != null "> and t1.share_start_time &lt;= #{shareStartTime}</if>
            <if test="shareEndTime != null "> and t1.share_end_time &gt;= #{shareEndTime}</if>
        </where>
    </select>
    
    <select id="selectDigitalKeyShareDetailByDetailId" parameterType="Long" resultMap="DigitalKeyShareDetailResult">
        <include refid="selectDigitalKeyShareDetailVo"/>
        where t1.detail_id = #{detailId}
    </select>
        
    <insert id="insertDigitalKeyShareDetail" parameterType="DigitalKeyShareDetail">
        insert into digital_key_share_detail
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="sharerId != null">sharer_id,</if>
            <if test="shareeId != null">sharee_id,</if>
            <if test="shareVehicleVin != null">share_vehicle_vin,</if>
            <if test="shareStartTime != null">share_start_time,</if>
            <if test="shareEndTime != null">share_end_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="sharerId != null">#{sharerId},</if>
            <if test="shareeId != null">#{shareeId},</if>
            <if test="shareVehicleVin != null">#{shareVehicleVin},</if>
            <if test="shareStartTime != null">#{shareStartTime},</if>
            <if test="shareEndTime != null">#{shareEndTime},</if>
         </trim>
    </insert>

    <update id="updateDigitalKeyShareDetail" parameterType="DigitalKeyShareDetail">
        update digital_key_share_detail
        <trim prefix="SET" suffixOverrides=",">
            <if test="shareeId != null">sharee_id = #{shareeId},</if>
            <if test="shareVehicleVin != null">share_vehicle_vin = #{shareVehicleVin},</if>
            <if test="shareStartTime != null">share_start_time = #{shareStartTime},</if>
            <if test="shareEndTime != null">share_end_time = #{shareEndTime},</if>
        </trim>
        where detail_id = #{detailId}
    </update>

    <delete id="deleteDigitalKeyShareDetailByDetailId" parameterType="Long">
        delete from digital_key_share_detail where detail_id = #{detailId}
    </delete>

    <delete id="deleteDigitalKeyShareDetailByDetailIds" parameterType="String">
        delete from digital_key_share_detail where detail_id in
        <foreach item="detailId" collection="array" open="(" separator="," close=")">
            #{detailId}
        </foreach>
    </delete>
</mapper>

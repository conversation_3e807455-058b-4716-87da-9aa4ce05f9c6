@startuml
skinparam monochrome true

participant "APP" as Mobile
participant "钥匙云平台" as Cloud
participant "TSP平台" as TSP
participant "OEM平台" as OEM
participant "车端（TBOX）" as CarTBOX

== 数字钥匙申请与车辆绑定流程 ==

Mobile -> Mobile : 1. 用户在APP中选择添加数字钥匙
Mobile -> Mobile : 2. 扫描车辆二维码或输入VIN码识别车辆
Mobile -> Cloud : 3. 发送车辆VIN码，请求获取数字钥匙
Cloud -> TSP : 4. 查询VIN码对应的车辆信息
TSP -> TSP : 5. 验证VIN码有效性
TSP -> Cloud : 6. 返回车辆基本信息
Cloud -> OEM : 7. 查询车辆生产配置信息
OEM -> Cloud : 8. 返回车辆配置信息
Cloud -> Cloud : 9. 根据VIN码生成虚拟密钥（VIRKEY）
Cloud -> Mobile : 10. 下发虚拟密钥到手机
Mobile -> Mobile : 11. 保存虚拟密钥到安全存储区

Cloud -> TSP : 12. 查询VIN码对应的TBOX信息
TSP -> Cloud : 13. 返回TBOX信息（ID、通信凭证等）
Cloud -> TSP : 14. 请求与车辆TBOX建立连接
TSP -> CarTBOX : 15. 通过蜂窝网络连接到车辆TBOX
CarTBOX -> TSP : 16. TBOX身份验证和连接确认
TSP -> Cloud : 17. 返回TBOX连接结果
Cloud -> CarTBOX : 18. 通过TSP平台下发根密钥到车辆
CarTBOX -> CarTBOX : 19. 存储根密钥
CarTBOX -> Cloud : 20. 确认密钥接收成功
Cloud -> Mobile : 21. 通知APP车辆绑定成功
Mobile -> Mobile : 22. 显示绑定成功，可以使用数字钥匙

== 远程控制车辆流程 ==

Mobile -> Mobile : 1. 用户在APP中选择远程控制功能（如开锁）
Mobile -> Cloud : 2. 发送远程控制请求
Cloud -> Cloud : 3. 验证用户权限和密钥有效性
Cloud -> TSP : 4. 发送远程控制指令
TSP -> TSP : 5. 验证指令合法性
TSP -> CarTBOX : 6. 下发控制指令到车辆TBOX
CarTBOX -> CarTBOX : 7. 执行控制指令（如开锁）
CarTBOX -> TSP : 8. 返回指令执行结果
TSP -> Cloud : 9. 转发执行结果
Cloud -> Mobile : 10. 通知APP执行结果
Mobile -> Mobile : 11. 显示执行结果（如开锁成功）

== 车辆诊断信息查询流程 ==

Mobile -> Mobile : 1. 用户在APP中选择查看车辆诊断信息
Mobile -> Cloud : 2. 发送诊断信息查询请求
Cloud -> OEM : 3. 请求车辆诊断信息
OEM -> CarTBOX : 4. 通过TSP平台请求车辆实时诊断数据
CarTBOX -> OEM : 5. 返回诊断数据
OEM -> OEM : 6. 分析诊断数据
OEM -> Cloud : 7. 返回诊断结果和建议
Cloud -> Mobile : 8. 转发诊断结果
Mobile -> Mobile : 9. 显示诊断结果和维修建议

== 钥匙权限变更流程 ==

Mobile -> Mobile : 1. 车主在APP中修改共享钥匙权限
Mobile -> Cloud : 2. 发送权限变更请求
Cloud -> Cloud : 3. 更新钥匙权限配置
Cloud -> TSP : 4. 通知TSP平台权限变更
TSP -> CarTBOX : 5. 下发权限变更指令到车辆
CarTBOX -> CarTBOX : 6. 更新本地权限配置
CarTBOX -> TSP : 7. 确认权限更新完成
TSP -> Cloud : 8. 转发确认信息
Cloud -> Mobile : 9. 通知APP权限变更成功
Mobile -> Mobile : 10. 显示权限变更成功

@enduml 
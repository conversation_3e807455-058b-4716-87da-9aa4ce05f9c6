@startuml
skinparam monochrome true

participant "APP(后台运行)" as Mobile
participant "车端（BLE & TBOX）" as CarBT
participant "钥匙云平台" as Cloud
participant "TSP/OEM平台" as ExternalSystem

== 无感连接建立 ==

note over Mobile, CarBT
  无感连接是指手机在不需要用户主动操作的情况下，
  自动与车辆建立蓝牙连接并执行预设操作的功能。
  前提是APP已在手机后台运行，且已获得必要权限。
end note

Mobile -> Mobile : 1a. 系统唤醒后台APP
Mobile -> Mobile : 1b. 检查是否满足连接条件
note right of Mobile
  连接条件包括:
  - 用户已启用无感连接
  - 手机电量充足
  - 检测到用户走路状态（省电策略）
end note

Mobile -> Mobile : 2. 从安全区域读取已存储的车辆连接信息
note right of Mobile
  连接信息包含:
  - BLE无感连接必要信息（设备UUID等）
  - 配对密钥
end note

Mobile -> CarBT : 3. 直接发起蓝牙连接请求

CarBT -> CarBT : 4a. 验证连接请求来源
CarBT -> CarBT : 4b. 检查连接信息有效性
CarBT -> CarBT : 4c. 检查是否满足自动接受条件
note right of CarBT
  自动接受条件:
  - 连接信息有效
  - 车辆处于安全状态
  - 未触发安全警报
  - 电量状态正常
end note

CarBT -> Mobile : 4d. 接受连接请求

== 安全认证过程 ==

Mobile -> CarBT : 8. 发送身份验证请求（包含设备ID和时间戳）
CarBT -> CarBT : 9a. 从安全区域读取该设备的会话密钥
CarBT -> Mobile : 9b. 发送认证挑战（随机数）

Mobile -> Mobile : 10a. 使用会话密钥计算认证响应
Mobile -> CarBT : 10b. 发送认证响应

CarBT -> CarBT : 11a. 验证认证响应
CarBT -> Mobile : 11b. 发送认证结果

note over Mobile, CarBT
  双向认证确保:
  1. 手机确实是已配对的授权设备
  2. 车辆确实是用户的车辆
  3. 防止中间人攻击和重放攻击
end note

== 快速安全通道建立 ==

Mobile <-> CarBT : 12. 快速建立加密安全通道
note over Mobile, CarBT
  使用存储的会话密钥快速建立安全通道,
  无需完整的密钥协商过程
end note

== 距离计算与状态同步 ==

CarBT -> CarBT : 13a. 从连接中获取RSSI值
CarBT -> CarBT : 13b. 计算与手机的距离（多节点则是成对RSSI值进入算法）
note right of CarBT
  距离计算考虑因素:
  - RSSI信号强度
  - 环境干扰因素
  - 信号反射影响
  - 历史数据校准
end note

CarBT -> Mobile : 14. 发送车辆当前状态信息
note right of CarBT
  - 车门锁状态
  - 车窗状态
  - 发动机状态
  - 电池电量
  - 其他车辆信息
  - 计算得到的距离信息
end note

Mobile -> Mobile : 15. 后台更新车辆状态缓存

alt 距离在安全范围内
    == 自动控车执行 ==
    
    Mobile -> Mobile : 16a. 根据用户预设策略准备自动控车指令
    note right of Mobile
      可能的自动控车指令:
      - 车门解锁
      - 迎宾灯控制等
    end note
    
    Mobile -> CarBT : 16b. 发送控车请求，请求挑战信息
    CarBT -> Mobile : 16c. 返回随机挑战数
    
    Mobile -> Mobile : 16d. 使用收到的随机挑战数生成控车指令并签名
    note right of Mobile
      指令包含:
      - 操作类型（如解锁、上锁等）
      - 时间戳（辅助信息）
      - 车端提供的随机挑战数（确保唯一性）
      - 数字签名（验证指令完整性）
    end note
    
    Mobile -> Mobile : 16e. 使用会话密钥加密指令
    Mobile -> CarBT : 17. 发送加密的控车指令
    
    CarBT -> CarBT : 18a. 使用会话密钥解密指令
    CarBT -> CarBT : 18b. 验证数字签名
    CarBT -> CarBT : 18c. 检查用户权限
    note right of CarBT
      权限检查包括:
      - 验证用户身份（车主/临时用户）
      - 检查操作权限级别
      - 验证操作授权状态
      - 检查安全限制条件
    end note
    CarBT -> CarBT : 18d. 验证指令中的随机挑战数是否为刚刚发出且未使用过的
    note right of CarBT
      随机挑战验证:
      - 确认是否为本次会话刚刚生成的随机数
      - 确保每个随机挑战数只被使用一次
      - 比传统时效性检查更安全，完全防止重放攻击
    end note
    
    alt 指令验证通过
        CarBT -> CarBT : 19a. 执行控车操作
        CarBT -> Mobile : 19b. 返回操作执行结果
        CarBT -> Cloud : 19c. 上报操作记录（可选，根据网络状态）
        Mobile -> Mobile : 19d. 生成系统通知提醒用户操作已执行
    else 指令验证失败
        CarBT -> Mobile : 20a. 返回验证失败信息
        note right of CarBT
          失败原因可能是:
          - 解密失败
          - 签名验证失败
          - 权限不足
          - 指令过期
          - 重复执行
        end note
        Mobile -> Mobile : 20b. 记录失败原因，不打扰用户
    end
else 距离超出安全范围
    == 断开连接处理 ==
    
    note over Mobile, CarBT
      距离超出安全范围的处理分为两种情况:
      1. 用户正常离开 - 距离逐渐增大，有足够时间执行完整断开流程
      2. 用户快速离开 - 距离快速增大，需要紧急处理
    end note
    
    alt 用户正常离开（距离逐渐增大）
        CarBT -> Mobile : 21a. 发送距离预警通知
        Mobile -> Mobile : 21b. 准备断开连接前的操作
        note right of Mobile
          可能的断开前操作:
          - 发送车门上锁指令
          - 关闭车窗
          - 其他安全措施
        end note
        
        Mobile -> CarBT : 21c. 发送最终控车指令(如上锁)
        CarBT -> CarBT : 21d. 执行最终控车操作
        CarBT -> Mobile : 21e. 返回操作结果
        
        Mobile -> CarBT : 21f. 发送断开连接请求
        CarBT -> CarBT : 21g. 清理临时会话资源
        Mobile -> Mobile : 21h. 清理临时会话资源
        Mobile -> Mobile : 21i. 生成系统通知提醒用户车辆已锁定
    else 用户快速离开（距离快速增大）
        CarBT -> CarBT : 22a. 检测到距离快速增大或信号急剧减弱
        CarBT -> CarBT : 22b. 触发紧急安全措施
        note right of CarBT
          紧急安全措施:
          - 自动执行车辆上锁
          - 关闭可能的危险设备（如车窗）
          - 记录异常断开事件
          - 可选：向云端发送紧急断开通知
        end note
        CarBT -> CarBT : 22c. 强制清理会话资源
        Mobile -> Mobile : 22d. 检测到连接丢失
        Mobile -> Mobile : 22e. 执行本地紧急处理
        note right of Mobile
          本地紧急处理:
          - 记录异常断开事件
          - 清理会话资源
          - 准备下次重连策略
          - 可选：向用户发送通知
        end note
    else 超出连接范围（信号丢失）
        CarBT -> CarBT : 23a. 检测到信号丢失
        CarBT -> Mobile : 23b. 尝试发送连接断开通知
        CarBT -> CarBT : 23c. 执行安全措施并清理会话资源
        Mobile -> Mobile : 23d. 检测到连接异常
        Mobile -> Mobile : 23e. 记录连接断开状态并清理资源
    end
end

== 持续状态监控 ==

CarBT -> CarBT : 24. 监控车辆状态变化
CarBT -> CarBT : 25. 持续计算并更新距离信息
alt 状态发生变化
    CarBT -> Mobile : 26a. 推送状态变化通知
    Mobile -> Mobile : 26b. 更新后台缓存状态
end

== 连接保持与心跳机制 ==

Mobile <-> CarBT : 27. 定期发送心跳包保持连接
note over Mobile, CarBT
  心跳机制:
  1. 检测连接是否正常
  2. 防止连接超时断开
  3. 定期更新车辆状态
  4. 更新距离信息
end note

alt 心跳超时
    Mobile -> Mobile : 28a. 检测到连接异常
    Mobile -> Mobile : 28b. 尝试重新连接
    alt 重连失败
        Mobile -> Mobile : 28c. 记录连接失败，不打扰用户
    end
end

== 无感连接优化策略 ==

note over Mobile
  手机端优化策略:
  1. 系统级唤醒 - 利用操作系统提供的后台唤醒机制
  2. 上下文感知扫描 - 根据位置、时间、活动状态调整扫描频率
  3. 电量自适应 - 根据手机电量调整扫描策略
  4. 学习优化 - 记录用户习惯，优化扫描策略
  5. 多因素触发 - 综合多种因素决定是否发起连接
  6. 防误触机制 - 避免意外连接
end note

note over CarBT
  车端优化策略:
  1. 智能广播 - 根据环境和状态调整广播参数
  2. 安全广播 - 确保广播内容安全
  3. 安全优先 - 严格验证连接请求
  4. 状态感知 - 根据车辆状态调整连接策略
  5. 异常防护 - 检测并防范异常连接
  6. 权限分级 - 不同操作需要不同级别的权限
  7. 距离计算优化 - 使用高级算法提高距离计算准确性
end note

== 异常处理 ==

note over Mobile, CarBT
  无感连接过程中可能出现的问题及处理方式:
end note

alt 认证失败
    CarBT -> Mobile : 返回认证失败信息
    Mobile -> Mobile : 记录认证失败，下次尝试完整连接流程
    Mobile -> Cloud : 上报认证异常（如果有网络）
else 指令执行超时
    Mobile -> Mobile : 检测到指令执行长时间无响应
    Mobile -> Mobile : 记录操作超时
    Mobile -> Mobile : 尝试重新发送指令
else 车辆状态异常
    CarBT -> Mobile : 返回车辆状态异常信息
    Mobile -> Mobile : 记录异常状态
    Mobile -> Cloud : 上报车辆异常（如果有网络）
else 距离计算异常
    CarBT -> CarBT : 检测到距离计算异常
    CarBT -> CarBT : 使用备用算法重新计算
    CarBT -> Mobile : 发送距离异常通知
    Mobile -> Mobile : 记录异常情况
end

== 安全保障措施 ==

note over Mobile, CarBT
  无感连接安全保障措施:
  1. 所有通信均经过加密
  2. 指令包含时间戳和随机数防重放
  3. 关键操作需要额外验证
  4. 异常情况自动处理
  5. 定期更新会话密钥
  6. 距离阈值严格控制
  7. 多因素验证机制
  8. 用户可随时通过APP设置禁用无感连接
  9. 距离计算采用多重验证机制
end note

@enduml

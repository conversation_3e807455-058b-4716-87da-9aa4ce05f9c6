/**
 * 4G控车WebSocket工具类
 * 用于处理手机端与云平台的4G控车通信
 */

import store from '@/store'
import { getToken } from '@/utils/auth'
import config from '@/config/4GControlConfig.js'

class FourGControlWebSocket {
  constructor() {
    this.ws = null
    this.isConnected = false
    this.reconnectTimer = null
    this.reconnectCount = 0
    this.maxReconnectCount = config.reconnect.maxAttempts
    this.reconnectInterval = config.reconnect.interval
    this.messageHandlers = new Map()
    this.vehicleStatusCallback = null
    this.connectionStatusCallback = null
    this.config = config
    this.heartbeatTimer = null
    this.heartbeatInterval = 30000 // 30秒心跳
    this.lastHeartbeatTime = 0
  }

  /**
   * 连接WebSocket
   * @param {string} serverUrl - 服务器地址，默认使用配置的地址
   */
  connect(serverUrl = null) {
    if (this.isConnected) {
      console.log('4G控车WebSocket已连接，无需重复连接')
      return
    }

    const baseUrl = serverUrl || this.config.websocketUrl
    const userId = store.state.user?.id || 1
    const wsUrl = `${baseUrl}/websocket/message?userId=${userId}`

    console.log('🚗 正在连接4G控车WebSocket:', wsUrl)

    try {
      this.ws = uni.connectSocket({
        url: wsUrl,
        header: {
          Authorization: getToken()
        },
        success: () => {
          console.log('4G控车WebSocket连接请求发送成功')
        },
        fail: (err) => {
          console.error('4G控车WebSocket连接失败:', err)
          this.handleConnectionError()
        }
      })

      // 监听连接打开
      uni.onSocketOpen((res) => {
        console.log('✅ 4G控车WebSocket连接成功:', res)
        this.isConnected = true
        this.reconnectCount = 0

        // 启动心跳
        this.startHeartbeat()

        if (this.connectionStatusCallback) {
          this.connectionStatusCallback(true)
        }
      })

      // 监听消息
      uni.onSocketMessage((res) => {
        this.handleMessage(res.data)
      })

      // 监听连接关闭
      uni.onSocketClose((res) => {
        console.log('❌ 4G控车WebSocket连接关闭:', res)
        this.isConnected = false

        // 停止心跳
        this.stopHeartbeat()

        if (this.connectionStatusCallback) {
          this.connectionStatusCallback(false)
        }

        this.handleReconnect()
      })

      // 监听连接错误
      uni.onSocketError((res) => {
        console.error('❌ 4G控车WebSocket连接错误:', res)
        this.isConnected = false
        
        if (this.connectionStatusCallback) {
          this.connectionStatusCallback(false)
        }
        
        this.handleConnectionError()
      })

    } catch (error) {
      console.error('4G控车WebSocket初始化失败:', error)
      this.handleConnectionError()
    }
  }

  /**
   * 断开连接
   */
  disconnect() {
    if (this.reconnectTimer) {
      clearTimeout(this.reconnectTimer)
      this.reconnectTimer = null
    }
    
    if (this.ws) {
      uni.closeSocket()
      this.ws = null
    }
    
    this.isConnected = false
    this.reconnectCount = 0
  }

  /**
   * 发送4G控车指令
   * @param {string} command - 控车指令 ('unlock' | 'lock')
   * @param {function} callback - 回调函数
   */
  sendControlCommand(command, callback = null) {
    if (!this.isConnected) {
      const error = '4G控车WebSocket未连接，无法发送指令'
      console.error(error)
      if (callback) callback({ success: false, message: error })
      return
    }

    const message = {
      messageType: 4, // 4G控车消息类型
      message: command,
      senderName: store.state.user?.name || '手机用户',
      senderId: store.state.user?.id || 1,
      timestamp: Date.now()
    }

    console.log('🚗 发送4G控车指令:', message)

    try {
      uni.sendSocketMessage({
        data: JSON.stringify(message),
        success: () => {
          console.log('✅ 4G控车指令发送成功')
          if (callback) callback({ success: true, message: '指令发送成功' })
        },
        fail: (err) => {
          console.error('❌ 4G控车指令发送失败:', err)
          if (callback) callback({ success: false, message: '指令发送失败' })
        }
      })
    } catch (error) {
      console.error('4G控车指令发送异常:', error)
      if (callback) callback({ success: false, message: '指令发送异常' })
    }
  }

  /**
   * 发送解锁指令
   */
  sendUnlockCommand(callback = null) {
    this.sendControlCommand('unlock', callback)
  }

  /**
   * 发送闭锁指令
   */
  sendLockCommand(callback = null) {
    this.sendControlCommand('lock', callback)
  }

  /**
   * 查询车辆状态
   */
  queryVehicleStatus(callback = null) {
    if (!this.isConnected) {
      const error = '4G控车WebSocket未连接，无法查询状态'
      console.error(error)
      if (callback) callback({ success: false, message: error })
      return
    }

    const message = {
      messageType: 5, // 状态查询消息类型
      message: 'query_status',
      senderName: store.state.user?.name || '手机用户',
      senderId: store.state.user?.id || 1,
      timestamp: Date.now()
    }

    try {
      uni.sendSocketMessage({
        data: JSON.stringify(message),
        success: () => {
          console.log('✅ 车辆状态查询请求发送成功')
          if (callback) callback({ success: true, message: '查询请求发送成功' })
        },
        fail: (err) => {
          console.error('❌ 车辆状态查询请求发送失败:', err)
          if (callback) callback({ success: false, message: '查询请求发送失败' })
        }
      })
    } catch (error) {
      console.error('车辆状态查询请求发送异常:', error)
      if (callback) callback({ success: false, message: '查询请求发送异常' })
    }
  }

  /**
   * 处理接收到的消息
   */
  handleMessage(data) {
    try {
      const message = JSON.parse(data)
      console.log('📨 收到4G控车消息:', message)

      // 处理4G控车系统消息
      if (message.senderName === '4G控车系统') {
        this.handleControlSystemMessage(message)
      }
      // 处理TBOX设备消息
      else if (message.senderName === 'TBOX') {
        this.handleTboxMessage(message)
      }
      // 处理车辆状态消息
      else if (message.messageType === 5 || message.message?.includes('状态')) {
        this.handleVehicleStatusMessage(message)
      }
      // 处理其他消息
      else {
        this.handleGeneralMessage(message)
      }

    } catch (error) {
      console.error('解析4G控车消息失败:', error, data)
    }
  }

  /**
   * 处理控车系统消息
   */
  handleControlSystemMessage(message) {
    console.log('🚗 控车系统消息:', message.message)
    
    // 触发消息处理器
    if (this.messageHandlers.has('control_system')) {
      this.messageHandlers.get('control_system')(message)
    }
  }

  /**
   * 处理TBOX设备消息
   */
  handleTboxMessage(message) {
    console.log('📡 TBOX设备消息:', message.message)
    
    // 解析车辆状态
    if (message.message.includes('解锁') || message.message.includes('闭锁')) {
      const isLocked = message.message.includes('闭锁')
      
      if (this.vehicleStatusCallback) {
        this.vehicleStatusCallback({
          locked: isLocked,
          message: message.message,
          timestamp: message.timestamp || Date.now()
        })
      }
    }
    
    // 触发消息处理器
    if (this.messageHandlers.has('tbox')) {
      this.messageHandlers.get('tbox')(message)
    }
  }

  /**
   * 处理车辆状态消息
   */
  handleVehicleStatusMessage(message) {
    console.log('🚙 车辆状态消息:', message)
    
    if (this.vehicleStatusCallback) {
      this.vehicleStatusCallback(message)
    }
    
    // 触发消息处理器
    if (this.messageHandlers.has('vehicle_status')) {
      this.messageHandlers.get('vehicle_status')(message)
    }
  }

  /**
   * 处理一般消息
   */
  handleGeneralMessage(message) {
    console.log('📝 一般消息:', message)
    
    // 触发消息处理器
    if (this.messageHandlers.has('general')) {
      this.messageHandlers.get('general')(message)
    }
  }

  /**
   * 处理重连
   */
  handleReconnect() {
    if (this.reconnectCount >= this.maxReconnectCount) {
      console.log('4G控车WebSocket重连次数已达上限，停止重连')
      return
    }

    this.reconnectCount++
    console.log(`4G控车WebSocket准备第${this.reconnectCount}次重连...`)

    this.reconnectTimer = setTimeout(() => {
      this.connect()
    }, this.reconnectInterval)
  }

  /**
   * 处理连接错误
   */
  handleConnectionError() {
    this.isConnected = false
    
    if (this.connectionStatusCallback) {
      this.connectionStatusCallback(false)
    }
    
    this.handleReconnect()
  }

  /**
   * 设置车辆状态回调
   */
  setVehicleStatusCallback(callback) {
    this.vehicleStatusCallback = callback
  }

  /**
   * 设置连接状态回调
   */
  setConnectionStatusCallback(callback) {
    this.connectionStatusCallback = callback
  }

  /**
   * 添加消息处理器
   */
  addMessageHandler(type, handler) {
    this.messageHandlers.set(type, handler)
  }

  /**
   * 移除消息处理器
   */
  removeMessageHandler(type) {
    this.messageHandlers.delete(type)
  }

  /**
   * 获取连接状态
   */
  getConnectionStatus() {
    return this.isConnected
  }

  /**
   * 启动心跳
   */
  startHeartbeat() {
    if (this.heartbeatTimer) {
      clearInterval(this.heartbeatTimer)
    }

    this.heartbeatTimer = setInterval(() => {
      if (this.isConnected) {
        this.sendHeartbeat()
      }
    }, this.heartbeatInterval)
  }

  /**
   * 停止心跳
   */
  stopHeartbeat() {
    if (this.heartbeatTimer) {
      clearInterval(this.heartbeatTimer)
      this.heartbeatTimer = null
    }
  }

  /**
   * 发送心跳消息
   */
  sendHeartbeat() {
    const heartbeatMessage = {
      messageType: 6, // 心跳消息类型
      message: 'heartbeat',
      senderName: store.state.user?.name || '手机用户',
      senderId: store.state.user?.id || 1,
      timestamp: Date.now()
    }

    this.sendMessage(heartbeatMessage)
    this.lastHeartbeatTime = Date.now()
  }

  /**
   * 检查连接健康状态
   */
  checkConnectionHealth() {
    const now = Date.now()
    const timeSinceLastHeartbeat = now - this.lastHeartbeatTime

    // 如果超过2个心跳间隔没有发送心跳，认为连接可能有问题
    if (timeSinceLastHeartbeat > this.heartbeatInterval * 2) {
      console.warn('4G控车连接可能异常，尝试重连')
      this.handleReconnect()
    }
  }
}

// 创建单例实例
const fourGControlWebSocket = new FourGControlWebSocket()

export default fourGControlWebSocket

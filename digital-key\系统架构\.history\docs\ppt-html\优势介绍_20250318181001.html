<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>车载智能钥匙系统 - 优势介绍</title>
    <script src="https://html2canvas.hertzen.com/dist/html2canvas.min.js"></script>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
            font-family: 'PingFang SC', 'Microsoft YaHei', sans-serif;
        }
        
        body {
            background: #f0f0f0;
            display: flex;
            justify-content: center;
            align-items: center;
            min-height: 100vh;
            color: #333;
            padding: 20px;
        }
        
        .ppt-container {
            position: relative;
            width: 1920px;
            height: 1080px;
            box-shadow: 0 0 30px rgba(0, 0, 0, 0.2);
            border: 2px dashed #ccc;
            background-image: linear-gradient(45deg, #ccc 25%, transparent 25%), 
                            linear-gradient(-45deg, #ccc 25%, transparent 25%), 
                            linear-gradient(45deg, transparent 75%, #ccc 75%), 
                            linear-gradient(-45deg, transparent 75%, #ccc 75%);
            background-size: 20px 20px;
            background-position: 0 0, 0 10px, 10px -10px, -10px 0px;
        }
        
        .ppt-label {
            position: absolute;
            top: -40px;
            left: 0;
            background-color: #555;
            color: white;
            padding: 5px 15px;
            border-radius: 4px;
            font-size: 16px;
        }
        
        .copy-button, .download-button {
            position: absolute;
            top: 20px;
            right: 20px;
            background: linear-gradient(135deg, #3498db, #2980b9);
            color: white;
            border: none;
            padding: 12px 25px;
            border-radius: 30px;
            font-size: 18px;
            cursor: pointer;
            z-index: 1000;
            box-shadow: 0 4px 10px rgba(0, 0, 0, 0.2);
            display: flex;
            align-items: center;
            gap: 10px;
            transition: all 0.3s ease;
        }
        
        .download-button {
            right: 230px;
            background: linear-gradient(135deg, #2ecc71, #27ae60);
        }
        
        .slide {
            width: 100%;
            height: 100%;
            background-color: transparent;
            padding: 60px;
            display: flex;
            flex-direction: column;
            overflow: hidden;
            position: relative;
        }
        
        .header {
            text-align: center;
            margin-bottom: 40px;
        }
        
        .header h1 {
            font-size: 42px;
            margin-bottom: 15px;
            color: #2c3e50;
        }
        
        .header .subtitle {
            font-size: 22px;
            color: #7f8c8d;
            font-weight: 300;
        }
        
        .advantages-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 30px;
            padding: 20px;
        }
        
        .advantage-card {
            background: white;
            border-radius: 20px;
            padding: 30px;
            display: flex;
            align-items: flex-start;
            gap: 20px;
            box-shadow: 0 8px 20px rgba(0, 0, 0, 0.07);
            transition: all 0.3s ease;
        }
        
        .advantage-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 12px 25px rgba(0, 0, 0, 0.1);
        }
        
        .advantage-icon {
            width: 60px;
            height: 60px;
            background: linear-gradient(135deg, #3498db, #2980b9);
            border-radius: 15px;
            display: flex;
            align-items: center;
            justify-content: center;
            flex-shrink: 0;
        }
        
        .advantage-icon img {
            width: 30px;
            height: 30px;
            filter: brightness(0) invert(1);
        }
        
        .advantage-content {
            flex: 1;
        }
        
        .advantage-title {
            font-size: 24px;
            font-weight: 600;
            color: #2c3e50;
            margin-bottom: 10px;
        }
        
        .advantage-desc {
            font-size: 16px;
            color: #7f8c8d;
            line-height: 1.6;
        }
        
        .advantage-features {
            margin-top: 15px;
            list-style: none;
        }
        
        .advantage-features li {
            font-size: 14px;
            color: #666;
            margin-bottom: 8px;
            padding-left: 20px;
            position: relative;
        }
        
        .advantage-features li:before {
            content: "•";
            color: #3498db;
            position: absolute;
            left: 0;
        }
        
        .toast {
            position: fixed;
            top: 50px;
            left: 50%;
            transform: translateX(-50%);
            background-color: rgba(0, 0, 0, 0.8);
            color: white;
            padding: 12px 25px;
            border-radius: 30px;
            font-size: 16px;
            z-index: 2000;
            display: none;
            animation: fadeInOut 2s ease;
        }
        
        @keyframes fadeInOut {
            0% { opacity: 0; }
            20% { opacity: 1; }
            80% { opacity: 1; }
            100% { opacity: 0; }
        }
    </style>
</head>
<body>
    <div class="ppt-container" id="pptContainer">
        <div class="ppt-label">PPT尺寸(1920×1080) - 透明背景</div>
        <button class="copy-button" id="copyButton">
            <span class="copy-icon"></span>复制为PPT图片
        </button>
        <button class="download-button" id="downloadButton">
            <span class="download-icon"></span>下载透明PNG
        </button>
        
        <div class="slide" id="slide">
            <div class="header">
                <h1>车载智能钥匙系统优势</h1>
                <div class="subtitle">全方位解决方案，引领智能出行新体验</div>
            </div>
            
            <div class="advantages-grid">
                <!-- 安全性优势 -->
                <div class="advantage-card">
                    <div class="advantage-icon">
                        <img src="data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHZpZXdCb3g9IjAgMCAyNCAyNCI+PHBhdGggZmlsbD0id2hpdGUiIGQ9Ik0xMiwxQzcuMDQsMSAzLDUuMDQgMywxMGMwLDQuOTYgNC4wNCw5IDksOXM5LTQuMDQgOS05QzIxLDUuMDQgMTYuOTYsMSAxMiwxeiBNMTIsMTlDNC4yOCwxOSA0LDEzLjcyIDQsMTJjMC0xLjcyLjI4LTcgOC03czgsNS4yOCA4LDdDMTksMTMuNzIgMTguNzIsMTkgMTIsMTl6Ii8+PC9zdmc+"/>
                    </div>
                    <div class="advantage-content">
                        <div class="advantage-title">全方位安全防护</div>
                        <div class="advantage-desc">采用多重安全机制，确保数字钥匙的安全性</div>
                        <ul class="advantage-features">
                            <li>端到端加密通信，防止数据泄露</li>
                            <li>生物识别认证，确保用户身份安全</li>
                            <li>防重放攻击机制，防止密钥被复制</li>
                            <li>实时安全监控，快速响应异常情况</li>
                        </ul>
                    </div>
                </div>
                
                <!-- 便捷性优势 -->
                <div class="advantage-card">
                    <div class="advantage-icon">
                        <img src="data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHZpZXdCb3g9IjAgMCAyNCAyNCI+PHBhdGggZmlsbD0id2hpdGUiIGQ9Ik0xMiwyQzYuNDgsMiAyLDYuNDggMiwxMnM0LjQ4LDEwIDEwLDEwIDEwLTQuNDggMTAtMTBTMTcuNTIsMiAxMiwyeiBNMTIsMjBjLTQuNDEsMC04LTMuNTktOC04czMuNTktOCA4LTggOCwzLjU5IDgsOFMtMTYuNDEsMjAgMTIsMjB6Ii8+PC9zdmc+"/>
                    </div>
                    <div class="advantage-content">
                        <div class="advantage-title">极致便捷体验</div>
                        <div class="advantage-desc">简化用户操作流程，提供无缝使用体验</div>
                        <ul class="advantage-features">
                            <li>无感解锁，靠近车辆自动识别</li>
                            <li>远程控制，随时随地管理车辆</li>
                            <li>一键分享，轻松授权家人使用</li>
                            <li>离线使用，确保网络异常时可用</li>
                        </ul>
                    </div>
                </div>
                
                <!-- 可靠性优势 -->
                <div class="advantage-card">
                    <div class="advantage-icon">
                        <img src="data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHZpZXdCb3g9IjAgMCAyNCAyNCI+PHBhdGggZmlsbD0id2hpdGUiIGQ9Ik0xMiwyQzYuNDgsMiAyLDYuNDggMiwxMnM0LjQ4LDEwIDEwLDEwIDEwLTQuNDggMTAtMTBTMTcuNTIsMiAxMiwyeiBNMTAsMTdsLTUtNWwxLjQxLTEuNDFMMTAsMTQuMTdsNy41OS03LjU5TDE5LDhsLTksOXoiLz48L3N2Zz4="/>
                    </div>
                    <div class="advantage-content">
                        <div class="advantage-title">高可靠性保障</div>
                        <div class="advantage-desc">多重保障机制，确保系统稳定运行</div>
                        <ul class="advantage-features">
                            <li>双通道通信，确保信号稳定</li>
                            <li>智能容错，自动切换备用方案</li>
                            <li>电池优化，延长设备续航</li>
                            <li>定期维护，持续优化性能</li>
                        </ul>
                    </div>
                </div>
                
                <!-- 扩展性优势 -->
                <div class="advantage-card">
                    <div class="advantage-icon">
                        <img src="data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHZpZXdCb3g9IjAgMCAyNCAyNCI+PHBhdGggZmlsbD0id2hpdGUiIGQ9Ik0xOSwxM2gtNlY3aDZ2NnptLTgsNnYtNkg1djZoNnptOC0xMHY2aDZWN2MwLTEuMS0uOS0yLTItMkgxOXptLTgsMHY2SDVWN2g2ek0xOSw1SDVjLTEuMSwwLTIsMC45LTIsMnY2aDZWNzBoMTBWNzFjMCwxLjEgMC45LDIgMiwyaDZWN0MywyIDIsMC45IDIsMnoiLz48L3N2Zz4="/>
                    </div>
                    <div class="advantage-content">
                        <div class="advantage-title">强大扩展能力</div>
                        <div class="advantage-desc">灵活的系统架构，支持功能持续扩展</div>
                        <ul class="advantage-features">
                            <li>模块化设计，易于功能扩展</li>
                            <li>开放接口，支持第三方集成</li>
                            <li>云端升级，持续优化体验</li>
                            <li>多车型适配，快速部署应用</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="toast" id="toast">操作成功！</div>
    
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const copyButton = document.getElementById('copyButton');
            const downloadButton = document.getElementById('downloadButton');
            const toast = document.getElementById('toast');
            const slide = document.getElementById('slide');
            
            function showToast(message) {
                toast.textContent = message;
                toast.style.display = 'block';
                setTimeout(function() {
                    toast.style.display = 'none';
                }, 2000);
            }
            
            function generateImage() {
                return html2canvas(slide, {
                    scale: 2,
                    useCORS: true,
                    allowTaint: true,
                    backgroundColor: null
                });
            }
            
            copyButton.addEventListener('click', function() {
                showToast("正在处理图片，请稍候...");
                
                generateImage().then(function(canvas) {
                    try {
                        canvas.toBlob(function(blob) {
                            try {
                                const item = new ClipboardItem({ 'image/png': blob });
                                navigator.clipboard.write([item]).then(function() {
                                    showToast("已复制到剪贴板！现在可以粘贴到PPT中");
                                }).catch(function(err) {
                                    showToast("自动复制失败，请使用下载按钮获取图片");
                                    console.error("剪贴板API错误:", err);
                                });
                            } catch (e) {
                                showToast("您的浏览器不支持复制图片，请使用下载按钮");
                                console.error("ClipboardItem错误:", e);
                            }
                        });
                    } catch (e) {
                        showToast("复制功能不可用，请使用下载图片功能");
                        console.error("toBlob错误:", e);
                    }
                }).catch(function(err) {
                    showToast("生成图片失败，请尝试下载或截图");
                    console.error("html2canvas错误:", err);
                });
            });
            
            downloadButton.addEventListener('click', function() {
                showToast("正在生成高清透明PNG图片，请稍候...");
                
                generateImage().then(function(canvas) {
                    const imageUrl = canvas.toDataURL('image/png');
                    const link = document.createElement('a');
                    link.download = "车载智能钥匙系统优势-透明背景.png";
                    link.href = imageUrl;
                    link.click();
                    
                    showToast("高清透明背景PNG已下载");
                }).catch(function(err) {
                    showToast("生成图片失败，请尝试截图");
                    console.error("Error:", err);
                });
            });
        });
    </script>
</body>
</html> 
package com.ruoyi.dk.service.impl;

import java.util.List;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ruoyi.dk.mapper.DkVehicleBluetoothKeysMapper;
import com.ruoyi.dk.domain.DkVehicleBluetoothKeys;
import com.ruoyi.dk.service.IDkVehicleBluetoothKeysService;

/**
 * 蓝牙密钥Service业务层处理
 * 
 * <AUTHOR>
 * @date 2024-05-14
 */
@Service
public class DkVehicleBluetoothKeysServiceImpl implements IDkVehicleBluetoothKeysService 
{
    @Autowired
    private DkVehicleBluetoothKeysMapper dkVehicleBluetoothKeysMapper;

    /**
     * 查询蓝牙密钥
     * 
     * @param userId 蓝牙密钥主键
     * @return 蓝牙密钥
     */
    @Override
    public DkVehicleBluetoothKeys selectDkVehicleBluetoothKeysByUserId(Long userId)
    {
        return dkVehicleBluetoothKeysMapper.selectDkVehicleBluetoothKeysByUserId(userId);
    }

    /**
     * 查询蓝牙密钥列表
     * 
     * @param dkVehicleBluetoothKeys 蓝牙密钥
     * @return 蓝牙密钥
     */
    @Override
    public List<DkVehicleBluetoothKeys> selectDkVehicleBluetoothKeysList(DkVehicleBluetoothKeys dkVehicleBluetoothKeys)
    {
        return dkVehicleBluetoothKeysMapper.selectDkVehicleBluetoothKeysList(dkVehicleBluetoothKeys);
    }

    /**
     * 新增蓝牙密钥
     * 
     * @param dkVehicleBluetoothKeys 蓝牙密钥
     * @return 结果
     */
    @Override
    public int insertDkVehicleBluetoothKeys(DkVehicleBluetoothKeys dkVehicleBluetoothKeys)
    {
        return dkVehicleBluetoothKeysMapper.insertDkVehicleBluetoothKeys(dkVehicleBluetoothKeys);
    }

    /**
     * 修改蓝牙密钥
     * 
     * @param dkVehicleBluetoothKeys 蓝牙密钥
     * @return 结果
     */
    @Override
    public int updateDkVehicleBluetoothKeys(DkVehicleBluetoothKeys dkVehicleBluetoothKeys)
    {
        return dkVehicleBluetoothKeysMapper.updateDkVehicleBluetoothKeys(dkVehicleBluetoothKeys);
    }

    /**
     * 批量删除蓝牙密钥
     * 
     * @param userIds 需要删除的蓝牙密钥主键
     * @return 结果
     */
    @Override
    public int deleteDkVehicleBluetoothKeysByUserIds(Long[] userIds)
    {
        return dkVehicleBluetoothKeysMapper.deleteDkVehicleBluetoothKeysByUserIds(userIds);
    }

    /**
     * 删除蓝牙密钥信息
     * 
     * @param userId 蓝牙密钥主键
     * @return 结果
     */
    @Override
    public int deleteDkVehicleBluetoothKeysByUserId(Long userId)
    {
        return dkVehicleBluetoothKeysMapper.deleteDkVehicleBluetoothKeysByUserId(userId);
    }

    @Override
    public int unbindVinCodes(Long userId, String[] vinCodes) 
    {
        return dkVehicleBluetoothKeysMapper.unbindVinCodes(userId, vinCodes);
    }
}

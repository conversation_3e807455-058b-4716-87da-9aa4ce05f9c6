<template>
  <view class="container">
    <view class="test-section">
      <text class="title">API测试页面</text>
      
      <!-- 测试申请列表API -->
      <view class="test-item">
        <button @click="testGetApplicationList" class="test-btn">测试获取申请列表</button>
        <text class="result">{{ applicationListResult }}</text>
      </view>
      
      <!-- 测试提交申请API -->
      <view class="test-item">
        <button @click="testSubmitApplication" class="test-btn">测试提交申请</button>
        <text class="result">{{ submitResult }}</text>
      </view>
      
      <!-- 测试获取申请详情API -->
      <view class="test-item">
        <button @click="testGetApplicationDetail" class="test-btn">测试获取申请详情</button>
        <text class="result">{{ detailResult }}</text>
      </view>
    </view>
  </view>
</template>

<script>
import { 
  getUserApplicationList, 
  submitUserApplication, 
  getUserApplicationDetail 
} from '@/api/dk'

export default {
  data() {
    return {
      applicationListResult: '未测试',
      submitResult: '未测试',
      detailResult: '未测试'
    }
  },
  
  methods: {
    async testGetApplicationList() {
      try {
        this.applicationListResult = '测试中...'
        const result = await getUserApplicationList()
        this.applicationListResult = `成功: ${JSON.stringify(result)}`
      } catch (error) {
        this.applicationListResult = `失败: ${JSON.stringify(error)}`
      }
    },
    
    async testSubmitApplication() {
      try {
        this.submitResult = '测试中...'
        const testData = {
          realName: '测试用户',
          phone: '13800138000',
          idCard: '110101199001011234',
          drivingLicense: 'D123456789',
          applicationReason: '测试申请'
        }
        const result = await submitUserApplication(testData)
        this.submitResult = `成功: ${JSON.stringify(result)}`
      } catch (error) {
        this.submitResult = `失败: ${JSON.stringify(error)}`
      }
    },
    
    async testGetApplicationDetail() {
      try {
        this.detailResult = '测试中...'
        const result = await getUserApplicationDetail(1)
        this.detailResult = `成功: ${JSON.stringify(result)}`
      } catch (error) {
        this.detailResult = `失败: ${JSON.stringify(error)}`
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.container {
  padding: 40rpx;
}

.title {
  font-size: 36rpx;
  font-weight: bold;
  text-align: center;
  margin-bottom: 40rpx;
}

.test-item {
  margin-bottom: 40rpx;
  padding: 20rpx;
  background: #f5f5f5;
  border-radius: 10rpx;
}

.test-btn {
  background: #007aff;
  color: white;
  border: none;
  padding: 20rpx;
  border-radius: 10rpx;
  width: 100%;
  margin-bottom: 20rpx;
}

.result {
  font-size: 24rpx;
  color: #666;
  word-break: break-all;
  line-height: 1.5;
}
</style>
</template>

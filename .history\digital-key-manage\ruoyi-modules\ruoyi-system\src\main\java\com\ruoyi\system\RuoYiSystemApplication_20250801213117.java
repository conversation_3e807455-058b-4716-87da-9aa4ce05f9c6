package com.ruoyi.system;

import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.autoconfigure.jdbc.DataSourceAutoConfiguration;
import org.springframework.boot.autoconfigure.jdbc.DataSourceTransactionManagerAutoConfiguration;
import org.springframework.boot.autoconfigure.orm.jpa.HibernateJpaAutoConfiguration;
import org.springframework.context.annotation.ComponentScan;
import com.ruoyi.common.security.annotation.EnableCustomConfig;
import com.ruoyi.common.security.annotation.EnableRyFeignClients;
import com.ruoyi.common.swagger.annotation.EnableCustomSwagger2;

/**
 * 系统模块
 *
 * <AUTHOR>
 */
@EnableCustomConfig
@ComponentScan({ "com.ruoyi.*" })
@EnableCustomSwagger2
@EnableRyFeignClients
@SpringBootApplication(exclude = {
                DataSourceAutoConfiguration.class,
                DataSourceTransactionManagerAutoConfiguration.class,
                HibernateJpaAutoConfiguration.class
})
public class RuoYiSystemApplication {
        public static void main(String[] args) {
                SpringApplication.run(RuoYiSystemApplication.class, args);
                System.out.println("(♥◠‿◠)ﾉﾞ  系统模块启动成功   ლ(´ڡ`ლ)ﾞ  \n" +
                                " .-------.       ____     __        \n" +
                                " |  _ _   \\      \\   \\   /  /    \n" +
                                " | ( ' )  |       \\  _. /  '       \n" +
                                " |(_ o _) /        _( )_ .'         \n" +
                                " | (_,_).' __  ___(_ o _)'          \n" +
                                " |  |\\ \\  |  ||   |(_,_)'         \n" +
                                " |  | \\ `'   /|   `-'  /           \n" +
                                " |  |  \\    /  \\      /           \n" +
                                " ''-'   `'-'    `-..-'              ");
        }
}

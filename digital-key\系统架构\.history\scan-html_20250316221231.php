<?php
// 允许跨域请求（如果需要）
header('Access-Control-Allow-Origin: *');
header('Content-Type: application/json');

// 获取当前目录下的所有HTML文件（排除index.html和自身）
$files = glob('*.html');
$htmlFiles = [];

foreach ($files as $file) {
    // 排除index.html和当前页面
    if ($file != 'index.html') {
        // 获取文件名作为ID（去掉.html后缀）
        $id = pathinfo($file, PATHINFO_FILENAME);
        // 使用文件名作为导航菜单中的名称（可以进一步美化）
        $name = str_replace('-', ' ', $id);
        
        $htmlFiles[] = [
            'id' => preg_replace('/[^a-z0-9]/', '', strtolower($id)),
            'name' => $name,
            'file' => $file
        ];
    }
}

// 返回JSON格式的文件列表
echo json_encode($htmlFiles);
?> 
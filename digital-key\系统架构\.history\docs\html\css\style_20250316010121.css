/* 整体样式 */
body {
    font-family: 'Microsoft YaHei', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    color: #333;
    background-color: #f5f7fa;
    margin: 0;
    padding: 0;
    line-height: 1.6;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 20px;
}

/* 顶部导航 */
.top-nav {
    position: sticky;
    top: 0;
    background: linear-gradient(135deg, #1a5276 0%, #3498db 100%);
    color: white;
    padding: 15px 0;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.2);
    z-index: 1000;
}

.top-nav-inner {
    max-width: 1200px;
    margin: 0 auto;
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0 20px;
}

.logo-title {
    display: flex;
    align-items: center;
}

.logo-title h1 {
    margin: 0;
    font-size: 1.5em;
    color: white;
}

.logo-img {
    height: 40px;
    margin-right: 15px;
}

.nav-links {
    display: flex;
    gap: 20px;
}

.nav-links a {
    color: white;
    text-decoration: none;
    font-weight: 500;
    transition: all 0.3s ease;
    padding: 5px 10px;
    border-radius: 5px;
}

.nav-links a:hover {
    background-color: rgba(255, 255, 255, 0.2);
}

/* 部分样式 */
.section {
    margin: 40px 0 80px 0;
    background-color: white;
    border-radius: 10px;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.08);
    overflow: hidden;
}

.section-header {
    background: linear-gradient(135deg, #2c3e50 0%, #3498db 100%);
    color: white;
    padding: 20px 30px;
    border-radius: 10px 10px 0 0;
    position: relative;
}

.section-header h2 {
    margin: 0;
    font-size: 1.8em;
    display: flex;
    align-items: center;
    color: white;
    border: none;
}

.section-header h2 i {
    margin-right: 15px;
    font-size: 1.2em;
    background-color: rgba(255, 255, 255, 0.2);
    width: 40px;
    height: 40px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
}

.section-content {
    padding: 30px;
}

/* 架构图样式 */
.architecture-diagram {
    display: flex;
    justify-content: space-around;
    margin: 30px 0;
    flex-wrap: wrap;
    gap: 30px;
}

.component {
    width: 280px;
    padding: 25px;
    border-radius: 10px;
    text-align: center;
    box-shadow: 0 8px 16px rgba(0, 0, 0, 0.1);
    transition: transform 0.3s ease, box-shadow 0.3s ease;
    position: relative;
    overflow: hidden;
}

.component:hover {
    transform: translateY(-10px);
    box-shadow: 0 12px 24px rgba(0, 0, 0, 0.15);
}

.component::before {
    content: "";
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 6px;
    background: linear-gradient(to right, rgba(52, 152, 219, 0.7), rgba(52, 152, 219, 0.3));
}

.component h3 {
    margin-top: 10px;
    margin-bottom: 20px;
    font-size: 1.6em;
    position: relative;
    display: inline-block;
}

.component h3:after {
    content: "";
    position: absolute;
    bottom: -10px;
    left: 50%;
    transform: translateX(-50%);
    width: 50px;
    height: 3px;
    background-color: #3498db;
}

.component p {
    margin: 15px 0;
    font-size: 1.1em;
    color: #555;
}

.component i {
    font-size: 2.5em;
    color: #3498db;
    margin-bottom: 15px;
    display: block;
}

.mobile {
    background-color: #eef7fe;
    border: 1px solid #d4e6f1;
}

.mobile::before {
    background: linear-gradient(to right, #3498db, #2980b9);
}

.mobile i {
    color: #3498db;
}

.car {
    background-color: #eefbf5;
    border: 1px solid #d5f5e3;
}

.car::before {
    background: linear-gradient(to right, #2ecc71, #27ae60);
}

.car i {
    color: #2ecc71;
}

.cloud {
    background-color: #f5eef8;
    border: 1px solid #ebdef0;
}

.cloud::before {
    background: linear-gradient(to right, #9b59b6, #8e44ad);
}

.cloud i {
    color: #9b59b6;
}

/* 功能列表样式 */
.feature-list {
    display: flex;
    flex-wrap: wrap;
    gap: 20px;
    margin: 30px 0;
}

.feature-item {
    flex: 1;
    min-width: 300px;
    background-color: #f8f9fa;
    border-radius: 8px;
    padding: 20px;
    box-shadow: 0 3px 6px rgba(0, 0, 0, 0.05);
    transition: all 0.3s ease;
    border-left: 4px solid #3498db;
}

.feature-item:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 15px rgba(0, 0, 0, 0.1);
}

.feature-item h3 {
    color: #2c3e50;
    margin-top: 0;
    display: flex;
    align-items: center;
    font-size: 1.3em;
}

.feature-item h3 i {
    margin-right: 10px;
    color: #3498db;
}

.feature-item ul, .feature-item ol {
    margin-top: 15px;
    padding-left: 20px;
}

.feature-item li {
    margin-bottom: 8px;
}

/* 定制列表样式 */
ul.custom-list {
    list-style-type: none;
    padding-left: 10px;
}

ul.custom-list li {
    position: relative;
    padding-left: 25px;
    margin-bottom: 12px;
}

ul.custom-list li:before {
    content: "\f00c";
    font-family: 'Font Awesome 5 Free';
    font-weight: 900;
    position: absolute;
    left: 0;
    color: #3498db;
}

ol.custom-list {
    list-style-type: none;
    counter-reset: item;
    padding-left: 10px;
}

ol.custom-list li {
    position: relative;
    padding-left: 30px;
    margin-bottom: 15px;
    counter-increment: item;
}

ol.custom-list li:before {
    content: counter(item);
    position: absolute;
    left: 0;
    top: -2px;
    background-color: #3498db;
    color: white;
    width: 22px;
    height: 22px;
    border-radius: 50%;
    text-align: center;
    line-height: 22px;
    font-size: 0.9em;
}

/* 流程图样式 */
.process-diagram {
    margin: 40px 0;
    position: relative;
    padding: 20px;
    background-color: white;
    border-radius: 10px;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.process-diagram img {
    max-width: 100%;
    border-radius: 5px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
    transition: transform 0.3s ease;
}

.process-diagram img:hover {
    transform: scale(1.02);
}

.process-diagram figcaption {
    text-align: center;
    margin-top: 15px;
    color: #7f8c8d;
    font-style: italic;
}

/* 标签页样式 */
.tabs {
    margin: 30px 0;
}

.tab-nav {
    display: flex;
    background-color: #f8f9fa;
    border-radius: 8px 8px 0 0;
    overflow: hidden;
}

.tab-btn {
    padding: 12px 20px;
    background-color: #f8f9fa;
    border: none;
    cursor: pointer;
    flex: 1;
    font-size: 1em;
    font-weight: 500;
    transition: all 0.3s ease;
    color: #7f8c8d;
    border-bottom: 3px solid transparent;
}

.tab-btn.active {
    background-color: white;
    color: #3498db;
    border-bottom: 3px solid #3498db;
}

.tab-content {
    display: none;
    padding: 30px;
    background-color: white;
    border-radius: 0 0 8px 8px;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.05);
}

.tab-content.active {
    display: block;
}

/* 卡片样式 */
.cards {
    display: flex;
    flex-wrap: wrap;
    gap: 20px;
    margin: 30px 0;
}

.card {
    flex: 1;
    min-width: 250px;
    background-color: white;
    border-radius: 10px;
    overflow: hidden;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
}

.card:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 16px rgba(0, 0, 0, 0.15);
}

.card-header {
    background-color: #3498db;
    color: white;
    padding: 15px 20px;
}

.card-header h3 {
    margin: 0;
    font-size: 1.3em;
    color: white;
}

.card-body {
    padding: 20px;
}

.card-body p:first-child {
    margin-top: 0;
}

.card-body p:last-child {
    margin-bottom: 0;
}

/* 响应式调整 */
@media (max-width: 768px) {
    .architecture-diagram {
        flex-direction: column;
        align-items: center;
    }
    
    .component {
        width: 90%;
    }
    
    .feature-item {
        min-width: 100%;
    }
    
    .process-diagram {
        padding: 10px;
    }
    
    .tabs {
        margin: 20px 0;
    }
    
    .tab-btn {
        padding: 10px;
        font-size: 0.9em;
    }
    
    .tab-content {
        padding: 20px;
    }
    
    .cards {
        flex-direction: column;
    }
    
    .card {
        min-width: 100%;
    }
}

/* 页脚样式 */
.footer {
    background: linear-gradient(135deg, #1a5276 0%, #3498db 100%);
    color: white;
    padding: 40px 0;
    margin-top: 50px;
}

.footer-content {
    max-width: 1200px;
    margin: 0 auto;
    display: flex;
    flex-wrap: wrap;
    justify-content: space-between;
    padding: 0 20px;
}

.footer-section {
    flex: 1;
    min-width: 250px;
    margin-bottom: 20px;
}

.footer-section h3 {
    color: white;
    margin-top: 0;
    margin-bottom: 20px;
    position: relative;
    padding-bottom: 10px;
}

.footer-section h3:after {
    content: "";
    position: absolute;
    bottom: 0;
    left: 0;
    width: 50px;
    height: 2px;
    background-color: rgba(255, 255, 255, 0.5);
}

.footer-section p, .footer-section a {
    color: rgba(255, 255, 255, 0.8);
}

.footer-section a {
    text-decoration: none;
    transition: color 0.3s ease;
}

.footer-section a:hover {
    color: white;
}

.footer-bottom {
    text-align: center;
    margin-top: 30px;
    padding-top: 20px;
    border-top: 1px solid rgba(255, 255, 255, 0.1);
}

/* 强调文本 */
.highlight {
    color: #e74c3c;
    font-weight: bold;
}

/* 按钮样式 */
.btn {
    display: inline-block;
    padding: 10px 20px;
    background-color: #3498db;
    color: white;
    text-decoration: none;
    border-radius: 5px;
    transition: all 0.3s ease;
    border: none;
    cursor: pointer;
    font-weight: 500;
}

.btn:hover {
    background-color: #2980b9;
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.btn-lg {
    padding: 12px 25px;
    font-size: 1.1em;
}

.btn-secondary {
    background-color: #95a5a6;
}

.btn-secondary:hover {
    background-color: #7f8c8d;
}

/* 动画 */
.fade-in {
    animation: fadeIn 1s ease-in-out;
}

@keyframes fadeIn {
    from { opacity: 0; transform: translateY(20px); }
    to { opacity: 1; transform: translateY(0); }
} 
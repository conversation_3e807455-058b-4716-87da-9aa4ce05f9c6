const TokenKey = 'App-Token'
const VinCodeKey = 'vin-code'
const SecretKey = 'secret-key'

export function getToken() {
  return uni.getStorageSync(TokenKey)
}

export function setToken(token) {
  return uni.setStorageSync(Token<PERSON>ey, token)
}

export function removeToken() {
  return uni.removeStorageSync(TokenKey)
}

export function getVinCode() {
  return uni.getStorageSync(VinCodeKey)
}

export function setVinCode(vinCode) {
  return uni.setStorageSync(VinCodeKey, vinCode)
}

export function removeVinCode() {
  return uni.removeStorageSync(VinCodeKey)
}

export function getSecretKey() {
  return uni.getStorageSync(SecretKey)
}

export function setSecretKey(SecretKeyStr) {
  return uni.setStorageSync(SecretKey, SecretKeyStr)
}

export function removeSecretKey() {
  return uni.removeStorageSync(SecretKey)
}

package com.ruoyi.framework.websocket;

import com.alibaba.fastjson2.JSON;
import com.ruoyi.common.security.utils.SecurityUtils;
import com.ruoyi.dk.domain.DKWebSocketMessage;
import com.ruoyi.framework.tcp.TcpControlService;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import javax.websocket.*;
import javax.websocket.server.ServerEndpoint;
import java.util.List;
import java.util.Map;
import java.util.concurrent.Semaphore;

/**
 * websocket 消息处理
 * 
 * <AUTHOR>
 */
@Component
@ServerEndpoint("/websocket/message")
public class WebSocketServer {

    private SecurityUtils securityUtils;

    private static TcpControlService tcpControlService;

    @PostConstruct
    public void init() {
        // securityUtils = SpringContextProvider.getBean(SecurityUtils.class);
        tcpControlService = SpringContextProvider.getBean(TcpControlService.class);
    }

    /**
     * WebSocketServer 日志控制器
     */
    private static final Logger LOGGER = LoggerFactory.getLogger(WebSocketServer.class);

    /**
     * 默认最多允许同时在线人数100
     */
    public static int socketMaxOnlineCount = 100;

    private static Semaphore socketSemaphore = new Semaphore(socketMaxOnlineCount);

    /**
     * 连接建立成功调用的方法
     */
    @OnOpen
    public void onOpen(Session session) throws Exception {

        // 获取查询参数
        Map<String, List<String>> queryParams = session.getRequestParameterMap();

        if (queryParams.containsKey("userId")) {
            // 将userId存储在Session的UserProperties中
            session.getUserProperties().put("userId", String.valueOf(queryParams.get("userId").get(0)));
        }

        DKWebSocketMessage dkWebSocketMessage = new DKWebSocketMessage();
        dkWebSocketMessage.setSenderName("系统");

        boolean semaphoreFlag = false;
        // 尝试获取信号量
        semaphoreFlag = SemaphoreUtils.tryAcquire(socketSemaphore);
        if (!semaphoreFlag) {
            // 未获取到信号量
            LOGGER.error("\n 当前在线人数超过限制数- {}", socketMaxOnlineCount);
            dkWebSocketMessage.setMessage("当前在线人数超过限制数：" + socketMaxOnlineCount);
            WebSocketUsers.sendMessageToUserByText(session, JSON.toJSONString(dkWebSocketMessage));
            session.close();
        } else {
            // 添加用户
            WebSocketUsers.put(session.getId(), session);
            // 缓存当前用户的session信息ID
            Long userId = null;
            try {
                userId = Long.valueOf(String.valueOf(session.getUserProperties().get("userId")));
            } catch (Exception e) {
                LOGGER.info("\n userId入参格式不正确，无法处理 - {}",
                        session.getId() + "：" + session.getUserProperties() + "，" + e.toString());
            }

            if (ObjectUtils.isEmpty(userId)) {
                return;
            }

            WebSocketUsers.putUserSessions(userId, session.getId());

            LOGGER.info("\n 建立连接 - {}", session);
            LOGGER.info("\n 当前人数 - {}", WebSocketUsers.getUsers().size());

            dkWebSocketMessage.setMessage("连接成功");
            WebSocketUsers.sendMessageToUserByText(session, JSON.toJSONString(dkWebSocketMessage));
        }
    }

    /**
     * 连接关闭时处理
     */
    @OnClose
    public void onClose(Session session) {
        LOGGER.info("\n 关闭连接 - {}", session);
        // 移除用户
        boolean removeFlag = WebSocketUsers.remove(session.getId());
        if (!removeFlag) {
            // 获取到信号量则需释放
            SemaphoreUtils.release(socketSemaphore);
        }
    }

    /**
     * 抛出异常时处理
     */
    @OnError
    public void onError(Session session, Throwable exception) throws Exception {
        if (session.isOpen()) {
            // 关闭连接
            session.close();
        }
        String sessionId = session.getId();
        LOGGER.info("\n 连接异常 - {}", sessionId);
        LOGGER.info("\n 异常信息 - {}", exception);
        // 移出用户
        WebSocketUsers.remove(sessionId);

        // 缓存当前用户的session信息ID
        String userId = String.valueOf(session.getUserProperties().get("userId"));

        if (ObjectUtils.isNotEmpty(userId)) {
            // 移出用户Session映射
            WebSocketUsers.removeUserSessions(userId);
        }

        // 获取到信号量则需释放
        SemaphoreUtils.release(socketSemaphore);
    }

    /**
     * 服务器接收到客户端消息时调用的方法
     */
    @OnMessage
    public void onMessage(String message, Session session) {
        if (StringUtils.isBlank(message)) {
            LOGGER.info("\n 消息无内容，不需要处理 - {}", session.getId());
            return;
        }

        // String msg = message.replace("你", "我").replace("吗", "");
        // WebSocketUsers.sendMessageToUserByText(session, msg);

        DKWebSocketMessage dkWebSocketMessage = JSON.parseObject(message, DKWebSocketMessage.class);

        if (ObjectUtils.isEmpty(dkWebSocketMessage) || StringUtils.isBlank(dkWebSocketMessage.getMessage())) {
            LOGGER.info("\n 消息内容格式不正确，无法处理 - {}", session.getId() + "：" + message);
            return;
        }

        // 检查是否为4G控车请求
        if (handle4GControlRequest(dkWebSocketMessage, session)) {
            return; // 如果是4G控车请求，处理完成后直接返回
        }

        // 获取当前分享人的session信息，发送确认通知给分享人
        Session shareSession = WebSocketUsers.getSessionByUserId(dkWebSocketMessage.getRecipientId());

        if (ObjectUtils.isEmpty(shareSession)) {
            LOGGER.info("\n 分享人通讯异常，无法处理 - {}", session.getId());
            dkWebSocketMessage.setSenderId(null);
            dkWebSocketMessage.setSenderName("系统");
            dkWebSocketMessage.setMessage("分享人通讯异常，请稍候再试...");
            WebSocketUsers.sendMessageToUserByText(session, JSON.toJSONString(dkWebSocketMessage));
            return;
        }

        WebSocketUsers.sendMessageToUserByText(shareSession, JSON.toJSONString(dkWebSocketMessage));
    }
}

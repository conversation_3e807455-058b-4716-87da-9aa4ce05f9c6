<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.dk.mapper.DkVehicleBluetoothKeysMapper">
    
    <resultMap type="DkVehicleBluetoothKeys" id="DkVehicleBluetoothKeysResult">
        <result property="userId"    column="user_id"    />
        <result property="userName"    column="user_name"    />
        <result property="vehicleVin"    column="vehicle_vin"    />
        <result property="bluetoothTempKey"    column="bluetooth_temp_key"    />
        <result property="bluetoothTempKeyTime"    column="bluetooth_temp_key_time"    />
        <result property="bluetoothPermKey"    column="bluetooth_perm_key"    />
        <result property="bluetoothPermKeyTime"    column="bluetooth_perm_key_time"    />
    </resultMap>

    <sql id="selectDkVehicleBluetoothKeysVo">
        select user_id, vehicle_vin, bluetooth_temp_key, bluetooth_temp_key_time, bluetooth_perm_key, bluetooth_perm_key_time from dk_vehicle_bluetooth_keys
    </sql>

    <select id="selectDkVehicleBluetoothKeysList" parameterType="DkVehicleBluetoothKeys" resultMap="DkVehicleBluetoothKeysResult">
        SELECT
            t1.user_id,
            t2.user_name,
            t1.vehicle_vin,
            t1.bluetooth_temp_key,
            t1.bluetooth_temp_key_time,
            t1.bluetooth_perm_key,
            t1.bluetooth_perm_key_time
        FROM
            dk_vehicle_bluetooth_keys t1
        INNER JOIN sys_user t2 ON t2.user_id = t1.user_id
        <where>  
            <if test="userName != null "> and t2.user_name like concat('%', #{userName}, '%')</if>
            <if test="vehicleVin != null  and vehicleVin != ''"> and t1.vehicle_vin = #{vehicleVin}</if>
            <if test="bluetoothTempKey != null  and bluetoothTempKey != ''"> and t1.bluetooth_temp_key = #{bluetoothTempKey}</if>
            <if test="bluetoothPermKey != null  and bluetoothPermKey != ''"> and t1.bluetooth_perm_key = #{bluetoothPermKey}</if>
        </where>
    </select>
    
    <select id="selectDkVehicleBluetoothKeysByUserId" parameterType="Long" resultMap="DkVehicleBluetoothKeysResult">
        <include refid="selectDkVehicleBluetoothKeysVo"/>
        where user_id = #{userId}
    </select>
        
    <insert id="insertDkVehicleBluetoothKeys" parameterType="DkVehicleBluetoothKeys">
        insert into dk_vehicle_bluetooth_keys
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="userId != null">user_id,</if>
            <if test="vehicleVin != null">vehicle_vin,</if>
            <if test="bluetoothTempKey != null">bluetooth_temp_key,</if>
            <if test="bluetoothTempKeyTime != null">bluetooth_temp_key_time,</if>
            <if test="bluetoothPermKey != null">bluetooth_perm_key,</if>
            <if test="bluetoothPermKeyTime != null">bluetooth_perm_key_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="userId != null">#{userId},</if>
            <if test="vehicleVin != null">#{vehicleVin},</if>
            <if test="bluetoothTempKey != null">#{bluetoothTempKey},</if>
            <if test="bluetoothTempKeyTime != null">#{bluetoothTempKeyTime},</if>
            <if test="bluetoothPermKey != null">#{bluetoothPermKey},</if>
            <if test="bluetoothPermKeyTime != null">#{bluetoothPermKeyTime},</if>
         </trim>
    </insert>

    <update id="updateDkVehicleBluetoothKeys" parameterType="DkVehicleBluetoothKeys">
        update dk_vehicle_bluetooth_keys
        <trim prefix="SET" suffixOverrides=",">
            <if test="vehicleVin != null">vehicle_vin = #{vehicleVin},</if>
            <if test="bluetoothTempKey != null">bluetooth_temp_key = #{bluetoothTempKey},</if>
            <if test="bluetoothTempKeyTime != null">bluetooth_temp_key_time = #{bluetoothTempKeyTime},</if>
            <if test="bluetoothPermKey != null">bluetooth_perm_key = #{bluetoothPermKey},</if>
            <if test="bluetoothPermKeyTime != null">bluetooth_perm_key_time = #{bluetoothPermKeyTime},</if>
        </trim>
        where user_id = #{userId}
    </update>

    <delete id="deleteDkVehicleBluetoothKeysByUserId" parameterType="Long">
        delete from dk_vehicle_bluetooth_keys where user_id = #{userId}
    </delete>

    <delete id="deleteDkVehicleBluetoothKeysByUserIds" parameterType="String">
        delete from dk_vehicle_bluetooth_keys where user_id in 
        <foreach item="userId" collection="array" open="(" separator="," close=")">
            #{userId}
        </foreach>
    </delete>

    <delete id="unbindVinCodes">
        DELETE FROM dk_vehicle_bluetooth_keys
        WHERE user_id = #{userId}
        AND vehicle_vin IN
        <foreach item="vin" index="index" collection="vinCodes" open="(" separator="," close=")">
            #{vin}
        </foreach>
    </delete>
</mapper>
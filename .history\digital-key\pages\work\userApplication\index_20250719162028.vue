<template>
  <view class="container">
    <!-- 自定义导航栏 -->
    <cu-custom bgColor="bg-gradual-blue" :isBack="true">
      <block slot="backText">返回</block>
      <block slot="content">申请数字钥匙</block>
    </cu-custom>

    <!-- 申请表单 -->
    <view class="form-container">
      <!-- 步骤指示器 -->
      <view class="steps-container">
        <view class="step" :class="{ active: currentStep >= 1 }">
          <view class="step-number">1</view>
          <view class="step-text">个人信息</view>
        </view>
        <view class="step-line" :class="{ active: currentStep >= 2 }"></view>
        <view class="step" :class="{ active: currentStep >= 2 }">
          <view class="step-number">2</view>
          <view class="step-text">证件上传</view>
        </view>
        <view class="step-line" :class="{ active: currentStep >= 3 }"></view>
        <view class="step" :class="{ active: currentStep >= 3 }">
          <view class="step-number">3</view>
          <view class="step-text">申请原因</view>
        </view>
      </view>

      <!-- 步骤1: 个人信息 -->
      <view v-if="currentStep === 1" class="step-content">
        <view class="form-title">请填写个人信息</view>
        
        <view class="form-item">
          <view class="form-label">真实姓名 <text class="required">*</text></view>
          <uni-easyinput 
            v-model="formData.realName" 
            placeholder="请输入真实姓名"
            :clearable="true"
          />
        </view>

        <view class="form-item">
          <view class="form-label">手机号码 <text class="required">*</text></view>
          <uni-easyinput 
            v-model="formData.phone" 
            placeholder="请输入手机号码"
            type="number"
            :clearable="true"
          />
        </view>

        <view class="form-item">
          <view class="form-label">身份证号 <text class="required">*</text></view>
          <uni-easyinput 
            v-model="formData.idCard" 
            placeholder="请输入身份证号"
            :clearable="true"
          />
        </view>

        <view class="form-item">
          <view class="form-label">驾驶证号</view>
          <uni-easyinput 
            v-model="formData.drivingLicense" 
            placeholder="请输入驾驶证号"
            :clearable="true"
          />
        </view>

        <view class="btn-container">
          <button class="cu-btn bg-blue lg round" @click="nextStep">下一步</button>
        </view>
      </view>

      <!-- 步骤2: 证件上传 -->
      <view v-if="currentStep === 2" class="step-content">
        <view class="form-title">请上传证件照片</view>
        
        <!-- 身份证正面 -->
        <view class="upload-item">
          <view class="upload-label">身份证正面 <text class="required">*</text></view>
          <view class="upload-container" @click="chooseImage('idCardFront')">
            <image v-if="formData.idCardFrontUrl" :src="formData.idCardFrontUrl" class="upload-image" />
            <view v-else class="upload-placeholder">
              <text class="cuIcon-add text-gray"></text>
              <text class="upload-text">点击上传身份证正面</text>
            </view>
          </view>
        </view>

        <!-- 身份证背面 -->
        <view class="upload-item">
          <view class="upload-label">身份证背面 <text class="required">*</text></view>
          <view class="upload-container" @click="chooseImage('idCardBack')">
            <image v-if="formData.idCardBackUrl" :src="formData.idCardBackUrl" class="upload-image" />
            <view v-else class="upload-placeholder">
              <text class="cuIcon-add text-gray"></text>
              <text class="upload-text">点击上传身份证背面</text>
            </view>
          </view>
        </view>

        <!-- 驾驶证 -->
        <view class="upload-item">
          <view class="upload-label">驾驶证</view>
          <view class="upload-container" @click="chooseImage('drivingLicense')">
            <image v-if="formData.drivingLicenseUrl" :src="formData.drivingLicenseUrl" class="upload-image" />
            <view v-else class="upload-placeholder">
              <text class="cuIcon-add text-gray"></text>
              <text class="upload-text">点击上传驾驶证</text>
            </view>
          </view>
        </view>

        <view class="btn-container">
          <button class="cu-btn line-blue lg round margin-right-sm" @click="prevStep">上一步</button>
          <button class="cu-btn bg-blue lg round" @click="nextStep">下一步</button>
        </view>
      </view>

      <!-- 步骤3: 申请原因 -->
      <view v-if="currentStep === 3" class="step-content">
        <view class="form-title">请填写申请原因</view>
        
        <view class="form-item">
          <view class="form-label">申请原因 <text class="required">*</text></view>
          <textarea 
            v-model="formData.applicationReason" 
            placeholder="请详细说明申请数字钥匙的原因，如租车、临时用车等"
            class="textarea"
            maxlength="500"
          />
          <view class="char-count">{{ formData.applicationReason.length }}/500</view>
        </view>

        <view class="btn-container">
          <button class="cu-btn line-blue lg round margin-right-sm" @click="prevStep">上一步</button>
          <button class="cu-btn bg-green lg round" @click="submitApplication" :loading="submitting">提交申请</button>
        </view>
      </view>
    </view>
  </view>
</template>

<script>
import { submitUserApplication, uploadCertificate } from '@/api/dk'
import upload from '@/utils/upload'

export default {
  created() {
    // 将upload工具挂载到实例上
    this.$upload = upload
  },

  data() {
    return {
      currentStep: 1,
      submitting: false,
      formData: {
        realName: '',
        phone: '',
        idCard: '',
        drivingLicense: '',
        idCardFrontUrl: '',
        idCardBackUrl: '',
        drivingLicenseUrl: '',
        applicationReason: ''
      }
    }
  },
  methods: {
    // 下一步
    nextStep() {
      if (this.validateCurrentStep()) {
        if (this.currentStep < 3) {
          this.currentStep++
        }
      }
    },
    
    // 上一步
    prevStep() {
      if (this.currentStep > 1) {
        this.currentStep--
      }
    },
    
    // 验证当前步骤
    validateCurrentStep() {
      if (this.currentStep === 1) {
        if (!this.formData.realName.trim()) {
          uni.showToast({ title: '请输入真实姓名', icon: 'none' })
          return false
        }
        if (!this.formData.phone.trim()) {
          uni.showToast({ title: '请输入手机号码', icon: 'none' })
          return false
        }
        if (!/^1[3-9]\d{9}$/.test(this.formData.phone)) {
          uni.showToast({ title: '请输入正确的手机号码', icon: 'none' })
          return false
        }
        if (!this.formData.idCard.trim()) {
          uni.showToast({ title: '请输入身份证号', icon: 'none' })
          return false
        }
        if (!/^[1-9]\d{5}(18|19|20)\d{2}((0[1-9])|(1[0-2]))(([0-2][1-9])|10|20|30|31)\d{3}[0-9Xx]$/.test(this.formData.idCard)) {
          uni.showToast({ title: '请输入正确的身份证号', icon: 'none' })
          return false
        }
      } else if (this.currentStep === 2) {
        if (!this.formData.idCardFrontUrl) {
          uni.showToast({ title: '请上传身份证正面', icon: 'none' })
          return false
        }
        if (!this.formData.idCardBackUrl) {
          uni.showToast({ title: '请上传身份证背面', icon: 'none' })
          return false
        }
      } else if (this.currentStep === 3) {
        if (!this.formData.applicationReason.trim()) {
          uni.showToast({ title: '请填写申请原因', icon: 'none' })
          return false
        }
      }
      return true
    },
    
    // 选择图片
    chooseImage(type) {
      uni.chooseImage({
        count: 1,
        sizeType: ['compressed'],
        sourceType: ['camera', 'album'],
        success: (res) => {
          this.uploadImage(res.tempFilePaths[0], type)
        }
      })
    },
    
    // 上传图片
    async uploadImage(filePath, type) {
      uni.showLoading({ title: '上传中...' })

      try {
        // 调用实际的上传接口
        const uploadResult = await this.$upload({
          url: '/upload',
          filePath: filePath,
          name: 'file'
        })

        uni.hideLoading()

        if (uploadResult && uploadResult.data && uploadResult.data.url) {
          const serverUrl = uploadResult.data.url
          if (type === 'idCardFront') {
            this.formData.idCardFrontUrl = serverUrl
          } else if (type === 'idCardBack') {
            this.formData.idCardBackUrl = serverUrl
          } else if (type === 'drivingLicense') {
            this.formData.drivingLicenseUrl = serverUrl
          }
          uni.showToast({ title: '上传成功', icon: 'success' })
        } else {
          throw new Error('上传失败，服务器返回异常')
        }
      } catch (error) {
        uni.hideLoading()
        console.error('上传失败:', error)
        uni.showToast({
          title: '上传失败，请重试',
          icon: 'none'
        })
      }
    },
    
    // 提交申请
    async submitApplication() {
      if (!this.validateCurrentStep()) {
        return
      }
      
      this.submitting = true
      
      try {
        const result = await submitUserApplication(this.formData)
        
        uni.showToast({ 
          title: '申请提交成功', 
          icon: 'success',
          duration: 2000
        })
        
        // 延迟跳转到申请状态页面
        setTimeout(() => {
          uni.redirectTo({
            url: '/pages/work/applicationStatus/index'
          })
        }, 2000)
        
      } catch (error) {
        console.error('提交申请失败:', error)
        uni.showToast({ 
          title: '提交失败，请重试', 
          icon: 'none' 
        })
      } finally {
        this.submitting = false
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.container {
  min-height: 100vh;
  background-color: #f5f5f5;
}

.form-container {
  padding: 20rpx;
}

/* 步骤指示器样式 */
.steps-container {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 40rpx 20rpx;
  background: white;
  margin-bottom: 20rpx;
  border-radius: 20rpx;
}

.step {
  display: flex;
  flex-direction: column;
  align-items: center;

  .step-number {
    width: 60rpx;
    height: 60rpx;
    border-radius: 50%;
    background: #e5e5e5;
    color: #999;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 28rpx;
    font-weight: bold;
    margin-bottom: 10rpx;
  }

  .step-text {
    font-size: 24rpx;
    color: #999;
  }

  &.active {
    .step-number {
      background: #007aff;
      color: white;
    }

    .step-text {
      color: #007aff;
    }
  }
}

.step-line {
  flex: 1;
  height: 2rpx;
  background: #e5e5e5;
  margin: 0 20rpx;

  &.active {
    background: #007aff;
  }
}

/* 表单内容样式 */
.step-content {
  background: white;
  border-radius: 20rpx;
  padding: 40rpx;
}

.form-title {
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
  text-align: center;
  margin-bottom: 40rpx;
}

.form-item {
  margin-bottom: 30rpx;
}

.form-label {
  font-size: 28rpx;
  color: #333;
  margin-bottom: 15rpx;

  .required {
    color: #ff4757;
  }
}

/* 上传组件样式 */
.upload-item {
  margin-bottom: 30rpx;
}

.upload-label {
  font-size: 28rpx;
  color: #333;
  margin-bottom: 15rpx;

  .required {
    color: #ff4757;
  }
}

.upload-container {
  width: 100%;
  height: 300rpx;
  border: 2rpx dashed #ddd;
  border-radius: 10rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #fafafa;
}

.upload-image {
  width: 100%;
  height: 100%;
  border-radius: 10rpx;
}

.upload-placeholder {
  display: flex;
  flex-direction: column;
  align-items: center;
  color: #999;

  .cuIcon-add {
    font-size: 60rpx;
    margin-bottom: 10rpx;
  }

  .upload-text {
    font-size: 24rpx;
  }
}

/* 文本域样式 */
.textarea {
  width: 100%;
  min-height: 200rpx;
  padding: 20rpx;
  border: 2rpx solid #e5e5e5;
  border-radius: 10rpx;
  font-size: 28rpx;
  line-height: 1.5;
  resize: none;
}

.char-count {
  text-align: right;
  font-size: 24rpx;
  color: #999;
  margin-top: 10rpx;
}

/* 按钮容器样式 */
.btn-container {
  display: flex;
  justify-content: center;
  margin-top: 60rpx;

  .cu-btn {
    flex: 1;
    max-width: 300rpx;
  }
}
</style>

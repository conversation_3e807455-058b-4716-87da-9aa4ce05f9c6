<template>
  <view class="container bg-cls">
    <!-- 自定义导航栏 -->
    <view class="custom-navbar">
      <text class="navbar-title">零感数字钥匙</text>
    </view>

    <!-- 页面内容 -->
    <view class="page-content bg-img">
      <image v-if="connected" class="connected_img connected dynamic-style-after-connection" src="~@/static/icons/icon_logo_connected.png" @click="testVersionSign = !testVersionSign"/>
      <image v-else :class="['connected_img', { 'dynamic-style-in-connection': connecting }]" src="~@/static/icons/icon_logo_not_connected.png" @click="testVersionSign = !testVersionSign"/>
      <view v-show="!testVersionSign">
        <view class="hid-cls">{{ dynamicConnInfo }}{{ connInfo }}</view>
        <view class="picker-container">
          <picker mode="selector" :range="selectedVinCodeList.map(item => item.lable)" @change="onChange">
            <view class="picker">
              {{ selectedVinLable }}
            </view>
          </picker>
        </view>
        <view v-if="!secretKey">
          <view class="ble-operation_prompt-cls">在下面输入VIN码绑定你的车辆</view>
          <view class="input-item flex align-center">
            <input v-model="vinCode" class="input" type="text" placeholder="请输入车辆VIN码" maxlength="30" />
          </view>
          <view class="vin-code-prompt-cls flex align-center">
            车辆VIN码也叫车架号、车辆识别码，是一辆车的唯一标识，相当于该车的"身份证号码"，它由大写字母和数字组成，长度为17位
          </view>
          <view>
            <button @click="getSecretKeyByVin" class="binding-vehicles-btn-cls cu-btn block lg round">绑定车辆</button>
          </view>
        </view>
        <view v-else>
          <!-- 添加车内检测人员图标，根据personInCar变量显示不同状态 -->
          <view class="ble-status-container">
            <view class="status-icons-container">
              <view :class="'iconfont ' + (connected ? 'icon-bleConnected' : 'icon-bleNotConnected') + ' icon ble-connt-cls'">{{ connected ? '钥匙已连接' : connecting ? '...连接中...' : '钥匙未连接' }}</view>
            </view>
          </view>

          <!-- 权限状态显示 -->
          <view class="permission-status-container">
            <view class="permission-item" :class="{ 'permission-restricted': keyPermission.isRestricted }">
              <text class="permission-icon">🔑</text>
              <view class="permission-info">
                <text class="permission-status">{{ keyPermission.isRestricted ? '权限受限' : '权限正常' }}</text>
                <text v-if="keyPermission.expireTime" class="permission-expire">
                  有效期至：{{ formatExpireTime(keyPermission.expireTime) }}
                </text>
              </view>
              <text class="permission-indicator" :class="{ 'restricted': keyPermission.isRestricted }">
                {{ keyPermission.isRestricted ? '⚠️' : '✅' }}
              </text>
            </view>

            <!-- 权限受限时的提示信息 -->
            <view v-if="keyPermission.isRestricted" class="restriction-notice">
              <text class="notice-text">{{ keyPermission.restrictionReason || '您的使用权限已被限制，请联系管理员' }}</text>
            </view>

            <!-- 即将过期提醒 -->
            <view v-if="keyPermission.willExpireSoon" class="expire-warning">
              <text class="warning-text">⏰ 钥匙即将过期，请及时续期</text>
            </view>
          </view>

          <!-- 添加有人警告显示 -->
          <view v-if="personInCar" class="person-warning-container" :class="{'warning-flash': personInCar}">
            <text class="warning-icon">⚠️</text>
            <view class="warning-text">车内有人警告</view>
          </view>
  
          <view v-if="!connected">
            <view class="ble-operation_prompt-cls">可以通过点击下方按钮控制车辆</view>
            <view hover-class="iconfont-active" class="iconfont icon-goConnectedBle icon go-connt-ble-cls" @tap="silentConnByVinCode">手动连接</view>
          </view>
  
          <view class="grid col-4 operation-btn-cls">
            <view class="margin-tb-sm text-center">
              <view :class="'iconfont icon ' + (startSign ? 'icon-startPress' : 'icon-startNor')" @tap="sendToBle(startSign ? '0203000105' : '0203000104'); startSign = !startSign">启动</view>
            </view>
            <view class="margin-tb-sm text-center">
              <view :class="'iconfont icon ' + (lockSign ? 'icon-lockPress' : 'icon-lockNor')" @tap="sendToBle(lockSign ? '0203000102' : '0203000101'); lockSign = !lockSign">门锁</view>
            </view>
            <view class="margin-tb-sm text-center">
              <view hover-class="icon-trunkPress" :class="'iconfont icon ' + (trunkSign ? 'icon-trunkPress' : 'icon-trunkNor')" @tap="sendToBle(trunkSign ? '0203000152' : '0203000151')">后备箱</view>
            </view>
            <view class="margin-tb-sm text-center">
              <view hover-class="icon-findCarPress" class="iconfont icon-findCarNor icon" @tap="sendToBle('0203000103')">寻车</view>
            </view>
          </view>
  
          <view class="grid col-4 operation-btn-cls">
            <view class="margin-tb-sm text-center">
              <!-- hover-class="icon-carWindowsPress" -->
              <view :class="'iconfont icon ' + (carWindowsSign ? 'icon-carWindowsPress' : 'icon-carWindowsNor')" @tap="carWindowsSign = !carWindowsSign">车窗</view>
            </view>
            <view class="margin-tb-sm text-center">
              <view hover-class="icon-defrostPress" class="iconfont icon-defrostNor icon" >除霜</view>
            </view>
          </view>
        </view>
      </view>

      <view  v-show="testVersionSign">
        <scroll-view scroll-y style="height: 100vh">
          <!-- 正在静默连接中的低功耗蓝牙设备信息 -->
          <view class="dynamicConnInfoCls">{{ dynamicConnInfo }}</view>
          <!-- 已连接的低功耗蓝牙设备信息 -->
          <view class="connInfoCls">{{ connInfo }}</view>
          <!-- <view class="connInfoCls">
            {{ serviceDetails }}
          </view> -->
  
          <view class="grid col-4 padding-sm">
            <view class="margin-tb-sm text-center">
              <button class="cu-btn text-yellow line-yellow">
                主：{{ mainRssi }}
              </button>
            </view>
            <view class="margin-tb-sm text-center">
              <button class="cu-btn text-yellow line-yellow">
                左：{{ leftRssi }}
              </button>
            </view>
            <view class="margin-tb-sm text-center">
              <button class="cu-btn text-yellow line-yellow">
                右：{{ rightRssi }}
              </button>
            </view>
            <view class="margin-tb-sm text-center">
              <button class="cu-btn text-yellow line-yellow">
                后：{{ rearRssi }}
              </button>
            </view>
          </view>
  
          <view v-if="secretKey" class="flex p-xs margin-bottom-sm mb-sm">
            <!-- <view class="flex-sub margin-xs radius"></view>
            <view class="margin-tb-sm text-center">
              <button class="cu-btn text-yellow line-yellow">
                {{ vinCode }}
              </button>
            </view>
            <view class="margin-tb-sm text-center">
              <button class="cu-btn text-yellow line-yellow" @click="
            !connected && !hasConnectingDevice ? silentC3onnByVinCode() : ''
            ">
                {{ !connected && !hasConnectingDevice ? '尝试重连' : '已连接' }}
              </button>
            </view> -->
  
            <view class="flex-sub margin-xs radius"></view>
            <view class="flex-treble margin-xs radius">
              <button class="cu-btn text-yellow line-yellow">
                {{ vinCode }}
              </button>
            </view>
            <view class="margin-tb-sm text-center">
              <button
                class="cu-btn text-yellow line-yellow"
                @click="
                  !connected && !hasConnectingDevice ? silentConnByVinCode() : ''
                "
              >
                {{ hasConnectingDevice ? "静默连接中" : !connected ? "尝试重连" : "已连接" }}
              </button>
            </view>
          </view>
  
          <view v-else class="flex p-xs margin-bottom-sm mb-sm">
            <view class="flex-sub margin-xs radius">
              <view @tap="navigateToCoUIPage">
                <image
                  class="coui-icon"
                  src="./../../../static/colorUiLogo.png"
                ></image>
              </view>
            </view>
            <view class="flex-treble margin-xs radius">
              <!-- 驾驶证VIN码输入框 -->
              <uni-easyinput
                :styles="{ 'background-color': 'transparent' }"
                v-model="vinCode"
                placeholder="驾驶证VIN码"
              />
            </view>
            <view class="margin-tb-sm text-center">
              <button
                class="cu-btn round text-white line-white"
                @click="getSecretKeyByVin"
                hover-class="btn_hover"
              >
                绑定车辆
              </button>
            </view>
          </view>
  
          <view v-if="secretKey">
            <!-- <view class="flex p-xs margin-bottom-sm mb-sm">
              <view class="flex-sub margin-xs radius">
                <view @tap="navigateToCoUIPage">
                  <image class="coui-icon" src="./../../../static/colorUiLogo.png"></image>
                </view>
              </view>
              <view class="flex-treble margin-xs radius">
                <uni-easyinput :styles="{ 'background-color': 'transparent' }" v-model="bleUUID" placeholder="主服务UUID" />
              </view>
              <view class="margin-tb-sm text-center" :key="index">
                <button class="cu-btn round text-white line-white" @click="searchBLE" hover-class="btn_hover">
                  搜索蓝牙
                </button>
              </view>
            </view> -->
  
            <view class="flex p-xs margin-bottom-sm mb-sm">
              <view class="flex-sub margin-xs radius">
                <view @tap="navigateToCoUIPage">
                  <image
                    class="coui-icon"
                    src="./../../../static/colorUiLogo.png"
                  ></image>
                </view>
              </view>
              <view class="flex-treble margin-xs radius">
                <!-- 蓝牙通信UUID输入框 -->
                <!-- 蓝牙通信的输入框 -->
                <uni-easyinput
                  v-model="bleSendInfo"
                  placeholder="填写指令，再点击发送"
                  :styles="{ 'background-color': 'transparent' }"
                />
              </view>
              <view class="margin-tb-sm text-center">
                <button
                  class="cu-btn round text-black line-black"
                  @click="sendToBle(null)"
                  hover-class="btn_hover"
                >
                  发送指令
                </button>
              </view>
            </view>
  
            <view class="grid col-3 padding-sm">
              <view class="margin-tb-sm text-center">
                <button
                  class="cu-btn round bg-orange"
                  @tap="sendToBle('0203000101')"
                >
                  解锁
                </button>
              </view>
              <view class="margin-tb-sm text-center">
                <button
                  class="cu-btn round bg-orange"
                  @tap="sendToBle('0203000102')"
                >
                  闭锁
                </button>
              </view>
              <view class="margin-tb-sm text-center">
                <button
                  class="cu-btn round bg-orange"
                  @tap="sendToBle('0203000103')"
                >
                  寻车
                </button>
              </view>
              <view class="margin-tb-sm text-center">
                <button
                  class="cu-btn round bg-orange"
                  @tap="sendToBle('0203000104')"
                >
                  启动
                </button>
              </view>
              <view class="margin-tb-sm text-center">
                <button
                  class="cu-btn round bg-orange"
                  @tap="sendToBle('0205000102')"
                >
                  车门状态查询
                </button>
              </view>
              <view class="margin-tb-sm text-center">
                <button
                  class="cu-btn round bg-orange"
                  @tap="sendToBle('0205000103')"
                >
                  引擎状态查询
                </button>
              </view>
              <view class="margin-tb-sm text-center">
                <button class="cu-btn round bg-orange" @tap="sendToBle('0203000111')">
                  主驾解锁
                </button>
              </view>
              <view class="margin-tb-sm text-center">
                <button class="cu-btn round bg-orange" @tap="sendToBle('0203000112')">
                  主驾闭锁
                </button>
              </view>
              <view class="margin-tb-sm text-center">
                <button class="cu-btn round bg-orange" @tap="sendToBle('0203000121')">
                  副驾解锁
                </button>
              </view>
              <view class="margin-tb-sm text-center">
                <button class="cu-btn round bg-orange" @tap="sendToBle('0203000122')">
                  副驾闭锁
                </button>
              </view>
              <view class="margin-tb-sm text-center">
                <button class="cu-btn round bg-orange" @tap="sendToBle('0203000131')">
                  左后门解锁
                </button>
              </view>
              <view class="margin-tb-sm text-center">
                <button class="cu-btn round bg-orange" @tap="sendToBle('0203000132')">
                  左后门闭锁
                </button>
              </view>
              <view class="margin-tb-sm text-center">
                <button class="cu-btn round bg-orange" @tap="sendToBle('0203000141')">
                  右后门解锁
                </button>
              </view>
              <view class="margin-tb-sm text-center">
                <button class="cu-btn round bg-orange" @tap="sendToBle('0203000142')">
                  右后门闭锁
                </button>
              </view>
              <view class="margin-tb-sm text-center">
                <button class="cu-btn round bg-orange" @tap="sendToBle('0203000151')">
                  后备箱解锁
                </button>
              </view>
              <view class="margin-tb-sm text-center">
                <button class="cu-btn round bg-orange" @tap="sendToBle('0203000152')">
                  后备箱闭锁
                </button>
              </view>
              <view class="margin-tb-sm text-center">
                <button class="cu-btn round bg-orange" @tap="sendToBle('0203000105')">
                  下电
                </button>
              </view>
              <view class="margin-tb-sm text-center">
                <button
                  class="cu-btn round bg-olive"
                  @tap="bluetoothNotifyHistoryInfo = []"
                >
                  清空日志
                </button>
              </view>
              <!-- <view class="margin-tb-sm text-center">
                <button
                  class="cu-btn round bg-olive"
                  @tap="goToPositioningCalibrationPage"
                >
                  定位校准
                </button>
              </view> -->
            </view>
  
            <!-- 监听到的蓝牙回复信息 -->
            <view class="connInfoCls">
              <!-- <div v-html="bluetoothNotify"></div> -->
              <!-- <br/> -->
  
              <div class="bluetooth-for-sending-and-receiving-messages">
                ### 蓝牙收发信息 ###<br /><br />
  
                <p
                  v-for="(item, index) in bluetoothNotifyHistoryInfo"
                  :key="index"
                  :class="item.class"
                >
                  {{ item.value }}
                </p>
              </div>
            </view>
          </view>
  
          <!-- 弹框 -->
          <uni-popup
            ref="popup"
            @change="popupClick"
            type="bottom"
            style="touch-action: none"
            backgroundColor="#ceeaff"
          >
            <view style="text-align: center; border-bottom: 2rpx solid #ccc">
              <view style="margin: 20upx 0">
                {{ other.BLEConn }} {{ searchBLETimesText }}
              </view>
            </view>
            <view class="pop" v-if="list.length != 0">
              <view
                class="type1"
                :class="{ add: index == num }"
                v-for="(item, index) in list"
                @tap="conn(item, false)"
                :key="index"
                hover-class="btn_hover"
              >
                {{ item.name }}
              </view>
            </view>
            <view v-else class="popNone" style="text-align: center">
              {{ other.BLENone }}
            </view>
          </uni-popup>
        </scroll-view>
      </view>
    </view>
  </view>
</template>

<script>
var connBLESuc = 0;
// import { uniEasyinput } from "@dcloudio/uni-ui";
import Base64 from "base-64";
import {
  getBlooth,
  createBLE,
  DeviceFound,
  closeBle,
  strToArr1,
  writeBLE,
  getCharacteristics,
  closeAllBluetoothConn,
} from "@/utils/socket/BLEConn.js";
import { setVinCode, getVinCode, getSecretKey, setSecretKeys} from '@/utils/auth'
// 导入链表
import { LinkedList } from "@/utils/LinkedList.js";
import { hexToString, stringToHex, ab2hex, padValToString, transformString } from '@/utils/common'

import store from '@/store';

// 引入CryptoJS库
const CryptoJS = require("crypto-js");

// 引入库 - 可根据定位获取经纬度信息
import { doGetLocation } from "@/utils/getLocation.js";

import {
  generateKey,
  getUserApplicationList,
  getUserApplicationDetail,
  activateDigitalKey,
  checkKeyPermission
} from "@/api/dk";

export default {
  data() {
    return {
      goConnectedBleClick: false,

      // 启动标记
      startSign: false,
      // 门锁标记
      lockSign: false,
      // 后备箱标记
      trunkSign: false,
      // 寻车标记
      findCarSign: false,
      // 车窗标记
      carWindowsSign: false,
      // 除霜标记
      defrostSign: false,
      // 车内检测人员状态，true表示有人，false表示无人
      personInCar: false,

      // 钥匙权限状态
      keyPermission: {
        isRestricted: false,        // 是否被限制
        expireTime: null,           // 过期时间
        restrictionReason: '',      // 限制原因
        willExpireSoon: false       // 是否即将过期
      },

      // 测试版本标记
      testVersionSign: false,
      // ===============================
      // 创建链表实例
      linkedList: new LinkedList(),
      // 是否有正在连接中的蓝牙设备
      hasConnectingDevice: false,
      // ===============================
      list: [], // 蓝牙列表
      scanBLE: false,
      searchBLETimes: 0, // 默认搜索蓝牙的次数
      searchBLETotleTimes: 2, // 蓝牙搜索默认总次数

      // 驾驶证上的VIN
      vinCode: "",
      // 根据驾驶证VIN码生成的密钥
      secretKey: "XXXXX",

      // 用于显示已连接的蓝牙设备信息
      deviceId: "",
      serviceId: "",
      characteristicId: "",
      deviceName: "",
      serviceDetails: "所有服务信息", // 已连接的设备，所有服务信息

      // 主服务UUID
      bleUUID: "",
      // 蓝牙通讯发送对象
      bleSendInfo: "",
      // 监听到的蓝牙回复信息
      bluetoothNotifyInfo: "",
      // 监听到的蓝牙回复历史信息
      bluetoothNotifyHistoryInfo: [],
      // 正在连接中的低功耗蓝牙设备信息
      dynamicConnInfo: "",
      timerId: null,
      timerIdForGetRSSI: null,
      connected: false,
      mainRssi: "-",
      leftRssi: "-",
      rightRssi: "-",
      rearRssi: "-",

      // 扫描设备信息集合
      scanDeviceInfoList: [],

      // 主设备设备ID
      mainDeviceId: "",
      // 主设备服务ID
      mainServiceId: "",
      // 主设备特征ID
      mainCharacteristicId: "",
      // 主设备名称
      mainDeviceName: "",
      // 从设备静默连接定时器ID
      subDevicesConnTimerId: null,
      // 从设备连接状态Map
      subDevicesConnStateMap: new Map(),
      // 静默连接超时校验定时器ID
      timeoutVerificationTimerId: null,

      // 当前手机型号
      currentPhoneModel: null,
      // 被分享的钥匙详情集合
      shareeDetails: [],

      // vinCode下拉框选项
      selectedVinCodeList: [],
      // 当前选择的vinCode标签
      selectedVinLable: '请选择',
    };
  },
  components: {
    // uniPopup,
    // uniEasyinput,
  },
  watch: {
    connected(val) {
      if (!val) {
        this.hasConnectingDevice = false;
        this.mainRssi = "-";
        this.leftRssi = "-";
        this.rightRssi = "-";
        this.rearRssi = "-";
      }
    }
  },
  computed: {
    // 当前蓝牙连接中
    connecting() {
      return this.dynamicConnInfo.includes('con-ing');
    },
    // 用于显示已连接的蓝牙设备信息
    connInfo() {
      if (this.connected && this.mainDeviceId) {
        // 使用三元运算符，把特征值限制在六位以内，如果超过六位，就在末尾添加省略号
        return `
        设备名称：${this.mainDeviceName}
        设备ID：${this.mainDeviceId}
        服务ID：${this.mainServiceId}
        特征ID：${this.mainCharacteristicId}`;
      } else {
        return "尚未连接蓝牙设备";
      }
    },
    // 用于显示监听到的蓝牙回复信息
    bluetoothNotify() {
      if (this.bluetoothNotifyInfo) {
        let bluetoothNotifyInfo = this.bluetoothNotifyInfo;
        this.bluetoothNotifyInfo = "";
        const now = new Date();
        const year = now.getFullYear(); // 获取当前年份
        const month = String(now.getMonth() + 1).padStart(2, "0"); // 获取当前月份，并确保是两位数（01-12）
        const day = String(now.getDate()).padStart(2, "0"); // 获取当前日期，并确保是两位数（01-31）
        const hours = String(now.getHours()).padStart(2, "0"); // 获取当前小时，并确保是两位数（00-23）
        const minutes = String(now.getMinutes()).padStart(2, "0"); // 获取当前分钟，并确保是两位数（00-59）
        const seconds = String(now.getSeconds()).padStart(2, "0"); // 获取当前秒数，并确保是两位数（00-59）

        // 拼接成所需的格式
        const formattedTime = `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
        // 是否自主通知
        let autoNotifyFlag = bluetoothNotifyInfo.startsWith("0302");
        // 将新信息插入到历史信息的开头
        this.bluetoothNotifyHistoryInfo.unshift({
          class: `replyMessageCls ${autoNotifyFlag ? "autoNotifyCls" : ""}`,
          value: `${formattedTime} 收：${bluetoothNotifyInfo} ${
            autoNotifyFlag ? "(自主通知)" : ""
          }`,
        });
        console.log(
          "this.bluetoothNotifyHistoryInfo :>> ",
          this.bluetoothNotifyHistoryInfo
        );
      }
      return `<div class="bluetooth-for-sending-and-receiving-messages">### 蓝牙收发信息 ###<br><br></div>`;
    },

    searchBLETimesText() {
      return ` ${this.searchBLETimes} / ${this.searchBLETotleTimes} `;
    },
    i18n() {
      return this.$t("login");
    },
    title() {
      return this.$t("title");
    },
    other() {
      return this.$t("other");
    },
    toast() {
      return this.$t("Toast");
    },
  },
  onLoad(options) {
    // 检查是否从申请状态页面跳转过来激活钥匙
    if (options.activate === 'true' && options.applicationId) {
      this.activateKeyFromApplication(options.applicationId);
    }

    // 从缓存里获取绑定的车辆VIN码
    this.vinCode = getVinCode();

    // 初始化车内人员状态为默认值（无人）
    this.personInCar = false;

    // 同步钥匙分享相关信息
    this.synchronizeShareDetails();

    // 检查用户是否有已审核通过的申请
    this.checkApprovedApplications();

    let that = this;
    // 获取当前手机的型号
    uni.getSystemInfo({
      success: function (res) {
        that.currentPhoneModel = res.model; // 手机型号
        console.log('当前手机型号:', that.currentPhoneModel);
      },
      fail: function (error) {
        console.log('获取手机型号失败', error);
      }
    });

    console.log('uni.getSystemInfoSync :>> ', uni.getSystemInfoSync);

    if (!this.vinCode?.trim() && !this.shareeDetails?.length) {
      // 未绑定VIN码，中断后续处理
      this.secretKey = '';
      return;
    }

    // 从缓存里获取根据绑定的车辆VIN码生成的密钥
    let secretKeyStr = getSecretKey();
    console.log("secretKeyStr :>> ", secretKeyStr);
    this.secretKey = '';
    if (secretKeyStr) {
      // 将十六进制字符串密钥转换成Uint8Array
      this.secretKey = this.secretKeyTransformation(secretKeyStr);

      // 根据已绑定的VIN码静默连接蓝牙设备
      this.silentConnByVinCode();
    } else if (this.shareeDetails?.length) {
      // 根据已绑定的VIN码静默连接蓝牙设备
      this.silentConnByVinCode();
    }

    console.log("this.vinCode :>> ", this.vinCode);
    console.log("this.secretKey :>> ", this.secretKey);

    // 如果密钥不存在，但还能获取车辆VIN码，则根据该VIN码自动获取密钥并缓存
    if (!this.secretKey && this.vinCode) {
      this.getSecretKeyByVin();
    }

    if ("尚未连接蓝牙设备" == this.connInfo) {
      this.timerId = setInterval(() => {
        // 启动蓝牙静默连接
        this.silentConnection();
        // // 获取设备的RSSI
        this.getRSSI();
      }, 3000); // 设置定时器，每隔3000毫秒（即3秒）执行一次回调函数

      // 监听蓝牙连接
      // let _that = this;
      // uni.onBLEConnectionStateChange((res) => {
      //   // this.connected = res.deviceId && res.connected;
      //   console.log("监听蓝牙连接res :>> ", res);

      //   uni.getConnectedBluetoothDevices({
      //     services: [],
      //     // services: [_that.serviceId],
      //     success: function (res) {
      //       console.log('123res.devices :>> ', res.devices);
      //       if (!res?.devices?.length) {
      //         _that.connected = false;
      //         if (_that.subDevicesConnStateMap?.size) {
      //           Array.from(_that.subDevicesConnStateMap.keys()).forEach(subDeviceId => {
      //             _that.subDevicesConnStateMap.set(subDeviceId, false);
      //           });
      //         }
      //       } 

      //       _that.connected = res.devices.find(device => device.deviceId == _that.mainDeviceId);

      //       let connectedBluetoothList = uni.getStorageSync("connectedBluetoothList");
      //       if (connectedBluetoothList?.length) {
      //         let blueInfos = connectedBluetoothList.filter(blueInfo => blueInfo.deviceId == _that.mainDeviceId);
      //         if (blueInfos?.length) {
      //           blueInfos[0].subDevices.forEach(subDeviceId => {
      //             _that.subDevicesConnStateMap.set(subDeviceId, res.devices.find(device => device.deviceId == subDeviceId))
      //           });
      //         }
      //       }

      //       // if (!_that.connected) {
      //       //   _that.closeBLEConnection(_that.deviceId);
      //       //   // _that.dynamicConnInfo = ">>> 设备连接已断开 <<<";
      //       // }
      //     },
      //     fail: function (err) {
      //       // 断开当前的蓝牙连接
      //       // _that.closeBLEConnection(_that.deviceId);
      //       _that.connected = false;
      //       if (_that.subDevicesConnStateMap?.size) {
      //         Array.from(_that.subDevicesConnStateMap.keys()).forEach(subDeviceId => {
      //           _that.subDevicesConnStateMap.set(subDeviceId, false);
      //         });
      //       }
      //       _that.dynamicConnInfo = ">>> 设备连接已断开 <<<";
      //     },
      //   });
      //   // console.info("监听蓝牙连接 res :>> ", res);
      // });
    }

    this.deviceId = "";
    this.serviceId = "";
    this.characteristicId = "";
    this.deviceName = "";
    this.bluetoothNotifyInfo = "";
    this.bluetoothNotifyHistoryInfo = [];

    if (uni.getStorageSync("deviceId")) {
      // await getBlooth()
      // await closeBle();
      uni.removeStorageSync("deviceId");
      uni.removeStorageSync("serviceId");
      uni.removeStorageSync("BLECONNID");
      console.log(
        "缓存清除后Storage -> deviceId :>> ",
        uni.getStorageSync("deviceId")
      );
      console.log(
        "缓存清除后Storage -> serviceId :>> ",
        uni.getStorageSync("serviceId")
      );
      console.log(
        "缓存清除后Storage -> BLECONNID :>> ",
        uni.getStorageSync("BLECONNID")
      );
    }
  },
  onUnload() {
    // 清除定时器
    if (this.timerId) {
      clearInterval(this.timerId);
      this.timerId = null;
    }
    if (this.subDevicesConnTimerId) {
      clearInterval(this.subDevicesConnTimerId);
      this.subDevicesConnTimerId = null;
    }
    if (this.timeoutVerificationTimerId) {
      clearInterval(this.timeoutVerificationTimerId);
      this.timeoutVerificationTimerId = null;
    }
    closeBle();
  },

  onShow() {
    console.log('切换到数字钥匙页面 :>> ');
    // 开启监听 notify 功能
    this.openWatchNotify(
      this.mainDeviceId,
      this.mainServiceId,
      this.mainCharacteristicId
    );

    // 检查钥匙权限状态
    this.checkKeyPermission();
  },

  methods: {
    /**
     * 检查用户是否有已审核通过的申请
     */
    async checkApprovedApplications() {
      try {
        const result = await getUserApplicationList({ status: '1' }); // 查询审核通过的申请
        if (result.data && result.data.length > 0) {
          const approvedApplication = result.data[0]; // 取第一个通过的申请

          // 如果当前没有绑定VIN码，提示用户激活钥匙
          if (!this.vinCode && approvedApplication.assignedVinCode) {
            uni.showModal({
              title: '发现可用钥匙',
              content: `您的申请已通过审核，是否激活数字钥匙？\n车辆：${approvedApplication.assignedVinCode}`,
              confirmText: '立即激活',
              cancelText: '稍后激活',
              success: (res) => {
                if (res.confirm) {
                  this.activateKeyFromApplication(approvedApplication.applicationId);
                }
              }
            });
          }
        }
      } catch (error) {
        console.error('检查申请状态失败:', error);
      }
    },

    /**
     * 从申请中激活钥匙
     */
    async activateKeyFromApplication(applicationId) {
      try {
        uni.showLoading({ title: '激活中...' });

        // 获取申请详情
        const applicationDetail = await getUserApplicationDetail(applicationId);
        if (applicationDetail.data && applicationDetail.data.assignedVinCode) {
          const vinCode = applicationDetail.data.assignedVinCode;

          // 设置VIN码并生成密钥
          this.vinCode = vinCode;
          await this.generateKeyFromApplication(vinCode);

          uni.hideLoading();
          uni.showToast({
            title: '钥匙激活成功',
            icon: 'success'
          });

          // 自动连接蓝牙
          setTimeout(() => {
            this.silentConnByVinCode();
          }, 1000);

        } else {
          uni.hideLoading();
          uni.showToast({
            title: '未找到分配的车辆',
            icon: 'none'
          });
        }
      } catch (error) {
        uni.hideLoading();
        console.error('激活钥匙失败:', error);
        uni.showToast({
          title: '激活失败，请重试',
          icon: 'none'
        });
      }
    },

    /**
     * 从申请中生成密钥
     */
    async generateKeyFromApplication(vinCode) {
      try {
        const result = await activateDigitalKey(vinCode);

        if (result.data && result.data.secretKey) {
          // 使用后台返回的密钥
          this.secretKey = new Uint8Array(result.data.secretKey);
        } else {
          // 如果后台没有返回密钥，使用原有的生成逻辑
          await generateKey({ vin: vinCode });

          // 暂时使用固定密钥（与原有逻辑保持一致）
          let hexValueStr = "0x7D 0x7C 0x00 0x75 0x77 0x70 0x78 0x06 0x06 0x78 0x70 0x00 0x00 0x06 0x03 0x00";
          const hexValues = hexValueStr.split(" ");
          const decimalValues = hexValues.map((hex) => parseInt(hex, 16));
          this.secretKey = new Uint8Array(decimalValues);
        }

        // 缓存密钥和VIN码
        setSecretKeys(this.secretKey);
        setVinCode(vinCode);

      } catch (error) {
        console.error('生成密钥失败:', error);
        throw error;
      }
    },

    /**
     * 检查钥匙权限状态
     */
    async checkKeyPermission() {
      if (!this.vinCode) return;

      try {
        const result = await checkKeyPermission(this.vinCode);
        if (result.data) {
          this.keyPermission = {
            isRestricted: result.data.isRestricted || false,
            expireTime: result.data.expireTime,
            restrictionReason: result.data.restrictionReason || '',
            willExpireSoon: this.checkWillExpireSoon(result.data.expireTime)
          };
        }
      } catch (error) {
        console.error('检查权限状态失败:', error);
      }
    },

    /**
     * 检查是否即将过期（7天内）
     */
    checkWillExpireSoon(expireTime) {
      if (!expireTime) return false;

      const expireDate = new Date(expireTime);
      const now = new Date();
      const diffTime = expireDate.getTime() - now.getTime();
      const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));

      return diffDays <= 7 && diffDays > 0;
    },

    /**
     * 格式化过期时间显示
     */
    formatExpireTime(expireTime) {
      if (!expireTime) return '';

      const date = new Date(expireTime);
      return `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}-${String(date.getDate()).padStart(2, '0')}`;
    },

    /**
     * @description: 切换钥匙
     * @param {*} event
     * @return {*}
     */
    onChange(event) {
      const index = event.detail.value;
      this.selectedVinLable = this.selectedVinCodeList[index].lable; // 更新选择的值
      this.vinCode = this.selectedVinCodeList[index].vinCode; // 更新选择的值

      console.log('selectedVinCode :>> ', this.vinCode);
      let shareeDetail = this.shareeDetails.filter(detail => detail.shareVehicleVin == this.vinCode);

      // 关闭所有蓝牙连接
      closeAllBluetoothConn();
      this.connected = false;
      this.dynamicConnInfo = "";
      this.bluetoothNotifyHistoryInfo = []
      if (this.subDevicesConnTimerId) {
        // 每次连主设备之前都需要把上次的从设备静默连接定时器关闭
        clearInterval(this.subDevicesConnTimerId);
        this.subDevicesConnTimerId = null;
      }

      if (shareeDetail?.length) {
        // 将十六进制字符串密钥转换成Uint8Array
        this.secretKey = this.secretKeyTransformation(shareeDetail[0].bluetoothPermKey);

        console.log('shareeDetail.bluetoothPermKey :>> ', this.secretKey);
        return;
      }

      // 将十六进制字符串密钥转换成Uint8Array
      this.secretKey = this.secretKeyTransformation(getSecretKey());
    },

    /**
     * @description: 将十六进制字符串密钥转换成Uint8Array
     * @param {*} secretKeyStr 字符串密钥
     * @return {*} Uint8Array密钥
     */     
    secretKeyTransformation(secretKeyStr) {
      if (!secretKeyStr) {
        return secretKeyStr;
      }

      // 原始十六进制字符串数组
      const hexValues = secretKeyStr.split(" ");

      // 将十六进制字符串转换为十进制数值的数组
      const decimalValues = hexValues.map((hex) => parseInt(hex, 16));

      // 使用十进制数值数组来创建Uint8Array
      return new Uint8Array(decimalValues);
    },

    /**
     * @description: 同步钥匙分享相关信息
     * @return {*}
     */      
    synchronizeShareDetails() {
      this.selectedVinCodeList = [];

      let _that = this;
      this.$store.dispatch('GetInfo').then(res => {
        setTimeout(() => {    
          console.log('store.state.user.shareDetails :>> ', store.state.user.shareDetails);
          console.log('store.state.user.id :>> ', store.state.user.id);
          let shareDetailList = store.state.user.shareDetails;
          let userId = store.state.user.id;

          _that.shareeDetails = shareDetailList.filter(detail => detail.shareeId == userId);

          let selfVinCode = getVinCode();
          if (selfVinCode) {
            _that.selectedVinCodeList.push({
              'lable': `我的车 - ${selfVinCode.slice(0, 3)}***${selfVinCode.slice(-3)}`,
              'vinCode': selfVinCode
            })
          } else {
            _that.selectedVinCodeList.push({
              'lable': `我的车 - 未绑定`,
              'vinCode': null
            })
          }

          if (_that.shareeDetails?.length) {
            _that.shareeDetails.forEach(detail => {
              if (!detail?.shareVehicleVin || _that.selectedVinCodeList.find(item => item.vinCode == detail.shareVehicleVin)) {
                return;
              }

              _that.selectedVinCodeList.push({
                'lable': `${detail.sharerName}的车 - ${detail.shareVehicleVin.slice(0, 3)}***${detail.shareVehicleVin.slice(-3)}`,
                'vinCode': detail.shareVehicleVin
              })
            })
          }

          console.log('_that.selectedVinCodeList--------------- :>> ', _that.selectedVinCodeList);

          if (_that.selectedVinCodeList?.length) {
            console.log('_that.vinCode :>> ', _that.vinCode);

            _that.selectedVinCodeList.forEach(item => {
              if (item.vinCode == _that.vinCode) {
                _that.selectedVinLable = item.lable;
              }
            });
          }
        }, 1000)
      })
    },

    /**
     * @description: 跳转到定位校准页面
     * @return {*}
     */    
    goToPositioningCalibrationPage() {
      uni.navigateTo({
        url: '../positioningCalibration/index'
      });
    },

    /**
     * @description: 从右开始每两位倒序重组新的字符串
     * @param {*} str
     * @return {*}
     */
    splitEveryTwoAndReverse(str) {
      // 使用正则表达式每两位切割字符串
      const matches = str.match(/.{1,2}/g);
      if (!matches) {
        return ""; // 如果没有匹配项，返回空字符串
      }
      // 倒序数组
      const reversedMatches = matches.reverse();
      // 拼接成字符串
      const reversedStr = reversedMatches.join("");
      return reversedStr;
    },

    /**
     * 通过VIN码获取密钥
     *
     * @returns {Uint8Array} 密钥字节数组
     */
    getSecretKeyByVin() {
      if (!this.vinCode?.trim()) {
        uni.showToast({
          title: "请输入驾驶证VIN码",
          icon: "none",
          mask: true,
        });
        return;
      }

      // 根据车辆VIN码生成密钥
      generateKey({ vin: this.vinCode }).then((res) => {
        console.log("res110---------- :>> ", res);

        // TODO 生成密钥的接口待提供，暂时固定返回密钥
        uni.showToast({
          title: "绑定成功",
        });

        let hexValueStr =
          "0x7D 0x7C 0x00 0x75 0x77 0x70 0x78 0x06 0x06 0x78 0x70 0x00 0x00 0x06 0x03 0x00";

        // 原始十六进制字符串数组
        const hexValues = hexValueStr.split(" ");

        // 将十六进制字符串转换为十进制数值的数组
        const decimalValues = hexValues.map((hex) => parseInt(hex, 16));

        // 使用十进制数值数组来创建Uint8Array
        this.secretKey = new Uint8Array(decimalValues);

        console.log("密钥secretKey-------- :>> ", this.secretKey);

        // 缓存密钥字符串
        setSecretKey(hexValueStr);
        // // 缓存驾驶证VIN码
        // uni.setStorageSync("vinCode", this.vinCode);
        setVinCode(this.vinCode);

        // 根据已绑定的VIN码静默连接蓝牙设备
        this.silentConnByVinCode();
      });
    },

    /**
     * @description: 根据已绑定的VIN码静默连接蓝牙设备
     * @return {*}
     */
    silentConnByVinCode() {
      let that = this;
      // 获取定位信息
      doGetLocation((data) => {
        console.log("doGetLocation :>> ", data);
        // 如果定位开了，就去搜索
        if (data?.latitude) {
          // 根据VIN码匹配车辆信息
          that.matchingVehicleInfo();
        } else {
          uni.showModal({
            title: "需要定位权限",
            content: "请手动打开定位权限\n否则无法搜索到蓝牙设备",
            success(modalRes) {
              // 二次获取定位信息
              doGetLocation((data) => {
                if (data?.latitude) {
                  // N次循环，搜索蓝牙设备,循环次数置0
                  that.searchBLETimes = 0
                  that.scanDeviceInfoList = [];
                  // 根据VIN码匹配车辆信息
                  that.matchingVehicleInfo();
                } else {
                  uni.showModal({
                    title: "定位权限",
                    content: "尚未打开定位权限，无法搜索",
                    success(modalRes) {},
                  });
                }
              });
            },
          });
        }
      });
    },

    /**
     * @description: 根据VIN码匹配车辆信息
     * @return {*}
     */
    matchingVehicleInfo() {
      // 被分享的钥匙vinCode集合
      let vinCodeList = this.shareeDetails.map(detail => detail.shareVehicleVin?.trim()).filter(shareVehicleVin => shareVehicleVin?.length >= 6) || [];

      if (this.vinCode?.trim() && this.vinCode.length >= 6) {
        vinCodeList.push(this.vinCode);
      }

      console.log('vinCodeList :>> ', vinCodeList);

      if (!vinCodeList?.length) {
        // 未绑定VIN码或无被分享的数字钥匙，则中断后续处理
        return;
      }

      let that = this;
      // 绑定成功后，根据VIN码静默连接相关车辆蓝牙设备
      getBlooth()
        .then((res) => {
          console.log(`>>>>>  第${that.searchBLETimes + 1} / ${that.searchBLETotleTimes}次搜索蓝牙结果 res :>> ${res}`);
          console.log('this.scanDeviceInfo :>> ', that.scanDeviceInfo);

          console.log("蓝牙设备情况 res.devices :>> ", res.devices);

          // 根据已绑定的车辆VIN码过滤出对应的蓝牙设备，用于静默连接
          res.devices.forEach(function (device) {
            console.log("找到设备", device);
            // ArrayBuffer转16进制字符串
            // let advertisData = "";
            // let serviceData = "";
            // if (uni.getSystemInfoSync().platform === 'android') {
            //   advertisData = Array.from(new Uint8Array(device.advertisData))
            //     .map(x => ('00' + x.toString(16)).slice(-2))
            //     .join('');
            //     const keys = Object.keys(device.serviceData);
            //   if (keys.length > 0) {
            //     serviceData = Array.from(new Uint8Array(device.serviceData[keys[0]]))
            //       .map(x => ('00' + x.toString(16)).slice(-2))
            //       .join('');
            //   }
            // } else if (uni.getSystemInfoSync().platform === 'ios') {
            //   advertisData = Array.prototype.map
            //     .call(new Uint8Array(device.advertisData), x => ('00' + x.toString(16)).slice(-2))
            //     .join('');
            //   serviceData = Array.prototype.map
            //     .call(new Uint8Array(device.serviceData[Object.keys(device.serviceData)[0]]), x => ('00' + x.toString(16)).slice(-2))
            //     .join('');
            // } else {
            //   // Handle other platforms if needed
            // }

            let advertisData = '';
            if (device.advertisData) {
              advertisData = ab2hex(device.advertisData);
            }
            console.log("广播数据包advertisData字符串", advertisData);
            let serviceData = '';
            if (device.serviceData) {
              let serviceDataKeys = Object.keys(device.serviceData);
              if (serviceDataKeys.length > 0) {
                serviceData = ab2hex(device.serviceData[serviceDataKeys[0]]);
              }
            }
            console.log("广播数据包serviceData字符串", serviceData);

            vinCodeList.forEach(currentVinCode => {
              if (serviceData?.length >= 14) {
                let vinCode = serviceData.slice(-12);
  
                // 计算倒数第14位的正数索引
                let startIndex = serviceData.length - 14;
                // 计算倒数第13位的正数索引的下一个位置（因为slice不包括结束索引）
                let endIndex = startIndex + 2;
                // 使用slice方法截取字符串
                let masterSlaveNode = serviceData.slice(startIndex, endIndex);
  
                console.log("serviceData--vinCode :>> ", vinCode);
                console.log("serviceData--masterSlaveNode :>> ", masterSlaveNode);
  
                let vincodeOfLastSixDigits = that.splitEveryTwoAndReverse(
                  stringToHex(currentVinCode.slice(-6))
                );
                console.log(
                  "vincodeOfLastSixDigits :>> ",
                  vincodeOfLastSixDigits
                );

                if (vinCode == vincodeOfLastSixDigits) {
                  if (that.scanDeviceInfoList?.length && that.scanDeviceInfoList.filter(item => item.vinCode == currentVinCode)?.length) {
                    that.scanDeviceInfoList.forEach(scanDeviceInfo => {
                      if (scanDeviceInfo.vinCode == currentVinCode) {
                        if ("01" == masterSlaveNode) {
                          scanDeviceInfo.mainDeviceInfo = device;
                        } else if ("02" == masterSlaveNode) {
                          scanDeviceInfo.subDeviceInfos.push(device);
                        }
                      }
                    });
                    return;
                  }

                  let scanDeviceInfo =  {
                    vinCode: currentVinCode,
                    mainDeviceInfo: null,
                    subDeviceInfos: [],
                  };

                  if ("01" == masterSlaveNode) {
                    scanDeviceInfo.mainDeviceInfo = device;
                  } else if ("02" == masterSlaveNode) {
                    scanDeviceInfo.subDeviceInfos.push(device);
                  }

                  that.scanDeviceInfoList.unshift(scanDeviceInfo);
                }
              }
            });
          });

          // N次循环，搜索蓝牙设备
          if (that.searchBLETimes < that.searchBLETotleTimes - 1) {
            that.matchingVehicleInfo();
            that.searchBLETimes++;
          }

          if (!that.scanDeviceInfoList?.length) {
            console.log("附近暂未检索到车辆");
            uni.showToast({
              title: `第 ${that.searchBLETimes}/${that.searchBLETotleTimes}次 未检索到车辆数据`,
              icon: "none",
              mask: true,
            });
            return;
          }

          that.scanDeviceInfoList.forEach(scanDeviceInfo => {
            // 兼容1 + 0方案
            if (!scanDeviceInfo?.subDeviceInfos?.length) {
              scanDeviceInfo.subDeviceInfos.push({
                deviceId: "88888888"
              })
            }
  
            that.$set(scanDeviceInfo.mainDeviceInfo, "subDevices", scanDeviceInfo.subDeviceInfos);
  
            console.log("需要连接的车辆蓝牙设备信息 :>> ", scanDeviceInfo.mainDeviceInfo);
  
            // 从本地存储中获取已经连接过的蓝牙设备列表
            let connectedBluetoothList = uni.getStorageSync(
              scanDeviceInfo.vinCode + "connectedBluetoothList"
            ) || [];
  
            // 从已经连接过的蓝牙设备列表中移除当前连接的蓝牙信息，预防重复添加
            connectedBluetoothList = connectedBluetoothList.filter(
              (ble) => ble.deviceId !== scanDeviceInfo.mainDeviceInfo.deviceId
            );
  
            // 将当前连接的蓝牙设备添加到已经连接过的蓝牙设备列表中
            scanDeviceInfo.mainDeviceInfo.mainServiceNodeUUID = "0000fffd-0000-1000-8000-00805f9b1910";
            connectedBluetoothList.unshift(scanDeviceInfo.mainDeviceInfo);
  
            console.log('connectedBluetoothList---- :>> ', connectedBluetoothList);
  
            // 将更新后的已经连接过的蓝牙设备列表存储回本地存储
            uni.setStorageSync(
              scanDeviceInfo.vinCode + "connectedBluetoothList",
              connectedBluetoothList
            );
          });


          // this.conn(deviceInfo, true);

          // TODO 时机待实际调试优化
          // setTimeout(() => {
          //   // 静默连接成功后，进行一次握手通信
          //   if (this.connected) {
          //     this.sendToBle(this.vinCode + uni.getStorageSync("secretKeyStr"));
          //   }
          // });
        })
        .catch((err) => {});
    },

    /**
     * 使用AES加密算法加密数据。
     *
     * @param {String} data - 需要加密的数据。
     * @param {Uint8Array} key - 用于加密的密钥，为byte数组。
     * @param {String} iv - 密钥偏移量
     * @returns {String} 加密后的数据，以Base64字符串形式返回。
     */
    encryptData(data, key, iv) {
      // 将byte数组转换为WordArray，CryptoJS使用WordArray作为密钥和数据的格式
      const keyWordArray = CryptoJS.lib.WordArray.create(key);
      // 将字符串数据转换为WordArray
      const dataWordArray = CryptoJS.enc.Utf8.parse(data);

      iv = CryptoJS.enc.Utf8.parse(iv);
      // 使用AES加密
      const encrypted = CryptoJS.AES.encrypt(dataWordArray, keyWordArray, {
        iv,
        mode: CryptoJS.mode.CBC,
        padding: CryptoJS.pad.Pkcs7,
      });
      // 返回加密后的数据的Base64字符串
      return encrypted.toString();
    },

    /**
     * 使用AES加密算法解密数据。
     *
     * @param {String} encryptedData - 加密后的Base64字符串数据。
     * @param {Uint8Array} key - 用于解密的密钥，为byte数组。
     * @param {String} iv - 密钥偏移量
     * @returns {String} 解密后的原始数据。
     */
    decryptData(encryptedData, key, iv) {
      // 将byte数组转换为WordArray
      const keyWordArray = CryptoJS.lib.WordArray.create(key);

      iv = CryptoJS.enc.Utf8.parse(iv);
      // 使用AES解密
      const decrypted = CryptoJS.AES.decrypt(encryptedData, keyWordArray, {
        iv,
        mode: CryptoJS.mode.CBC,
        padding: CryptoJS.pad.Pkcs7,
      });
      // 将解密后的WordArray转换为Utf8字符串
      return decrypted.toString(CryptoJS.enc.Utf8);
    },

    /**
     * @description: 生成一个8位的随机十六进制数
     * @return {*}
     */
    generate8DigitUUID() {
      return Math.floor(16 ** 8).toString(16);
    },

    getConnectedBluetoothDevices(ble, mainServiceNodeUUIDs) {
      let _that = this;
      console.log("mainServiceNodeUUIDs :>> ", mainServiceNodeUUIDs);
      return new Promise((resolve, reject) => {
        // // 如果以有设备连接，当前则不再检查
        // if (_that.connected) {
        //   resolve(true);
        //   return;
        // }

        // 检查已连接的蓝牙设备
        uni.getConnectedBluetoothDevices({
          services: mainServiceNodeUUIDs, // 替换为你的蓝牙服务UUID
          success: function (res) {
            console.log(
              "getConnectedBluetoothDevices res------>>>> :>> ",
              res,
              ble
            );

            var storedDeviceId = uni.getStorageSync("deviceId");
            res.devices.forEach(function (device) {
              if (device.deviceId !== storedDeviceId) {
                // 调用关闭蓝牙的方法
                _that.closeBLEConnection(device.deviceId);
              }
            });

            _that.connected = res.devices.length > 0;
            resolve(res);
          },
          fail: function (err) {
            // 获取已连接设备失败
            console.log(
              "getConnectedBluetoothDevices err------>>>> :>> ",
              err,
              ble
            );
            // 断开当前的蓝牙连接
            _that.closeBLEConnection(ble.deviceId);
            _that.connected = false;
            reject(err);
          },
        });
      });
    },

    // 断开当前的蓝牙连接
    closeBLEConnection(deviceId) {
      return new Promise((resolve, reject) => {
        uni.closeBLEConnection({
          deviceId: deviceId,
          success(res) {
            console.log("断开蓝牙连接 res :>> ", res);
            resolve(res);
          },
          fail: (err) => {
            console.error("断开蓝牙连接 失败err :>> ", err);
            resolve(err);
          },
        });
      });
    },

    // 获取设备的RSSI
    getRSSI() {
      if (!this.connected) return; // 设备连接以后，再获取RSSI
      
      // 主从设备Rssi值对应的字段名称
      let masterSlaveDeviceRssiFieldName = ["mainRssi", "leftRssi", "rightRssi", "rearRssi"];

      console.log('this.subDevicesConnStateMap :>> ', this.subDevicesConnStateMap);
      console.log('this.subDevicesConnStateMap.size :>> ', this.subDevicesConnStateMap.size);

      let masterSlaveDeviceIds = [this.mainDeviceId];
      if (this.subDevicesConnStateMap?.size) {
        // 获取已连接成功的子设备ID
        let subDeviceIds = Array.from(this.subDevicesConnStateMap.keys().filter(subDeviceId => this.subDevicesConnStateMap.get(subDeviceId)));
        console.log('subDeviceIds :>> ', subDeviceIds);
        masterSlaveDeviceIds = masterSlaveDeviceIds.concat(subDeviceIds);
      }

      console.log('masterSlaveDeviceIds :>> ', masterSlaveDeviceIds);

      masterSlaveDeviceIds.forEach((deviceId, index) => {
        setTimeout(() => {
          let _that = this;
          uni.getBLEDeviceRSSI({
            deviceId,
            success: function (rssiRes) {
              // 接收RSSI值
              _that[masterSlaveDeviceRssiFieldName[index]] = rssiRes?.RSSI || "-";
            },
            fail: function (err) {
              console.error(`获取${masterSlaveDeviceRssiFieldName[index]}失败：`, err);
              _that[masterSlaveDeviceRssiFieldName[index]] = "-";
            },
          });
        }, (index + 1) * 100)
      });
    },

    navigateToCoUIPage() {
      uni.navigateTo({
        url: "/pages/coUI/index/index",
      });
    },
    /**
     * @description: 开启监听 蓝牙notify 功能
     * @param {*} deviceId
     * @param {*} serviceId
     * @param {*} characteristicId
     * @return {*}
     *
     */
    openWatchNotify(deviceId, serviceId, characteristicId) {
      let that = this;
      uni.notifyBLECharacteristicValueChange({
        state: true, // 启用 notify 功能
        // 这里的 deviceId 需要已经通过 createBLEConnection 与对应设备建立链接
        deviceId,
        // 这里的 serviceId 需要在 getBLEDeviceServices 接口中获取
        serviceId,
        // 这里的 characteristicId 需要在 getBLEDeviceCharacteristics 接口中获取
        characteristicId,
        success(res) {
          console.log("notifyBLECharacteristicValueChange success", res.errMsg);
          uni.onBLECharacteristicValueChange(function (res) {
            console.log(res);
            // 只处理当前已连接的主设备通知
            if (res?.deviceId != that.mainDeviceId) {
              return;
            }
            // 监听帧头帧尾
            var resCode1 = ab2hex(res.value);
            // 收到蓝牙返回的命令（16进制）
            var resCode = resCode1?.toUpperCase();

            console.error(res?.deviceId + "：监听蓝牙回复信息内容：" + hexToString(resCode));

            // // TODO 偏移量位置待确认
            // let iv = "";
            // // 使用AES加密算法解密数据。
            // let decryptResCode = this.decryptData(resCode, this.secretKey, iv);

            // console.log("解密后的蓝牙回复信息 :>> ", decryptResCode);

            // 转成UTF-8返回
            that.bluetoothNotifyInfo = hexToString(resCode);

            let statusMsgList = [
              {
                code: "00",
                msg: "成功",
              },
              {
                code: "01",
                msg: "指令处理失败",
              },
              {
                code: "02",
                msg: "不支持改Message的请求",
              },
              {
                code: "03",
                msg: "不支持改Command的请求",
              },
              {
                code: "04",
                msg: "无权限",
              },
              {
                code: "05",
                msg: "系统忙",
              },
              {
                code: "08",
                msg: "设备认证失败/设备认证状态失效",
              },
              {
                code: "09",
                msg: "响应超时",
              },
              {
                code: "0A",
                msg: "指令已收到，等待执行结果",
              },
              // 0B ~ FF 为保留值
            ];

            let statusMsg = statusMsgList.find(
              (e) => e.code == that.bluetoothNotifyInfo?.substring(4, 6)
            );

            if (statusMsg) {
              let toast = { title: statusMsg.msg };
              if (statusMsg.msg != "成功") {
                toast["icon"] = "none";
              }
              uni.showToast(toast);
            }

            
            // 处理车内检测逻辑
            // 根据图片显示，车端发送: 030200010D 表示车内检测有人
            if (that.bluetoothNotifyInfo === "030200010D") {
              that.personInCar = true;
              console.log("车内检测：有人");
              uni.showToast({
                title: "检测到车内有人",
                icon: "none",
                duration: 2000
              });
            }
            // 根据图片显示，车端发送: 030200010E 表示车内检测无人
            else if (that.bluetoothNotifyInfo === "030200010E") {
              that.personInCar = false;
              console.log("车内检测：无人");
            }


            // 开启成功标记映射
            let openSuccessfulMap = new Map();
            openSuccessfulMap.set("0203000104", "startSign")
            openSuccessfulMap.set("0203000101", "lockSign")
            openSuccessfulMap.set("0203000151", "trunkSign")
            // openSuccessfulMap.set("", "carWindowsSign")
            // openSuccessfulMap.set("", "defrostSign")

            let openSuccessfulSign = openSuccessfulMap.get(that.bluetoothNotifyInfo);
            if (openSuccessfulSign) {
              this[openSuccessfulSign] = true;
            }

            // 关闭成功标记映射
            let closeSuccessfulMap = new Map();
            closeSuccessfulMap.set("0203000105", "startSign")
            closeSuccessfulMap.set("0203000102", "lockSign")
            closeSuccessfulMap.set("0203000152", "trunkSign")
            // closeSuccessfulMap.set("", "carWindowsSign")
            // closeSuccessfulMap.set("", "defrostSign")

            let closeSuccessfulSign = closeSuccessfulMap.get(that.bluetoothNotifyInfo);
            if (closeSuccessfulSign) {
              this[closeSuccessfulSign] = false;
            }

            if (
              [
                "0302000104",
                "0302000103",
                "030200010A",
                "030200010B",
                "030200010C",
                "030200010D", // 车内检测有人
                "030200010E", // 车内检测无人
              ].includes(that.bluetoothNotifyInfo) ||
              ["030200020A", "030200020B", "030200020C"].find((e) =>
                that.bluetoothNotifyInfo.startsWith(e)
              )
            ) {
              setTimeout(
                () => {
                  // 通知类消息接收到后没有后续响应，所以回复都为成功
                  that.sendToBle("0302000000", false);
                },
                statusMsg ? 1000 : 0
              );
            }
          });
        },
        fail(err) {
          console.error("监听蓝牙回复信息失败", err);
          that.bluetoothNotifyInfo = "";
        },
      });
    },

    async silentConnection() {
      // ===============================
      // 当前已有蓝牙设备处于连接状态、链表无节点，或者未绑定VIN码，都需要中断后续处理
      if (this.connected || this.linkedList?.getLength() > 0 || !this.vinCode?.trim()) {
        return;
      }
      // ===============================
      console.log(
        "启动蓝牙静默连接 this.connInfo",
        this.connInfo,
        "尚未连接蓝牙设备" == this.connInfo
      );

      // 从本地存储中获取已经连接过的蓝牙设备列表
      let connectedBluetoothList = uni.getStorageSync(this.vinCode + "connectedBluetoothList");
      // 控制台打印已连接的蓝牙设备列表
      console.log("静默连接设备列表：>> ", connectedBluetoothList);

      if (!connectedBluetoothList?.length) {
        // 如果已连接的蓝牙设备列表为空
        console.log("未连接过蓝牙设备，无需静默连接");
        return;
      }

      // 在链表尾部批量添加节点
      this.linkedList.batchAppend(connectedBluetoothList);

      // const mainServiceNodeUUIDs = connectedBluetoothList.map(
      //   (device) => device.mainServiceNodeUUID
      // );

      // // 使用uni.openBluetoothAdapter API来初始化蓝牙适配器
      // await uni.openBluetoothAdapter({
      //   // 如果成功打开蓝牙适配器，执行以下代码
      //   success: async () => {

      // ===================================
      
      if (this.subDevicesConnTimerId) {
        // 每次连主设备之前都需要把上次的从设备静默连接定时器关闭
        clearInterval(this.subDevicesConnTimerId);
        this.subDevicesConnTimerId = null;
      }

      let connTimerId = setInterval(() => {
        // 如果链表为空，则清除当前设置的定时器
        if (this.linkedList?.getLength() <= 0) {
          clearInterval(connTimerId);
          return;
        }

        // let currentlyConnectedDevice = this.linkedList.getFirstValue();
        if (this.hasConnectingDevice) {
          return;
        }
        console.info(
          "+++++++++++++++++++++++++++++ silentConnection +++++++++++++++++++++++++++++++++++"
        );

        this.conn(this.linkedList.getFirstValue(), true);
      }, 100); // 设置定时器，每隔100毫秒（即0.1秒）执行一次回调函数
      // ===================================

      // =======================================================================================
      // 注释
      // 使用Array.reduce方法来遍历connectedBluetoothList数组
      // await connectedBluetoothList.reduce(async (previousPromise, ble) => {
      //   // 等待前一个Promise对象完成
      //   await previousPromise;
      //   // 获取已连接的蓝牙设备信息
      //   // await this.getConnectedBluetoothDevices(ble, mainServiceNodeUUIDs);
      //   // 连接到当前遍历到的蓝牙设备
      //   console.info(
      //     "+++++++++++++++++++++++++++++ silentConnection +++++++++++++++++++++++++++++++++++"
      //   );
      //   return this.conn(ble, true);
      // }, Promise.resolve()); // 初始值为一个已完成（resolved）状态的Promise对象
      // =======================================================================================
      //   },
      //   // 如果打开蓝牙适配器失败，执行以下代码
      //   fail: (err) => {
      //     // 在控制台输出错误信息
      //     console.error("蓝牙适配器打开失败", err);
      //     // 更新动态连接信息提示用户打开蓝牙
      //     this.dynamicConnInfo = ">>> 请打开蓝牙 <<<";
      //   },
      // });

      // await uni.openBluetoothAdapter({
      //   success: async () => {
      //     // 蓝牙适配器打开成功的回调函数
      //     // 控制台打印提示信息
      //     // console.log('蓝牙适配器打开成功');

      //     // 如果已连接的蓝牙设备列表不为空
      //     // 遍历已连接的蓝牙设备列表
      //     for (const ble of connectedBluetoothList) {   
      //       // 控制台打印当前连接信息
      //       // console.log('this.connInfo :>> ', this.connInfo);

      //       // 调用conn方法连接蓝牙设备
      //       await this.conn(ble, true);
      //     }
      //   },
      //   fail: (err) => {
      //     // 蓝牙适配器打开失败的回调函数
      //     console.error("蓝牙适配器打开失败", err);
      //     this.dynamicConnInfo = ">>>请打开蓝牙<<<";
      //   },
      // });
    },

    // 弹窗开启、关闭
    popupClick(e) {
      if (!e.show) {
        this.serviceDetails = "";

        let index = 0;
        let serviceInfo = uni.getStorageSync(`serviceInfo_${index}`);
        while (serviceInfo) {
          this.serviceDetails += `服务 ${index}: UUID - ${
            serviceInfo.uuid
          }, 是否主服务 - ${serviceInfo.isPrimary ? "是" : "否"}\n`;
          index++;
          serviceInfo = uni.getStorageSync(`serviceInfo_${index}`);
        }

        // 当列表关闭时，检查是否有蓝牙已被连接，决定将已连接的蓝牙信息显示到主页面上
        var deviceId = uni.getStorageSync("deviceId");
        var serviceId = uni.getStorageSync("serviceId");
        var deviceName = uni.getStorageSync("deviceName");
        var characteristicId = uni.getStorageSync("characteristicId");
        console.log("characteristicId :>> ", characteristicId);
        if (deviceId && serviceId) {
          this.deviceId = deviceId;
          this.serviceId = serviceId;
          this.characteristicId = characteristicId;
          this.deviceName = deviceName;
        } else {
          this.deviceId = "";
          this.deviceName = "";
        }
      }
    },
    // 发送信息给蓝牙设备
    async sendToBle(bleSendInfo, isThereAPopup = true) { // isThereAPopup: 是否弹窗提示
      if (bleSendInfo) {
        this.bleSendInfo = bleSendInfo;
      }
      console.log("发送给蓝牙设备的信息为 bleSendInfo :>> ", this.bleSendInfo);
      const now = new Date();
      const year = now.getFullYear(); // 获取当前年份
      const month = String(now.getMonth() + 1).padStart(2, "0"); // 获取当前月份，并确保是两位数（01-12）
      const day = String(now.getDate()).padStart(2, "0"); // 获取当前日期，并确保是两位数（01-31）
      const hours = String(now.getHours()).padStart(2, "0"); // 获取当前小时，并确保是两位数（00-23）
      const minutes = String(now.getMinutes()).padStart(2, "0"); // 获取当前分钟，并确保是两位数（00-59）
      const seconds = String(now.getSeconds()).padStart(2, "0"); // 获取当前秒数，并确保是两位数（00-59）

      const formattedTime = `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
      // 将新发送的信息插入到历史信息的开头
      this.bluetoothNotifyHistoryInfo.unshift({
        class: "bleSendInfoCls",
        value: `${formattedTime} 发：${this.bleSendInfo}`,
      });
      console.log(
        "this.bluetoothNotifyHistoryInfo :>> ",
        this.bluetoothNotifyHistoryInfo
      );

      // let iv = this.generate8DigitUUID();

      // let encryptBleSendInfo = this.encryptData(
      //   this.bleSendInfo,
      //   this.secretKey,
      //   iv
      // );

      // console.log("加密后的发送信息 :>> ", encryptBleSendInfo);

      // let decryptResCode = this.decryptData(encryptBleSendInfo, this.secretKey, iv);

      // console.log('测试解密后的信息 :>> ', decryptResCode);

      // console.log("发送给蓝牙设备的信息为 strToArr1 :>> ", strToArr1(this.bleSendInfo, 2));
      // await writeBLE(strToArr1(this.bleSendInfo, 2));
      // await writeBLE(this.bleSendInfo);
      uni.writeBLECharacteristicValue({
        // 这里的 deviceId 需要在 getBluetoothDevices 或 onBluetoothDeviceFound 接口中获取
        deviceId: this.mainDeviceId,
        // 这里的 serviceId 需要在 getBLEDeviceServices 接口中获取
        serviceId: this.mainServiceId,
        // 这里的 characteristicId 需要在 getBLEDeviceCharacteristics 接口中获取
        characteristicId: this.mainCharacteristicId,
        value: uni.base64ToArrayBuffer(Base64.encode(this.bleSendInfo)),
        success(res) {
          if (isThereAPopup) {
            uni.hideLoading();
            setTimeout(() => {
              uni.showToast({
                title: "发送成功",
              });
            }, 500);
          }

        },
        fail(err) {
          console.log("发送数据失败 err :>> ", err);
          uni.hideLoading();
          if (isThereAPopup) {
            setTimeout(() => {
              uni.showToast({
                title: "发送失败" + JSON.stringify(err),
              });
            }, 500);
          }
        },
      });
    },
    // n次循环搜索蓝牙列表
    async searchBLE() {
      this.searchBLETimes = 0;
      // this.list = [];
      var deviceId = uni.getStorageSync("deviceId");
      this.list = this.list.filter((ble) => ble.deviceId !== deviceId);

      while (this.searchBLETimes < this.searchBLETotleTimes) {
        console.log(`搜索蓝牙中: ${this.searchBLETimesText}`);
        await this.getList();
        this.searchBLETimes++;
      }
    },

    // 实际去搜索蓝牙列表
    async getList() {
      let that = this;

      // 获取定位信息
      doGetLocation((data) => {
        console.log("data :>> ", data);
        // 如果定位开了，就去搜索
        if (data?.latitude) {
          // 直接搜索蓝牙设备
          that.startBluetoothSearch();
        } else {
          uni.showModal({
            title: "需要定位权限",
            content: "请手动打开定位权限\n否则无法搜索到蓝牙设备",
            success(modalRes) {
              // 二次获取定位信息
              doGetLocation((data) => {
                if (data?.latitude) {
                  // 直接搜索蓝牙设备
                  that.startBluetoothSearch();
                } else {
                  uni.showModal({
                    title: "定位权限",
                    content: "尚未打开定位权限，无法搜索",
                    success(modalRes) {},
                  });
                }
              });
            },
          });
        }
      });
    },

    // 直接搜索蓝牙设备
    async startBluetoothSearch() {
      if (this.list.length == 0) {
        uni.showLoading({
          title: `搜索中...`,
          icon: "none",
        });
      }
      let searchBLETimes = this.searchBLETimes;
      // 打开底部蓝牙列表弹框
      this.$refs.popup.open();

      var flag = false; // 标记是否进入了getBLooth

      let that = this;
      console.log("调用 获取蓝牙getBlooth()");
      await getBlooth()
        .then((res) => {
          console.log("蓝牙搜索结果 res :>> ", res);
          flag = true;

          console.log("蓝牙设备情况 res.devices :>> ", res.devices);
          // 隐藏搜索中的弹窗
          uni.hideLoading();

          that.scanBLE = true;
          // 将检索到的设备添加到list里面，并去重
          res.devices.forEach((device) => {
            // 判断device是否已经存在于list数组中
            if (!that.list.find((item) => item.deviceId === device.deviceId)) {
              that.list.push(device); // 如果不存在，则添加到list数组
            }
          });
          console.log("最新蓝牙列表 that.list :>> ", that.list);
        })
        .catch((err) => {
          flag = true;

          console.log(
            `----------搜索蓝牙失败: ${
              that.searchBLETimesText
            } \n ${JSON.stringify(err)}`
          );

          // 如果第1次检测到蓝牙已经连接成功，那么后续再次检测到异常就不要再报错。
          // 默认用户的蓝牙是始终开启或者始终关闭状态。
          if (
            searchBLETimes == 0 ||
            searchBLETimes == that.searchBLETotleTimes - 1
          ) {
            uni.showToast({
              title: "蓝牙状态异常,请检查蓝牙打开状态。",
              icon: "none",
              mask: true,
            });
            console.log("蓝牙状态异常,请检查蓝牙打开状态。");
          }

          // 隐藏搜索中的弹窗
          uni.hideLoading();
        });
      console.log(flag);
      if (!flag) {
        uni.hideLoading();
        uni.showToast({
          title: "没有进入",
          icon: "none",
          mask: true,
        });
        console.log("没有进入getBlooth");
      }
    },

    // 2. 选中蓝牙并连接
    async conn(item, isSilentConn, isSubDevice) {
      if (!this.vinCode?.trim()) {
        // 未绑定VIN码，中断后续处理
        return;
      }
      
      if (!isSubDevice) {
        // =======================
        this.hasConnectingDevice = true;
        // =======================
      }

      // 静默连接成功以后，将不再执行后续静默连接操作
      if (isSilentConn && this.connected && !isSubDevice) {
        return;
      }

      // 手动连接时
      if (!isSilentConn) {
        // await this.closeBLEConnection( );
        // await this.closeBLEConnection(item.deviceId);
        this.connected = false;
      }

      if (!isSubDevice) {
        this.dynamicConnInfo = `con-ing >>> 正在${isSilentConn ? "静默" : "手动"}连接${
          item.localName
        }中... <<<`;
      }

      if (!isSilentConn) {
        uni.showLoading({
          title: this.other.checking,
          // title: "检测中...",
          icon: "loading",
          mask: true,
        });
        setTimeout(() => {
          uni.hideLoading();
        }, 10000);
      }
      connBLESuc++;
      let create = true; // 判断有没有进入createBLE

      console.log("this.dynamicConnInfo :>> ", this.dynamicConnInfo);
      console.log("2. 选中蓝牙并连接 Start！！！ item :>> ", item);
      console.log(
        "             连接前检查缓存情况Storage -> deviceId :>> ",
        uni.getStorageSync("deviceId")
      );
      console.log(
        "             连接前检查缓存情况Storage -> serviceId :>> ",
        uni.getStorageSync("serviceId")
      );
      console.log(
        "             连接前检查缓存情况Storage -> BLECONNID :>> ",
        uni.getStorageSync("BLECONNID")
      );

      if (!isSubDevice && uni.getStorageSync("mainDeviceId")) {
        uni.removeStorageSync("mainDeviceId");
        uni.removeStorageSync("mainServiceId");
        uni.removeStorageSync("mainCharacteristicId");
        console.log(
          "             缓存清除后Storage -> mainDeviceId :>> ",
          uni.getStorageSync("mainDeviceId")
        );
        console.log(
          "             缓存清除后Storage -> mainServiceId :>> ",
          uni.getStorageSync("mainServiceId")
        );
        console.log(
          "             缓存清除后Storage -> mainCharacteristicId :>> ",
          uni.getStorageSync("mainCharacteristicId")
        );
      }

      if (uni.getStorageSync("deviceId")) {
        // await getBlooth()
        // await closeBle();
        uni.removeStorageSync("deviceId");
        uni.removeStorageSync("serviceId");
        uni.removeStorageSync("BLECONNID");
        console.log(
          "             缓存清除后Storage -> deviceId :>> ",
          uni.getStorageSync("deviceId")
        );
        console.log(
          "             缓存清除后Storage -> serviceId :>> ",
          uni.getStorageSync("serviceId")
        );
        console.log(
          "             缓存清除后Storage -> BLECONNID :>> ",
          uni.getStorageSync("BLECONNID")
        );
      }

      // 主服务节点UUID，用于通讯
      let mainServiceNodeUUID = this.bleUUID
        ? this.bleUUID
        : "0000fffd-0000-1000-8000-00805f9b1910";

      // 静默连接时，主服务UUID来自于缓存，而非页面用户输入
      if (isSilentConn) {
        if (this.timeoutVerificationTimerId) {
          clearInterval(this.timeoutVerificationTimerId);
          this.timeoutVerificationTimerId = null;
        }

        mainServiceNodeUUID =
          item.mainServiceNodeUUID || "0000fffd-0000-1000-8000-00805f9b1910";

          let _that = this;
          this.timeoutVerificationTimerId = setInterval(() => {
            if (!_that.connected) {
              console.log('====================连接超时======================');
              uni.closeBluetoothAdapter({
                success(res) {
                  console.log("closeBluetoothAdapter :>> ");
                  // resolve(res);
                },
                fail: function (err) {
                  console.error("关闭蓝牙适配器失败", err);
                  // reject(err);
                },
              });
              _that.hasConnectingDevice = false;
            }
            if (_that.timeoutVerificationTimerId) {
              clearInterval(_that.timeoutVerificationTimerId);
              _that.timeoutVerificationTimerId = null;
            }
          }, 10000)
      }

      return createBLE(item, mainServiceNodeUUID, isSubDevice)
        .then((res) => {
          console.log("2. 选中蓝牙并连接 res :>> ", res);
          create = false;
          connBLESuc = 0;

          if (isSubDevice) {
            setTimeout(() => {
              this.subDevicesConnStateMap.set(item.deviceId, true);
            }, 150);
            return;
          }

          uni.showLoading({
            title: this.other.sucWait,
            // title: "连接成功,请稍等",
            icon: "loading",
            mask: true,
          });
          uni.hideLoading();
          setTimeout(() => {
            uni.showToast({
              title: "连接成功",
            });
            // 关闭底部蓝牙列表弹框
            this.$refs.popup.close();

            // 从本地存储中获取已经连接过的蓝牙设备列表
            let connectedBluetoothList = uni.getStorageSync(
              this.vinCode + "connectedBluetoothList"
            );

            // 如果已经连接过的蓝牙设备列表存在，则使用它；否则，初始化一个空数组
            connectedBluetoothList = connectedBluetoothList
              ? connectedBluetoothList
              : [];

            // 从已经连接过的蓝牙设备列表中移除当前连接的蓝牙信息，预防重复添加
            connectedBluetoothList = connectedBluetoothList.filter(
              (ble) => ble.deviceId !== item.deviceId
            );

            // 将当前连接的蓝牙设备添加到已经连接过的蓝牙设备列表中
            item.mainServiceNodeUUID = mainServiceNodeUUID;
            connectedBluetoothList.unshift(item);

            // 将更新后的已经连接过的蓝牙设备列表存储回本地存储
            uni.setStorageSync(
              this.vinCode + "connectedBluetoothList",
              connectedBluetoothList
            );

            // 开启监听 notify 功能
            this.openWatchNotify(
              item.deviceId,
              this.serviceId,
              this.characteristicId
            );
            this.dynamicConnInfo = `con-ok >>> 已${
              isSilentConn ? "静默" : "手动"
            }连接至${item.localName || item.name} <<<`;
            this.connected = true;

            console.log('this.connected ------:>> ', this.connected);
            if (isSilentConn && !isSubDevice) {
              console.log('this.deviceId :>> ', this.deviceId);
              this.mainDeviceId = JSON.parse(JSON.stringify(this.deviceId));
              this.mainDeviceName = JSON.parse(JSON.stringify(this.deviceName));
              this.mainServiceId = JSON.parse(JSON.stringify(this.serviceId));
              this.mainCharacteristicId = JSON.parse(JSON.stringify(this.characteristicId));

              uni.setStorageSync("mainDeviceId", this.mainDeviceId);
              uni.setStorageSync("mainServiceId", this.mainServiceId);
              uni.setStorageSync("mainCharacteristicId", this.mainCharacteristicId);

              console.log('this.mainDeviceId :>> ', this.mainDeviceId);

              let userInfoDataPacket = padValToString(store.state.user.id) + padValToString(this.currentPhoneModel)
              console.log('40位用户信息数据包=------ :>> ', userInfoDataPacket);
              console.log('6位唯一码=------ :>> ', transformString(userInfoDataPacket));

              // 主设备连接成功后给车端发送40位用户信息数据包【用户唯一标识 + 当前手机型号】
              this.sendToBle(userInfoDataPacket);

              // 根据当前已连接的设备ID过滤出对应的设备详情信息
              let currentConnectedBleInfo = connectedBluetoothList.filter(bleInfo => bleInfo.deviceId == this.mainDeviceId)[0];
              console.log('currentConnectedBleInfo :>> ', currentConnectedBleInfo);

              if (currentConnectedBleInfo?.subDevices?.length) {
                console.log('currentConnectedBleInfo?.subDevices :>> ', currentConnectedBleInfo?.subDevices);
                let _that = this;
                // 存在从设备信息时，开启从设备静默连接定时器
                this.subDevicesConnTimerId = setInterval(() => {

                  // TODO 第二方案


                  // 初始化服务数组，安卓可以不传，但IOS要求传UUID
                  let paraServices = [];

                  // 检查当前平台是否为 iOS
                  if (uni.getSystemInfoSync().platform === 'ios') {
                    // 如果是 iOS 平台，设置服务 UUID 为 mainServiceId
                    paraServices = [this.mainServiceId];
                  }
                  uni.getConnectedBluetoothDevices({
                    services: paraServices,
                    success: function (res) {
                      console.log('subDevicesConnTimer-getConnectedBluetoothDevices :>> ', res.devices);
                      if (!res?.devices?.length) {
                        _that.connected = false;
                        if (_that.subDevicesConnStateMap?.size) {
                          Array.from(_that.subDevicesConnStateMap.keys()).forEach(subDeviceId => {
                            _that.subDevicesConnStateMap.set(subDeviceId, false);
                          });
                        }
                      } 

                      _that.connected = res.devices.find(device => device.deviceId == _that.mainDeviceId) ? true : false;

                      let connectedBluetoothList = uni.getStorageSync(this.vinCode + "connectedBluetoothList");
                      if (connectedBluetoothList?.length) {
                        let blueInfos = connectedBluetoothList.filter(blueInfo => blueInfo.deviceId == _that.mainDeviceId);
                        if (blueInfos?.length) {
                          blueInfos[0].subDevices.forEach(subDevice => {
                            _that.subDevicesConnStateMap.set(subDevice.deviceId, res.devices.find(device => device.deviceId == subDevice.deviceId));
                          });
                        }
                      }

                      // if (!_that.connected) {
                      //   _that.closeBLEConnection(_that.deviceId);
                      //   // _that.dynamicConnInfo = ">>> 设备连接已断开 <<<";
                      // }
                    },
                    fail: function (err) {
                      // 断开当前的蓝牙连接
                      // _that.closeBLEConnection(_that.deviceId);
                      _that.connected = false;
                      if (_that.subDevicesConnStateMap?.size) {
                        Array.from(_that.subDevicesConnStateMap.keys()).forEach(subDeviceId => {
                          _that.subDevicesConnStateMap.set(subDeviceId, false);
                        });
                      }
                      _that.dynamicConnInfo = "con-fail >>> 设备连接已断开 <<<";
                    },
                  });

                  currentConnectedBleInfo.subDevices.forEach(subDevice => {
                    if ("88888888" == subDevice?.deviceId) {
                      // 兼容策略子设备不需要进行连接
                      return;
                    }

                    if (!this.subDevicesConnStateMap.get(subDevice?.deviceId)) {
                      this.conn(subDevice, true, true);
                    }
                  });
                }, 3000);
              }

              this.linkedList.clear();
              return;
            }

            // =======================
            this.hasConnectingDevice = false;
            this.linkedList.clear();
            // =======================
          }, 150);
        })
        .catch((err) => {
          // 除了子设备连接外的任何场景出发了catch，都要标记当前连接状态为未连接。
          if (!isSubDevice) {
            this.connected = false;
          }
          create = false;

          console.error("最外层catch err :>> ", err);

          var deviceId = uni.getStorageSync("deviceId");
          var serviceId = uni.getStorageSync("serviceId");
          var characteristicId = uni.getStorageSync("characteristicId");
          console.log("catch -> deviceId :>> ", deviceId);
          console.log("catch -> serviceId :>> ", serviceId);
          console.log("catch -> characteristicId :>> ", characteristicId);
          // 如未获取到设备特征值，再次获取
          // if (!characteristicId) {
          //   getCharacteristics(deviceId, serviceId);
          // }

          if (deviceId && serviceId) {
            create = false;
            console.log("低功耗蓝牙设备已连接, 后续重试错误不必展示");
            uni.hideLoading();
            setTimeout(() => {
              if (!isSubDevice) {
                // 关闭底部蓝牙列表弹框
                this.$refs.popup.close();
  
                // 从本地存储中获取已经连接过的蓝牙设备列表
                let connectedBluetoothList = uni.getStorageSync(
                  this.vinCode + "connectedBluetoothList"
                );
  
                // 如果已经连接过的蓝牙设备列表存在，则使用它；否则，初始化一个空数组
                connectedBluetoothList = connectedBluetoothList
                  ? connectedBluetoothList
                  : [];
  
                // 从已经连接过的蓝牙设备列表中移除当前连接的蓝牙信息，预防重复添加
                connectedBluetoothList = connectedBluetoothList.filter(
                  (ble) => ble.deviceId !== item.deviceId
                );
  
                // 将当前连接的蓝牙设备添加到已经连接过的蓝牙设备列表中
                // 主服务节点UUID，用于通讯
                let mainServiceNodeUUID = this.bleUUID
                  ? this.bleUUID
                  : "0000fffd-0000-1000-8000-00805f9b1910";
  
                // 静默连接时，主服务UUID来自于缓存，而非页面用户输入
                if (isSilentConn) {
                  mainServiceNodeUUID = item.mainServiceNodeUUID || "0000fffd-0000-1000-8000-00805f9b1910";
                }
  
                item.mainServiceNodeUUID = mainServiceNodeUUID;
                connectedBluetoothList.unshift(item);
  
                // 将更新后的已经连接过的蓝牙设备列表存储回本地存储
                uni.setStorageSync(
                  this.vinCode + "connectedBluetoothList",
                  connectedBluetoothList
                );
  
                // 开启监听 notify 功能
                this.openWatchNotify(
                  item.deviceId,
                  this.serviceId,
                  this.characteristicId
                );
                this.dynamicConnInfo = `con-ok >>> 已${
                  isSilentConn ? "静默" : "手动"
                }连接至${item.localName || item.name} <<<`;
                
                // =======================
                this.hasConnectingDevice = false;
                this.linkedList.clear();
                // =======================
              } else {
                this.subDevicesConnStateMap.set(item.deviceId, true);
              }

              this.connected = true;

              console.log('this.connected ------:>> ', this.connected);
              if (isSilentConn && !isSubDevice) {
                console.log('this.deviceId :>> ', this.deviceId);
                this.mainDeviceId = JSON.parse(JSON.stringify(this.deviceId));
                this.mainDeviceName = JSON.parse(JSON.stringify(this.deviceName));
                this.mainServiceId = JSON.parse(JSON.stringify(this.serviceId));
                this.mainCharacteristicId = JSON.parse(JSON.stringify(this.characteristicId));

                uni.setStorageSync("mainDeviceId", this.mainDeviceId);
                uni.setStorageSync("mainServiceId", this.mainServiceId);
                uni.setStorageSync("mainCharacteristicId", this.mainCharacteristicId);
				
                let userInfoDataPacket = padValToString(store.state.user.id) + padValToString(this.currentPhoneModel)
                console.log('40位用户信息数据包=------ :>> ', userInfoDataPacket);
                console.log('6位唯一码=------ :>> ', transformString(userInfoDataPacket));
                
                // 主设备连接成功后给车端发送40位用户信息数据包【用户唯一标识 + 当前手机型号】
                this.sendToBle(userInfoDataPacket);

                console.log('this.mainDeviceId :>> ', this.mainDeviceId);

                // 根据当前已连接的设备ID过滤出对应的设备详情信息
                let currentConnectedBleInfo = connectedBluetoothList.filter(bleInfo => bleInfo.deviceId == this.mainDeviceId)[0];
                console.log('currentConnectedBleInfo :>> ', currentConnectedBleInfo);

                if (currentConnectedBleInfo?.subDevices?.length) {
                  // 存在从设备信息时，开启从设备静默连接定时器
                  this.subDevicesConnTimerId = setInterval(() => {
                    currentConnectedBleInfo.subDevices.forEach(subDevice => {
                      if ("88888888" == subDevice?.deviceId) {
                        // 兼容策略子设备不需要进行连接
                        return;
                      }
                      
                      if (!this.subDevicesConnStateMap.get(subDevice?.deviceId)) {
                        this.conn(subDevice, true, true);
                      }
                    });
                  }, 3000);
                }

                this.linkedList.clear();
                return;
              }
            }, 150);
            return;
          }

          // this.subDevicesConnStateMap.set(item.deviceId, false);

          // =======================
          this.hasConnectingDevice = false;
          // =======================

          if (isSilentConn) {
            // 如果静默连接，则忽略重连逻辑
            return;
          }

          setTimeout(() => {
            uni.hideLoading();
            uni.showToast({
              title: `${this.other.fail}: ${err?.errMsg}`,
              // title: "连接失败",
              icon: "none",
            });
          }, 500);

          // ========================================
          // if (connBLESuc <= 3) {
          //   console.log(
          //     "---------------重连-----------------connBLESuc :>> ",
          //     connBLESuc
          //   );
          //   uni.hideLoading();
          //   this.conn(item, false);
          //   uni.showLoading({
          //     title: `${this.other.reconnection} ${connBLESuc} / 3`,
          //     // title: "重连中...",
          //     icon: "loading",
          //     mask: true,
          //   });
          // } else {
          //   connBLESuc = 0;
          //   setTimeout(() => {
          //     uni.hideLoading();
          //     uni.showToast({
          //       title: `${this.other.fail}: ${err?.errMsg}`,
          //       // title: "连接失败",
          //       icon: "none",
          //     });
          //   }, 500);
          // }
          // ========================================
        });
      // setTimeout(() => {
      //   if (create) {
      //     console.error("没有进入回调");
      //     uni.hideLoading();
      //     setTimeout(() => {
      //       uni.showToast({
      //         title: `${this.other.fail}: 没有进入回调`,
      //         // title: "连接失败",
      //         icon: "none",
      //       });
      //     }, 500);
      //   }
      // }, 3000);
    },
  },
};
</script>

<style lang="scss">
.hid-cls {
  display: none;
}
// @import "@/common/login.scss";

page {
  /* 设置背景颜色为深蓝色 */
  // background-color: #004080;
  /* 或者使用 rgb(0, 64, 128) */

  /* 创建线性渐变，从上到下渐变，从深蓝色到浅蓝色 */
  // background-image: linear-gradient(to bottom, #004080, #66b3ff);
  /* 或者使用 rgb(0, 64, 128) 到 rgb(102, 179, 255) */

  /* 设置背景固定，不随页面滚动 */
  background-attachment: fixed;

  /* 其他样式，比如设置高度 */
  height: 70vh;
  /* 设置高度为视口高度 */
}

.container {
  .custom-navbar {
    display: flex;
    justify-content: center;
    align-items: center;
    height: 184rpx; /* 导航栏高度 */
    border-radius: 0rpx 0rpx 0rpx 0rpx;
    background-color: rgba(0, 0, 0, 0); /* 自定义背景色 */
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    z-index: 999;
  }

  .navbar-title {
    margin-top: 75rpx;
    color: #121010; /* 标题颜色 */
  }

  .page-content {
    .connected_img {
      padding: 107rpx, 0rpx;
      width: 750rpx;
      height: 826rpx;
    }

    .person-state-cls {
      color: #333333;
      font-size: 24rpx;
      font-weight: 400;
      position: relative;
      display: flex;
      align-items: center;
      justify-content: center;
      width: 30rpx;
      height: 40rpx;
    }

    .ble-connt-cls {
      color: #333333;
      font-size: 24rpx;
      font-weight: 400;
      position: relative;
      display: flex;
      align-items: center;
      justify-content: center;
      width: 165rpx;
      height: 40rpx;
    }

    .ble-operation_prompt-cls {
      color: #999999;
      font-size: 24rpx;
      font-weight: 400;
      position: absolute;
      top: 692rpx;
      left: 207rpx; 
      line-height: 48rpx;
    }

    .go-connt-ble-cls {
      width: 232rpx;
      height: 63rpx;
      border-radius: 48rpx 48rpx 48rpx 48rpx;
      border: 2rpx solid #333333;
      position: absolute;
      top: 762rpx;
      left: 259rpx; 
      padding-left: 5%;
      line-height: 63rpx;
    }

    .operation-btn-cls {
      color: #333333;
      font-size: 28rpx;
      font-weight: 400;
      line-height: 39rpx;
    }
  }
}

.input-item {
  margin-top: -120rpx;
  width: 678rpx;
  margin: 0 auto;
  background-color: rgba(0, 0, 0, 0);
  height: 45px;
  border-bottom-style: solid;
  border-bottom-width: 1rpx;
  border-bottom-color: #999999;

  .input-placeholder {
    color: #C1C1C1;
  }

  .input { 
    font-size: 28rpx;
    line-height: 39rpx;
    text-align: left;
    width: 100%;
  }

}

.vin-code-prompt-cls {
  width: 678rpx;
  margin: 20px auto;
  font-size: 24rpx;
  line-height: 42rpx;
  font-weight: 400;
  color: #767676;
}

.binding-vehicles-btn-cls {
  width: 678rpx;
  border-style: solid;
  border-width: 2rpx;
  border-color: #333333;
  margin: 0 auto;
  font-size: 32rpx;
  line-height: 45rpx;
  font-weight: 400;
  color: #333333;
  margin-top: 300rpx;
}

.bg_img {
  width: 100%;
  height: 100%;
  top: 0;
  position: fixed;
}

.page-content::before {
  content: "";
  position: absolute;
  top: 0;
  left: 50%;
  width: 100%;
  height: 1px;
  background: rgba(255, 255, 255, 0.3);
  /* 白色系 */
  transform: translateX(-50%);
  z-index: -1;
  filter: blur(2px);
}

.page-content {
  position: relative;
  touch-action: none;
  padding-top: 8upx;
  height: 100%;

  .imgBox {
    height: 278upx;
    width: 278upx;
    margin: auto;
    margin-bottom: 100upx;

    image {
      width: 100%;
      height: 100%;
    }
  }

  .input {
    input {
      padding: 29upx 0;
      padding-left: 12upx;
      border-bottom: 1upx solid #fff;
      font-size: 30upx;
      color: #fff;
    }

    .changePwd {
      width: 100%;
      text-align: right;
      margin-top: 35upx;
      color: #fff;
      font-size: 30upx;
      margin-bottom: 61upx;
    }
  }
}

.login-but {
  font-size: 30rpx;
  width: 200rpx;
  height: 65rpx;
  margin: 0 auto;
  border: 1px solid #ffffff;
  border-radius: 45upx;
  line-height: 65rpx;
  color: #fff;

  & + .login-but {
    // margin-top: 40rpx;
  }
}

.pop {
  max-height: 800rpx;
  min-height: 800rpx;

  .type1 {
    width: 100%;
    line-height: 90rpx;
    height: 90rpx;
    padding-left: 20rpx;
    border-bottom: 1rpx solid #eee;
  }
}

.popNone {
  min-height: 300rpx;
  line-height: 300rpx;
}

.btn_hover {
  background: rgba($color: #999, $alpha: 0.5);
}

.iconfont-active {
  // background: rgba($color: #999, $alpha: 0.5);
  // background-color: #eee!important;
  // background: rgba($color: rgb(255, 255, 255), $alpha: 0.5);
  border: 1rpx solid blue!important;
  box-shadow: 2px 2px 1px 0px rgba(94, 92, 92, 0.75);
}

.connInfoCls {
  white-space: pre-wrap;
  margin-bottom: -20rpx;
  display: flex;
  justify-content: center;
  color: rgb(28, 27, 27);
  font-size: 12px;
}

.dynamicConnInfoCls {
  margin-top: 20rpx;
  white-space: pre-wrap;
  display: flex;
  justify-content: center;
  color: rgb(28, 27, 27);
  font-size: 12px;
}

.replyMessageCls {
  color: rgb(28, 27, 27);
  background-color: green;
  padding: 2rpx 10rpx;
  text-align: left;
}

.bleSendInfoCls {
  color: green;
  background-color: white;
  padding: 2rpx 10rpx;
  text-align: left;
}

.autoNotifyCls {
  color: yellow !important;
  background-color: black !important;
}

.bluetooth-for-sending-and-receiving-messages {
  text-align: center;
  padding-bottom: 20rpx;
}

.coui-icon {
  width: 89rpx;
  height: 89rpx;
  margin-right: 10rpx;
}

.align-center {
  display: flex;
  align-items: center;
}


.picker {
  position: relative; /* Positioning context for the pseudo-element */
  padding: 10px;
  text-align: center;
  // background-color: #fff; /* Background color for content */
  z-index: 1; /* Ensure content is above the pseudo-element */
  border: 1px solid transparent; /* Transparent border to account for the pseudo-element */
}

.picker::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  border: 1px solid;
  border-image: radial-gradient(circle, rgba(75, 73, 73, 0.5) 0%, rgba(75, 73, 73, 0) 100%) 1; /* 从中心向外渐变 */
  border-image-slice: 1;
  z-index: -1;
}

.ble-status-container {
  position: absolute;
  top: 180rpx;
  right: 40rpx;
  display: flex;
  z-index: 100;
}

.status-icons-container {
  display: flex;
  align-items: center;
}

.person-warning-container {
  position: fixed;
  top: 160rpx;
  left: 25%;
  transform: translateX(-50%);
  display: flex;
  z-index: 1000;
  align-items: center;
  padding: 15rpx 30rpx;
  background-color: rgba(255, 0, 0, 0.8);
  border-radius: 10rpx;
  box-shadow: 0 0 10rpx rgba(0, 0, 0, 0.3);

  .warning-icon {
    font-size: 36rpx;
    margin-right: 10rpx;
    color: #fff;
  }

  .warning-text {
    font-size: 32rpx;
    font-weight: bold;
    color: #fff;
  }
}

.warning-flash {
  animation: flash 0.8s linear infinite;
}

@keyframes flash {
  0%, 100% { opacity: 1; background-color: rgba(255, 0, 0, 0.8); }
  50% { opacity: 0.8; background-color: rgba(255, 50, 50, 0.6); }
}

/* 权限状态样式 */
.permission-status-container {
  margin: 20rpx;
  padding: 20rpx;
  background: rgba(255, 255, 255, 0.9);
  border-radius: 15rpx;
  backdrop-filter: blur(10rpx);
}

.permission-item {
  display: flex;
  align-items: center;
  padding: 15rpx;
  border-radius: 10rpx;
  background: linear-gradient(135deg, #4CAF50, #45a049);
  margin-bottom: 15rpx;

  &.permission-restricted {
    background: linear-gradient(135deg, #f44336, #d32f2f);
  }

  .permission-icon {
    font-size: 40rpx;
    margin-right: 15rpx;
  }

  .permission-info {
    flex: 1;

    .permission-status {
      display: block;
      font-size: 28rpx;
      font-weight: bold;
      color: white;
      margin-bottom: 5rpx;
    }

    .permission-expire {
      font-size: 24rpx;
      color: rgba(255, 255, 255, 0.8);
    }
  }

  .permission-indicator {
    font-size: 32rpx;

    &.restricted {
      animation: warning-pulse 1.5s ease-in-out infinite;
    }
  }
}

.restriction-notice {
  padding: 15rpx;
  background: rgba(255, 193, 7, 0.1);
  border: 1rpx solid #FFC107;
  border-radius: 8rpx;
  margin-bottom: 15rpx;

  .notice-text {
    font-size: 26rpx;
    color: #FF8F00;
    line-height: 1.4;
  }
}

.expire-warning {
  padding: 15rpx;
  background: rgba(255, 152, 0, 0.1);
  border: 1rpx solid #FF9800;
  border-radius: 8rpx;

  .warning-text {
    font-size: 26rpx;
    color: #F57C00;
    line-height: 1.4;
  }
}

@keyframes warning-pulse {
  0%, 100% {
    transform: scale(1);
    opacity: 1;
  }
  50% {
    transform: scale(1.1);
    opacity: 0.8;
  }
}
</style>

package com.ruoyi.dk.mapper;

import java.util.List;
import com.ruoyi.dk.domain.DigitalKeyShareDetail;

/**
 * 车辆数字钥匙分享详情Mapper接口
 * 
 * <AUTHOR>
 * @date 2024-08-12
 */
public interface DigitalKeyShareDetailMapper 
{
    /**
     * 查询车辆数字钥匙分享详情
     * 
     * @param detailId 车辆数字钥匙分享详情主键
     * @return 车辆数字钥匙分享详情
     */
    public DigitalKeyShareDetail selectDigitalKeyShareDetailByDetailId(Long detailId);

    /**
     * 查询车辆数字钥匙分享详情列表
     * 
     * @param digitalKeyShareDetail 车辆数字钥匙分享详情
     * @return 车辆数字钥匙分享详情集合
     */
    public List<DigitalKeyShareDetail> selectDigitalKeyShareDetailList(DigitalKeyShareDetail digitalKeyShareDetail);

    /**
     * 新增车辆数字钥匙分享详情
     * 
     * @param digitalKeyShareDetail 车辆数字钥匙分享详情
     * @return 结果
     */
    public int insertDigitalKeyShareDetail(DigitalKeyShareDetail digitalKeyShareDetail);

    /**
     * 修改车辆数字钥匙分享详情
     * 
     * @param digitalKeyShareDetail 车辆数字钥匙分享详情
     * @return 结果
     */
    public int updateDigitalKeyShareDetail(DigitalKeyShareDetail digitalKeyShareDetail);

    /**
     * 删除车辆数字钥匙分享详情
     * 
     * @param detailId 车辆数字钥匙分享详情主键
     * @return 结果
     */
    public int deleteDigitalKeyShareDetailByDetailId(Long detailId);

    /**
     * 批量删除车辆数字钥匙分享详情
     * 
     * @param detailIds 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteDigitalKeyShareDetailByDetailIds(Long[] detailIds);
}

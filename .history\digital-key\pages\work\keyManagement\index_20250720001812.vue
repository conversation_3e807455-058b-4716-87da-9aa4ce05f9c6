<template>
  <view class="container">
    <!-- 自定义导航栏 -->
    <cu-custom bgColor="bg-gradual-blue" :isBack="false">
      <block slot="content">钥匙管理</block>
    </cu-custom>

    <!-- 标签页切换 -->
    <view class="tab-container">
      <view class="tab-header">
        <view 
          class="tab-item" 
          :class="{ active: activeTab === 'keys' }"
          @click="switchTab('keys')"
        >
          我的钥匙
        </view>
        <view 
          class="tab-item" 
          :class="{ active: activeTab === 'applications' }"
          @click="switchTab('applications')"
        >
          申请记录
        </view>
      </view>
    </view>

    <!-- 我的钥匙标签页 -->
    <view v-if="activeTab === 'keys'" class="tab-content">
      <!-- 加载状态 -->
      <view v-if="keysLoading" class="loading-container">
        <uni-load-more status="loading" />
      </view>

      <!-- 无钥匙记录 -->
      <view v-else-if="keysList.length === 0" class="empty-container">
        <view class="empty-icon">🔑</view>
        <text class="empty-text">暂无可用钥匙</text>
        <text class="empty-desc">请先提交申请，等待管理员分配钥匙</text>
        <button class="cu-btn bg-blue round margin-top" @click="goToApply">立即申请</button>
      </view>

      <!-- 钥匙列表 -->
      <view v-else class="keys-list">
        <view 
          v-for="(item, index) in keysList" 
          :key="index"
          class="key-item"
          @click="viewKeyDetail(item)"
        >
          <!-- 钥匙状态标识 -->
          <view class="key-header" :class="[getKeyStatusClass(item.status)]">
            <view class="key-info">
              <text class="key-title">{{ item.vehicleBrand }} {{ item.vehicleModel }}</text>
              <text class="key-status">{{ getKeyStatusText(item.status) }}</text>
            </view>
            <view class="key-icon">
              <text class="cuIcon-key"></text>
            </view>
          </view>

          <!-- 车辆信息 -->
          <view class="vehicle-info">
            <view class="info-row">
              <text class="info-label">车牌号：</text>
              <text class="info-value">{{ item.licensePlate || '暂无' }}</text>
            </view>
            <view class="info-row">
              <text class="info-label">VIN码：</text>
              <text class="info-value">{{ item.vinCode || '暂无' }}</text>
            </view>
          </view>

          <!-- 使用期限 -->
          <view class="validity-info">
            <view class="info-row">
              <text class="info-label">有效期：</text>
              <text class="info-value">
                {{ formatTimeFilter(item.validStartTime) }} 至 {{ formatTimeFilter(item.validEndTime) }}
              </text>
            </view>
            <view class="info-row">
              <text class="info-label">分配时间：</text>
              <text class="info-value">{{ formatTimeFilter(item.assignmentTime) }}</text>
            </view>
          </view>

          <!-- 操作按钮 -->
          <view class="key-actions">
            <button
              v-if="item.status === '1'"
              class="cu-btn bg-green sm round"
              @click.stop="useKey(item)"
            >
              使用钥匙
            </button>
          </view>
        </view>
      </view>
    </view>

    <!-- 申请记录标签页 -->
    <view v-if="activeTab === 'applications'" class="tab-content">
      <!-- 加载状态 -->
      <view v-if="applicationsLoading" class="loading-container">
        <uni-load-more status="loading" />
      </view>

      <!-- 无申请记录 -->
      <view v-else-if="applicationsList.length === 0" class="empty-container">
        <view class="empty-icon">📋</view>
        <text class="empty-text">暂无申请记录</text>
        <button class="cu-btn bg-blue round margin-top" @click="goToApply">立即申请</button>
      </view>

      <!-- 申请记录列表 -->
      <view v-else class="applications-list">
        <view 
          v-for="(item, index) in applicationsList" 
          :key="index"
          class="application-item"
          @click="viewApplicationDetail(item)"
        >
          <!-- 申请状态标识 -->
          <view class="status-header" :class="[getStatusClass(item.status)]">
            <view class="status-info">
              <text class="status-text">{{ getStatusText(item.status) }}</text>
              <text class="application-no">申请单号：{{ item.applicationNo }}</text>
            </view>
            <view class="status-icon">
              <text :class="[getStatusIcon(item.status)]"></text>
            </view>
          </view>

          <!-- 申请信息 -->
          <view class="application-info">
            <view class="info-row">
              <text class="info-label">申请时间：</text>
              <text class="info-value">{{ formatTimeFilter(item.createTime) }}</text>
            </view>
            <view v-if="item.auditTime" class="info-row">
              <text class="info-label">审核时间：</text>
              <text class="info-value">{{ formatTimeFilter(item.auditTime) }}</text>
            </view>
          </view>

          <!-- 操作按钮 -->
          <view class="action-buttons">
            <button
              v-if="item.status === '0'"
              class="cu-btn line-red sm round"
              @click.stop="withdrawApplication(item)"
            >
              撤销申请
            </button>
            <button
              v-if="item.status === '3'"
              class="cu-btn bg-orange sm round"
              @click.stop="supplementApplication(item)"
            >
              补充资料
            </button>
          </view>
        </view>
      </view>
    </view>

    <!-- 浮动申请按钮 -->
    <view class="floating-button" @click="goToApply">
      <text class="cuIcon-add"></text>
    </view>
  </view>
</template>

<script>
import { getUserApplicationList } from '@/api/dk.js'
import { getUserDigitalKeys } from '@/api/dk.js'

export default {
  data() {
    return {
      activeTab: 'keys', // 默认显示我的钥匙
      
      // 钥匙相关
      keysLoading: false,
      keysList: [],
      
      // 申请相关
      applicationsLoading: false,
      applicationsList: []
    }
  },
  
  onLoad() {
    this.loadData()
  },
  
  onShow() {
    this.loadData()
  },
  
  methods: {
    // 切换标签页
    switchTab(tab) {
      this.activeTab = tab
      this.loadData()
    },
    
    // 加载数据
    loadData() {
      if (this.activeTab === 'keys') {
        this.loadKeysList()
      } else {
        this.loadApplicationsList()
      }
    },
    
    // 加载钥匙列表
    async loadKeysList() {
      this.keysLoading = true
      try {
        const response = await getUserDigitalKeys()
        this.keysList = response.data || []
      } catch (error) {
        console.error('加载钥匙列表失败:', error)
        uni.showToast({
          title: '加载失败',
          icon: 'none'
        })
      } finally {
        this.keysLoading = false
      }
    },
    
    // 加载申请列表
    async loadApplicationsList() {
      this.applicationsLoading = true
      try {
        const response = await getUserApplicationList()
        this.applicationsList = response.rows || []
      } catch (error) {
        console.error('加载申请列表失败:', error)
        uni.showToast({
          title: '加载失败',
          icon: 'none'
        })
      } finally {
        this.applicationsLoading = false
      }
    },
    
    // 钥匙状态相关方法
    getKeyStatusClass(status) {
      const statusMap = {
        '1': 'status-active',
        '2': 'status-expired',
        '0': 'status-inactive'
      }
      return statusMap[status] || 'status-unknown'
    },
    
    getKeyStatusText(status) {
      const statusMap = {
        '1': '可用',
        '2': '已过期',
        '0': '未激活'
      }
      return statusMap[status] || '未知'
    },
    
    // 申请状态相关方法
    getStatusClass(status) {
      const statusMap = {
        '0': 'status-pending',
        '1': 'status-approved',
        '2': 'status-rejected',
        '3': 'status-supplement'
      }
      return statusMap[status] || 'status-unknown'
    },
    
    getStatusText(status) {
      const statusMap = {
        '0': '待审核',
        '1': '已通过',
        '2': '已拒绝',
        '3': '需补充'
      }
      return statusMap[status] || '未知'
    },
    
    getStatusIcon(status) {
      const iconMap = {
        '0': 'cuIcon-time',
        '1': 'cuIcon-check',
        '2': 'cuIcon-close',
        '3': 'cuIcon-warn'
      }
      return iconMap[status] || 'cuIcon-question'
    },
    
    // 时间格式化
    formatTimeFilter(time) {
      if (!time) return ''
      const date = new Date(time)
      return `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}-${String(date.getDate()).padStart(2, '0')} ${String(date.getHours()).padStart(2, '0')}:${String(date.getMinutes()).padStart(2, '0')}`
    },
    
    // 操作方法
    goToApply() {
      uni.navigateTo({
        url: '/pages/work/userApplication/index'
      })
    },
    
    useKey(item) {
      // 跳转到数字钥匙使用页面
      uni.switchTab({
        url: '/pages/work/digitalKey/index'
      })
    },
    
    viewVehicleLocation(item) {
      uni.showToast({
        title: '查看车辆位置功能开发中',
        icon: 'none'
      })
    },
    
    viewKeyDetail(item) {
      // 查看钥匙详情
      console.log('查看钥匙详情:', item)
    },
    
    viewApplicationDetail(item) {
      // 查看申请详情
      console.log('查看申请详情:', item)
    },
    
    withdrawApplication(item) {
      uni.showModal({
        title: '确认撤销',
        content: '确定要撤销这个申请吗？',
        success: (res) => {
          if (res.confirm) {
            // 调用撤销申请接口
            uni.showToast({
              title: '撤销成功',
              icon: 'success'
            })
            this.loadApplicationsList()
          }
        }
      })
    },
    
    supplementApplication(item) {
      uni.navigateTo({
        url: `/pages/work/userApplication/supplement?id=${item.applicationId}`
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.container {
  min-height: 100vh;
  background-color: #f5f5f5;
}

.tab-container {
  background-color: white;
  border-bottom: 1px solid #eee;
}

.tab-header {
  display: flex;
  height: 88rpx;
}

.tab-item {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 32rpx;
  color: #666;
  position: relative;

  &.active {
    color: #007aff;
    font-weight: 500;

    &::after {
      content: '';
      position: absolute;
      bottom: 0;
      left: 50%;
      transform: translateX(-50%);
      width: 60rpx;
      height: 4rpx;
      background-color: #007aff;
      border-radius: 2rpx;
    }
  }
}

.tab-content {
  padding: 20rpx;
}

.loading-container {
  padding: 100rpx 0;
  text-align: center;
}

.empty-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 120rpx 40rpx;
  text-align: center;
}

.empty-icon {
  font-size: 120rpx;
  margin-bottom: 30rpx;
}

.empty-text {
  font-size: 32rpx;
  color: #666;
  margin-bottom: 20rpx;
}

.empty-desc {
  font-size: 28rpx;
  color: #999;
  margin-bottom: 40rpx;
}

// 钥匙列表样式
.keys-list {
  .key-item {
    background-color: white;
    border-radius: 16rpx;
    margin-bottom: 20rpx;
    overflow: hidden;
    box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.1);
  }

  .key-header {
    padding: 30rpx;
    display: flex;
    align-items: center;
    justify-content: space-between;

    &.status-active {
      background: linear-gradient(135deg, #4CAF50, #45a049);
      color: white;
    }

    &.status-expired {
      background: linear-gradient(135deg, #f44336, #d32f2f);
      color: white;
    }

    &.status-inactive {
      background: linear-gradient(135deg, #9e9e9e, #757575);
      color: white;
    }
  }

  .key-info {
    flex: 1;
  }

  .key-title {
    font-size: 36rpx;
    font-weight: 500;
    display: block;
    margin-bottom: 10rpx;
  }

  .key-status {
    font-size: 28rpx;
    opacity: 0.9;
  }

  .key-icon {
    font-size: 48rpx;
  }

  .vehicle-info, .validity-info {
    padding: 20rpx 30rpx;
    border-bottom: 1px solid #f0f0f0;
  }

  .key-actions {
    padding: 20rpx 30rpx;
    display: flex;
    gap: 20rpx;
  }
}

// 申请列表样式
.applications-list {
  .application-item {
    background-color: white;
    border-radius: 16rpx;
    margin-bottom: 20rpx;
    overflow: hidden;
    box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.1);
  }

  .status-header {
    padding: 30rpx;
    display: flex;
    align-items: center;
    justify-content: space-between;

    &.status-pending {
      background: linear-gradient(135deg, #ff9800, #f57c00);
      color: white;
    }

    &.status-approved {
      background: linear-gradient(135deg, #4CAF50, #45a049);
      color: white;
    }

    &.status-rejected {
      background: linear-gradient(135deg, #f44336, #d32f2f);
      color: white;
    }

    &.status-supplement {
      background: linear-gradient(135deg, #2196F3, #1976D2);
      color: white;
    }
  }

  .status-info {
    flex: 1;
  }

  .status-text {
    font-size: 36rpx;
    font-weight: 500;
    display: block;
    margin-bottom: 10rpx;
  }

  .application-no {
    font-size: 28rpx;
    opacity: 0.9;
  }

  .status-icon {
    font-size: 48rpx;
  }

  .application-info {
    padding: 20rpx 30rpx;
    border-bottom: 1px solid #f0f0f0;
  }

  .action-buttons {
    padding: 20rpx 30rpx;
    display: flex;
    gap: 20rpx;
  }
}

// 通用信息行样式
.info-row {
  display: flex;
  align-items: center;
  margin-bottom: 16rpx;

  &:last-child {
    margin-bottom: 0;
  }
}

.info-label {
  font-size: 28rpx;
  color: #666;
  min-width: 140rpx;
}

.info-value {
  font-size: 28rpx;
  color: #333;
  flex: 1;
}

// 浮动按钮
.floating-button {
  position: fixed;
  right: 40rpx;
  bottom: 120rpx;
  width: 120rpx;
  height: 120rpx;
  background: linear-gradient(135deg, #007aff, #0056cc);
  border-radius: 60rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 8rpx 24rpx rgba(0, 122, 255, 0.3);
  z-index: 999;

  .cuIcon-add {
    font-size: 48rpx;
    color: white;
  }
}
</style>

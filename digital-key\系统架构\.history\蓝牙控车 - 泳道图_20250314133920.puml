@startuml
skinparam monochrome true

participant "APP(后台运行)" as Mobile
participant "车端（BLE & TBOX）" as CarBT
participant "钥匙云平台" as Cloud
participant "TSP/OEM平台" as ExternalSystem

== 无感连接建立 ==

note over Mobile, CarBT
  无感连接是指手机在不需要用户主动操作的情况下，
  自动与车辆建立蓝牙连接并执行预设操作的功能。
  前提是APP已在手机后台运行，且已获得必要权限。
end note

CarBT -> CarBT : 1. 车辆定期发送特殊格式的蓝牙广播
note right of CarBT
  广播内容包含:
  - 加密的车辆标识
  - 广播序列号(防重放)
  - 车辆当前状态摘要
end note

Mobile -> Mobile : 2a. 系统唤醒后台APP进行周期性扫描
Mobile -> Mobile : 2b. 检查是否满足扫描条件
note right of Mobile
  扫描条件包括:
  - 用户已启用无感连接
  - 当前位置接近常用停车位置
  - 当前时间在用户常用时段
  - 手机电量充足
  - 检测到用户走路状态
end note

Mobile -> Mobile : 3. 开始低功耗蓝牙扫描
Mobile -> CarBT : 4. 接收并解析车辆广播信号
Mobile -> Mobile : 5. 计算与车辆的距离(基于RSSI)

alt 距离达到触发阈值
    Mobile -> Mobile : 6a. 从安全区域读取车辆信息和会话密钥
    Mobile -> Mobile : 6b. 准备连接参数
    note right of Mobile
      连接参数包含:
      - 设备唯一标识
      - 时间戳
      - 上次连接会话信息
      - 随机挑战数
    end note
    
    Mobile -> CarBT : 7. 自动发起蓝牙连接请求
    
    CarBT -> CarBT : 8a. 验证连接请求来源
    CarBT -> CarBT : 8b. 检查设备是否在授权列表
    CarBT -> CarBT : 8c. 检查是否满足自动接受条件
    note right of CarBT
      自动接受条件:
      - 设备在授权列表中
      - 车辆处于安全状态
      - 未触发安全警报
      - 电量状态正常
    end note
    
    CarBT -> Mobile : 8d. 接受连接请求
    
    == 安全认证过程 ==
    
    Mobile -> CarBT : 9. 发送身份验证请求（包含设备ID和时间戳）
    CarBT -> CarBT : 10a. 从安全区域读取该设备的会话密钥
    CarBT -> Mobile : 10b. 发送认证挑战（随机数）
    
    Mobile -> Mobile : 11a. 使用会话密钥计算认证响应
    Mobile -> CarBT : 11b. 发送认证响应
    
    CarBT -> CarBT : 12a. 验证认证响应
    CarBT -> Mobile : 12b. 发送认证结果
    
    note over Mobile, CarBT
      双向认证确保:
      1. 手机确实是已配对的授权设备
      2. 车辆确实是用户的车辆
      3. 防止中间人攻击和重放攻击
    end note
    
    == 快速安全通道建立 ==
    
    Mobile <-> CarBT : 13. 快速建立加密安全通道
    note over Mobile, CarBT
      使用存储的会话密钥快速建立安全通道,
      无需完整的密钥协商过程
    end note
    
    == 车辆状态同步 ==
    
    CarBT -> Mobile : 14. 发送车辆当前状态信息
    note right of CarBT
      - 车门锁状态
      - 车窗状态
      - 发动机状态
      - 电池电量
      - 其他车辆信息
    end note
    
    Mobile -> Mobile : 15. 后台更新车辆状态缓存
    
    == 自动控车执行 ==
    
    Mobile -> Mobile : 16a. 根据用户预设策略准备自动控车指令
    note right of Mobile
      可能的自动控车指令:
      - 车门解锁
      - 启动空调
      - 调整座椅位置
      - 个性化车内设置
    end note
    
    Mobile -> Mobile : 16b. 生成控车指令并签名
    note right of Mobile
      指令包含:
      - 操作类型
      - 时间戳
      - 随机数
      - 数字签名
    end note
    
    Mobile -> CarBT : 17. 发送加密的控车指令
    
    CarBT -> CarBT : 18a. 解密并验证指令
    CarBT -> CarBT : 18b. 检查用户权限
    CarBT -> CarBT : 18c. 检查指令时效性（防重放）
    
    alt 指令验证通过
        CarBT -> CarBT : 19a. 执行控车操作
        CarBT -> Mobile : 19b. 返回操作执行结果
        CarBT -> Cloud : 19c. 上报操作记录（可选，根据网络状态）
        Mobile -> Mobile : 19d. 生成系统通知提醒用户操作已执行
    else 指令验证失败
        CarBT -> Mobile : 20a. 返回验证失败信息
        Mobile -> Mobile : 20b. 记录失败原因，不打扰用户
    end
    
    == 持续状态监控 ==
    
    CarBT -> CarBT : 21. 监控车辆状态变化
    alt 状态发生变化
        CarBT -> Mobile : 22a. 推送状态变化通知
        Mobile -> Mobile : 22b. 更新后台缓存状态
    end
    
    == 连接保持与心跳机制 ==
    
    Mobile <-> CarBT : 23. 定期发送心跳包保持连接
    note over Mobile, CarBT
      心跳机制:
      1. 检测连接是否正常
      2. 防止连接超时断开
      3. 定期更新车辆状态
    end note
    
    alt 心跳超时
        Mobile -> Mobile : 24a. 检测到连接异常
        Mobile -> Mobile : 24b. 尝试重新连接
        alt 重连失败
            Mobile -> Mobile : 24c. 记录连接失败，不打扰用户
        end
    end
    
    == 自动断开连接 ==
    
    alt 用户远离车辆
        Mobile -> Mobile : 25a. 检测到用户远离车辆(RSSI信号减弱)
        Mobile -> Mobile : 25b. 准备断开连接前的操作
        note right of Mobile
          可能的断开前操作:
          - 发送车门上锁指令
          - 关闭车窗
          - 其他安全措施
        end note
        
        Mobile -> CarBT : 25c. 发送最终控车指令(如上锁)
        CarBT -> CarBT : 25d. 执行最终控车操作
        CarBT -> Mobile : 25e. 返回操作结果
        
        Mobile -> CarBT : 26a. 发送断开连接请求
        CarBT -> CarBT : 26b. 清理临时会话资源
        Mobile -> Mobile : 26c. 清理临时会话资源
        Mobile -> Mobile : 26d. 生成系统通知提醒用户车辆已锁定
    else 超出连接范围
        Mobile -> Mobile : 27a. 检测到信号丢失
        Mobile -> Mobile : 27b. 记录连接断开状态
        CarBT -> CarBT : 27c. 检测到连接断开，清理临时会话资源
    end
else 距离未达到触发阈值
    Mobile -> Mobile : 28. 继续周期性扫描，进入低功耗模式
end

== 无感连接优化策略 ==

note over Mobile
  手机端优化策略:
  1. 系统级唤醒 - 利用操作系统提供的后台唤醒机制
  2. 上下文感知扫描 - 根据位置、时间、活动状态调整扫描频率
  3. 电量自适应 - 根据手机电量调整扫描策略
  4. 学习优化 - 记录用户习惯，优化扫描策略
  5. 多因素触发 - 综合多种因素决定是否发起连接
  6. 防误触机制 - 避免意外连接
end note

note over CarBT
  车端优化策略:
  1. 智能广播 - 根据环境和状态调整广播参数
  2. 安全广播 - 确保广播内容安全
  3. 安全优先 - 严格验证连接请求
  4. 状态感知 - 根据车辆状态调整连接策略
  5. 异常防护 - 检测并防范异常连接
  6. 权限分级 - 不同操作需要不同级别的权限
end note

== 异常处理 ==

note over Mobile, CarBT
  无感连接过程中可能出现的问题及处理方式:
end note

alt 认证失败
    CarBT -> Mobile : 返回认证失败信息
    Mobile -> Mobile : 记录认证失败，下次尝试完整连接流程
    Mobile -> Cloud : 上报认证异常（如果有网络）
else 指令执行超时
    Mobile -> Mobile : 检测到指令执行长时间无响应
    Mobile -> Mobile : 记录操作超时
    Mobile -> Mobile : 尝试重新发送指令
else 车辆状态异常
    CarBT -> Mobile : 返回车辆状态异常信息
    Mobile -> Mobile : 记录异常状态
    Mobile -> Cloud : 上报车辆异常（如果有网络）
end

== 安全保障措施 ==

note over Mobile, CarBT
  无感连接安全保障措施:
  1. 所有通信均经过加密
  2. 指令包含时间戳和随机数防重放
  3. 关键操作需要额外验证
  4. 异常情况自动处理
  5. 定期更新会话密钥
  6. 距离阈值严格控制
  7. 多因素验证机制
  8. 用户可随时通过APP设置禁用无感连接
end note

@enduml

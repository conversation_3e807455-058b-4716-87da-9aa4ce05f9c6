package com.ruoyi.common.redis.configure;

import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;
import org.springframework.data.redis.core.RedisTemplate;

/**
 * Redis配置 - Mock实现
 * 创建一个Mock RedisTemplate Bean，避免依赖注入错误
 *
 * <AUTHOR>
 */
@Configuration
public class RedisConfig {

    public RedisConfig() {
        System.out.println("✅ RedisConfig Mock配置已加载，创建Mock RedisTemplate Bean");
    }

    /**
     * 创建一个Mock RedisTemplate Bean
     * 这个Bean不会被实际使用，因为我们的RedisService使用内存实现
     */
    @Bean
    @Primary
    public RedisTemplate<Object, Object> redisTemplate() {
        System.out.println("⚠️  创建Mock RedisTemplate Bean（不会被实际使用）");
        return new RedisTemplate<>();
    }
}

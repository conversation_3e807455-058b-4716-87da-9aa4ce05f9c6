<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>4G控车系统</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            justify-content: center;
            align-items: center;
        }

        .container {
            background: white;
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            padding: 40px;
            max-width: 600px;
            width: 90%;
        }

        .header {
            text-align: center;
            margin-bottom: 30px;
        }

        .header h1 {
            color: #333;
            font-size: 2.5em;
            margin-bottom: 10px;
        }

        .header p {
            color: #666;
            font-size: 1.1em;
        }

        .status-panel {
            background: #f8f9fa;
            border-radius: 15px;
            padding: 20px;
            margin-bottom: 30px;
            border-left: 5px solid #28a745;
        }

        .status-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 10px;
        }

        .status-item:last-child {
            margin-bottom: 0;
        }

        .status-label {
            font-weight: bold;
            color: #333;
        }

        .status-value {
            color: #666;
            font-family: monospace;
        }

        .control-panel {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
            margin-bottom: 30px;
        }

        .control-btn {
            padding: 20px;
            border: none;
            border-radius: 15px;
            font-size: 1.2em;
            font-weight: bold;
            cursor: pointer;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 10px;
        }

        .unlock-btn {
            background: linear-gradient(135deg, #28a745, #20c997);
            color: white;
        }

        .unlock-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 20px rgba(40, 167, 69, 0.3);
        }

        .lock-btn {
            background: linear-gradient(135deg, #dc3545, #fd7e14);
            color: white;
        }

        .lock-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 20px rgba(220, 53, 69, 0.3);
        }

        .control-btn:disabled {
            opacity: 0.6;
            cursor: not-allowed;
            transform: none !important;
        }

        .message-panel {
            background: #f8f9fa;
            border-radius: 15px;
            padding: 20px;
            max-height: 300px;
            overflow-y: auto;
            border: 1px solid #dee2e6;
        }

        .message-item {
            padding: 10px;
            margin-bottom: 10px;
            border-radius: 8px;
            border-left: 4px solid #007bff;
            background: white;
        }

        .message-item.success {
            border-left-color: #28a745;
            background: #d4edda;
        }

        .message-item.error {
            border-left-color: #dc3545;
            background: #f8d7da;
        }

        .message-item.info {
            border-left-color: #17a2b8;
            background: #d1ecf1;
        }

        .message-time {
            font-size: 0.8em;
            color: #666;
            margin-bottom: 5px;
        }

        .message-content {
            font-weight: bold;
        }

        .connection-status {
            display: inline-block;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-right: 8px;
        }

        .connection-status.connected {
            background: #28a745;
            animation: pulse 2s infinite;
        }

        .connection-status.disconnected {
            background: #dc3545;
        }

        @keyframes pulse {
            0% { opacity: 1; }
            50% { opacity: 0.5; }
            100% { opacity: 1; }
        }

        .refresh-btn {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 8px;
            cursor: pointer;
            font-size: 0.9em;
            margin-left: 10px;
        }

        .refresh-btn:hover {
            background: #0056b3;
        }

        @media (max-width: 768px) {
            .container {
                padding: 20px;
                margin: 20px;
            }

            .control-panel {
                grid-template-columns: 1fr;
            }

            .header h1 {
                font-size: 2em;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>� TBOX设备模拟器</h1>
            <p>模拟TBOX设备与云平台的TCP通信</p>
        </div>

        <div class="status-panel">
            <div class="status-item">
                <span class="status-label">TCP连接状态:</span>
                <span class="status-value">
                    <span id="connectionStatus" class="connection-status disconnected"></span>
                    <span id="connectionText">未连接</span>
                </span>
            </div>
            <div class="status-item">
                <span class="status-label">TBOX设备ID:</span>
                <span class="status-value" id="tboxId">TBOX_001</span>
            </div>
            <div class="status-item">
                <span class="status-label">最后更新时间:</span>
                <span class="status-value" id="lastUpdate">--</span>
                <button class="refresh-btn" onclick="refreshStatus()">刷新</button>
            </div>
        </div>

        <div class="control-panel">
            <button id="connectBtn" class="control-btn unlock-btn" onclick="connectToServer()">
                � 连接云平台
            </button>
            <button id="heartbeatBtn" class="control-btn lock-btn" onclick="sendHeartbeat()" disabled>
                💓 发送心跳
            </button>
            <button id="statusBtn" class="control-btn" onclick="sendStatusReport()" disabled>
                � 上报状态
            </button>
        </div>

        <div class="message-panel">
            <h3 style="margin-bottom: 15px; color: #333;">📨 消息日志</h3>
            <div id="messageContainer">
                <div class="message-item info">
                    <div class="message-time">系统启动</div>
                    <div class="message-content">TBOX设备模拟器已初始化，等待连接云平台...</div>
                </div>
            </div>
        </div>
    </div>

    <script>
        let websocket = null;
        let isConnected = false;

        // 初始化WebSocket连接
        function initWebSocket() {
            const protocol = window.location.protocol === 'https:' ? 'wss:' : 'ws:';
            // 添加userId参数，4G控车系统使用固定的用户ID
            const wsUrl = `${protocol}//${window.location.host}/websocket/message?userId=1`;

            try {
                websocket = new WebSocket(wsUrl);
                
                websocket.onopen = function(event) {
                    isConnected = true;
                    updateConnectionStatus(true);
                    addMessage('success', 'WebSocket连接成功');
                    refreshStatus();
                };
                
                websocket.onmessage = function(event) {
                    try {
                        const message = JSON.parse(event.data);
                        handleWebSocketMessage(message);
                    } catch (e) {
                        addMessage('info', '收到消息: ' + event.data);
                    }
                };
                
                websocket.onclose = function(event) {
                    isConnected = false;
                    updateConnectionStatus(false);
                    addMessage('error', 'WebSocket连接已断开');
                    
                    // 5秒后尝试重连
                    setTimeout(initWebSocket, 5000);
                };
                
                websocket.onerror = function(error) {
                    addMessage('error', 'WebSocket连接错误');
                    console.error('WebSocket error:', error);
                };
                
            } catch (e) {
                addMessage('error', 'WebSocket初始化失败: ' + e.message);
                console.error('WebSocket init error:', e);
            }
        }

        // 处理WebSocket消息
        function handleWebSocketMessage(message) {
            if (message.senderName === '4G控车系统') {
                if (message.message.includes('错误')) {
                    addMessage('error', message.message);
                } else {
                    addMessage('success', message.message);
                }
            } else if (message.senderName === 'TBOX') {
                addMessage('info', message.message);
            } else {
                addMessage('info', `${message.senderName}: ${message.message}`);
            }
        }

        // 发送解锁指令
        function sendUnlockCommand() {
            if (!isConnected) {
                addMessage('error', '请先连接WebSocket');
                return;
            }
            
            const message = {
                messageType: 4,
                message: 'unlock',
                senderName: '用户',
                senderId: 1
            };
            
            websocket.send(JSON.stringify(message));
            addMessage('info', '发送解锁指令...');
        }

        // 发送闭锁指令
        function sendLockCommand() {
            if (!isConnected) {
                addMessage('error', '请先连接WebSocket');
                return;
            }
            
            const message = {
                messageType: 4,
                message: 'lock',
                senderName: '用户',
                senderId: 1
            };
            
            websocket.send(JSON.stringify(message));
            addMessage('info', '发送闭锁指令...');
        }

        // 刷新状态
        function refreshStatus() {
            fetch('/api/vehicle/status')
                .then(response => response.json())
                .then(data => {
                    if (data.code === 200) {
                        document.getElementById('tboxCount').textContent = data.data.connectedTboxCount;
                        document.getElementById('lastUpdate').textContent = new Date().toLocaleString();
                    }
                })
                .catch(error => {
                    console.error('刷新状态失败:', error);
                    addMessage('error', '刷新状态失败');
                });
        }

        // 更新连接状态
        function updateConnectionStatus(connected) {
            const statusElement = document.getElementById('connectionStatus');
            const textElement = document.getElementById('connectionText');
            const unlockBtn = document.getElementById('unlockBtn');
            const lockBtn = document.getElementById('lockBtn');
            
            if (connected) {
                statusElement.className = 'connection-status connected';
                textElement.textContent = '已连接';
                unlockBtn.disabled = false;
                lockBtn.disabled = false;
            } else {
                statusElement.className = 'connection-status disconnected';
                textElement.textContent = '未连接';
                unlockBtn.disabled = true;
                lockBtn.disabled = true;
            }
        }

        // 添加消息
        function addMessage(type, content) {
            const container = document.getElementById('messageContainer');
            const messageDiv = document.createElement('div');
            messageDiv.className = `message-item ${type}`;
            
            const timeDiv = document.createElement('div');
            timeDiv.className = 'message-time';
            timeDiv.textContent = new Date().toLocaleString();
            
            const contentDiv = document.createElement('div');
            contentDiv.className = 'message-content';
            contentDiv.textContent = content;
            
            messageDiv.appendChild(timeDiv);
            messageDiv.appendChild(contentDiv);
            
            container.appendChild(messageDiv);
            container.scrollTop = container.scrollHeight;
            
            // 限制消息数量
            while (container.children.length > 50) {
                container.removeChild(container.firstChild);
            }
        }

        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', function() {
            initWebSocket();
            
            // 定期刷新状态
            setInterval(refreshStatus, 30000); // 每30秒刷新一次
        });
    </script>
</body>
</html>

@startuml
skinparam monochrome true

participant "APP" as Mobile
participant "车端（BLE & TBOX）" as CarBT
participant "钥匙云平台" as Cloud
participant "TSP/OEM平台" as ExternalSystem

== 蓝牙连接建立 ==

Mobile -> Mobile : 1. 用户打开APP，选择已配对的车辆
Mobile -> Mobile : 2. 从安全区域读取车辆信息和会话密钥
Mobile -> CarBT : 3. 扫描并识别车辆蓝牙广播信号
CarBT -> Mobile : 4. 发送蓝牙广播（包含车辆标识信息）

note over Mobile, CarBT
  车辆广播信号中包含特殊标识，
  只有配对过的手机才能识别并连接
end note

Mobile -> CarBT : 5. 发起蓝牙连接请求
CarBT -> CarBT : 6. 接收连接请求
CarBT -> Mobile : 7. 接受连接

== 安全认证过程 ==

Mobile -> CarBT : 8. 发送身份验证请求（包含设备ID和时间戳）
CarBT -> CarBT : 9a. 从安全区域读取该设备的会话密钥
CarBT -> Mobile : 9b. 发送认证挑战（随机数）

Mobile -> Mobile : 10a. 使用会话密钥计算认证响应
Mobile -> CarBT : 10b. 发送认证响应

CarBT -> CarBT : 11a. 验证认证响应
CarBT -> Mobile : 11b. 发送认证结果

note over Mobile, CarBT
  双向认证确保:
  1. 手机确实是已配对的授权设备
  2. 车辆确实是用户的车辆
  3. 防止中间人攻击和重放攻击
end note

== 安全通道建立 ==

Mobile -> Mobile : 12a. 生成临时会话密钥材料
Mobile -> CarBT : 12b. 发送部分密钥材料
CarBT -> CarBT : 13a. 生成临时会话密钥材料
CarBT -> Mobile : 13b. 发送部分密钥材料

Mobile -> Mobile : 14. 计算本次通信的会话密钥
CarBT -> CarBT : 15. 计算本次通信的会话密钥

note over Mobile, CarBT
  每次连接都会生成新的临时会话密钥:
  1. 增强安全性，即使一次密钥泄露也不影响后续通信
  2. 防止重放攻击
  3. 确保前向安全性
end note

Mobile <-> CarBT : 16. 建立加密安全通道

== 车辆状态同步 ==

CarBT -> Mobile : 17. 发送车辆当前状态信息
note right of CarBT
  - 车门锁状态
  - 车窗状态
  - 发动机状态
  - 电池电量
  - 其他车辆信息
end note

Mobile -> Mobile : 18. 更新APP界面显示车辆状态

== 控车指令执行 ==

Mobile -> Mobile : 19. 用户在APP中选择控车功能（如开锁）
Mobile -> Mobile : 20. 生成控车指令并签名
note right of Mobile
  指令包含:
  - 操作类型
  - 时间戳
  - 随机数
  - 数字签名
end note

Mobile -> CarBT : 21. 发送加密的控车指令

CarBT -> CarBT : 22a. 解密并验证指令
CarBT -> CarBT : 22b. 检查用户权限
CarBT -> CarBT : 22c. 检查指令时效性（防重放）

alt 指令验证通过
    CarBT -> CarBT : 23a. 执行控车操作
    CarBT -> Mobile : 23b. 返回操作执行结果
    CarBT -> Cloud : 23c. 上报操作记录（可选，根据网络状态）
    Mobile -> Mobile : 23d. 显示操作结果
else 指令验证失败
    CarBT -> Mobile : 24a. 返回验证失败信息
    Mobile -> Mobile : 24b. 显示操作失败原因
end

== 持续状态监控 ==

CarBT -> CarBT : 25. 监控车辆状态变化
alt 状态发生变化
    CarBT -> Mobile : 26a. 推送状态变化通知
    Mobile -> Mobile : 26b. 更新APP界面显示
end

== 连接保持与心跳机制 ==

Mobile <-> CarBT : 27. 定期发送心跳包保持连接
note over Mobile, CarBT
  心跳机制:
  1. 检测连接是否正常
  2. 防止连接超时断开
  3. 定期更新车辆状态
end note

alt 心跳超时
    Mobile -> Mobile : 28a. 检测到连接异常
    Mobile -> Mobile : 28b. 尝试重新连接
    alt 重连失败
        Mobile -> Mobile : 28c. 显示连接已断开
    end
end

== 连接断开 ==

alt 用户主动断开
    Mobile -> Mobile : 29a. 用户退出APP或切换到后台
    Mobile -> CarBT : 29b. 发送断开连接请求
    CarBT -> CarBT : 29c. 清理临时会话资源
    Mobile -> Mobile : 29d. 清理临时会话资源
else 超出连接范围
    Mobile -> Mobile : 30a. 检测到信号丢失
    Mobile -> Mobile : 30b. 显示连接已断开
    CarBT -> CarBT : 30c. 检测到连接断开，清理临时会话资源
end

== 异常处理 ==

note over Mobile, CarBT
  控车过程中可能出现的问题及处理方式:
end note

alt 认证失败
    CarBT -> Mobile : 返回认证失败信息
    Mobile -> Mobile : 显示"认证失败，请重新配对"
    Mobile -> Cloud : 上报认证异常（如果有网络）
else 指令执行超时
    Mobile -> Mobile : 检测到指令执行长时间无响应
    Mobile -> Mobile : 显示"操作超时，请重试"
    Mobile -> Mobile : 尝试重新发送指令
else 车辆状态异常
    CarBT -> Mobile : 返回车辆状态异常信息
    Mobile -> Mobile : 显示具体异常原因
    Mobile -> Cloud : 上报车辆异常（如果有网络）
end

== 无感控车模式（可选功能） ==

note over Mobile, CarBT
  无感控车模式:
  当用户靠近车辆时自动解锁，离开时自动上锁
end note

alt 无感控车已启用
    Mobile -> Mobile : 31a. 后台监测与车辆的距离
    alt 接近车辆
        Mobile -> CarBT : 31b. 自动连接并发送解锁指令
        CarBT -> CarBT : 31c. 验证并执行解锁
    else 远离车辆
        Mobile -> CarBT : 31d. 发送上锁指令
        CarBT -> CarBT : 31e. 验证并执行上锁
    end
end

== 安全保障措施 ==

note over Mobile, CarBT
  安全保障措施:
  1. 所有通信均经过加密
  2. 指令包含时间戳和随机数防重放
  3. 关键操作需要额外验证
  4. 异常情况自动处理
  5. 定期更新会话密钥
end note

@enduml

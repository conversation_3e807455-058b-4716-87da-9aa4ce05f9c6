# 数字钥匙系统文件归类说明

本项目的文件已按照以下结构进行归类整理：

## 文件夹结构

- **docs/** - 所有文档相关文件
  - **html/** - HTML格式的文档文件
  - **images/** - 图片文件（PNG格式）
  - **puml/** - PlantUML源文件
  - **requirements/** - 需求相关文档

## 文件说明

### HTML文档

HTML文档位于 `docs/html/` 目录下，包括：

- `00-头部样式.html` - 头部样式定义
- `01-系统整体架构.html` - 系统整体架构说明
- `02-数字钥匙系统整体架构.html` - 数字钥匙系统整体架构详细说明
- `03-核心组件功能概述.html` - 核心组件功能概述
- `04-系统架构概述.html` - 系统架构概述
- `05-脚本功能.html` - 脚本功能说明
- `index.html` - 主页面
- `index2.html` - 备用主页面

### 图片文件

图片文件位于 `docs/images/` 目录下，包括：

- `整体架构图.png` - 系统整体架构图
- `首次配对 - 泳道图.png` - 首次配对流程图
- `无感控车 - 泳道图.png` - 无感控车流程图

### PlantUML源文件

PlantUML源文件位于 `docs/puml/` 目录下，包括：

- `整体架构图.puml` - 系统整体架构图的PlantUML源文件
- `首次配对 - 泳道图.puml` - 首次配对流程图的PlantUML源文件
- `无感控车 - 泳道图.puml` - 无感控车流程图的PlantUML源文件

### 需求文档

需求文档位于 `docs/requirements/` 目录下，包括：

- `需求描述.ini` - 系统需求描述文件

## 其他文件

- `README.md` - 项目说明文档，位于项目根目录和docs目录下

## 注意事项

1. HTML文件中的图片引用路径已更新为相对路径，指向 `../images/` 目录
2. README.md文件中的PUML文件引用路径已更新，根目录下的README.md指向 `docs/puml/` 目录，docs目录下的README.md指向 `puml/` 目录
3. 所有文件的内容保持不变，仅调整了文件的组织结构 
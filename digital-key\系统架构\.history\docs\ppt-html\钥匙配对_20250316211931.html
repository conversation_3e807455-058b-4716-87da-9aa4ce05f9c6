<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>无感控车功能</title>
    <!-- 引入html2canvas库 -->
    <script src="https://html2canvas.hertzen.com/dist/html2canvas.min.js"></script>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
            font-family: 'PingFang SC', 'Microsoft YaHei', sans-serif;
        }
        
        body {
            background: #f0f0f0;
            display: flex;
            justify-content: center;
            align-items: center;
            min-height: 100vh;
            color: #333;
            padding: 20px;
        }
        
        /* PPT边框指示器 */
        .ppt-container {
            position: relative;
            width: 1920px;
            height: 1080px;
            box-shadow: 0 0 30px rgba(0, 0, 0, 0.2);
            border: 2px dashed #ccc;
            /* 添加棋盘格背景以显示透明度 */
            background-image: linear-gradient(45deg, #ccc 25%, transparent 25%), 
                              linear-gradient(-45deg, #ccc 25%, transparent 25%), 
                              linear-gradient(45deg, transparent 75%, #ccc 75%), 
                              linear-gradient(-45deg, transparent 75%, #ccc 75%);
            background-size: 20px 20px;
            background-position: 0 0, 0 10px, 10px -10px, -10px 0px;
        }
        
        .ppt-label {
            position: absolute;
            top: -40px;
            left: 0;
            background-color: #555;
            color: white;
            padding: 5px 15px;
            border-radius: 4px;
            font-size: 16px;
        }
        
        /* 复制按钮 */
        .copy-button {
            position: absolute;
            top: 20px;
            right: 20px;
            background: linear-gradient(135deg, #3498db, #2980b9);
            color: white;
            border: none;
            padding: 12px 25px;
            border-radius: 30px;
            font-size: 18px;
            cursor: pointer;
            z-index: 1000;
            box-shadow: 0 4px 10px rgba(0, 0, 0, 0.2);
            display: flex;
            align-items: center;
            gap: 10px;
            transition: all 0.3s ease;
        }
        
        /* 下载按钮 */
        .download-button {
            position: absolute;
            top: 20px;
            right: 230px;
            background: linear-gradient(135deg, #2ecc71, #27ae60);
            color: white;
            border: none;
            padding: 12px 25px;
            border-radius: 30px;
            font-size: 18px;
            cursor: pointer;
            z-index: 1000;
            box-shadow: 0 4px 10px rgba(0, 0, 0, 0.2);
            display: flex;
            align-items: center;
            gap: 10px;
            transition: all 0.3s ease;
        }
        
        .copy-button:hover, .download-button:hover {
            transform: translateY(-3px);
            box-shadow: 0 6px 15px rgba(0, 0, 0, 0.25);
        }
        
        .copy-button:active, .download-button:active {
            transform: translateY(0);
        }
        
        .copy-icon {
            width: 20px;
            height: 20px;
            background-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="white"><path d="M19,21H8V7H19M19,5H8A2,2 0 0,0 6,7V21A2,2 0 0,0 8,23H19A2,2 0 0,0 21,21V7A2,2 0 0,0 19,5M16,1H4A2,2 0 0,0 2,3V17H4V3H16V1Z" /></svg>');
            background-size: contain;
            background-repeat: no-repeat;
        }
        
        .download-icon {
            width: 20px;
            height: 20px;
            background-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="white"><path d="M5,20H19V18H5M19,9H15V3H9V9H5L12,16L19,9Z" /></svg>');
            background-size: contain;
            background-repeat: no-repeat;
        }
        
        .slide {
            width: 100%;
            height: 100%;
            background-color: transparent; /* 透明背景 */
            padding: 60px;
            display: flex;
            flex-direction: column;
            overflow: hidden;
            position: relative;
        }
        
        .slide-header {
            text-align: center;
            margin-bottom: 60px;
            position: relative;
        }
        
        .slide-header h1 {
            font-size: 64px;
            color: #2c3e50;
            margin-bottom: 20px;
            font-weight: 600;
            position: relative;
            display: inline-block;
        }
        
        .slide-header h1::after {
            content: '';
            position: absolute;
            bottom: -10px;
            left: 50%;
            transform: translateX(-50%);
            width: 100px;
            height: 4px;
            background: linear-gradient(90deg, #3498db, #2980b9);
            border-radius: 2px;
        }
        
        .slide-header h3 {
            font-size: 32px;
            color: #7f8c8d;
            font-weight: 400;
        }
        
        .slide-content {
            display: flex;
            flex: 1;
            gap: 60px;
        }
        
        .section {
            flex: 1;
            border-radius: 12px;
            padding: 40px;
            display: flex;
            flex-direction: column;
            position: relative;
            overflow: hidden;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.08);
        }
        
        .section-left {
            background-color: rgba(240, 247, 255, 0.9); /* 调整透明度 */
        }
        
        .section-right {
            background-color: rgba(255, 248, 240, 0.9); /* 调整透明度 */
        }
        
        .section::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 6px;
        }
        
        .section-left::before {
            background: linear-gradient(90deg, #3498db, #2980b9);
        }
        
        .section-right::before {
            background: linear-gradient(90deg, #e67e22, #d35400);
        }
        
        .section h2 {
            font-size: 42px;
            color: #2c3e50;
            margin-bottom: 40px;
            padding-bottom: 15px;
            position: relative;
            display: inline-block;
        }
        
        .section-left h2 {
            color: #2980b9;
        }
        
        .section-right h2 {
            color: #d35400;
        }
        
        .section h2::after {
            content: '';
            position: absolute;
            bottom: 0;
            left: 0;
            width: 60px;
            height: 3px;
            border-radius: 2px;
        }
        
        .section-left h2::after {
            background-color: #3498db;
        }
        
        .section-right h2::after {
            background-color: #e67e22;
        }
        
        .section-content {
            flex: 1;
            display: flex;
            flex-direction: column;
        }
        
        /* 改进流程步骤连线样式 */
        .flow-steps {
            list-style: none;
            position: relative;
            display: flex;
            flex-direction: column;
            height: 100%;
            padding-left: 30px;
        }
        
        .flow-steps::before {
            content: '';
            position: absolute;
            left: 19px;
            top: 25px;
            width: 4px;
            height: calc(100% - 80px);
            background: linear-gradient(to bottom, #3498db, #2980b9);
            border-radius: 2px;
            z-index: 1;
        }
        
        .flow-steps li {
            margin-bottom: 35px;
            padding-left: 60px;
            position: relative;
            line-height: 1.4;
            font-size: 28px;
            z-index: 2;
            color: #34495e;
            display: flex;
            align-items: center;
        }
        
        .flow-steps li::before {
            content: attr(data-step);
            position: absolute;
            left: 0;
            top: 50%;
            transform: translateY(-50%);
            display: flex;
            justify-content: center;
            align-items: center;
            width: 42px;
            height: 42px;
            background: linear-gradient(135deg, #3498db, #2980b9);
            color: white;
            border-radius: 50%;
            font-size: 22px;
            font-weight: bold;
            z-index: 3;
            box-shadow: 0 4px 10px rgba(52, 152, 219, 0.3);
        }
        
        .flow-steps li::after {
            content: '';
            position: absolute;
            left: 21px;
            top: 50%;
            transform: translateY(-50%);
            width: 80px;
            height: 2px;
            background-color: rgba(52, 152, 219, 0.2);
            z-index: 0;
        }
        
        /* 改进安全要点块状样式 */
        .security-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 30px;
            height: calc(100% - 20px); /* 稍微减少高度，给底部留出更多空间 */
            margin-bottom: 20px; /* 底部增加间距 */
        }
        
        .security-item {
            background-color: white;
            border-radius: 10px;
            padding: 20px 30px;
            font-size: 28px;
            display: flex;
            flex-direction: column;
            align-items: flex-start;
            justify-content: center;
            position: relative;
            box-shadow: 0 8px 20px rgba(0, 0, 0, 0.05);
            color: #34495e;
            transition: all 0.3s ease;
            border-left: 0;
            overflow: hidden;
        }
        
        .security-item::before {
            content: '';
            position: absolute;
            left: 0;
            top: 0;
            height: 100%;
            width: 6px;
            background: linear-gradient(to bottom, #e67e22, #d35400);
            border-radius: 3px 0 0 3px;
        }
        
        .security-item::after {
            content: "•";
            color: #e67e22;
            font-size: 40px;
            position: absolute;
            left: 15px;
            top: 20px;
            line-height: 0;
        }
        
        .security-item strong {
            font-size: 28px;
            font-weight: 600;
            margin-bottom: 8px;
            color: #34495e;
        }
        
        .security-item span {
            font-size: 16px;
            color: #7f8c8d;
            line-height: 1.3;
        }
        
        .security-item:hover {
            transform: translateY(-5px);
            box-shadow: 0 12px 25px rgba(0, 0, 0, 0.08);
        }
        
        /* 底部图标优化 */
        .icon-container {
            position: absolute;
            bottom: 30px;
            right: 60px;
            display: flex;
            gap: 25px;
        }
        
        .icon {
            width: 70px;
            height: 70px;
            background-size: contain;
            background-repeat: no-repeat;
            background-position: center;
            opacity: 0.6;
            transition: opacity 0.3s, transform 0.3s;
        }
        
        .icon:hover {
            opacity: 0.8;
            transform: scale(1.1);
        }
        
        .icon-car {
            width: 85px;
            height: 85px;
            background-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="%232c3e50"><path d="M5,11L6.5,6.5H17.5L19,11M17.5,16A1.5,1.5 0 0,1 16,14.5A1.5,1.5 0 0,1 17.5,13A1.5,1.5 0 0,1 19,14.5A1.5,1.5 0 0,1 17.5,16M6.5,16A1.5,1.5 0 0,1 5,14.5A1.5,1.5 0 0,1 6.5,13A1.5,1.5 0 0,1 8,14.5A1.5,1.5 0 0,1 6.5,16M18.92,6C18.72,5.42 18.16,5 17.5,5H6.5C5.84,5 5.28,5.42 5.08,6L3,12V20A1,1 0 0,0 4,21H5A1,1 0 0,0 6,20V19H18V20A1,1 0 0,0 19,21H20A1,1 0 0,0 21,20V12L18.92,6Z" /></svg>');
        }
        
        .icon-key {
            background-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="%23e67e22"><path d="M7,14A2,2 0 0,1 5,12A2,2 0 0,1 7,10A2,2 0 0,1 9,12A2,2 0 0,1 7,14M12.65,10C11.83,7.67 9.61,6 7,6A6,6 0 0,0 1,12A6,6 0 0,0 7,18C9.61,18 11.83,16.33 12.65,14H17V18H21V14H23V10H12.65Z" /></svg>');
        }
        
        .icon-security {
            background-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="%233498db"><path d="M12,1L3,5V11C3,16.55 6.84,21.74 12,23C17.16,21.74 21,16.55 21,11V5L12,1M12,5A3,3 0 0,1 15,8A3,3 0 0,1 12,11A3,3 0 0,1 9,8A3,3 0 0,1 12,5M17.13,17C15.92,18.85 14.11,20.24 12,20.92C9.89,20.24 8.08,18.85 6.87,17C6.53,16.5 6.24,16 6,15.47C6,13.82 8.71,12.47 12,12.47C15.29,12.47 18,13.79 18,15.47C17.76,16 17.47,16.5 17.13,17Z" /></svg>');
        }
        
        /* 装饰元素 */
        .decoration {
            position: absolute;
            opacity: 0.03;
            z-index: 0;
        }
        
        .decoration-left {
            width: 300px;
            height: 300px;
            background-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="%233498db"><path d="M7,14A2,2 0 0,1 5,12A2,2 0 0,1 7,10A2,2 0 0,1 9,12A2,2 0 0,1 7,14M12.65,10C11.83,7.67 9.61,6 7,6A6,6 0 0,0 1,12A6,6 0 0,0 7,18C9.61,18 11.83,16.33 12.65,14H17V18H21V14H23V10H12.65Z" /></svg>');
            bottom: -50px;
            left: -50px;
            transform: rotate(-20deg);
        }
        
        .decoration-right {
            width: 300px;
            height: 300px;
            background-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="%23e67e22"><path d="M12,1L3,5V11C3,16.55 6.84,21.74 12,23C17.16,21.74 21,16.55 21,11V5L12,1M12,5A3,3 0 0,1 15,8A3,3 0 0,1 12,11A3,3 0 0,1 9,8A3,3 0 0,1 12,5M17.13,17C15.92,18.85 14.11,20.24 12,20.92C9.89,20.24 8.08,18.85 6.87,17C6.53,16.5 6.24,16 6,15.47C6,13.82 8.71,12.47 12,12.47C15.29,12.47 18,13.79 18,15.47C17.76,16 17.47,16.5 17.13,17Z" /></svg>');
            top: -50px;
            right: -50px;
            transform: rotate(20deg);
        }
        
        /* 消息提示 */
        .toast {
            position: fixed;
            top: 50px;
            left: 50%;
            transform: translateX(-50%);
            background-color: rgba(0, 0, 0, 0.8);
            color: white;
            padding: 12px 25px;
            border-radius: 30px;
            font-size: 16px;
            z-index: 2000;
            display: none;
            animation: fadeInOut 2s ease;
        }
        
        @keyframes fadeInOut {
            0% { opacity: 0; }
            20% { opacity: 1; }
            80% { opacity: 1; }
            100% { opacity: 0; }
        }
        
        /* 清晰度选择器样式 */
        .resolution-selector {
            position: absolute;
            top: 70px;
            right: 230px;
            display: flex;
            align-items: center;
            background: rgba(255, 255, 255, 0.9);
            padding: 8px 15px;
            border-radius: 30px;
            box-shadow: 0 4px 10px rgba(0, 0, 0, 0.1);
            z-index: 1000;
        }
        
        .resolution-label {
            margin-right: 10px;
            font-size: 14px;
            color: #555;
        }
        
        .resolution-btn {
            border: none;
            background: #f0f0f0;
            padding: 5px 10px;
            margin: 0 3px;
            border-radius: 15px;
            font-size: 14px;
            cursor: pointer;
            transition: all 0.2s ease;
            color: #555;
        }
        
        .resolution-btn:hover {
            background: #e0e0e0;
        }
        
        .resolution-btn.active {
            background: linear-gradient(135deg, #3498db, #2980b9);
            color: white;
        }
    </style>
</head>
<body>
    <div class="ppt-container" id="pptContainer">
        <div class="ppt-label">PPT尺寸(1920×1080) - 透明背景</div>
        <button class="copy-button" id="copyButton">
            <span class="copy-icon"></span>复制为PPT图片
        </button>
        <button class="download-button" id="downloadButton">
            <span class="download-icon"></span>下载透明PNG
        </button>
        
        <!-- 添加清晰度选择按钮组 -->
        <div class="resolution-selector" id="resolutionSelector">
            <span class="resolution-label">清晰度:</span>
            <button class="resolution-btn" data-scale="1">标准</button>
            <button class="resolution-btn active" data-scale="2">高清</button>
            <button class="resolution-btn" data-scale="3">超清</button>
        </div>
        
        <div class="slide" id="slide">
            <div class="decoration decoration-left"></div>
            <div class="decoration decoration-right"></div>
            
            <div class="slide-header">
                <h1>钥匙配对功能</h1>
                <h3>安全、可靠的智能车钥匙配对体验</h3>
            </div>
            
            <div class="slide-content">
                <div class="section section-left">
                    <h2>用户操作流程</h2>
                    <div class="section-content">
                        <ul class="flow-steps">
                            <li data-step="1">用户点击"添加新车钥匙"</li>
                            <li data-step="2">扫描车辆二维码或输入VIN码</li>
                            <li data-step="3">完成身份验证</li>
                            <li data-step="4">系统获取云端授权</li>
                            <li data-step="5">自动连接车辆蓝牙</li>
                            <li data-step="6">确认配对码一致</li>
                            <li data-step="7">配对成功，命名钥匙</li>
                            <li data-step="8">测试钥匙操作</li>
                        </ul>
                    </div>
                </div>
                
                <div class="section section-right">
                    <h2>安全设计要点</h2>
                    <div class="section-content">
                        <div class="security-grid">
                            <div class="security-item">
                                <strong>双向身份验证机制</strong>
                                <span>手机和车辆互相验证对方身份，就像互相确认暗号，防止陌生设备冒充</span>
                            </div>
                            <div class="security-item">
                                <strong>一次性配对码校验</strong>
                                <span>基于车辆信息、手机标识和时间生成临时配对码，即使被截获也无法重复使用</span>
                            </div>
                            <div class="security-item">
                                <strong>安全硬件专区存储</strong>
                                <span>关键信息存储在手机和车辆的特殊安全区域，就像放在无法撬开的保险箱中</span>
                            </div>
                            <div class="security-item">
                                <strong>密钥协商保护通信</strong>
                                <span>双方各自保留秘密，只交换部分信息，却能计算出相同的加密密钥</span>
                            </div>
                            <div class="security-item">
                                <strong>双重配对确认机制</strong>
                                <span>只有手机和车辆都向云端确认成功，配对才算完成，防止配对状态不一致</span>
                            </div>
                            <div class="security-item">
                                <strong>异常自动保护措施</strong>
                                <span>配对超时、验证失败等异常情况会自动取消配对过程并上报，确保安全</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="icon-container">
                <div class="icon icon-car"></div>
                <div class="icon icon-key"></div>
                <div class="icon icon-security"></div>
            </div>
        </div>
    </div>
    
    <div class="toast" id="toast">操作成功！</div>
    
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const copyButton = document.getElementById('copyButton');
            const downloadButton = document.getElementById('downloadButton');
            const toast = document.getElementById('toast');
            const slide = document.getElementById('slide');
            const resolutionBtns = document.querySelectorAll('.resolution-btn');
            
            // 默认清晰度为2x (高清)
            let currentScale = 2;
            
            // 清晰度按钮点击事件
            resolutionBtns.forEach(btn => {
                btn.addEventListener('click', function() {
                    // 移除所有按钮的active类
                    resolutionBtns.forEach(b => b.classList.remove('active'));
                    // 给当前按钮添加active类
                    this.classList.add('active');
                    // 更新当前清晰度
                    currentScale = parseFloat(this.getAttribute('data-scale'));
                    
                    // 显示提示
                    showToast(`清晰度已设置为${this.textContent}模式`);
                });
            });
            
            // 显示提示消息
            function showToast(message) {
                toast.textContent = message;
                toast.style.display = 'block';
                setTimeout(function() {
                    toast.style.display = 'none';
                }, 2000);
            }
            
            // 生成透明背景图片
            function generateImage() {
                return html2canvas(slide, {
                    scale: currentScale, // 使用用户选择的清晰度
                    useCORS: true,
                    allowTaint: true,
                    backgroundColor: null // 关键：设置为null以支持透明背景
                });
            }
            
            // 复制按钮功能
            copyButton.addEventListener('click', function() {
                showToast("正在处理图片，请稍候...");
                
                generateImage().then(function(canvas) {
                    try {
                        // 使用现代剪贴板API
                        canvas.toBlob(function(blob) {
                            try {
                                const item = new ClipboardItem({ 'image/png': blob });
                                navigator.clipboard.write([item]).then(function() {
                                    showToast("已复制到剪贴板！现在可以粘贴到PPT中");
                                }).catch(function(err) {
                                    showToast("自动复制失败，请使用下载按钮获取图片");
                                    console.error("剪贴板API错误:", err);
                                });
                            } catch (e) {
                                showToast("您的浏览器不支持复制图片，请使用下载按钮");
                                console.error("ClipboardItem错误:", e);
                            }
                        });
                    } catch (e) {
                        showToast("复制功能不可用，请使用下载图片功能");
                        console.error("toBlob错误:", e);
                    }
                }).catch(function(err) {
                    showToast("生成图片失败，请尝试下载或截图");
                    console.error("html2canvas错误:", err);
                });
            });
            
            // 下载按钮功能
            downloadButton.addEventListener('click', function() {
                showToast(`正在生成${currentScale}x清晰度透明PNG图片，请稍候...`);
                
                generateImage().then(function(canvas) {
                    // 将Canvas转换为PNG并下载
                    const imageUrl = canvas.toDataURL('image/png');
                    const link = document.createElement('a');
                    link.download = `钥匙配对功能-透明-${currentScale}x清晰度.png`;
                    link.href = imageUrl;
                    link.click();
                    
                    showToast(`${currentScale}x清晰度透明背景PNG已下载`);
                }).catch(function(err) {
                    showToast("生成图片失败，请尝试截图");
                    console.error("Error:", err);
                });
            });
        });
    </script>
</body>
</html> 
package com.ruoyi.dk.controller;

import com.ruoyi.common.core.web.controller.BaseController;
import com.ruoyi.common.core.web.domain.AjaxResult;
import com.ruoyi.common.core.web.page.TableDataInfo;
import com.ruoyi.dk.domain.DkVehicleInfo;
import com.ruoyi.dk.service.IDkVehicleInfoService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

/**
 * 车辆信息管理Controller
 * 
 * <AUTHOR>
 * @date 2024-01-15
 */
@RestController
@RequestMapping("/vehicleInfo")
public class DkVehicleInfoController extends BaseController {

    @Autowired(required = false)
    private IDkVehicleInfoService dkVehicleInfoService;

    /**
     * 查询车辆信息列表
     */
    @GetMapping("/list")
    public TableDataInfo list(DkVehicleInfo dkVehicleInfo) {
        startPage();
        List<DkVehicleInfo> list = dkVehicleInfoService.selectDkVehicleInfoList(dkVehicleInfo);
        return getDataTable(list);
    }

    /**
     * 获取车辆信息详细信息
     */
    @GetMapping(value = "/{vehicleId}")
    public AjaxResult getInfo(@PathVariable Long vehicleId) {
        return AjaxResult.success(dkVehicleInfoService.selectDkVehicleInfoByVehicleId(vehicleId));
    }

    /**
     * 新增车辆信息
     */
    @PostMapping
    public AjaxResult add(@RequestBody DkVehicleInfo dkVehicleInfo) {
        return toAjax(dkVehicleInfoService.insertDkVehicleInfo(dkVehicleInfo));
    }

    /**
     * 修改车辆信息
     */
    @PutMapping
    public AjaxResult edit(@RequestBody DkVehicleInfo dkVehicleInfo) {
        return toAjax(dkVehicleInfoService.updateDkVehicleInfo(dkVehicleInfo));
    }

    /**
     * 删除车辆信息
     */
    @DeleteMapping("/{vehicleIds}")
    public AjaxResult remove(@PathVariable Long[] vehicleIds) {
        return toAjax(dkVehicleInfoService.deleteDkVehicleInfoByVehicleIds(vehicleIds));
    }

    /**
     * 获取可用车辆列表
     */
    @GetMapping("/available")
    public AjaxResult listAvailableVehicles() {
        List<DkVehicleInfo> list = dkVehicleInfoService.selectAvailableVehicles();
        return AjaxResult.success(list);
    }

    /**
     * 更新车辆状态
     */
    @PutMapping("/status/{vehicleId}")
    public AjaxResult updateVehicleStatus(@PathVariable Long vehicleId, @RequestBody Map<String, Object> statusData) {
        String status = statusData.get("status").toString();
        return toAjax(dkVehicleInfoService.updateVehicleStatus(vehicleId, status));
    }

    /**
     * 批量更新车辆状态
     */
    @PutMapping("/batchStatus")
    public AjaxResult batchUpdateVehicleStatus(@RequestBody Map<String, Object> statusData) {
        Long[] vehicleIds = (Long[]) statusData.get("vehicleIds");
        String status = statusData.get("status").toString();
        return toAjax(dkVehicleInfoService.batchUpdateVehicleStatus(vehicleIds, status));
    }

    /**
     * 根据VIN码查询车辆
     */
    @GetMapping("/vin/{vinCode}")
    public AjaxResult getVehicleByVin(@PathVariable String vinCode) {
        DkVehicleInfo vehicle = dkVehicleInfoService.selectDkVehicleInfoByVinCode(vinCode);
        return AjaxResult.success(vehicle);
    }

    /**
     * 获取车辆统计信息
     */
    @GetMapping("/statistics")
    public AjaxResult getVehicleStatistics() {
        Map<String, Object> statistics = dkVehicleInfoService.getVehicleStatistics();
        return AjaxResult.success(statistics);
    }
}

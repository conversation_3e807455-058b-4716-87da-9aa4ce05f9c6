<mxfile host="65bd71144e">
    <diagram id="digital-key-architecture" name="数字钥匙三端架构图">
        <mxGraphModel dx="1110" dy="1966" grid="1" gridSize="10" guides="1" tooltips="1" connect="1" arrows="1" fold="1" page="1" pageScale="1" pageWidth="1169" pageHeight="827" math="0" shadow="0">
            <root>
                <mxCell id="0"/>
                <mxCell id="1" parent="0"/>
                <mxCell id="title" value="数字钥匙三端架构" style="text;html=1;strokeColor=none;fillColor=none;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=28;fontStyle=1;fontColor=#0066CC;" parent="1" vertex="1">
                    <mxGeometry x="300" y="-90" width="570" height="50" as="geometry"/>
                </mxCell>
                <mxCell id="cloud-platform" value="钥匙云平台" style="shape=hexagon;perimeter=hexagonPerimeter2;whiteSpace=wrap;html=1;fixedSize=1;size=30;fillColor=#dae8fc;strokeColor=#6c8ebf;gradientColor=#7ea6e0;fontSize=18;fontStyle=1;shadow=1;" vertex="1" parent="1">
                    <mxGeometry x="485" width="200" height="120" as="geometry"/>
                </mxCell>
                <mxCell id="cloud-module-1" value="认证授权模块" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#dae8fc;strokeColor=#6c8ebf;gradientColor=#7ea6e0;shadow=1;fontSize=11;fontStyle=1;" vertex="1" parent="1">
                    <mxGeometry x="380" y="10" width="100" height="30" as="geometry"/>
                </mxCell>
                <mxCell id="cloud-module-2" value="密钥管理模块" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#dae8fc;strokeColor=#6c8ebf;gradientColor=#7ea6e0;shadow=1;fontSize=11;fontStyle=1;" vertex="1" parent="1">
                    <mxGeometry x="379" y="80" width="100" height="30" as="geometry"/>
                </mxCell>
                <mxCell id="cloud-module-3" value="时间服务模块" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#dae8fc;strokeColor=#6c8ebf;gradientColor=#7ea6e0;shadow=1;fontSize=11;fontStyle=1;" vertex="1" parent="1">
                    <mxGeometry x="690" y="10" width="100" height="30" as="geometry"/>
                </mxCell>
                <mxCell id="cloud-module-4" value="车辆关联服务" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#dae8fc;strokeColor=#6c8ebf;gradientColor=#7ea6e0;shadow=1;fontSize=11;fontStyle=1;" vertex="1" parent="1">
                    <mxGeometry x="690" y="80" width="100" height="30" as="geometry"/>
                </mxCell>
                <mxCell id="mobile-side" value="手机端" style="shape=hexagon;perimeter=hexagonPerimeter2;whiteSpace=wrap;html=1;fixedSize=1;size=30;fillColor=#d5e8d4;strokeColor=#82b366;gradientColor=#97d077;fontSize=18;fontStyle=1;shadow=1;" vertex="1" parent="1">
                    <mxGeometry x="229" y="220" width="150" height="120" as="geometry"/>
                </mxCell>
                <mxCell id="mobile-module-1" value="控车指令发送" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#d5e8d4;strokeColor=#82b366;gradientColor=#97d077;shadow=1;fontSize=11;fontStyle=1;" vertex="1" parent="1">
                    <mxGeometry x="129" y="215" width="100" height="30" as="geometry"/>
                </mxCell>
                <mxCell id="mobile-module-2" value="蓝牙交互模块" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#d5e8d4;strokeColor=#82b366;gradientColor=#97d077;shadow=1;fontSize=11;fontStyle=1;" vertex="1" parent="1">
                    <mxGeometry x="110" y="275" width="100" height="30" as="geometry"/>
                </mxCell>
                <mxCell id="mobile-module-3" value="安全区域存储" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#d5e8d4;strokeColor=#82b366;gradientColor=#97d077;shadow=1;fontSize=11;fontStyle=1;" vertex="1" parent="1">
                    <mxGeometry x="140" y="335" width="100" height="30" as="geometry"/>
                </mxCell>
                <mxCell id="mobile-module-4" value="RSSI标定模块" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#d5e8d4;strokeColor=#82b366;gradientColor=#97d077;shadow=1;fontSize=11;fontStyle=1;" vertex="1" parent="1">
                    <mxGeometry x="250" y="367" width="100" height="30" as="geometry"/>
                </mxCell>
                <mxCell id="car-side" value="车端" style="shape=hexagon;perimeter=hexagonPerimeter2;whiteSpace=wrap;html=1;fixedSize=1;size=30;fillColor=#ffe6cc;strokeColor=#d79b00;gradientColor=#ffce9f;fontSize=18;fontStyle=1;shadow=1;" vertex="1" parent="1">
                    <mxGeometry x="790" y="220" width="150" height="120" as="geometry"/>
                </mxCell>
                <mxCell id="car-module-1" value="控车指令执行" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#ffe6cc;strokeColor=#d79b00;gradientColor=#ffce9f;shadow=1;fontSize=11;fontStyle=1;" vertex="1" parent="1">
                    <mxGeometry x="935" y="220" width="100" height="30" as="geometry"/>
                </mxCell>
                <mxCell id="car-module-2" value="蓝牙加密通信" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#ffe6cc;strokeColor=#d79b00;gradientColor=#ffce9f;shadow=1;fontSize=11;fontStyle=1;" vertex="1" parent="1">
                    <mxGeometry x="960" y="275" width="100" height="30" as="geometry"/>
                </mxCell>
                <mxCell id="car-module-3" value="安全区域存储" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#ffe6cc;strokeColor=#d79b00;gradientColor=#ffce9f;shadow=1;fontSize=11;fontStyle=1;" vertex="1" parent="1">
                    <mxGeometry x="935" y="335" width="100" height="30" as="geometry"/>
                </mxCell>
                <mxCell id="car-module-4" value="异常行为监控" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#ffe6cc;strokeColor=#d79b00;gradientColor=#ffce9f;shadow=1;fontSize=11;fontStyle=1;" vertex="1" parent="1">
                    <mxGeometry x="820" y="367" width="100" height="30" as="geometry"/>
                </mxCell>
                <mxCell id="security-center" value="安全层" style="shape=hexagon;perimeter=hexagonPerimeter2;whiteSpace=wrap;html=1;fixedSize=1;size=40;fillColor=#f8cecc;strokeColor=#b85450;gradientColor=#ea6b66;fontSize=18;fontStyle=1;shadow=1;" vertex="1" parent="1">
                    <mxGeometry x="485" y="450" width="200" height="120" as="geometry"/>
                </mxCell>
                <mxCell id="security-module-1" value="端到端加密通信" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f8cecc;strokeColor=#b85450;gradientColor=#ea6b66;shadow=1;fontSize=11;fontStyle=1;" vertex="1" parent="1">
                    <mxGeometry x="350" y="585" width="100" height="30" as="geometry"/>
                </mxCell>
                <mxCell id="security-module-2" value="多因素身份认证" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f8cecc;strokeColor=#b85450;gradientColor=#ea6b66;shadow=1;fontSize=11;fontStyle=1;" vertex="1" parent="1">
                    <mxGeometry x="460" y="585" width="100" height="30" as="geometry"/>
                </mxCell>
                <mxCell id="security-module-3" value="密钥生成与分发" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f8cecc;strokeColor=#b85450;gradientColor=#ea6b66;shadow=1;fontSize=11;fontStyle=1;" vertex="1" parent="1">
                    <mxGeometry x="570" y="585" width="100" height="30" as="geometry"/>
                </mxCell>
                <mxCell id="security-module-4" value="时间同步与防重放" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f8cecc;strokeColor=#b85450;gradientColor=#ea6b66;shadow=1;fontSize=11;fontStyle=1;" vertex="1" parent="1">
                    <mxGeometry x="680" y="585" width="120" height="30" as="geometry"/>
                </mxCell>
                <mxCell id="cloud-to-mobile" value="" style="endArrow=classic;startArrow=classic;html=1;rounded=0;curved=1;strokeWidth=2;exitX=0.25;exitY=1;exitDx=0;exitDy=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;strokeColor=#4D4D4D;shadow=0;" parent="1" source="cloud-platform" target="mobile-side" edge="1">
                    <mxGeometry width="50" height="50" relative="1" as="geometry">
                        <mxPoint x="480" y="200" as="sourcePoint"/>
                        <mxPoint x="310" y="260" as="targetPoint"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="cloud-to-mobile-protocol" value="HTTPS/API" style="text;html=1;strokeColor=#666666;fillColor=#f5f5f5;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=1;fontSize=11;fontStyle=1;gradientColor=#b3b3b3;" vertex="1" parent="1">
                    <mxGeometry x="340" y="170" width="80" height="20" as="geometry"/>
                </mxCell>
                <mxCell id="cloud-to-car" value="" style="endArrow=classic;startArrow=classic;html=1;rounded=0;curved=1;strokeWidth=2;entryX=0.5;entryY=0;entryDx=0;entryDy=0;exitX=0.75;exitY=1;exitDx=0;exitDy=0;strokeColor=#4D4D4D;" edge="1" parent="1" source="cloud-platform" target="car-side">
                    <mxGeometry width="50" height="50" relative="1" as="geometry">
                        <mxPoint x="660" y="200" as="sourcePoint"/>
                        <mxPoint x="780" y="260" as="targetPoint"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="cloud-to-car-protocol" value="MQTT/WebSocket" style="text;html=1;strokeColor=#666666;fillColor=#f5f5f5;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=1;fontSize=11;fontStyle=1;gradientColor=#b3b3b3;" vertex="1" parent="1">
                    <mxGeometry x="740" y="170" width="110" height="20" as="geometry"/>
                </mxCell>
                <mxCell id="mobile-to-car" value="" style="endArrow=classic;startArrow=classic;html=1;rounded=0;curved=1;strokeWidth=2;entryX=0;entryY=0.5;entryDx=0;entryDy=0;exitX=1;exitY=0.5;exitDx=0;exitDy=0;strokeColor=#4D4D4D;" edge="1" parent="1" source="mobile-side" target="car-side">
                    <mxGeometry width="50" height="50" relative="1" as="geometry">
                        <mxPoint x="430" y="350" as="sourcePoint"/>
                        <mxPoint x="740" y="350" as="targetPoint"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="mobile-to-car-protocol" value="蓝牙/RSSI" style="text;html=1;strokeColor=#666666;fillColor=#f5f5f5;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=1;fontSize=11;fontStyle=1;gradientColor=#b3b3b3;" vertex="1" parent="1">
                    <mxGeometry x="535" y="255" width="100" height="20" as="geometry"/>
                </mxCell>
                <mxCell id="mobile-car-interaction" value="" style="rounded=1;whiteSpace=wrap;html=1;fontSize=11;fillColor=#f5f5f5;strokeColor=#666666;gradientColor=#b3b3b3;shadow=1;" vertex="1" parent="1">
                    <mxGeometry x="467.5" y="285" width="235" height="85" as="geometry"/>
                </mxCell>
                <mxCell id="mobile-car-desc" value="&lt;div style=&quot;text-align: left; font-size: 11px;&quot;&gt;&lt;b&gt;交互内容：&lt;/b&gt;&lt;/div&gt;&lt;div style=&quot;text-align: left; font-size: 11px;&quot;&gt;• 暗号交换：挑战-响应方式建立临时会话&lt;/div&gt;&lt;div style=&quot;text-align: left; font-size: 11px;&quot;&gt;• 距离感知：RSSI信号强度计算距离&lt;/div&gt;&lt;div style=&quot;text-align: left; font-size: 11px;&quot;&gt;• 控制指令：发送/接收解锁、启动等指令&lt;/div&gt;&lt;div style=&quot;text-align: left; font-size: 11px;&quot;&gt;• 状态同步：获取车辆状态变化反馈&lt;/div&gt;" style="text;html=1;strokeColor=none;fillColor=none;align=left;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=11;" vertex="1" parent="1">
                    <mxGeometry x="472.5" y="290" width="225" height="75" as="geometry"/>
                </mxCell>
                <mxCell id="mobile-to-security" value="" style="endArrow=classic;startArrow=classic;html=1;rounded=0;strokeWidth=2;exitX=0.5;exitY=1;exitDx=0;exitDy=0;entryX=0;entryY=0.25;entryDx=0;entryDy=0;strokeColor=#4D4D4D;dashed=1;" edge="1" parent="1" source="mobile-module-3" target="security-center">
                    <mxGeometry width="50" height="50" relative="1" as="geometry">
                        <mxPoint x="400" y="450" as="sourcePoint"/>
                        <mxPoint x="450" y="400" as="targetPoint"/>
                        <Array as="points">
                            <mxPoint x="250" y="460"/>
                            <mxPoint x="400" y="480"/>
                        </Array>
                    </mxGeometry>
                </mxCell>
                <mxCell id="mobile-to-security-label" value="安全存储调用" style="text;html=1;strokeColor=#666666;fillColor=#f5f5f5;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=1;fontSize=10;fontStyle=0;gradientColor=#b3b3b3;" vertex="1" parent="1">
                    <mxGeometry x="380" y="430" width="80" height="20" as="geometry"/>
                </mxCell>
                <mxCell id="car-to-security" value="" style="endArrow=classic;startArrow=classic;html=1;rounded=0;strokeWidth=2;exitX=0.5;exitY=1;exitDx=0;exitDy=0;entryX=1;entryY=0.25;entryDx=0;entryDy=0;strokeColor=#4D4D4D;dashed=1;" edge="1" parent="1" source="car-module-3" target="security-center">
                    <mxGeometry width="50" height="50" relative="1" as="geometry">
                        <mxPoint x="400" y="450" as="sourcePoint"/>
                        <mxPoint x="450" y="400" as="targetPoint"/>
                        <Array as="points">
                            <mxPoint x="1010" y="480"/>
                        </Array>
                    </mxGeometry>
                </mxCell>
                <mxCell id="car-to-security-label" value="安全存储调用" style="text;html=1;strokeColor=#666666;fillColor=#f5f5f5;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=1;fontSize=10;fontStyle=0;gradientColor=#b3b3b3;" vertex="1" parent="1">
                    <mxGeometry x="990" y="430" width="80" height="20" as="geometry"/>
                </mxCell>
                <mxCell id="bt-connection-label" value="加密蓝牙通信" style="text;html=1;strokeColor=#666666;fillColor=#f5f5f5;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=1;fontSize=10;fontStyle=0;gradientColor=#b3b3b3;" vertex="1" parent="1">
                    <mxGeometry x="535" y="375" width="100" height="20" as="geometry"/>
                </mxCell>
                <mxCell id="cmd-connection-label" value="空车指令传递" style="text;html=1;strokeColor=#666666;fillColor=#f5f5f5;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=1;fontSize=10;fontStyle=0;gradientColor=#b3b3b3;" vertex="1" parent="1">
                    <mxGeometry x="430" y="375" width="100" height="20" as="geometry"/>
                </mxCell>
                <mxCell id="cloud-to-security" value="" style="endArrow=classic;startArrow=classic;html=1;rounded=0;strokeWidth=2;exitX=0.5;exitY=1;exitDx=0;exitDy=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;strokeColor=#4D4D4D;dashed=1;" edge="1" parent="1" source="cloud-module-2" target="security-center">
                    <mxGeometry width="50" height="50" relative="1" as="geometry">
                        <mxPoint x="400" y="450" as="sourcePoint"/>
                        <mxPoint x="450" y="400" as="targetPoint"/>
                        <Array as="points">
                            <mxPoint x="535" y="240"/>
                            <mxPoint x="585" y="240"/>
                        </Array>
                    </mxGeometry>
                </mxCell>
                <mxCell id="cloud-to-security-label" value="密钥分发服务" style="text;html=1;strokeColor=#666666;fillColor=#f5f5f5;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=1;fontSize=10;fontStyle=0;gradientColor=#b3b3b3;" vertex="1" parent="1">
                    <mxGeometry x="580" y="220" width="80" height="20" as="geometry"/>
                </mxCell>
            </root>
        </mxGraphModel>
    </diagram>
</mxfile>
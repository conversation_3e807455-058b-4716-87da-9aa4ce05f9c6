<mxfile host="65bd71144e">
    <diagram id="9fk8n3h7iYNLtwYTn9JK" name="驾驶侧左前车窗开关原理图">
        <mxGraphModel dx="956" dy="557" grid="1" gridSize="10" guides="1" tooltips="1" connect="1" arrows="1" fold="1" page="1" pageScale="1" pageWidth="827" pageHeight="1169" math="0" shadow="0">
            <root>
                <mxCell id="0"/>
                <mxCell id="1" parent="0"/>
                <mxCell id="2" value="" style="endArrow=none;html=1;strokeWidth=1.5;fillColor=#dae8fc;strokeColor=#0000FF;" edge="1" parent="1">
                    <mxGeometry width="50" height="50" relative="1" as="geometry">
                        <mxPoint x="120" y="120" as="sourcePoint"/>
                        <mxPoint x="400" y="120" as="targetPoint"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="3" value="" style="endArrow=none;html=1;strokeWidth=1.5;fillColor=#dae8fc;strokeColor=#0000FF;" edge="1" parent="1">
                    <mxGeometry width="50" height="50" relative="1" as="geometry">
                        <mxPoint x="120" y="160" as="sourcePoint"/>
                        <mxPoint x="400" y="160" as="targetPoint"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="4" value="" style="endArrow=none;html=1;strokeWidth=1.5;fillColor=#dae8fc;strokeColor=#0000FF;" edge="1" parent="1">
                    <mxGeometry width="50" height="50" relative="1" as="geometry">
                        <mxPoint x="120" y="200" as="sourcePoint"/>
                        <mxPoint x="400" y="200" as="targetPoint"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="5" value="" style="endArrow=none;html=1;strokeWidth=1.5;fillColor=#dae8fc;strokeColor=#0000FF;" edge="1" parent="1">
                    <mxGeometry width="50" height="50" relative="1" as="geometry">
                        <mxPoint x="120" y="240" as="sourcePoint"/>
                        <mxPoint x="400" y="240" as="targetPoint"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="6" value="" style="endArrow=none;html=1;strokeWidth=1.5;fillColor=#dae8fc;strokeColor=#0000FF;" edge="1" parent="1">
                    <mxGeometry width="50" height="50" relative="1" as="geometry">
                        <mxPoint x="120" y="280" as="sourcePoint"/>
                        <mxPoint x="440" y="280" as="targetPoint"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="7" value="" style="endArrow=none;html=1;strokeWidth=1.5;fillColor=#dae8fc;strokeColor=#0000FF;" edge="1" parent="1">
                    <mxGeometry width="50" height="50" relative="1" as="geometry">
                        <mxPoint x="120" y="120" as="sourcePoint"/>
                        <mxPoint x="120" y="320" as="targetPoint"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="8" value="GND" style="text;html=1;strokeColor=none;fillColor=none;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontColor=#FF6666;" vertex="1" parent="1">
                    <mxGeometry x="90" y="320" width="60" height="30" as="geometry"/>
                </mxCell>
                <mxCell id="9" value="R22" style="text;html=1;strokeColor=none;fillColor=none;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontColor=#0000FF;" vertex="1" parent="1">
                    <mxGeometry x="150" y="100" width="60" height="20" as="geometry"/>
                </mxCell>
                <mxCell id="10" value="" style="endArrow=none;html=1;strokeWidth=1.5;fillColor=#dae8fc;strokeColor=#0000FF;entryX=0;entryY=0.5;entryDx=0;entryDy=0;" edge="1" parent="1" target="16">
                    <mxGeometry width="50" height="50" relative="1" as="geometry">
                        <mxPoint x="190" y="120" as="sourcePoint"/>
                        <mxPoint x="240" y="120" as="targetPoint"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="11" value="1.5K" style="text;html=1;strokeColor=none;fillColor=none;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontColor=#0000FF;" vertex="1" parent="1">
                    <mxGeometry x="180" y="100" width="60" height="20" as="geometry"/>
                </mxCell>
                <mxCell id="12" value="R33" style="text;html=1;strokeColor=none;fillColor=none;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontColor=#0000FF;" vertex="1" parent="1">
                    <mxGeometry x="150" y="140" width="60" height="20" as="geometry"/>
                </mxCell>
                <mxCell id="13" value="3K" style="text;html=1;strokeColor=none;fillColor=none;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontColor=#0000FF;" vertex="1" parent="1">
                    <mxGeometry x="180" y="140" width="60" height="20" as="geometry"/>
                </mxCell>
                <mxCell id="14" value="R34" style="text;html=1;strokeColor=none;fillColor=none;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontColor=#0000FF;" vertex="1" parent="1">
                    <mxGeometry x="150" y="180" width="60" height="20" as="geometry"/>
                </mxCell>
                <mxCell id="15" value="330K" style="text;html=1;strokeColor=none;fillColor=none;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontColor=#0000FF;" vertex="1" parent="1">
                    <mxGeometry x="180" y="180" width="60" height="20" as="geometry"/>
                </mxCell>
                <mxCell id="16" value="" style="shape=mxgraph.electrical.electro-mechanical.resistor;html=1;strokeWidth=1.5;fillColor=#dae8fc;strokeColor=#0000FF;rotation=0;" vertex="1" parent="1">
                    <mxGeometry x="210" y="110" width="60" height="20" as="geometry"/>
                </mxCell>
                <mxCell id="17" value="" style="shape=mxgraph.electrical.electro-mechanical.resistor;html=1;strokeWidth=1.5;fillColor=#dae8fc;strokeColor=#0000FF;rotation=0;" vertex="1" parent="1">
                    <mxGeometry x="210" y="150" width="60" height="20" as="geometry"/>
                </mxCell>
                <mxCell id="18" value="" style="shape=mxgraph.electrical.electro-mechanical.resistor;html=1;strokeWidth=1.5;fillColor=#dae8fc;strokeColor=#0000FF;rotation=0;" vertex="1" parent="1">
                    <mxGeometry x="210" y="190" width="60" height="20" as="geometry"/>
                </mxCell>
                <mxCell id="19" value="" style="endArrow=none;html=1;strokeWidth=1.5;fillColor=#dae8fc;strokeColor=#0000FF;" edge="1" parent="1">
                    <mxGeometry width="50" height="50" relative="1" as="geometry">
                        <mxPoint x="270" y="120" as="sourcePoint"/>
                        <mxPoint x="320" y="120" as="targetPoint"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="20" value="" style="endArrow=none;html=1;strokeWidth=1.5;fillColor=#dae8fc;strokeColor=#0000FF;" edge="1" parent="1">
                    <mxGeometry width="50" height="50" relative="1" as="geometry">
                        <mxPoint x="270" y="160" as="sourcePoint"/>
                        <mxPoint x="320" y="160" as="targetPoint"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="21" value="" style="endArrow=none;html=1;strokeWidth=1.5;fillColor=#dae8fc;strokeColor=#0000FF;" edge="1" parent="1">
                    <mxGeometry width="50" height="50" relative="1" as="geometry">
                        <mxPoint x="270" y="200" as="sourcePoint"/>
                        <mxPoint x="320" y="200" as="targetPoint"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="22" value="FL_U_AUTO" style="text;html=1;strokeColor=none;fillColor=none;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontColor=#0000FF;" vertex="1" parent="1">
                    <mxGeometry x="370" y="100" width="80" height="20" as="geometry"/>
                </mxCell>
                <mxCell id="23" value="FL_U" style="text;html=1;strokeColor=none;fillColor=none;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontColor=#0000FF;" vertex="1" parent="1">
                    <mxGeometry x="370" y="140" width="40" height="20" as="geometry"/>
                </mxCell>
                <mxCell id="24" value="FL_D" style="text;html=1;strokeColor=none;fillColor=none;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontColor=#0000FF;" vertex="1" parent="1">
                    <mxGeometry x="370" y="180" width="40" height="20" as="geometry"/>
                </mxCell>
                <mxCell id="25" value="FL_D_AUTO" style="text;html=1;strokeColor=none;fillColor=none;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontColor=#0000FF;" vertex="1" parent="1">
                    <mxGeometry x="370" y="220" width="80" height="20" as="geometry"/>
                </mxCell>
                <mxCell id="26" value="U1" style="text;html=1;strokeColor=none;fillColor=none;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontColor=#0000FF;" vertex="1" parent="1">
                    <mxGeometry x="400" y="120" width="30" height="20" as="geometry"/>
                </mxCell>
                <mxCell id="27" value="" style="rounded=0;whiteSpace=wrap;html=1;strokeColor=#0000FF;fillColor=none;strokeWidth=1.5;" vertex="1" parent="1">
                    <mxGeometry x="340" y="110" width="20" height="20" as="geometry"/>
                </mxCell>
                <mxCell id="28" value="" style="ellipse;whiteSpace=wrap;html=1;aspect=fixed;strokeColor=#0000FF;fillColor=none;strokeWidth=1.5;" vertex="1" parent="1">
                    <mxGeometry x="325" y="115" width="10" height="10" as="geometry"/>
                </mxCell>
                <mxCell id="29" value="" style="ellipse;whiteSpace=wrap;html=1;aspect=fixed;strokeColor=#0000FF;fillColor=none;strokeWidth=1.5;" vertex="1" parent="1">
                    <mxGeometry x="365" y="115" width="10" height="10" as="geometry"/>
                </mxCell>
                <mxCell id="30" value="" style="rounded=0;whiteSpace=wrap;html=1;strokeColor=#0000FF;fillColor=none;strokeWidth=1.5;" vertex="1" parent="1">
                    <mxGeometry x="340" y="150" width="20" height="20" as="geometry"/>
                </mxCell>
                <mxCell id="31" value="" style="ellipse;whiteSpace=wrap;html=1;aspect=fixed;strokeColor=#0000FF;fillColor=none;strokeWidth=1.5;" vertex="1" parent="1">
                    <mxGeometry x="325" y="155" width="10" height="10" as="geometry"/>
                </mxCell>
                <mxCell id="32" value="" style="ellipse;whiteSpace=wrap;html=1;aspect=fixed;strokeColor=#0000FF;fillColor=none;strokeWidth=1.5;" vertex="1" parent="1">
                    <mxGeometry x="365" y="155" width="10" height="10" as="geometry"/>
                </mxCell>
                <mxCell id="33" value="" style="rounded=0;whiteSpace=wrap;html=1;strokeColor=#0000FF;fillColor=none;strokeWidth=1.5;" vertex="1" parent="1">
                    <mxGeometry x="340" y="190" width="20" height="20" as="geometry"/>
                </mxCell>
                <mxCell id="34" value="" style="ellipse;whiteSpace=wrap;html=1;aspect=fixed;strokeColor=#0000FF;fillColor=none;strokeWidth=1.5;" vertex="1" parent="1">
                    <mxGeometry x="325" y="195" width="10" height="10" as="geometry"/>
                </mxCell>
                <mxCell id="35" value="" style="ellipse;whiteSpace=wrap;html=1;aspect=fixed;strokeColor=#0000FF;fillColor=none;strokeWidth=1.5;" vertex="1" parent="1">
                    <mxGeometry x="365" y="195" width="10" height="10" as="geometry"/>
                </mxCell>
                <mxCell id="36" value="" style="rounded=0;whiteSpace=wrap;html=1;strokeColor=#0000FF;fillColor=none;strokeWidth=1.5;" vertex="1" parent="1">
                    <mxGeometry x="340" y="230" width="20" height="20" as="geometry"/>
                </mxCell>
                <mxCell id="37" value="" style="ellipse;whiteSpace=wrap;html=1;aspect=fixed;strokeColor=#0000FF;fillColor=none;strokeWidth=1.5;" vertex="1" parent="1">
                    <mxGeometry x="325" y="235" width="10" height="10" as="geometry"/>
                </mxCell>
                <mxCell id="38" value="" style="ellipse;whiteSpace=wrap;html=1;aspect=fixed;strokeColor=#0000FF;fillColor=none;strokeWidth=1.5;" vertex="1" parent="1">
                    <mxGeometry x="365" y="235" width="10" height="10" as="geometry"/>
                </mxCell>
                <mxCell id="39" value="" style="endArrow=none;html=1;strokeWidth=1.5;fillColor=#dae8fc;strokeColor=#0000FF;" edge="1" parent="1">
                    <mxGeometry width="50" height="50" relative="1" as="geometry">
                        <mxPoint x="120" y="320" as="sourcePoint"/>
                        <mxPoint x="120" y="320" as="targetPoint"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="40" value="" style="verticalLabelPosition=bottom;shadow=0;dashed=0;align=center;html=1;verticalAlign=top;strokeWidth=1.5;shape=mxgraph.electrical.signal_sources.signal_ground;strokeColor=#FF6666;" vertex="1" parent="1">
                    <mxGeometry x="110" y="320" width="20" height="20" as="geometry"/>
                </mxCell>
                <mxCell id="41" value="图3-1 驾驶侧左前车窗开关原理图" style="text;html=1;strokeColor=none;fillColor=none;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontColor=#000000;fontSize=14;fontStyle=1" vertex="1" parent="1">
                    <mxGeometry x="160" y="360" width="240" height="30" as="geometry"/>
                </mxCell>
            </root>
        </mxGraphModel>
    </diagram>
</mxfile>


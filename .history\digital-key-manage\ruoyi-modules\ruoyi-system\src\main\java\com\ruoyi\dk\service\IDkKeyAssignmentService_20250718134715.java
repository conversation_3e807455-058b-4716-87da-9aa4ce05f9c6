package com.ruoyi.dk.service;

import java.util.List;
import com.ruoyi.dk.domain.DkKeyAssignment;

/**
 * 钥匙分配Service接口
 * 
 * <AUTHOR>
 * @date 2024-01-15
 */
public interface IDkKeyAssignmentService {
    /**
     * 查询钥匙分配
     * 
     * @param assignmentId 钥匙分配主键
     * @return 钥匙分配
     */
    public DkKeyAssignment selectDkKeyAssignmentByAssignmentId(Long assignmentId);

    /**
     * 查询钥匙分配列表
     * 
     * @param dkKeyAssignment 钥匙分配
     * @return 钥匙分配集合
     */
    public List<DkKeyAssignment> selectDkKeyAssignmentList(DkKeyAssignment dkKeyAssignment);

    /**
     * 新增钥匙分配
     * 
     * @param dkKeyAssignment 钥匙分配
     * @return 结果
     */
    public int insertDkKeyAssignment(DkKeyAssignment dkKeyAssignment);

    /**
     * 修改钥匙分配
     * 
     * @param dkKeyAssignment 钥匙分配
     * @return 结果
     */
    public int updateDkKeyAssignment(DkKeyAssignment dkKeyAssignment);

    /**
     * 批量删除钥匙分配
     * 
     * @param assignmentIds 需要删除的钥匙分配主键集合
     * @return 结果
     */
    public int deleteDkKeyAssignmentByAssignmentIds(Long[] assignmentIds);

    /**
     * 删除钥匙分配信息
     * 
     * @param assignmentId 钥匙分配主键
     * @return 结果
     */
    public int deleteDkKeyAssignmentByAssignmentId(Long assignmentId);

    /**
     * 分配数字钥匙
     *
     * @param userId             用户ID
     * @param vehicleId          车辆ID
     * @param expectedReturnTime 预期归还时间
     * @param remark             备注
     * @return 结果
     */
    public int assignDigitalKey(Long userId, Long vehicleId, String expectedReturnTime, String remark);

    /**
     * 撤销数字钥匙
     * 
     * @param assignmentId 分配ID
     * @param remark       撤销原因
     * @return 结果
     */
    public int revokeDigitalKey(Long assignmentId, String remark);

    /**
     * 批量撤销数字钥匙
     *
     * @param assignmentIds 分配ID数组
     * @param remark        撤销原因
     * @return 结果
     */
    public int batchRevokeKeys(Long[] assignmentIds, String remark);

    /**
     * 更新分配状态
     * 
     * @param assignmentId 分配ID
     * @param status       状态
     * @param remark       备注
     * @return 结果
     */
    public int updateAssignmentStatus(Long assignmentId, String status, String remark);

    /**
     * 获取分配统计信息
     * 
     * @return 统计信息
     */
    public java.util.Map<String, Object> getAssignmentStatistics();

    /**
     * 根据用户ID查询分配列表
     * 
     * @param userId 用户ID
     * @return 分配列表
     */
    public List<DkKeyAssignment> selectDkKeyAssignmentByUserId(Long userId);

    /**
     * 根据车辆ID查询分配列表
     *
     * @param vehicleId 车辆ID
     * @return 分配列表
     */
    public List<DkKeyAssignment> selectDkKeyAssignmentByVehicleId(Long vehicleId);

    /**
     * 获取待分配的申请列表
     *
     * @return 待分配申请列表
     */
    public List<java.util.Map<String, Object>> getPendingAssignments();
}

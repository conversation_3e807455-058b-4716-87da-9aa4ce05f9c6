<template>
  <view class="container bg-cls">
    <!-- 自定义导航栏 -->
    <view class="custom-navbar">
      <!-- 回退按钮 -->
      <view class="back-button iconfont icon icon-return" @tap="goBack">
        <!-- <text class="back-button-text">↩</text> -->
      </view>

      <text class="navbar-title">零感数字钥匙</text>
    </view>

    <!-- 页面内容 -->
    <view class="content">
      <view class="padding">
        <view style="display: flex;justify-content: center;align-items: center;height: 300px;">
          <canvas id="qrcode" canvas-id="qrcode" style="width: 300px;height:300px;" />
        </view>

        <view class="flex" v-if="shareDetails && shareDetails.length">
          <view class="flex-sub bg-grey padding-sm margin-xs radius">
            <view>我分享的钥匙（{{ shareDetails.length }}）</view>
            <view style="margin-top: 10px; font-size: small;" v-for="(item,index) in shareDetails" :key="index">
              {{index + 1}}、被分享人：{{item.shareeName}}<br/>有效期：{{ item.shareStartTime }}~{{ item.shareEndTime }}
            </view>
          </view>
        </view>

        <view class="margin-tb-sm text-center">
          <button class="bg-olive" @click="startScan">开始扫码</button>
        </view>

        <view class="flex" v-if="shareeDetails && shareeDetails.length">
          <view class="flex-sub bg-cyan padding-sm margin-xs radius">
            <view>分享给我的钥匙（{{ shareeDetails.length }}）</view>
            <view style="margin-top: 10px; font-size: small;" v-for="(item,index) in shareeDetails" :key="index">
              {{index + 1}}、分享人：{{item.sharerName}}<br/>有效期：{{ item.shareStartTime }}~{{ item.shareEndTime }}
            </view>
          </view>
        </view>

      </view>
    </view>
  </view>
</template>

<script>
import { addDigitalKeyShareDetail } from "@/api/dk";
import { getToken, getVinCode } from '@/utils/auth'
import store from '@/store';
import UQRCode from 'uqrcodejs';

  export default {
    data() {
      return {
        url: "wss://dk-api.scsoi.com:4/websocket/message",
        message: "",
        text_content: "",
        ws: null,
        qrCodeUrl: '',
        // 已分享的钥匙详情集合
        shareDetails: [],
        // 被分享的钥匙详情集合
        shareeDetails: [],
      }
    },

    onLoad: function() {
      // 同步钥匙分享相关信息
      this.synchronizeShareDetails();
      // 建立webSocket连接
      this.join();
    },

    onUnload() {
      this.exit();
    },

    onReady() {
      // 生成分享二维码
      this.generateQRCode();
    },

    methods: {
      /**
       * @description: 同步钥匙分享相关信息
       * @return {*}
       */      
      synchronizeShareDetails() {
        let _that = this;
        this.$store.dispatch('GetInfo').then(res => {
          setTimeout(() => {    
            console.log('store.state.user.shareDetails :>> ', store.state.user.shareDetails);
            console.log('store.state.user.id :>> ', store.state.user.id);
            let shareDetailList = store.state.user.shareDetails;
            let userId = store.state.user.id;
  
            _that.shareDetails = shareDetailList.filter(detail => detail.sharerId == userId);
            _that.shareeDetails = shareDetailList.filter(detail => detail.shareeId == userId);
          }, 1000);
        })
      },

      /**
       * @description: 生成分享二维码
       * @return {*}
       */      
      generateQRCode() {
        const qr = new UQRCode();
        qr.data = `${store.state.user.id},${getVinCode()}`;
        qr.size = 300;
        qr.make();
        const ctx = uni.createCanvasContext('qrcode', this); // 组件内调用需传this，vue3 中 this 为 getCurrentInstance()?.proxy
        qr.canvasContext = ctx;
        qr.drawCanvas();
      },

      /**
       * @description: 开始扫码
       * @return {*}
       */      
      startScan() {
        uni.scanCode({
          success: (res) => {
            let scanResult = res.result; // 保存扫描结果
            console.log('扫描结果:', res.result);

            if (!scanResult || scanResult.split(",").length < 2) {
              uni.showToast({
                title: "二维码错误，请确认后重新操作！",
                icon: "none",
              });
              return
            }

            uni.showLoading({
              title: `分享人正在确认中，请稍后...`,
              mask: true
            });

            // 暂定2分钟超时自动关闭
            setTimeout(() => {
              uni.showToast({
                title: "操作超时，请稍后再手动进入分享页面确认！",
                icon: "none",
              });
              uni.hideLoading();
            }, 1000 * 120)

            let scanResultList = scanResult.split(",");

            let msgInfo = {
              messageType: 1,
              recipientId: scanResultList[0],
              message: `确认要分享钥匙给${store.state.user.name}吗？`,
              senderId: store.state.user.id,
              senderName: store.state.user.name
            };

            // 发送消息
            uni.sendSocketMessage({
              data: JSON.stringify(msgInfo)
            });
          },
          fail: (err) => {
            console.error('扫描失败:', err);
          }
        });
      },

      /**
       * @description: 建立webSocket连接
       * @return {*}
       */      
      join() {
        let _that = this;
        const wsuri = this.url + `?userId=${store.state.user.id}`;
        console.log('wsuri :>> ', wsuri);

        if (!getToken()) {
          uni.showToast({
            title: "登录失效，请重新登录！",
            icon: "none",
          });
          return;
        }

        // 创建 WebSocket 连接
        this.ws = uni.connectSocket({
          url: wsuri, // WebSocket 服务器地址
          header: {
            Authorization: getToken()
          },
          success: () => {
            console.log('WebSocket 连接成功');

            // 监听 WebSocket 连接打开事件
            uni.onSocketOpen((res) => {
              console.log('WebSocket 连接已打开===================', res);
            });

            // 监听 WebSocket 消息事件
            uni.onSocketMessage((res) => {
              console.log('收到 WebSocket 消息', res.data);
              if (!res?.data) {
                return;
              }

              let recipientMsgInfo = JSON.parse(res.data);
              let messageType = recipientMsgInfo?.messageType || 0;

              // 消息类型（0：普通通知  1：分享确认通知  2：已确认分享通知  3：已拒绝分享通知）
              switch (messageType) {
                case 0:
                  uni.showToast({
                    title: recipientMsgInfo.message,
                    icon: "none",
                  });
                  break;
                case 1:
                  uni.showModal({
                    title: "分享确认",
                    content: recipientMsgInfo.message,
                    success(modalRes) {
                      console.log('modalRes :>> ', modalRes);
                      if (modalRes?.cancel) {
                        let msgInfo = {
                          messageType: 3,
                          recipientId: recipientMsgInfo.senderId,
                          message: "已拒绝分享",
                          senderId: store.state.user.id,
                          senderName: store.state.user.name
                        };
                        // 发送消息
                        uni.sendSocketMessage({
                          data: JSON.stringify(msgInfo)
                        });
                        return;
                      }

                      if (!recipientMsgInfo.senderId) {
                        uni.showToast({
                          title: "被分享人信息缺失，请确认后，重新操作！",
                        });
                        return;
                      }

                      uni.showToast({
                        title: "处理中，请稍后...",
                      });

                      addDigitalKeyShareDetail({ 
                        sharerId: store.state.user.id,
                        shareeId: recipientMsgInfo.senderId,
                        shareVehicleVin: getVinCode()
                      }).then((res) => {
                        uni.showToast({
                          title: `已成功分享钥匙给${recipientMsgInfo.senderName}`,
                        });

                        let msgInfo = {
                          messageType: 2,
                          recipientId: recipientMsgInfo.senderId,
                          message: "已确认分享",
                          senderId: store.state.user.id,
                          senderName: store.state.user.name
                        };
                        // 发送消息
                        uni.sendSocketMessage({
                          data: JSON.stringify(msgInfo)
                        });

                        // 同步钥匙分享相关信息
                        _that.synchronizeShareDetails();
                      })
                    },
                  });
                  break;
                case 2:
                  uni.showToast({
                    title: "分享者已确认，有效期内可正常使用该钥匙！",
                  });
                  // 同步钥匙分享相关信息
                  _that.synchronizeShareDetails();
                  break;
                case 3:
                  uni.showToast({
                    title: "分享者拒绝分享，请确认后，重新操作！",
                    icon: "none",
                  });
                  break;
                default:
                  // 暂无默认操作
              }
            });

            // 监听 WebSocket 错误事件
            uni.onSocketError((res) => {
              console.error('WebSocket 错误', res);
            });

            // 监听 WebSocket 连接关闭事件
            uni.onSocketClose((res) => {
              console.log('WebSocket 连接已关闭', res);
            });

            
          },
          fail: (err) => {
            console.error('WebSocket 连接失败', err);
          }
        });

      },
      
      exit() {
        if (this.ws) {
          this.ws.close();
          this.ws = null;
        }
      },

      /**
       * @description: 返回上一页
       * @return {*}
       */      
      goBack() {
        wx.navigateBack({
          delta: 1 // 表示回退一层，如果需要回退多层，可以修改这个值
        });
      },

      /**
       * @description: 跳转到开始校准页面
       * @return {*}
       */    
      goToStartCalibrationPage() {
        uni.navigateTo({
          url: '../startCalibration/index'
        });
      },
    }
  }
</script>

<style lang="scss">
  page {
    /* 设置背景固定，不随页面滚动 */
    background-attachment: fixed;

    /* 其他样式，比如设置高度 */
    height: 70vh;
    /* 设置高度为视口高度 */
  }

  .custom-navbar {
    display: flex;
    justify-content: center;
    align-items: center;
    height: 184rpx; /* 导航栏高度 */
    border-radius: 0rpx 0rpx 0rpx 0rpx;
    background-color: rgba(0, 0, 0, 0); /* 自定义背景色 */
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    z-index: 999;
  }

  .navbar-title {
    margin-top: 75rpx;
    color: #121010; /* 标题颜色 */
  }

  .content {
    padding-top: 184rpx;
    width: 100%;
    height: 100%;
  }
  .margin-tb-sm {
    // margin-top: 10%;
  }

  .back-button {
    position: absolute;
    top: 118rpx;
    left: 28rpx; 
  }
</style>

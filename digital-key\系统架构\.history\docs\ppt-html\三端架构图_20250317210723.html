<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>钥匙云平台</title>
    <!-- 引入html2canvas库 -->
    <script src="https://html2canvas.hertzen.com/dist/html2canvas.min.js"></script>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
            font-family: 'PingFang SC', 'Microsoft YaHei', sans-serif;
        }
        
        body {
            background: #f0f0f0;
            display: flex;
            justify-content: center;
            align-items: center;
            min-height: 100vh;
            color: #333;
            padding: 20px;
        }
        
        /* PPT边框指示器 */
        .ppt-container {
            position: relative;
            width: 1920px; /* 标准PPT宽度 */
            height: 1080px; /* 标准PPT高度 */
            box-shadow: 0 0 30px rgba(0, 0, 0, 0.2);
            border: 2px dashed #ccc;
            background-color: white; /* 修改为纯白色背景，方便预览 */
        }
        
        .ppt-label {
            position: absolute;
            top: -40px;
            left: 0;
            background-color: #555;
            color: white;
            padding: 5px 15px;
            border-radius: 4px;
            font-size: 16px;
        }
        
        /* 复制按钮 */
        .copy-button {
            position: absolute;
            top: 20px;
            right: 20px;
            background: linear-gradient(135deg, #3498db, #2980b9);
            color: white;
            border: none;
            padding: 12px 25px;
            border-radius: 30px;
            font-size: 18px;
            cursor: pointer;
            z-index: 1000;
            box-shadow: 0 4px 10px rgba(0, 0, 0, 0.2);
            display: flex;
            align-items: center;
            gap: 10px;
            transition: all 0.3s ease;
        }
        
        /* 下载按钮 */
        .download-button {
            position: absolute;
            top: 20px;
            right: 230px;
            background: linear-gradient(135deg, #2ecc71, #27ae60);
            color: white;
            border: none;
            padding: 12px 25px;
            border-radius: 30px;
            font-size: 18px;
            cursor: pointer;
            z-index: 1000;
            box-shadow: 0 4px 10px rgba(0, 0, 0, 0.2);
            display: flex;
            align-items: center;
            gap: 10px;
            transition: all 0.3s ease;
        }
        
        .copy-button:hover, .download-button:hover {
            transform: translateY(-3px);
            box-shadow: 0 6px 15px rgba(0, 0, 0, 0.25);
        }
        
        .copy-button:active, .download-button:active {
            transform: translateY(0);
        }
        
        .copy-icon {
            width: 20px;
            height: 20px;
            background-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="white"><path d="M19,21H8V7H19M19,5H8A2,2 0 0,0 6,7V21A2,2 0 0,0 8,23H19A2,2 0 0,0 21,21V7A2,2 0 0,0 19,5M16,1H4A2,2 0 0,0 2,3V17H4V3H16V1Z" /></svg>');
            background-size: contain;
            background-repeat: no-repeat;
        }
        
        .download-icon {
            width: 20px;
            height: 20px;
            background-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="white"><path d="M5,20H19V18H5M19,9H15V3H9V9H5L12,16L19,9Z" /></svg>');
            background-size: contain;
            background-repeat: no-repeat;
        }
        
        /* 清晰度选择器样式 */
        .resolution-selector {
            position: absolute;
            top: 70px;
            right: 230px;
            display: flex;
            align-items: center;
            background: rgba(255, 255, 255, 0.9);
            padding: 8px 15px;
            border-radius: 30px;
            box-shadow: 0 4px 10px rgba(0, 0, 0, 0.1);
            z-index: 1000;
        }
        
        .resolution-label {
            margin-right: 10px;
            font-size: 14px;
            color: #555;
        }
        
        .resolution-btn {
            border: none;
            background: #f0f0f0;
            padding: 5px 10px;
            margin: 0 3px;
            border-radius: 15px;
            font-size: 14px;
            cursor: pointer;
            transition: all 0.2s ease;
            color: #555;
        }
        
        .resolution-btn:hover {
            background: #e0e0e0;
        }
        
        .resolution-btn.active {
            background: linear-gradient(135deg, #3498db, #2980b9);
            color: white;
        }
        
        /* 新设计开始 - 完全透明背景 */
        .slide {
            width: 100%;
            height: 100%;
            background-color: transparent; /* 完全透明背景 */
            padding: 60px;
            display: flex;
            flex-direction: column;
            overflow: hidden;
            position: relative;
        }
        
        /* 顶部标题区域 - 修改了标题样式，删除背景渐变 */
        .header {
            text-align: center;
            margin-bottom: 40px;
            position: relative;
            z-index: 10;
        }
        
        .header h1 {
            font-size: 64px;
            color: #2c3e50; /* 使用纯色而非渐变 */
            margin-bottom: 15px;
            position: relative;
            display: inline-block;
            font-weight: 700;
            background: none; /* 移除背景 */
        }
        
        .header .subtitle {
            font-size: 28px;
            color: #7f8c8d;
            font-weight: 400;
            max-width: 800px;
            margin: 0 auto;
        }
        
        /* 特色功能网格布局 - 修改为5列 */
        .features-grid {
            display: grid;
            grid-template-columns: repeat(5, 1fr);
            gap: 20px;
            padding: 0 40px;
            margin-top: 20px;
            position: relative;
            z-index: 10;
        }
        
        /* 特色功能卡片 - 适应更小的卡片 */
        .feature-card {
            background: white;
            border-radius: 16px;
            padding: 25px;
            display: flex;
            flex-direction: column;
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.05);
            height: 100%;
        }
        
        .feature-card:hover {
            transform: translateY(-10px);
            box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
        }
        
        .feature-icon {
            width: 70px;
            height: 70px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-bottom: 20px;
            position: relative;
        }
        
        .feature-icon::before {
            content: '';
            position: absolute;
            width: 70px;
            height: 70px;
            border-radius: 50%;
            background: linear-gradient(135deg, rgba(52, 152, 219, 0.2), rgba(41, 128, 185, 0.2));
            z-index: 1;
        }
        
        .feature-icon svg {
            width: 40px;
            height: 40px;
            z-index: 2;
        }
        
        /* 调整标题和描述字体大小 */
        .feature-title {
            font-size: 20px;
            font-weight: 600;
            color: #2c3e50;
            margin-bottom: 12px;
            position: relative;
        }
        
        .feature-title::after {
            content: '';
            position: absolute;
            bottom: -8px;
            left: 0;
            width: 40px;
            height: 3px;
            background: linear-gradient(90deg, #3498db, transparent);
            border-radius: 3px;
        }
        
        .feature-desc {
            font-size: 14px;
            color: #7f8c8d;
            line-height: 1.5;
            flex-grow: 1;
        }
        
        /* 自定义每个卡片的图标和颜色 */
        .feature-1 .feature-icon::before { background: linear-gradient(135deg, rgba(52, 152, 219, 0.2), rgba(41, 128, 185, 0.2)); }
        .feature-2 .feature-icon::before { background: linear-gradient(135deg, rgba(46, 204, 113, 0.2), rgba(39, 174, 96, 0.2)); }
        .feature-3 .feature-icon::before { background: linear-gradient(135deg, rgba(155, 89, 182, 0.2), rgba(142, 68, 173, 0.2)); }
        .feature-4 .feature-icon::before { background: linear-gradient(135deg, rgba(230, 126, 34, 0.2), rgba(211, 84, 0, 0.2)); }
        .feature-5 .feature-icon::before { background: linear-gradient(135deg, rgba(52, 73, 94, 0.2), rgba(44, 62, 80, 0.2)); }
        .feature-6 .feature-icon::before { background: linear-gradient(135deg, rgba(241, 196, 15, 0.2), rgba(243, 156, 18, 0.2)); }
        .feature-7 .feature-icon::before { background: linear-gradient(135deg, rgba(231, 76, 60, 0.2), rgba(192, 57, 43, 0.2)); }
        .feature-8 .feature-icon::before { background: linear-gradient(135deg, rgba(26, 188, 156, 0.2), rgba(22, 160, 133, 0.2)); }
        .feature-9 .feature-icon::before { background: linear-gradient(135deg, rgba(142, 68, 173, 0.2), rgba(125, 60, 152, 0.2)); }
        .feature-10 .feature-icon::before { background: linear-gradient(135deg, rgba(22, 160, 133, 0.2), rgba(19, 141, 117, 0.2)); }
        
        /* 卡片顶部颜色条 */
        .feature-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 5px;
            background: linear-gradient(90deg, transparent, #3498db, transparent);
        }
        
        .feature-1::before { background: linear-gradient(90deg, transparent, #3498db, transparent); }
        .feature-2::before { background: linear-gradient(90deg, transparent, #2ecc71, transparent); }
        .feature-3::before { background: linear-gradient(90deg, transparent, #9b59b6, transparent); }
        .feature-4::before { background: linear-gradient(90deg, transparent, #e67e22, transparent); }
        .feature-5::before { background: linear-gradient(90deg, transparent, #34495e, transparent); }
        .feature-6::before { background: linear-gradient(90deg, transparent, #f1c40f, transparent); }
        .feature-7::before { background: linear-gradient(90deg, transparent, #e74c3c, transparent); }
        .feature-8::before { background: linear-gradient(90deg, transparent, #1abc9c, transparent); }
        .feature-9::before { background: linear-gradient(90deg, transparent, #8e44ad, transparent); }
        .feature-10::before { background: linear-gradient(90deg, transparent, #16a085, transparent); }
        
        /* 底部装饰和版权 */
        .footer {
            position: absolute;
            bottom: 20px;
            width: 100%;
            text-align: center;
            font-size: 14px;
            color: #95a5a6;
            left: 0;
            z-index: 10;
        }
        
        /* 侧边装饰 */
        .side-decoration {
            position: absolute;
            bottom: 40px;
            right: 40px;
            display: flex;
            gap: 15px;
            z-index: 10;
        }
        
        .side-icon {
            width: 50px;
            height: 50px;
            opacity: 0.7;
            transition: all 0.3s ease;
        }
        
        .side-icon:hover {
            transform: scale(1.1);
            opacity: 1;
        }
        
        /* 消息提示 */
        .toast {
            position: fixed;
            top: 50px;
            left: 50%;
            transform: translateX(-50%);
            background-color: rgba(0, 0, 0, 0.8);
            color: white;
            padding: 12px 25px;
            border-radius: 30px;
            font-size: 16px;
            z-index: 2000;
            display: none;
            animation: fadeInOut 2s ease;
        }
        
        @keyframes fadeInOut {
            0% { opacity: 0; }
            20% { opacity: 1; }
            80% { opacity: 1; }
            100% { opacity: 0; }
        }
    </style>
</head>
<body>
    <div class="ppt-container" id="pptContainer">
        <div class="ppt-label">PPT尺寸(1920×1080) - 透明背景</div>
        <button class="copy-button" id="copyButton">
            <span class="copy-icon"></span>复制为PPT图片
        </button>
        <button class="download-button" id="downloadButton">
            <span class="download-icon"></span>下载透明PNG
        </button>
        
        <!-- 添加清晰度选择按钮组 -->
        <div class="resolution-selector" id="resolutionSelector">
            <span class="resolution-label">清晰度:</span>
            <button class="resolution-btn" data-scale="1">标准</button>
            <button class="resolution-btn active" data-scale="2">高清</button>
            <button class="resolution-btn" data-scale="3">超清</button>
        </div>
        
        <div class="slide" id="slide">
            <!-- 顶部标题 -->
            <div class="header">
                <h1>数字钥匙三端架构图</h1>
                <div class="subtitle">钥匙云平台、手机端、车端的安全交互与核心功能架构</div>
            </div>
            
            <!-- 功能展示网格 - 改为三列布局 -->
            <div class="features-grid" style="grid-template-columns: repeat(3, 1fr); gap: 30px;">
                <!-- 第一列：钥匙云平台 -->
                <div class="feature-card feature-1" style="background: linear-gradient(to bottom, rgba(52, 152, 219, 0.1), rgba(52, 152, 219, 0.05)); border: 2px solid rgba(52, 152, 219, 0.3);">
                    <div class="feature-icon">
                        <svg viewBox="0 0 24 24" fill="#3498db">
                            <path d="M12,2A7,7 0 0,0 5,9C5,11.88 6.68,14.38 9,15.93V20H12V23H17V20H15V15.93C17.32,14.38 19,11.88 19,9A7,7 0 0,0 12,2M9,9A3,3 0 0,1 12,6A3,3 0 0,1 15,9A3,3 0 0,1 12,12A3,3 0 0,1 9,9Z" />
                        </svg>
                    </div>
                    <div class="feature-title" style="color: #3498db; font-size: 24px;">钥匙云平台</div>
                    <div class="feature-desc" style="margin-bottom: 20px;">
                        <ul style="list-style-type: none; padding-left: 0;">
                            <li style="margin-bottom: 10px; border-left: 3px solid #3498db; padding-left: 10px;">
                                <b>钥匙生命周期管理：</b>创建、授权、撤销数字钥匙
                            </li>
                            <li style="margin-bottom: 10px; border-left: 3px solid #3498db; padding-left: 10px;">
                                <b>车辆关联服务：</b>验证车辆信息，建立安全绑定关系
                            </li>
                            <li style="margin-bottom: 10px; border-left: 3px solid #3498db; padding-left: 10px;">
                                <b>安全认证中心：</b>确保用户身份可信，执行权限验证
                            </li>
                            <li style="margin-bottom: 10px; border-left: 3px solid #3498db; padding-left: 10px;">
                                <b>密钥管理系统：</b>生成加密密钥，确保安全存储与分发
                            </li>
                            <li style="margin-bottom: 10px; border-left: 3px solid #3498db; padding-left: 10px;">
                                <b>统一接口服务：</b>提供标准化API，连接外部系统
                            </li>
                            <li style="margin-bottom: 10px; border-left: 3px solid #3498db; padding-left: 10px;">
                                <b>安全通信通道：</b>实现端到端加密通信，防止数据被窃听
                            </li>
                            <li style="margin-bottom: 10px; border-left: 3px solid #3498db; padding-left: 10px;">
                                <b>异常监控与处理：</b>检测异常行为，执行安全响应
                            </li>
                            <li style="margin-bottom: 10px; border-left: 3px solid #3498db; padding-left: 10px;">
                                <b>时间服务器：</b>提供精确时间基准，防止重放攻击
                            </li>
                        </ul>
                    </div>
                </div>
                
                <!-- 第二列：手机端 -->
                <div class="feature-card feature-2" style="background: linear-gradient(to bottom, rgba(46, 204, 113, 0.1), rgba(46, 204, 113, 0.05)); border: 2px solid rgba(46, 204, 113, 0.3);">
                    <div class="feature-icon">
                        <svg viewBox="0 0 24 24" fill="#2ecc71">
                            <path d="M17,19H7V5H17M17,1H7C5.89,1 5,1.89 5,3V21A2,2 0 0,0 7,23H17A2,2 0 0,0 19,21V3C19,1.89 18.1,1 17,1Z" />
                        </svg>
                    </div>
                    <div class="feature-title" style="color: #2ecc71; font-size: 24px;">手机端</div>
                    <div class="feature-desc" style="margin-bottom: 20px;">
                        <ul style="list-style-type: none; padding-left: 0;">
                            <li style="margin-bottom: 10px; border-left: 3px solid #2ecc71; padding-left: 10px;">
                                <b>钥匙管理模块：</b>查看、使用和管理个人数字钥匙
                            </li>
                            <li style="margin-bottom: 10px; border-left: 3px solid #2ecc71; padding-left: 10px;">
                                <b>车辆控制模块：</b>发送控制指令，接收车辆状态反馈
                            </li>
                            <li style="margin-bottom: 10px; border-left: 3px solid #2ecc71; padding-left: 10px;">
                                <b>安全存储模块：</b>在安全区域存储密钥和连接信息
                            </li>
                            <li style="margin-bottom: 10px; border-left: 3px solid #2ecc71; padding-left: 10px;">
                                <b>蓝牙通信模块：</b>扫描识别车辆广播，建立蓝牙连接
                            </li>
                            <li style="margin-bottom: 10px; border-left: 3px solid #2ecc71; padding-left: 10px;">
                                <b>时间同步模块：</b>与云端时间服务器同步，提供可信时间戳
                            </li>
                            <li style="margin-bottom: 10px; border-left: 3px solid #2ecc71; padding-left: 10px;">
                                <b>暗号交换模块：</b>与车端协商生成临时暗号，确保通信安全
                            </li>
                            <li style="margin-bottom: 10px; border-left: 3px solid #2ecc71; padding-left: 10px;">
                                <b>智能场景管理：</b>优化扫描频率，提升电量使用效率
                            </li>
                            <li style="margin-bottom: 10px; border-left: 3px solid #2ecc71; padding-left: 10px;">
                                <b>异常处理模块：</b>检测并处理连接异常，确保使用安全
                            </li>
                            <li style="margin-bottom: 10px; border-left: 3px solid #2ecc71; padding-left: 10px;">
                                <b>标定模块：</b>校准RSSI信号与实际距离关系，提高精度
                            </li>
                        </ul>
                    </div>
                </div>
                
                <!-- 第三列：车端 -->
                <div class="feature-card feature-3" style="background: linear-gradient(to bottom, rgba(231, 76, 60, 0.1), rgba(231, 76, 60, 0.05)); border: 2px solid rgba(231, 76, 60, 0.3);">
                    <div class="feature-icon">
                        <svg viewBox="0 0 24 24" fill="#e74c3c">
                            <path d="M18.92,6.01C18.72,5.42 18.16,5 17.5,5H6.5C5.84,5 5.29,5.42 5.08,6.01L3,12V20A1,1 0 0,0 4,21H5A1,1 0 0,0 6,20V19H18V20A1,1 0 0,0 19,21H20A1,1 0 0,0 21,20V12L18.92,6.01M6.5,16C5.67,16 5,15.33 5,14.5C5,13.67 5.67,13 6.5,13C7.33,13 8,13.67 8,14.5C8,15.33 7.33,16 6.5,16M17.5,16C16.67,16 16,15.33 16,14.5C16,13.67 16.67,13 17.5,13C18.33,13 19,13.67 19,14.5C19,15.33 18.33,16 17.5,16M5,11L6.5,6.5H17.5L19,11H5Z" />
                        </svg>
                    </div>
                    <div class="feature-title" style="color: #e74c3c; font-size: 24px;">车端</div>
                    <div class="feature-desc" style="margin-bottom: 20px;">
                        <ul style="list-style-type: none; padding-left: 0;">
                            <li style="margin-bottom: 10px; border-left: 3px solid #e74c3c; padding-left: 10px;">
                                <b>蓝牙通信模块：</b>发送加密广播信号，处理连接请求
                            </li>
                            <li style="margin-bottom: 10px; border-left: 3px solid #e74c3c; padding-left: 10px;">
                                <b>钥匙验证模块：</b>验证手机身份，确保只有授权设备能连接
                            </li>
                            <li style="margin-bottom: 10px; border-left: 3px solid #e74c3c; padding-left: 10px;">
                                <b>指令执行模块：</b>接收并执行控制指令，反馈执行结果
                            </li>
                            <li style="margin-bottom: 10px; border-left: 3px solid #e74c3c; padding-left: 10px;">
                                <b>安全存储模块：</b>存储根密钥、会话密钥和授权设备信息
                            </li>
                            <li style="margin-bottom: 10px; border-left: 3px solid #e74c3c; padding-left: 10px;">
                                <b>远程通信模块：</b>与云平台建立安全连接，同步状态
                            </li>
                            <li style="margin-bottom: 10px; border-left: 3px solid #e74c3c; padding-left: 10px;">
                                <b>时间同步模块：</b>与云端时间服务器同步，提供时间基准
                            </li>
                            <li style="margin-bottom: 10px; border-left: 3px solid #e74c3c; padding-left: 10px;">
                                <b>暗号交换模块：</b>与手机端协商生成临时暗号确保通信
                            </li>
                            <li style="margin-bottom: 10px; border-left: 3px solid #e74c3c; padding-left: 10px;">
                                <b>用户行为分析：</b>基于RSSI信号强度计算与手机的距离
                            </li>
                            <li style="margin-bottom: 10px; border-left: 3px solid #e74c3c; padding-left: 10px;">
                                <b>异常处理模块：</b>处理异常连接请求，监控异常操作
                            </li>
                            <li style="margin-bottom: 10px; border-left: 3px solid #e74c3c; padding-left: 10px;">
                                <b>标定配置模块：</b>管理RSSI信号标定数据，支持精确计算
                            </li>
                        </ul>
                    </div>
                </div>
            </div>
            
            <!-- 交互关系图 -->
            <div style="position: absolute; bottom: 70px; width: 100%; text-align: center;">
                <svg width="1000" height="120" style="margin: 0 auto;">
                    <!-- 云平台到手机端的双向箭头 -->
                    <path d="M350,30 L500,30" stroke="#3498db" stroke-width="3" fill="none" marker-end="url(#arrowhead)"></path>
                    <path d="M500,50 L350,50" stroke="#2ecc71" stroke-width="3" fill="none" marker-end="url(#arrowhead)"></path>
                    
                    <!-- 手机端到车端的双向箭头 -->
                    <path d="M650,30 L800,30" stroke="#2ecc71" stroke-width="3" fill="none" marker-end="url(#arrowhead)"></path>
                    <path d="M800,50 L650,50" stroke="#e74c3c" stroke-width="3" fill="none" marker-end="url(#arrowhead)"></path>
                    
                    <!-- 云平台到车端的双向箭头（虚线表示间接连接） -->
                    <path d="M350,90 C500,120 650,120 800,90" stroke="#3498db" stroke-width="2" stroke-dasharray="5,5" fill="none" marker-end="url(#arrowhead)"></path>
                    <path d="M800,70 C650,40 500,40 350,70" stroke="#e74c3c" stroke-width="2" stroke-dasharray="5,5" fill="none" marker-end="url(#arrowhead)"></path>
                    
                    <!-- 箭头定义 -->
                    <defs>
                        <marker id="arrowhead" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
                            <polygon points="0 0, 10 3.5, 0 7" fill="#333" />
                        </marker>
                    </defs>
                    
                    <!-- 交互说明 -->
                    <text x="425" y="20" font-size="14" fill="#333" text-anchor="middle">钥匙授权/认证</text>
                    <text x="425" y="70" font-size="14" fill="#333" text-anchor="middle">状态同步/更新</text>
                    
                    <text x="725" y="20" font-size="14" fill="#333" text-anchor="middle">蓝牙控制指令</text>
                    <text x="725" y="70" font-size="14" fill="#333" text-anchor="middle">状态反馈/验证</text>
                    
                    <text x="575" y="105" font-size="14" fill="#333" text-anchor="middle">远程控制/配置更新</text>
                    <text x="575" y="40" font-size="14" fill="#333" text-anchor="middle">状态上报/安全校验</text>
                </svg>
            </div>
            
            <!-- 底部说明 -->
            <div class="footer">
                数字钥匙三端架构 - 安全、高效的车控系统核心技术框架
            </div>
        </div>
    </div>
    
    <div class="toast" id="toast">操作成功！</div>
    
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const copyButton = document.getElementById('copyButton');
            const downloadButton = document.getElementById('downloadButton');
            const toast = document.getElementById('toast');
            const slide = document.getElementById('slide');
            const resolutionBtns = document.querySelectorAll('.resolution-btn');
            
            // 默认清晰度为2x (高清)
            let currentScale = 2;
            
            // 清晰度按钮点击事件
            resolutionBtns.forEach(btn => {
                btn.addEventListener('click', function() {
                    // 移除所有按钮的active类
                    resolutionBtns.forEach(b => b.classList.remove('active'));
                    // 给当前按钮添加active类
                    this.classList.add('active');
                    // 更新当前清晰度
                    currentScale = parseFloat(this.getAttribute('data-scale'));
                    
                    // 显示提示
                    showToast(`清晰度已设置为${this.textContent}模式`);
                });
            });
            
            // 显示提示消息
            function showToast(message) {
                toast.textContent = message;
                toast.style.display = 'block';
                setTimeout(function() {
                    toast.style.display = 'none';
                }, 2000);
            }
            
            // 生成透明背景图片
            function generateImage() {
                return html2canvas(slide, {
                    scale: currentScale, // 使用用户选择的清晰度
                    useCORS: true,
                    allowTaint: true,
                    backgroundColor: null // 关键：设置为null以支持透明背景
                });
            }
            
            // 复制按钮功能
            copyButton.addEventListener('click', function() {
                showToast("正在处理图片，请稍候...");
                
                generateImage().then(function(canvas) {
                    try {
                        // 使用现代剪贴板API
                        canvas.toBlob(function(blob) {
                            try {
                                const item = new ClipboardItem({ 'image/png': blob });
                                navigator.clipboard.write([item]).then(function() {
                                    showToast("已复制到剪贴板！现在可以粘贴到PPT中");
                                }).catch(function(err) {
                                    showToast("自动复制失败，请使用下载按钮获取图片");
                                    console.error("剪贴板API错误:", err);
                                });
                            } catch (e) {
                                showToast("您的浏览器不支持复制图片，请使用下载按钮");
                                console.error("ClipboardItem错误:", e);
                            }
                        });
                    } catch (e) {
                        showToast("复制功能不可用，请使用下载图片功能");
                        console.error("toBlob错误:", e);
                    }
                }).catch(function(err) {
                    showToast("生成图片失败，请尝试下载或截图");
                    console.error("html2canvas错误:", err);
                });
            });
            
         downloadButton.addEventListener('click', function () {
                showToast(`正在生成${currentScale}x清晰度透明PNG图片，请稍候...`);

                generateImage().then(function (canvas) {
                    // 将Canvas转换为PNG并下载
                    const imageUrl = canvas.toDataURL('image/png');
                    const link = document.createElement('a');

                    // 首先从URL路径获取文件名
                    let fileName = "";
                    const pathParts = window.location.pathname.split('/');
                    const htmlFileName = pathParts[pathParts.length - 1];
                    // 移除.html后缀并进行URL解码以支持中文文件名
                    fileName = decodeURIComponent(htmlFileName.replace('.html', ''));

                    // 如果从URL获取的文件名为空，尝试从标题获取
                    if (!fileName) {
                        const titleElement = document.querySelector('.header h1');
                        if (titleElement && titleElement.textContent) {
                            fileName = titleElement.textContent.trim();
                        }
                    }

                    // 确保文件名不为空
                    if (!fileName) {
                        fileName = "测试";
                    }

                    link.download = `${fileName}-透明-${currentScale}x清晰度.png`;
                    link.href = imageUrl;
                    link.click();

                    showToast(`${currentScale}x清晰度透明背景PNG已下载`);
                }).catch(function (err) {
                    showToast("生成图片失败，请尝试截图");
                    console.error("Error:", err);
                });
            });
        });
    </script>
</body>
</html> 
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>数字钥匙技术架构</title>
    <link rel="stylesheet" href="css/style.css">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/@fortawesome/fontawesome-free@5.15.4/css/all.min.css">
</head>
<body>
    <!-- 顶部导航 -->
    <div class="top-nav">
        <div class="top-nav-inner">
            <div class="logo-title">
                <img src="data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHZpZXdCb3g9IjAgMCAyMDAgNTAiPjxwYXRoIGZpbGw9IiMzNDk4ZGIiIGQ9Ik0yMCwxMEMzMCw1IDQwLDUgNTAsMTBDNjAsMTUgNzAsMTUgODAsMTBDOTAsNSAxMDAsNSAxMTAsMTBDMTIwLDE1IDEzMCwxNSAxNDAsMTBDMTUwLDUgMTYwLDUgMTcwLDEwQzE4MCwxNSAxOTAsMTUgMjAwLDEwVjQwQzE5MCw0NSAxODAsNDUgMTcwLDQwQzE2MCwzNSAxNTAsMzUgMTQwLDQwQzEzMCw0NSAxMjAsNDUgMTEwLDQwQzEwMCwzNSA5MCwzNSA4MCw0MEM3MCw0NSA2MCw0MEM1MCw0MEM0MCwzNSAzMCwzNSAyMCw0MEMxMCw0NSAwLDQ1IC0xMCw0MFYxMEMwLDUgMTAsNSAyMCwxMFoiLz48dGV4dCB4PSI4MCIgeT0iMzAiIGZvbnQtZmFtaWx5PSJBcmlhbCIgZm9udC1zaXplPSIxNiIgZmlsbD0id2hpdGUiIHRleHQtYW5jaG9yPSJtaWRkbGUiPnplcm9zZW5zZTwvdGV4dD48L3N2Zz4=" alt="零感智能" class="logo-img">
                <h1>数字钥匙技术架构</h1>
            </div>
            <div class="nav-links">
                <a href="#overview"><i class="fas fa-project-diagram"></i> 整体架构</a>
                <a href="#components"><i class="fas fa-puzzle-piece"></i> 系统组件</a>
                <a href="#processes"><i class="fas fa-link"></i> 业务流程</a>
                <a href="#security"><i class="fas fa-shield-alt"></i> 安全机制</a>
                <a href="#emergency"><i class="fas fa-exclamation-triangle"></i> 应急处理</a>
            </div>
        </div>
    </div>

    <div class="container">
        <!-- 顶部标题区域 -->
        <div class="section fade-in">
            <div class="section-header">
                <h2><i class="fas fa-key"></i> 数字钥匙系统简介</h2>
            </div>
            <div class="section-content">
                <p>数字钥匙系统是一种基于移动设备的车辆访问和控制解决方案，允许用户通过手机应用远程或近场控制车辆的锁定、解锁和启动等功能。系统基于严格的安全协议，确保仅授权用户能够访问车辆。</p>
                <p>该系统采用蓝牙低功耗(BLE)技术实现手机与车辆之间的无线通信，并通过云平台进行钥匙的授权和管理。</p>
                <div class="architecture-diagram fade-in">
                    <div class="component mobile">
                        <i class="fas fa-mobile-alt"></i>
                        <h3>手机端</h3>
                        <p>用户交互界面与核心SDK</p>
                    </div>
                    <div class="component car">
                        <i class="fas fa-car"></i>
                        <h3>车端</h3>
                        <p>BLE模块与TBOX</p>
                    </div>
                    <div class="component cloud">
                        <i class="fas fa-cloud"></i>
                        <h3>钥匙云平台</h3>
                        <p>密钥管理与安全认证</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- 系统架构图部分 -->
        <div id="overview" class="section fade-in">
            <div class="section-header">
                <h2><i class="fas fa-sitemap"></i> 系统架构图</h2>
            </div>
            <div class="section-content">
                <p>数字车钥匙系统主要包括三个实体，即移动智能终端（移动终端或手机）、云端服务器与车辆端。如下图所示：</p>
                
                <div class="process-diagram">
                    <img src="../images/system-architecture.png" alt="数字钥匙系统架构图" class="architecture-full-img">
                    <figcaption><i class="fas fa-info-circle"></i> 数字钥匙系统完整架构图（包含各模块间的交互关系）</figcaption>
                </div>
                
                <div class="architecture-details">
                    <h3><i class="fas fa-layer-group"></i> 架构层次说明</h3>
                    <div class="feature-list">
                        <div class="feature-item">
                            <h3><i class="fas fa-cloud"></i> 云端</h3>
                            <ul class="custom-list">
                                <li><strong>DK Server</strong>：数字钥匙服务器，负责钥匙生命周期管理和授权管理</li>
                                <li><strong>OEM TSP</strong>：车厂远程服务平台，提供车辆远程控制服务</li>
                                <li><strong>TSM</strong>：可信服务管理平台，安全管理芯片应用和密钥</li>
                            </ul>
                        </div>
                        <div class="feature-item">
                            <h3><i class="fas fa-mobile-alt"></i> 移动端</h3>
                            <ul class="custom-list">
                                <li><strong>NFC & BLE</strong>：近场通信和蓝牙低功耗通信模块</li>
                                <li><strong>钥匙业务模块</strong>：处理钥匙相关的核心业务逻辑</li>
                                <li><strong>钥匙标准基础板块SDK</strong>：提供标准化的钥匙操作接口</li>
                            </ul>
                        </div>
                        <div class="feature-item">
                            <h3><i class="fas fa-car"></i> 车辆端</h3>
                            <ul class="custom-list">
                                <li><strong>数字钥匙NFC模块</strong>：通过射频天线和CLF进行近场通信</li>
                                <li><strong>数字钥匙BLE模块</strong>：通过MCU和BLE芯片实现蓝牙通信</li>
                                <li><strong>eSE</strong>：嵌入式安全元件，安全存储密钥和执行加密操作</li>
                                <li><strong>NFC卡片钥匙</strong>：实体NFC卡片，可作为备用钥匙使用</li>
                            </ul>
                        </div>
                    </div>
                    
                    <h3><i class="fas fa-exchange-alt"></i> 系统通信流程</h3>
                    <p>系统中的三大模块通过安全的通信协议相互连接：</p>
                    <ul class="custom-list">
                        <li><strong>云端与移动端</strong>：通过互联网进行通信，实现钥匙下发和权限管理</li>
                        <li><strong>云端与车辆端</strong>：TSP平台通过蜂窝网络与车辆通信，实现远程控制和配置</li>
                        <li><strong>移动端与车辆端</strong>：通过BLE或NFC建立近场加密通信，实现车辆控制</li>
                        <li><strong>产线前装</strong>：在车辆生产过程中安装和检测数字钥匙相关硬件和软件</li>
                    </ul>
                </div>
            </div>
        </div>

        <!-- 系统组件部分 -->
        <div id="components" class="section fade-in">
            <div class="section-header">
                <h2><i class="fas fa-puzzle-piece"></i> 系统组件详解</h2>
            </div>
            <div class="section-content">
                <div class="tabs">
                    <div class="tab-nav">
                        <button class="tab-btn active" onclick="openTab(event, 'tab-mobile')"><i class="fas fa-mobile-alt"></i> 手机端组件</button>
                        <button class="tab-btn" onclick="openTab(event, 'tab-car')"><i class="fas fa-car"></i> 车端组件</button>
                        <button class="tab-btn" onclick="openTab(event, 'tab-cloud')"><i class="fas fa-cloud"></i> 云平台组件</button>
                    </div>
                    
                    <div id="tab-mobile" class="tab-content active">
                        <div class="feature-list">
                            <div class="feature-item">
                                <h3><i class="fas fa-key"></i> 钥匙管理模块</h3>
                                <p>管理数字钥匙的生命周期，包括钥匙的激活、使用、分享和注销等功能。</p>
                            </div>
                            <div class="feature-item">
                                <h3><i class="fas fa-cogs"></i> 车辆控制模块</h3>
                                <p>执行车辆控制指令，如锁车、解锁、启动等操作，并显示操作反馈。</p>
                            </div>
                            <div class="feature-item">
                                <h3><i class="fas fa-bluetooth"></i> 蓝牙通信模块</h3>
                                <p>与车端建立安全连接，发送控制命令并接收车辆状态反馈。</p>
                            </div>
                            <div class="feature-item">
                                <h3><i class="fas fa-database"></i> 安全存储模块</h3>
                                <p>安全存储密钥和配置信息，确保敏感数据不被未授权访问。</p>
                            </div>
                            <div class="feature-item">
                                <h3><i class="fas fa-clock"></i> 时间同步模块</h3>
                                <p>确保手机端与车端和云端的时间同步，防止重放攻击。</p>
                            </div>
                            <div class="feature-item">
                                <h3><i class="fas fa-exchange-alt"></i> 暗号交换模块</h3>
                                <p>与车端协商生成临时暗号，用于加密通信和身份验证。</p>
                            </div>
                            <div class="feature-item">
                                <h3><i class="fas fa-magic"></i> 智能场景管理</h3>
                                <p>管理无感连接触发条件，基于用户行为和环境进行智能决策。</p>
                            </div>
                            <div class="feature-item">
                                <h3><i class="fas fa-exclamation-circle"></i> 异常处理模块</h3>
                                <p>处理通信异常和认证失败，提供用户友好的错误提示。</p>
                            </div>
                        </div>
                    </div>
                    
                    <div id="tab-car" class="tab-content">
                        <div class="feature-list">
                            <div class="feature-item">
                                <h3><i class="fas fa-bluetooth"></i> 蓝牙通信模块</h3>
                                <p>与手机建立安全连接，接收控制命令并返回执行结果。</p>
                            </div>
                            <div class="feature-item">
                                <h3><i class="fas fa-check-circle"></i> 钥匙验证模块</h3>
                                <p>验证数字钥匙的有效性，确保只有授权用户能够控制车辆。</p>
                            </div>
                            <div class="feature-item">
                                <h3><i class="fas fa-tasks"></i> 指令执行模块</h3>
                                <p>接收控制指令并转换为车辆电子系统可执行的动作。</p>
                            </div>
                            <div class="feature-item">
                                <h3><i class="fas fa-database"></i> 安全存储模块</h3>
                                <p>安全存储密钥和配置，采用硬件安全模块保护敏感数据。</p>
                            </div>
                            <div class="feature-item">
                                <h3><i class="fas fa-satellite-dish"></i> 远程通信模块</h3>
                                <p>与云平台通信，接收远程控制指令和安全更新。</p>
                            </div>
                            <div class="feature-item">
                                <h3><i class="fas fa-clock"></i> 时间同步模块</h3>
                                <p>确保车辆时间系统与标准时间的同步，支持安全校验。</p>
                            </div>
                            <div class="feature-item">
                                <h3><i class="fas fa-exchange-alt"></i> 暗号交换模块</h3>
                                <p>与手机协商生成临时暗号，用于身份验证和加密通信。</p>
                            </div>
                            <div class="feature-item">
                                <h3><i class="fas fa-chart-bar"></i> 用户行为分析</h3>
                                <p>分析用户行为和距离，执行无感控车的核心算法。</p>
                            </div>
                            <div class="feature-item">
                                <h3><i class="fas fa-exclamation-circle"></i> 异常处理模块</h3>
                                <p>监测和处理异常连接和操作，确保系统安全运行。</p>
                            </div>
                        </div>
                    </div>
                    
                    <div id="tab-cloud" class="tab-content">
                        <div class="feature-list">
                            <div class="feature-item">
                                <h3><i class="fas fa-key"></i> 钥匙生命周期管理</h3>
                                <p>管理钥匙的创建、授权、共享和撤销，维护钥匙权限。</p>
                            </div>
                            <div class="feature-item">
                                <h3><i class="fas fa-car"></i> 车辆关联服务</h3>
                                <p>管理车辆信息和绑定关系，维护车主和授权用户。</p>
                            </div>
                            <div class="feature-item">
                                <h3><i class="fas fa-user-shield"></i> 安全认证中心</h3>
                                <p>负责身份验证和安全事件处理，是安全体系的核心。</p>
                            </div>
                            <div class="feature-item">
                                <h3><i class="fas fa-key"></i> 密钥管理系统</h3>
                                <p>生成和管理各种密钥，支持密钥更新和撤销。</p>
                            </div>
                            <div class="feature-item">
                                <h3><i class="fas fa-clock"></i> 时间服务器</h3>
                                <p>提供精确时间基准，确保各端系统时间同步。</p>
                            </div>
                            <div class="feature-item">
                                <h3><i class="fas fa-plug"></i> 统一接口服务</h3>
                                <p>提供标准接口和安全通信，支持多种客户端接入。</p>
                            </div>
                            <div class="feature-item">
                                <h3><i class="fas fa-lock"></i> 安全通信通道</h3>
                                <p>提供加密通信和防篡改措施，确保数据传输安全。</p>
                            </div>
                            <div class="feature-item">
                                <h3><i class="fas fa-eye"></i> 异常监控与处理</h3>
                                <p>监控异常登录和使用模式，及时发现安全威胁。</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 业务流程部分 -->
        <div id="processes" class="section fade-in">
            <div class="section-header">
                <h2><i class="fas fa-link"></i> 核心业务流程</h2>
            </div>
            <div class="section-content">
                <div class="cards">
                    <div class="card">
                        <div class="card-header">
                            <h3><i class="fas fa-link"></i> 首次配对流程</h3>
                        </div>
                        <div class="card-body">
                            <p>数字钥匙首次与车辆配对的过程，建立安全连接并授权使用权限。</p>
                            <div class="btn" onclick="showProcess('pairing')">查看详情</div>
                        </div>
                    </div>
                    <div class="card">
                        <div class="card-header">
                            <h3><i class="fas fa-bluetooth"></i> 蓝牙通讯过程</h3>
                        </div>
                        <div class="card-body">
                            <p>手机与车辆之间安全通讯的建立、身份验证和数据交换。</p>
                            <div class="btn" onclick="showProcess('communication')">查看详情</div>
                        </div>
                    </div>
                    <div class="card">
                        <div class="card-header">
                            <h3><i class="fas fa-magic"></i> 无感控车流程</h3>
                        </div>
                        <div class="card-body">
                            <p>用户接近车辆时自动解锁、离开时自动锁车的无感操作流程。</p>
                            <div class="btn" onclick="showProcess('passive-control')">查看详情</div>
                        </div>
                    </div>
                </div>

                <div id="pairing-process" class="process-details" style="display: none;">
                    <h3><i class="fas fa-list-ol"></i> 配对流程概述</h3>
                    <ol class="custom-list">
                        <li>用户在APP中选择添加数字钥匙</li>
                        <li>扫描车辆二维码或输入VIN码识别车辆</li>
                        <li>云平台生成虚拟密钥和配对令牌</li>
                        <li>手机与车辆建立蓝牙连接</li>
                        <li>双方进行安全验证</li>
                        <li>建立加密安全通道</li>
                        <li>完成配对并记录绑定关系</li>
                    </ol>
                    
                    <div class="process-diagram">
                        <img data-html-src="diagrams/pairing-swimlane.html" alt="首次配对泳道图">
                        <figcaption><i class="fas fa-info-circle"></i> 手机、车辆和云平台之间的交互过程</figcaption>
                    </div>
                    
                    <h3><i class="fas fa-shield-alt"></i> 配对安全保障</h3>
                    <ul class="custom-list">
                        <li>双向验证确保设备身份</li>
                        <li>一次性配对码防止重放攻击</li>
                        <li>临时暗号定期更换</li>
                        <li>密钥存储在特殊安全区域</li>
                        <li>使用蓝牙4.2+安全特性</li>
                        <li>距离测量防止中继攻击</li>
                    </ul>
                </div>

                <div id="communication-process" class="process-details" style="display: none;">
                    <h3><i class="fas fa-list-ol"></i> 通讯流程概述</h3>
                    <ol class="custom-list">
                        <li>手机识别车辆蓝牙广播信号</li>
                        <li>建立蓝牙连接</li>
                        <li>进行身份验证</li>
                        <li>建立安全通道</li>
                        <li>发送控车指令</li>
                        <li>执行操作并返回结果</li>
                    </ol>
                    
                    <h3><i class="fas fa-lock"></i> 通讯安全机制</h3>
                    <ul class="custom-list">
                        <li>会话密钥加密所有通信内容</li>
                        <li>每条消息都有唯一标识，防止重放</li>
                        <li>消息完整性校验，防止篡改</li>
                        <li>会话密钥定期更新</li>
                        <li>通信异常自动处理</li>
                    </ul>
                </div>

                <div id="passive-control-process" class="process-details" style="display: none;">
                    <h3><i class="fas fa-list-ol"></i> 无感控车流程</h3>
                    <ol class="custom-list">
                        <li>APP后台检测用户状态和环境</li>
                        <li>满足条件时自动发起蓝牙连接</li>
                        <li>快速建立安全通道</li>
                        <li>车端计算与手机的距离</li>
                        <li>距离在安全范围内时执行预设操作</li>
                        <li>用户离开时自动断开连接并锁车</li>
                    </ol>
                    
                    <div class="process-diagram">
                        <img data-html-src="diagrams/passive-control-swimlane.html" alt="无感控车泳道图">
                        <figcaption><i class="fas fa-info-circle"></i> 无感控车的详细交互流程</figcaption>
                    </div>
                    
                    <h3><i class="fas fa-ruler-combined"></i> 距离计算算法</h3>
                    <ul class="custom-list">
                        <li>基于RSSI信号强度计算距离</li>
                        <li>考虑环境干扰因素</li>
                        <li>应用机器学习算法过滤干扰</li>
                        <li>多节点定位提高精度</li>
                        <li>根据距离执行分级权限控制</li>
                    </ul>
                    
                    <h3><i class="fas fa-battery-three-quarters"></i> 省电策略</h3>
                    <ul class="custom-list">
                        <li>基于用户行为智能唤醒</li>
                        <li>动态调整扫描频率</li>
                        <li>优化连接参数</li>
                        <li>低电量模式自动调整</li>
                        <li>智能场景识别减少不必要的连接</li>
                    </ul>
                </div>
            </div>
        </div>

        <!-- 安全机制部分 -->
        <div id="security" class="section fade-in">
            <div class="section-header">
                <h2><i class="fas fa-shield-alt"></i> 安全机制设计</h2>
            </div>
            <div class="section-content">
                <div class="cards">
                    <div class="card">
                        <div class="card-header">
                            <h3><i class="fas fa-user-check"></i> 认证与加密</h3>
                        </div>
                        <div class="card-body">
                            <ul class="custom-list">
                                <li>基于挑战-响应的双向身份认证</li>
                                <li>非对称加密技术保护密钥交换</li>
                                <li>对称加密保护通信内容</li>
                                <li>数字签名确保指令完整性</li>
                                <li>随机挑战数防止重放攻击</li>
                            </ul>
                        </div>
                    </div>
                    <div class="card">
                        <div class="card-header">
                            <h3><i class="fas fa-server"></i> 安全基础设施</h3>
                        </div>
                        <div class="card-body">
                            <ul class="custom-list">
                                <li><strong>密码机(HSM)</strong>：执行密钥操作，确保密钥安全</li>
                                <li><strong>证书系统(PKI/CA)</strong>：提供设备身份证书，支持证书验证和撤销</li>
                                <li><strong>密钥备份系统</strong>：安全备份关键信息，防止数据丢失</li>
                                <li><strong>安全监控</strong>：监控系统使用情况，检测异常行为</li>
                            </ul>
                        </div>
                    </div>
                    <div class="card">
                        <div class="card-header">
                            <h3><i class="fas fa-user-shield"></i> 防攻击措施</h3>
                        </div>
                        <div class="card-body">
                            <ul class="custom-list">
                                <li>防中继攻击：基于距离测量和时间窗口验证</li>
                                <li>防重放攻击：使用随机挑战和序列号</li>
                                <li>防篡改：消息完整性校验和数字签名</li>
                                <li>防窃听：端到端加密通信</li>
                                <li>防暴力破解：多次失败自动锁定</li>
                            </ul>
                        </div>
                    </div>
                </div>

                <div class="feature-list">
                    <div class="feature-item">
                        <h3><i class="fas fa-key"></i> 密钥管理机制</h3>
                        <div class="card-body">
                            <h4><i class="fas fa-sitemap"></i> 密钥体系</h4>
                            <ul class="custom-list">
                                <li><strong>根密钥</strong>：车端保存的主密钥，是安全通信的基础</li>
                                <li><strong>虚拟密钥</strong>：手机端保存的密钥，用于识别和连接车辆</li>
                                <li><strong>配对令牌</strong>：双方验证身份的凭证</li>
                                <li><strong>会话密钥</strong>：临时通信密钥，定期更新</li>
                            </ul>
                            
                            <h4><i class="fas fa-database"></i> 密钥存储</h4>
                            <ul class="custom-list">
                                <li>手机端：存储在TEE/SE等安全区域</li>
                                <li>车端：存储在HSM或安全芯片中</li>
                                <li>云平台：使用密码机和分布式存储</li>
                            </ul>
                            
                            <h4><i class="fas fa-sync"></i> 密钥更新机制</h4>
                            <ul class="custom-list">
                                <li>定期自动更新会话密钥</li>
                                <li>根据安全策略更新虚拟密钥</li>
                                <li>异常情况触发紧急更新</li>
                                <li>更新过程不影响用户体验</li>
                            </ul>
                        </div>
                    </div>
                    <div class="feature-item">
                        <h3><i class="fas fa-clock"></i> 时间同步机制</h3>
                        <div class="card-body">
                            <h4><i class="fas fa-exclamation-circle"></i> 时间同步重要性</h4>
                            <ul class="custom-list">
                                <li>确保安全验证的时效性</li>
                                <li>防止重放攻击</li>
                                <li>保证操作记录的准确性</li>
                                <li>支持基于时间的权限控制</li>
                            </ul>
                            
                            <h4><i class="fas fa-cogs"></i> 同步机制设计</h4>
                            <ul class="custom-list">
                                <li>云平台作为时间权威源</li>
                                <li>支持NTP/PTP时间同步协议</li>
                                <li>车端与云端定期同步</li>
                                <li>手机端与网络时间服务器同步</li>
                                <li>通信过程中的时间偏差校正</li>
                                <li>离线状态下的时钟漂移补偿</li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 应急处理部分 -->
        <div id="emergency" class="section fade-in">
            <div class="section-header">
                <h2><i class="fas fa-exclamation-triangle"></i> 故障应急处理机制</h2>
            </div>
            <div class="section-content">
                <p>数字钥匙系统设计了完善的故障应急处理机制，以应对各种异常情况，确保系统的稳定性和用户体验。</p>
                
                <div class="feature-list">
                    <div class="feature-item">
                        <h3><i class="fas fa-mobile-alt"></i> <i class="fas fa-times"></i> 丢失手机处理</h3>
                        <ul class="custom-list">
                            <li>用户通过云平台远程撤销钥匙</li>
                            <li>支持多设备登录，在其他设备上操作</li>
                            <li>紧急联系人协助处理</li>
                            <li>备用物理钥匙应急</li>
                        </ul>
                    </div>
                    <div class="feature-item">
                        <h3><i class="fas fa-exchange-alt"></i> 换手机处理</h3>
                        <ul class="custom-list">
                            <li>在新手机上登录账号</li>
                            <li>云平台验证身份后恢复钥匙</li>
                            <li>旧手机钥匙自动失效</li>
                            <li>新手机重新配对或直接恢复</li>
                        </ul>
                    </div>
                    <div class="feature-item">
                        <h3><i class="fas fa-wifi"></i> <i class="fas fa-times"></i> 通信异常处理</h3>
                        <ul class="custom-list">
                            <li>自动重连机制</li>
                            <li>备用通信通道</li>
                            <li>离线操作缓存</li>
                            <li>网络恢复后自动同步</li>
                        </ul>
                    </div>
                    <div class="feature-item">
                        <h3><i class="fas fa-user-secret"></i> 安全威胁处理</h3>
                        <ul class="custom-list">
                            <li>异常行为检测与阻断</li>
                            <li>紧急锁定机制</li>
                            <li>安全事件上报与分析</li>
                            <li>远程安全干预</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>

        <!-- 页脚区域 -->
        <div class="footer">
            <div class="footer-content">
                <div class="footer-section">
                    <h3>关于我们</h3>
                    <p>零感智能科技致力于为用户提供安全、便捷的数字钥匙解决方案，让用户的移动设备成为智能汽车的钥匙。</p>
                </div>
                <div class="footer-section">
                    <h3>联系方式</h3>
                    <p><i class="fas fa-envelope"></i> <EMAIL></p>
                    <p><i class="fas fa-phone"></i> +86 123 4567 8910</p>
                </div>
                <div class="footer-section">
                    <h3>技术支持</h3>
                    <p><i class="fas fa-headset"></i> <EMAIL></p>
                    <p><i class="fas fa-question-circle"></i> 常见问题解答</p>
                </div>
            </div>
            <div class="footer-bottom">
                <p>&copy; 2023 零感智能科技 版权所有</p>
            </div>
        </div>
    </div>

    <script src="js/image-loader.js"></script>
    <script>
        // 标签页切换功能
        function openTab(evt, tabId) {
            var i, tabContent, tabBtns;
            
            tabContent = document.getElementsByClassName("tab-content");
            for (i = 0; i < tabContent.length; i++) {
                tabContent[i].style.display = "none";
            }
            
            tabBtns = document.getElementsByClassName("tab-btn");
            for (i = 0; i < tabBtns.length; i++) {
                tabBtns[i].className = tabBtns[i].className.replace(" active", "");
            }
            
            document.getElementById(tabId).style.display = "block";
            evt.currentTarget.className += " active";
        }
        
        // 流程详情显示
        function showProcess(processId) {
            var processes = document.getElementsByClassName("process-details");
            for (var i = 0; i < processes.length; i++) {
                processes[i].style.display = "none";
            }
            
            document.getElementById(processId + "-process").style.display = "block";
            
            // 滚动到流程详情区域
            document.getElementById(processId + "-process").scrollIntoView({
                behavior: 'smooth',
                block: 'start'
            });
        }
    </script>
</body>
</html>

package com.ruoyi.common.core.constant;

/**
 * 缓存常量信息
 * 
 * <AUTHOR>
 */
public class CacheConstants
{
    /**
     * 缓存有效期，默认720（分钟）
     */
    public final static long EXPIRATION = 720;

    /**
     * 缓存刷新时间，默认120（分钟）
     */
    public final static long REFRESH_TIME = 120;

    /**
     * 密码最大错误次数
     */
    public final static int PASSWORD_MAX_RETRY_COUNT = 5;

    /**
     * 密码锁定时间，默认10（分钟）
     */
    public final static long PASSWORD_LOCK_TIME = 10;

    /**
     * 权限缓存前缀
     */
    public final static String LOGIN_TOKEN_KEY = "dk_login_tokens:";

    /**
     * 验证码 redis key
     */
    public static final String CAPTCHA_CODE_KEY = "dk_captcha_codes:";

    /**
     * 参数管理 cache key
     */
    public static final String SYS_CONFIG_KEY = "dk_sys_config:";

    /**
     * 字典管理 cache key
     */
    public static final String SYS_DICT_KEY = "dk_sys_dict:";

    /**
     * 登录账户密码错误次数 redis key
     */
    public static final String PWD_ERR_CNT_KEY = "dk_pwd_err_cnt:";

    /**
     * 车辆数字钥匙最大分享次数 redis key
     */
    public static final String DK_SHARING_QUANTITY = "dk_sharing_quantity";

    /**
     * 车辆数字钥匙分享有效时间 redis key
     */
    public static final String DK_SHARING_EFFECTIVE_TIME = "dk_sharing_effective_time";

    /**
     * 登录IP黑名单 cache key
     */
    public static final String SYS_LOGIN_BLACKIPLIST = SYS_CONFIG_KEY + "sys.login.blackIPList";
}

<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>整体架构图</title>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/@fortawesome/fontawesome-free@5.15.4/css/all.min.css">
    <style>
        body {
            font-family: 'Microsoft YaHei', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
            color: #333;
        }
        .architecture-container {
            max-width: 1200px;
            margin: 0 auto;
            background-color: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
        }
        .header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 30px;
            border-bottom: 2px solid #3498db;
            padding-bottom: 10px;
        }
        .header h1 {
            color: #2874a6;
            margin: 0;
        }
        .logo {
            max-height: 60px;
        }
        .system-title {
            background: linear-gradient(135deg, #1a5276 0%, #3498db 100%);
            color: white;
            padding: 10px 20px;
            border-radius: 5px 5px 0 0;
            font-weight: bold;
            font-size: 1.2em;
            display: flex;
            align-items: center;
        }
        .system-title i {
            margin-right: 10px;
        }
        .system-container {
            border: 1px solid #ddd;
            border-radius: 5px;
            margin-bottom: 20px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.05);
        }
        .system-content {
            padding: 15px;
            background-color: #f8f9fa;
        }
        .module-group {
            display: flex;
            flex-wrap: wrap;
            gap: 15px;
            margin-bottom: 15px;
        }
        .module-title {
            width: 100%;
            background-color: #e8f4fc;
            padding: 8px 15px;
            font-weight: bold;
            border-left: 4px solid #3498db;
            margin-bottom: 10px;
        }
        .module {
            flex: 1;
            min-width: 200px;
            background-color: #d4e6f1;
            border-radius: 5px;
            padding: 10px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .module h3 {
            margin-top: 0;
            margin-bottom: 10px;
            font-size: 1em;
            color: #2c3e50;
            text-align: center;
            padding-bottom: 5px;
            border-bottom: 1px solid rgba(0,0,0,0.1);
        }
        .function-list {
            display: flex;
            flex-wrap: wrap;
            gap: 5px;
        }
        .function {
            background-color: #3498db;
            color: white;
            padding: 5px 10px;
            border-radius: 3px;
            font-size: 0.9em;
            text-align: center;
            flex: 1;
            min-width: 100px;
        }
        .database-container {
            display: flex;
            gap: 15px;
            margin-top: 15px;
        }
        .database {
            flex: 1;
            background-color: #eaeded;
            border-radius: 5px;
            padding: 10px;
            text-align: center;
            position: relative;
        }
        .database::before {
            content: "";
            position: absolute;
            top: -10px;
            left: 50%;
            transform: translateX(-50%);
            width: 0;
            height: 0;
            border-left: 10px solid transparent;
            border-right: 10px solid transparent;
            border-bottom: 10px solid #eaeded;
        }
        .external-systems {
            display: flex;
            justify-content: space-around;
            margin-top: 20px;
            gap: 15px;
        }
        .external-system {
            flex: 1;
            background-color: #fdebd0;
            border: 1px solid #f39c12;
            border-radius: 50px;
            padding: 15px;
            text-align: center;
            max-width: 200px;
        }
        .connection-line {
            text-align: center;
            margin: 10px 0;
            color: #7f8c8d;
            font-size: 1.5em;
        }
        .page-number {
            text-align: right;
            margin-top: 20px;
            color: #7f8c8d;
            font-size: 0.9em;
        }
    </style>
</head>
<body>
    <div class="architecture-container">
        <div class="header">
            <h1>数字钥匙系统架构</h1>
            <div>
                <img src="data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHZpZXdCb3g9IjAgMCAyMDAgNTAiPjxwYXRoIGZpbGw9IiMzNDk4ZGIiIGQ9Ik0yMCwxMEMzMCw1IDQwLDUgNTAsMTBDNjAsMTUgNzAsMTUgODAsMTBDOTAsNSAxMDAsNSAxMTAsMTBDMTIwLDE1IDEzMCwxNSAxNDAsMTBDMTUwLDUgMTYwLDUgMTcwLDEwQzE4MCwxNSAxOTAsMTUgMjAwLDEwVjQwQzE5MCw0NSAxODAsNDUgMTcwLDQwQzE2MCwzNSAxNTAsMzUgMTQwLDQwQzEzMCw0NSAxMjAsNDUgMTEwLDQwQzEwMCwzNSA5MCwzNSA4MCw0MEM3MCw0NSA2MCw0NSA1MCw0MEM0MCwzNSAzMCwzNSAyMCw0MEMxMCw0NSAwLDQ1IC0xMCw0MFYxMEMwLDUgMTAsNSAyMCwxMFoiLz48dGV4dCB4PSI4MCIgeT0iMzAiIGZvbnQtZmFtaWx5PSJBcmlhbCIgZm9udC1zaXplPSIxNiIgZmlsbD0id2hpdGUiIHRleHQtYW5jaG9yPSJtaWRkbGUiPnplcm9zZW5zZTwvdGV4dD48L3N2Zz4=" alt="零感智能" class="logo">
            </div>
        </div>
        
        <div class="system-container">
            <div class="system-title">
                <i class="fas fa-cloud"></i> 云端 - 数字钥匙服务 Digital Key Server
            </div>
            <div class="system-content">
                <div class="module-group">
                    <div class="module-title">钥匙业务集群</div>
                    <div class="module">
                        <h3>外部服务对接</h3>
                        <div class="function-list">
                            <div class="function">生产信息同步</div>
                            <div class="function">售后信息同步</div>
                            <div class="function">人车关系校验</div>
                            <div class="function">定位标定服务</div>
                        </div>
                    </div>
                    <div class="module">
                        <h3>钥匙业务</h3>
                        <div class="function-list">
                            <div class="function">车主钥匙开通/注销</div>
                            <div class="function">钥匙分享/终止</div>
                            <div class="function">钥匙冻结/解冻</div>
                            <div class="function">可信时钟服务</div>
                        </div>
                    </div>
                    <div class="module">
                        <h3>钥匙工厂</h3>
                        <div class="function-list">
                            <div class="function">多协议钥匙生成</div>
                            <div class="function">钥匙加密</div>
                        </div>
                    </div>
                </div>
                
                <div class="module-group">
                    <div class="module-title">运维/运营集群</div>
                    <div class="module">
                        <h3>监控系统</h3>
                        <div class="function-list">
                            <div class="function">服务监控</div>
                            <div class="function">异常告警</div>
                        </div>
                    </div>
                    <div class="module">
                        <h3>日志系统</h3>
                        <div class="function-list">
                            <div class="function">操作日志</div>
                            <div class="function">安全审计</div>
                        </div>
                    </div>
                    <div class="module">
                        <h3>配置管理系统</h3>
                        <div class="function-list">
                            <div class="function">参数配置</div>
                            <div class="function">权限管理</div>
                        </div>
                    </div>
                    <div class="module">
                        <h3>发布系统</h3>
                        <div class="function-list">
                            <div class="function">版本管理</div>
                            <div class="function">灰度发布</div>
                        </div>
                    </div>
                    <div class="module">
                        <h3>运营管理系统</h3>
                        <div class="function-list">
                            <div class="function">数据分析</div>
                            <div class="function">用户管理</div>
                        </div>
                    </div>
                </div>
                
                <div class="module-group">
                    <div class="module-title">远程服务</div>
                    <div class="module">
                        <h3>远程控制</h3>
                        <div class="function-list">
                            <div class="function">远程锁车</div>
                            <div class="function">远程开车</div>
                        </div>
                    </div>
                    <div class="module">
                        <h3>SOTA</h3>
                        <div class="function-list">
                            <div class="function">远程升级</div>
                            <div class="function">配置更新</div>
                        </div>
                    </div>
                </div>
                
                <div class="database-container">
                    <div class="database">
                        <i class="fas fa-database"></i>
                        <div>业务数据库</div>
                    </div>
                    <div class="database">
                        <i class="fas fa-lock"></i>
                        <div>密码机/HSM</div>
                    </div>
                    <div class="database">
                        <i class="fas fa-certificate"></i>
                        <div>PKI/CA</div>
                    </div>
                    <div class="database">
                        <i class="fas fa-route"></i>
                        <div>钥匙追踪服务/KTS</div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="connection-line">
            <i class="fas fa-exchange-alt"></i> 安全通信
        </div>
        
        <div class="external-systems">
            <div class="external-system">
                <i class="fas fa-car"></i>
                <div>车辆厂商服务</div>
                <div>Vehicle OEM Server</div>
            </div>
            <div class="external-system">
                <i class="fas fa-mobile-alt"></i>
                <div>终端厂商服务</div>
                <div>Device OEM Server</div>
            </div>
            <div class="external-system">
                <i class="fas fa-plug"></i>
                <div>第三方开放服务</div>
            </div>
        </div>
        
        <div class="page-number">3</div>
    </div>
</body>
</html> 
<template>
  <view class="container">
    <!-- 自定义导航栏 -->
    <cu-custom bgColor="bg-gradual-blue" :isBack="true">
      <block slot="backText">返回</block>
      <block slot="content">申请状态</block>
    </cu-custom>

    <!-- 加载状态 -->
    <view v-if="loading" class="loading-container">
      <uni-load-more status="loading" />
    </view>

    <!-- 申请列表 -->
    <view v-else class="content">
      <!-- 无申请记录 -->
      <view v-if="applicationList.length === 0" class="empty-container">
        <view class="empty-icon">📋</view>
        <text class="empty-text">暂无申请记录</text>
        <button class="cu-btn bg-blue round margin-top" @click="goToApply">立即申请</button>
      </view>

      <!-- 申请记录列表 -->
      <view v-else class="application-list">
        <view 
          v-for="(item, index) in applicationList" 
          :key="index"
          class="application-item"
          @click="viewDetail(item)"
        >
          <!-- 申请状态标识 -->
          <view class="status-header" :class="[getStatusClassComputed(item.status)]">
            <view class="status-info">
              <text class="status-text">{{ getStatusTextComputed(item.status) }}</text>
              <text class="application-no">申请单号：{{ item.applicationNo }}</text>
            </view>
            <view class="status-icon">
              <text :class="[getStatusIconComputed(item.status)]"></text>
            </view>
          </view>

          <!-- 申请信息 -->
          <view class="application-info">
            <view class="info-row">
              <text class="info-label">申请人：</text>
              <text class="info-value">{{ item.realName }}</text>
            </view>
            <view class="info-row">
              <text class="info-label">手机号：</text>
              <text class="info-value">{{ item.phone }}</text>
            </view>
            <view class="info-row">
              <text class="info-label">申请时间：</text>
              <text class="info-value">{{ formatTimeFilter(item.createTime) }}</text>
            </view>
            <view v-if="item.auditTime" class="info-row">
              <text class="info-label">审核时间：</text>
              <text class="info-value">{{ formatTimeFilter(item.auditTime) }}</text>
            </view>
          </view>

          <!-- 审核备注 -->
          <view v-if="item.auditRemark" class="audit-remark">
            <text class="remark-label">审核备注：</text>
            <text class="remark-content">{{ item.auditRemark }}</text>
          </view>

          <!-- 操作按钮 -->
          <view class="action-buttons">
            <button 
              v-if="item.status === '0'" 
              class="cu-btn line-red sm round"
              @click.stop="withdrawApplication(item)"
            >
              撤销申请
            </button>
            <button 
              v-if="item.status === '3'" 
              class="cu-btn bg-orange sm round"
              @click.stop="supplementApplication(item)"
            >
              补充资料
            </button>
            <button 
              v-if="item.status === '1'" 
              class="cu-btn bg-green sm round"
              @click.stop="activateKey(item)"
            >
              激活钥匙
            </button>
          </view>
        </view>
      </view>

      <!-- 底部操作 -->
      <view class="bottom-actions">
        <button class="cu-btn bg-blue lg round" @click="goToApply">
          <text class="cuIcon-add margin-right-xs"></text>
          新增申请
        </button>
      </view>
    </view>
  </view>
</template>

<script>
import { getUserApplicationList, withdrawApplication as withdrawApplicationApi } from '@/api/dk'

export default {
  data() {
    return {
      loading: true,
      applicationList: []
    }
  },

  computed: {
    // 获取状态样式类的计算属性
    getStatusClassComputed() {
      return (status) => {
        const statusMap = {
          '0': 'status-pending',    // 待审核
          '1': 'status-approved',   // 审核通过
          '2': 'status-rejected',   // 审核拒绝
          '3': 'status-supplement', // 补充资料
          '4': 'status-withdrawn'   // 已撤销
        }
        return statusMap[status] || 'status-pending'
      }
    },

    // 获取状态文本的计算属性
    getStatusTextComputed() {
      return (status) => {
        const statusMap = {
          '0': '待审核',
          '1': '审核通过',
          '2': '审核拒绝',
          '3': '需补充资料'
        }
        return statusMap[status] || '未知状态'
      }
    },

    // 获取状态图标的计算属性
    getStatusIconComputed() {
      return (status) => {
        const iconMap = {
          '0': 'cuIcon-time text-orange',
          '1': 'cuIcon-check text-green',
          '2': 'cuIcon-close text-red',
          '3': 'cuIcon-warn text-yellow'
        }
        return iconMap[status] || 'cuIcon-time'
      }
    },

    // 格式化时间的计算属性
    formatTimeFilter() {
      return (timeStr) => {
        if (!timeStr) return ''
        const date = new Date(timeStr)
        return `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}-${String(date.getDate()).padStart(2, '0')} ${String(date.getHours()).padStart(2, '0')}:${String(date.getMinutes()).padStart(2, '0')}`
      }
    }
  },
  
  onLoad() {
    this.loadApplicationList()
  },
  
  onShow() {
    // 页面显示时刷新数据
    this.loadApplicationList()
  },
  
  methods: {
    // 加载申请列表
    async loadApplicationList() {
      this.loading = true
      try {
        const result = await getUserApplicationList()
        console.log('API返回数据:', result)

        // 若依框架返回的是TableDataInfo结构，数据在rows字段中
        if (result.rows) {
          this.applicationList = result.rows || []
        } else if (result.data) {
          // 兼容其他可能的数据结构
          this.applicationList = result.data || []
        } else {
          this.applicationList = []
        }

        console.log('处理后的申请列表:', this.applicationList)
      } catch (error) {
        console.error('加载申请列表失败:', error)
        uni.showToast({ title: '加载失败', icon: 'none' })
      } finally {
        this.loading = false
      }
    },
    
    // 查看详情
    viewDetail(item) {
      uni.navigateTo({
        url: `/pages/work/applicationDetail/index?id=${item.applicationId}`
      })
    },
    
    // 撤销申请
    async withdrawApplication(item) {
      const result = await uni.showModal({
        title: '确认撤销',
        content: '确定要撤销这个申请吗？撤销后无法恢复。'
      })
      
      if (result.confirm) {
        try {
          await withdrawApplicationApi(item.applicationId)
          uni.showToast({ title: '撤销成功', icon: 'success' })
          this.loadApplicationList() // 刷新列表
        } catch (error) {
          console.error('撤销申请失败:', error)
          uni.showToast({ title: '撤销失败', icon: 'none' })
        }
      }
    },
    
    // 补充资料
    supplementApplication(item) {
      uni.navigateTo({
        url: `/pages/work/supplementApplication/index?id=${item.applicationId}`
      })
    },
    
    // 激活钥匙
    activateKey(item) {
      uni.navigateTo({
        url: `/pages/work/digitalKey/index?activate=true&applicationId=${item.applicationId}`
      })
    },
    
    // 去申请页面
    goToApply() {
      uni.navigateTo({
        url: '/pages/work/userApplication/index'
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.container {
  min-height: 100vh;
  background-color: #f5f5f5;
}

.loading-container {
  padding: 100rpx 0;
  text-align: center;
}

.content {
  padding: 20rpx;
}

/* 空状态样式 */
.empty-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 100rpx 40rpx;
  background: white;
  border-radius: 20rpx;

  .empty-icon {
    font-size: 120rpx;
    margin-bottom: 30rpx;
    opacity: 0.6;
  }

  .empty-text {
    font-size: 28rpx;
    color: #999;
    margin-bottom: 40rpx;
  }
}

/* 申请列表样式 */
.application-list {
  .application-item {
    background: white;
    border-radius: 20rpx;
    margin-bottom: 20rpx;
    overflow: hidden;
  }
}

/* 状态头部样式 */
.status-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 30rpx;
  
  &.status-pending {
    background: linear-gradient(45deg, #ff9a56, #ffad56);
  }
  
  &.status-approved {
    background: linear-gradient(45deg, #39b54a, #8dc63f);
  }
  
  &.status-rejected {
    background: linear-gradient(45deg, #e54d42, #f37b1d);
  }
  
  &.status-supplement {
    background: linear-gradient(45deg, #fbbd08, #ffc107);
  }
  
  .status-info {
    .status-text {
      display: block;
      font-size: 32rpx;
      font-weight: bold;
      color: white;
      margin-bottom: 10rpx;
    }
    
    .application-no {
      font-size: 24rpx;
      color: rgba(255, 255, 255, 0.8);
    }
  }
  
  .status-icon {
    font-size: 60rpx;
    color: white;
  }
}

/* 申请信息样式 */
.application-info {
  padding: 30rpx;
  
  .info-row {
    display: flex;
    margin-bottom: 15rpx;
    
    &:last-child {
      margin-bottom: 0;
    }
    
    .info-label {
      font-size: 28rpx;
      color: #666;
      width: 160rpx;
    }
    
    .info-value {
      font-size: 28rpx;
      color: #333;
      flex: 1;
    }
  }
}

/* 审核备注样式 */
.audit-remark {
  padding: 20rpx 30rpx;
  background: #f8f9fa;
  border-top: 1rpx solid #e5e5e5;
  
  .remark-label {
    font-size: 26rpx;
    color: #666;
  }
  
  .remark-content {
    font-size: 26rpx;
    color: #333;
    line-height: 1.5;
  }
}

/* 操作按钮样式 */
.action-buttons {
  display: flex;
  justify-content: flex-end;
  padding: 20rpx 30rpx;
  border-top: 1rpx solid #e5e5e5;
  
  .cu-btn {
    margin-left: 20rpx;
  }
}

/* 底部操作样式 */
.bottom-actions {
  padding: 40rpx 0;
  text-align: center;
}
</style>

-- H2数据库初始化脚本 - 4G控车功能测试
-- 创建系统配置表
CREATE TABLE IF NOT EXISTS sys_config (
    config_id int NOT NULL AUTO_INCREMENT,
    config_name varchar(100) DEFAULT '' COMMENT '参数名称',
    config_key varchar(100) DEFAULT '' COMMENT '参数键名',
    config_value varchar(500) DEFAULT '' COMMENT '参数键值',
    config_type char(1) DEFAULT 'N' COMMENT '系统内置（Y是 N否）',
    create_by varchar(64) DEFAULT '' COMMENT '创建者',
    create_time datetime DEFAULT NULL COMMENT '创建时间',
    update_by varchar(64) DEFAULT '' COMMENT '更新者',
    update_time datetime DEFAULT NULL COMMENT '更新时间',
    remark varchar(500) DEFAULT NULL COMMENT '备注',
    PRIMARY KEY (config_id)
) COMMENT='参数配置表';

-- 清空并插入基础配置数据
DELETE FROM sys_config WHERE config_key IN ('4g.control.enabled', 'tcp.server.port', 'websocket.server.port');
INSERT INTO sys_config (config_name, config_key, config_value, config_type, create_by, create_time, remark) VALUES
('4G控车功能开关', '4g.control.enabled', 'true', 'Y', 'admin', NOW(), '4G控车功能总开关'),
('TCP服务器端口', 'tcp.server.port', '9999', 'Y', 'admin', NOW(), 'TCP服务器监听端口'),
('WebSocket服务端口', 'websocket.server.port', '9201', 'Y', 'admin', NOW(), 'WebSocket服务监听端口');

-- 创建数字钥匙分享详情表（简化版）
CREATE TABLE IF NOT EXISTS dk_digital_key_share_detail (
    id bigint NOT NULL AUTO_INCREMENT,
    share_id varchar(64) DEFAULT NULL COMMENT '分享ID',
    user_id varchar(64) DEFAULT NULL COMMENT '用户ID',
    vehicle_id varchar(64) DEFAULT NULL COMMENT '车辆ID',
    status char(1) DEFAULT '1' COMMENT '状态（1正常 0停用）',
    create_time datetime DEFAULT NULL COMMENT '创建时间',
    update_time datetime DEFAULT NULL COMMENT '更新时间',
    PRIMARY KEY (id)
) COMMENT='数字钥匙分享详情表';

-- 插入测试数据（如果不存在）
MERGE INTO dk_digital_key_share_detail (share_id, user_id, vehicle_id, status, create_time) VALUES
('test_share_001', '1001', 'vehicle_001', '1', NOW()),
('test_share_002', '1002', 'vehicle_002', '1', NOW());

-- 创建TBOX设备表（用于TCP连接管理）
CREATE TABLE IF NOT EXISTS tbox_device (
    device_id varchar(64) NOT NULL,
    vehicle_id varchar(64) DEFAULT NULL COMMENT '车辆ID',
    device_status char(1) DEFAULT '1' COMMENT '设备状态（1在线 0离线）',
    last_heartbeat datetime DEFAULT NULL COMMENT '最后心跳时间',
    lock_status char(1) DEFAULT '3' COMMENT '锁状态（3已锁 4已解锁）',
    create_time datetime DEFAULT NULL COMMENT '创建时间',
    update_time datetime DEFAULT NULL COMMENT '更新时间',
    PRIMARY KEY (device_id)
) COMMENT='TBOX设备表';

-- 插入测试TBOX设备（如果不存在）
MERGE INTO tbox_device (device_id, vehicle_id, device_status, last_heartbeat, lock_status, create_time) VALUES
('TBOX_001', 'vehicle_001', '1', NOW(), '3', NOW()),
('TBOX_002', 'vehicle_002', '1', NOW(), '3', NOW());

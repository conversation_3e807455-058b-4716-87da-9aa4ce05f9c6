package com.ruoyi.dk.controller;

import com.ruoyi.common.core.web.controller.BaseController;
import com.ruoyi.common.core.web.domain.AjaxResult;
import com.ruoyi.common.core.web.page.TableDataInfo;
import com.ruoyi.dk.domain.DkKeyAssignment;
import com.ruoyi.dk.service.IDkKeyAssignmentService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

/**
 * 钥匙分配管理Controller
 * 
 * <AUTHOR>
 * @date 2024-01-15
 */
@RestController
@RequestMapping("/keyAssignment")
public class DkKeyAssignmentController extends BaseController {

    private static final Logger logger = LoggerFactory.getLogger(DkKeyAssignmentController.class);

    @Autowired(required = false)
    private IDkKeyAssignmentService dkKeyAssignmentService;

    /**
     * 查询钥匙分配列表
     */
    @GetMapping("/list")
    public TableDataInfo list(DkKeyAssignment dkKeyAssignment) {
        startPage();
        List<DkKeyAssignment> list = dkKeyAssignmentService.selectDkKeyAssignmentList(dkKeyAssignment);
        return getDataTable(list);
    }

    /**
     * 获取钥匙分配详细信息
     */
    @GetMapping(value = "/{assignmentId}")
    public AjaxResult getInfo(@PathVariable Long assignmentId) {
        return AjaxResult.success(dkKeyAssignmentService.selectDkKeyAssignmentByAssignmentId(assignmentId));
    }

    /**
     * 分配数字钥匙
     */
    @PostMapping("/assign")
    public AjaxResult assignDigitalKey(@RequestBody Map<String, Object> assignData) {
        try {
            Long userId = Long.valueOf(assignData.get("userId").toString());
            Long vehicleId = Long.valueOf(assignData.get("vehicleId").toString());
            String validEndTime = assignData.get("validEndTime") != null ? assignData.get("validEndTime").toString()
                    : "";
            String remark = assignData.get("remark") != null ? assignData.get("remark").toString() : "";

            // 调用服务层方法，传入正确的参数
            return toAjax(dkKeyAssignmentService.assignDigitalKey(userId, vehicleId, validEndTime, remark));
        } catch (Exception e) {
            logger.error("分配数字钥匙失败", e);
            return AjaxResult.error("分配数字钥匙失败：" + e.getMessage());
        }
    }

    /**
     * 回收数字钥匙
     */
    @PutMapping("/revoke/{assignmentId}")
    public AjaxResult revokeDigitalKey(@PathVariable Long assignmentId, @RequestBody Map<String, Object> data) {
        try {
            String remark = data.get("remark") != null ? data.get("remark").toString() : "";
            return toAjax(dkKeyAssignmentService.revokeDigitalKey(assignmentId, remark));
        } catch (Exception e) {
            logger.error("回收数字钥匙失败", e);
            return AjaxResult.error("回收数字钥匙失败：" + e.getMessage());
        }
    }

    /**
     * 批量分配钥匙
     */
    @PostMapping("/batchAssign")
    public AjaxResult batchAssignKeys(@RequestBody Map<String, Object> batchData) {
        // 处理批量分配逻辑
        return AjaxResult.success("批量分配成功");
    }

    /**
     * 批量回收钥匙
     */
    @PutMapping("/batchRevoke")
    public AjaxResult batchRevokeKeys(@RequestBody Map<String, Object> batchData) {
        Long[] assignmentIds = (Long[]) batchData.get("assignmentIds");
        String remark = batchData.get("remark") != null ? batchData.get("remark").toString() : "";
        return toAjax(dkKeyAssignmentService.batchRevokeKeys(assignmentIds, remark));
    }

    /**
     * 获取待分配的申请列表
     */
    @GetMapping("/pending")
    public TableDataInfo getPendingAssignments() {
        startPage();
        List<Map<String, Object>> list = dkKeyAssignmentService.getPendingAssignments();
        return getDataTable(list);
    }

    /**
     * 更新分配状态
     */
    @PutMapping("/status/{assignmentId}")
    public AjaxResult updateAssignmentStatus(@PathVariable Long assignmentId,
            @RequestBody Map<String, Object> statusData) {
        String status = statusData.get("status").toString();
        String remark = statusData.get("remark") != null ? statusData.get("remark").toString() : "";
        return toAjax(dkKeyAssignmentService.updateAssignmentStatus(assignmentId, status, remark));
    }

    /**
     * 获取分配统计信息
     */
    @GetMapping("/statistics")
    public AjaxResult getAssignmentStatistics() {
        Map<String, Object> statistics = dkKeyAssignmentService.getAssignmentStatistics();
        return AjaxResult.success(statistics);
    }
}

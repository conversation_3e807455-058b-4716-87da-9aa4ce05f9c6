# TBOX设备TCP通信对接文档

## 📋 文档概述

本文档专门为TBOX设备开发人员提供与4G控车云平台的TCP通信对接指南。TBOX设备只需要关心TCP连接和通信协议，无需关心WebSocket或HTTP接口。

## 🌐 TCP服务器信息

### 连接参数
- **服务器地址**: `dk-api.scsoi.com`
- **TCP端口**: `9999`
- **连接方式**: TBOX作为TCP客户端主动连接云平台

### 完整连接地址
```
dk-api.scsoi.com:9999
```

## 🔌 TCP连接建立

### 连接方式
TBOX设备作为**TCP客户端**主动连接云平台TCP服务器：

```java
// Java示例
Socket socket = new Socket("dk-api.scsoi.com", 9999);
```

```python
# Python示例
import socket
sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
sock.connect(("dk-api.scsoi.com", 9999))
```

```c
// C语言示例
struct sockaddr_in server_addr;
int sock = socket(AF_INET, SOCK_STREAM, 0);
server_addr.sin_family = AF_INET;
server_addr.sin_port = htons(9999);
inet_pton(AF_INET, "dk-api.scsoi.com", &server_addr.sin_addr);
connect(sock, (struct sockaddr*)&server_addr, sizeof(server_addr));
```

### 连接要求
- **长连接**: 保持TCP连接不断开，用于实时接收控车指令
- **心跳机制**: 建议每30秒发送一次心跳消息保持连接活跃
- **自动重连**: 连接断开后需要自动重连
- **并发支持**: 云平台支持多个TBOX设备同时连接

## 📡 4G控车通信协议

### 协议格式
所有消息采用十六进制字符串格式，遵循以下结构：

```
7E + 固定头部 + 状态码 + 固定尾部 + 7E
```

### 完整协议格式
```
7E000A000A000102030405060708090A00003033303230303031[状态码]007E
```

### 状态码定义
- **03**: 闭锁状态
- **04**: 解锁状态

### 标准消息示例

#### 解锁状态消息
```
7E000A000A000102030405060708090A000030333032303030313034007E
```

#### 闭锁状态消息
```
7E000A000A000102030405060708090A000030333032303030313033007E
```

## 🚗 TBOX设备通信流程

### 1. 连接建立
```
TBOX设备 → TCP连接 → 云平台服务器(dk-api.scsoi.com:9999)
```

### 2. 接收控车指令
云平台会向TBOX发送控车指令：
- **解锁指令**: `7E000A000A000102030405060708090A000030333032303030313034007E`
- **闭锁指令**: `7E000A000A000102030405060708090A000030333032303030313033007E`

### 3. 状态上报
TBOX执行控车操作后，需要上报当前状态：
- **解锁成功**: 发送解锁状态消息
- **闭锁成功**: 发送闭锁状态消息
- **操作失败**: 发送错误状态消息

### 4. 心跳保活
定期发送心跳消息保持连接：
```
7E000A000A000102030405060708090A000030333032303030313030007E
```

## 🔧 TBOX开发实现示例

### Java实现示例
```java
public class TboxClient {
    private static final String SERVER_HOST = "dk-api.scsoi.com";
    private static final int SERVER_PORT = 9999;
    
    private Socket socket;
    private BufferedReader reader;
    private PrintWriter writer;
    
    public void connect() throws IOException {
        socket = new Socket(SERVER_HOST, SERVER_PORT);
        reader = new BufferedReader(new InputStreamReader(socket.getInputStream()));
        writer = new PrintWriter(socket.getOutputStream(), true);
        
        // 启动消息监听线程
        new Thread(this::listenForMessages).start();
        
        // 启动心跳线程
        new Thread(this::sendHeartbeat).start();
    }
    
    private void listenForMessages() {
        try {
            String message;
            while ((message = reader.readLine()) != null) {
                handleControlCommand(message);
            }
        } catch (IOException e) {
            // 处理连接异常，尝试重连
            reconnect();
        }
    }
    
    private void handleControlCommand(String command) {
        if (command.contains("04")) {
            // 收到解锁指令
            executeUnlock();
            sendStatusReport("04"); // 上报解锁状态
        } else if (command.contains("03")) {
            // 收到闭锁指令
            executeLock();
            sendStatusReport("03"); // 上报闭锁状态
        }
    }
    
    private void sendStatusReport(String status) {
        String message = "7E000A000A000102030405060708090A00003033303230303031" + status + "007E";
        writer.println(message);
    }
    
    private void sendHeartbeat() {
        while (true) {
            try {
                Thread.sleep(30000); // 30秒心跳
                String heartbeat = "7E000A000A000102030405060708090A000030333032303030313030007E";
                writer.println(heartbeat);
            } catch (InterruptedException e) {
                break;
            }
        }
    }
}
```

### Python实现示例
```python
import socket
import threading
import time

class TboxClient:
    def __init__(self):
        self.host = "dk-api.scsoi.com"
        self.port = 9999
        self.socket = None
        self.running = False
    
    def connect(self):
        try:
            self.socket = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            self.socket.connect((self.host, self.port))
            self.running = True
            
            # 启动消息监听线程
            threading.Thread(target=self.listen_messages, daemon=True).start()
            
            # 启动心跳线程
            threading.Thread(target=self.send_heartbeat, daemon=True).start()
            
            print(f"TBOX已连接到服务器: {self.host}:{self.port}")
            
        except Exception as e:
            print(f"连接失败: {e}")
            self.reconnect()
    
    def listen_messages(self):
        try:
            while self.running:
                data = self.socket.recv(1024).decode('utf-8')
                if data:
                    self.handle_control_command(data)
        except Exception as e:
            print(f"消息监听异常: {e}")
            self.reconnect()
    
    def handle_control_command(self, command):
        if "04" in command:
            # 收到解锁指令
            self.execute_unlock()
            self.send_status_report("04")
        elif "03" in command:
            # 收到闭锁指令
            self.execute_lock()
            self.send_status_report("03")
    
    def send_status_report(self, status):
        message = f"7E000A000A000102030405060708090A00003033303230303031{status}007E"
        self.socket.send(message.encode('utf-8'))
    
    def send_heartbeat(self):
        while self.running:
            try:
                time.sleep(30)  # 30秒心跳
                heartbeat = "7E000A000A000102030405060708090A000030333032303030313030007E"
                self.socket.send(heartbeat.encode('utf-8'))
            except Exception as e:
                print(f"心跳发送失败: {e}")
                break
```

## 🌐 REST API接口

### 基础URL
```
https://dk-api.scsoi.com:4/api/vehicle
```

### 1. 车辆解锁
```bash
curl -X POST "https://dk-api.scsoi.com:4/api/vehicle/unlock" \
  -H "Content-Type: application/json"
```

### 2. 车辆闭锁
```bash
curl -X POST "https://dk-api.scsoi.com:4/api/vehicle/lock" \
  -H "Content-Type: application/json"
```

### 3. 查询车辆状态
```bash
curl -X GET "https://dk-api.scsoi.com:4/api/vehicle/status" \
  -H "Content-Type: application/json"
```

### 4. 通用控车接口
```bash
curl -X POST "https://dk-api.scsoi.com:4/api/vehicle/control" \
  -H "Content-Type: application/json" \
  -d '{
    "action": "unlock",
    "tboxId": "TBOX_001"
  }'
```

### 响应格式
```json
{
  "code": 200,
  "msg": "操作成功",
  "data": {
    "action": "unlock",
    "status": "success",
    "timestamp": 1691234567890
  }
}
```

## 📱 WebSocket接口

### 连接地址
```
wss://dk-api.scsoi.com:4/websocket/message?userId=1001
```

### 消息格式
```json
{
  "messageType": 4,
  "message": "unlock",
  "senderName": "TBOX设备",
  "senderId": 1001,
  "timestamp": 1691234567890
}
```

### 消息类型
- **messageType: 4** - 4G控车指令
- **messageType: 5** - 状态查询
- **messageType: 6** - 心跳消息

## 🔍 测试和调试

### 1. 连接测试
使用telnet测试TCP连接：
```bash
telnet dk-api.scsoi.com 9999
```

### 2. 协议测试
连接成功后发送测试消息：
```
7E000A000A000102030405060708090A000030333032303030313034007E
```

### 3. API测试
使用curl测试REST接口：
```bash
curl -X GET "https://dk-api.scsoi.com:4/api/vehicle/status"
```

## ⚠️ 注意事项

### 1. 网络要求
- 确保TBOX设备能够访问公网
- 支持TCP出站连接到9999端口
- 支持HTTPS/WSS协议

### 2. 协议要求
- 严格按照协议格式发送消息
- 消息必须以7E开头和结尾
- 状态码必须正确（03=闭锁，04=解锁）

### 3. 连接管理
- 实现自动重连机制
- 定期发送心跳保持连接
- 处理网络异常情况

### 4. 安全考虑
- 建议实现消息加密
- 添加设备身份验证
- 记录操作日志

## 📞 技术支持

如有技术问题，请联系开发团队：
- **服务器地址**: dk-api.scsoi.com:4
- **TCP端口**: 9999
- **协议版本**: v1.0
- **文档版本**: v1.0

---

**注意**: 本文档基于当前云平台配置生成，如有变更请及时更新TBOX设备配置。

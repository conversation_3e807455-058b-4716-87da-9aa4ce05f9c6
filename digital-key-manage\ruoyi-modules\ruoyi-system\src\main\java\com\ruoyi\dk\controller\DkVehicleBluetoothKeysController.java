package com.ruoyi.dk.controller;

import java.util.List;
import java.io.IOException;
import javax.servlet.http.HttpServletResponse;

import com.ruoyi.common.security.utils.SecurityUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import com.ruoyi.common.log.annotation.Log;
import com.ruoyi.common.log.enums.BusinessType;
import com.ruoyi.common.security.annotation.RequiresPermissions;
import com.ruoyi.dk.domain.DkVehicleBluetoothKeys;
import com.ruoyi.dk.service.IDkVehicleBluetoothKeysService;
import com.ruoyi.common.core.web.controller.BaseController;
import com.ruoyi.common.core.web.domain.AjaxResult;
import com.ruoyi.common.core.utils.poi.ExcelUtil;
import com.ruoyi.common.core.web.page.TableDataInfo;

/**
 * 蓝牙密钥Controller
 * 
 * <AUTHOR>
 * @date 2024-05-14
 */
@RestController
@RequestMapping("/vehicleBluetoothKeys")
public class DkVehicleBluetoothKeysController extends BaseController
{
    @Autowired
    private IDkVehicleBluetoothKeysService dkVehicleBluetoothKeysService;

    /**
     * 查询蓝牙密钥列表
     */
    @RequiresPermissions("dk:vehicleBluetoothKeys:list")
    @GetMapping("/list")
    public TableDataInfo list(DkVehicleBluetoothKeys dkVehicleBluetoothKeys)
    {
        startPage();
        List<DkVehicleBluetoothKeys> list = dkVehicleBluetoothKeysService.selectDkVehicleBluetoothKeysList(dkVehicleBluetoothKeys);
        return getDataTable(list);
    }

    /**
     * 查询蓝牙密钥列表(根据当前用户获取)
     */
    @GetMapping("/listByCurrentUser")
    public TableDataInfo listByCurrentUser(DkVehicleBluetoothKeys dkVehicleBluetoothKeys)
    {
        dkVehicleBluetoothKeys.setUserName(SecurityUtils.getUsername());
        List<DkVehicleBluetoothKeys> list = dkVehicleBluetoothKeysService.selectDkVehicleBluetoothKeysList(dkVehicleBluetoothKeys);
        return getDataTable(list);
    }

    /**
     * 导出蓝牙密钥列表
     */
    @RequiresPermissions("dk:vehicleBluetoothKeys:export")
    @Log(title = "蓝牙密钥", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, DkVehicleBluetoothKeys dkVehicleBluetoothKeys)
    {
        List<DkVehicleBluetoothKeys> list = dkVehicleBluetoothKeysService.selectDkVehicleBluetoothKeysList(dkVehicleBluetoothKeys);
        ExcelUtil<DkVehicleBluetoothKeys> util = new ExcelUtil<DkVehicleBluetoothKeys>(DkVehicleBluetoothKeys.class);
        util.exportExcel(response, list, "蓝牙密钥数据");
    }

    /**
     * 获取蓝牙密钥详细信息
     */
    @RequiresPermissions("dk:vehicleBluetoothKeys:query")
    @GetMapping(value = "/{userId}")
    public AjaxResult getInfo(@PathVariable("userId") Long userId)
    {
        return success(dkVehicleBluetoothKeysService.selectDkVehicleBluetoothKeysByUserId(userId));
    }

    /**
     * 新增蓝牙密钥
     */
    @RequiresPermissions("dk:vehicleBluetoothKeys:add")
    @Log(title = "蓝牙密钥", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody DkVehicleBluetoothKeys dkVehicleBluetoothKeys)
    {
        return toAjax(dkVehicleBluetoothKeysService.insertDkVehicleBluetoothKeys(dkVehicleBluetoothKeys));
    }

    /**
     * 修改蓝牙密钥
     */
    @RequiresPermissions("dk:vehicleBluetoothKeys:edit")
    @Log(title = "蓝牙密钥", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody DkVehicleBluetoothKeys dkVehicleBluetoothKeys)
    {
        return toAjax(dkVehicleBluetoothKeysService.updateDkVehicleBluetoothKeys(dkVehicleBluetoothKeys));
    }

    /**
     * 删除蓝牙密钥
     */
    @RequiresPermissions("dk:vehicleBluetoothKeys:remove")
    @Log(title = "蓝牙密钥", businessType = BusinessType.DELETE)
	@DeleteMapping("/{userIds}")
    public AjaxResult remove(@PathVariable Long[] userIds)
    {
        return toAjax(dkVehicleBluetoothKeysService.deleteDkVehicleBluetoothKeysByUserIds(userIds));
    }

    /**
     * 解绑用户的VIN码
     */
    @Log(title = "蓝牙密钥", businessType = BusinessType.DELETE)
    @DeleteMapping("/unbind/{vinCodes}")
    public AjaxResult unbindVinCodes(@PathVariable String[] vinCodes)
    {
        Long userId = SecurityUtils.getUserId();
        return toAjax(dkVehicleBluetoothKeysService.unbindVinCodes(userId, vinCodes));
    }
}

<!-- 修改内容为2个完整网页截图 -->
<style>
    /* 现有样式保留... */
    
    /* 新增样式：完整网页布局 */
    .complete-webpage {
        display: flex;
        flex-direction: column;
        width: 100%;
        height: 100%;
        background-color: #f5f7f9;
        border-radius: 8px;
        overflow: hidden;
    }
    
    /* 网页顶部导航栏 */
    .webpage-header {
        display: flex;
        align-items: center;
        justify-content: space-between;
        padding: 15px 25px;
        background: linear-gradient(to right, #233753, #3a5275);
        color: white;
        box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    }
    
    .logo-area {
        display: flex;
        align-items: center;
        gap: 10px;
    }
    
    .logo-icon {
        width: 32px;
        height: 32px;
        background-color: white;
        border-radius: 6px;
        display: flex;
        align-items: center;
        justify-content: center;
    }
    
    .logo-text {
        font-size: 18px;
        font-weight: 600;
    }
    
    .header-actions {
        display: flex;
        align-items: center;
        gap: 20px;
    }
    
    .user-profile {
        display: flex;
        align-items: center;
        gap: 10px;
        padding: 5px 10px;
        border-radius: 20px;
        background-color: rgba(255,255,255,0.1);
        cursor: pointer;
    }
    
    .user-avatar {
        width: 30px;
        height: 30px;
        border-radius: 50%;
        background-color: #4a90e2;
        display: flex;
        align-items: center;
        justify-content: center;
        color: white;
        font-weight: bold;
    }
    
    /* 网页主体区域 */
    .webpage-body {
        display: flex;
        flex: 1;
        overflow: hidden;
    }
    
    /* 侧边导航 */
    .sidebar {
        width: 220px;
        background-color: #233753;
        color: white;
        padding: 20px 0;
        box-shadow: 2px 0 10px rgba(0,0,0,0.1);
    }
    
    .sidebar-menu {
        list-style: none;
        padding: 0;
        margin: 0;
    }
    
    .sidebar-menu-item {
        padding: 12px 25px;
        display: flex;
        align-items: center;
        gap: 12px;
        cursor: pointer;
        transition: all 0.2s;
        font-size: 14px;
    }
    
    .sidebar-menu-item:hover {
        background-color: rgba(255,255,255,0.1);
    }
    
    .sidebar-menu-item.active {
        background-color: rgba(255,255,255,0.2);
        border-left: 4px solid #4a90e2;
    }
    
    .menu-icon {
        width: 20px;
        height: 20px;
        opacity: 0.7;
    }
    
    /* 主内容区域 */
    .main-content {
        flex: 1;
        padding: 20px;
        overflow: auto;
    }
    
    .page-title {
        font-size: 22px;
        font-weight: 600;
        margin-bottom: 20px;
        color: #233753;
    }
    
    /* 仪表盘卡片样式 */
    .dashboard-cards {
        display: grid;
        grid-template-columns: repeat(4, 1fr);
        gap: 20px;
        margin-bottom: 20px;
    }
    
    .dashboard-card {
        background-color: white;
        border-radius: 8px;
        padding: 20px;
        box-shadow: 0 2px 10px rgba(0,0,0,0.05);
    }
    
    .card-title {
        font-size: 14px;
        color: #6c757d;
        margin-bottom: 10px;
    }
    
    .card-value {
        font-size: 24px;
        font-weight: 600;
        color: #233753;
    }
    
    .card-trend {
        font-size: 12px;
        margin-top: 5px;
    }
    
    .trend-up {
        color: #28a745;
    }
    
    .trend-down {
        color: #dc3545;
    }
    
    /* 仪表盘图表区域 */
    .dashboard-charts {
        display: grid;
        grid-template-columns: 2fr 1fr;
        gap: 20px;
        margin-bottom: 20px;
    }
    
    .chart-container {
        background-color: white;
        border-radius: 8px;
        padding: 20px;
        box-shadow: 0 2px 10px rgba(0,0,0,0.05);
    }
    
    .chart-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 15px;
    }
    
    .chart-title {
        font-size: 16px;
        font-weight: 600;
        color: #233753;
    }
    
    .chart-legend {
        display: flex;
        gap: 15px;
    }
    
    .legend-item {
        display: flex;
        align-items: center;
        gap: 5px;
        font-size: 12px;
        color: #6c757d;
    }
    
    .legend-color {
        width: 12px;
        height: 12px;
        border-radius: 2px;
    }
    
    .chart-body {
        height: 250px;
        position: relative;
    }
    
    /* 折线图模拟 */
    .line-chart {
        position: relative;
        height: 100%;
        padding-bottom: 20px;
    }
    
    .chart-lines {
        position: absolute;
        bottom: 20px;
        left: 0;
        right: 0;
        top: 0;
    }
    
    .chart-line {
        position: absolute;
        bottom: 0;
        left: 0;
        right: 0;
        height: 1px;
        background-color: #e9ecef;
    }
    
    .chart-line:nth-child(1) { bottom: 0; }
    .chart-line:nth-child(2) { bottom: 25%; }
    .chart-line:nth-child(3) { bottom: 50%; }
    .chart-line:nth-child(4) { bottom: 75%; }
    
    .chart-data {
        position: absolute;
        bottom: 0;
        left: 0;
        right: 0;
        height: 100%;
    }
    
    .data-line {
        position: absolute;
        bottom: 0;
        left: 0;
        right: 0;
        height: 100%;
        z-index: 1;
    }
    
    .data-line::before {
        content: '';
        position: absolute;
        bottom: 0;
        left: 0;
        right: 0;
        height: 60%;
        background: linear-gradient(to right, rgba(74, 144, 226, 0.1), rgba(74, 144, 226, 0.3));
        clip-path: polygon(0 100%, 8.33% 80%, 16.66% 90%, 25% 70%, 33.33% 85%, 41.66% 60%, 50% 75%, 58.33% 50%, 66.66% 65%, 75% 40%, 83.33% 55%, 91.66% 30%, 100% 45%, 100% 100%);
    }
    
    .data-line::after {
        content: '';
        position: absolute;
        bottom: 0;
        left: 0;
        right: 0;
        height: 2px;
        background: linear-gradient(to right, rgba(74, 144, 226, 0.5), rgba(74, 144, 226, 1));
        clip-path: polygon(0 0, 8.33% 100%, 16.66% 40%, 25% 70%, 33.33% 10%, 41.66% 60%, 50% 0, 58.33% 50%, 66.66% 20%, 75% 70%, 83.33% 30%, 91.66% 80%, 100% 10%);
    }
    
    .chart-labels {
        position: absolute;
        bottom: 0;
        left: 0;
        right: 0;
        height: 20px;
        display: flex;
        justify-content: space-between;
    }
    
    .chart-label {
        font-size: 10px;
        color: #6c757d;
        text-align: center;
        width: 8.33%;
    }
    
    /* 饼图模拟 */
    .pie-chart {
        position: relative;
        height: 200px;
        width: 200px;
        margin: 0 auto;
    }
    
    .pie-chart-container {
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        border-radius: 50%;
        background: conic-gradient(
            #4a90e2 0% 45%,
            #f39c12 45% 65%,
            #e74c3c 65% 80%,
            #2ecc71 80% 100%
        );
        box-shadow: 0 4px 10px rgba(0,0,0,0.1);
    }
    
    .pie-center {
        position: absolute;
        top: 25%;
        left: 25%;
        width: 50%;
        height: 50%;
        border-radius: 50%;
        background-color: white;
        display: flex;
        align-items: center;
        justify-content: center;
        box-shadow: 0 2px 5px rgba(0,0,0,0.1);
    }
    
    .pie-data {
        font-size: 20px;
        font-weight: 600;
        color: #233753;
    }
    
    .pie-legends {
        margin-top: 20px;
        display: grid;
        grid-template-columns: 1fr 1fr;
        gap: 10px;
    }
    
    /* 最近活动列表 */
    .recent-activity {
        background-color: white;
        border-radius: 8px;
        padding: 20px;
        box-shadow: 0 2px 10px rgba(0,0,0,0.05);
    }
    
    .activity-list {
        list-style: none;
        padding: 0;
        margin: 0;
    }
    
    .activity-item {
        padding: 15px 0;
        border-bottom: 1px solid #e9ecef;
        display: flex;
        align-items: center;
        gap: 15px;
    }
    
    .activity-item:last-child {
        border-bottom: none;
    }
    
    .activity-icon {
        width: 40px;
        height: 40px;
        border-radius: 8px;
        display: flex;
        align-items: center;
        justify-content: center;
        color: white;
    }
    
    .activity-icon-create {
        background-color: #4a90e2;
    }
    
    .activity-icon-revoke {
        background-color: #e74c3c;
    }
    
    .activity-icon-auth {
        background-color: #2ecc71;
    }
    
    .activity-content {
        flex: 1;
    }
    
    .activity-text {
        font-size: 14px;
        margin-bottom: 5px;
    }
    
    .activity-time {
        font-size: 12px;
        color: #6c757d;
    }
    
    .activity-action {
        padding: 5px 10px;
        border-radius: 4px;
        font-size: 12px;
        background-color: #f8f9fa;
        color: #495057;
        cursor: pointer;
    }
    
    /* 表格样式增强 */
    .data-table-container {
        background-color: white;
        border-radius: 8px;
        padding: 20px;
        box-shadow: 0 2px 10px rgba(0,0,0,0.05);
        margin-bottom: 20px;
    }
    
    .table-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 15px;
    }
    
    .table-title {
        font-size: 16px;
        font-weight: 600;
        color: #233753;
    }
    
    .table-actions {
        display: flex;
        gap: 10px;
    }
    
    .data-table {
        width: 100%;
        border-collapse: collapse;
    }
    
    .data-table th {
        background-color: #f8f9fa;
        padding: 12px 15px;
        text-align: left;
        font-weight: 600;
        color: #495057;
        border-bottom: 2px solid #e9ecef;
    }
    
    .data-table td {
        padding: 12px 15px;
        border-bottom: 1px solid #e9ecef;
    }
    
    .data-table tr:last-child td {
        border-bottom: none;
    }
    
    .data-table tr:hover {
        background-color: #f8f9fa;
    }
    
    .table-pagination {
        display: flex;
        justify-content: flex-end;
        align-items: center;
        margin-top: 15px;
        gap: 10px;
    }
    
    .page-button {
        width: 30px;
        height: 30px;
        border-radius: 4px;
        display: flex;
        align-items: center;
        justify-content: center;
        background-color: #f8f9fa;
        cursor: pointer;
    }
    
    .page-button.active {
        background-color: #4a90e2;
        color: white;
    }
    
    /* 表单控件样式 */
    .form-control {
        display: flex;
        flex-direction: column;
        margin-bottom: 15px;
    }
    
    .form-label {
        font-size: 14px;
        margin-bottom: 5px;
        color: #495057;
    }
    
    .form-input {
        padding: 8px 12px;
        border: 1px solid #ced4da;
        border-radius: 4px;
        font-size: 14px;
    }
    
    .form-select {
        padding: 8px 12px;
        border: 1px solid #ced4da;
        border-radius: 4px;
        font-size: 14px;
        background-color: white;
    }
    
    .key-status {
        padding: 4px 8px;
        border-radius: 4px;
        font-size: 12px;
        text-align: center;
    }
    
    .status-active {
        background-color: #d4edda;
        color: #155724;
    }
    
    .status-pending {
        background-color: #fff3cd;
        color: #856404;
    }
    
    .status-revoked {
        background-color: #f8d7da;
        color: #721c24;
    }
    
    .btn {
        padding: 8px 16px;
        border-radius: 4px;
        font-size: 14px;
        cursor: pointer;
        border: none;
        display: inline-flex;
        align-items: center;
        gap: 5px;
    }
    
    .btn-primary {
        background-color: #4a90e2;
        color: white;
    }
    
    .btn-secondary {
        background-color: #6c757d;
        color: white;
    }
    
    .btn-success {
        background-color: #28a745;
        color: white;
    }
    
    .btn-danger {
        background-color: #dc3545;
        color: white;
    }
    
    /* 响应式调整 */
    .screenshots-grid {
        grid-template-columns: repeat(2, 1fr);
        grid-template-rows: 1fr;
    }
    
    /* 修复截图容器内的样式 */
    .screenshot-image {
        padding: 0;
        background-color: white !important;
        background-image: none !important;
    }
    
    /* 美化面板样式 */
    .ui-panel {
        background-color: #fff;
        border-radius: 4px;
        box-shadow: 0 2px 5px rgba(0,0,0,0.05);
        margin: 0;
        width: 100%;
        height: 100%;
        padding: 0;
        box-sizing: border-box;
        overflow: hidden;
    }
</style>

<!-- 修改slide内容，只保留两个完整网页截图 -->
<div class="slide" id="slide">
    <!-- 顶部标题 -->
    <div class="header">
        <h1>钥匙云平台</h1>
        <div class="subtitle">安全、高效的智能车辆数字钥匙全生命周期管理系统</div>
    </div>
    
    <!-- 功能截图网格 - 改为2列 -->
    <div class="screenshots-grid">
        <!-- 系统仪表盘/首页 -->
        <div class="screenshot-container">
            <div class="screenshot-title-bar">
                <div class="window-controls">
                    <div class="window-circle window-close"></div>
                    <div class="window-circle window-minimize"></div>
                    <div class="window-circle window-maximize"></div>
                </div>
                <div class="screenshot-title">系统仪表盘</div>
            </div>
            <div class="screenshot-content">
                <div class="screenshot-image">
                    <!-- 完整网页UI -->
                    <div class="complete-webpage">
                        <!-- 顶部导航 -->
                        <div class="webpage-header">
                            <div class="logo-area">
                                <div class="logo-icon">
                                    <svg viewBox="0 0 24 24" width="20" height="20" fill="#233753">
                                        <path d="M7,14A2,2 0 0,1 5,12A2,2 0 0,1 7,10A2,2 0 0,1 9,12A2,2 0 0,1 7,14M12.65,10C11.83,7.67 9.61,6 7,6A6,6 0 0,0 1,12A6,6 0 0,0 7,18C9.61,18 11.83,16.33 12.65,14H17V18H21V14H23V10H12.65Z"/>
                                    </svg>
                                </div>
                                <div class="logo-text">钥匙云平台</div>
                            </div>
                            <div class="header-actions">
                                <div style="display: flex; gap: 15px;">
                                    <svg viewBox="0 0 24 24" width="20" height="20" fill="white">
                                        <path d="M21,19V20H3V19L5,17V11C5,7.9 7.03,5.17 10,4.29C10,4.19 10,4.1 10,4A2,2 0 0,1 12,2A2,2 0 0,1 14,4C14,4.1 14,4.19 14,4.29C16.97,5.17 19,7.9 19,11V17L21,19M14,21A2,2 0 0,1 12,23A2,2 0 0,1 10,21"/>
                                    </svg>
                                    <svg viewBox="0 0 24 24" width="20" height="20" fill="white">
                                        <path d="M20,8L12,13L4,8V6L12,11L20,6M20,4H4C2.89,4 2,4.89 2,6V18A2,2 0 0,0 4,20H20A2,2 0 0,0 22,18V6C22,4.89 21.1,4 20,4Z"/>
                                    </svg>
                                    <svg viewBox="0 0 24 24" width="20" height="20" fill="white">
                                        <path d="M12,15.5A3.5,3.5 0 0,1 8.5,12A3.5,3.5 0 0,1 12,8.5A3.5,3.5 0 0,1 15.5,12A3.5,3.5 0 0,1 12,15.5M19.43,12.97C19.47,12.65 19.5,12.33 19.5,12C19.5,11.67 19.47,11.34 19.43,11L21.54,9.37C21.73,9.22 21.78,8.95 21.66,8.73L19.66,5.27C19.54,5.05 19.27,4.96 19.05,5.05L16.56,6.05C16.04,5.66 15.5,5.32 14.87,5.07L14.5,2.42C14.46,2.18 14.25,2 14,2H10C9.75,2 9.54,2.18 9.5,2.42L9.13,5.07C8.5,5.32 7.96,5.66 7.44,6.05L4.95,5.05C4.73,4.96 4.46,5.05 4.34,5.27L2.34,8.73C2.21,8.95 2.27,9.22 2.46,9.37L4.57,11C4.53,11.34 4.5,11.67 4.5,12C4.5,12.33 4.53,12.65 4.57,12.97L2.46,14.63C2.27,14.78 2.21,15.05 2.34,15.27L4.34,18.73C4.46,18.95 4.73,19.03 4.95,18.95L7.44,17.94C7.96,18.34 8.5,18.68 9.13,18.93L9.5,21.58C9.54,21.82 9.75,22 10,22H14C14.25,22 14.46,21.82 14.5,21.58L14.87,18.93C15.5,18.67 16.04,18.34 16.56,17.94L19.05,18.95C19.27,19.03 19.54,18.95 19.66,18.73L21.66,15.27C21.78,15.05 21.73,14.78 21.54,14.63L19.43,12.97Z"/>
                                    </svg>
                                </div>
                                <div class="user-profile">
                                    <div class="user-avatar">A</div>
                                    <span>管理员</span>
                                </div>
                            </div>
                        </div>
                        
                        <!-- 主体部分 -->
                        <div class="webpage-body">
                            <!-- 侧边导航 -->
                            <div class="sidebar">
                                <ul class="sidebar-menu">
                                    <li class="sidebar-menu-item active">
                                        <svg viewBox="0 0 24 24" width="20" height="20" fill="white">
                                            <path d="M13,3V9H21V3M13,21H21V11H13M3,21H11V15H3M3,13H11V3H3V13Z"/>
                                        </svg>
                                        仪表盘
                                    </li>
                                    <li class="sidebar-menu-item">
                                        <svg viewBox="0 0 24 24" width="20" height="20" fill="white">
                                            <path d="M7,14A2,2 0 0,1 5,12A2,2 0 0,1 7,10A2,2 0 0,1 9,12A2,2 0 0,1 7,14M12.65,10C11.83,7.67 9.61,6 7,6A6,6 0 0,0 1,12A6,6 0 0,0 7,18C9.61,18 11.83,16.33 12.65,14H17V18H21V14H23V10H12.65Z"/>
                                        </svg>
                                        钥匙管理
                                    </li>
                                    <li class="sidebar-menu-item">
                                        <svg viewBox="0 0 24 24" width="20" height="20" fill="white">
                                            <path d="M18,18.5A1.5,1.5 0 0,1 16.5,17A1.5,1.5 0 0,1 18,15.5A1.5,1.5 0 0,1 19.5,17A1.5,1.5 0 0,1 18,18.5M19.5,9.5L21.46,12H17V9.5M6,18.5A1.5,1.5 0 0,1 4.5,17A1.5,1.5 0 0,1 6,15.5A1.5,1.5 0 0,1 7.5,17A1.5,1.5 0 0,1 6,18.5M20,8L23,12V17H21A3,3 0 0,1 18,20A3,3 0 0,1 15,17H9A3,3 0 0,1 6,20A3,3 0 0,1 3,17H1V6C1,4.89 1.89,4 3,4H17V8H20Z"/>
                                        </svg>
                                        车辆管理
                                    </li>
                                    <li class="sidebar-menu-item">
                                        <svg viewBox="0 0 24 24" width="20" height="20" fill="white">
                                            <path d="M12,1L3,5V11C3,16.55 6.84,21.74 12,23C17.16,21.74 21,16.55 21,11V5L12,1M12,5A3,3 0 0,1 15,8A3,3 0 0,1 12,11A3,3 0 0,1 9,8A3,3 0 0,1 12,5M17.13,17C15.92,18.85 14.11,20.24 12,20.92C9.89,20.24 8.08,18.85 6.87,17C6.53,16.5 6.24,16 6,15.47C6,13.82 8.71,12.47 12,12.47C15.29,12.47 18,13.79 18,15.47C17.76,16 17.47,16.5 17.13,17Z"/>
                                        </svg>
                                        用户管理
                                    </li>
                                    <li class="sidebar-menu-item">
                                        <svg viewBox="0 0 24 24" width="20" height="20" fill="white">
                                            <path d="M12,9A3,3 0 0,1 15,12A3,3 0 0,1 12,15A3,3 0 0,1 9,12A3,3 0 0,1 12,9M12,4.5C17,4.5 21.27,7.61 23,12C21.27,16.39 17,19.5 12,19.5C7,19.5 2.73,16.39 1,12C2.73,7.61 7,4.5 12,4.5M3.18,12C4.83,15.36 8.24,17.5 12,17.5C15.76,17.5 19.17,15.36 20.82,12C19.17,8.64 15.76,6.5 12,6.5C8.24,6.5 4.83,8.64 3.18,12Z"/>
                                        </svg>
                                        安全监控
                                    </li>
                                    <li class="sidebar-menu-item">
                                        <svg viewBox="0 0 24 24" width="20" height="20" fill="white">
                                            <path d="M20,19V7H4V19H20M20,3A2,2 0 0,1 22,5V19A2,2 0 0,1 20,21H4A2,2 0 0,1 2,19V5A2,2 0 0,1 4,3H20M13,17V15H18V17H13M9.58,13L5.57,9H8.4L11.7,12.3C12.09,12.69 12.09,13.33 11.7,13.72L8.42,17H5.59L9.58,13Z"/>
                                        </svg>
                                        接口服务
                                    </li>
                                    <li class="sidebar-menu-item">
                                        <svg viewBox="0 0 24 24" width="20" height="20" fill="white">
                                            <path d="M12,20A8,8 0 0,0 20,12A8,8 0 0,0 12,4A8,8 0 0,0 4,12A8,8 0 0,0 12,20M12,2A10,10 0 0,1 22,12A10,10 0 0,1 12,22C6.47,22 2,17.5 2,12A10,10 0 0,1 12,2M12.5,7V12.25L17,14.92L16.25,16.15L11,13V7H12.5Z"/>
                                        </svg>
                                        时间服务
                                    </li>
                                    <li class="sidebar-menu-item">
                                        <svg viewBox="0 0 24 24" width="20" height="20" fill="white">
                                            <path d="M12,15.5A3.5,3.5 0 0,1 8.5,12A3.5,3.5 0 0,1 12,8.5A3.5,3.5 0 0,1 15.5,12A3.5,3.5 0 0,1 12,15.5M19.43,12.97C19.47,12.65 19.5,12.33 19.5,12C19.5,11.67 19.47,11.34 19.43,11L21.54,9.37C21.73,9.22 21.78,8.95 21.66,8.73L19.66,5.27C19.54,5.05 19.27,4.96 19.05,5.05L16.56,6.05C16.04,5.66 15.5,5.32 14.87,5.07L14.5,2.42C14.46,2.18 14.25,2 14,2H10C9.75,2 9.54,2.18 9.5,2.42L9.13,5.07C8.5,5.32 7.96,5.66 7.44,6.05L4.95,5.05C4.73,4.96 4.46,5.05 4.34,5.27L2.34,8.73C2.21,8.95 2.27,9.22 2.46,9.37L4.57,11C4.53,11.34 4.5,11.67 4.5,12C4.5,12.33 4.53,12.65 4.57,12.97L2.46,14.63C2.27,14.78 2.21,15.05 2.34,15.27L4.34,18.73C4.46,18.95 4.73,19.03 4.95,18.95L7.44,17.94C7.96,18.34 8.5,18.68 9.13,18.93L9.5,21.58C9.54,21.82 9.75,22 10,22H14C14.25,22 14.46,21.82 14.5,21.58L14.87,18.93C15.5,18.67 16.04,18.34 16.56,17.94L19.05,18.95C19.27,19.03 19.54,18.95 19.66,18.73L21.66,15.27C21.78,15.05 21.73,14.78 21.54,14.63L19.43,12.97Z"/>
                                        </svg>
                                        系统设置
                                    </li>
                                </ul>
                            </div>
                            
                            <!-- 主内容区域 -->
                            <div class="main-content">
                                <div class="page-title">系统概览</div>
                                
                                <!-- 仪表盘卡片 -->
                                <div class="dashboard-cards">
                                    <div class="dashboard-card">
                                        <div class="card-title">活跃钥匙</div>
                                        <div class="card-value">3,258</div>
                                        <div class="card-trend trend-up">↑ 5.6% 较上月</div>
                                    </div>
                                    <div class="dashboard-card">
                                        <div class="card-title">已绑定车辆</div>
                                        <div class="card-value">1,842</div>
                                        <div class="card-trend trend-up">↑ 3.2% 较上月</div>
                                    </div>
                                    <div class="dashboard-card">
                                        <div class="card-title">认证用户</div>
                                        <div class="card-value">2,157</div>
                                        <div class="card-trend trend-up">↑ 7.8% 较上月</div>
                                    </div>
                                    <div class="dashboard-card">
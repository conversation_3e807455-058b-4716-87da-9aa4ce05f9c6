<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>数字钥匙三端架构图</title>
    <script src="https://html2canvas.hertzen.com/dist/html2canvas.min.js"></script>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
            font-family: 'PingFang SC', 'Microsoft YaHei', sans-serif;
        }
        
        body {
            background: #f5f5f5;
            display: flex;
            justify-content: center;
            align-items: center;
            min-height: 100vh;
            padding: 20px;
        }
        
        .ppt-container {
            position: relative;
            width: 1920px;
            height: 1080px;
            background-color: white;
            box-shadow: 0 0 30px rgba(0, 0, 0, 0.1);
            border: 2px dashed #ddd;
        }
        
        .ppt-label {
            position: absolute;
            top: -40px;
            left: 0;
            background-color: #555;
            color: white;
            padding: 5px 15px;
            border-radius: 4px;
            font-size: 16px;
        }
        
        .copy-button {
            position: absolute;
            top: 20px;
            right: 20px;
            background: linear-gradient(135deg, #3498db, #2980b9);
            color: white;
            border: none;
            padding: 12px 25px;
            border-radius: 30px;
            font-size: 18px;
            cursor: pointer;
            z-index: 1000;
            box-shadow: 0 4px 10px rgba(0, 0, 0, 0.2);
            display: flex;
            align-items: center;
            gap: 10px;
            transition: all 0.3s ease;
        }
        
        .download-button {
            position: absolute;
            top: 20px;
            right: 230px;
            background: linear-gradient(135deg, #2ecc71, #27ae60);
            color: white;
            border: none;
            padding: 12px 25px;
            border-radius: 30px;
            font-size: 18px;
            cursor: pointer;
            z-index: 1000;
            box-shadow: 0 4px 10px rgba(0, 0, 0, 0.2);
            display: flex;
            align-items: center;
            gap: 10px;
            transition: all 0.3s ease;
        }
        
        .resolution-selector {
            position: absolute;
            top: 70px;
            right: 230px;
            display: flex;
            align-items: center;
            background: rgba(255, 255, 255, 0.9);
            padding: 8px 15px;
            border-radius: 30px;
            box-shadow: 0 4px 10px rgba(0, 0, 0, 0.1);
            z-index: 1000;
        }
        
        .copy-icon {
            width: 20px;
            height: 20px;
            background-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="white"><path d="M19,21H8V7H19M19,5H8A2,2 0 0,0 6,7V21A2,2 0 0,0 8,23H19A2,2 0 0,0 21,21V7A2,2 0 0,0 19,5M16,1H4A2,2 0 0,0 2,3V17H4V3H16V1Z" /></svg>');
            background-size: contain;
            background-repeat: no-repeat;
        }
        
        .download-icon {
            width: 20px;
            height: 20px;
            background-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="white"><path d="M5,20H19V18H5M19,9H15V3H9V9H5L12,16L19,9Z" /></svg>');
            background-size: contain;
            background-repeat: no-repeat;
        }
        
        .resolution-label {
            margin-right: 10px;
            font-size: 14px;
            color: #555;
        }
        
        .resolution-btn {
            border: none;
            background: #f0f0f0;
            padding: 5px 10px;
            margin: 0 3px;
            border-radius: 15px;
            font-size: 14px;
            cursor: pointer;
            transition: all 0.2s ease;
            color: #555;
        }
        
        .resolution-btn:hover {
            background: #e0e0e0;
        }
        
        .resolution-btn.active {
            background: linear-gradient(135deg, #3498db, #2980b9);
            color: white;
        }
        
        /* 滑动内容区域 */
        .slide {
            width: 100%;
            height: 100%;
            padding: 60px;
            position: relative;
        }
        
        /* 标题样式 */
        .header {
            text-align: center;
            margin-bottom: 40px;
        }
        
        .header h1 {
            font-size: 42px;
            color: #2c3e50;
            margin-bottom: 15px;
            font-weight: 600;
        }
        
        .header .subtitle {
            font-size: 22px;
            color: #7f8c8d;
            font-weight: 400;
        }
        
        /* 架构图容器 */
        .architecture-container {
            position: relative;
            width: 100%;
            height: 750px;
            margin: 0 auto;
        }
        
        /* 系统模块样式 */
        .system-module {
            position: absolute;
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.08);
            background-color: #fff;
        }
        
        /* 模块标题栏 */
        .module-header {
            padding: 16px 20px;
            color: white;
            font-weight: 600;
            font-size: 20px;
            display: flex;
            align-items: center;
        }
        
        .module-header i {
            margin-right: 12px;
            font-size: 22px;
        }
        
        /* 模块内容区 */
        .module-content {
            padding: 25px;
        }
        
        /* 模块说明文本 */
        .module-description {
            font-size: 14px;
            color: #666;
            margin-bottom: 20px;
            line-height: 1.5;
        }
        
        /* 功能核心分组 */
        .function-group {
            margin-bottom: 25px;
        }
        
        .function-group-title {
            font-size: 16px;
            font-weight: 600;
            color: #444;
            margin-bottom: 12px;
            border-left: 4px solid;
            padding-left: 10px;
            line-height: 1.2;
        }
        
        .cloud-group-title {
            border-color: #2478D8;
        }
        
        .mobile-group-title {
            border-color: #3CC562;
        }
        
        .car-group-title {
            border-color: #E9422E;
        }
        
        /* 模块项目 */
        .module-item {
            background-color: #f9f9f9;
            border: 1px solid #eaeaea;
            padding: 12px 16px;
            border-radius: 6px;
            margin-bottom: 10px;
            font-size: 15px;
            color: #333;
            position: relative;
            display: flex;
            align-items: center;
        }
        
        .module-item-icon {
            width: 24px;
            height: 24px;
            min-width: 24px;
            margin-right: 10px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 50%;
            color: white;
            font-size: 14px;
            font-weight: bold;
        }
        
        .cloud-item-icon {
            background-color: #2478D8;
        }
        
        .mobile-item-icon {
            background-color: #3CC562;
        }
        
        .car-item-icon {
            background-color: #E9422E;
        }
        
        .module-item:last-child {
            margin-bottom: 0;
        }
        
        /* 云平台 */
        .cloud-platform {
            width: 1280px;
            height: 250px;
            top: 0;
            left: 50%;
            transform: translateX(-50%);
        }
        
        .cloud-platform .module-header {
            background-color: #2478D8;
        }
        
        .cloud-platform .module-content {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            grid-gap: 20px;
        }
        
        /* 手机端 */
        .mobile-platform {
            width: 500px;
            height: 330px;
            top: 310px;
            left: 50%;
            transform: translateX(-50%);
        }
        
        .mobile-platform .module-header {
            background-color: #3CC562;
        }
        
        .mobile-platform .module-content {
            display: grid;
            grid-template-columns: 1fr;
            grid-gap: 15px;
        }
        
        /* 车端 */
        .car-platform {
            width: 1280px;
            height: 250px;
            bottom: 0;
            left: 50%;
            transform: translateX(-50%);
        }
        
        .car-platform .module-header {
            background-color: #E9422E;
        }
        
        .car-platform .module-content {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            grid-gap: 20px;
        }
        
        /* 连接线和标签容器 */
        .connections {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            pointer-events: none;
        }
        
        /* 连接标签 */
        .connection-label {
            position: absolute;
            background-color: white;
            padding: 8px 14px;
            border-radius: 20px;
            font-size: 14px;
            box-shadow: 0 1px 3px rgba(0,0,0,0.1);
            z-index: 5;
            white-space: nowrap;
            border: 1px solid;
        }
        
        /* 标签颜色样式 */
        .label-cloud {
            color: #2478D8;
            border-color: rgba(36, 120, 216, 0.3);
        }
        
        .label-mobile {
            color: #3CC562;
            border-color: rgba(60, 197, 98, 0.3);
        }
        
        .label-car {
            color: #E9422E;
            border-color: rgba(233, 66, 46, 0.3);
        }
        
        /* 距离标签 */
        .distance-tag {
            position: absolute;
            width: 32px;
            height: 32px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            font-size: 14px;
            color: white;
            z-index: 10;
        }
        
        .tag-near {
            background-color: #3CC562;
        }
        
        .tag-far {
            background-color: #2478D8;
        }
        
        /* 底部说明 */
        .footer {
            position: absolute;
            bottom: 20px;
            width: 100%;
            text-align: center;
            font-size: 15px;
            color: #95a5a6;
            left: 0;
        }
        
        .toast {
            position: fixed;
            top: 50px;
            left: 50%;
            transform: translateX(-50%);
            background-color: rgba(0, 0, 0, 0.8);
            color: white;
            padding: 12px 25px;
            border-radius: 30px;
            font-size: 16px;
            z-index: 2000;
            display: none;
            animation: fadeInOut 2s ease;
        }
        
        @keyframes fadeInOut {
            0% { opacity: 0; }
            20% { opacity: 1; }
            80% { opacity: 1; }
            100% { opacity: 0; }
        }
    </style>
</head>
<body>
    <div class="ppt-container" id="pptContainer">
        <div class="ppt-label">PPT尺寸(1920×1080) - 透明背景</div>
        <button class="copy-button" id="copyButton">
            <span class="copy-icon"></span>复制为PPT图片
        </button>
        <button class="download-button" id="downloadButton">
            <span class="download-icon"></span>下载透明PNG
        </button>
        
        <div class="resolution-selector" id="resolutionSelector">
            <span class="resolution-label">清晰度:</span>
            <button class="resolution-btn" data-scale="1">标准</button>
            <button class="resolution-btn active" data-scale="2">高清</button>
            <button class="resolution-btn" data-scale="3">超清</button>
        </div>
        
        <div class="slide" id="slide">
            <div class="header">
                <h1>数字钥匙三端架构图</h1>
                <div class="subtitle">车辆数字钥匙系统技术架构与通信流程</div>
            </div>
            
            <div class="architecture-container" id="architectureContainer">
                <!-- 云平台 -->
                <div class="system-module cloud-platform">
                    <div class="module-header">
                        <i>🌩️</i> 钥匙云平台 (管理与服务层)
                    </div>
                    <div class="module-content">
                        <!-- 密钥管理与安全 -->
                        <div class="function-group">
                            <div class="function-group-title cloud-group-title">密钥管理与安全</div>
                            <div class="module-item">
                                <div class="module-item-icon cloud-item-icon">1</div>
                                <span>钥匙生命周期管理</span>
                            </div>
                            <div class="module-item">
                                <div class="module-item-icon cloud-item-icon">2</div>
                                <span>密钥管理系统</span>
                            </div>
                            <div class="module-item">
                                <div class="module-item-icon cloud-item-icon">3</div>
                                <span>安全认证中心</span>
                            </div>
                        </div>
                        
                        <!-- 接口和通信服务 -->
                        <div class="function-group">
                            <div class="function-group-title cloud-group-title">接口和通信服务</div>
                            <div class="module-item">
                                <div class="module-item-icon cloud-item-icon">4</div>
                                <span>统一接口服务</span>
                            </div>
                            <div class="module-item">
                                <div class="module-item-icon cloud-item-icon">5</div>
                                <span>安全通信通道</span>
                            </div>
                            <div class="module-item">
                                <div class="module-item-icon cloud-item-icon">6</div>
                                <span>时间同步服务</span>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- 手机端 -->
                <div class="system-module mobile-platform">
                    <div class="module-header">
                        <i>📱</i> 手机端 (用户交互层)
                    </div>
                    <div class="module-content">
                        <div class="function-group">
                            <div class="function-group-title mobile-group-title">核心功能</div>
                            <div class="module-item">
                                <div class="module-item-icon mobile-item-icon">1</div>
                                <span>钥匙与车辆管理</span>
                            </div>
                            <div class="module-item">
                                <div class="module-item-icon mobile-item-icon">2</div>
                                <span>蓝牙近场通信</span>
                            </div>
                        </div>
                        
                        <div class="function-group">
                            <div class="function-group-title mobile-group-title">安全保障</div>
                            <div class="module-item">
                                <div class="module-item-icon mobile-item-icon">3</div>
                                <span>安全存储模块</span>
                            </div>
                            <div class="module-item">
                                <div class="module-item-icon mobile-item-icon">4</div>
                                <span>暗号交换机制</span>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- 车端 -->
                <div class="system-module car-platform">
                    <div class="module-header">
                        <i>🚗</i> 车端 (执行与控制层)
                    </div>
                    <div class="module-content">
                        <!-- 验证与执行 -->
                        <div class="function-group">
                            <div class="function-group-title car-group-title">验证与执行</div>
                            <div class="module-item">
                                <div class="module-item-icon car-item-icon">1</div>
                                <span>钥匙验证模块</span>
                            </div>
                            <div class="module-item">
                                <div class="module-item-icon car-item-icon">2</div>
                                <span>指令执行控制</span>
                            </div>
                            <div class="module-item">
                                <div class="module-item-icon car-item-icon">3</div>
                                <span>暗号交换模块</span>
                            </div>
                        </div>
                        
                        <!-- 通信与安全 -->
                        <div class="function-group">
                            <div class="function-group-title car-group-title">通信与安全</div>
                            <div class="module-item">
                                <div class="module-item-icon car-item-icon">4</div>
                                <span>蓝牙通信模块</span>
                            </div>
                            <div class="module-item">
                                <div class="module-item-icon car-item-icon">5</div>
                                <span>远程通信模块</span>
                            </div>
                            <div class="module-item">
                                <div class="module-item-icon car-item-icon">6</div>
                                <span>安全存储模块</span>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- 连接线和标签将在这里通过SVG绘制 -->
                <div class="connections" id="connections"></div>
            </div>
            
            <div class="footer">
                数字钥匙三端架构 - 安全高效的车辆接入控制系统
            </div>
        </div>
    </div>
    
    <div class="toast" id="toast">操作成功！</div>
    
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const copyButton = document.getElementById('copyButton');
            const downloadButton = document.getElementById('downloadButton');
            const toast = document.getElementById('toast');
            const slide = document.getElementById('slide');
            const resolutionBtns = document.querySelectorAll('.resolution-btn');
            const connectionsContainer = document.getElementById('connections');
            
            // 创建SVG元素
            const svg = document.createElementNS('http://www.w3.org/2000/svg', 'svg');
            svg.setAttribute('width', '100%');
            svg.setAttribute('height', '100%');
            svg.style.position = 'absolute';
            svg.style.top = '0';
            svg.style.left = '0';
            svg.style.zIndex = '1';
            
            // 创建箭头定义
            const defs = document.createElementNS('http://www.w3.org/2000/svg', 'defs');
            
            // 云平台箭头
            const cloudMarker = document.createElementNS('http://www.w3.org/2000/svg', 'marker');
            cloudMarker.setAttribute('id', 'arrow-cloud');
            cloudMarker.setAttribute('viewBox', '0 0 10 10');
            cloudMarker.setAttribute('refX', '5');
            cloudMarker.setAttribute('refY', '5');
            cloudMarker.setAttribute('markerWidth', '6');
            cloudMarker.setAttribute('markerHeight', '6');
            cloudMarker.setAttribute('orient', 'auto-start-reverse');
            
            const cloudPolygon = document.createElementNS('http://www.w3.org/2000/svg', 'polygon');
            cloudPolygon.setAttribute('points', '0,0 10,5 0,10');
            cloudPolygon.setAttribute('fill', '#2478D8');
            cloudMarker.appendChild(cloudPolygon);
            defs.appendChild(cloudMarker);
            
            // 手机端箭头
            const mobileMarker = document.createElementNS('http://www.w3.org/2000/svg', 'marker');
            mobileMarker.setAttribute('id', 'arrow-mobile');
            mobileMarker.setAttribute('viewBox', '0 0 10 10');
            mobileMarker.setAttribute('refX', '5');
            mobileMarker.setAttribute('refY', '5');
            mobileMarker.setAttribute('markerWidth', '6');
            mobileMarker.setAttribute('markerHeight', '6');
            mobileMarker.setAttribute('orient', 'auto-start-reverse');
            
            const mobilePolygon = document.createElementNS('http://www.w3.org/2000/svg', 'polygon');
            mobilePolygon.setAttribute('points', '0,0 10,5 0,10');
            mobilePolygon.setAttribute('fill', '#3CC562');
            mobileMarker.appendChild(mobilePolygon);
            defs.appendChild(mobileMarker);
            
            // 车端箭头
            const carMarker = document.createElementNS('http://www.w3.org/2000/svg', 'marker');
            carMarker.setAttribute('id', 'arrow-car');
            carMarker.setAttribute('viewBox', '0 0 10 10');
            carMarker.setAttribute('refX', '5');
            carMarker.setAttribute('refY', '5');
            carMarker.setAttribute('markerWidth', '6');
            carMarker.setAttribute('markerHeight', '6');
            carMarker.setAttribute('orient', 'auto-start-reverse');
            
            const carPolygon = document.createElementNS('http://www.w3.org/2000/svg', 'polygon');
            carPolygon.setAttribute('points', '0,0 10,5 0,10');
            carPolygon.setAttribute('fill', '#E9422E');
            carMarker.appendChild(carPolygon);
            defs.appendChild(carMarker);
            
            svg.appendChild(defs);
            
            // 创建连接线 - 更准确的对齐到各个平台中心
            
            // 云平台到手机端 - 钥匙授权/认证
            const cloudToMobile = document.createElementNS('http://www.w3.org/2000/svg', 'path');
            cloudToMobile.setAttribute('d', 'M 720 250 L 720 310');
            cloudToMobile.setAttribute('stroke', '#2478D8');
            cloudToMobile.setAttribute('stroke-width', '3');
            cloudToMobile.setAttribute('fill', 'none');
            cloudToMobile.setAttribute('marker-end', 'url(#arrow-cloud)');
            svg.appendChild(cloudToMobile);
            
            // 手机端到云平台 - 状态同步/更新
            const mobileToCloud = document.createElementNS('http://www.w3.org/2000/svg', 'path');
            mobileToCloud.setAttribute('d', 'M 970 310 L 970 250');
            mobileToCloud.setAttribute('stroke', '#3CC562');
            mobileToCloud.setAttribute('stroke-width', '3');
            mobileToCloud.setAttribute('fill', 'none');
            mobileToCloud.setAttribute('marker-end', 'url(#arrow-mobile)');
            svg.appendChild(mobileToCloud);
            
            // 手机端到车端 - 蓝牙控制指令
            const mobileToCar = document.createElementNS('http://www.w3.org/2000/svg', 'path');
            mobileToCar.setAttribute('d', 'M 720 640 L 720 560');
            mobileToCar.setAttribute('stroke', '#3CC562');
            mobileToCar.setAttribute('stroke-width', '3');
            mobileToCar.setAttribute('fill', 'none');
            mobileToCar.setAttribute('marker-end', 'url(#arrow-mobile)');
            svg.appendChild(mobileToCar);
            
            // 车端到手机端 - 状态反馈/确认
            const carToMobile = document.createElementNS('http://www.w3.org/2000/svg', 'path');
            carToMobile.setAttribute('d', 'M 970 560 L 970 640');
            carToMobile.setAttribute('stroke', '#E9422E');
            carToMobile.setAttribute('stroke-width', '3');
            carToMobile.setAttribute('fill', 'none');
            carToMobile.setAttribute('marker-end', 'url(#arrow-car)');
            svg.appendChild(carToMobile);
            
            // 云平台到车端（虚线弧形）- 远程控制
            const cloudToCar = document.createElementNS('http://www.w3.org/2000/svg', 'path');
            cloudToCar.setAttribute('d', 'M 450 250 C 350 350, 350 550, 450 640');
            cloudToCar.setAttribute('stroke', '#2478D8');
            cloudToCar.setAttribute('stroke-width', '3');
            cloudToCar.setAttribute('stroke-dasharray', '8, 4');
            cloudToCar.setAttribute('fill', 'none');
            cloudToCar.setAttribute('marker-end', 'url(#arrow-cloud)');
            svg.appendChild(cloudToCar);
            
            // 车端到云平台（虚线弧形）- 状态上报
            const carToCloud = document.createElementNS('http://www.w3.org/2000/svg', 'path');
            carToCloud.setAttribute('d', 'M 1250 640 C 1350 550, 1350 350, 1250 250');
            carToCloud.setAttribute('stroke', '#E9422E');
            carToCloud.setAttribute('stroke-width', '3');
            carToCloud.setAttribute('stroke-dasharray', '8, 4');
            carToCloud.setAttribute('fill', 'none');
            carToCloud.setAttribute('marker-end', 'url(#arrow-car)');
            svg.appendChild(carToCloud);
            
            connectionsContainer.appendChild(svg);
            
            // 添加连接标签
            function createLabel(text, className, left, top, transform = '') {
                const label = document.createElement('div');
                label.className = `connection-label ${className}`;
                label.textContent = text;
                label.style.left = `${left}px`;
                label.style.top = `${top}px`;
                if (transform) {
                    label.style.transform = transform;
                }
                connectionsContainer.appendChild(label);
                return label;
            }
            
            // 创建距离标签
            function createDistanceTag(text, className, left, top) {
                const tag = document.createElement('div');
                tag.className = `distance-tag ${className}`;
                tag.textContent = text;
                tag.style.left = `${left}px`;
                tag.style.top = `${top}px`;
                connectionsContainer.appendChild(tag);
                return tag;
            }
            
            // 添加垂直连接标签 - 调整位置以更准确对应连接线
            createLabel('钥匙授权/认证', 'label-cloud', 650, 270);
            createLabel('状态同步/更新', 'label-mobile', 990, 270);
            
            createLabel('蓝牙控制指令', 'label-mobile', 650, 590);
            createLabel('状态反馈/确认', 'label-car', 990, 590);
            
            // 添加弧形连接标签
            createLabel('远程控制/OTA更新', 'label-cloud', 320, 425, 'rotate(-30deg)');
            createLabel('状态上报/安全校验', 'label-car', 1335, 425, 'rotate(30deg)');
            
            // 添加距离标签
            createDistanceTag('近', 'tag-near', 600, 475);
            createDistanceTag('远', 'tag-far', 420, 425);
            
            // 默认清晰度为2x (高清)
            let currentScale = 2;
            
            // 清晰度按钮点击事件
            resolutionBtns.forEach(btn => {
                btn.addEventListener('click', function() {
                    // 移除所有按钮的active类
                    resolutionBtns.forEach(b => b.classList.remove('active'));
                    // 给当前按钮添加active类
                    this.classList.add('active');
                    // 更新当前清晰度
                    currentScale = parseFloat(this.getAttribute('data-scale'));
                    
                    // 显示提示
                    showToast(`清晰度已设置为${this.textContent}模式`);
                });
            });
            
            // 显示提示消息
            function showToast(message) {
                toast.textContent = message;
                toast.style.display = 'block';
                setTimeout(function() {
                    toast.style.display = 'none';
                }, 2000);
            }
            
            // 生成透明背景图片
            function generateImage() {
                return html2canvas(slide, {
                    scale: currentScale, // 使用用户选择的清晰度
                    useCORS: true,
                    allowTaint: true,
                    backgroundColor: null // 关键：设置为null以支持透明背景
                });
            }
            
            // 复制按钮功能
            copyButton.addEventListener('click', function() {
                showToast("正在处理图片，请稍候...");
                
                generateImage().then(function(canvas) {
                    try {
                        canvas.toBlob(function(blob) {
                            try {
                                const item = new ClipboardItem({ 'image/png': blob });
                                navigator.clipboard.write([item]).then(function() {
                                    showToast("已复制到剪贴板！现在可以粘贴到PPT中");
                                }).catch(function(err) {
                                    showToast("自动复制失败，请使用下载按钮获取图片");
                                    console.error("剪贴板API错误:", err);
                                });
                            } catch (e) {
                                showToast("您的浏览器不支持复制图片，请使用下载按钮");
                                console.error("ClipboardItem错误:", e);
                            }
                        });
                    } catch (e) {
                        showToast("复制功能不可用，请使用下载图片功能");
                        console.error("toBlob错误:", e);
                    }
                }).catch(function(err) {
                    showToast("生成图片失败，请尝试下载或截图");
                    console.error("html2canvas错误:", err);
                });
            });
            
            // 下载按钮功能
            downloadButton.addEventListener('click', function () {
                showToast(`正在生成${currentScale}x清晰度透明PNG图片，请稍候...`);

                generateImage().then(function (canvas) {
                    // 将Canvas转换为PNG并下载
                    const imageUrl = canvas.toDataURL('image/png');
                    const link = document.createElement('a');

                    // 首先从URL路径获取文件名
                    let fileName = "";
                    const pathParts = window.location.pathname.split('/');
                    const htmlFileName = pathParts[pathParts.length - 1];
                    // 移除.html后缀并进行URL解码以支持中文文件名
                    fileName = decodeURIComponent(htmlFileName.replace('.html', ''));

                    // 如果从URL获取的文件名为空，尝试从标题获取
                    if (!fileName) {
                        const titleElement = document.querySelector('.header h1');
                        if (titleElement && titleElement.textContent) {
                            fileName = titleElement.textContent.trim();
                        }
                    }

                    // 确保文件名不为空
                    if (!fileName) {
                        fileName = "数字钥匙三端架构图";
                    }

                    link.download = `${fileName}-透明-${currentScale}x清晰度.png`;
                    link.href = imageUrl;
                    link.click();

                    showToast(`${currentScale}x清晰度透明背景PNG已下载`);
                }).catch(function (err) {
                    showToast("生成图片失败，请尝试截图");
                    console.error("Error:", err);
                });
            });
        });
    </script>
</body>
</html> 
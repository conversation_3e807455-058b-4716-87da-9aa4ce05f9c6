# TBOX设备TCP通信对接文档

## 📋 文档概述

本文档专门为TBOX设备开发人员提供与4G控车云平台的TCP通信对接指南。

## 🌐 TCP服务器信息

### 连接参数
- **服务器地址**: `dk-api.scsoi.com`
- **TCP端口**: `19999`
- **连接方式**: TBOX作为TCP客户端主动连接云平台

### 完整连接地址
```
dk-api.scsoi.com:19999
```

## 🔌 TCP连接建立

### 连接方式
TBOX设备作为**TCP客户端**主动连接云平台TCP服务器：

```java
// Java示例
Socket socket = new Socket("dk-api.scsoi.com", 19999);
```

```python
# Python示例
import socket
sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
sock.connect(("dk-api.scsoi.com", 19999))
```

```c
// C语言示例
struct sockaddr_in server_addr;
int sock = socket(AF_INET, SOCK_STREAM, 0);
server_addr.sin_family = AF_INET;
server_addr.sin_port = htons(19999);
inet_pton(AF_INET, "dk-api.scsoi.com", &server_addr.sin_addr);
connect(sock, (struct sockaddr*)&server_addr, sizeof(server_addr));
```

### 连接要求
- **长连接**: 保持TCP连接不断开，用于实时接收控车指令
- **心跳机制**: 建议每30秒发送一次心跳消息保持连接活跃
- **自动重连**: 连接断开后需要自动重连
- **并发支持**: 云平台支持多个TBOX设备同时连接

## 📡 4G控车通信协议

### 协议格式
所有消息采用十六进制字符串格式，遵循以下结构：

```
7E + 固定头部 + 状态码 + 固定尾部 + 7E
```

### 完整协议格式
```
7E000A000A000102030405060708090A00003033303230303031[状态码]007E
```

### 状态码定义
- **03**: 闭锁状态
- **04**: 解锁状态

### 标准消息示例

#### 解锁状态消息
```
7E000A000A000102030405060708090A000030333032303030313034007E
```

#### 闭锁状态消息
```
7E000A000A000102030405060708090A000030333032303030313033007E
```

## 🚗 TCP通信流程

### 1. 建立连接
```
TBOX设备 → TCP连接 → 云平台服务器(dk-api.scsoi.com:19999)
```

### 2. 接收云平台控车指令
云平台通过TCP连接向TBOX发送控车指令，TBOX需要监听TCP连接接收：
- **解锁指令**: `7E000A000A000102030405060708090A000030333032303030313034007E`
- **闭锁指令**: `7E000A000A000102030405060708090A000030333032303030313033007E`

### 3. 执行控车操作并上报状态
TBOX收到指令后：
1. 执行相应的车辆控制操作（解锁/闭锁）
2. 通过TCP连接向云平台上报执行结果：
   - **解锁成功**: 发送解锁状态消息
   - **闭锁成功**: 发送闭锁状态消息
   - **操作失败**: 发送错误状态消息

### 4. 心跳保活
定期通过TCP连接发送心跳消息保持连接：
```
7E000A000A000102030405060708090A000030333032303030313030007E
```

## 🔧 TBOX TCP通信实现示例

### Java实现示例
```java
public class TboxTcpClient {
    private static final String SERVER_HOST = "dk-api.scsoi.com";
    private static final int SERVER_PORT = 19999;

    private Socket socket;
    private BufferedReader reader;
    private PrintWriter writer;
    private boolean running = false;

    // 建立TCP连接
    public void connect() throws IOException {
        socket = new Socket(SERVER_HOST, SERVER_PORT);
        reader = new BufferedReader(new InputStreamReader(socket.getInputStream()));
        writer = new PrintWriter(socket.getOutputStream(), true);
        running = true;

        System.out.println("TBOX已连接到云平台: " + SERVER_HOST + ":" + SERVER_PORT);

        // 启动消息监听线程 - 接收云平台控车指令
        new Thread(this::listenForControlCommands).start();

        // 启动心跳线程 - 保持连接活跃
        new Thread(this::sendHeartbeat).start();
    }

    // 监听云平台发送的控车指令
    private void listenForControlCommands() {
        try {
            String command;
            while (running && (command = reader.readLine()) != null) {
                System.out.println("收到云平台控车指令: " + command);
                handleControlCommand(command);
            }
        } catch (IOException e) {
            System.err.println("TCP连接异常: " + e.getMessage());
            // 连接断开，尝试重连
            reconnect();
        }
    }

    // 处理控车指令并执行车辆操作
    private void handleControlCommand(String command) {
        if (command.contains("04")) {
            // 收到解锁指令，执行车辆解锁
            boolean success = executeVehicleUnlock();
            if (success) {
                reportVehicleStatus("04"); // 上报解锁成功状态
            } else {
                reportVehicleStatus("FF"); // 上报操作失败状态
            }
        } else if (command.contains("03")) {
            // 收到闭锁指令，执行车辆闭锁
            boolean success = executeVehicleLock();
            if (success) {
                reportVehicleStatus("03"); // 上报闭锁成功状态
            } else {
                reportVehicleStatus("FF"); // 上报操作失败状态
            }
        }
    }

    // 执行车辆解锁操作（需要TBOX厂商实现具体逻辑）
    private boolean executeVehicleUnlock() {
        // TODO: 在这里实现具体的车辆解锁逻辑
        System.out.println("执行车辆解锁操作...");
        // 返回操作是否成功
        return true;
    }

    // 执行车辆闭锁操作（需要TBOX厂商实现具体逻辑）
    private boolean executeVehicleLock() {
        // TODO: 在这里实现具体的车辆闭锁逻辑
        System.out.println("执行车辆闭锁操作...");
        // 返回操作是否成功
        return true;
    }

    // 向云平台上报车辆状态
    private void reportVehicleStatus(String status) {
        String statusMessage = "7E000A000A000102030405060708090A00003033303230303031" + status + "007E";
        writer.println(statusMessage);
        System.out.println("上报车辆状态: " + statusMessage);
    }

    // 发送心跳保持连接
    private void sendHeartbeat() {
        while (running) {
            try {
                Thread.sleep(30000); // 每30秒发送一次心跳
                String heartbeat = "7E000A000A000102030405060708090A000030333032303030313030007E";
                writer.println(heartbeat);
                System.out.println("发送心跳: " + heartbeat);
            } catch (InterruptedException e) {
                break;
            }
        }
    }

    // 重连机制
    private void reconnect() {
        running = false;
        try {
            if (socket != null) socket.close();
        } catch (IOException e) {
            // 忽略关闭异常
        }

        // 等待5秒后重连
        try {
            Thread.sleep(5000);
            connect();
        } catch (Exception e) {
            System.err.println("重连失败: " + e.getMessage());
            // 继续尝试重连
            new Thread(() -> {
                try {
                    Thread.sleep(10000);
                    reconnect();
                } catch (InterruptedException ie) {
                    Thread.currentThread().interrupt();
                }
            }).start();
        }
    }
}
```

### Python实现示例
```python
import socket
import threading
import time

class TboxTcpClient:
    def __init__(self):
        self.host = "dk-api.scsoi.com"
        self.port = 19999
        self.socket = None
        self.running = False

    def connect(self):
        try:
            self.socket = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            self.socket.connect((self.host, self.port))
            self.running = True

            print(f"TBOX已连接到云平台: {self.host}:{self.port}")

            # 启动消息监听线程 - 接收云平台控车指令
            threading.Thread(target=self.listen_control_commands, daemon=True).start()

            # 启动心跳线程 - 保持连接活跃
            threading.Thread(target=self.send_heartbeat, daemon=True).start()

        except Exception as e:
            print(f"TCP连接失败: {e}")
            self.reconnect()

    def listen_control_commands(self):
        try:
            while self.running:
                data = self.socket.recv(1024).decode('utf-8')
                if data:
                    print(f"收到云平台控车指令: {data}")
                    self.handle_control_command(data)
        except Exception as e:
            print(f"TCP连接异常: {e}")
            self.reconnect()

    def handle_control_command(self, command):
        if "04" in command:
            # 收到解锁指令，执行车辆解锁
            success = self.execute_vehicle_unlock()
            if success:
                self.report_vehicle_status("04")  # 上报解锁成功
            else:
                self.report_vehicle_status("FF")  # 上报操作失败
        elif "03" in command:
            # 收到闭锁指令，执行车辆闭锁
            success = self.execute_vehicle_lock()
            if success:
                self.report_vehicle_status("03")  # 上报闭锁成功
            else:
                self.report_vehicle_status("FF")  # 上报操作失败

    def execute_vehicle_unlock(self):
        # TODO: 在这里实现具体的车辆解锁逻辑
        print("执行车辆解锁操作...")
        # 返回操作是否成功
        return True

    def execute_vehicle_lock(self):
        # TODO: 在这里实现具体的车辆闭锁逻辑
        print("执行车辆闭锁操作...")
        # 返回操作是否成功
        return True

    def report_vehicle_status(self, status):
        message = f"7E000A000A000102030405060708090A00003033303230303031{status}007E"
        self.socket.send(message.encode('utf-8'))
        print(f"上报车辆状态: {message}")

    def send_heartbeat(self):
        while self.running:
            try:
                time.sleep(30)  # 每30秒发送一次心跳
                heartbeat = "7E000A000A000102030405060708090A000030333032303030313030007E"
                self.socket.send(heartbeat.encode('utf-8'))
                print(f"发送心跳: {heartbeat}")
            except Exception as e:
                print(f"心跳发送失败: {e}")
                break

    def reconnect(self):
        self.running = False
        if self.socket:
            self.socket.close()

        # 等待5秒后重连
        time.sleep(5)
        print("尝试重连...")
        self.connect()
```

## 🔍 TCP连接测试

### 使用telnet测试连接
```bash
telnet dk-api.scsoi.com 19999
```

### 连接成功后测试发送消息
连接成功后，可以手动发送测试消息验证通信：

#### 发送解锁状态上报
```
7E000A000A000102030405060708090A000030333032303030313034007E
```

#### 发送闭锁状态上报
```
7E000A000A000102030405060708090A000030333032303030313033007E
```

#### 发送心跳消息
```
7E000A000A000102030405060708090A000030333032303030313030007E
```

## 📋 TBOX开发要点总结

### 核心功能要求
1. **建立TCP连接**: 作为客户端连接到 `dk-api.scsoi.com:19999`
2. **监听控车指令**: 通过TCP连接接收云平台发送的控车指令
3. **执行车辆操作**: 根据指令执行实际的车辆解锁/闭锁操作
4. **状态上报**: 将车辆操作结果通过TCP连接上报给云平台
5. **保持连接**: 通过心跳机制保持TCP连接活跃
6. **自动重连**: 连接断开时自动重连

### 开发重点
- **只需要关心TCP通信**，无需关心WebSocket或HTTP接口
- **重点实现车辆控制逻辑**：`executeVehicleUnlock()` 和 `executeVehicleLock()` 方法
- **确保状态上报准确**：操作成功/失败都要及时上报给云平台
- **网络异常处理**：实现可靠的重连机制

## ⚠️ 重要注意事项

### 1. 网络要求
- 确保TBOX设备能够访问公网
- 支持TCP出站连接到19999端口
- 网络稳定，支持长连接

### 2. 协议要求
- **严格按照协议格式**发送消息
- 消息必须以`7E`开头和`7E`结尾
- 状态码必须正确：`03`=闭锁，`04`=解锁，`FF`=操作失败

### 3. 连接管理
- **必须实现自动重连机制**
- **定期发送心跳**保持连接（建议30秒间隔）
- **及时处理网络异常**情况

### 4. 状态上报
- **操作成功必须上报**：执行解锁/闭锁成功后立即上报状态
- **操作失败也要上报**：使用`FF`状态码上报失败
- **上报要及时**：收到指令后尽快执行并上报结果

## 📞 技术支持

### 连接信息
- **TCP服务器**: `dk-api.scsoi.com:19999`
- **协议版本**: v1.0
- **文档版本**: v2.0

### 联系方式
如有技术问题，请提供以下信息：
- TBOX设备型号和版本
- 网络连接日志
- 协议消息收发日志
- 具体错误现象描述

---

**重要提醒**: TBOX设备只需要实现TCP通信，无需关心WebSocket、HTTP等其他接口。专注于TCP连接、消息接收、车辆控制和状态上报四个核心功能即可。

<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>整体架构图</title>
    <style>
        body {
            font-family: 'Microsoft YaHei', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .architecture-container {
            max-width: 1000px;
            margin: 0 auto;
            background-color: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
        }
        h1 {
            color: #2874a6;
            text-align: center;
            margin-bottom: 30px;
        }
        .module {
            margin-bottom: 30px;
            padding: 15px;
            border-radius: 8px;
        }
        .mobile-module {
            background-color: #d4e6f1;
            border: 2px solid #3498db;
        }
        .car-module {
            background-color: #d5f5e3;
            border: 2px solid #2ecc71;
        }
        .cloud-module {
            background-color: #ebdef0;
            border: 2px solid #9b59b6;
        }
        .external-module {
            background-color: #fdebd0;
            border: 2px solid #f39c12;
        }
        .database-module {
            background-color: #f2f3f4;
            border: 2px solid #7f8c8d;
        }
        h2 {
            margin-top: 0;
            color: #2c3e50;
        }
        .component {
            margin: 10px 0;
            padding: 10px;
            background-color: rgba(255, 255, 255, 0.7);
            border-radius: 5px;
        }
        .component h3 {
            margin-top: 0;
            color: #34495e;
        }
        .component p {
            margin: 5px 0 0;
            color: #7f8c8d;
            font-size: 0.9em;
        }
        .connection {
            margin: 20px 0;
            padding: 10px;
            background-color: #eaeded;
            border-radius: 5px;
            text-align: center;
        }
    </style>
</head>
<body>
    <div class="architecture-container">
        <h1>数字钥匙整体架构图</h1>
        
        <div class="module mobile-module">
            <h2>手机端</h2>
            <div class="component">
                <h3>钥匙管理模块</h3>
                <p>管理数字钥匙的生命周期</p>
            </div>
            <div class="component">
                <h3>车辆控制模块</h3>
                <p>执行车辆控制指令</p>
            </div>
            <div class="component">
                <h3>蓝牙通信模块</h3>
                <p>与车端建立安全连接</p>
            </div>
            <div class="component">
                <h3>安全存储模块</h3>
                <p>安全存储密钥和配置</p>
            </div>
            <div class="component">
                <h3>时间同步模块</h3>
                <p>提供精确时间基准，防止重放攻击</p>
            </div>
            <div class="component">
                <h3>暗号交换模块</h3>
                <p>与车端协商生成临时暗号</p>
            </div>
            <div class="component">
                <h3>智能场景管理模块</h3>
                <p>管理无感连接触发条件，优化电量与连接策略</p>
            </div>
            <div class="component">
                <h3>异常处理模块</h3>
                <p>处理通信异常和认证失败</p>
            </div>
        </div>
        
        <div class="connection">↕️ 蓝牙通信 / 互联网通信 ↕️</div>
        
        <div class="module car-module">
            <h2>车端（BLE模块与TBOX）</h2>
            <div class="component">
                <h3>蓝牙通信模块</h3>
                <p>与手机建立安全连接</p>
            </div>
            <div class="component">
                <h3>钥匙验证模块</h3>
                <p>验证数字钥匙的有效性</p>
            </div>
            <div class="component">
                <h3>指令执行模块</h3>
                <p>执行控车指令</p>
            </div>
            <div class="component">
                <h3>安全存储模块</h3>
                <p>安全存储密钥和配置</p>
            </div>
            <div class="component">
                <h3>远程通信模块</h3>
                <p>与云平台通信</p>
            </div>
            <div class="component">
                <h3>时间同步模块</h3>
                <p>确保精确时间基准</p>
            </div>
            <div class="component">
                <h3>暗号交换模块</h3>
                <p>与手机协商生成临时暗号</p>
            </div>
            <div class="component">
                <h3>用户行为分析模块</h3>
                <p>核心无感控车算法，分析RSSI信号强度，计算距离</p>
            </div>
            <div class="component">
                <h3>异常处理模块</h3>
                <p>处理异常连接和操作</p>
            </div>
        </div>
        
        <div class="connection">↕️ 互联网通信 ↕️</div>
        
        <div class="module cloud-module">
            <h2>钥匙云平台</h2>
            <div class="component">
                <h3>钥匙生命周期管理</h3>
                <p>管理钥匙的创建、授权和撤销</p>
            </div>
            <div class="component">
                <h3>车辆关联服务</h3>
                <p>管理车辆信息和绑定关系</p>
            </div>
            <div class="component">
                <h3>安全认证中心</h3>
                <p>负责身份验证和安全事件处理</p>
            </div>
            <div class="component">
                <h3>密钥管理系统</h3>
                <p>生成和管理各种密钥</p>
            </div>
            <div class="component">
                <h3>时间服务器</h3>
                <p>提供精确时间基准</p>
            </div>
            <div class="component">
                <h3>统一接口服务</h3>
                <p>提供标准接口和安全通信</p>
            </div>
            <div class="component">
                <h3>安全通信通道</h3>
                <p>提供加密通信和防篡改措施</p>
            </div>
            <div class="component">
                <h3>异常监控与处理</h3>
                <p>监控异常登录和使用模式</p>
            </div>
        </div>
        
        <div class="connection">↔️ 安全对接 ↔️</div>
        
        <div class="module external-module">
            <h2>外部平台(TSP/OEM等)</h2>
            <div class="component">
                <h3>车辆远程服务</h3>
                <p>提供车辆远程服务和状态查询</p>
            </div>
            <div class="component">
                <h3>厂商专有安全协议</h3>
                <p>结合厂商专有安全协议</p>
            </div>
        </div>
        
        <div class="module database-module">
            <h2>数据存储</h2>
            <div class="component">
                <h3>钥匙数据</h3>
                <p>存储钥匙信息</p>
            </div>
            <div class="component">
                <h3>密钥材料</h3>
                <p>安全存储密钥材料</p>
            </div>
            <div class="component">
                <h3>通信记录</h3>
                <p>记录通信事件，支持异常检测</p>
            </div>
            <div class="component">
                <h3>操作日志</h3>
                <p>记录所有钥匙操作，支持安全审计</p>
            </div>
        </div>
        
        <div class="module database-module">
            <h2>安全基础设施</h2>
            <div class="component">
                <h3>密码机(HSM)</h3>
                <p>执行密钥操作，确保密钥安全</p>
            </div>
            <div class="component">
                <h3>证书系统(PKI/CA)</h3>
                <p>提供设备身份证书，支持证书验证和撤销</p>
            </div>
            <div class="component">
                <h3>密钥备份系统</h3>
                <p>安全备份关键信息，防止数据丢失</p>
            </div>
            <div class="component">
                <h3>安全监控</h3>
                <p>监控系统使用情况，检测异常行为</p>
            </div>
        </div>
    </div>
</body>
</html> 
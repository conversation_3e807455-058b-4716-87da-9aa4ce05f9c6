package com.ruoyi.dk.service.impl;

import com.ruoyi.dk.domain.DkUserApplication;
import com.ruoyi.dk.mapper.DkUserApplicationMapper;
import com.ruoyi.dk.service.IDkUserApplicationService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Date;

/**
 * 用户申请Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-01-15
 */
@Service
public class DkUserApplicationServiceImpl implements IDkUserApplicationService {

    @Autowired
    private DkUserApplicationMapper dkUserApplicationMapper;

    @Override
    public List<DkUserApplication> selectDkUserApplicationList(DkUserApplication dkUserApplication) {
        return dkUserApplicationMapper.selectDkUserApplicationList(dkUserApplication);
    }

    @Override
    public DkUserApplication selectDkUserApplicationByApplicationId(Long applicationId) {
        return dkUserApplicationMapper.selectDkUserApplicationByApplicationId(applicationId);
    }

    @Override
    public int insertDkUserApplication(DkUserApplication dkUserApplication) {
        // 生成申请单号
        if (dkUserApplication.getApplicationNo() == null || dkUserApplication.getApplicationNo().isEmpty()) {
            String applicationNo = generateApplicationNo();
            dkUserApplication.setApplicationNo(applicationNo);
        }

        // 设置默认状态为待审核
        if (dkUserApplication.getStatus() == null || dkUserApplication.getStatus().isEmpty()) {
            dkUserApplication.setStatus("0"); // 0-待审核
        }

        dkUserApplication.setCreateTime(new Date());
        return dkUserApplicationMapper.insertDkUserApplication(dkUserApplication);
    }

    /**
     * 生成申请单号
     * 格式：DK + yyyyMMdd + 6位随机数
     */
    private String generateApplicationNo() {
        java.text.SimpleDateFormat sdf = new java.text.SimpleDateFormat("yyyyMMdd");
        String dateStr = sdf.format(new Date());

        // 生成6位随机数
        int randomNum = (int) (Math.random() * 900000) + 100000;

        return "DK" + dateStr + randomNum;
    }

    @Override
    public int updateDkUserApplication(DkUserApplication dkUserApplication) {
        dkUserApplication.setUpdateTime(new Date());
        return dkUserApplicationMapper.updateDkUserApplication(dkUserApplication);
    }

    @Override
    public int deleteDkUserApplicationByApplicationIds(Long[] applicationIds) {
        return dkUserApplicationMapper.deleteDkUserApplicationByApplicationIds(applicationIds);
    }

    @Override
    public int auditApplication(Long applicationId, String status, String auditRemark) {
        DkUserApplication application = new DkUserApplication();
        application.setApplicationId(applicationId);
        application.setStatus(status);
        application.setAuditRemark(auditRemark);
        application.setAuditTime(new Date());
        application.setUpdateTime(new Date());
        return dkUserApplicationMapper.updateDkUserApplication(application);
    }

    @Override
    public int batchAuditApplication(Long[] applicationIds, String status, String auditRemark) {
        int result = 0;
        for (Long applicationId : applicationIds) {
            result += auditApplication(applicationId, status, auditRemark);
        }
        return result;
    }

    @Override
    public int getPendingApplicationCount() {
        return dkUserApplicationMapper.countPendingApplications();
    }

    @Override
    public Map<String, Object> getApplicationStatistics() {
        Map<String, Object> statistics = new HashMap<>();
        statistics.put("total", dkUserApplicationMapper.selectDkUserApplicationList(new DkUserApplication()).size());
        statistics.put("pending", dkUserApplicationMapper.countApplicationsByStatus("0"));
        statistics.put("approved", dkUserApplicationMapper.countApplicationsByStatus("1"));
        statistics.put("rejected", dkUserApplicationMapper.countApplicationsByStatus("2"));
        return statistics;
    }

    @Override
    public int deleteDkUserApplicationByApplicationId(Long applicationId) {
        return dkUserApplicationMapper.deleteDkUserApplicationByApplicationId(applicationId);
    }

    @Override
    public int submitApplication(Long vehicleId, String applicationType, String validStartTime, String validEndTime,
            String applicationReason) {
        DkUserApplication application = new DkUserApplication();
        // 注意：这里需要根据实际的实体类属性进行调整
        application.setApplicationReason(applicationReason);
        application.setStatus("0"); // 待审核
        application.setCreateTime(new Date());
        return dkUserApplicationMapper.insertDkUserApplication(application);
    }

    @Override
    public int withdrawApplication(Long applicationId) {
        DkUserApplication application = new DkUserApplication();
        application.setApplicationId(applicationId);
        application.setStatus("3"); // 已撤销
        application.setUpdateTime(new Date());
        return dkUserApplicationMapper.updateDkUserApplication(application);
    }

    @Override
    public List<DkUserApplication> selectDkUserApplicationByUserId(Long userId) {
        return dkUserApplicationMapper.selectDkUserApplicationByUserId(userId);
    }

    @Override
    public List<DkUserApplication> selectDkUserApplicationByVehicleId(Long vehicleId) {
        return dkUserApplicationMapper.selectDkUserApplicationByVehicleId(vehicleId);
    }
}

# 开发环境配置
server:
  port: 9201

# Spring配置
spring:
  # 数据源配置
  datasource:
    druid:
      stat-view-servlet:
        enabled: true
        loginUsername: admin
        loginPassword: 123456
    dynamic:
      druid:
        initial-size: 5
        min-idle: 5
        maxActive: 20
        maxWait: 60000
        connectTimeout: 30000
        socketTimeout: 60000
        timeBetweenEvictionRunsMillis: 60000
        minEvictableIdleTimeMillis: 300000
        validationQuery: SELECT 1 FROM DUAL
        testWhileIdle: true
        testOnBorrow: false
        testOnReturn: false
        poolPreparedStatements: true
        maxPoolPreparedStatementPerConnectionSize: 20
        filters: stat,slf4j
        connectionProperties: druid.stat.mergeSql\=true;druid.stat.slowSqlMillis\=5000
      datasource:
        # 主库数据源 - 使用H2内存数据库进行4G控车功能测试
        master:
          driver-class-name: org.h2.Driver
          url: jdbc:h2:mem:testdb;DB_CLOSE_DELAY=-1;DB_CLOSE_ON_EXIT=FALSE;MODE=MySQL
          username: sa
          password:
  
  # H2数据库配置
  h2:
    console:
      enabled: true
      path: /h2-console

  # 数据库初始化
  sql:
    init:
      mode: embedded
      schema-locations: classpath:data.sql
  
  # Redis配置（禁用）
  # redis:
  #   host: localhost
  #   port: 6379
  #   password:
  #   timeout: 10s
  #   lettuce:
  #     pool:
  #       min-idle: 0
  #       max-idle: 8
  #       max-active: 8
  #       max-wait: -1ms

# MyBatis配置
mybatis:
  # 搜索指定包别名
  typeAliasesPackage: com.ruoyi.system,com.ruoyi.dk
  # 配置mapper的扫描，找到所有的mapper.xml映射文件
  mapperLocations: classpath:mapper/**/*.xml

# TCP服务器配置
tcp:
  server:
    # TCP服务器端口
    port: 9999
    # 是否启用TCP服务器
    enabled: true

# 日志配置
logging:
  level:
    com.ruoyi: debug
    com.ruoyi.framework.tcp: debug
    org.springframework.web: debug
    org.springframework.websocket: debug

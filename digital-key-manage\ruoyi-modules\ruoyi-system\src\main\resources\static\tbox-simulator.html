<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>📡 TBOX设备模拟器</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }

        .container {
            max-width: 800px;
            margin: 0 auto;
            background: rgba(255, 255, 255, 0.95);
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            overflow: hidden;
        }

        .header {
            background: linear-gradient(135deg, #4CAF50, #45a049);
            color: white;
            padding: 30px;
            text-align: center;
        }

        .header h1 {
            font-size: 2.5em;
            margin-bottom: 10px;
            text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
        }

        .header p {
            font-size: 1.1em;
            opacity: 0.9;
        }

        .status-panel {
            padding: 20px;
            background: #f8f9fa;
            border-bottom: 1px solid #e9ecef;
        }

        .status-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 15px;
            padding: 10px;
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        }

        .status-label {
            font-weight: 600;
            color: #495057;
        }

        .status-value {
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .connection-status {
            width: 12px;
            height: 12px;
            border-radius: 50%;
            display: inline-block;
        }

        .connection-status.connected {
            background-color: #28a745;
            box-shadow: 0 0 10px rgba(40, 167, 69, 0.5);
        }

        .connection-status.disconnected {
            background-color: #dc3545;
            box-shadow: 0 0 10px rgba(220, 53, 69, 0.5);
        }

        .control-panel {
            padding: 20px;
            display: flex;
            gap: 15px;
            flex-wrap: wrap;
            justify-content: center;
        }

        .control-btn {
            padding: 15px 25px;
            border: none;
            border-radius: 10px;
            font-size: 1.1em;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            min-width: 150px;
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
        }

        .control-btn:hover:not(:disabled) {
            transform: translateY(-2px);
            box-shadow: 0 6px 12px rgba(0, 0, 0, 0.15);
        }

        .control-btn:disabled {
            opacity: 0.6;
            cursor: not-allowed;
        }

        .connect-btn {
            background: linear-gradient(135deg, #28a745, #20c997);
            color: white;
        }

        .heartbeat-btn {
            background: linear-gradient(135deg, #fd7e14, #e63946);
            color: white;
        }

        .status-btn {
            background: linear-gradient(135deg, #6f42c1, #e83e8c);
            color: white;
        }

        .disconnect-btn {
            background: linear-gradient(135deg, #6c757d, #495057);
            color: white;
        }

        .message-panel {
            padding: 20px;
            max-height: 400px;
            overflow-y: auto;
        }

        .message-item {
            margin-bottom: 10px;
            padding: 12px;
            border-radius: 8px;
            border-left: 4px solid;
        }

        .message-item.info {
            background-color: #d1ecf1;
            border-left-color: #17a2b8;
            color: #0c5460;
        }

        .message-item.success {
            background-color: #d4edda;
            border-left-color: #28a745;
            color: #155724;
        }

        .message-item.error {
            background-color: #f8d7da;
            border-left-color: #dc3545;
            color: #721c24;
        }

        .message-item.tcp_message {
            background-color: #fff3cd;
            border-left-color: #ffc107;
            color: #856404;
        }

        .message-time {
            font-size: 0.8em;
            opacity: 0.7;
            margin-bottom: 5px;
        }

        .message-content {
            font-weight: 500;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>📡 TBOX设备模拟器</h1>
            <p>模拟TBOX设备与云平台的TCP通信</p>
        </div>

        <div class="status-panel">
            <div class="status-item">
                <span class="status-label">WebSocket代理状态:</span>
                <span class="status-value">
                    <span id="wsStatus" class="connection-status disconnected"></span>
                    <span id="wsText">未连接</span>
                </span>
            </div>
            <div class="status-item">
                <span class="status-label">TCP连接状态:</span>
                <span class="status-value">
                    <span id="tcpStatus" class="connection-status disconnected"></span>
                    <span id="tcpText">未连接</span>
                </span>
            </div>
            <div class="status-item">
                <span class="status-label">TBOX设备ID:</span>
                <span class="status-value" id="tboxId">TBOX_001</span>
            </div>
        </div>

        <div class="control-panel">
            <button id="connectBtn" class="control-btn connect-btn" onclick="connectToTcpServer()">
                🔌 连接云平台
            </button>
            <button id="heartbeatBtn" class="control-btn heartbeat-btn" onclick="sendHeartbeat()" disabled>
                💓 发送心跳
            </button>
            <button id="statusBtn" class="control-btn status-btn" onclick="sendStatusReport()" disabled>
                📊 上报状态
            </button>
            <button id="disconnectBtn" class="control-btn disconnect-btn" onclick="disconnectFromTcpServer()" disabled>
                🔌 断开连接
            </button>
        </div>

        <div class="message-panel">
            <h3 style="margin-bottom: 15px; color: #333;">📨 通信日志</h3>
            <div id="messageContainer">
                <div class="message-item info">
                    <div class="message-time">系统启动</div>
                    <div class="message-content">TBOX设备模拟器已初始化，等待连接云平台...</div>
                </div>
            </div>
        </div>
    </div>

    <script>
        let websocket = null;
        let wsConnected = false;
        let tcpConnected = false;

        // 初始化WebSocket连接到TBOX代理
        function initWebSocket() {
            const protocol = window.location.protocol === 'https:' ? 'wss:' : 'ws:';
            const wsUrl = `${protocol}//${window.location.host}/websocket/tbox-proxy`;
            
            try {
                websocket = new WebSocket(wsUrl);
                
                websocket.onopen = function(event) {
                    wsConnected = true;
                    updateWebSocketStatus(true);
                    addMessage('success', 'TBOX模拟器WebSocket代理连接成功');
                };
                
                websocket.onmessage = function(event) {
                    try {
                        const message = JSON.parse(event.data);
                        handleTboxMessage(message);
                    } catch (e) {
                        addMessage('info', '收到消息: ' + event.data);
                    }
                };
                
                websocket.onclose = function(event) {
                    wsConnected = false;
                    tcpConnected = false;
                    updateWebSocketStatus(false);
                    updateTcpStatus(false);
                    addMessage('error', 'TBOX模拟器WebSocket代理连接已断开');
                    
                    // 5秒后尝试重连
                    setTimeout(initWebSocket, 5000);
                };
                
                websocket.onerror = function(error) {
                    addMessage('error', 'TBOX模拟器WebSocket代理连接错误');
                    console.error('WebSocket error:', error);
                };
                
            } catch (e) {
                addMessage('error', 'TBOX模拟器WebSocket代理初始化失败: ' + e.message);
                console.error('WebSocket init error:', e);
            }
        }

        // 处理TBOX消息
        function handleTboxMessage(message) {
            const type = message.type || 'info';
            const content = message.content || message.message || '未知消息';
            
            if (type === 'tcp_message') {
                addMessage('tcp_message', content);
            } else if (content.includes('TCP连接建立成功')) {
                tcpConnected = true;
                updateTcpStatus(true);
                addMessage('success', content);
            } else if (content.includes('TCP连接已断开')) {
                tcpConnected = false;
                updateTcpStatus(false);
                addMessage('info', content);
            } else {
                addMessage(type, content);
            }
        }

        // 连接到TCP服务器
        function connectToTcpServer() {
            if (!wsConnected) {
                addMessage('error', '请先等待WebSocket代理连接成功');
                return;
            }
            
            const command = {
                action: 'connect',
                host: 'localhost',
                port: 9999
            };
            
            websocket.send(JSON.stringify(command));
            addMessage('info', '正在连接TCP服务器...');
        }

        // 发送心跳
        function sendHeartbeat() {
            if (!tcpConnected) {
                addMessage('error', '请先建立TCP连接');
                return;
            }
            
            const command = {
                action: 'heartbeat'
            };
            
            websocket.send(JSON.stringify(command));
        }

        // 发送状态报告
        function sendStatusReport() {
            if (!tcpConnected) {
                addMessage('error', '请先建立TCP连接');
                return;
            }
            
            const command = {
                action: 'status'
            };
            
            websocket.send(JSON.stringify(command));
        }

        // 断开TCP连接
        function disconnectFromTcpServer() {
            if (!tcpConnected) {
                addMessage('error', '没有活动的TCP连接');
                return;
            }
            
            const command = {
                action: 'disconnect'
            };
            
            websocket.send(JSON.stringify(command));
        }

        // 更新WebSocket状态
        function updateWebSocketStatus(connected) {
            const statusElement = document.getElementById('wsStatus');
            const textElement = document.getElementById('wsText');
            
            if (connected) {
                statusElement.className = 'connection-status connected';
                textElement.textContent = '已连接';
            } else {
                statusElement.className = 'connection-status disconnected';
                textElement.textContent = '未连接';
            }
        }

        // 更新TCP状态
        function updateTcpStatus(connected) {
            const statusElement = document.getElementById('tcpStatus');
            const textElement = document.getElementById('tcpText');
            const connectBtn = document.getElementById('connectBtn');
            const heartbeatBtn = document.getElementById('heartbeatBtn');
            const statusBtn = document.getElementById('statusBtn');
            const disconnectBtn = document.getElementById('disconnectBtn');
            
            if (connected) {
                statusElement.className = 'connection-status connected';
                textElement.textContent = '已连接';
                connectBtn.disabled = true;
                heartbeatBtn.disabled = false;
                statusBtn.disabled = false;
                disconnectBtn.disabled = false;
            } else {
                statusElement.className = 'connection-status disconnected';
                textElement.textContent = '未连接';
                connectBtn.disabled = false;
                heartbeatBtn.disabled = true;
                statusBtn.disabled = true;
                disconnectBtn.disabled = true;
            }
        }

        // 添加消息
        function addMessage(type, content) {
            const container = document.getElementById('messageContainer');
            const messageDiv = document.createElement('div');
            messageDiv.className = `message-item ${type}`;
            
            const time = new Date().toLocaleTimeString();
            messageDiv.innerHTML = `
                <div class="message-time">${time}</div>
                <div class="message-content">${content}</div>
            `;
            
            container.appendChild(messageDiv);
            container.scrollTop = container.scrollHeight;
        }

        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', function() {
            initWebSocket();
        });
    </script>
</body>
</html>

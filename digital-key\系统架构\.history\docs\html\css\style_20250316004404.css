/* 整体样式 */
body {
    font-family: 'Microsoft YaHei', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    color: #333;
    background-color: #f8f9fa;
}

.reveal .slides {
    width: 100%;
    height: 100%;
}

.reveal .slides section {
    background: linear-gradient(135deg, #ffffff 0%, #f5f7fa 100%);
    border-radius: 10px;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.08);
    padding: 20px;
}

.reveal h1, .reveal h2, .reveal h3, .reveal h4 {
    font-family: 'Microsoft YaHei', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    color: #2c3e50;
    margin-bottom: 30px;
    text-shadow: 1px 1px 2px rgba(0,0,0,0.1);
}

.reveal h1 {
    font-size: 2.5em;
    color: #1a5276;
    border-bottom: 2px solid #3498db;
    padding-bottom: 10px;
    margin-bottom: 30px;
}

.reveal h2 {
    font-size: 1.8em;
    color: #2874a6;
    border-bottom: 1px solid #3498db;
    padding-bottom: 8px;
}

.reveal h3 {
    font-size: 1.4em;
    color: #3498db;
}

.reveal p {
    margin-bottom: 20px;
    line-height: 1.5;
}

.reveal ul, .reveal ol {
    display: inline-block;
    text-align: left;
    margin: 0 auto;
    width: 80%;
}

.reveal li {
    margin-bottom: 12px;
    line-height: 1.4;
}

/* 架构图样式 */
.architecture-diagram {
    display: flex;
    justify-content: space-around;
    margin: 30px 0;
    flex-wrap: wrap;
}

.component {
    width: 200px;
    padding: 20px;
    margin: 10px;
    border-radius: 10px;
    text-align: center;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
    transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.component:hover {
    transform: translateY(-5px);
    box-shadow: 0 6px 12px rgba(0, 0, 0, 0.15);
}

.component h3 {
    margin-top: 0;
    margin-bottom: 10px;
}

.component p {
    margin: 0;
    font-size: 0.9em;
}

.mobile {
    background-color: #d4e6f1;
    border: 2px solid #3498db;
}

.car {
    background-color: #d5f5e3;
    border: 2px solid #2ecc71;
}

.cloud {
    background-color: #ebdef0;
    border: 2px solid #9b59b6;
}

/* 组件链接样式 */
.component-links {
    display: flex;
    justify-content: space-around;
    margin: 30px 0;
}

.component-links a {
    display: inline-block;
    padding: 10px 20px;
    background: linear-gradient(135deg, #3498db 0%, #2980b9 100%);
    color: white;
    text-decoration: none;
    border-radius: 5px;
    transition: all 0.3s ease;
    box-shadow: 0 2px 5px rgba(0,0,0,0.1);
}

.component-links a:hover {
    background: linear-gradient(135deg, #2980b9 0%, #1a5276 100%);
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0,0,0,0.2);
}

/* 图片样式 */
.reveal img {
    max-width: 90%;
    max-height: 70vh;
    margin: 20px auto;
    border: 1px solid #ddd;
    border-radius: 5px;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
    transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.reveal img:hover {
    transform: scale(1.02);
    box-shadow: 0 6px 12px rgba(0, 0, 0, 0.15);
}

/* 强调文本 */
.reveal strong {
    color: #e74c3c;
    font-weight: bold;
}

/* 注释文本 */
.reveal small {
    color: #7f8c8d;
    font-size: 0.8em;
}

/* 自定义列表样式 */
.reveal ul {
    list-style-type: none;
}

.reveal ul li:before {
    content: "• ";
    color: #3498db;
    font-weight: bold;
    display: inline-block;
    width: 1em;
    margin-left: -1em;
}

.reveal ol {
    counter-reset: li;
    list-style-type: none;
}

.reveal ol li:before {
    content: counter(li) ". ";
    counter-increment: li;
    color: #3498db;
    font-weight: bold;
    display: inline-block;
    width: 1.5em;
    margin-left: -1.5em;
}

/* 封面页样式 */
.cover-slide {
    text-align: center;
    background: linear-gradient(135deg, #1a5276 0%, #3498db 100%) !important;
    color: white !important;
}

.cover-slide h1, .cover-slide h3 {
    color: white !important;
    border: none !important;
    text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
}

.cover-slide p {
    color: rgba(255,255,255,0.8);
}

/* 目录页样式 */
.toc-slide ul {
    width: 60%;
    margin: 0 auto;
}

.toc-slide li {
    margin-bottom: 15px;
}

.toc-slide a {
    color: #2874a6;
    text-decoration: none;
    transition: color 0.3s ease;
}

.toc-slide a:hover {
    color: #3498db;
    text-decoration: underline;
}

/* 系统架构图样式 */
.system-architecture {
    width: 90%;
    margin: 0 auto;
    background-color: white;
    border-radius: 10px;
    box-shadow: 0 4px 8px rgba(0,0,0,0.1);
    padding: 20px;
}

.system-module {
    margin-bottom: 20px;
    border-radius: 5px;
    padding: 15px;
}

.system-module h3 {
    margin-top: 0;
    border-bottom: 1px solid rgba(0,0,0,0.1);
    padding-bottom: 5px;
}

.module-blue {
    background-color: #d4e6f1;
    border-left: 5px solid #3498db;
}

.module-green {
    background-color: #d5f5e3;
    border-left: 5px solid #2ecc71;
}

.module-purple {
    background-color: #ebdef0;
    border-left: 5px solid #9b59b6;
}

/* 响应式调整 */
@media (max-width: 768px) {
    .architecture-diagram {
        flex-direction: column;
        align-items: center;
    }
    
    .component {
        width: 80%;
        margin: 10px 0;
    }
    
    .component-links {
        flex-direction: column;
        align-items: center;
    }
    
    .component-links a {
        margin: 5px 0;
        width: 80%;
        text-align: center;
    }
} 
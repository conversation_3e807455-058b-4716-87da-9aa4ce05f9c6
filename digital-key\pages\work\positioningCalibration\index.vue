<template>
  <view class="container bg-cls">
    <!-- 自定义导航栏 -->
    <view class="custom-navbar">
      <!-- 回退按钮 -->
      <view class="back-button iconfont icon icon-return" @tap="goBack">
        <!-- <text class="back-button-text">↩</text> -->
      </view>

      <text class="navbar-title">零感数字钥匙</text>
    </view>

    <!-- 页面内容 -->
    <view class="content">
      <view class="padding">
        <view class="flex">
          <view class="flex-sub bg-grey padding-sm margin-xs radius">位置1：主驾驶门锁处</view>
        </view>
        <view class="flex  p-xs margin-bottom-sm mb-sm">
          <view class="flex-sub bg-grey padding-sm margin-xs radius">位置2：主驾驶一侧，3米外位置</view>
        </view>
      </view>
      <view class="margin-tb-sm text-center">
        <button
          class="cu-btn round bg-olive"
          @tap="goToStartCalibrationPage"
        >
          开始校准
        </button>
      </view>
    </view>
  </view>
</template>

<script>
  export default {
    onLoad: function() {
    },
    methods: {
      /**
       * @description: 返回上一页
       * @return {*}
       */      
      goBack() {
        wx.navigateBack({
          delta: 1 // 表示回退一层，如果需要回退多层，可以修改这个值
        });
      },

      /**
       * @description: 跳转到开始校准页面
       * @return {*}
       */    
      goToStartCalibrationPage() {
        uni.navigateTo({
          url: '../startCalibration/index'
        });
      },
    }
  }
</script>

<style lang="scss">
  page {
    /* 设置背景固定，不随页面滚动 */
    background-attachment: fixed;

    /* 其他样式，比如设置高度 */
    height: 70vh;
    /* 设置高度为视口高度 */
  }

  .custom-navbar {
    display: flex;
    justify-content: center;
    align-items: center;
    height: 184rpx; /* 导航栏高度 */
    border-radius: 0rpx 0rpx 0rpx 0rpx;
    background-color: rgba(0, 0, 0, 0); /* 自定义背景色 */
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    z-index: 999;
  }

  .navbar-title {
    margin-top: 75rpx;
    color: #121010; /* 标题颜色 */
  }

  .content {
    padding-top: 184rpx;
    width: 100%;
    height: 100%;
  }
  .margin-tb-sm {
    // margin-top: 10%;
  }

  .back-button {
    position: absolute;
    top: 118rpx;
    left: 28rpx; 
  }
</style>

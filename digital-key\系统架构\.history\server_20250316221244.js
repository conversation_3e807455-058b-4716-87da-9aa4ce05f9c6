const express = require('express');
const fs = require('fs');
const path = require('path');
const app = express();
const port = 3000;

// 提供静态文件访问
app.use(express.static('./'));

// 创建API端点来获取HTML文件列表
app.get('/api/html-files', (req, res) => {
    const directoryPath = path.join(__dirname);
    
    fs.readdir(directoryPath, (err, files) => {
        if (err) {
            return res.status(500).send('无法扫描目录: ' + err);
        }
        
        // 过滤出HTML文件，排除index.html
        const htmlFiles = files
            .filter(file => file.endsWith('.html') && file !== 'index.html')
            .map(file => {
                const id = path.parse(file).name;
                const name = id.replace(/-/g, ' ');
                
                return {
                    id: id.toLowerCase().replace(/[^a-z0-9]/g, ''),
                    name: name,
                    file: file
                };
            });
            
        res.json(htmlFiles);
    });
});

app.listen(port, () => {
    console.log(`服务器运行在 http://localhost:${port}`);
}); 
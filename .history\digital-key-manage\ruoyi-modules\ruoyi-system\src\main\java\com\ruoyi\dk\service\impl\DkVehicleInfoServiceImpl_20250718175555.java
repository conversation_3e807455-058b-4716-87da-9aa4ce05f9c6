package com.ruoyi.dk.service.impl;

import com.ruoyi.dk.domain.DkVehicleInfo;
import com.ruoyi.dk.mapper.DkVehicleInfoMapper;
import com.ruoyi.dk.service.IDkVehicleInfoService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Date;

/**
 * 车辆信息Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-01-15
 */
@Service
public class DkVehicleInfoServiceImpl implements IDkVehicleInfoService {

    @Autowired
    private DkVehicleInfoMapper dkVehicleInfoMapper;

    @Override
    public List<DkVehicleInfo> selectDkVehicleInfoList(DkVehicleInfo dkVehicleInfo) {
        return dkVehicleInfoMapper.selectDkVehicleInfoList(dkVehicleInfo);
    }

    @Override
    public DkVehicleInfo selectDkVehicleInfoByVehicleId(Long vehicleId) {
        return dkVehicleInfoMapper.selectDkVehicleInfoByVehicleId(vehicleId);
    }

    @Override
    public int insertDkVehicleInfo(DkVehicleInfo dkVehicleInfo) {
        dkVehicleInfo.setCreateTime(new Date());
        return dkVehicleInfoMapper.insertDkVehicleInfo(dkVehicleInfo);
    }

    @Override
    public int updateDkVehicleInfo(DkVehicleInfo dkVehicleInfo) {
        dkVehicleInfo.setUpdateTime(new Date());
        return dkVehicleInfoMapper.updateDkVehicleInfo(dkVehicleInfo);
    }

    @Override
    public int deleteDkVehicleInfoByVehicleIds(Long[] vehicleIds) {
        return dkVehicleInfoMapper.deleteDkVehicleInfoByVehicleIds(vehicleIds);
    }

    @Override
    public List<DkVehicleInfo> selectAvailableVehicles() {
        return dkVehicleInfoMapper.selectDkVehicleInfoByStatus("0");
    }

    @Override
    public int updateVehicleStatus(Long vehicleId, String status) {
        return dkVehicleInfoMapper.updateVehicleStatus(vehicleId, status);
    }

    @Override
    public int batchUpdateVehicleStatus(Long[] vehicleIds, String status) {
        return dkVehicleInfoMapper.batchUpdateVehicleStatus(vehicleIds, status);
    }

    @Override
    public DkVehicleInfo selectDkVehicleInfoByVinCode(String vinCode) {
        return dkVehicleInfoMapper.selectDkVehicleInfoByVinCode(vinCode);
    }

    @Override
    public Map<String, Object> getVehicleStatistics() {
        Map<String, Object> statistics = new HashMap<>();
        statistics.put("total", dkVehicleInfoMapper.countVehicles());
        statistics.put("available", dkVehicleInfoMapper.countVehiclesByStatus("0"));
        statistics.put("inUse", dkVehicleInfoMapper.countVehiclesByStatus("1"));
        statistics.put("maintenance", dkVehicleInfoMapper.countVehiclesByStatus("2"));
        statistics.put("disabled", dkVehicleInfoMapper.countVehiclesByStatus("3"));
        return statistics;
    }

    @Override
    public int deleteDkVehicleInfoByVehicleId(Long vehicleId) {
        return dkVehicleInfoMapper.deleteDkVehicleInfoByVehicleId(vehicleId);
    }

    @Override
    public List<DkVehicleInfo> getAvailableVehicles() {
        return dkVehicleInfoMapper.selectDkVehicleInfoByStatus("0");
    }

    @Override
    public List<DkVehicleInfo> getInUseVehicles() {
        return dkVehicleInfoMapper.selectDkVehicleInfoByStatus("1");
    }

    @Override
    public List<DkVehicleInfo> getMaintenanceVehicles() {
        return dkVehicleInfoMapper.selectDkVehicleInfoByStatus("2");
    }

    @Override
    public List<DkVehicleInfo> selectDkVehicleInfoByBrand(String brand) {
        return dkVehicleInfoMapper.selectDkVehicleInfoByBrand(brand);
    }

    @Override
    public List<DkVehicleInfo> selectDkVehicleInfoByModel(String model) {
        return dkVehicleInfoMapper.selectDkVehicleInfoByModel(model);
    }

    @Override
    public String importVehicle(List<DkVehicleInfo> vehicleList, Boolean isUpdateSupport, String operName) {
        if (vehicleList == null || vehicleList.isEmpty()) {
            return "导入数据为空";
        }

        int successCount = 0;
        int failureCount = 0;

        for (DkVehicleInfo vehicle : vehicleList) {
            try {
                vehicle.setCreateTime(new Date());
                vehicle.setCreateBy(operName);

                if (isUpdateSupport && vehicle.getVehicleId() != null) {
                    vehicle.setUpdateTime(new Date());
                    vehicle.setUpdateBy(operName);
                    dkVehicleInfoMapper.updateDkVehicleInfo(vehicle);
                } else {
                    dkVehicleInfoMapper.insertDkVehicleInfo(vehicle);
                }
                successCount++;
            } catch (Exception e) {
                failureCount++;
            }
        }

        return String.format("导入完成，成功%d条，失败%d条", successCount, failureCount);
    }
}

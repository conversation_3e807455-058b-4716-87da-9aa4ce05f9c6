package com.ruoyi.framework.tcp;

/**
 * 快速验证测试
 * 用于验证代码基本语法和类加载
 * 
 * <AUTHOR>
 */
public class QuickValidationTest {
    
    public static void main(String[] args) {
        System.out.println("🧪 开始快速验证测试");
        
        try {
            // 测试TcpControlService
            TcpControlService service = new TcpControlService();
            
            // 测试指令生成
            String unlockCmd = service.generateUnlockCommand();
            String lockCmd = service.generateLockCommand();
            
            System.out.println("✅ 指令生成成功:");
            System.out.println("   解锁: " + unlockCmd);
            System.out.println("   闭锁: " + lockCmd);
            
            // 测试协议解析
            String testMessage = "7E000A000A000102030405060708090A000030333032303030313034007E";
            TcpControlService.TboxStatusResult result = service.parseTboxMessage(testMessage);
            
            System.out.println("✅ 协议解析成功:");
            System.out.println("   有效: " + result.isValid());
            System.out.println("   状态: " + result.getLockStatus());
            System.out.println("   描述: " + result.getStatusDescription());
            
            // 测试协议验证
            boolean isValid = service.isValidProtocolFormat(testMessage);
            System.out.println("✅ 协议验证成功: " + isValid);
            
            // 测试连接数量获取
            int count = service.getConnectedTboxCount();
            System.out.println("✅ 连接数量获取成功: " + count);
            
            System.out.println("🎉 所有基本功能验证通过！");
            
        } catch (Exception e) {
            System.err.println("❌ 验证失败: " + e.getMessage());
            e.printStackTrace();
        }
    }
}

# 数字钥匙运营系统菜单结构设计

## 当前问题分析

### 现有菜单问题
1. **菜单命名不直观**："蓝牙密钥管理" → 应为"数字钥匙管理"
2. **缺少核心模块**：车辆管理、用户申请管理
3. **流程不完整**：缺少从申请到审核到分配的完整流程
4. **操作不友好**：需要手动输入VIN码，没有车辆选择功能

## 新菜单结构设计

### 一级菜单：数字钥匙运营系统

#### 1. 运营工作台 📊
- **待处理申请** - 显示待审核的用户申请
- **今日统计** - 今日申请数、审核数、钥匙分配数
- **车辆状态概览** - 在用车辆、空闲车辆统计
- **异常告警** - 权限限制用户、异常车辆等

#### 2. 用户申请管理 👥
- **申请列表** - 所有用户申请记录
  - 待审核申请
  - 已通过申请  
  - 已拒绝申请
- **申请审核** - 审核用户提交的资料
- **申请统计** - 申请数据统计分析

#### 3. 车辆管理 🚗
- **车辆信息** - 车辆基础信息管理
  - 车辆录入
  - VIN码管理
  - 车辆状态（空闲/在用/维护）
  - 车辆位置信息
- **车辆分配** - 车辆分配给用户
- **车辆监控** - 车辆实时状态监控

#### 4. 数字钥匙管理 🔑
- **钥匙分配** - 为通过审核的用户分配钥匙
- **钥匙状态** - 查看所有钥匙状态
- **权限管理** - 用户权限限制/恢复
- **钥匙回收** - 回收用户钥匙

#### 5. 系统管理 ⚙️
- **运营人员管理** - 运营人员账号管理
- **权限配置** - 运营权限配置
- **操作日志** - 所有运营操作记录
- **系统设置** - 系统参数配置

## 运营流程设计

### 完整业务流程
```
用户注册申请 → 提交资料 → 运营审核 → 分配车辆 → 生成钥匙 → 钥匙下发 → 使用监控 → 权限管理
```

### 详细流程说明

#### 1. 用户申请阶段
- 用户在APP端提交租车申请
- 上传身份证、驾驶证等资料
- 系统生成申请单号

#### 2. 运营审核阶段
- 运营人员在"申请审核"页面查看申请
- 审核用户资料的真实性和完整性
- 审核结果：通过/拒绝/补充资料

#### 3. 车辆分配阶段
- 从"车辆管理"中选择空闲车辆
- 将车辆分配给通过审核的用户
- 系统自动关联用户和车辆

#### 4. 钥匙生成阶段
- 系统根据车辆VIN码自动生成数字钥匙
- 运营人员确认钥匙参数
- 钥匙状态设为"已分配"

#### 5. 钥匙下发阶段
- 系统将钥匙推送到用户APP
- 用户收到钥匙使用通知
- 钥匙状态更新为"使用中"

## 页面功能设计

### 1. 运营工作台
```
┌─────────────────────────────────────────┐
│ 运营工作台                               │
├─────────────────────────────────────────┤
│ 📊 今日数据                             │
│ 新申请: 12  待审核: 8  已分配: 15        │
│                                         │
│ 🚨 待处理事项                           │
│ • 3个申请待审核                         │
│ • 2个用户权限异常                       │
│ • 1辆车辆需要维护                       │
│                                         │
│ 📈 车辆状态                             │
│ 总车辆: 100  使用中: 65  空闲: 30  维护: 5│
└─────────────────────────────────────────┘
```

### 2. 申请审核页面
```
┌─────────────────────────────────────────┐
│ 用户申请审核                             │
├─────────────────────────────────────────┤
│ 申请人: 张三    手机: 138****1234        │
│ 申请时间: 2024-01-15 10:30              │
│                                         │
│ 📄 资料审核                             │
│ ✓ 身份证: 已上传                        │
│ ✓ 驾驶证: 已上传                        │
│ ✓ 信用记录: 良好                        │
│                                         │
│ [通过审核] [拒绝申请] [要求补充资料]      │
└─────────────────────────────────────────┘
```

### 3. 车辆分配页面
```
┌─────────────────────────────────────────┐
│ 车辆分配 - 张三                          │
├─────────────────────────────────────────┤
│ 可用车辆列表:                           │
│                                         │
│ 🚗 比亚迪秦 VIN: LL3AGCJ55NA032336      │
│    位置: 停车场A区  状态: 空闲           │
│    [选择此车辆]                         │
│                                         │
│ 🚗 特斯拉Model3 VIN: 5YJ3E1EA8KF123456  │
│    位置: 停车场B区  状态: 空闲           │
│    [选择此车辆]                         │
└─────────────────────────────────────────┘
```

## 数据库设计扩展

### 新增表结构

#### 1. 车辆信息表 (dk_vehicle_info)
```sql
CREATE TABLE dk_vehicle_info (
    vehicle_id BIGINT PRIMARY KEY AUTO_INCREMENT,
    vin_code VARCHAR(17) UNIQUE NOT NULL COMMENT 'VIN码',
    brand VARCHAR(50) COMMENT '品牌',
    model VARCHAR(50) COMMENT '型号',
    color VARCHAR(20) COMMENT '颜色',
    license_plate VARCHAR(20) COMMENT '车牌号',
    status CHAR(1) DEFAULT '0' COMMENT '状态(0空闲 1使用中 2维护)',
    location VARCHAR(100) COMMENT '停放位置',
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP,
    update_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);
```

#### 2. 用户申请表 (dk_user_application)
```sql
CREATE TABLE dk_user_application (
    application_id BIGINT PRIMARY KEY AUTO_INCREMENT,
    user_id BIGINT COMMENT '用户ID',
    application_no VARCHAR(32) UNIQUE COMMENT '申请单号',
    real_name VARCHAR(50) COMMENT '真实姓名',
    phone VARCHAR(11) COMMENT '手机号',
    id_card VARCHAR(18) COMMENT '身份证号',
    driving_license VARCHAR(50) COMMENT '驾驶证号',
    status CHAR(1) DEFAULT '0' COMMENT '状态(0待审核 1通过 2拒绝)',
    audit_user_id BIGINT COMMENT '审核人ID',
    audit_time DATETIME COMMENT '审核时间',
    audit_remark VARCHAR(500) COMMENT '审核备注',
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP
);
```

#### 3. 车辆分配记录表 (dk_vehicle_assignment)
```sql
CREATE TABLE dk_vehicle_assignment (
    assignment_id BIGINT PRIMARY KEY AUTO_INCREMENT,
    user_id BIGINT COMMENT '用户ID',
    vehicle_id BIGINT COMMENT '车辆ID',
    assignment_time DATETIME COMMENT '分配时间',
    return_time DATETIME COMMENT '归还时间',
    status CHAR(1) DEFAULT '1' COMMENT '状态(1使用中 2已归还)',
    operator_id BIGINT COMMENT '操作人ID',
    remark VARCHAR(500) COMMENT '备注'
);
```

## 实施建议

### 第一阶段：基础模块
1. 创建车辆管理模块
2. 创建用户申请管理模块
3. 优化现有钥匙管理模块

### 第二阶段：流程整合
1. 打通申请→审核→分配→钥匙生成流程
2. 实现运营工作台
3. 完善权限管理

### 第三阶段：功能增强
1. 添加统计报表
2. 实现消息通知
3. 移动端适配

这样的设计更符合实际运营需求，让运营人员的工作更加高效和直观。

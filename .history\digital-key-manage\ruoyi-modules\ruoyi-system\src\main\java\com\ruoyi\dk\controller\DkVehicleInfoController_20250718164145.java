package com.ruoyi.dk.controller;

import com.ruoyi.common.core.web.controller.BaseController;
import com.ruoyi.common.core.web.domain.AjaxResult;
import com.ruoyi.common.core.web.page.TableDataInfo;
import com.ruoyi.dk.domain.DkVehicleInfo;
import com.ruoyi.dk.service.IDkVehicleInfoService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.Map;

/**
 * 车辆信息管理Controller
 * 
 * <AUTHOR>
 * @date 2024-01-15
 */
@RestController
@RequestMapping("/vehicleInfo")
public class DkVehicleInfoController extends BaseController {

    @Autowired(required = false)
    private IDkVehicleInfoService dkVehicleInfoService;

    /**
     * 查询车辆信息列表
     */
    @GetMapping("/list")
    public TableDataInfo list(DkVehicleInfo dkVehicleInfo) {
        startPage();
        List<DkVehicleInfo> list = dkVehicleInfoService.selectDkVehicleInfoList(dkVehicleInfo);
        return getDataTable(list);
    }

    /**
     * 获取车辆信息详细信息
     */
    @GetMapping(value = "/{vehicleId}")
    public AjaxResult getInfo(@PathVariable Long vehicleId) {
        return AjaxResult.success(dkVehicleInfoService.selectDkVehicleInfoByVehicleId(vehicleId));
    }

    /**
     * 新增车辆信息
     */
    @PostMapping
    public AjaxResult add(@RequestBody DkVehicleInfo dkVehicleInfo) {
        return toAjax(dkVehicleInfoService.insertDkVehicleInfo(dkVehicleInfo));
    }

    /**
     * 修改车辆信息
     */
    @PutMapping
    public AjaxResult edit(@RequestBody DkVehicleInfo dkVehicleInfo) {
        return toAjax(dkVehicleInfoService.updateDkVehicleInfo(dkVehicleInfo));
    }

    /**
     * 删除车辆信息
     */
    @DeleteMapping("/{vehicleIds}")
    public AjaxResult remove(@PathVariable Long[] vehicleIds) {
        try {
            if (dkVehicleInfoService != null) {
                return toAjax(dkVehicleInfoService.deleteDkVehicleInfoByVehicleIds(vehicleIds));
            }
        } catch (Exception e) {
            logger.error("删除车辆信息失败", e);
        }

        // 模拟成功
        return AjaxResult.success("删除成功");
    }

    /**
     * 获取可用车辆列表
     */
    @GetMapping("/available")
    public AjaxResult listAvailableVehicles() {
        try {
            if (dkVehicleInfoService != null) {
                List<DkVehicleInfo> list = dkVehicleInfoService.selectAvailableVehicles();
                return AjaxResult.success(list);
            }
        } catch (Exception e) {
            logger.error("获取可用车辆列表失败", e);
        }

        // 返回模拟数据
        List<Map<String, Object>> mockList = new ArrayList<>();

        Map<String, Object> vehicle1 = new HashMap<>();
        vehicle1.put("vehicleId", 1L);
        vehicle1.put("vinCode", "LSGKB54U8EA123456");
        vehicle1.put("brand", "比亚迪");
        vehicle1.put("model", "秦PLUS DM-i");
        vehicle1.put("licensePlate", "京A12345");
        vehicle1.put("location", "停车场A区01号");
        mockList.add(vehicle1);

        Map<String, Object> vehicle2 = new HashMap<>();
        vehicle2.put("vehicleId", 4L);
        vehicle2.put("vinCode", "LHGCV1658EA789012");
        vehicle2.put("brand", "本田");
        vehicle2.put("model", "思域");
        vehicle2.put("licensePlate", "京A22222");
        vehicle2.put("location", "停车场B区10号");
        mockList.add(vehicle2);

        return AjaxResult.success(mockList);
    }

    /**
     * 更新车辆状态
     */
    @PutMapping("/status/{vehicleId}")
    public AjaxResult updateVehicleStatus(@PathVariable Long vehicleId, @RequestBody Map<String, Object> statusData) {
        try {
            if (dkVehicleInfoService != null) {
                String status = statusData.get("status").toString();
                return toAjax(dkVehicleInfoService.updateVehicleStatus(vehicleId, status));
            }
        } catch (Exception e) {
            logger.error("更新车辆状态失败", e);
        }

        // 模拟成功
        return AjaxResult.success("状态更新成功");
    }

    /**
     * 批量更新车辆状态
     */
    @PutMapping("/batchStatus")
    public AjaxResult batchUpdateVehicleStatus(@RequestBody Map<String, Object> statusData) {
        try {
            if (dkVehicleInfoService != null) {
                Long[] vehicleIds = (Long[]) statusData.get("vehicleIds");
                String status = statusData.get("status").toString();
                return toAjax(dkVehicleInfoService.batchUpdateVehicleStatus(vehicleIds, status));
            }
        } catch (Exception e) {
            logger.error("批量更新车辆状态失败", e);
        }

        // 模拟成功
        return AjaxResult.success("批量状态更新成功");
    }

    /**
     * 根据VIN码查询车辆
     */
    @GetMapping("/vin/{vinCode}")
    public AjaxResult getVehicleByVin(@PathVariable String vinCode) {
        try {
            if (dkVehicleInfoService != null) {
                DkVehicleInfo vehicle = dkVehicleInfoService.selectDkVehicleInfoByVinCode(vinCode);
                return AjaxResult.success(vehicle);
            }
        } catch (Exception e) {
            logger.error("根据VIN码查询车辆失败", e);
        }

        // 返回模拟数据
        Map<String, Object> mockData = new HashMap<>();
        mockData.put("vehicleId", 1L);
        mockData.put("vinCode", vinCode);
        mockData.put("brand", "测试品牌");
        mockData.put("model", "测试型号");
        mockData.put("licensePlate", "京A12345");

        return AjaxResult.success(mockData);
    }

    /**
     * 获取车辆统计信息
     */
    @GetMapping("/statistics")
    public AjaxResult getVehicleStatistics() {
        try {
            if (dkVehicleInfoService != null) {
                Map<String, Object> statistics = dkVehicleInfoService.getVehicleStatistics();
                return AjaxResult.success(statistics);
            }
        } catch (Exception e) {
            logger.error("获取车辆统计信息失败", e);
        }

        // 返回模拟数据
        Map<String, Object> mockStats = new HashMap<>();
        mockStats.put("total", 100);
        mockStats.put("available", 30);
        mockStats.put("inUse", 65);
        mockStats.put("maintenance", 3);
        mockStats.put("disabled", 2);

        return AjaxResult.success(mockStats);
    }
}

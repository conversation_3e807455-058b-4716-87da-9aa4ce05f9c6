<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>钥匙云平台</title>
    <!-- 引入html2canvas库 -->
    <script src="https://html2canvas.hertzen.com/dist/html2canvas.min.js"></script>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
            font-family: 'PingFang SC', 'Microsoft YaHei', sans-serif;
        }
        
        body {
            background: #f0f0f0;
            display: flex;
            justify-content: center;
            align-items: center;
            min-height: 100vh;
            color: #333;
            padding: 20px;
        }
        
        /* PPT边框指示器 */
        .ppt-container {
            position: relative;
            width: 1920px; /* 标准PPT宽度 */
            height: 1080px; /* 标准PPT高度 */
            box-shadow: 0 0 30px rgba(0, 0, 0, 0.2);
            border: 2px dashed #ccc;
            background-image: linear-gradient(45deg, #ccc 25%, transparent 25%), 
                              linear-gradient(-45deg, #ccc 25%, transparent 25%), 
                              linear-gradient(45deg, transparent 75%, #ccc 75%), 
                              linear-gradient(-45deg, transparent 75%, #ccc 75%);
            background-size: 20px 20px;
            background-position: 0 0, 0 10px, 10px -10px, -10px 0px;
        }
        
        .ppt-label {
            position: absolute;
            top: -40px;
            left: 0;
            background-color: #555;
            color: white;
            padding: 5px 15px;
            border-radius: 4px;
            font-size: 16px;
        }
        
        /* 复制按钮 */
        .copy-button {
            position: absolute;
            top: 20px;
            right: 20px;
            background: linear-gradient(135deg, #3498db, #2980b9);
            color: white;
            border: none;
            padding: 12px 25px;
            border-radius: 30px;
            font-size: 18px;
            cursor: pointer;
            z-index: 1000;
            box-shadow: 0 4px 10px rgba(0, 0, 0, 0.2);
            display: flex;
            align-items: center;
            gap: 10px;
            transition: all 0.3s ease;
        }
        
        /* 下载按钮 */
        .download-button {
            position: absolute;
            top: 20px;
            right: 230px;
            background: linear-gradient(135deg, #2ecc71, #27ae60);
            color: white;
            border: none;
            padding: 12px 25px;
            border-radius: 30px;
            font-size: 18px;
            cursor: pointer;
            z-index: 1000;
            box-shadow: 0 4px 10px rgba(0, 0, 0, 0.2);
            display: flex;
            align-items: center;
            gap: 10px;
            transition: all 0.3s ease;
        }
        
        .copy-button:hover, .download-button:hover {
            transform: translateY(-3px);
            box-shadow: 0 6px 15px rgba(0, 0, 0, 0.25);
        }
        
        .copy-button:active, .download-button:active {
            transform: translateY(0);
        }
        
        .copy-icon {
            width: 20px;
            height: 20px;
            background-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="white"><path d="M19,21H8V7H19M19,5H8A2,2 0 0,0 6,7V21A2,2 0 0,0 8,23H19A2,2 0 0,0 21,21V7A2,2 0 0,0 19,5M16,1H4A2,2 0 0,0 2,3V17H4V3H16V1Z" /></svg>');
            background-size: contain;
            background-repeat: no-repeat;
        }
        
        .download-icon {
            width: 20px;
            height: 20px;
            background-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="white"><path d="M5,20H19V18H5M19,9H15V3H9V9H5L12,16L19,9Z" /></svg>');
            background-size: contain;
            background-repeat: no-repeat;
        }
        
        /* 清晰度选择器样式 */
        .resolution-selector {
            position: absolute;
            top: 70px;
            right: 230px;
            display: flex;
            align-items: center;
            background: rgba(255, 255, 255, 0.9);
            padding: 8px 15px;
            border-radius: 30px;
            box-shadow: 0 4px 10px rgba(0, 0, 0, 0.1);
            z-index: 1000;
        }
        
        .resolution-label {
            margin-right: 10px;
            font-size: 14px;
            color: #555;
        }
        
        .resolution-btn {
            border: none;
            background: #f0f0f0;
            padding: 5px 10px;
            margin: 0 3px;
            border-radius: 15px;
            font-size: 14px;
            cursor: pointer;
            transition: all 0.2s ease;
            color: #555;
        }
        
        .resolution-btn:hover {
            background: #e0e0e0;
        }
        
        .resolution-btn.active {
            background: linear-gradient(135deg, #3498db, #2980b9);
            color: white;
        }
        
        /* 新设计开始 - 完全透明背景 */
        .slide {
            width: 100%;
            height: 100%;
            background-color: transparent; /* 完全透明背景 */
            padding: 60px;
            display: flex;
            flex-direction: column;
            overflow: hidden;
            position: relative;
        }
        
        /* 顶部标题区域 - 修改了标题样式，删除背景渐变 */
        .header {
            text-align: center;
            margin-bottom: 30px;
            position: relative;
            z-index: 10;
        }
        
        .header h1 {
            font-size: 54px;
            color: #2c3e50;
            margin-bottom: 10px;
            position: relative;
            display: inline-block;
            font-weight: 700;
            background: none;
        }
        
        .header .subtitle {
            font-size: 24px;
            color: #7f8c8d;
            font-weight: 400;
            max-width: 800px;
            margin: 0 auto;
        }
        
        /* 功能截图网格布局 - 修改为1行3列 */
        .screenshots-grid {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            grid-template-rows: 1fr;
            gap: 30px;
            padding: 0 30px;
            position: relative;
            z-index: 10;
            height: 780px; /* 控制截图区域高度 */
        }
        
        /* 截图容器样式 */
        .screenshot-container {
            background: white;
            border-radius: 12px;
            padding: 5px;
            display: flex;
            flex-direction: column;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.08);
            overflow: hidden;
            height: 100%;
        }
        
        /* 截图标题栏 */
        .screenshot-title-bar {
            display: flex;
            align-items: center;
            padding: 8px 12px;
            background: linear-gradient(to right, #f8f9fa, #e9ecef);
            border-top-left-radius: 8px;
            border-top-right-radius: 8px;
            border-bottom: 1px solid #dee2e6;
        }
        
        .window-controls {
            display: flex;
            gap: 6px;
            margin-right: 10px;
        }
        
        .window-circle {
            width: 12px;
            height: 12px;
            border-radius: 50%;
        }
        
        .window-close { background-color: #ff5f56; }
        .window-minimize { background-color: #ffbd2e; }
        .window-maximize { background-color: #27c93f; }
        
        .screenshot-title {
            font-size: 14px;
            font-weight: 600;
            color: #495057;
            text-align: center;
            flex-grow: 1;
        }
        
        /* 截图内容区域 */
        .screenshot-content {
            flex-grow: 1;
            overflow: hidden;
            position: relative;
            background: #f8f9fa;
            border-bottom-left-radius: 8px;
            border-bottom-right-radius: 8px;
        }
        
        .screenshot-image {
            width: 100%;
            height: 100%;
            background-size: cover;
            background-position: center;
            background-repeat: no-repeat;
        }
        
        /* 截图下方的功能说明 */
        .screenshot-caption {
            position: absolute;
            bottom: 0;
            left: 0;
            right: 0;
            background: rgba(0, 0, 0, 0.7);
            color: white;
            padding: 8px 12px;
            font-size: 14px;
            font-weight: 500;
        }
        
        /* 各个功能的截图背景颜色和图片设置 */
        #key-lifecycle .screenshot-image {
            background-color: #e3f2fd; /* 浅蓝色背景 */
            background-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" width="100" height="100" fill="%233498db" opacity="0.2"><path d="M7,14A2,2 0 0,1 5,12A2,2 0 0,1 7,10A2,2 0 0,1 9,12A2,2 0 0,1 7,14M12.65,10C11.83,7.67 9.61,6 7,6A6,6 0 0,0 1,12A6,6 0 0,0 7,18C9.61,18 11.83,16.33 12.65,14H17V18H21V14H23V10H12.65Z" /></svg>');
        }
        
        #vehicle-service .screenshot-image {
            background-color: #e8f5e9; /* 浅绿色背景 */
            background-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" width="100" height="100" fill="%232ecc71" opacity="0.2"><path d="M18,18.5A1.5,1.5 0 0,1 16.5,17A1.5,1.5 0 0,1 18,15.5A1.5,1.5 0 0,1 19.5,17A1.5,1.5 0 0,1 18,18.5M19.5,9.5L21.46,12H17V9.5M6,18.5A1.5,1.5 0 0,1 4.5,17A1.5,1.5 0 0,1 6,15.5A1.5,1.5 0 0,1 7.5,17A1.5,1.5 0 0,1 6,18.5M20,8L23,12V17H21A3,3 0 0,1 18,20A3,3 0 0,1 15,17H9A3,3 0 0,1 6,20A3,3 0 0,1 3,17H1V6C1,4.89 1.89,4 3,4H17V8H20Z" /></svg>');
        }
        
        #auth-center .screenshot-image {
            background-color: #f3e5f5; /* 浅紫色背景 */
            background-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" width="100" height="100" fill="%239b59b6" opacity="0.2"><path d="M12,1L3,5V11C3,16.55 6.84,21.74 12,23C17.16,21.74 21,16.55 21,11V5L12,1M12,5A3,3 0 0,1 15,8A3,3 0 0,1 12,11A3,3 0 0,1 9,8A3,3 0 0,1 12,5M17.13,17C15.92,18.85 14.11,20.24 12,20.92C9.89,20.24 8.08,18.85 6.87,17C6.53,16.5 6.24,16 6,15.47C6,13.82 8.71,12.47 12,12.47C15.29,12.47 18,13.79 18,15.47C17.76,16 17.47,16.5 17.13,17Z" /></svg>');
        }
        
        #key-management .screenshot-image {
            background-color: #fff3e0; /* 浅橙色背景 */
            background-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" width="100" height="100" fill="%23e67e22" opacity="0.2"><path d="M22,18V22H18V19H15V16H12L9.74,13.74C9.19,13.91 8.61,14 8,14A6,6 0 0,1 2,8A6,6 0 0,1 8,2A6,6 0 0,1 14,8C14,8.61 13.91,9.19 13.74,9.74L22,18M7,5A2,2 0 0,0 5,7A2,2 0 0,0 7,9A2,2 0 0,0 9,7A2,2 0 0,0 7,5Z" /></svg>');
        }
        
        #api-service .screenshot-image {
            background-color: #eceff1; /* 浅灰色背景 */
            background-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" width="100" height="100" fill="%2334495e" opacity="0.2"><path d="M20,19V7H4V19H20M20,3A2,2 0 0,1 22,5V19A2,2 0 0,1 20,21H4A2,2 0 0,1 2,19V5A2,2 0 0,1 4,3H20M13,17V15H18V17H13M9.58,13L5.57,9H8.4L11.7,12.3C12.09,12.69 12.09,13.33 11.7,13.72L8.42,17H5.59L9.58,13Z" /></svg>');
        }
        
        #secure-channel .screenshot-image {
            background-color: #fffde7; /* 浅黄色背景 */
            background-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" width="100" height="100" fill="%23f1c40f" opacity="0.2"><path d="M12,1L3,5V11C3,16.55 6.84,21.74 12,23C17.16,21.74 21,16.55 21,11V5L12,1M12,5.68C12.5,5.68 12.95,6.11 12.95,6.63V10.11L18,13.26V14.53L12.95,12.95V16.42L14.21,17.37V18.32L12,17.68L9.79,18.32V17.37L11.05,16.42V12.95L6,14.53V13.26L11.05,10.11V6.63C11.05,6.11 11.5,5.68 12,5.68Z" /></svg>');
        }
        
        #monitoring .screenshot-image {
            background-color: #ffebee; /* 浅红色背景 */
            background-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" width="100" height="100" fill="%23e74c3c" opacity="0.2"><path d="M12,9A3,3 0 0,1 15,12A3,3 0 0,1 12,15A3,3 0 0,1 9,12A3,3 0 0,1 12,9M12,4.5C17,4.5 21.27,7.61 23,12C21.27,16.39 17,19.5 12,19.5C7,19.5 2.73,16.39 1,12C2.73,7.61 7,4.5 12,4.5M3.18,12C4.83,15.36 8.24,17.5 12,17.5C15.76,17.5 19.17,15.36 20.82,12C19.17,8.64 15.76,6.5 12,6.5C8.24,6.5 4.83,8.64 3.18,12Z" /></svg>');
        }
        
        #time-server .screenshot-image {
            background-color: #e0f2f1; /* 浅青色背景 */
            background-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" width="100" height="100" fill="%231abc9c" opacity="0.2"><path d="M12,20A8,8 0 0,0 20,12A8,8 0 0,0 12,4A8,8 0 0,0 4,12A8,8 0 0,0 12,20M12,2A10,10 0 0,1 22,12A10,10 0 0,1 12,22C6.47,22 2,17.5 2,12A10,10 0 0,1 12,2M12.5,7V12.25L17,14.92L16.25,16.15L11,13V7H12.5Z" /></svg>');
        }
        
        /* 添加界面元素样式 */
        .ui-table {
            width: 90%;
            margin: 20px auto;
            border-collapse: collapse;
            font-size: 12px;
        }
        
        .ui-table th, .ui-table td {
            border: 1px solid #dee2e6;
            padding: 8px;
            text-align: left;
        }
        
        .ui-table th {
            background-color: #f8f9fa;
        }
        
        .ui-button {
            display: inline-block;
            padding: 6px 12px;
            background: linear-gradient(to bottom, #007bff, #0069d9);
            color: white;
            border-radius: 4px;
            font-size: 12px;
            margin: 5px;
        }
        
        .ui-search {
            display: flex;
            width: 90%;
            margin: 15px auto;
        }
        
        .ui-search input {
            flex-grow: 1;
            padding: 8px;
            border: 1px solid #ced4da;
            border-radius: 4px 0 0 4px;
        }
        
        .ui-search button {
            padding: 8px 15px;
            background: #007bff;
            color: white;
            border: 1px solid #007bff;
            border-radius: 0 4px 4px 0;
        }
        
        .ui-tabs {
            display: flex;
            width: 90%;
            margin: 10px auto;
            border-bottom: 1px solid #dee2e6;
        }
        
        .ui-tab {
            padding: 8px 16px;
            border: 1px solid transparent;
            border-top-left-radius: 4px;
            border-top-right-radius: 4px;
            margin-right: 5px;
            font-size: 14px;
            cursor: pointer;
        }
        
        .ui-tab.active {
            border-color: #dee2e6 #dee2e6 #fff;
            background-color: #fff;
        }
        
        .ui-panel {
            background-color: #fff;
            border-radius: 4px;
            box-shadow: 0 2px 5px rgba(0,0,0,0.05);
            margin: 10px auto;
            width: 90%;
            padding: 10px;
        }
        
        .ui-chart {
            width: 90%;
            height: 150px;
            margin: 10px auto;
            background: linear-gradient(to right, rgba(41,128,185,0.1), rgba(52,152,219,0.1));
            border-radius: 4px;
            position: relative;
            overflow: hidden;
        }
        
        .ui-chart::before {
            content: '';
            position: absolute;
            bottom: 0;
            left: 0;
            right: 0;
            height: 50px;
            background: linear-gradient(to right, rgba(41,128,185,0.3), rgba(52,152,219,0.3));
            clip-path: polygon(0 100%, 10% 70%, 20% 90%, 30% 50%, 40% 60%, 50% 30%, 60% 40%, 70% 20%, 80% 30%, 90% 10%, 100% 40%, 100% 100%);
        }
        
        /* 底部装饰和版权 */
        .footer {
            position: absolute;
            bottom: 20px;
            width: 100%;
            text-align: center;
            font-size: 14px;
            color: #95a5a6;
            left: 0;
            z-index: 10;
        }
        
        /* 侧边装饰 */
        .side-decoration {
            position: absolute;
            bottom: 40px;
            right: 40px;
            display: flex;
            gap: 15px;
            z-index: 10;
        }
        
        .side-icon {
            width: 50px;
            height: 50px;
            opacity: 0.7;
            transition: all 0.3s ease;
        }
        
        .side-icon:hover {
            transform: scale(1.1);
            opacity: 1;
        }
        
        /* 消息提示 */
        .toast {
            position: fixed;
            top: 50px;
            left: 50%;
            transform: translateX(-50%);
            background-color: rgba(0, 0, 0, 0.8);
            color: white;
            padding: 12px 25px;
            border-radius: 30px;
            font-size: 16px;
            z-index: 2000;
            display: none;
            animation: fadeInOut 2s ease;
        }
        
        @keyframes fadeInOut {
            0% { opacity: 0; }
            20% { opacity: 1; }
            80% { opacity: 1; }
            100% { opacity: 0; }
        }
        
        /* 美化表格样式 */
        .ui-table {
            width: 100%;
            border-collapse: collapse;
            font-size: 12px;
            background-color: white;
        }
        
        .ui-table th, .ui-table td {
            border: 1px solid #e9ecef;
            padding: 8px;
            text-align: left;
        }
        
        .ui-table th {
            background-color: #f8f9fa;
            font-weight: 600;
            color: #495057;
        }
        
        .ui-table tr:nth-child(even) {
            background-color: #f9f9f9;
        }
        
        .ui-table tr:hover {
            background-color: #f1f1f1;
        }
        
        /* 美化按钮样式 */
        .ui-button {
            display: inline-block;
            padding: 6px 12px;
            background: linear-gradient(to bottom, #007bff, #0069d9);
            color: white;
            border-radius: 4px;
            font-size: 12px;
            margin: 5px;
            cursor: pointer;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            text-align: center;
        }
        
        .ui-button:hover {
            transform: translateY(-1px);
            box-shadow: 0 4px 6px rgba(0,0,0,0.15);
        }
        
        /* 美化搜索框样式 */
        .ui-search {
            display: flex;
            width: 100%;
            margin: 15px 0;
        }
        
        .ui-search input {
            flex-grow: 1;
            padding: 8px 12px;
            border: 1px solid #ced4da;
            border-radius: 4px 0 0 4px;
            font-size: 12px;
        }
        
        .ui-search button {
            padding: 8px 15px;
            background: linear-gradient(to bottom, #007bff, #0069d9);
            color: white;
            border: none;
            border-radius: 0 4px 4px 0;
            cursor: pointer;
            font-size: 12px;
        }
        
        /* 美化标签页样式 */
        .ui-tabs {
            display: flex;
            width: 100%;
            margin: 10px 0;
            border-bottom: 1px solid #dee2e6;
        }
        
        .ui-tab {
            padding: 8px 16px;
            border: 1px solid transparent;
            border-top-left-radius: 4px;
            border-top-right-radius: 4px;
            margin-right: 5px;
            font-size: 14px;
            cursor: pointer;
            color: #6c757d;
        }
        
        .ui-tab.active {
            border-color: #dee2e6 #dee2e6 #fff;
            background-color: #fff;
            color: #495057;
            font-weight: 600;
        }
        
        .ui-tab:hover:not(.active) {
            background-color: #f8f9fa;
            color: #495057;
        }
        
        /* 修复截图容器内的样式 */
        .screenshot-image {
            padding: 0;
            background-color: white !important;
            background-image: none !important;
        }
        
        #key-lifecycle, #auth-center, #api-service, #monitoring {
            height: 100%;
            width: 100%;
            background-color: white !important;
            background-image: none !important;
        }
        
        /* 美化面板样式 */
        .ui-panel {
            background-color: #fff;
            border-radius: 4px;
            box-shadow: 0 2px 5px rgba(0,0,0,0.05);
            margin: 0;
            width: 100%;
            height: 100%;
            padding: 10px;
            box-sizing: border-box;
            overflow: auto;
        }
    </style>
</head>
<body>
    <div class="ppt-container" id="pptContainer">
        <div class="ppt-label">PPT尺寸(1920×1080) - 透明背景</div>
        <button class="copy-button" id="copyButton">
            <span class="copy-icon"></span>复制为PPT图片
        </button>
        <button class="download-button" id="downloadButton">
            <span class="download-icon"></span>下载透明PNG
        </button>
        
        <!-- 添加清晰度选择按钮组 -->
        <div class="resolution-selector" id="resolutionSelector">
            <span class="resolution-label">清晰度:</span>
            <button class="resolution-btn" data-scale="1">标准</button>
            <button class="resolution-btn active" data-scale="2">高清</button>
            <button class="resolution-btn" data-scale="3">超清</button>
        </div>
        
        <div class="slide" id="slide">
            <!-- 顶部标题 -->
            <div class="header">
                <h1>钥匙云平台</h1>
                <div class="subtitle">安全、高效的智能车辆数字钥匙全生命周期管理系统</div>
            </div>
            
            <!-- 功能截图网格 - 3个主要功能模块 -->
            <div class="screenshots-grid">
                <!-- 功能1: 系统首页-数据监控 -->
                <div class="screenshot-container feature-1">
                    <div class="screenshot-title-bar">
                        <div class="window-controls">
                            <div class="window-circle window-close"></div>
                            <div class="window-circle window-minimize"></div>
                            <div class="window-circle window-maximize"></div>
                        </div>
                        <div class="screenshot-title">系统首页 - 数据监控</div>
                    </div>
                    <div class="screenshot-content">
                        <div class="screenshot-image" id="dashboard">
                            <div class="ui-panel" style="height: 100%; padding: 15px; box-sizing: border-box;">
                                <!-- 顶部数据卡片 -->
                                <div style="display: grid; grid-template-columns: repeat(4, 1fr); gap: 15px; margin-bottom: 20px;">
                                    <div style="background: linear-gradient(135deg, #3498db, #2980b9); padding: 15px; border-radius: 8px; color: white;">
                                        <div style="font-size: 24px; font-weight: bold;">1,234</div>
                                        <div style="font-size: 14px;">总钥匙数量</div>
                                        <div style="font-size: 12px; margin-top: 5px;">较昨日 +12</div>
                                    </div>
                                    <div style="background: linear-gradient(135deg, #2ecc71, #27ae60); padding: 15px; border-radius: 8px; color: white;">
                                        <div style="font-size: 24px; font-weight: bold;">856</div>
                                        <div style="font-size: 14px;">活跃钥匙</div>
                                        <div style="font-size: 12px; margin-top: 5px;">使用率 69.3%</div>
                                    </div>
                                    <div style="background: linear-gradient(135deg, #e74c3c, #c0392b); padding: 15px; border-radius: 8px; color: white;">
                                        <div style="font-size: 24px; font-weight: bold;">23</div>
                                        <div style="font-size: 14px;">异常警报</div>
                                        <div style="font-size: 12px; margin-top: 5px;">待处理 5</div>
                                    </div>
                                    <div style="background: linear-gradient(135deg, #9b59b6, #8e44ad); padding: 15px; border-radius: 8px; color: white;">
                                        <div style="font-size: 24px; font-weight: bold;">98.7%</div>
                                        <div style="font-size: 14px;">系统稳定性</div>
                                        <div style="font-size: 12px; margin-top: 5px;">运行时间 30天</div>
                                    </div>
                                </div>

                                <!-- 图表区域 -->
                                <div style="display: grid; grid-template-columns: 2fr 1fr; gap: 15px; margin-bottom: 20px;">
                                    <!-- 左侧折线图 -->
                                    <div style="background: white; padding: 15px; border-radius: 8px; box-shadow: 0 2px 4px rgba(0,0,0,0.1);">
                                        <div style="font-weight: bold; margin-bottom: 10px;">钥匙使用趋势</div>
                                        <div class="ui-chart" style="height: 200px;">
                                            <!-- 模拟折线图 -->
                                            <svg width="100%" height="100%" viewBox="0 0 300 100" preserveAspectRatio="none">
                                                <path d="M0,50 C50,30 100,60 150,40 S250,70 300,50" stroke="#3498db" fill="none" stroke-width="2"/>
                                            </svg>
                                        </div>
                                    </div>
                                    <!-- 右侧饼图 -->
                                    <div style="background: white; padding: 15px; border-radius: 8px; box-shadow: 0 2px 4px rgba(0,0,0,0.1);">
                                        <div style="font-weight: bold; margin-bottom: 10px;">钥匙类型分布</div>
                                        <div style="display: flex; flex-direction: column; gap: 10px;">
                                            <div style="display: flex; justify-content: space-between;">
                                                <span>临时钥匙</span>
                                                <span>45%</span>
                                            </div>
                                            <div style="display: flex; justify-content: space-between;">
                                                <span>长期钥匙</span>
                                                <span>35%</span>
                                            </div>
                                            <div style="display: flex; justify-content: space-between;">
                                                <span>共享钥匙</span>
                                                <span>20%</span>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <!-- 底部列表 -->
                                <div style="background: white; padding: 15px; border-radius: 8px; box-shadow: 0 2px 4px rgba(0,0,0,0.1);">
                                    <div style="font-weight: bold; margin-bottom: 10px;">最近活动</div>
                                    <table class="ui-table">
                                        <thead>
                                            <tr>
                                                <th>时间</th>
                                                <th>事件</th>
                                                <th>用户</th>
                                                <th>状态</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <tr>
                                                <td>10:23:45</td>
                                                <td>创建新钥匙</td>
                                                <td>张三</td>
                                                <td><span style="color: green;">成功</span></td>
                                            </tr>
                                            <tr>
                                                <td>10:15:32</td>
                                                <td>钥匙授权</td>
                                                <td>李四</td>
                                                <td><span style="color: green;">成功</span></td>
                                            </tr>
                                            <tr>
                                                <td>10:05:18</td>
                                                <td>异常登录</td>
                                                <td>王五</td>
                                                <td><span style="color: red;">失败</span></td>
                                            </tr>
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>
                        <div class="screenshot-caption">实时监控系统运行状态，展示关键数据指标和趋势分析</div>
                    </div>
                </div>
                
                <!-- 功能2: 钥匙管理中心 -->
                <div class="screenshot-container feature-2">
                    <div class="screenshot-title-bar">
                        <div class="window-controls">
                            <div class="window-circle window-close"></div>
                            <div class="window-circle window-minimize"></div>
                            <div class="window-circle window-maximize"></div>
                        </div>
                        <div class="screenshot-title">钥匙管理中心</div>
                    </div>
                    <div class="screenshot-content">
                        <div class="screenshot-image" id="key-management">
                            <div class="ui-panel" style="height: 100%; padding: 15px; box-sizing: border-box;">
                                <!-- 顶部操作栏 -->
                                <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 20px;">
                                    <div class="ui-tabs">
                                        <div class="ui-tab active">全部钥匙</div>
                                        <div class="ui-tab">待授权</div>
                                        <div class="ui-tab">已授权</div>
                                        <div class="ui-tab">已过期</div>
                                    </div>
                                    <div>
                                        <button class="ui-button" style="background: linear-gradient(135deg, #2ecc71, #27ae60);">
                                            <i class="fas fa-plus"></i> 新建钥匙
                                        </button>
                                        <button class="ui-button" style="background: linear-gradient(135deg, #e74c3c, #c0392b);">
                                            批量操作
                                        </button>
                                    </div>
                                </div>

                                <!-- 搜索和筛选 -->
                                <div style="display: flex; gap: 15px; margin-bottom: 20px;">
                                    <div class="ui-search" style="margin: 0;">
                                        <input type="text" placeholder="搜索钥匙ID、用户名、车辆信息...">
                                        <button>搜索</button>
                                    </div>
                                    <select style="padding: 8px; border: 1px solid #ced4da; border-radius: 4px;">
                                        <option>所有类型</option>
                                        <option>临时钥匙</option>
                                        <option>长期钥匙</option>
                                        <option>共享钥匙</option>
                                    </select>
                                    <select style="padding: 8px; border: 1px solid #ced4da; border-radius: 4px;">
                                        <option>所有状态</option>
                                        <option>正常</option>
                                        <option>即将过期</option>
                                        <option>已过期</option>
                                    </select>
                                </div>

                                <!-- 钥匙列表 -->
                                <table class="ui-table">
                                    <thead>
                                        <tr>
                                            <th><input type="checkbox"></th>
                                            <th>钥匙ID</th>
                                            <th>用户</th>
                                            <th>车辆信息</th>
                                            <th>类型</th>
                                            <th>授权时间</th>
                                            <th>过期时间</th>
                                            <th>状态</th>
                                            <th>操作</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                            <td><input type="checkbox"></td>
                                            <td>KEY-001</td>
                                            <td>张三</td>
                                            <td>宝马 X5</td>
                                            <td>长期钥匙</td>
                                            <td>2024-03-01</td>
                                            <td>2025-03-01</td>
                                            <td><span style="color: green;">正常</span></td>
                                            <td>
                                                <button class="ui-button">编辑</button>
                                                <button class="ui-button">续期</button>
                                                <button class="ui-button">撤销</button>
                                            </td>
                                        </tr>
                                        <tr>
                                            <td><input type="checkbox"></td>
                                            <td>KEY-002</td>
                                            <td>李四</td>
                                            <td>奔驰 C300L</td>
                                            <td>临时钥匙</td>
                                            <td>2024-03-10</td>
                                            <td>2024-03-17</td>
                                            <td><span style="color: orange;">即将过期</span></td>
                                            <td>
                                                <button class="ui-button">编辑</button>
                                                <button class="ui-button">续期</button>
                                                <button class="ui-button">撤销</button>
                                            </td>
                                        </tr>
                                        <tr>
                                            <td><input type="checkbox"></td>
                                            <td>KEY-003</td>
                                            <td>王五</td>
                                            <td>奥迪 A6L</td>
                                            <td>共享钥匙</td>
                                            <td>2024-02-15</td>
                                            <td>2024-03-15</td>
                                            <td><span style="color: red;">已过期</span></td>
                                            <td>
                                                <button class="ui-button">编辑</button>
                                                <button class="ui-button">续期</button>
                                                <button class="ui-button">撤销</button>
                                            </td>
                                        </tr>
                                    </tbody>
                                </table>

                                <!-- 分页 -->
                                <div style="display: flex; justify-content: space-between; align-items: center; margin-top: 20px;">
                                    <div>
                                        共 <b>256</b> 条记录
                                    </div>
                                    <div style="display: flex; gap: 10px;">
                                        <button class="ui-button">上一页</button>
                                        <button class="ui-button" style="background: #3498db;">1</button>
                                        <button class="ui-button">2</button>
                                        <button class="ui-button">3</button>
                                        <button class="ui-button">下一页</button>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="screenshot-caption">全面管理数字钥匙，支持创建、授权、撤销等完整生命周期操作</div>
                    </div>
                </div>
                
                <!-- 功能3: 钥匙分享管理 -->
                <div class="screenshot-container feature-3">
                    <div class="screenshot-title-bar">
                        <div class="window-controls">
                            <div class="window-circle window-close"></div>
                            <div class="window-circle window-minimize"></div>
                            <div class="window-circle window-maximize"></div>
                        </div>
                        <div class="screenshot-title">钥匙分享管理</div>
                    </div>
                    <div class="screenshot-content">
                        <div class="screenshot-image" id="key-sharing">
                            <div class="ui-panel" style="height: 100%; padding: 15px; box-sizing: border-box;">
                                <!-- 顶部分享记录和权限设置切换 -->
                                <div class="ui-tabs" style="margin-bottom: 20px;">
                                    <div class="ui-tab active">分享记录</div>
                                    <div class="ui-tab">权限设置</div>
                                    <div class="ui-tab">分享统计</div>
                                </div>

                                <!-- 分享记录列表 -->
                                <div style="background: white; padding: 15px; border-radius: 8px; box-shadow: 0 2px 4px rgba(0,0,0,0.1); margin-bottom: 20px;">
                                    <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 15px;">
                                        <h3 style="margin: 0;">最近分享记录</h3>
                                        <button class="ui-button" style="background: linear-gradient(135deg, #2ecc71, #27ae60);">
                                            新建分享
                                        </button>
                                    </div>
                                    <table class="ui-table">
                                        <thead>
                                            <tr>
                                                <th>分享ID</th>
                                                <th>分享人</th>
                                                <th>接收人</th>
                                                <th>车辆信息</th>
                                                <th>分享时间</th>
                                                <th>有效期</th>
                                                <th>使用次数</th>
                                                <th>状态</th>
                                                <th>操作</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <tr>
                                                <td>SHARE-001</td>
                                                <td>张三</td>
                                                <td>李四</td>
                                                <td>宝马 X5</td>
                                                <td>2024-03-10</td>
                                                <td>7天</td>
                                                <td>3/10</td>
                                                <td><span style="color: green;">生效中</span></td>
                                                <td>
                                                    <button class="ui-button">查看</button>
                                                    <button class="ui-button">撤销</button>
                                                </td>
                                            </tr>
                                            <tr>
                                                <td>SHARE-002</td>
                                                <td>王五</td>
                                                <td>赵六</td>
                                                <td>奔驰 C300L</td>
                                                <td>2024-03-09</td>
                                                <td>3天</td>
                                                <td>5/5</td>
                                                <td><span style="color: orange;">已用完</span></td>
                                                <td>
                                                    <button class="ui-button">查看</button>
                                                    <button class="ui-button">续期</button>
                                                </td>
                                            </tr>
                                        </tbody>
                                    </table>
                                </div>

                                <!-- 分享权限设置 -->
                                <div style="display: grid; grid-template-columns: repeat(2, 1fr); gap: 20px;">
                                    <!-- 左侧：权限模板 -->
                                    <div style="background: white; padding: 15px; border-radius: 8px; box-shadow: 0 2px 4px rgba(0,0,0,0.1);">
                                        <h3 style="margin-bottom: 15px;">权限模板</h3>
                                        <div style="display: flex; flex-direction: column; gap: 10px;">
                                            <div style="display: flex; justify-content: space-between; align-items: center; padding: 10px; background: #f8f9fa; border-radius: 4px;">
                                                <span>完全访问权限</span>
                                                <button class="ui-button">使用</button>
                                            </div>
                                            <div style="display: flex; justify-content: space-between; align-items: center; padding: 10px; background: #f8f9fa; border-radius: 4px;">
                                                <span>仅开关车门</span>
                                                <button class="ui-button">使用</button>
                                            </div>
                                            <div style="display: flex; justify-content: space-between; align-items: center; padding: 10px; background: #f8f9fa; border-radius: 4px;">
                                                <span>仅查看位置</span>
                                                <button class="ui-button">使用</button>
                                            </div>
                                        </div>
                                    </div>

                                    <!-- 右侧：使用限制 -->
                                    <div style="background: white; padding: 15px; border-radius: 8px; box-shadow: 0 2px 4px rgba(0,0,0,0.1);">
                                        <h3 style="margin-bottom: 15px;">使用限制</h3>
                                        <div style="display: flex; flex-direction: column; gap: 15px;">
                                            <div>
                                                <label style="display: block; margin-bottom: 5px;">最大使用次数</label>
                                                <input type="number" style="width: 100%; padding: 8px; border: 1px solid #ced4da; border-radius: 4px;" placeholder="输入使用次数限制">
                                            </div>
                                            <div>
                                                <label style="display: block; margin-bottom: 5px;">有效期限制</label>
                                                <select style="width: 100%; padding: 8px; border: 1px solid #ced4da; border-radius: 4px;">
                                                    <option>1天</option>
                                                    <option>3天</option>
                                                    <option>7天</option>
                                                    <option>30天</option>
                                                </select>
                                            </div>
                                            <div>
                                                <label style="display: block; margin-bottom: 5px;">地理位置限制</label>
                                                <input type="text" style="width: 100%; padding: 8px; border: 1px solid #ced4da; border-radius: 4px;" placeholder="输入允许使用的地理范围">
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="screenshot-caption">管理钥匙分享记录，设置分享权限和使用限制，确保安全可控</div>
                    </div>
                </div>
            </div>
            
            <!-- 侧边装饰图标 -->
            <div class="side-decoration">
                <svg class="side-icon" viewBox="0 0 24 24" fill="#3498db">
                    <path d="M12,17A2,2 0 0,0 14,15C14,13.89 13.1,13 12,13A2,2 0 0,0 10,15A2,2 0 0,0 12,17M18,8A2,2 0 0,1 20,10V20A2,2 0 0,1 18,22H6A2,2 0 0,1 4,20V10C4,8.89 4.9,8 6,8H7V6A5,5 0 0,1 12,1A5,5 0 0,1 17,6V8H18M12,3A3,3 0 0,0 9,6V8H15V6A3,3 0 0,0 12,3Z" />
                </svg>
                <svg class="side-icon" viewBox="0 0 24 24" fill="#2ecc71">
                    <path d="M21,11C21,16.55 17.16,21.74 12,23C6.84,21.74 3,16.55 3,11V5L12,1L21,5V11M12,21C15.75,20 19,15.54 19,11.22V6.3L12,3.18L5,6.3V11.22C5,15.54 8.25,20 12,21Z" />
                </svg>
                <svg class="side-icon" viewBox="0 0 24 24" fill="#e67e22">
                    <path d="M12,12H19C18.47,16.11 15.72,19.78 12,20.92V12H5V6.3L12,3.19M12,1L3,5V11C3,16.55 6.84,21.73 12,23C17.16,21.73 21,16.55 21,11V5L12,1Z" />
                </svg>
            </div>
        </div>
    </div>
    
    <div class="toast" id="toast">操作成功！</div>
    
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const copyButton = document.getElementById('copyButton');
            const downloadButton = document.getElementById('downloadButton');
            const toast = document.getElementById('toast');
            const slide = document.getElementById('slide');
            const resolutionBtns = document.querySelectorAll('.resolution-btn');
            
            // 默认清晰度为2x (高清)
            let currentScale = 2;
            
            // 清晰度按钮点击事件
            resolutionBtns.forEach(btn => {
                btn.addEventListener('click', function() {
                    // 移除所有按钮的active类
                    resolutionBtns.forEach(b => b.classList.remove('active'));
                    // 给当前按钮添加active类
                    this.classList.add('active');
                    // 更新当前清晰度
                    currentScale = parseFloat(this.getAttribute('data-scale'));
                    
                    // 显示提示
                    showToast(`清晰度已设置为${this.textContent}模式`);
                });
            });
            
            // 显示提示消息
            function showToast(message) {
                toast.textContent = message;
                toast.style.display = 'block';
                setTimeout(function() {
                    toast.style.display = 'none';
                }, 2000);
            }
            
            // 生成透明背景图片
            function generateImage() {
                return html2canvas(slide, {
                    scale: currentScale, // 使用用户选择的清晰度
                    useCORS: true,
                    allowTaint: true,
                    backgroundColor: null // 关键：设置为null以支持透明背景
                });
            }
            
            // 复制按钮功能
            copyButton.addEventListener('click', function() {
                showToast("正在处理图片，请稍候...");
                
                generateImage().then(function(canvas) {
                    try {
                        // 使用现代剪贴板API
                        canvas.toBlob(function(blob) {
                            try {
                                const item = new ClipboardItem({ 'image/png': blob });
                                navigator.clipboard.write([item]).then(function() {
                                    showToast("已复制到剪贴板！现在可以粘贴到PPT中");
                                }).catch(function(err) {
                                    showToast("自动复制失败，请使用下载按钮获取图片");
                                    console.error("剪贴板API错误:", err);
                                });
                            } catch (e) {
                                showToast("您的浏览器不支持复制图片，请使用下载按钮");
                                console.error("ClipboardItem错误:", e);
                            }
                        });
                    } catch (e) {
                        showToast("复制功能不可用，请使用下载图片功能");
                        console.error("toBlob错误:", e);
                    }
                }).catch(function(err) {
                    showToast("生成图片失败，请尝试下载或截图");
                    console.error("html2canvas错误:", err);
                });
            });
            
            // 下载按钮功能
            downloadButton.addEventListener('click', function () {
                showToast(`正在生成${currentScale}x清晰度透明PNG图片，请稍候...`);

                generateImage().then(function (canvas) {
                    // 将Canvas转换为PNG并下载
                    const imageUrl = canvas.toDataURL('image/png');
                    const link = document.createElement('a');

                    // 首先从URL路径获取文件名
                    let fileName = "";
                    const pathParts = window.location.pathname.split('/');
                    const htmlFileName = pathParts[pathParts.length - 1];
                    // 移除.html后缀并进行URL解码以支持中文文件名
                    fileName = decodeURIComponent(htmlFileName.replace('.html', ''));

                    // 如果从URL获取的文件名为空，尝试从标题获取
                    if (!fileName) {
                        const titleElement = document.querySelector('.header h1');
                        if (titleElement && titleElement.textContent) {
                            fileName = titleElement.textContent.trim();
                        }
                    }

                    // 确保文件名不为空
                    if (!fileName) {
                        fileName = "测试";
                    }

                    link.download = `${fileName}-透明-${currentScale}x清晰度.png`;
                    link.href = imageUrl;
                    link.click();

                    showToast(`${currentScale}x清晰度透明背景PNG已下载`);
                }).catch(function (err) {
                    showToast("生成图片失败，请尝试截图");
                    console.error("Error:", err);
                });
            });
        });
    </script>
</body>
</html> 
</body>
</html> 
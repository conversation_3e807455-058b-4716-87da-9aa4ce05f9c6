@startuml
skinparam monochrome true

participant "APP" as Mobile
participant "车端" as CarBT
participant "钥匙云平台" as Cloud

== 蓝牙配对流程 ==

Mobile -> Mobile : 1. 用户在APP中选择添加数字钥匙
Mobile -> Cloud : 2. 请求获取数字钥匙
Cloud -> Cloud : 3. 生成虚拟密钥（VIRKEY）
Cloud -> Mobile : 4. 下发虚拟密钥到手机
Mobile -> Mobile : 5. 保存虚拟密钥到安全存储区
Cloud -> CarBT : 6. 生成根密钥并下发到汽车
CarBT -> CarBT : 7. 存储根密钥
CarBT -> Mobile : 8. 发送蓝牙广播（包含MAC地址等信息）
Mobile -> CarBT : 9. 扫描并连接到汽车蓝牙模块
Mobile -> CarBT : 10. 发送蓝牙配对请求
CarBT -> Mobile : 11. 发起蓝牙安全请求
CarBT -> CarBT : 12. 生成配对码
Mobile -> Mobile : 13. 生成或输入配对码
Mobile -> CarBT : 14. 发送配对码进行校验
CarBT -> Mobile : 15. 返回配对码校验结果
CarBT -> CarBT : 16. 配对成功，绑定设备信息
Mobile -> Mobile : 17. 配对成功，绑定设备信息
Mobile -> CarBT : 18. 发送加密的虚拟密钥
CarBT -> CarBT : 19. 验证虚拟密钥
CarBT -> Mobile : 20. 返回验证结果
Mobile -> Cloud : 21. 上报配对结果
Cloud -> Cloud : 22. 记录钥匙绑定状态
@enduml
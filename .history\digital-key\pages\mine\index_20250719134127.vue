<template>
  <view class="container bg-cls">
    <!-- 自定义导航栏 -->
    <view class="custom-navbar">
      <text class="navbar-title">零感数字钥匙</text>
    </view>

    <!-- 页面内容 -->
    <view class="mine-container" >
      <!--顶部个人信息栏-->
      <view class="header-section">
        <view class="flex padding justify-between">
          <view class="flex align-center">
            <view v-if="!avatar" class="cu-avatar xl round bg-white">
              <view class="iconfont icon-people text-gray icon"></view>
            </view>
            <image v-if="avatar" @click="handleToAvatar" :src="avatar" class="cu-avatar xl round" mode="widthFix">
            </image>
            <view v-if="!name" @click="handleToLogin" class="login-tip">
              点击登录
            </view>
            <view v-if="name" @click="handleToInfo" class="user-info">
              <view class="u_title">
                账号：{{ name }}
              </view>
            </view>
          </view>
          <view @click="handleToInfo" class="flex align-center">
            <text>个人信息</text>
            <view class="iconfont icon-right"></view>
          </view>
        </view>
      </view>

      <view class="content-section">
        <!-- 申请相关功能区 -->
        <view class="section-title">数字钥匙申请</view>
        <view class="mine-actions grid col-2 text-center">
          <view class="action-item" @click="handleToApply">
            <view class="iconfont icon-add text-blue icon"></view>
            <text class="text">申请钥匙</text>
          </view>
          <view class="action-item" @click="handleToApplicationStatus">
            <view class="iconfont icon-list text-orange icon"></view>
            <text class="text">申请状态</text>
          </view>
        </view>

        <!-- 个人设置功能区 -->
        <view class="section-title">个人设置</view>
        <view class="mine-actions grid col-4 text-center">
          <view class="action-item" @click="handleToEditInfo">
            <view class="iconfont icon-user text-pink icon"></view>
            <text class="text">编辑资料</text>
          </view>
          <view class="action-item" @click="handleToPwd">
            <view class="iconfont icon-password text-blue icon"></view>
            <text class="text">修改密码</text>
          </view>
          <view class="action-item" @click="handleAbout">
            <view class="iconfont icon-aixin text-mauve icon"></view>
            <text class="text">关于我们</text>
          </view>
          <view class="action-item" @click="handleLogout">
            <view class="iconfont icon-clean text-green icon"></view>
            <text class="text">退出登录</text>
          </view>
        </view>

        <view class="mine-actions grid col-4 text-center">
          <view class="action-item" @click="goToStartCalibrationPage">
            <view class="iconfont icon-refresh text-pink icon"></view>
            <text class="text">定位校准</text>
          </view>
          <view class="action-item" @click="goToDigitalKeySharePage">
            <view class="iconfont icon-share text-blue icon"></view>
            <text class="text">分享钥匙</text>
          </view>
        </view>

        <!-- <view class="menu-list"> -->
          <!-- <view class="list-cell list-cell-arrow" @click="handleAbout"> -->
            <!-- <view class="menu-item-box"> -->
              <!-- <view class="iconfont icon-aixin menu-icon"></view> -->
              <!-- <view>关于我们</view> -->
            <!-- </view> -->
          <!-- </view> -->
        <!-- </view> -->

      </view>
    </view>
  </view>
</template>

<script>
  import storage from '@/utils/storage'
  
  export default {
    data() {
      return {
        name: this.$store.state.user.name,
        version: getApp().globalData.config.appInfo.version
      }
    },
    computed: {
      avatar() {
        return this.$store.state.user.avatar
      },
      windowHeight() {
        return uni.getSystemInfoSync().windowHeight - 50
      }
    },
    methods: {
       /**
       * @description: 跳转到分享钥匙页面
       * @return {*}
       */    
      goToDigitalKeySharePage() {
        uni.navigateTo({
          url: '../work/digitalKeyShare/index'
        });
      },

       /**
       * @description: 跳转到开始校准页面
       * @return {*}
       */    
      goToStartCalibrationPage() {
        uni.navigateTo({
          url: '../work/startCalibration/index'
        });
      },

      handleToPwd() {
        this.$tab.navigateTo('/pages/mine/pwd/index')
      },
      handleLogout() {
        this.$modal.confirm('确定注销并退出系统吗？').then(() => {
          this.$store.dispatch('LogOut').then(() => {
            this.$tab.reLaunch('/pages/index')
          })
        })
      },
      handleToInfo() {
        this.$tab.navigateTo('/pages/mine/info/index')
      },
      handleToEditInfo() {
        this.$tab.navigateTo('/pages/mine/info/edit')
      },
      handleToSetting() {
        this.$tab.navigateTo('/pages/mine/setting/index')
      },
      handleToLogin() {
        this.$tab.reLaunch('/pages/login')
      },
      handleToAvatar() {
        this.$tab.navigateTo('/pages/mine/avatar/index')
      },
      handleLogout() {
        this.$modal.confirm('确定注销并退出系统吗？').then(() => {
          // 从缓存里获取登录相关信息
          let loginForm = uni.getStorageSync("loginForm");

          if (loginForm) {
            // 本次不需要自动登录
            loginForm.autoLogonSign = false;
            // 缓存登录相关信息
            uni.setStorageSync("loginForm", loginForm);
          }
          
          this.$store.dispatch('LogOut').then(() => {
            this.$tab.reLaunch('/pages/index')
          })
        })
      },
      handleHelp() {
        this.$tab.navigateTo('/pages/mine/help/index')
      },
      handleAbout() {
        this.$tab.navigateTo('/pages/mine/about/index')
      },
      handleJiaoLiuQun() {
        this.$modal.showToast('QQ群：①133713780、②146013835')
      },
      handleBuilding() {
        this.$modal.showToast('模块建设中~')
      }
    }
  }
</script>

<style lang="scss">
  page {
    /* 设置背景固定，不随页面滚动 */
    background-attachment: fixed;

    /* 其他样式，比如设置高度 */
    height: 70vh;
    /* 设置高度为视口高度 */
  }

  .custom-navbar {
    display: flex;
    justify-content: center;
    align-items: center;
    height: 184rpx; /* 导航栏高度 */
    border-radius: 0rpx 0rpx 0rpx 0rpx;
    background-color: rgba(0, 0, 0, 0); /* 自定义背景色 */
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    z-index: 999;
  }

  .navbar-title {
    margin-top: 75rpx;
    color: #121010; /* 标题颜色 */
  }

  .mine-container::before {
    content: '';
    position: absolute;
    top: 184rpx;
    left: 50%;
    width: 100%;
    height: 1px;
    background: rgba(255, 255, 255, 0.3); /* 白色系 */
    transform: translateX(-50%);
    z-index: -1;
    filter: blur(2px);
  }

  .mine-container {
    padding-top: 184rpx;
    width: 100%;
    height: 100%;


    .header-section {
      padding: 15px 15px 45px 15px;
      background-color: transparent;
      color: rgb(10, 10, 10);

      .login-tip {
        font-size: 18px;
        margin-left: 10px;
      }

      .cu-avatar {
        border: 2px solid #eaeaea;

        .icon {
          font-size: 40px;
        }
      }

      .user-info {
        margin-left: 15px;

        .u_title {
          font-size: 18px;
          line-height: 30px;
        }
      }
    }

    .content-section {
      position: relative;
      top: -50px;

      .mine-actions {
        margin: 15px 15px;
        padding: 20px 0px;
        border-radius: 8px;
        // background-color: white;

        .action-item {
          .icon {
            font-size: 28px;
          }

          .text {
            display: block;
            font-size: 13px;
            margin: 8px 0px;
          }
        }
      }
    }
  }
</style>

/**
 * 4G控车配置文件
 */

// 开发环境配置
const development = {
  // WebSocket服务器地址
  websocketUrl: 'ws://localhost:9201',
  
  // 是否启用4G控车功能
  enabled: true,
  
  // 重连配置
  reconnect: {
    maxAttempts: 5,
    interval: 5000
  },
  
  // 超时配置
  timeout: {
    connect: 10000,
    command: 5000
  }
}

// 生产环境配置
const production = {
  // WebSocket服务器地址 - 小程序可能不支持非标准端口，尝试使用标准端口
  websocketUrl: 'wss://dk-api.scsoi.com',

  // 是否启用4G控车功能
  enabled: true,

  // 重连配置
  reconnect: {
    maxAttempts: 3,
    interval: 10000
  },

  // 超时配置
  timeout: {
    connect: 15000,
    command: 8000
  }
}

// 测试环境配置
const testing = {
  // WebSocket服务器地址
  websocketUrl: 'ws://test-server:9201',
  
  // 是否启用4G控车功能
  enabled: true,
  
  // 重连配置
  reconnect: {
    maxAttempts: 5,
    interval: 3000
  },
  
  // 超时配置
  timeout: {
    connect: 8000,
    command: 3000
  }
}

// 根据环境变量选择配置
const getConfig = () => {
  // 检测是否在微信小程序环境中
  const isWeixinMiniProgram = typeof wx !== 'undefined' && wx.getSystemInfoSync

  // 检测是否在开发工具中
  const isDevTools = isWeixinMiniProgram && wx.getSystemInfoSync().platform === 'devtools'

  // 小程序发布后使用生产环境配置
  const env = process.env.NODE_ENV || 'production'

  // 如果是开发工具，可以使用开发环境；如果是真机，强制使用生产环境
  if (isWeixinMiniProgram && !isDevTools) {
    console.log('🚗 检测到小程序真机环境，使用生产配置')
    return production
  }

  switch (env) {
    case 'production':
      return production
    case 'testing':
      return testing
    case 'development':
      return development
    default:
      return production // 默认使用生产环境
  }
}

// 导出配置
export default getConfig()

// 也可以导出所有配置供调试使用
export {
  development,
  production,
  testing,
  getConfig
}

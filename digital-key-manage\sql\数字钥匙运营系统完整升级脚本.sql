-- =====================================================
-- 数字钥匙运营系统完整升级脚本 v1.0
-- 创建时间：2024-01-15
-- 
-- 【业务改动总览】
-- 原系统：简单的蓝牙密钥管理
-- 新系统：完整的数字钥匙运营管理系统
--
-- 【核心改动说明】
-- 1. 扩展钥匙管理 - 在现有基础上增加用户权限控制功能
-- 2. 新增车辆管理 - 统一管理车辆基础信息和状态
-- 3. 新增申请管理 - 用户申请→审核→分配的完整流程
-- 4. 新增分配管理 - 车辆与用户的分配记录和状态跟踪
-- 5. 完善运营功能 - 支持运营人员的日常管理工作
--
-- 【兼容性保证】
-- ✅ 对现有业务零影响 - 只新增，不修改现有数据和结构
-- ✅ 现有功能继续工作 - 所有现有查询、插入、更新操作不受影响
-- ✅ 数据完整性保证 - 新增字段都有合理默认值
-- =====================================================

-- 设置安全模式
SET SQL_SAFE_UPDATES = 0;
SET FOREIGN_KEY_CHECKS = 0;

-- 开始事务，确保原子性
START TRANSACTION;

SELECT '🚀 开始数字钥匙运营系统数据库升级...' as message;

-- =====================================================
-- 【改动1】扩展现有钥匙管理表 dk_vehicle_bluetooth_keys
-- 目的：在现有钥匙管理基础上增加运营管理功能
-- 影响：对现有业务零影响，只新增字段
-- =====================================================

SELECT '📝 正在扩展现有钥匙管理表...' as message;

-- 1.1 添加用户权限管理字段（支持权限限制和恢复功能）
SET @sql = (SELECT IF(
    (SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS 
     WHERE TABLE_SCHEMA = DATABASE() 
     AND TABLE_NAME = 'dk_vehicle_bluetooth_keys' 
     AND COLUMN_NAME = 'user_status') = 0,
    'ALTER TABLE dk_vehicle_bluetooth_keys ADD COLUMN user_status CHAR(1) DEFAULT ''0'' COMMENT ''用户状态（0正常 1限制）'' AFTER bluetooth_perm_key_time',
    'SELECT ''✅ user_status 字段已存在'' as info'
));
PREPARE stmt FROM @sql; EXECUTE stmt; DEALLOCATE PREPARE stmt;

-- 1.2 添加状态更新时间字段（记录权限变更时间）
SET @sql = (SELECT IF(
    (SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS 
     WHERE TABLE_SCHEMA = DATABASE() 
     AND TABLE_NAME = 'dk_vehicle_bluetooth_keys' 
     AND COLUMN_NAME = 'status_update_time') = 0,
    'ALTER TABLE dk_vehicle_bluetooth_keys ADD COLUMN status_update_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT ''状态更新时间'' AFTER user_status',
    'SELECT ''✅ status_update_time 字段已存在'' as info'
));
PREPARE stmt FROM @sql; EXECUTE stmt; DEALLOCATE PREPARE stmt;

-- 1.3 添加操作人员字段（记录是谁操作的）
SET @sql = (SELECT IF(
    (SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS 
     WHERE TABLE_SCHEMA = DATABASE() 
     AND TABLE_NAME = 'dk_vehicle_bluetooth_keys' 
     AND COLUMN_NAME = 'operator_id') = 0,
    'ALTER TABLE dk_vehicle_bluetooth_keys ADD COLUMN operator_id BIGINT(20) DEFAULT NULL COMMENT ''操作人员ID'' AFTER status_update_time',
    'SELECT ''✅ operator_id 字段已存在'' as info'
));
PREPARE stmt FROM @sql; EXECUTE stmt; DEALLOCATE PREPARE stmt;

-- 1.4 添加备注字段（记录操作原因）
SET @sql = (SELECT IF(
    (SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS 
     WHERE TABLE_SCHEMA = DATABASE() 
     AND TABLE_NAME = 'dk_vehicle_bluetooth_keys' 
     AND COLUMN_NAME = 'remark') = 0,
    'ALTER TABLE dk_vehicle_bluetooth_keys ADD COLUMN remark VARCHAR(500) DEFAULT '''' COMMENT ''备注信息'' AFTER operator_id',
    'SELECT ''✅ remark 字段已存在'' as info'
));
PREPARE stmt FROM @sql; EXECUTE stmt; DEALLOCATE PREPARE stmt;

-- 1.5 添加车辆关联字段（关联到车辆管理表）
SET @sql = (SELECT IF(
    (SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS 
     WHERE TABLE_SCHEMA = DATABASE() 
     AND TABLE_NAME = 'dk_vehicle_bluetooth_keys' 
     AND COLUMN_NAME = 'vehicle_id') = 0,
    'ALTER TABLE dk_vehicle_bluetooth_keys ADD COLUMN vehicle_id BIGINT(20) COMMENT ''车辆ID'' AFTER vehicle_vin',
    'SELECT ''✅ vehicle_id 字段已存在'' as info'
));
PREPARE stmt FROM @sql; EXECUTE stmt; DEALLOCATE PREPARE stmt;

-- 1.6 添加分配记录关联字段（关联到分配记录表）
SET @sql = (SELECT IF(
    (SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS 
     WHERE TABLE_SCHEMA = DATABASE() 
     AND TABLE_NAME = 'dk_vehicle_bluetooth_keys' 
     AND COLUMN_NAME = 'assignment_id') = 0,
    'ALTER TABLE dk_vehicle_bluetooth_keys ADD COLUMN assignment_id BIGINT(20) COMMENT ''分配记录ID'' AFTER vehicle_id',
    'SELECT ''✅ assignment_id 字段已存在'' as info'
));
PREPARE stmt FROM @sql; EXECUTE stmt; DEALLOCATE PREPARE stmt;

-- 1.7 为现有数据设置默认状态（确保数据完整性）
UPDATE dk_vehicle_bluetooth_keys SET user_status = '0' WHERE user_status IS NULL;

SELECT '✅ 现有钥匙管理表扩展完成' as message;

-- =====================================================
-- 【改动2】新增车辆信息管理表 dk_vehicle_info
-- 目的：统一管理车辆基础信息，支持车辆选择功能
-- 影响：全新表，对现有业务无影响
-- =====================================================

SELECT '🚗 正在创建车辆信息管理表...' as message;

CREATE TABLE IF NOT EXISTS dk_vehicle_info (
    vehicle_id BIGINT(20) NOT NULL AUTO_INCREMENT COMMENT '车辆ID',
    vin_code VARCHAR(17) NOT NULL COMMENT 'VIN码',
    brand VARCHAR(50) DEFAULT '' COMMENT '品牌',
    model VARCHAR(50) DEFAULT '' COMMENT '型号',
    color VARCHAR(20) DEFAULT '' COMMENT '颜色',
    license_plate VARCHAR(20) DEFAULT '' COMMENT '车牌号',
    vehicle_type VARCHAR(20) DEFAULT '' COMMENT '车辆类型',
    engine_no VARCHAR(50) DEFAULT '' COMMENT '发动机号',
    purchase_date DATE COMMENT '购买日期',
    status CHAR(1) DEFAULT '0' COMMENT '车辆状态（0空闲 1使用中 2维护中 3停用）',
    location VARCHAR(100) DEFAULT '' COMMENT '停放位置',
    mileage DECIMAL(10,2) DEFAULT 0.00 COMMENT '里程数(公里)',
    fuel_level DECIMAL(5,2) DEFAULT 100.00 COMMENT '燃油/电量百分比',
    last_maintenance_date DATE COMMENT '上次保养日期',
    next_maintenance_date DATE COMMENT '下次保养日期',
    insurance_expire_date DATE COMMENT '保险到期日期',
    annual_inspection_date DATE COMMENT '年检到期日期',
    remark VARCHAR(500) DEFAULT '' COMMENT '备注信息',
    create_by VARCHAR(64) DEFAULT '' COMMENT '创建者',
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    update_by VARCHAR(64) DEFAULT '' COMMENT '更新者',
    update_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    del_flag CHAR(1) DEFAULT '0' COMMENT '删除标志（0代表存在 2代表删除）',
    PRIMARY KEY (vehicle_id),
    UNIQUE KEY uk_vin_code (vin_code),
    KEY idx_status (status),
    KEY idx_location (location),
    KEY idx_create_time (create_time)
) ENGINE=InnoDB AUTO_INCREMENT=1 COMMENT = '车辆信息表';

SELECT '✅ 车辆信息管理表创建完成' as message;

-- =====================================================
-- 【改动3】新增用户申请管理表 dk_user_application
-- 目的：支持用户申请→审核→分配的完整业务流程
-- 影响：全新表，对现有业务无影响
-- =====================================================

SELECT '👥 正在创建用户申请管理表...' as message;

CREATE TABLE IF NOT EXISTS dk_user_application (
    application_id BIGINT(20) NOT NULL AUTO_INCREMENT COMMENT '申请ID',
    application_no VARCHAR(32) NULL COMMENT '申请单号',
    user_id BIGINT(20) COMMENT '用户ID',
    real_name VARCHAR(50) NOT NULL COMMENT '真实姓名',
    phone VARCHAR(11) NOT NULL COMMENT '手机号',
    id_card VARCHAR(18) NOT NULL COMMENT '身份证号',
    driving_license VARCHAR(50) COMMENT '驾驶证号',
    id_card_front_url VARCHAR(255) COMMENT '身份证正面照片URL',
    id_card_back_url VARCHAR(255) COMMENT '身份证背面照片URL',
    driving_license_url VARCHAR(255) COMMENT '驾驶证照片URL',
    application_reason VARCHAR(500) COMMENT '申请原因',
    status CHAR(1) DEFAULT '0' COMMENT '申请状态（0待审核 1审核通过 2审核拒绝 3补充资料）',
    audit_user_id BIGINT(20) COMMENT '审核人ID',
    audit_user_name VARCHAR(50) COMMENT '审核人姓名',
    audit_time DATETIME COMMENT '审核时间',
    audit_remark VARCHAR(500) COMMENT '审核备注',
    create_by VARCHAR(64) DEFAULT '' COMMENT '创建者',
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    update_by VARCHAR(64) DEFAULT '' COMMENT '更新者',
    update_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    del_flag CHAR(1) DEFAULT '0' COMMENT '删除标志（0代表存在 2代表删除）',
    PRIMARY KEY (application_id),
    UNIQUE KEY uk_application_no (application_no),
    KEY idx_user_id (user_id),
    KEY idx_status (status),
    KEY idx_phone (phone),
    KEY idx_create_time (create_time)
) ENGINE=InnoDB AUTO_INCREMENT=1 COMMENT = '用户申请表';

SELECT '✅ 用户申请管理表创建完成' as message;

-- =====================================================
-- 【改动4】新增车辆分配记录表 dk_vehicle_assignment
-- 目的：记录车辆与用户的分配关系和使用状态
-- 影响：全新表，对现有业务无影响
-- =====================================================

SELECT '📋 正在创建车辆分配记录表...' as message;

CREATE TABLE IF NOT EXISTS dk_vehicle_assignment (
    assignment_id BIGINT(20) NOT NULL AUTO_INCREMENT COMMENT '分配ID',
    user_id BIGINT(20) NOT NULL COMMENT '用户ID',
    user_name VARCHAR(50) COMMENT '用户姓名',
    vehicle_id BIGINT(20) NOT NULL COMMENT '车辆ID',
    vin_code VARCHAR(17) COMMENT 'VIN码',
    assignment_time DATETIME NOT NULL COMMENT '分配时间',
    planned_return_time DATETIME COMMENT '计划归还时间',
    actual_return_time DATETIME COMMENT '实际归还时间',
    status CHAR(1) DEFAULT '1' COMMENT '分配状态（1使用中 2已归还 3逾期 4异常）',
    start_mileage DECIMAL(10,2) DEFAULT 0.00 COMMENT '起始里程',
    end_mileage DECIMAL(10,2) DEFAULT 0.00 COMMENT '结束里程',
    operator_id BIGINT(20) COMMENT '操作人ID',
    operator_name VARCHAR(50) COMMENT '操作人姓名',
    remark VARCHAR(500) DEFAULT '' COMMENT '备注信息',
    create_by VARCHAR(64) DEFAULT '' COMMENT '创建者',
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    update_by VARCHAR(64) DEFAULT '' COMMENT '更新者',
    update_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (assignment_id),
    KEY idx_user_id (user_id),
    KEY idx_vehicle_id (vehicle_id),
    KEY idx_status (status),
    KEY idx_assignment_time (assignment_time)
) ENGINE=InnoDB AUTO_INCREMENT=1 COMMENT = '车辆分配记录表';

SELECT '✅ 车辆分配记录表创建完成' as message;

-- =====================================================
-- 【改动5】为新增字段创建性能索引
-- 目的：提升查询性能，支持高效的运营管理
-- 影响：只提升性能，对现有业务无影响
-- =====================================================

SELECT '🚀 正在创建性能优化索引...' as message;

-- 为dk_vehicle_bluetooth_keys表的新字段创建索引
SET @sql = (SELECT IF(
    (SELECT COUNT(*) FROM INFORMATION_SCHEMA.STATISTICS
     WHERE TABLE_SCHEMA = DATABASE()
     AND TABLE_NAME = 'dk_vehicle_bluetooth_keys'
     AND INDEX_NAME = 'idx_user_status') = 0,
    'CREATE INDEX idx_user_status ON dk_vehicle_bluetooth_keys(user_status)',
    'SELECT ''✅ idx_user_status 索引已存在'' as info'
));
PREPARE stmt FROM @sql; EXECUTE stmt; DEALLOCATE PREPARE stmt;

SET @sql = (SELECT IF(
    (SELECT COUNT(*) FROM INFORMATION_SCHEMA.STATISTICS
     WHERE TABLE_SCHEMA = DATABASE()
     AND TABLE_NAME = 'dk_vehicle_bluetooth_keys'
     AND INDEX_NAME = 'idx_status_update_time') = 0,
    'CREATE INDEX idx_status_update_time ON dk_vehicle_bluetooth_keys(status_update_time)',
    'SELECT ''✅ idx_status_update_time 索引已存在'' as info'
));
PREPARE stmt FROM @sql; EXECUTE stmt; DEALLOCATE PREPARE stmt;

SET @sql = (SELECT IF(
    (SELECT COUNT(*) FROM INFORMATION_SCHEMA.STATISTICS
     WHERE TABLE_SCHEMA = DATABASE()
     AND TABLE_NAME = 'dk_vehicle_bluetooth_keys'
     AND INDEX_NAME = 'idx_vehicle_id') = 0,
    'CREATE INDEX idx_vehicle_id ON dk_vehicle_bluetooth_keys(vehicle_id)',
    'SELECT ''✅ idx_vehicle_id 索引已存在'' as info'
));
PREPARE stmt FROM @sql; EXECUTE stmt; DEALLOCATE PREPARE stmt;

SET @sql = (SELECT IF(
    (SELECT COUNT(*) FROM INFORMATION_SCHEMA.STATISTICS
     WHERE TABLE_SCHEMA = DATABASE()
     AND TABLE_NAME = 'dk_vehicle_bluetooth_keys'
     AND INDEX_NAME = 'idx_assignment_id') = 0,
    'CREATE INDEX idx_assignment_id ON dk_vehicle_bluetooth_keys(assignment_id)',
    'SELECT ''✅ idx_assignment_id 索引已存在'' as info'
));
PREPARE stmt FROM @sql; EXECUTE stmt; DEALLOCATE PREPARE stmt;

SELECT '✅ 性能优化索引创建完成' as message;

-- =====================================================
-- 【改动6】数据字典说明
-- 目的：状态管理采用Java枚举类，无需数据库字典表
-- 影响：无数据库操作，通过代码枚举类管理
-- =====================================================

SELECT '📚 数据字典采用Java枚举类管理，无需数据库操作' as message;

-- =====================================================
-- 【改动7】插入示例数据（可选）
-- 目的：提供测试数据，便于功能验证
-- 影响：只新增测试数据，对现有业务无影响
-- =====================================================

SELECT '🎯 正在插入示例数据...' as message;

-- 插入车辆信息示例数据
INSERT IGNORE INTO dk_vehicle_info (vin_code, brand, model, color, license_plate, vehicle_type, status, location, fuel_level) VALUES
('LL3AGCJ55NA032336', '比亚迪', '秦PLUS DM-i', '白色', '京A12345', '混动轿车', '0', '停车场A区01号位', 85.5),
('5YJ3E1EA8KF123456', '特斯拉', 'Model 3', '黑色', '京B67890', '纯电轿车', '0', '停车场A区02号位', 92.0),
('LSGGH54U8GA123789', '广汽埃安', 'AION S', '蓝色', '京C11111', '纯电轿车', '0', '停车场B区01号位', 78.3),
('LVVDB11B8LD123456', '大众', '朗逸', '银色', '京D22222', '燃油轿车', '2', '维修车间', 45.0),
('LFV2A21K8G3123456', '福特', '福克斯', '红色', '京E33333', '燃油轿车', '0', '停车场B区02号位', 67.8);

SELECT '✅ 示例数据插入完成' as message;

-- =====================================================
-- 【升级完成】验证和总结
-- =====================================================

SELECT '🎉 数据库升级完成！正在进行最终验证...' as message;

-- 显示升级后的表结构信息
SELECT
    '📊 升级后数据库表统计' as info,
    (SELECT COUNT(*) FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_SCHEMA = DATABASE() AND TABLE_NAME LIKE 'dk_%') as '数字钥匙相关表数量',
    (SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_SCHEMA = DATABASE() AND TABLE_NAME = 'dk_vehicle_bluetooth_keys') as '钥匙管理表字段数量',
    (SELECT COUNT(*) FROM dk_vehicle_info) as '车辆信息记录数',
    (SELECT COUNT(*) FROM dk_vehicle_bluetooth_keys) as '现有钥匙记录数';

-- 验证现有数据完整性
SELECT '✅ 现有钥匙数据完整性验证' as info,
       COUNT(*) as '总记录数',
       SUM(CASE WHEN user_status = '0' THEN 1 ELSE 0 END) as '正常状态记录数',
       SUM(CASE WHEN user_status IS NULL THEN 1 ELSE 0 END) as '空状态记录数'
FROM dk_vehicle_bluetooth_keys;

-- 提交事务
COMMIT;

-- 恢复安全模式
SET FOREIGN_KEY_CHECKS = 1;
SET SQL_SAFE_UPDATES = 1;

-- =====================================================
-- 【升级总结】
-- =====================================================

SELECT '
🎉 数字钥匙运营系统升级完成！

📋 升级内容总结：
✅ 扩展现有钥匙管理表 - 新增6个字段支持运营管理
✅ 新增车辆信息管理表 - 统一管理车辆基础信息
✅ 新增用户申请管理表 - 支持申请审核流程
✅ 新增车辆分配记录表 - 跟踪分配状态
✅ 创建性能优化索引 - 提升查询效率
✅ 数据字典枚举类管理 - 代码层面管理状态
✅ 插入示例数据 - 便于功能测试

🔒 兼容性保证：
✅ 现有数据完全保留
✅ 现有功能继续工作
✅ 现有查询不受影响
✅ 可安全回滚

🚀 新增功能支持：
✅ 用户权限限制和恢复
✅ 车辆信息统一管理
✅ 申请审核完整流程
✅ 车辆分配状态跟踪
✅ 运营工作台数据支持

升级成功！系统已准备好支持完整的运营管理功能。
' as upgrade_summary;

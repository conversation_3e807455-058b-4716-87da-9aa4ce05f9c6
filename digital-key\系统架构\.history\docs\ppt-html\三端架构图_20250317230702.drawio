<?xml version="1.0" encoding="UTF-8"?>
<mxfile host="app.diagrams.net" agent="Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********" version="26.1.0">
  <diagram name="第 1 页" id="sT5CrcH_zo9G9MpETPcf">
    <mxGraphModel dx="2634" dy="1109" grid="1" gridSize="10" guides="1" tooltips="1" connect="1" arrows="1" fold="1" page="1" pageScale="1" pageWidth="827" pageHeight="1169" math="0" shadow="0">
      <root>
        <mxCell id="0" />
        <mxCell id="1" parent="0" />
        <mxCell id="10" value="数字钥匙系统安全架构图" style="text;html=1;strokeColor=none;fillColor=none;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=24;fontStyle=1" vertex="1" parent="1">
          <mxGeometry x="213.5" y="40" width="400" height="40" as="geometry" />
        </mxCell>
        
        <!-- 云平台层 -->
        <mxCell id="11" value="云平台层" style="swimlane;whiteSpace=wrap;html=1;fillColor=#dae8fc;strokeColor=#6c8ebf;" vertex="1" parent="1">
          <mxGeometry x="120" y="120" width="600" height="160" as="geometry" />
        </mxCell>
        <mxCell id="12" value="钥匙生命周期管理" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#d5e8d4;strokeColor=#82b366;" vertex="1" parent="11">
          <mxGeometry x="40" y="40" width="160" height="40" as="geometry" />
        </mxCell>
        <mxCell id="13" value="安全认证中心" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#d5e8d4;strokeColor=#82b366;" vertex="1" parent="11">
          <mxGeometry x="220" y="40" width="160" height="40" as="geometry" />
        </mxCell>
        <mxCell id="14" value="密钥管理系统" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#d5e8d4;strokeColor=#82b366;" vertex="1" parent="11">
          <mxGeometry x="400" y="40" width="160" height="40" as="geometry" />
        </mxCell>
        <mxCell id="15" value="时间服务器" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#d5e8d4;strokeColor=#82b366;" vertex="1" parent="11">
          <mxGeometry x="40" y="100" width="160" height="40" as="geometry" />
        </mxCell>
        <mxCell id="16" value="异常监控与处理" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#d5e8d4;strokeColor=#82b366;" vertex="1" parent="11">
          <mxGeometry x="220" y="100" width="160" height="40" as="geometry" />
        </mxCell>
        <mxCell id="17" value="统一接口服务" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#d5e8d4;strokeColor=#82b366;" vertex="1" parent="11">
          <mxGeometry x="400" y="100" width="160" height="40" as="geometry" />
        </mxCell>
        
        <!-- 云平台内部连接 -->
        <mxCell id="101" value="" style="endArrow=classic;startArrow=classic;html=1;rounded=0;exitX=1;exitY=0.5;exitDx=0;exitDy=0;entryX=0;entryY=0.5;entryDx=0;entryDy=0;" edge="1" parent="11" source="12" target="13">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="210" y="60" as="sourcePoint" />
            <mxPoint x="260" y="10" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="102" value="" style="endArrow=classic;startArrow=classic;html=1;rounded=0;exitX=1;exitY=0.5;exitDx=0;exitDy=0;entryX=0;entryY=0.5;entryDx=0;entryDy=0;" edge="1" parent="11" source="13" target="14">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="210" y="60" as="sourcePoint" />
            <mxPoint x="260" y="10" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="103" value="" style="endArrow=classic;startArrow=classic;html=1;rounded=0;exitX=0.5;exitY=1;exitDx=0;exitDy=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;" edge="1" parent="11" source="12" target="15">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="210" y="60" as="sourcePoint" />
            <mxPoint x="260" y="10" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="104" value="" style="endArrow=classic;startArrow=classic;html=1;rounded=0;exitX=0.5;exitY=1;exitDx=0;exitDy=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;" edge="1" parent="11" source="13" target="16">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="210" y="60" as="sourcePoint" />
            <mxPoint x="260" y="10" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="105" value="" style="endArrow=classic;startArrow=classic;html=1;rounded=0;exitX=0.5;exitY=1;exitDx=0;exitDy=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;" edge="1" parent="11" source="14" target="17">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="210" y="60" as="sourcePoint" />
            <mxPoint x="260" y="10" as="targetPoint" />
          </mxGeometry>
        </mxCell>

        <!-- 手机端层 -->
        <mxCell id="18" value="手机端层" style="swimlane;whiteSpace=wrap;html=1;fillColor=#fff2cc;strokeColor=#d6b656;" vertex="1" parent="1">
          <mxGeometry x="120" y="320" width="600" height="160" as="geometry" />
        </mxCell>
        <mxCell id="19" value="钥匙管理模块" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#ffe6cc;strokeColor=#d79b00;" vertex="1" parent="18">
          <mxGeometry x="40" y="40" width="160" height="40" as="geometry" />
        </mxCell>
        <mxCell id="20" value="安全存储模块" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#ffe6cc;strokeColor=#d79b00;" vertex="1" parent="18">
          <mxGeometry x="220" y="40" width="160" height="40" as="geometry" />
        </mxCell>
        <mxCell id="21" value="蓝牙通信模块" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#ffe6cc;strokeColor=#d79b00;" vertex="1" parent="18">
          <mxGeometry x="400" y="40" width="160" height="40" as="geometry" />
        </mxCell>
        <mxCell id="22" value="暗号交换模块" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#ffe6cc;strokeColor=#d79b00;" vertex="1" parent="18">
          <mxGeometry x="40" y="100" width="160" height="40" as="geometry" />
        </mxCell>
        <mxCell id="23" value="智能场景管理" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#ffe6cc;strokeColor=#d79b00;" vertex="1" parent="18">
          <mxGeometry x="220" y="100" width="160" height="40" as="geometry" />
        </mxCell>
        <mxCell id="24" value="异常处理模块" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#ffe6cc;strokeColor=#d79b00;" vertex="1" parent="18">
          <mxGeometry x="400" y="100" width="160" height="40" as="geometry" />
        </mxCell>
        
        <!-- 手机端内部连接 -->
        <mxCell id="106" value="" style="endArrow=classic;html=1;rounded=0;exitX=1;exitY=0.5;exitDx=0;exitDy=0;entryX=0;entryY=0.5;entryDx=0;entryDy=0;" edge="1" parent="18" source="19" target="20">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="210" y="60" as="sourcePoint" />
            <mxPoint x="260" y="10" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="107" value="" style="endArrow=classic;html=1;rounded=0;exitX=1;exitY=0.5;exitDx=0;exitDy=0;entryX=0;entryY=0.5;entryDx=0;entryDy=0;" edge="1" parent="18" source="20" target="21">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="210" y="60" as="sourcePoint" />
            <mxPoint x="260" y="10" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="108" value="" style="endArrow=classic;html=1;rounded=0;exitX=0.5;exitY=1;exitDx=0;exitDy=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;" edge="1" parent="18" source="19" target="22">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="210" y="60" as="sourcePoint" />
            <mxPoint x="260" y="10" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="109" value="" style="endArrow=classic;html=1;rounded=0;exitX=0.5;exitY=1;exitDx=0;exitDy=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;" edge="1" parent="18" source="20" target="23">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="210" y="60" as="sourcePoint" />
            <mxPoint x="260" y="10" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="110" value="" style="endArrow=classic;html=1;rounded=0;exitX=0.5;exitY=1;exitDx=0;exitDy=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;" edge="1" parent="18" source="21" target="24">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="210" y="60" as="sourcePoint" />
            <mxPoint x="260" y="10" as="targetPoint" />
          </mxGeometry>
        </mxCell>

        <!-- 车端层 -->
        <mxCell id="25" value="车端层" style="swimlane;whiteSpace=wrap;html=1;fillColor=#e1d5e7;strokeColor=#9673a6;" vertex="1" parent="1">
          <mxGeometry x="120" y="520" width="600" height="160" as="geometry" />
        </mxCell>
        <mxCell id="26" value="钥匙验证模块" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f8cecc;strokeColor=#b85450;" vertex="1" parent="25">
          <mxGeometry x="40" y="40" width="160" height="40" as="geometry" />
        </mxCell>
        <mxCell id="27" value="安全存储模块" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f8cecc;strokeColor=#b85450;" vertex="1" parent="25">
          <mxGeometry x="220" y="40" width="160" height="40" as="geometry" />
        </mxCell>
        <mxCell id="28" value="蓝牙通信模块" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f8cecc;strokeColor=#b85450;" vertex="1" parent="25">
          <mxGeometry x="400" y="40" width="160" height="40" as="geometry" />
        </mxCell>
        <mxCell id="29" value="暗号交换模块" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f8cecc;strokeColor=#b85450;" vertex="1" parent="25">
          <mxGeometry x="40" y="100" width="160" height="40" as="geometry" />
        </mxCell>
        <mxCell id="30" value="用户行为分析" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f8cecc;strokeColor=#b85450;" vertex="1" parent="25">
          <mxGeometry x="220" y="100" width="160" height="40" as="geometry" />
        </mxCell>
        <mxCell id="31" value="异常处理模块" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f8cecc;strokeColor=#b85450;" vertex="1" parent="25">
          <mxGeometry x="400" y="100" width="160" height="40" as="geometry" />
        </mxCell>

        <!-- 安全层 -->
        <mxCell id="32" value="安全层" style="swimlane;whiteSpace=wrap;html=1;fillColor=#f8cecc;strokeColor=#b85450;" vertex="1" parent="1">
          <mxGeometry x="120" y="720" width="600" height="120" as="geometry" />
        </mxCell>
        <mxCell id="33" value="端到端加密" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#e1d5e7;strokeColor=#9673a6;" vertex="1" parent="32">
          <mxGeometry x="40" y="40" width="160" height="40" as="geometry" />
        </mxCell>
        <mxCell id="34" value="安全芯片" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#e1d5e7;strokeColor=#9673a6;" vertex="1" parent="32">
          <mxGeometry x="220" y="40" width="160" height="40" as="geometry" />
        </mxCell>
        <mxCell id="35" value="生物识别" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#e1d5e7;strokeColor=#9673a6;" vertex="1" parent="32">
          <mxGeometry x="400" y="40" width="160" height="40" as="geometry" />
        </mxCell>

        <!-- 连接线 -->
        <mxCell id="36" value="1. 钥匙分发&#xa;2. 权限管理&#xa;3. 状态同步&#xa;4. 异常处理" style="endArrow=classic;startArrow=classic;html=1;rounded=0;exitX=0.5;exitY=0;exitDx=0;exitDy=0;entryX=0.5;entryY=1;entryDx=0;entryDy=0;align=center;verticalAlign=bottom;spacingTop=0;spacingLeft=0;spacingRight=0;spacingBottom=0;labelBackgroundColor=none;" edge="1" parent="1" source="18" target="11">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="390" y="590" as="sourcePoint" />
            <mxPoint x="440" y="540" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="37" value="1. 钥匙验证&#xa;2. 蓝牙配对&#xa;3. 暗号交换&#xa;4. 开锁指令" style="endArrow=classic;startArrow=classic;html=1;rounded=0;exitX=0.5;exitY=0;exitDx=0;exitDy=0;entryX=0.5;entryY=1;entryDx=0;entryDy=0;align=center;verticalAlign=bottom;spacingTop=0;spacingLeft=0;spacingRight=0;spacingBottom=0;labelBackgroundColor=none;" edge="1" parent="1" source="25" target="18">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="390" y="590" as="sourcePoint" />
            <mxPoint x="440" y="540" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="38" value="1. 安全认证&#xa;2. 加密通信&#xa;3. 防重放攻击&#xa;4. 完整性校验" style="endArrow=classic;startArrow=classic;html=1;rounded=0;exitX=0.5;exitY=0;exitDx=0;exitDy=0;entryX=0.5;entryY=1;entryDx=0;entryDy=0;align=center;verticalAlign=bottom;spacingTop=0;spacingLeft=0;spacingRight=0;spacingBottom=0;labelBackgroundColor=none;" edge="1" parent="1" source="32" target="25">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="390" y="590" as="sourcePoint" />
            <mxPoint x="440" y="540" as="targetPoint" />
          </mxGeometry>
        </mxCell>

        <!-- 添加业务场景说明 -->
        <mxCell id="39" value="业务场景：&#xa;1. 钥匙分发与激活&#xa;2. 远程开锁&#xa;3. 临时授权&#xa;4. 异常处理" style="text;html=1;strokeColor=none;fillColor=none;align=left;verticalAlign=top;spacingLeft=4;spacingRight=4;overflow=hidden;rotatable=0;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;" vertex="1" parent="1">
          <mxGeometry x="120" y="880" width="600" height="60" as="geometry" />
        </mxCell>
      </root>
    </mxGraphModel>
  </diagram>
</mxfile>
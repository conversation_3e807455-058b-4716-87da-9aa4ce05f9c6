<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>数字车钥匙分享功能</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
            font-family: 'PingFang SC', 'Microsoft YaHei', sans-serif;
        }
        
        body {
            background: linear-gradient(135deg, #1a2a6c, #b21f1f, #fdbb2d);
            display: flex;
            justify-content: center;
            align-items: center;
            min-height: 100vh;
            color: #333;
        }
        
        .slide {
            width: 1920px;
            height: 1080px;
            background-color: rgba(255, 255, 255, 0.95);
            border-radius: 12px;
            box-shadow: 0 25px 50px rgba(0, 0, 0, 0.3);
            padding: 50px;
            display: flex;
            flex-direction: column;
            overflow: hidden;
            position: relative;
        }
        
        .slide-header {
            text-align: center;
            margin-bottom: 60px;
        }
        
        .slide-header h1 {
            font-size: 64px;
            color: #2c3e50;
            margin-bottom: 20px;
            font-weight: 600;
        }
        
        .slide-header h3 {
            font-size: 32px;
            color: #7f8c8d;
            font-weight: 400;
        }
        
        .slide-content {
            display: flex;
            flex: 1;
            gap: 60px;
        }
        
        .section {
            flex: 1;
            background-color: rgba(255, 255, 255, 0.9);
            border-radius: 12px;
            padding: 40px;
            box-shadow: 0 10px 20px rgba(0, 0, 0, 0.1);
            display: flex;
            flex-direction: column;
        }
        
        .section-left {
            background-color: rgba(240, 247, 255, 0.8);
            border-left: 8px solid #3498db;
        }
        
        .section-right {
            background-color: rgba(255, 248, 240, 0.8);
            border-left: 8px solid #e67e22;
        }
        
        .section h2 {
            font-size: 42px;
            color: #2c3e50;
            margin-bottom: 40px;
            padding-bottom: 15px;
            border-bottom: 3px solid rgba(0, 0, 0, 0.1);
            position: relative;
        }
        
        .section h2::before {
            content: '';
            position: absolute;
            width: 80px;
            height: 5px;
            bottom: -3px;
            left: 0;
        }
        
        .section-left h2::before {
            background-color: #3498db;
        }
        
        .section-right h2::before {
            background-color: #e67e22;
        }
        
        .section-content {
            flex: 1;
        }
        
        .flow-steps, .security-points {
            list-style: none;
        }
        
        .flow-steps li, .security-points li {
            margin-bottom: 30px;
            padding-left: 50px;
            position: relative;
            line-height: 1.5;
            font-size: 28px;
        }
        
        .flow-steps li::before {
            content: attr(data-step);
            position: absolute;
            left: 0;
            top: 0;
            display: flex;
            justify-content: center;
            align-items: center;
            width: 36px;
            height: 36px;
            background-color: #3498db;
            color: white;
            border-radius: 50%;
            font-size: 20px;
            font-weight: bold;
        }
        
        .security-points li::before {
            content: "•";
            position: absolute;
            left: 15px;
            top: -3px;
            color: #e67e22;
            font-size: 40px;
            font-weight: bold;
        }
        
        .icon-container {
            position: absolute;
            bottom: 50px;
            right: 50px;
            display: flex;
            gap: 20px;
        }
        
        .icon {
            width: 100px;
            height: 100px;
            background-size: contain;
            background-repeat: no-repeat;
            background-position: center;
            opacity: 0.7;
        }
        
        .icon-car {
            width: 130px;
            height: 130px;
            background-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="%232c3e50"><path d="M5,11L6.5,6.5H17.5L19,11M17.5,16A1.5,1.5 0 0,1 16,14.5A1.5,1.5 0 0,1 17.5,13A1.5,1.5 0 0,1 19,14.5A1.5,1.5 0 0,1 17.5,16M6.5,16A1.5,1.5 0 0,1 5,14.5A1.5,1.5 0 0,1 6.5,13A1.5,1.5 0 0,1 8,14.5A1.5,1.5 0 0,1 6.5,16M18.92,6C18.72,5.42 18.16,5 17.5,5H6.5C5.84,5 5.28,5.42 5.08,6L3,12V20A1,1 0 0,0 4,21H5A1,1 0 0,0 6,20V19H18V20A1,1 0 0,0 19,21H20A1,1 0 0,0 21,20V12L18.92,6Z" /></svg>');
        }
        
        .icon-key {
            background-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="%23e67e22"><path d="M7,14A2,2 0 0,1 5,12A2,2 0 0,1 7,10A2,2 0 0,1 9,12A2,2 0 0,1 7,14M12.65,10C11.83,7.67 9.61,6 7,6A6,6 0 0,0 1,12A6,6 0 0,0 7,18C9.61,18 11.83,16.33 12.65,14H17V18H21V14H23V10H12.65Z" /></svg>');
        }
        
        .icon-security {
            background-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="%233498db"><path d="M12,1L3,5V11C3,16.55 6.84,21.74 12,23C17.16,21.74 21,16.55 21,11V5L12,1M12,5A3,3 0 0,1 15,8A3,3 0 0,1 12,11A3,3 0 0,1 9,8A3,3 0 0,1 12,5M17.13,17C15.92,18.85 14.11,20.24 12,20.92C9.89,20.24 8.08,18.85 6.87,17C6.53,16.5 6.24,16 6,15.47C6,13.82 8.71,12.47 12,12.47C15.29,12.47 18,13.79 18,15.47C17.76,16 17.47,16.5 17.13,17Z" /></svg>');
        }
    </style>
</head>
<body>
    <div class="slide">
        <div class="slide-header">
            <h1>数字车钥匙分享功能</h1>
            <h3>安全、便捷的智能车辆使用权限管理</h3>
        </div>
        
        <div class="slide-content">
            <div class="section section-left">
                <h2>用户操作流程</h2>
                <div class="section-content">
                    <ul class="flow-steps">
                        <li data-step="1">车主打开APP，点击"钥匙分享"</li>
                        <li data-step="2">设置权限和有效期</li>
                        <li data-step="3">生成分享二维码</li>
                        <li data-step="4">接收方打开APP，扫描二维码</li>
                        <li data-step="5">车主确认分享请求</li>
                        <li data-step="6">接收方获得数字钥匙</li>
                        <li data-step="7">接收方使用钥匙控制车辆</li>
                        <li data-step="8">车主可随时查看或撤销分享</li>
                    </ul>
                </div>
            </div>
            
            <div class="section section-right">
                <h2>安全设计要点</h2>
                <div class="section-content">
                    <ul class="security-points">
                        <li>分享前需身份二次验证</li>
                        <li>二维码加密且定时刷新</li>
                        <li>严格的权限隔离机制</li>
                        <li>完整的使用记录追踪</li>
                        <li>一键紧急撤销功能</li>
                        <li>离线状态下权限仍有效</li>
                    </ul>
                </div>
            </div>
        </div>
        
        <div class="icon-container">
            <div class="icon icon-car"></div>
            <div class="icon icon-key"></div>
            <div class="icon icon-security"></div>
        </div>
    </div>
</body>
</html> 
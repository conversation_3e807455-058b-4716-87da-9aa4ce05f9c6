@startuml
skinparam componentStyle rectangle

package "数字钥匙系统整体架构" {
    package "手机APP端" {
        component "用户认证模块" as UserAuth
        component "钥匙管理模块" as KeyManage
        component "车辆控制模块" as VehicleCtrl
        component "蓝牙通信模块" as BLEComm
        component "安全存储模块" as SecStorage
        component "时间同步模块" as TimeSync
    }
    
    package "车端系统" {
        component "BLE通信模块" as CarBLE
        component "钥匙验证模块" as KeyVerify
        component "指令执行模块" as CmdExec
        component "安全存储模块" as CarSecStorage
        component "T-Box通信模块" as TBoxComm
        component "时间同步模块" as CarTimeSync
    }
    
    package "钥匙云平台" {
        package "钥匙核心服务" {
            component "钥匙生命周期管理" as KeyLifecycle
            component "用户管理系统" as UserMgmt
            component "车辆管理系统" as VehicleMgmt
            component "安全认证中心" as AuthCenter
            component "密钥管理系统" as KeyMgmt
        }
        
        package "基础服务" {
            component "审计日志系统" as AuditLog
            component "时间服务器" as TimeServer
            component "API网关" as APIGateway
        }
        
        package "运维/运营集群" {
            component "监控系统" as Monitor
            component "日志系统" as LogSystem
            component "配置管理系统" as ConfigMgmt
            component "发布系统" as ReleaseSystem
        }
    }
    
    database "核心数据库" {
        database "用户表" as UsersDB
        database "车辆表" as VehiclesDB
        database "钥匙表" as KeysDB
        database "钥匙分享记录表" as KeySharingDB
        database "操作日志表" as LogsDB
    }
    
    database "安全基础设施" {
        database "密钥材料表" as KeyMaterialsDB
        database "密码机/HSM" as HSM
        database "PKI/CA系统" as PKICA
    }
}

' APP端内部关系
UserAuth --> KeyManage
KeyManage --> VehicleCtrl
VehicleCtrl --> BLEComm
BLEComm --> SecStorage
SecStorage --> TimeSync

' 车端内部关系
CarBLE --> KeyVerify
KeyVerify --> CmdExec
CmdExec --> CarSecStorage
CarSecStorage --> TBoxComm
TBoxComm --> CarTimeSync

' 云平台内部关系
KeyLifecycle --> UserMgmt
UserMgmt --> VehicleMgmt
VehicleMgmt --> AuthCenter
AuthCenter --> KeyMgmt

AuditLog --> TimeServer
TimeServer --> APIGateway

Monitor --> LogSystem
LogSystem --> ConfigMgmt
ConfigMgmt --> ReleaseSystem

' 跨系统关系
BLEComm <--> CarBLE : 蓝牙通信
APIGateway <--> TBoxComm : 远程通信
APIGateway <--> UserAuth : HTTPS通信
TimeServer --> TimeSync : 时间同步
TimeServer --> CarTimeSync : 时间同步

' 数据库关系
KeyLifecycle --> UsersDB
KeyLifecycle --> VehiclesDB
KeyLifecycle --> KeysDB
KeyLifecycle --> KeySharingDB
AuditLog --> LogsDB

AuthCenter --> KeyMaterialsDB
KeyMgmt --> HSM
AuthCenter --> PKICA

@enduml
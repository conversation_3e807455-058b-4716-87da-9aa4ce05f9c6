<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>数字钥匙系统技术方案</title>
    <!-- 添加jsPlumb库 -->
    <script src="https://cdn.jsdelivr.net/npm/jsplumb@2.15.6/dist/js/jsplumb.min.js"></script>
    <style>
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            line-height: 1.6;
            color: #333;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f9f9f9;
        }
        h1, h2, h3, h4 {
            color: #0066cc;
            margin-top: 30px;
        }
        h1 {
            text-align: center;
            padding: 20px 0;
            background-color: #0066cc;
            color: white;
            border-radius: 8px;
            margin-top: 0;
        }
        
        /* 架构图样式 */
        .architecture-overview {
            margin: 30px 0;
            background-color: white;
            border-radius: 10px;
            padding: 20px;
            box-shadow: 0 4px 12px rgba(0,0,0,0.1);
        }
        .architecture-overview h2 {
            text-align: center;
            margin-bottom: 25px;
            color: #0066cc;
            border-bottom: 2px solid #e0e0e0;
            padding-bottom: 10px;
        }
        .architecture-diagram-container {
            position: relative;
            margin: 20px 0;
            min-height: 600px;
        }
        
        /* 组件卡片样式 */
        .component-container {
            display: flex;
            flex-wrap: wrap;
            justify-content: space-around;
            gap: 20px;
            margin: 30px 0;
        }
        .component-card {
            background-color: white;
            border-radius: 10px;
            box-shadow: 0 4px 8px rgba(0,0,0,0.1);
            width: 300px;
            transition: transform 0.3s, box-shadow 0.3s;
            overflow: hidden;
        }
        .component-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 8px 16px rgba(0,0,0,0.15);
        }
        .component-header {
            background-color: #0066cc;
            color: white;
            padding: 15px;
            display: flex;
            align-items: center;
        }
        .component-icon {
            font-size: 24px;
            margin-right: 10px;
        }
        .component-title {
            font-size: 18px;
            font-weight: bold;
        }
        .component-body {
            padding: 15px;
        }
        .key-features {
            display: flex;
            flex-wrap: wrap;
            margin-top: 10px;
        }
        .feature {
            display: flex;
            align-items: center;
            margin: 5px 0;
            width: 50%;
        }
        .feature-icon {
            margin-right: 8px;
            color: #0066cc;
        }
        
        /* 架构模块样式 */
        .architecture-container {
            display: flex;
            flex-wrap: wrap;
            gap: 20px;
            margin: 30px 0;
        }
        .architecture-module {
            background-color: white;
            border-radius: 10px;
            box-shadow: 0 4px 8px rgba(0,0,0,0.1);
            flex: 1;
            min-width: 300px;
            transition: transform 0.3s;
        }
        .architecture-module:hover {
            transform: translateY(-5px);
        }
        .module-header {
            background-color: #0066cc;
            color: white;
            padding: 15px;
            border-radius: 10px 10px 0 0;
            font-size: 18px;
            font-weight: bold;
            display: flex;
            align-items: center;
        }
        .module-icon {
            margin-right: 10px;
            font-size: 24px;
        }
        .module-body {
            padding: 15px;
        }
        .module-list {
            list-style-type: none;
            padding: 0;
            margin: 0;
        }
        .module-list-item {
            margin: 10px 0;
            padding: 10px;
            border-radius: 5px;
            background-color: #f5f9ff;
            display: flex;
            align-items: flex-start;
        }
        .module-list-icon {
            margin-right: 10px;
        }
        
        /* 流程图样式 */
        .flow-diagram {
            margin: 30px 0;
            background-color: white;
            border-radius: 10px;
            padding: 20px;
            box-shadow: 0 4px 12px rgba(0,0,0,0.1);
        }
        .flow-diagram h3 {
            text-align: center;
            margin-bottom: 25px;
            color: #0066cc;
            border-bottom: 2px solid #e0e0e0;
            padding-bottom: 10px;
        }
        .flow-container {
            position: relative;
            margin: 20px 0;
            min-height: 500px;
        }
        .flow-module {
            position: absolute;
            width: 150px;
            height: 80px;
            background-color: white;
            border: 2px solid #0066cc;
            border-radius: 10px;
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            text-align: center;
            padding: 10px;
            box-shadow: 0 4px 8px rgba(0,0,0,0.1);
            transition: transform 0.3s, box-shadow 0.3s;
            cursor: pointer;
        }
        .flow-module:hover {
            transform: scale(1.05);
            box-shadow: 0 8px 16px rgba(0,0,0,0.15);
        }
        .flow-icon {
            font-size: 24px;
            margin-bottom: 5px;
        }
        .flow-text {
            font-weight: bold;
        }
        .flow-arrow {
            position: absolute;
            background-color: #0066cc;
            height: 2px;
        }
        .right-arrow {
            transform: rotate(0deg);
        }
        .left-arrow {
            transform: rotate(180deg);
        }
        .diagonal-arrow {
            transform: rotate(45deg);
        }
        
        /* 系统图表样式 */
        .system-diagram {
            margin: 30px 0;
            background-color: white;
            border-radius: 10px;
            padding: 20px;
            box-shadow: 0 4px 12px rgba(0,0,0,0.1);
        }
        .system-diagram h3 {
            text-align: center;
            margin-bottom: 25px;
            color: #0066cc;
            border-bottom: 2px solid #e0e0e0;
            padding-bottom: 10px;
        }
        .diagram-container {
            position: relative;
            margin: 20px 0;
            min-height: 500px;
        }
        .diagram-module {
            position: absolute;
            width: 150px;
            height: 100px;
            background-color: white;
            border: 2px solid #0066cc;
            border-radius: 10px;
            display: flex;
            flex-direction: column;
            text-align: center;
            box-shadow: 0 4px 8px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        .diagram-header {
            background-color: #0066cc;
            color: white;
            padding: 5px;
            font-weight: bold;
        }
        .diagram-body {
            flex: 1;
            display: flex;
            justify-content: center;
            align-items: center;
            padding: 10px;
        }
        .diagram-icon {
            font-size: 32px;
        }
        .diagram-connections {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            z-index: -1;
        }
        .connection-text {
            font-size: 12px;
            font-weight: bold;
        }
        .diagram-legend {
            margin-top: 30px;
            background-color: #f5f9ff;
            border-radius: 10px;
            padding: 15px;
        }
        .legend-title {
            font-weight: bold;
            margin-bottom: 10px;
            color: #0066cc;
        }
        .legend-items {
            display: flex;
            flex-wrap: wrap;
        }
        .legend-item {
            display: flex;
            align-items: center;
            margin: 5px 15px 5px 0;
        }
        .legend-color {
            width: 20px;
            height: 3px;
            background-color: #0066cc;
            margin-right: 8px;
        }
        .legend-icon {
            margin-right: 8px;
        }
        
        /* 折叠面板样式 */
        .collapsible {
            background-color: #0066cc;
            color: white;
            cursor: pointer;
            padding: 18px;
            width: 100%;
            border: none;
            text-align: left;
            outline: none;
            font-size: 16px;
            border-radius: 8px;
            margin-top: 20px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        .collapsible:after {
            content: '\002B';
            font-weight: bold;
            float: right;
            margin-left: 5px;
        }
        .active:after {
            content: "\2212";
        }
        .collapsible-content {
            max-height: 0;
            overflow: hidden;
            transition: max-height 0.2s ease-out;
            background-color: white;
            border-radius: 0 0 8px 8px;
            margin-bottom: 20px;
        }
        
        /* 表格样式 */
        table {
            width: 100%;
            border-collapse: collapse;
            margin: 20px 0;
            background-color: white;
            box-shadow: 0 4px 8px rgba(0,0,0,0.1);
        }
        th, td {
            padding: 12px 15px;
            text-align: left;
            border-bottom: 1px solid #e0e0e0;
        }
        th {
            background-color: #0066cc;
            color: white;
        }
        tr:hover {
            background-color: #f5f9ff;
        }
        
        /* 响应式设计 */
        @media (max-width: 768px) {
            .component-container, .architecture-container {
                flex-direction: column;
            }
            .component-card, .architecture-module {
                width: 100%;
            }
            .flow-module, .diagram-module {
                position: relative;
                margin: 10px auto;
                left: auto !important;
                top: auto !important;
            }
            .flow-arrow, .diagram-connections {
                display: none;
            }
        }
    </style>
</head>
<body>
    <h1>数字钥匙系统技术方案</h1>
</body>
</html> 
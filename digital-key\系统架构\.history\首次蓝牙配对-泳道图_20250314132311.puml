@startuml
skinparam monochrome true

participant "APP" as Mobile
participant "车端（BLE & TBOX）" as CarBT
participant "钥匙云平台" as Cloud
participant "TSP/OEM平台" as ExternalSystem

== 蓝牙配对流程 ==

Mobile -> Mobile : 1. 在APP中选择添加数字钥匙
Mobile -> Mobile : 2. 扫描车辆二维码或输入VIN码识别车辆
Mobile -> Cloud : 3. 发送车辆VIN码，请求获取数字钥匙
Cloud -> Cloud : 4. 根据VIN码生成虚拟密钥和唯一配对令牌
Cloud -> Mobile : 5. 下发虚拟密钥和配对令牌到手机
Mobile -> Mobile : 6. 保存虚拟密钥和配对令牌到安全区域

note over Cloud, ExternalSystem
  钥匙云平台通过安全通道与TSP/OEM平台交互
  获取车辆信息并建立安全连接
end note

Cloud -> ExternalSystem : 7. 查询VIN码对应的车辆信息
ExternalSystem -> Cloud : 8. 返回车辆信息
Cloud -> CarBT : 9. 通过4G连接到车辆并下发根密钥

note over Cloud, CarBT
  根密钥相当于主钥匙，是后续所有安全通信的基础
end note

CarBT -> CarBT : 10. 存储根密钥和配对令牌
CarBT -> Mobile : 11. 发送蓝牙广播信号（包含车辆标识信息“加密后的VIN”）

note over Mobile, CarBT
  只有持有正确虚拟密钥的手机才能识别并连接到对应车辆
  就像只有知道暗号的人才能找到正确的门
end note

Mobile -> Mobile : 12a. 识别广播中的车辆信息
Mobile -> CarBT : 12b. 连接到对应车辆的蓝牙
Mobile -> CarBT : 13. 发送配对请求（包含密钥和配对令牌）
CarBT -> CarBT : 14a. 验证手机身份
CarBT -> Mobile : 14b. 发起安全验证请求

CarBT -> CarBT : 15. 生成配对码
Mobile -> Mobile : 16. 生成配对码
Mobile -> CarBT : 17. 发送配对码进行双向验证
CarBT -> CarBT : 18a. 验证配对码
CarBT -> Mobile : 18b. 返回验证结果

note over Mobile, CarBT
  双向验证确保:
  1. 手机确实是授权的手机
  2. 车辆确实是正确的车辆
  就像双方互相确认对方的身份
end note

== 安全通道建立 ==

Mobile -> Mobile : 19a. 准备创建临时暗号材料
Mobile -> CarBT : 19b. 发送部分暗号材料
CarBT -> CarBT : 20a. 准备创建临时暗号材料
CarBT -> Mobile : 20b. 发送部分暗号材料

note over Mobile, CarBT
  手机和车辆通过特殊方式各自创建暗号:
  1. 双方各自有一个秘密
  2. 交换一些公开信息
  3. 用自己的秘密和对方的公开信息计算出相同的结果
  4. 这个结果就是它们共同的暗号(会话密钥)
  就像两个人不用告诉对方自己的秘密，却能算出同一个数字
end note

Mobile -> Mobile : 21. 计算出共同暗号(会话密钥)
CarBT -> CarBT : 22. 计算出共同暗号(会话密钥)

note over Mobile, CarBT
  会话密钥特点:
  1. 是临时的，定期更换
  2. 存储在特殊安全区域
  3. 只有配对的手机和车辆知道
  就像每天更换的通行密码
end note

CarBT -> CarBT : 23. 存储以下信息到安全区域
note right of CarBT
  - 会话密钥（临时暗号）
  - 绑定的手机信息
  - 密钥有效期
  - 授权等级
end note

Mobile -> Mobile : 24. 存储以下信息到手机安全区域
note right of Mobile
  - 会话密钥（临时暗号）
  - 车辆信息
  - 蓝牙连接信息
  - 密钥有效期
  - 权限等级
end note

CarBT <-> Mobile : 25. 建立加密安全通道

note over Mobile, CarBT
  安全通道特点:
  1. 所有消息都经过加密，外人无法读取
  2. 每条消息都有标记，防止被重复使用
  3. 能检测消息是否被篡改
  就像一条只有你和车能使用的秘密通道
end note

Mobile -> Cloud : 26. 上报配对成功
Cloud -> Cloud : 27. 记录钥匙绑定关系
note right of Cloud
  - 用户与车辆的绑定关系
  - 设备与钥匙的绑定关系
  - 钥匙权限等级
  - 有效期限
end note

Cloud -> Mobile : 28. 返回数字钥匙信息和权限配置
Mobile -> Mobile : 29. 显示数字钥匙添加成功

== 异常处理 ==

note over Mobile, CarBT
  配对过程中可能出现的问题及处理方式:
end note

alt 配对超时
    Mobile -> Mobile : 检测到配对请求长时间无响应
    Mobile -> Mobile : 显示"配对超时，请重试"
    Mobile -> Cloud : 上报配对失败
else 验证失败
    CarBT -> Mobile : 返回验证失败信息
    Mobile -> Mobile : 显示"验证失败，请确认车辆状态"
    Mobile -> Cloud : 上报配对失败
else 暗号生成失败
    Mobile -> Mobile : 检测到密钥交换异常
    Mobile -> Mobile : 显示"连接异常，请重试"
    Mobile -> Cloud : 上报配对失败
end

note over Cloud, CarBT
  配对完成后各方保存的重要信息:
  1. 手机端: 临时暗号、车辆信息、权限配置
  2. 车端: 临时暗号、授权手机信息、权限等级
  3. 云平台: 用户-车辆-设备的关系记录
  
  这些信息用于:
  - 后续蓝牙连接验证
  - 功能权限控制
  - 远程管理
end note

note over Mobile, CarBT
  安全保障措施:
  1. 临时暗号定期更换
  2. 密钥存储在特殊安全区域
  3. 异常情况自动处理
  就像定期更换密码并存放在保险箱中
end note

@enduml
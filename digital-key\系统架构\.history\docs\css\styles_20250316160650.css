/* 调整整体背景和配色 */
body {
    /* 原来可能是深蓝色背景 */
    background-color: #f5f7fa;
    color: #2c3e50;
}

.header-container {
    /* 将标题区域改为渐变淡蓝色，保持科技感 */
    background: linear-gradient(135deg, #e0e8ff 0%, #c7d9ff 100%);
    padding: 3rem 0;
    border-bottom: 1px solid #eaeef5;
}

h1, h2, h3 {
    color: #3a539b;
    font-weight: 500;
}

/* 内容区域背景 */
.content-container {
    background-color: white;
    box-shadow: 0 0 15px rgba(0, 0, 0, 0.03);
    border-radius: 8px;
    margin: 2rem auto;
    padding: 2rem;
} 
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>钥匙云平台</title>
    <!-- 引入html2canvas库 -->
    <script src="https://html2canvas.hertzen.com/dist/html2canvas.min.js"></script>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
            font-family: 'PingFang SC', 'Microsoft YaHei', sans-serif;
        }
        
        body {
            background: #f0f0f0;
            display: flex;
            justify-content: center;
            align-items: center;
            min-height: 100vh;
            color: #333;
            padding: 20px;
        }
        
        /* PPT边框指示器 */
        .ppt-container {
            position: relative;
            width: 1920px; /* 标准PPT宽度 */
            height: 1080px; /* 标准PPT高度 */
            box-shadow: 0 0 30px rgba(0, 0, 0, 0.2);
            border: 2px dashed #ccc;
            background-color: white; /* 纯白色背景 */
        }
        
        .ppt-label {
            position: absolute;
            top: -40px;
            left: 0;
            background-color: #555;
            color: white;
            padding: 5px 15px;
            border-radius: 4px;
            font-size: 16px;
        }
        
        /* 复制按钮 */
        .copy-button {
            position: absolute;
            top: 20px;
            right: 20px;
            background: linear-gradient(135deg, #3498db, #2980b9);
            color: white;
            border: none;
            padding: 12px 25px;
            border-radius: 30px;
            font-size: 18px;
            cursor: pointer;
            z-index: 1000;
            box-shadow: 0 4px 10px rgba(0, 0, 0, 0.2);
            display: flex;
            align-items: center;
            gap: 10px;
            transition: all 0.3s ease;
        }
        
        /* 下载按钮 */
        .download-button {
            position: absolute;
            top: 20px;
            right: 230px;
            background: linear-gradient(135deg, #2ecc71, #27ae60);
            color: white;
            border: none;
            padding: 12px 25px;
            border-radius: 30px;
            font-size: 18px;
            cursor: pointer;
            z-index: 1000;
            box-shadow: 0 4px 10px rgba(0, 0, 0, 0.2);
            display: flex;
            align-items: center;
            gap: 10px;
            transition: all 0.3s ease;
        }
        
        .copy-button:hover, .download-button:hover {
            transform: translateY(-3px);
            box-shadow: 0 6px 15px rgba(0, 0, 0, 0.25);
        }
        
        .copy-button:active, .download-button:active {
            transform: translateY(0);
        }
        
        .copy-icon {
            width: 20px;
            height: 20px;
            background-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="white"><path d="M19,21H8V7H19M19,5H8A2,2 0 0,0 6,7V21A2,2 0 0,0 8,23H19A2,2 0 0,0 21,21V7A2,2 0 0,0 19,5M16,1H4A2,2 0 0,0 2,3V17H4V3H16V1Z" /></svg>');
            background-size: contain;
            background-repeat: no-repeat;
        }
        
        .download-icon {
            width: 20px;
            height: 20px;
            background-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="white"><path d="M5,20H19V18H5M19,9H15V3H9V9H5L12,16L19,9Z" /></svg>');
            background-size: contain;
            background-repeat: no-repeat;
        }
        
        /* 清晰度选择器样式 */
        .resolution-selector {
            position: absolute;
            top: 70px;
            right: 230px;
            display: flex;
            align-items: center;
            background: rgba(255, 255, 255, 0.9);
            padding: 8px 15px;
            border-radius: 30px;
            box-shadow: 0 4px 10px rgba(0, 0, 0, 0.1);
            z-index: 1000;
        }
        
        .resolution-label {
            margin-right: 10px;
            font-size: 14px;
            color: #555;
        }
        
        .resolution-btn {
            border: none;
            background: #f0f0f0;
            padding: 5px 10px;
            margin: 0 3px;
            border-radius: 15px;
            font-size: 14px;
            cursor: pointer;
            transition: all 0.2s ease;
            color: #555;
        }
        
        .resolution-btn:hover {
            background: #e0e0e0;
        }
        
        .resolution-btn.active {
            background: linear-gradient(135deg, #3498db, #2980b9);
            color: white;
        }
        
        /* 滑动内容区 */
        .slide {
            width: 100%;
            height: 100%;
            background-color: transparent;
            padding: 60px;
            position: relative;
        }
        
        /* 标题样式 */
        .header {
            text-align: center;
            margin-bottom: 40px;
        }
        
        .header h1 {
            font-size: 48px;
            color: #2c3e50;
            margin-bottom: 15px;
            font-weight: 700;
        }
        
        .header .subtitle {
            font-size: 24px;
            color: #7f8c8d;
            font-weight: 400;
        }
        
        /* 架构图容器 */
        .architecture-container {
            width: 100%;
            height: 800px;
            position: relative;
        }
        
        /* 三端样式 */
        .platform {
            position: absolute;
            border-radius: 10px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
            padding: 20px;
            transition: all 0.3s ease;
        }
        
        .platform:hover {
            box-shadow: 0 8px 25px rgba(0,0,0,0.15);
            transform: translateY(-5px);
        }
        
        .platform-title {
            font-size: 24px;
            font-weight: 600;
            margin-bottom: 15px;
            text-align: center;
            padding-bottom: 10px;
            border-bottom: 1px solid rgba(0,0,0,0.1);
        }
        
        /* 云平台样式 */
        .cloud-platform {
            width: 90%;
            height: 180px;
            left: 5%;
            top: 0;
            background: linear-gradient(to right, #E3F2FD, #BBDEFB);
            border: 2px solid #2196F3;
        }
        
        .cloud-platform .platform-title {
            color: #1565C0;
        }
        
        /* 手机端样式 */
        .mobile-platform {
            width: 33%;
            height: 480px;
            left: 33.5%;
            top: 240px;
            background: linear-gradient(to bottom, #E8F5E9, #C8E6C9);
            border: 2px solid #4CAF50;
        }
        
        .mobile-platform .platform-title {
            color: #2E7D32;
        }
        
        /* 车端样式 */
        .car-platform {
            width: 90%;
            height: 180px;
            left: 5%;
            bottom: 0;
            background: linear-gradient(to left, #FFEBEE, #FFCDD2);
            border: 2px solid #F44336;
        }
        
        .car-platform .platform-title {
            color: #C62828;
        }
        
        /* 模块样式 */
        .module {
            position: absolute;
            border-radius: 8px;
            padding: 12px 15px;
            font-size: 16px;
            font-weight: 500;
            box-shadow: 0 3px 8px rgba(0,0,0,0.1);
            text-align: center;
            transition: all 0.3s ease;
        }
        
        .module:hover {
            transform: scale(1.05);
            box-shadow: 0 5px 15px rgba(0,0,0,0.2);
            z-index: 10;
        }
        
        /* 云平台模块 */
        .cloud-module-1 {
            width: 170px;
            height: 70px;
            left: 100px;
            top: 60px;
            background-color: #2196F3;
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        
        .cloud-module-2 {
            width: 170px;
            height: 70px;
            left: 300px;
            top: 60px;
            background-color: #42A5F5;
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        
        .cloud-module-3 {
            width: 170px;
            height: 70px;
            left: 500px;
            top: 60px;
            background-color: #64B5F6;
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        
        .cloud-module-4 {
            width: 170px;
            height: 70px;
            left: 700px;
            top: 60px;
            background-color: #90CAF9;
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        
        .cloud-module-5 {
            width: 170px;
            height: 70px;
            left: 900px;
            top: 60px;
            background-color: #BBDEFB;
            color: #1565C0;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        
        .cloud-module-6 {
            width: 170px;
            height: 70px;
            left: 1100px;
            top: 60px;
            background-color: #E3F2FD;
            color: #1565C0;
            border: 1px solid #2196F3;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        
        /* 手机端模块 */
        .mobile-module-1 {
            width: 200px;
            height: 70px;
            left: 73px;
            top: 70px;
            background-color: #4CAF50;
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        
        .mobile-module-2 {
            width: 200px;
            height: 70px;
            left: 73px;
            top: 160px;
            background-color: #66BB6A;
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        
        .mobile-module-3 {
            width: 200px;
            height: 70px;
            left: 73px;
            top: 250px;
            background-color: #81C784;
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        
        .mobile-module-4 {
            width: 200px;
            height: 70px;
            left: 73px;
            top: 340px;
            background-color: #A5D6A7;
            color: #2E7D32;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        
        /* 车端模块 */
        .car-module-1 {
            width: 170px;
            height: 70px;
            left: 100px;
            top: 60px;
            background-color: #F44336;
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        
        .car-module-2 {
            width: 170px;
            height: 70px;
            left: 300px;
            top: 60px;
            background-color: #EF5350;
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        
        .car-module-3 {
            width: 170px;
            height: 70px;
            left: 500px;
            top: 60px;
            background-color: #E57373;
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        
        .car-module-4 {
            width: 170px;
            height: 70px;
            left: 700px;
            top: 60px;
            background-color: #EF9A9A;
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        
        .car-module-5 {
            width: 170px;
            height: 70px;
            left: 900px;
            top: 60px;
            background-color: #FFCDD2;
            color: #C62828;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        
        .car-module-6 {
            width: 170px;
            height: 70px;
            left: 1100px;
            top: 60px;
            background-color: #FFEBEE;
            color: #C62828;
            border: 1px solid #F44336;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        
        /* 连接线 */
        .connector {
            position: absolute;
            z-index: 1;
            pointer-events: none;
        }
        
        /* 标签样式 */
        .label {
            position: absolute;
            background-color: white;
            border-radius: 15px;
            padding: 5px 15px;
            font-size: 14px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            z-index: 2;
        }
        
        .cloud-to-mobile-label {
            top: 200px;
            left: 640px;
            color: #1565C0;
            border: 1px solid #90CAF9;
        }
        
        .mobile-to-car-label {
            top: 480px;
            left: 640px;
            color: #2E7D32;
            border: 1px solid #A5D6A7;
        }
        
        .cloud-to-car-label {
            top: 340px;
            left: 200px;
            color: #C62828;
            border: 1px solid #FFCDD2;
            transform: rotate(-45deg);
        }
        
        /* 底部说明 */
        .footer {
            position: absolute;
            bottom: 20px;
            width: 100%;
            text-align: center;
            font-size: 16px;
            color: #95a5a6;
            left: 0;
        }
        
        /* 消息提示 */
        .toast {
            position: fixed;
            top: 50px;
            left: 50%;
            transform: translateX(-50%);
            background-color: rgba(0, 0, 0, 0.8);
            color: white;
            padding: 12px 25px;
            border-radius: 30px;
            font-size: 16px;
            z-index: 2000;
            display: none;
            animation: fadeInOut 2s ease;
        }
        
        @keyframes fadeInOut {
            0% { opacity: 0; }
            20% { opacity: 1; }
            80% { opacity: 1; }
            100% { opacity: 0; }
        }
    </style>
</head>
<body>
    <div class="ppt-container" id="pptContainer">
        <div class="ppt-label">PPT尺寸(1920×1080) - 透明背景</div>
        <button class="copy-button" id="copyButton">
            <span class="copy-icon"></span>复制为PPT图片
        </button>
        <button class="download-button" id="downloadButton">
            <span class="download-icon"></span>下载透明PNG
        </button>
        
        <!-- 添加清晰度选择按钮组 -->
        <div class="resolution-selector" id="resolutionSelector">
            <span class="resolution-label">清晰度:</span>
            <button class="resolution-btn" data-scale="1">标准</button>
            <button class="resolution-btn active" data-scale="2">高清</button>
            <button class="resolution-btn" data-scale="3">超清</button>
        </div>
        
        <div class="slide" id="slide">
            <!-- 顶部标题 -->
            <div class="header">
                <h1>数字钥匙三端架构图</h1>
                <div class="subtitle">车辆数字钥匙系统技术架构与通信流程</div>
            </div>
            
            <!-- 架构图主体 -->
            <div class="architecture-container">
                <!-- 云平台 -->
                <div class="platform cloud-platform">
                    <div class="platform-title">钥匙云平台</div>
                    <div class="module cloud-module-1">钥匙生命周期管理</div>
                    <div class="module cloud-module-2">安全认证中心</div>
                    <div class="module cloud-module-3">密钥管理系统</div>
                    <div class="module cloud-module-4">统一接口服务</div>
                    <div class="module cloud-module-5">安全通信通道</div>
                    <div class="module cloud-module-6">时间同步服务</div>
                </div>
                
                <!-- 手机端 -->
                <div class="platform mobile-platform">
                    <div class="platform-title">手机端</div>
                    <div class="module mobile-module-1">钥匙与车辆管理</div>
                    <div class="module mobile-module-2">蓝牙通信模块</div>
                    <div class="module mobile-module-3">安全存储模块</div>
                    <div class="module mobile-module-4">标定与算法模块</div>
                </div>
                
                <!-- 车端 -->
                <div class="platform car-platform">
                    <div class="platform-title">车端</div>
                    <div class="module car-module-1">钥匙验证模块</div>
                    <div class="module car-module-2">蓝牙通信模块</div>
                    <div class="module car-module-3">指令执行模块</div>
                    <div class="module car-module-4">暗号交换模块</div>
                    <div class="module car-module-5">安全存储模块</div>
                    <div class="module car-module-6">标定配置模块</div>
                </div>
                
                <!-- 连接线 - 使用SVG绘制 -->
                <svg class="connector" width="100%" height="100%" viewBox="0 0 1920 800" xmlns="http://www.w3.org/2000/svg">
                    <!-- 云平台到手机端的箭头 -->
                    <defs>
                        <marker id="arrowBlue" viewBox="0 0 10 10" refX="5" refY="5" 
                                markerWidth="6" markerHeight="6" orient="auto-start-reverse">
                            <path d="M 0 0 L 10 5 L 0 10 z" fill="#2196F3"/>
                        </marker>
                        <marker id="arrowGreen" viewBox="0 0 10 10" refX="5" refY="5" 
                                markerWidth="6" markerHeight="6" orient="auto-start-reverse">
                            <path d="M 0 0 L 10 5 L 0 10 z" fill="#4CAF50"/>
                        </marker>
                        <marker id="arrowRed" viewBox="0 0 10 10" refX="5" refY="5" 
                                markerWidth="6" markerHeight="6" orient="auto-start-reverse">
                            <path d="M 0 0 L 10 5 L 0 10 z" fill="#F44336"/>
                        </marker>
                    </defs>
                    
                    <!-- 云平台到手机端 -->
                    <path d="M 640 180 L 640 240" stroke="#2196F3" stroke-width="3" fill="none" marker-end="url(#arrowBlue)" stroke-dasharray="0"/>
                    <path d="M 680 240 L 680 180" stroke="#4CAF50" stroke-width="3" fill="none" marker-end="url(#arrowGreen)" stroke-dasharray="0"/>
                    
                    <!-- 手机端到车端 -->
                    <path d="M 640 720 L 640 780" stroke="#4CAF50" stroke-width="3" fill="none" marker-end="url(#arrowGreen)" stroke-dasharray="0"/>
                    <path d="M 680 780 L 680 720" stroke="#F44336" stroke-width="3" fill="none" marker-end="url(#arrowRed)" stroke-dasharray="0"/>
                    
                    <!-- 云平台到车端（虚线表示间接连接） -->
                    <path d="M 200 180 C 150 380, 150 580, 200 780" stroke="#2196F3" stroke-width="2" fill="none" marker-end="url(#arrowBlue)" stroke-dasharray="10,5"/>
                    <path d="M 240 780 C 290 580, 290 380, 240 180" stroke="#F44336" stroke-width="2" fill="none" marker-end="url(#arrowRed)" stroke-dasharray="10,5"/>
                </svg>
                
                <!-- 标签说明 -->
                <div class="label cloud-to-mobile-label">钥匙授权/状态同步</div>
                <div class="label mobile-to-car-label">近场蓝牙控制/认证</div>
                <div class="label cloud-to-car-label">远程指令/状态上报</div>
            </div>
            
            <!-- 底部说明 -->
            <div class="footer">
                数字钥匙三端架构 - 安全高效的车辆接入控制系统
            </div>
        </div>
    </div>
    
    <div class="toast" id="toast">操作成功！</div>
    
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const copyButton = document.getElementById('copyButton');
            const downloadButton = document.getElementById('downloadButton');
            const toast = document.getElementById('toast');
            const slide = document.getElementById('slide');
            const resolutionBtns = document.querySelectorAll('.resolution-btn');
            
            // 默认清晰度为2x (高清)
            let currentScale = 2;
            
            // 清晰度按钮点击事件
            resolutionBtns.forEach(btn => {
                btn.addEventListener('click', function() {
                    // 移除所有按钮的active类
                    resolutionBtns.forEach(b => b.classList.remove('active'));
                    // 给当前按钮添加active类
                    this.classList.add('active');
                    // 更新当前清晰度
                    currentScale = parseFloat(this.getAttribute('data-scale'));
                    
                    // 显示提示
                    showToast(`清晰度已设置为${this.textContent}模式`);
                });
            });
            
            // 显示提示消息
            function showToast(message) {
                toast.textContent = message;
                toast.style.display = 'block';
                setTimeout(function() {
                    toast.style.display = 'none';
                }, 2000);
            }
            
            // 生成透明背景图片
            function generateImage() {
                return html2canvas(slide, {
                    scale: currentScale, // 使用用户选择的清晰度
                    useCORS: true,
                    allowTaint: true,
                    backgroundColor: null // 关键：设置为null以支持透明背景
                });
            }
            
            // 复制按钮功能
            copyButton.addEventListener('click', function() {
                showToast("正在处理图片，请稍候...");
                
                generateImage().then(function(canvas) {
                    try {
                        // 使用现代剪贴板API
                        canvas.toBlob(function(blob) {
                            try {
                                const item = new ClipboardItem({ 'image/png': blob });
                                navigator.clipboard.write([item]).then(function() {
                                    showToast("已复制到剪贴板！现在可以粘贴到PPT中");
                                }).catch(function(err) {
                                    showToast("自动复制失败，请使用下载按钮获取图片");
                                    console.error("剪贴板API错误:", err);
                                });
                            } catch (e) {
                                showToast("您的浏览器不支持复制图片，请使用下载按钮");
                                console.error("ClipboardItem错误:", e);
                            }
                        });
                    } catch (e) {
                        showToast("复制功能不可用，请使用下载图片功能");
                        console.error("toBlob错误:", e);
                    }
                }).catch(function(err) {
                    showToast("生成图片失败，请尝试下载或截图");
                    console.error("html2canvas错误:", err);
                });
            });
            
            downloadButton.addEventListener('click', function () {
                showToast(`正在生成${currentScale}x清晰度透明PNG图片，请稍候...`);

                generateImage().then(function (canvas) {
                    // 将Canvas转换为PNG并下载
                    const imageUrl = canvas.toDataURL('image/png');
                    const link = document.createElement('a');

                    // 首先从URL路径获取文件名
                    let fileName = "";
                    const pathParts = window.location.pathname.split('/');
                    const htmlFileName = pathParts[pathParts.length - 1];
                    // 移除.html后缀并进行URL解码以支持中文文件名
                    fileName = decodeURIComponent(htmlFileName.replace('.html', ''));

                    // 如果从URL获取的文件名为空，尝试从标题获取
                    if (!fileName) {
                        const titleElement = document.querySelector('.header h1');
                        if (titleElement && titleElement.textContent) {
                            fileName = titleElement.textContent.trim();
                        }
                    }

                    // 确保文件名不为空
                    if (!fileName) {
                        fileName = "测试";
                    }

                    link.download = `${fileName}-透明-${currentScale}x清晰度.png`;
                    link.href = imageUrl;
                    link.click();

                    showToast(`${currentScale}x清晰度透明背景PNG已下载`);
                }).catch(function (err) {
                    showToast("生成图片失败，请尝试截图");
                    console.error("Error:", err);
                });
            });
        });
    </script>
</body>
</html> 
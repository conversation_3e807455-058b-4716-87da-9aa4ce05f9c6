<template>
  <view class="container">
    <!-- 自定义导航栏 -->
    <cu-custom bgColor="bg-gradual-blue" :isBack="true">
      <block slot="backText">返回</block>
      <block slot="content">申请数字钥匙</block>
    </cu-custom>

    <!-- 申请表单 -->
    <view class="form-container">
      <!-- 步骤指示器 -->
      <view class="steps-container">
        <view class="step" :class="{ active: currentStep >= 1 }">
          <view class="step-number">1</view>
          <view class="step-text">个人信息</view>
        </view>
        <view class="step-line" :class="{ active: currentStep >= 2 }"></view>
        <view class="step" :class="{ active: currentStep >= 2 }">
          <view class="step-number">2</view>
          <view class="step-text">证件上传</view>
        </view>
        <view class="step-line" :class="{ active: currentStep >= 3 }"></view>
        <view class="step" :class="{ active: currentStep >= 3 }">
          <view class="step-number">3</view>
          <view class="step-text">申请原因</view>
        </view>
      </view>

      <!-- 步骤1: 个人信息 -->
      <view v-if="currentStep === 1" class="step-content">
        <view class="form-title">请填写个人信息</view>
        
        <view class="form-item">
          <view class="form-label">真实姓名 <text class="required">*</text></view>
          <uni-easyinput 
            v-model="formData.realName" 
            placeholder="请输入真实姓名"
            :clearable="true"
          />
        </view>

        <view class="form-item">
          <view class="form-label">手机号码 <text class="required">*</text></view>
          <uni-easyinput 
            v-model="formData.phone" 
            placeholder="请输入手机号码"
            type="number"
            :clearable="true"
          />
        </view>

        <view class="form-item">
          <view class="form-label">身份证号 <text class="required">*</text></view>
          <uni-easyinput 
            v-model="formData.idCard" 
            placeholder="请输入身份证号"
            :clearable="true"
          />
        </view>

        <view class="form-item">
          <view class="form-label">驾驶证号</view>
          <uni-easyinput 
            v-model="formData.drivingLicense" 
            placeholder="请输入驾驶证号"
            :clearable="true"
          />
        </view>

        <view class="btn-container">
          <button class="cu-btn bg-blue lg round" @click="nextStep">下一步</button>
        </view>
      </view>

      <!-- 步骤2: 证件上传 -->
      <view v-if="currentStep === 2" class="step-content">
        <view class="form-title">请上传证件照片</view>
        
        <!-- 身份证正面 -->
        <view class="upload-item">
          <view class="upload-label">身份证正面 <text class="required">*</text></view>
          <view class="upload-container" @click="chooseImage('idCardFront')">
            <image v-if="formData.idCardFrontUrl" :src="formData.idCardFrontUrl" class="upload-image" />
            <view v-else class="upload-placeholder">
              <text class="cuIcon-add text-gray"></text>
              <text class="upload-text">点击上传身份证正面</text>
            </view>
          </view>
        </view>

        <!-- 身份证背面 -->
        <view class="upload-item">
          <view class="upload-label">身份证背面 <text class="required">*</text></view>
          <view class="upload-container" @click="chooseImage('idCardBack')">
            <image v-if="formData.idCardBackUrl" :src="formData.idCardBackUrl" class="upload-image" />
            <view v-else class="upload-placeholder">
              <text class="cuIcon-add text-gray"></text>
              <text class="upload-text">点击上传身份证背面</text>
            </view>
          </view>
        </view>

        <!-- 驾驶证 -->
        <view class="upload-item">
          <view class="upload-label">驾驶证</view>
          <view class="upload-container" @click="chooseImage('drivingLicense')">
            <image v-if="formData.drivingLicenseUrl" :src="formData.drivingLicenseUrl" class="upload-image" />
            <view v-else class="upload-placeholder">
              <text class="cuIcon-add text-gray"></text>
              <text class="upload-text">点击上传驾驶证</text>
            </view>
          </view>
        </view>

        <view class="btn-container">
          <button class="cu-btn line-blue lg round margin-right-sm" @click="prevStep">上一步</button>
          <button class="cu-btn bg-blue lg round" @click="nextStep">下一步</button>
        </view>
      </view>

      <!-- 步骤3: 申请原因 -->
      <view v-if="currentStep === 3" class="step-content">
        <view class="form-title">请填写申请原因</view>
        
        <view class="form-item">
          <view class="form-label">申请原因 <text class="required">*</text></view>
          <textarea 
            v-model="formData.applicationReason" 
            placeholder="请详细说明申请数字钥匙的原因，如租车、临时用车等"
            class="textarea"
            maxlength="500"
          />
          <view class="char-count">{{ formData.applicationReason.length }}/500</view>
        </view>

        <view class="btn-container">
          <button class="cu-btn line-blue lg round margin-right-sm" @click="prevStep">上一步</button>
          <button class="cu-btn bg-green lg round" @click="submitApplication" :loading="submitting">提交申请</button>
        </view>
      </view>
    </view>
  </view>
</template>

<script>
import { submitUserApplication, uploadCertificate } from '@/api/dk'

export default {
  data() {
    return {
      currentStep: 1,
      submitting: false,
      formData: {
        realName: '',
        phone: '',
        idCard: '',
        drivingLicense: '',
        idCardFrontUrl: '',
        idCardBackUrl: '',
        drivingLicenseUrl: '',
        applicationReason: ''
      }
    }
  },
  methods: {
    // 下一步
    nextStep() {
      if (this.validateCurrentStep()) {
        if (this.currentStep < 3) {
          this.currentStep++
        }
      }
    },
    
    // 上一步
    prevStep() {
      if (this.currentStep > 1) {
        this.currentStep--
      }
    },
    
    // 验证当前步骤
    validateCurrentStep() {
      if (this.currentStep === 1) {
        if (!this.formData.realName.trim()) {
          uni.showToast({ title: '请输入真实姓名', icon: 'none' })
          return false
        }
        if (!this.formData.phone.trim()) {
          uni.showToast({ title: '请输入手机号码', icon: 'none' })
          return false
        }
        if (!/^1[3-9]\d{9}$/.test(this.formData.phone)) {
          uni.showToast({ title: '请输入正确的手机号码', icon: 'none' })
          return false
        }
        if (!this.formData.idCard.trim()) {
          uni.showToast({ title: '请输入身份证号', icon: 'none' })
          return false
        }
        if (!/^[1-9]\d{5}(18|19|20)\d{2}((0[1-9])|(1[0-2]))(([0-2][1-9])|10|20|30|31)\d{3}[0-9Xx]$/.test(this.formData.idCard)) {
          uni.showToast({ title: '请输入正确的身份证号', icon: 'none' })
          return false
        }
      } else if (this.currentStep === 2) {
        if (!this.formData.idCardFrontUrl) {
          uni.showToast({ title: '请上传身份证正面', icon: 'none' })
          return false
        }
        if (!this.formData.idCardBackUrl) {
          uni.showToast({ title: '请上传身份证背面', icon: 'none' })
          return false
        }
      } else if (this.currentStep === 3) {
        if (!this.formData.applicationReason.trim()) {
          uni.showToast({ title: '请填写申请原因', icon: 'none' })
          return false
        }
      }
      return true
    },
    
    // 选择图片
    chooseImage(type) {
      uni.chooseImage({
        count: 1,
        sizeType: ['compressed'],
        sourceType: ['camera', 'album'],
        success: (res) => {
          this.uploadImage(res.tempFilePaths[0], type)
        }
      })
    },
    
    // 上传图片
    uploadImage(filePath, type) {
      uni.showLoading({ title: '上传中...' })
      
      // 这里应该调用实际的上传接口
      // 暂时模拟上传成功
      setTimeout(() => {
        uni.hideLoading()
        if (type === 'idCardFront') {
          this.formData.idCardFrontUrl = filePath
        } else if (type === 'idCardBack') {
          this.formData.idCardBackUrl = filePath
        } else if (type === 'drivingLicense') {
          this.formData.drivingLicenseUrl = filePath
        }
        uni.showToast({ title: '上传成功', icon: 'success' })
      }, 1000)
    },
    
    // 提交申请
    async submitApplication() {
      if (!this.validateCurrentStep()) {
        return
      }
      
      this.submitting = true
      
      try {
        const result = await submitUserApplication(this.formData)
        
        uni.showToast({ 
          title: '申请提交成功', 
          icon: 'success',
          duration: 2000
        })
        
        // 延迟跳转到申请状态页面
        setTimeout(() => {
          uni.redirectTo({
            url: '/pages/work/applicationStatus/index'
          })
        }, 2000)
        
      } catch (error) {
        console.error('提交申请失败:', error)
        uni.showToast({ 
          title: '提交失败，请重试', 
          icon: 'none' 
        })
      } finally {
        this.submitting = false
      }
    }
  }
}
</script>

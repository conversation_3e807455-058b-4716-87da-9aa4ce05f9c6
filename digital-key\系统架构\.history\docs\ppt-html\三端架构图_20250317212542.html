<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>钥匙云平台</title>
    <!-- 引入html2canvas库 -->
    <script src="https://html2canvas.hertzen.com/dist/html2canvas.min.js"></script>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
            font-family: 'PingFang SC', 'Microsoft YaHei', sans-serif;
        }
        
        body {
            background: #f0f0f0;
            display: flex;
            justify-content: center;
            align-items: center;
            min-height: 100vh;
            color: #333;
            padding: 20px;
        }
        
        .ppt-container {
            position: relative;
            width: 1920px; /* 标准PPT宽度 */
            height: 1080px; /* 标准PPT高度 */
            box-shadow: 0 0 30px rgba(0, 0, 0, 0.2);
            border: 2px dashed #ccc;
            background-color: white;
        }
        
        .ppt-label {
            position: absolute;
            top: -40px;
            left: 0;
            background-color: #555;
            color: white;
            padding: 5px 15px;
            border-radius: 4px;
            font-size: 16px;
        }
        
        /* 按钮样式 */
        .copy-button {
            position: absolute;
            top: 20px;
            right: 20px;
            background: linear-gradient(135deg, #3498db, #2980b9);
            color: white;
            border: none;
            padding: 12px 25px;
            border-radius: 30px;
            font-size: 18px;
            cursor: pointer;
            z-index: 1000;
            box-shadow: 0 4px 10px rgba(0, 0, 0, 0.2);
            display: flex;
            align-items: center;
            gap: 10px;
            transition: all 0.3s ease;
        }
        
        .download-button {
            position: absolute;
            top: 20px;
            right: 230px;
            background: linear-gradient(135deg, #2ecc71, #27ae60);
            color: white;
            border: none;
            padding: 12px 25px;
            border-radius: 30px;
            font-size: 18px;
            cursor: pointer;
            z-index: 1000;
            box-shadow: 0 4px 10px rgba(0, 0, 0, 0.2);
            display: flex;
            align-items: center;
            gap: 10px;
            transition: all 0.3s ease;
        }
        
        .resolution-selector {
            position: absolute;
            top: 70px;
            right: 230px;
            display: flex;
            align-items: center;
            background: rgba(255, 255, 255, 0.9);
            padding: 8px 15px;
            border-radius: 30px;
            box-shadow: 0 4px 10px rgba(0, 0, 0, 0.1);
            z-index: 1000;
        }
        
        .copy-icon {
            width: 20px;
            height: 20px;
            background-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="white"><path d="M19,21H8V7H19M19,5H8A2,2 0 0,0 6,7V21A2,2 0 0,0 8,23H19A2,2 0 0,0 21,21V7A2,2 0 0,0 19,5M16,1H4A2,2 0 0,0 2,3V17H4V3H16V1Z" /></svg>');
            background-size: contain;
            background-repeat: no-repeat;
        }
        
        .download-icon {
            width: 20px;
            height: 20px;
            background-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="white"><path d="M5,20H19V18H5M19,9H15V3H9V9H5L12,16L19,9Z" /></svg>');
            background-size: contain;
            background-repeat: no-repeat;
        }
        
        .resolution-label {
            margin-right: 10px;
            font-size: 14px;
            color: #555;
        }
        
        .resolution-btn {
            border: none;
            background: #f0f0f0;
            padding: 5px 10px;
            margin: 0 3px;
            border-radius: 15px;
            font-size: 14px;
            cursor: pointer;
            transition: all 0.2s ease;
            color: #555;
        }
        
        .resolution-btn:hover {
            background: #e0e0e0;
        }
        
        .resolution-btn.active {
            background: linear-gradient(135deg, #3498db, #2980b9);
            color: white;
        }
        
        /* 滑动内容区域 */
        .slide {
            width: 100%;
            height: 100%;
            background-color: transparent;
            padding: 40px;
            position: relative;
        }
        
        /* 标题样式 */
        .header {
            text-align: center;
            margin-bottom: 50px;
        }
        
        .header h1 {
            font-size: 46px;
            color: #2c3e50;
            margin-bottom: 15px;
            font-weight: 700;
        }
        
        .header .subtitle {
            font-size: 24px;
            color: #7f8c8d;
            font-weight: 400;
        }
        
        /* 架构图主要内容区域 */
        .architecture-content {
            width: 1700px;
            height: 750px;
            margin: 0 auto;
            position: relative;
        }
        
        /* 云平台区域 */
        .cloud-platform {
            width: 100%;
            height: 150px;
            background-color: #f8fbff;
            border-radius: 8px;
            box-shadow: 0 1px 2px rgba(0,0,0,0.1);
            position: relative;
            overflow: hidden;
        }
        
        .platform-header {
            height: 50px;
            background-color: #2196F3;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 22px;
            font-weight: 700;
        }
        
        .platform-content {
            padding: 20px;
            display: flex;
            gap: 20px;
            justify-content: center;
        }
        
        .platform-module {
            padding: 10px 15px;
            background-color: white;
            border: 1px solid #e0e0e0;
            border-radius: 6px;
            min-width: 180px;
            text-align: center;
            color: #666;
            font-size: 15px;
            box-shadow: 0 1px 3px rgba(0,0,0,0.05);
        }
        
        /* 手机端区域 */
        .mobile-platform {
            width: 580px;
            height: 330px;
            background-color: #f5fff7;
            border-radius: 8px;
            box-shadow: 0 1px 2px rgba(0,0,0,0.1);
            position: absolute;
            top: 240px;
            left: 50%;
            transform: translateX(-50%);
            overflow: hidden;
        }
        
        .mobile-header {
            height: 50px;
            background-color: #4CAF50;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 22px;
            font-weight: 700;
        }
        
        .mobile-content {
            padding: 20px;
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
            justify-content: center;
        }
        
        .mobile-module {
            padding: 10px 15px;
            background-color: white;
            border: 1px solid #e0e0e0;
            border-radius: 6px;
            text-align: center;
            color: #666;
            font-size: 15px;
            box-shadow: 0 1px 3px rgba(0,0,0,0.05);
            height: 115px;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        
        /* 车端区域 */
        .car-platform {
            width: 100%;
            height: 120px;
            background-color: #fff8f8;
            border-radius: 8px;
            box-shadow: 0 1px 2px rgba(0,0,0,0.1);
            position: absolute;
            bottom: 0;
            overflow: hidden;
        }
        
        .car-header {
            height: 45px;
            background-color: #F44336;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 22px;
            font-weight: 700;
        }
        
        .car-content {
            padding: 15px;
            display: flex;
            gap: 15px;
            justify-content: center;
        }
        
        .car-module {
            padding: 8px 12px;
            background-color: white;
            border: 1px solid #e0e0e0;
            border-radius: 6px;
            min-width: 160px;
            text-align: center;
            color: #666;
            font-size: 15px;
            box-shadow: 0 1px 3px rgba(0,0,0,0.05);
        }
        
        /* 连接线和标签 */
        .connector-container {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            pointer-events: none;
        }
        
        .label {
            position: absolute;
            background-color: white;
            padding: 5px 12px;
            border-radius: 15px;
            font-size: 14px;
            box-shadow: 0 1px 4px rgba(0,0,0,0.1);
            white-space: nowrap;
            z-index: 100;
            text-align: center;
        }
        
        .blue-label {
            border: 1px solid rgba(33, 150, 243, 0.3);
            color: #1976D2;
        }
        
        .green-label {
            border: 1px solid rgba(76, 175, 80, 0.3);
            color: #388E3C;
        }
        
        .red-label {
            border: 1px solid rgba(244, 67, 54, 0.3); 
            color: #D32F2F;
        }
        
        .proximity-tag {
            position: absolute;
            width: 30px;
            height: 30px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            color: white;
            font-size: 14px;
            z-index: 100;
        }
        
        .near-tag {
            background-color: #4CAF50;
        }
        
        .far-tag {
            background-color: #2196F3;
        }
        
        /* 底部说明 */
        .footer {
            position: absolute;
            bottom: 20px;
            width: 100%;
            text-align: center;
            font-size: 16px;
            color: #95a5a6;
            left: 0;
        }
        
        .toast {
            position: fixed;
            top: 50px;
            left: 50%;
            transform: translateX(-50%);
            background-color: rgba(0, 0, 0, 0.8);
            color: white;
            padding: 12px 25px;
            border-radius: 30px;
            font-size: 16px;
            z-index: 2000;
            display: none;
            animation: fadeInOut 2s ease;
        }
        
        @keyframes fadeInOut {
            0% { opacity: 0; }
            20% { opacity: 1; }
            80% { opacity: 1; }
            100% { opacity: 0; }
        }
        
        /* 倾斜的连接标签 */
        .slant-label {
            transform-origin: center;
        }
    </style>
</head>
<body>
    <div class="ppt-container" id="pptContainer">
        <div class="ppt-label">PPT尺寸(1920×1080) - 透明背景</div>
        <button class="copy-button" id="copyButton">
            <span class="copy-icon"></span>复制为PPT图片
        </button>
        <button class="download-button" id="downloadButton">
            <span class="download-icon"></span>下载透明PNG
        </button>
        
        <!-- 添加清晰度选择按钮组 -->
        <div class="resolution-selector" id="resolutionSelector">
            <span class="resolution-label">清晰度:</span>
            <button class="resolution-btn" data-scale="1">标准</button>
            <button class="resolution-btn active" data-scale="2">高清</button>
            <button class="resolution-btn" data-scale="3">超清</button>
        </div>
        
        <div class="slide" id="slide">
            <!-- 顶部标题 -->
            <div class="header">
                <h1>数字钥匙三端架构图</h1>
                <div class="subtitle">车辆数字钥匙系统技术架构与通信流程</div>
            </div>
            
            <!-- 架构图内容 -->
            <div class="architecture-content">
                <!-- 云平台 -->
                <div class="cloud-platform">
                    <div class="platform-header">钥匙云平台</div>
                    <div class="platform-content">
                        <div class="platform-module">钥匙生命周期管理</div>
                        <div class="platform-module">安全认证中心</div>
                        <div class="platform-module">密钥管理系统</div>
                        <div class="platform-module">统一接口服务</div>
                        <div class="platform-module">安全通信通道</div>
                        <div class="platform-module">时间同步服务</div>
                    </div>
                </div>
                
                <!-- 手机端 -->
                <div class="mobile-platform">
                    <div class="mobile-header">手机端</div>
                    <div class="mobile-content">
                        <div class="mobile-module">钥匙与车辆管理</div>
                        <div class="mobile-module">蓝牙通信模块</div>
                        <div class="mobile-module">安全存储模块</div>
                        <div class="mobile-module">暗号交换模块</div>
                    </div>
                </div>
                
                <!-- 车端 -->
                <div class="car-platform">
                    <div class="car-header">车端</div>
                    <div class="car-content">
                        <div class="car-module">钥匙验证模块</div>
                        <div class="car-module">蓝牙通信模块</div>
                        <div class="car-module">指令执行模块</div>
                        <div class="car-module">暗号交换模块</div>
                        <div class="car-module">安全存储模块</div>
                        <div class="car-module">远程通信模块</div>
                    </div>
                </div>
                
                <!-- 连接标签 -->
                <div id="labelContainer"></div>
                
                <!-- 连接线将由SVG动态绘制 -->
                <svg id="connector-svg" class="connector-container"></svg>
            </div>
            
            <!-- 底部说明 -->
            <div class="footer">
                数字钥匙三端架构 - 安全高效的车辆接入控制系统
            </div>
        </div>
    </div>
    
    <div class="toast" id="toast">操作成功！</div>
    
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const copyButton = document.getElementById('copyButton');
            const downloadButton = document.getElementById('downloadButton');
            const toast = document.getElementById('toast');
            const slide = document.getElementById('slide');
            const resolutionBtns = document.querySelectorAll('.resolution-btn');
            const svgContainer = document.getElementById('connector-svg');
            const labelContainer = document.getElementById('labelContainer');
            
            // 设置SVG视窗
            svgContainer.setAttribute('viewBox', '0 0 1700 750');
            
            // 绘制垂直连接线 - 云平台到手机端
            const cloudToMobile = document.createElementNS('http://www.w3.org/2000/svg', 'path');
            cloudToMobile.setAttribute('d', 'M 800 150 L 800 240');
            cloudToMobile.setAttribute('stroke', '#2196F3');
            cloudToMobile.setAttribute('stroke-width', '2.5');
            cloudToMobile.setAttribute('fill', 'none');
            cloudToMobile.setAttribute('marker-end', 'url(#arrowBlue)');
            svgContainer.appendChild(cloudToMobile);
            
            const mobileToCloud = document.createElementNS('http://www.w3.org/2000/svg', 'path');
            mobileToCloud.setAttribute('d', 'M 900 240 L 900 150');
            mobileToCloud.setAttribute('stroke', '#4CAF50');
            mobileToCloud.setAttribute('stroke-width', '2.5');
            mobileToCloud.setAttribute('fill', 'none');
            mobileToCloud.setAttribute('marker-end', 'url(#arrowGreen)');
            svgContainer.appendChild(mobileToCloud);
            
            // 绘制垂直连接线 - 手机端到车端
            const mobileTocar = document.createElementNS('http://www.w3.org/2000/svg', 'path');
            mobileTocar.setAttribute('d', 'M 800 570 L 800 630');
            mobileTocar.setAttribute('stroke', '#4CAF50');
            mobileTocar.setAttribute('stroke-width', '2.5');
            mobileTocar.setAttribute('fill', 'none');
            mobileTocar.setAttribute('marker-end', 'url(#arrowGreen)');
            svgContainer.appendChild(mobileTocar);
            
            const carToMobile = document.createElementNS('http://www.w3.org/2000/svg', 'path');
            carToMobile.setAttribute('d', 'M 900 630 L 900 570');
            carToMobile.setAttribute('stroke', '#F44336');
            carToMobile.setAttribute('stroke-width', '2.5');
            carToMobile.setAttribute('fill', 'none');
            carToMobile.setAttribute('marker-end', 'url(#arrowRed)');
            svgContainer.appendChild(carToMobile);
            
            // 绘制弧形连接线 - 云平台到车端 (蓝色)
            const cloudToCar = document.createElementNS('http://www.w3.org/2000/svg', 'path');
            cloudToCar.setAttribute('d', 'M 400 150 C 350 350, 350 500, 400 630');
            cloudToCar.setAttribute('stroke', '#2196F3');
            cloudToCar.setAttribute('stroke-width', '2.5');
            cloudToCar.setAttribute('stroke-dasharray', '8,5');
            cloudToCar.setAttribute('fill', 'none');
            cloudToCar.setAttribute('marker-end', 'url(#arrowBlue)');
            svgContainer.appendChild(cloudToCar);
            
            // 绘制弧形连接线 - 车端到云平台 (红色)
            const carToCloud = document.createElementNS('http://www.w3.org/2000/svg', 'path');
            carToCloud.setAttribute('d', 'M 500 630 C 550 500, 550 350, 500 150');
            carToCloud.setAttribute('stroke', '#F44336');
            carToCloud.setAttribute('stroke-width', '2.5');
            carToCloud.setAttribute('stroke-dasharray', '8,5');
            carToCloud.setAttribute('fill', 'none');
            carToCloud.setAttribute('marker-end', 'url(#arrowRed)');
            svgContainer.appendChild(carToCloud);
            
            // 定义箭头标记
            const defs = document.createElementNS('http://www.w3.org/2000/svg', 'defs');
            
            const arrowBlue = document.createElementNS('http://www.w3.org/2000/svg', 'marker');
            arrowBlue.setAttribute('id', 'arrowBlue');
            arrowBlue.setAttribute('viewBox', '0 0 10 10');
            arrowBlue.setAttribute('refX', '5');
            arrowBlue.setAttribute('refY', '5');
            arrowBlue.setAttribute('markerWidth', '6');
            arrowBlue.setAttribute('markerHeight', '6');
            arrowBlue.setAttribute('orient', 'auto');
            
            const pathBlue = document.createElementNS('http://www.w3.org/2000/svg', 'path');
            pathBlue.setAttribute('d', 'M 0 0 L 10 5 L 0 10 z');
            pathBlue.setAttribute('fill', '#2196F3');
            arrowBlue.appendChild(pathBlue);
            defs.appendChild(arrowBlue);
            
            const arrowGreen = document.createElementNS('http://www.w3.org/2000/svg', 'marker');
            arrowGreen.setAttribute('id', 'arrowGreen');
            arrowGreen.setAttribute('viewBox', '0 0 10 10');
            arrowGreen.setAttribute('refX', '5');
            arrowGreen.setAttribute('refY', '5');
            arrowGreen.setAttribute('markerWidth', '6');
            arrowGreen.setAttribute('markerHeight', '6');
            arrowGreen.setAttribute('orient', 'auto');
            
            const pathGreen = document.createElementNS('http://www.w3.org/2000/svg', 'path');
            pathGreen.setAttribute('d', 'M 0 0 L 10 5 L 0 10 z');
            pathGreen.setAttribute('fill', '#4CAF50');
            arrowGreen.appendChild(pathGreen);
            defs.appendChild(arrowGreen);
            
            const arrowRed = document.createElementNS('http://www.w3.org/2000/svg', 'marker');
            arrowRed.setAttribute('id', 'arrowRed');
            arrowRed.setAttribute('viewBox', '0 0 10 10');
            arrowRed.setAttribute('refX', '5');
            arrowRed.setAttribute('refY', '5');
            arrowRed.setAttribute('markerWidth', '6');
            arrowRed.setAttribute('markerHeight', '6');
            arrowRed.setAttribute('orient', 'auto');
            
            const pathRed = document.createElementNS('http://www.w3.org/2000/svg', 'path');
            pathRed.setAttribute('d', 'M 0 0 L 10 5 L 0 10 z');
            pathRed.setAttribute('fill', '#F44336');
            arrowRed.appendChild(pathRed);
            defs.appendChild(arrowRed);
            
            svgContainer.appendChild(defs);
            
            // 创建标签
            function createLabel(text, className, left, top, transform = '') {
                const label = document.createElement('div');
                label.className = `label ${className}`;
                label.textContent = text;
                label.style.left = left + 'px';
                label.style.top = top + 'px';
                if (transform) {
                    label.classList.add('slant-label');
                    label.style.transform = transform;
                }
                labelContainer.appendChild(label);
            }
            
            // 添加各种标签
            createLabel('钥匙授权/认证', 'blue-label', 725, 185);
            createLabel('状态同步/更新', 'green-label', 925, 185);
            
            createLabel('蓝牙控制指令', 'green-label', 725, 590);
            createLabel('状态反馈/验证', 'red-label', 925, 590);
            
            createLabel('远程控制/配置更新', 'blue-label', 300, 350, 'rotate(-35deg)');
            createLabel('状态上报/安全校验', 'red-label', 520, 350, 'rotate(35deg)');
            
            // 添加近、远标签
            const nearTag = document.createElement('div');
            nearTag.className = 'proximity-tag near-tag';
            nearTag.textContent = '近';
            nearTag.style.left = '575px';
            nearTag.style.top = '470px';
            labelContainer.appendChild(nearTag);
            
            const farTag = document.createElement('div');
            farTag.className = 'proximity-tag far-tag';
            farTag.textContent = '远';
            farTag.style.left = '310px';
            farTag.style.top = '375px';
            labelContainer.appendChild(farTag);
            
            // 默认清晰度为2x (高清)
            let currentScale = 2;
            
            // 清晰度按钮点击事件
            resolutionBtns.forEach(btn => {
                btn.addEventListener('click', function() {
                    // 移除所有按钮的active类
                    resolutionBtns.forEach(b => b.classList.remove('active'));
                    // 给当前按钮添加active类
                    this.classList.add('active');
                    // 更新当前清晰度
                    currentScale = parseFloat(this.getAttribute('data-scale'));
                    
                    // 显示提示
                    showToast(`清晰度已设置为${this.textContent}模式`);
                });
            });
            
            // 显示提示消息
            function showToast(message) {
                toast.textContent = message;
                toast.style.display = 'block';
                setTimeout(function() {
                    toast.style.display = 'none';
                }, 2000);
            }
            
            // 生成透明背景图片
            function generateImage() {
                return html2canvas(slide, {
                    scale: currentScale, // 使用用户选择的清晰度
                    useCORS: true,
                    allowTaint: true,
                    backgroundColor: null // 关键：设置为null以支持透明背景
                });
            }
            
            // 复制按钮功能
            copyButton.addEventListener('click', function() {
                showToast("正在处理图片，请稍候...");
                
                generateImage().then(function(canvas) {
                    try {
                        // 使用现代剪贴板API
                        canvas.toBlob(function(blob) {
                            try {
                                const item = new ClipboardItem({ 'image/png': blob });
                                navigator.clipboard.write([item]).then(function() {
                                    showToast("已复制到剪贴板！现在可以粘贴到PPT中");
                                }).catch(function(err) {
                                    showToast("自动复制失败，请使用下载按钮获取图片");
                                    console.error("剪贴板API错误:", err);
                                });
                            } catch (e) {
                                showToast("您的浏览器不支持复制图片，请使用下载按钮");
                                console.error("ClipboardItem错误:", e);
                            }
                        });
                    } catch (e) {
                        showToast("复制功能不可用，请使用下载图片功能");
                        console.error("toBlob错误:", e);
                    }
                }).catch(function(err) {
                    showToast("生成图片失败，请尝试下载或截图");
                    console.error("html2canvas错误:", err);
                });
            });
            
            // 下载按钮功能
            downloadButton.addEventListener('click', function () {
                showToast(`正在生成${currentScale}x清晰度透明PNG图片，请稍候...`);

                generateImage().then(function (canvas) {
                    // 将Canvas转换为PNG并下载
                    const imageUrl = canvas.toDataURL('image/png');
                    const link = document.createElement('a');

                    // 首先从URL路径获取文件名
                    let fileName = "";
                    const pathParts = window.location.pathname.split('/');
                    const htmlFileName = pathParts[pathParts.length - 1];
                    // 移除.html后缀并进行URL解码以支持中文文件名
                    fileName = decodeURIComponent(htmlFileName.replace('.html', ''));

                    // 如果从URL获取的文件名为空，尝试从标题获取
                    if (!fileName) {
                        const titleElement = document.querySelector('.header h1');
                        if (titleElement && titleElement.textContent) {
                            fileName = titleElement.textContent.trim();
                        }
                    }

                    // 确保文件名不为空
                    if (!fileName) {
                        fileName = "测试";
                    }

                    link.download = `${fileName}-透明-${currentScale}x清晰度.png`;
                    link.href = imageUrl;
                    link.click();

                    showToast(`${currentScale}x清晰度透明背景PNG已下载`);
                }).catch(function (err) {
                    showToast("生成图片失败，请尝试截图");
                    console.error("Error:", err);
                });
            });
        });
    </script>
</body>
</html> 
<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="账号" prop="userId">
        <el-input
          v-model="queryParams.userName"
          placeholder="请输入账号"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="车辆VIN" prop="vehicleVin">
        <el-input
          v-model="queryParams.vehicleVin"
          placeholder="请输入车辆VIN"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="临时密钥" prop="bluetoothTempKey">
        <el-input
          v-model="queryParams.bluetoothTempKey"
          placeholder="请输入临时密钥"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="正式密钥" prop="bluetoothPermKey">
        <el-input
          v-model="queryParams.bluetoothPermKey"
          placeholder="请输入正式密钥"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="el-icon-plus"
          size="mini"
          @click="handleAdd"
          v-hasPermi="['dk:vehicleBluetoothKeys:add']"
        >新增</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="success"
          plain
          icon="el-icon-edit"
          size="mini"
          :disabled="single"
          @click="handleUpdate"
          v-hasPermi="['dk:vehicleBluetoothKeys:edit']"
        >修改</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="danger"
          plain
          icon="el-icon-delete"
          size="mini"
          :disabled="multiple"
          @click="handleDelete"
          v-hasPermi="['dk:vehicleBluetoothKeys:remove']"
        >批量解绑</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="el-icon-download"
          size="mini"
          @click="handleExport"
          v-hasPermi="['dk:vehicleBluetoothKeys:export']"
        >导出</el-button>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="vehicleBluetoothKeysList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="账号" align="center" prop="userName" />
      <el-table-column label="车辆VIN" align="center" prop="vehicleVin" />
      <el-table-column label="临时密钥" align="center" prop="bluetoothTempKey" />
      <el-table-column label="&lt; 生成时间" align="center" prop="bluetoothTempKeyTime" width="180">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.bluetoothTempKeyTime, '{y}-{m}-{d}') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="正式密钥" align="center" prop="bluetoothPermKey" />
      <el-table-column label="&lt; 生成时间" align="center" prop="bluetoothPermKeyTime" width="180">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.bluetoothPermKeyTime, '{y}-{m}-{d}') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="text"
            icon="el-icon-edit"
            @click="handleUpdate(scope.row)"
            v-hasPermi="['dk:vehicleBluetoothKeys:edit']"
          >修改</el-button>
          <el-button
            size="mini"
            type="text"
            icon="el-icon-delete"
            @click="handleDelete(scope.row)"
            v-hasPermi="['dk:vehicleBluetoothKeys:remove']"
          >解绑</el-button>
        </template>
      </el-table-column>
    </el-table>
    
    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加或修改蓝牙密钥对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="500px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="80px">
        <el-form-item label="临时密钥" prop="bluetoothTempKey">
          <el-input v-model="form.bluetoothTempKey" placeholder="请输入临时密钥" />
        </el-form-item>
        <el-form-item label="&lt; 生成时间" prop="bluetoothTempKeyTime">
          <el-date-picker clearable
            v-model="form.bluetoothTempKeyTime"
            type="date"
            value-format="yyyy-MM-dd"
            placeholder="请选择&lt; 生成时间">
          </el-date-picker>
        </el-form-item>
        <el-form-item label="正式密钥" prop="bluetoothPermKey">
          <el-input v-model="form.bluetoothPermKey" placeholder="请输入正式密钥" />
        </el-form-item>
        <el-form-item label="&lt; 生成时间" prop="bluetoothPermKeyTime">
          <el-date-picker clearable
            v-model="form.bluetoothPermKeyTime"
            type="date"
            value-format="yyyy-MM-dd"
            placeholder="请选择&lt; 生成时间">
          </el-date-picker>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { listVehicleBluetoothKeys, getVehicleBluetoothKeys, delVehicleBluetoothKeys, addVehicleBluetoothKeys, updateVehicleBluetoothKeys } from "@/api/dk/vehicleBluetoothKeys";

export default {
  name: "VehicleBluetoothKeys",
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 蓝牙密钥表格数据
      vehicleBluetoothKeysList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        userName: null,
        vehicleVin: null,
        bluetoothTempKey: null,
        bluetoothPermKey: null,
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
      }
    };
  },
  created() {
    this.getList();
  },
  methods: {
    /** 查询蓝牙密钥列表 */
    getList() {
      this.loading = true;
      listVehicleBluetoothKeys(this.queryParams).then(response => {
        this.vehicleBluetoothKeysList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        userId: null,
        vehicleVin: null,
        bluetoothTempKey: null,
        bluetoothTempKeyTime: null,
        bluetoothPermKey: null,
        bluetoothPermKeyTime: null
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.userId)
      this.single = selection.length!==1
      this.multiple = !selection.length
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "添加蓝牙密钥";
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const userId = row.userId || this.ids
      getVehicleBluetoothKeys(userId).then(response => {
        this.form = response.data;
        this.open = true;
        this.title = "修改蓝牙密钥";
      });
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate(valid => {
        if (valid) {
          if (this.form.userId != null) {
            updateVehicleBluetoothKeys(this.form).then(response => {
              this.$modal.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            });
          } else {
            addVehicleBluetoothKeys(this.form).then(response => {
              this.$modal.msgSuccess("新增成功");
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },
    /** 解绑按钮操作 */
    handleDelete(row) {
      const userIds = row.userId || this.ids;
      this.$modal.confirm('是否确认解绑？').then(function() {
        return delVehicleBluetoothKeys(userIds);
      }).then(() => {
        this.getList();
        this.$modal.msgSuccess("解绑成功");
      }).catch(() => {});
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download('dk/vehicleBluetoothKeys/export', {
        ...this.queryParams
      }, `vehicleBluetoothKeys_${new Date().getTime()}.xlsx`)
    }
  }
};
</script>

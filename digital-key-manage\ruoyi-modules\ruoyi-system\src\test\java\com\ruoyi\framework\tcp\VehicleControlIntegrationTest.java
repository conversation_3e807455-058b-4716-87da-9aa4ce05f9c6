package com.ruoyi.framework.tcp;

import com.alibaba.fastjson2.JSON;
import com.ruoyi.dk.domain.DKWebSocketMessage;
import org.junit.jupiter.api.Test;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit.jupiter.SpringJUnitConfig;

import java.io.*;
import java.net.Socket;
import java.net.URI;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.TimeUnit;

/**
 * 4G控车系统集成测试
 * 测试完整的控车流程：WebSocket -> TCP -> TBOX模拟器
 * 
 * <AUTHOR>
 */
@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.DEFINED_PORT)
@SpringJUnitConfig
public class VehicleControlIntegrationTest {
    
    private static final Logger LOGGER = LoggerFactory.getLogger(VehicleControlIntegrationTest.class);
    
    private static final String TCP_HOST = "localhost";
    private static final int TCP_PORT = 9999;
    
    /**
     * 测试TCP连接和协议通信
     */
    @Test
    public void testTcpConnection() throws Exception {
        LOGGER.info("🧪 开始测试TCP连接和协议通信");
        
        CountDownLatch latch = new CountDownLatch(1);
        
        // 模拟TBOX客户端连接
        Thread tboxThread = new Thread(() -> {
            try (Socket socket = new Socket(TCP_HOST, TCP_PORT);
                 BufferedReader reader = new BufferedReader(new InputStreamReader(socket.getInputStream()));
                 PrintWriter writer = new PrintWriter(socket.getOutputStream(), true)) {
                
                LOGGER.info("✅ TBOX模拟器已连接到TCP服务器");
                
                // 等待服务器指令
                String command;
                int messageCount = 0;
                while ((command = reader.readLine()) != null && messageCount < 2) {
                    LOGGER.info("📨 TBOX收到指令: {}", command);
                    
                    // 根据指令发送相应的状态响应
                    if (command.contains("04007E")) {
                        // 解锁指令，发送解锁状态
                        String unlockResponse = "7E000A000A000102030405060708090A000030333032303030313034007E";
                        writer.println(unlockResponse);
                        LOGGER.info("📤 TBOX发送解锁状态: {}", unlockResponse);
                        
                    } else if (command.contains("03007E")) {
                        // 闭锁指令，发送闭锁状态
                        String lockResponse = "7E000A000A000102030405060708090A000030333032303030313033007E";
                        writer.println(lockResponse);
                        LOGGER.info("📤 TBOX发送闭锁状态: {}", lockResponse);
                    }
                    
                    messageCount++;
                }
                
                latch.countDown();
                
            } catch (Exception e) {
                LOGGER.error("❌ TBOX模拟器测试失败: {}", e.getMessage(), e);
            }
        });
        
        tboxThread.start();
        
        // 等待TBOX连接建立
        Thread.sleep(2000);
        
        // 测试TCP控制服务
        TcpControlService tcpControlService = new TcpControlService();
        
        // 发送解锁指令
        LOGGER.info("🔓 发送解锁指令");
        tcpControlService.sendUnlockCommandToAllTbox();
        
        Thread.sleep(1000);
        
        // 发送闭锁指令
        LOGGER.info("🔒 发送闭锁指令");
        tcpControlService.sendLockCommandToAllTbox();
        
        // 等待测试完成
        boolean completed = latch.await(10, TimeUnit.SECONDS);
        if (completed) {
            LOGGER.info("✅ TCP连接和协议通信测试通过");
        } else {
            LOGGER.error("❌ TCP连接和协议通信测试超时");
        }
    }
    
    /**
     * 测试协议解析功能
     */
    @Test
    public void testProtocolParsing() {
        LOGGER.info("🧪 开始测试协议解析功能");
        
        TcpControlService tcpControlService = new TcpControlService();
        
        // 测试解锁状态解析
        String unlockMessage = "7E000A000A000102030405060708090A000030333032303030313034007E";
        TcpControlService.TboxStatusResult unlockResult = tcpControlService.parseTboxMessage(unlockMessage);
        
        assert unlockResult.isValid() : "解锁消息解析失败";
        assert "unlocked".equals(unlockResult.getLockStatus()) : "解锁状态识别错误";
        LOGGER.info("✅ 解锁状态解析正确: {}", unlockResult.getStatusDescription());
        
        // 测试闭锁状态解析
        String lockMessage = "7E000A000A000102030405060708090A000030333032303030313033007E";
        TcpControlService.TboxStatusResult lockResult = tcpControlService.parseTboxMessage(lockMessage);
        
        assert lockResult.isValid() : "闭锁消息解析失败";
        assert "locked".equals(lockResult.getLockStatus()) : "闭锁状态识别错误";
        LOGGER.info("✅ 闭锁状态解析正确: {}", lockResult.getStatusDescription());
        
        // 测试无效消息
        String invalidMessage = "invalid_message";
        TcpControlService.TboxStatusResult invalidResult = tcpControlService.parseTboxMessage(invalidMessage);
        
        assert !invalidResult.isValid() : "无效消息应该解析失败";
        LOGGER.info("✅ 无效消息正确识别: {}", invalidResult.getErrorMessage());
        
        // 测试指令生成
        String unlockCommand = tcpControlService.generateUnlockCommand();
        String lockCommand = tcpControlService.generateLockCommand();
        
        assert unlockCommand.contains("04007E") : "解锁指令生成错误";
        assert lockCommand.contains("03007E") : "闭锁指令生成错误";
        
        LOGGER.info("✅ 指令生成正确 - 解锁: {}, 闭锁: {}", unlockCommand, lockCommand);
        LOGGER.info("✅ 协议解析功能测试通过");
    }
    
    /**
     * 测试WebSocket消息格式
     */
    @Test
    public void testWebSocketMessageFormat() {
        LOGGER.info("🧪 开始测试WebSocket消息格式");
        
        // 测试4G控车请求消息
        DKWebSocketMessage controlMessage = new DKWebSocketMessage();
        controlMessage.setMessageType(4);
        controlMessage.setMessage("unlock");
        controlMessage.setSenderName("用户");
        controlMessage.setSenderId(1L);
        
        String jsonMessage = JSON.toJSONString(controlMessage);
        LOGGER.info("📤 4G控车请求消息: {}", jsonMessage);
        
        // 解析消息
        DKWebSocketMessage parsedMessage = JSON.parseObject(jsonMessage, DKWebSocketMessage.class);
        
        assert parsedMessage.getMessageType() == 4 : "消息类型解析错误";
        assert "unlock".equals(parsedMessage.getMessage()) : "消息内容解析错误";
        assert "用户".equals(parsedMessage.getSenderName()) : "发送者解析错误";
        
        LOGGER.info("✅ WebSocket消息格式测试通过");
        
        // 测试状态反馈消息
        DKWebSocketMessage statusMessage = new DKWebSocketMessage();
        statusMessage.setMessageType(0);
        statusMessage.setMessage("车辆状态: 车辆已解锁");
        statusMessage.setSenderName("TBOX");
        
        String statusJson = JSON.toJSONString(statusMessage);
        LOGGER.info("📤 状态反馈消息: {}", statusJson);
        
        DKWebSocketMessage parsedStatus = JSON.parseObject(statusJson, DKWebSocketMessage.class);
        assert parsedStatus.getMessageType() == 0 : "状态消息类型错误";
        assert parsedStatus.getMessage().contains("车辆已解锁") : "状态消息内容错误";
        
        LOGGER.info("✅ 状态反馈消息格式测试通过");
    }
    
    /**
     * 测试错误处理
     */
    @Test
    public void testErrorHandling() {
        LOGGER.info("🧪 开始测试错误处理");
        
        TcpControlService tcpControlService = new TcpControlService();
        
        // 测试空消息处理
        TcpControlService.TboxStatusResult emptyResult = tcpControlService.parseTboxMessage("");
        assert !emptyResult.isValid() : "空消息应该处理失败";
        LOGGER.info("✅ 空消息错误处理正确");
        
        // 测试null消息处理
        TcpControlService.TboxStatusResult nullResult = tcpControlService.parseTboxMessage(null);
        assert !nullResult.isValid() : "null消息应该处理失败";
        LOGGER.info("✅ null消息错误处理正确");
        
        // 测试格式错误消息
        TcpControlService.TboxStatusResult formatErrorResult = tcpControlService.parseTboxMessage("7E123");
        assert !formatErrorResult.isValid() : "格式错误消息应该处理失败";
        LOGGER.info("✅ 格式错误消息处理正确");
        
        // 测试协议格式验证
        assert !tcpControlService.isValidProtocolFormat("") : "空字符串应该验证失败";
        assert !tcpControlService.isValidProtocolFormat("invalid") : "无效格式应该验证失败";
        assert tcpControlService.isValidProtocolFormat("7E000A000A000102030405060708090A000030333032303030313034007E") : "有效格式应该验证成功";
        
        LOGGER.info("✅ 错误处理测试通过");
    }
    
    /**
     * 性能测试
     */
    @Test
    public void testPerformance() {
        LOGGER.info("🧪 开始性能测试");
        
        TcpControlService tcpControlService = new TcpControlService();
        String testMessage = "7E000A000A000102030405060708090A000030333032303030313034007E";
        
        // 测试协议解析性能
        long startTime = System.currentTimeMillis();
        int iterations = 10000;
        
        for (int i = 0; i < iterations; i++) {
            TcpControlService.TboxStatusResult result = tcpControlService.parseTboxMessage(testMessage);
            assert result.isValid() : "解析失败";
        }
        
        long endTime = System.currentTimeMillis();
        long duration = endTime - startTime;
        double avgTime = (double) duration / iterations;
        
        LOGGER.info("✅ 协议解析性能测试完成");
        LOGGER.info("📊 解析{}次消息耗时: {}ms, 平均每次: {}ms", iterations, duration, avgTime);
        
        assert avgTime < 1.0 : "协议解析性能不达标，平均耗时超过1ms";
        
        // 测试指令生成性能
        startTime = System.currentTimeMillis();
        
        for (int i = 0; i < iterations; i++) {
            String unlockCmd = tcpControlService.generateUnlockCommand();
            String lockCmd = tcpControlService.generateLockCommand();
            assert unlockCmd != null && lockCmd != null : "指令生成失败";
        }
        
        endTime = System.currentTimeMillis();
        duration = endTime - startTime;
        avgTime = (double) duration / iterations;
        
        LOGGER.info("✅ 指令生成性能测试完成");
        LOGGER.info("📊 生成{}次指令耗时: {}ms, 平均每次: {}ms", iterations * 2, duration, avgTime);
        
        assert avgTime < 0.1 : "指令生成性能不达标，平均耗时超过0.1ms";
        
        LOGGER.info("✅ 性能测试通过");
    }
}

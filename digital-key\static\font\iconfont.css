@font-face {
  font-family: "iconfont";
  src: url('@/static/font/iconfont.ttf') format('truetype');
}

.iconfont {
  font-family: "iconfont" !important;
  font-size: 16px;
  display: inline-block;
  font-style: normal;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.icon-user:before {
  content: "";
  display: inline-block;
  width: 40rpx; /* 图标宽度 */
  height: 40rpx; /* 图标高度 */
  background-image: url('@/static/icons/icon_phone.png'); 
  background-size: cover;
}

.icon-password:before {
  content: "";
  display: inline-block;
  width: 40rpx; /* 图标宽度 */
  height: 40rpx; /* 图标高度 */
  background-image: url('@/static/icons/icon_password.png'); 
  background-size: cover;
}

/* 车内检测人员图标 - 有人 */
.icon-personIn:before {
  content: "";
  display: inline-block;
  width: 34rpx; /* 图标宽度 */
  height: 34rpx; /* 图标高度 */
  background-image: url('@/static/icons/icon_my_nor.png'); /* 使用基础图标 */
  background-size: cover;
  position: relative;
  /* 使用更现代、更柔和的蓝色 */
  background-color: #4285F4;
  border-radius: 50%;
  box-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.1);
}

/* 车内检测人员图标 - 无人 */
.icon-personOut:before {
  content: "";
  display: inline-block;
  width: 34rpx; /* 图标宽度 */
  height: 34rpx; /* 图标高度 */
  background-image: url('@/static/icons/icon_my_nor.png'); /* 使用现有的个人图标灰色版本 */
  background-size: cover;
  position: relative;
}

.icon-bleNotConnected:before {
  content: "";
  display: inline-block;
  width: 34rpx; /* 图标宽度 */
  height: 34rpx; /* 图标高度 */
  background-image: url('@/static/icons/icon_bluetooth_not_connected.png'); 
  background-size: cover;
  position: relative;
  margin-right: 8rpx;
}

.icon-bleConnected:before {
  content: "";
  display: inline-block;
  width: 34rpx; /* 图标宽度 */
  height: 34rpx; /* 图标高度 */
  background-image: url('@/static/icons/icon_bluetooth_connected.png'); 
  background-size: cover;
  position: relative;
  margin-right: 8rpx;
}

.icon-goConnectedBle:before {
  content: "";
  display: inline-block;
  width: 34rpx; /* 图标宽度 */
  height: 34rpx; /* 图标高度 */
  background-image: url('@/static/icons/icon_go.png'); 
  background-size: cover;
  position: absolute;
  top: 50%;
  transform: translate(390%, -50%);
}

.icon-startNor:before {
  content: "";
  display: block;
  width: 120rpx; /* 图标宽度 */
  height: 120rpx; /* 图标高度 */
  background-image: url('@/static/icons/icon_start_nor.png'); 
  background-size: cover; /* 设置背景图像大小，cover将缩放背景图像以完全覆盖元素区域 */
  /* border: 1rpx solid blue!important; */
}

.icon-startPress:before {
  content: "";
  display: block;
  width: 120rpx; /* 图标宽度 */
  height: 120rpx; /* 图标高度 */
  background-image: url('@/static/icons/icon_start_press.png'); 
  background-size: cover; /* 设置背景图像大小，cover将缩放背景图像以完全覆盖元素区域 */
  /* border: 1rpx solid blue!important; */
}

.icon-lockNor:before {
  content: "";
  display: block;
  width: 120rpx; /* 图标宽度 */
  height: 120rpx; /* 图标高度 */
  background-image: url('@/static/icons/icon_lock_nor.png'); 
  background-size: cover; /* 设置背景图像大小，cover将缩放背景图像以完全覆盖元素区域 */
}

.icon-lockPress:before {
  content: "";
  display: block;
  width: 120rpx; /* 图标宽度 */
  height: 120rpx; /* 图标高度 */
  background-image: url('@/static/icons/icon_lock_press.png'); 
  background-size: cover; /* 设置背景图像大小，cover将缩放背景图像以完全覆盖元素区域 */
}

.icon-trunkNor:before {
  content: "";
  display: block;
  width: 120rpx; /* 图标宽度 */
  height: 120rpx; /* 图标高度 */
  background-image: url('@/static/icons/icon_trunk_nor.png'); 
  background-size: cover; /* 设置背景图像大小，cover将缩放背景图像以完全覆盖元素区域 */
}

.icon-trunkPress:before {
  content: "";
  display: block;
  width: 120rpx; /* 图标宽度 */
  height: 120rpx; /* 图标高度 */
  background-image: url('@/static/icons/icon_trunk_press.png'); 
  background-size: cover; /* 设置背景图像大小，cover将缩放背景图像以完全覆盖元素区域 */
}

.icon-findCarNor:before {
  content: "";
  display: block;
  width: 120rpx; /* 图标宽度 */
  height: 120rpx; /* 图标高度 */
  background-image: url('@/static/icons/icon_find car_nor.png'); 
  background-size: cover; /* 设置背景图像大小，cover将缩放背景图像以完全覆盖元素区域 */
}

.icon-findCarPress:before {
  content: "";
  display: block;
  width: 120rpx; /* 图标宽度 */
  height: 120rpx; /* 图标高度 */
  background-image: url('@/static/icons/icon_find car_press.png'); 
  background-size: cover; /* 设置背景图像大小，cover将缩放背景图像以完全覆盖元素区域 */
}

.icon-carWindowsNor:before {
  content: "";
  display: block;
  width: 120rpx; /* 图标宽度 */
  height: 120rpx; /* 图标高度 */
  background-image: url('@/static/icons/icon_car windows_nor.png'); 
  background-size: cover; /* 设置背景图像大小，cover将缩放背景图像以完全覆盖元素区域 */
}

.icon-carWindowsPress:before {
  content: "";
  display: block;
  width: 120rpx; /* 图标宽度 */
  height: 120rpx; /* 图标高度 */
  background-image: url('@/static/icons/icon_car windows_press.png'); 
  background-size: cover; /* 设置背景图像大小，cover将缩放背景图像以完全覆盖元素区域 */
}

.icon-defrostNor:before {
  content: "";
  display: block;
  width: 120rpx; /* 图标宽度 */
  height: 120rpx; /* 图标高度 */
  background-image: url('@/static/icons/icon_defrost_nor.png'); 
  background-size: cover; /* 设置背景图像大小，cover将缩放背景图像以完全覆盖元素区域 */
}

.icon-defrostPress:before {
  content: "";
  display: block;
  width: 120rpx; /* 图标宽度 */
  height: 120rpx; /* 图标高度 */
  background-image: url('@/static/icons/icon_defrost_press.png'); 
  background-size: cover; /* 设置背景图像大小，cover将缩放背景图像以完全覆盖元素区域 */
}

.icon-return:before {
  content: "";
  display: block;
  width: 32rpx; /* 图标宽度 */
  height: 32rpx; /* 图标高度 */
  background-image: url('@/static/icons/icon_return.png'); 
  background-size: cover; /* 设置背景图像大小，cover将缩放背景图像以完全覆盖元素区域 */
}

.icon-code:before {
  content: "\e699";
}

.icon-setting:before {
  content: "\e6cc";
}

.icon-share:before {
  content: "\e739";
}

.icon-edit:before {
  content: "\e60c";
}

.icon-version:before {
  content: "\e63f";
}

.icon-service:before {
  content: "\e6ff";
}

.icon-friendfill:before {
  content: "\e726";
}

.icon-community:before {
  content: "\e741";
}

.icon-people:before {
  content: "\e736";
}

.icon-dianzan:before {
  content: "\ec7f";
}

.icon-right:before {
  content: "\e7eb";
}

.icon-logout:before {
  content: "\e61d";
}

.icon-help:before {
  content: "\e616";
}

.icon-github:before {
  content: "\e628";
}

.icon-aixin:before {
  content: "\e601";
}

.icon-clean:before {
  content: "\e607";
}

.icon-refresh:before {
  content: "\e604";
}


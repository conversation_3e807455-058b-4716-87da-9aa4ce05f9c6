<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>数字钥匙系统技术架构</title>
    <style>
        body {
            font-family: "Microsoft YaHei", Arial, sans-serif;
            line-height: 1.6;
            color: #333;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }
        header {
            background-color: #1e88e5;
            color: white;
            padding: 20px;
            border-radius: 5px;
            margin-bottom: 20px;
            text-align: center;
        }
        h1, h2, h3, h4 {
            color: #1565c0;
        }
        h1 {
            font-size: 2.5em;
            margin-bottom: 20px;
        }
        h2 {
            font-size: 1.8em;
            border-bottom: 2px solid #1e88e5;
            padding-bottom: 10px;
            margin-top: 40px;
        }
        h3 {
            font-size: 1.4em;
            margin-top: 30px;
        }
        nav {
            background-color: #f5f5f5;
            padding: 15px;
            border-radius: 5px;
            margin-bottom: 30px;
        }
        nav ul {
            list-style: none;
            padding: 0;
            display: flex;
            flex-wrap: wrap;
        }
        nav li {
            margin-right: 20px;
            margin-bottom: 10px;
        }
        nav a {
            text-decoration: none;
            color: #1976d2;
            font-weight: bold;
        }
        nav a:hover {
            color: #1565c0;
            text-decoration: underline;
        }
        section {
            margin-bottom: 40px;
            background-color: #fff;
            padding: 20px;
            border-radius: 5px;
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
        }
        .architecture-diagram {
            text-align: center;
            margin: 30px 0;
            padding: 20px;
            background-color: #f9f9f9;
            border-radius: 5px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .architecture-diagram img {
            max-width: 100%;
            height: auto;
        }
        table {
            width: 100%;
            border-collapse: collapse;
            margin: 20px 0;
        }
        table, th, td {
            border: 1px solid #ddd;
        }
        th, td {
            padding: 12px;
            text-align: left;
        }
        th {
            background-color: #f2f2f2;
        }
        tr:nth-child(even) {
            background-color: #f9f9f9;
        }
        .note {
            background-color: #fff9c4;
            padding: 15px;
            border-left: 4px solid #ffd600;
            margin: 20px 0;
        }
        .component {
            background-color: #e3f2fd;
            padding: 15px;
            border-radius: 5px;
            margin: 15px 0;
        }
        footer {
            text-align: center;
            margin-top: 50px;
            padding: 20px;
            background-color: #f5f5f5;
            border-radius: 5px;
        }
        code {
            font-family: Consolas, Monaco, monospace;
            background-color: #f5f5f5;
            padding: 2px 5px;
            border-radius: 3px;
            font-size: 0.9em;
        }
        .flow-diagram {
            display: flex;
            justify-content: space-between;
            overflow-x: auto;
            margin: 20px 0;
            padding: 10px 0;
        }
        .flow-step {
            flex: 1;
            min-width: 120px;
            text-align: center;
            padding: 15px;
            background-color: #e8f5e9;
            border-radius: 5px;
            margin: 0 5px;
            position: relative;
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
            border-left: 3px solid #4caf50;
        }
        .flow-step:not(:last-child):after {
            content: "→";
            position: absolute;
            right: -15px;
            top: 50%;
            font-size: 24px;
            color: #388e3c;
            transform: translateY(-50%);
        }
        .diagram-note {
            font-style: italic;
            color: #666;
            margin-top: 15px;
        }
        .component-container {
            display: flex;
            flex-wrap: wrap;
            gap: 20px;
            margin: 20px 0;
        }
        .component {
            flex: 1;
            min-width: 300px;
            background-color: #e3f2fd;
            padding: 15px;
            border-radius: 5px;
            border-left: 4px solid #1976d2;
            margin: 15px 0;
            box-shadow: 0 2px 5px rgba(0,0,0,0.05);
        }
        .component h4 {
            margin-top: 0;
            color: #1976d2;
            border-bottom: 1px solid #bbdefb;
            padding-bottom: 8px;
        }
        .security-control-points {
            margin-top: 20px;
            padding: 20px;
            background-color: #fff9c4;
            border-radius: 5px;
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
        }
        .control-points-container {
            display: flex;
            justify-content: space-between;
            margin-bottom: 15px;
        }
        .control-point {
            text-align: center;
            width: 24%;
        }
        .control-icon {
            font-size: 2em;
            margin-bottom: 5px;
            height: 60px;
            width: 60px;
            margin: 0 auto 10px;
            background-size: contain;
            background-repeat: no-repeat;
            background-position: center;
        }
        .user-auth {
            background-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="%231976d2"><path d="M12 12c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm0 2c-2.67 0-8 1.34-8 4v2h16v-2c0-2.66-5.33-4-8-4z"/></svg>');
        }
        .vehicle-auth {
            background-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="%231976d2"><path d="M18.92 6.01C18.72 5.42 18.16 5 17.5 5h-11c-.66 0-1.21.42-1.42 1.01L3 12v8c0 .55.45 1 1 1h1c.55 0 1-.45 1-1v-1h12v1c0 .55.45 1 1 1h1c.55 0 1-.45 1-1v-8l-2.08-5.99zM6.5 16c-.83 0-1.5-.67-1.5-1.5S5.67 13 6.5 13s1.5.67 1.5 1.5S7.33 16 6.5 16zm11 0c-.83 0-1.5-.67-1.5-1.5s.67-1.5 1.5-1.5 1.5.67 1.5 1.5-.67 1.5-1.5 1.5zM5 11l1.5-4.5h11L19 11H5z"/></svg>');
        }
        .secure-channel {
            background-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="%231976d2"><path d="M18 8h-1V6c0-2.76-2.24-5-5-5S7 3.24 7 6v2H6c-1.1 0-2 .9-2 2v10c0 1.1.9 2 2 2h12c1.1 0 2-.9 2-2V10c0-1.1-.9-2-2-2zm-6 9c-1.1 0-2-.9-2-2s.9-2 2-2 2 .9 2 2-.9 2-2 2zm3.1-9H8.9V6c0-1.71 1.39-3.1 3.1-3.1 1.71 0 3.1 1.39 3.1 3.1v2z"/></svg>');
        }
        .key-protection {
            background-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="%231976d2"><path d="M12.65 10C11.83 7.67 9.61 6 7 6c-3.31 0-6 2.69-6 6s2.69 6 6 6c2.61 0 4.83-1.67 5.65-4H17v4h4v-4h2v-4H12.65zM7 14c-1.1 0-2-.9-2-2s.9-2 2-2 2 .9 2 2-.9 2-2 2z"/></svg>');
        }
        .success-icon {
            font-size: 2em;
            margin-bottom: 5px;
            height: 40px;
            width: 40px;
            margin: 0 auto 10px;
            background-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="%234caf50"><path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z"/></svg>');
            background-size: contain;
            background-repeat: no-repeat;
            background-position: center;
        }
        .flow-steps-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin: 20px 0;
        }
        .step-card {
            background-color: #e3f2fd;
            border-radius: 8px;
            padding: 20px;
            text-align: center;
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
            transition: transform 0.3s ease, box-shadow 0.3s ease;
        }
        .step-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.2);
        }
        .step-number {
            font-size: 1.5em;
            font-weight: bold;
            color: #1976d2;
            margin-bottom: 10px;
        }
        .step-description {
            font-size: 1em;
        }
        .optimization-strategy {
            display: flex;
            flex-wrap: wrap;
            justify-content: space-between;
            margin: 20px 0;
        }
        .strategy-item {
            width: 48%;
            background-color: #f5f5f5;
            border-left: 4px solid #1976d2;
            margin-bottom: 15px;
            padding: 15px;
            border-radius: 0 4px 4px 0;
            box-shadow: 0 2px 5px rgba(0,0,0,0.05);
        }
        .strategy-title {
            font-weight: bold;
            margin-bottom: 5px;
            color: #1565c0;
        }
        .strategy-desc {
            font-size: 0.9em;
            color: #555;
        }
        .control-text {
            font-size: 0.9em;
        }
        .success-message {
            text-align: center;
            margin-top: 15px;
            padding: 10px;
            background-color: #e8f5e9;
            border-radius: 5px;
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
        }
        .success-icon {
            font-size: 2em;
            margin-bottom: 5px;
        }
        .key-management-visual {
            display: flex;
            justify-content: space-between;
            margin-top: 20px;
        }
        .key-hierarchy-diagram, .key-storage-cards, .key-update-timeline {
            flex: 1;
            padding: 20px;
            border-radius: 5px;
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
        }
        .key-pyramid {
            display: flex;
            justify-content: space-between;
            margin-bottom: 20px;
        }
        .key-level {
            text-align: center;
            padding: 10px;
            border-radius: 5px;
            box-shadow: 0 2px 5px rgba(0,0,0,0.05);
        }
        .key-icon {
            font-size: 2em;
            margin-bottom: 5px;
            height: 60px;
            width: 60px;
            margin: 0 auto 10px;
            background-size: contain;
            background-repeat: no-repeat;
            background-position: center;
        }
        .key-name {
            font-weight: bold;
            margin-bottom: 5px;
            color: #1565c0;
        }
        .key-desc {
            font-size: 0.9em;
            color: #555;
        }
        .storage-container {
            display: flex;
            justify-content: space-between;
            margin-bottom: 20px;
        }
        .storage-card {
            text-align: center;
            padding: 10px;
            border-radius: 5px;
            box-shadow: 0 2px 5px rgba(0,0,0,0.05);
        }
        .storage-icon {
            font-size: 2em;
            margin-bottom: 5px;
            height: 60px;
            width: 60px;
            margin: 0 auto 10px;
            background-size: contain;
            background-repeat: no-repeat;
            background-position: center;
        }
        .storage-title {
            font-weight: bold;
            margin-bottom: 5px;
            color: #1565c0;
        }
        .storage-desc {
            font-size: 0.9em;
            color: #555;
        }
        .timeline {
            display: flex;
            justify-content: space-between;
            margin-bottom: 20px;
        }
        .timeline-item {
            text-align: center;
            padding: 10px;
            border-radius: 5px;
            box-shadow: 0 2px 5px rgba(0,0,0,0.05);
        }
        .timeline-icon {
            font-size: 2em;
            margin-bottom: 5px;
            height: 60px;
            width: 60px;
            margin: 0 auto 10px;
            background-size: contain;
            background-repeat: no-repeat;
            background-position: center;
        }
        .timeline-title {
            font-weight: bold;
            margin-bottom: 5px;
            color: #1565c0;
        }
        .timeline-desc {
            font-size: 0.9em;
            color: #555;
        }
        .defense-group {
            fill: #f3e5f5;
            stroke: #9c27b0;
            stroke-width: 2;
        }
        .group-title {
            font-family: "Microsoft YaHei", Arial, sans-serif;
            fill: #6a1b9a;
            font-size: 16px;
            font-weight: bold;
        }
        .defense-item {
            fill: #e1bee7;
            stroke: #9c27b0;
            stroke-width: 1;
        }
        .item-text {
            font-family: "Microsoft YaHei", Arial, sans-serif;
            fill: #333;
            font-size: 14px;
        }
        .desc-text {
            font-family: "Microsoft YaHei", Arial, sans-serif;
            fill: #333;
            font-size: 13px;
        }
        .root-key .key-icon {
            background-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="%234caf50"><path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z"/></svg>');
        }
        .intermediate-key .key-icon {
            background-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="%232e7d32"><path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z"/></svg>');
        }
        .session-key .key-icon {
            background-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="%23fbc02d"><path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z"/></svg>');
        }
        .regular .timeline-icon {
            background-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="%232e7d32"><path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z"/></svg>');
        }
        .emergency .timeline-icon {
            background-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="%23fbc02d"><path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z"/></svg>');
        }
        .version .timeline-icon {
            background-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="%23fbc02d"><path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z"/></svg>');
        }
        .cloud .storage-icon {
            background-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="%234caf50"><path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z"/></svg>');
        }
        .mobile .storage-icon {
            background-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="%232e7d32"><path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z"/></svg>');
        }
        .vehicle .storage-icon {
            background-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="%23fbc02d"><path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z"/></svg>');
        }
        .security-features-container {
            margin: 30px 0;
        }
        .features-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(240px, 1fr));
            gap: 20px;
        }
        .feature-card {
            background-color: #f5f5f5;
            border-radius: 8px;
            padding: 20px;
            box-shadow: 0 3px 6px rgba(0,0,0,0.1);
            transition: transform 0.3s ease, box-shadow 0.3s ease;
        }
        .feature-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 6px 12px rgba(0,0,0,0.15);
        }
        .feature-icon {
            height: 60px;
            width: 60px;
            margin: 0 auto 15px;
            background-size: contain;
            background-repeat: no-repeat;
            background-position: center;
        }
        .feature-title {
            font-weight: bold;
            font-size: 16px;
            color: #6a1b9a;
            margin-bottom: 10px;
            text-align: center;
        }
        .feature-desc {
            font-size: 14px;
            color: #555;
            text-align: center;
        }
        .timestamp .feature-icon {
            background-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="%236a1b9a"><path d="M11.99 2C6.47 2 2 6.48 2 12s4.47 10 9.99 10C17.52 22 22 17.52 22 12S17.52 2 11.99 2zM12 20c-4.42 0-8-3.58-8-8s3.58-8 8-8 8 3.58 8 8-3.58 8-8 8zm.5-13H11v6l5.25 3.15.75-1.23-4.5-2.67z"/></svg>');
        }
        .challenge .feature-icon {
            background-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="%236a1b9a"><path d="M18 8h-1V6c0-2.76-2.24-5-5-5S7 3.24 7 6v2H6c-1.1 0-2 .9-2 2v10c0 1.1.9 2 2 2h12c1.1 0 2-.9 2-2V10c0-1.1-.9-2-2-2zm-6 9c-1.1 0-2-.9-2-2s.9-2 2-2 2 .9 2 2-.9 2-2 2zm3.1-9H8.9V6c0-1.71 1.39-3.1 3.1-3.1 1.71 0 3.1 1.39 3.1 3.1v2z"/></svg>');
        }
        .rssi .feature-icon {
            background-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="%236a1b9a"><path d="M12 3C7.41 3 3.86 4.53 0.59 7.59L2 9c2.97-2.66 5.77-4 10-4s7.03 1.34 10 4l1.41-1.41C20.14 4.53 16.59 3 12 3zm0 4c-3.31 0-6.28 1.33-8.47 3.59L5 12c1.97-1.79 3.96-3 7-3s5.03 1.21 7 3l1.47-1.41C18.28 8.33 15.31 7 12 7zm0 4c-1.66 0-3 1.34-3 3s1.34 3 3 3 3-1.34 3-3-1.34-3-3-3z"/></svg>');
        }
        .multinode .feature-icon {
            background-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="%236a1b9a"><path d="M15 11V5l-3-3-3 3v2H3v14h18V11h-6zm-8 8H5v-2h2v2zm0-4H5v-2h2v2zm0-4H5V9h2v2zm6 8h-2v-2h2v2zm0-4h-2v-2h2v2zm0-4h-2V9h2v2zm0-4h-2V5h2v2zm6 12h-2v-2h2v2zm0-4h-2v-2h2v2z"/></svg>');
        }
        .twofactor .feature-icon {
            background-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="%236a1b9a"><path d="M12 1L3 5v6c0 5.55 3.84 10.74 9 12 5.16-1.26 9-6.45 9-12V5l-9-4zm0 10.99h7c-.53 4.12-3.28 7.79-7 8.94V12H5V6.3l7-3.11v8.8z"/></svg>');
        }
        .certificate .feature-icon {
            background-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="%236a1b9a"><path d="M12 3c-4.97 0-9 4.03-9 9s4.03 9 9 9 9-4.03 9-9-4.03-9-9-9zm0 16c-3.86 0-7-3.14-7-7s3.14-7 7-7 7 3.14 7 7-3.14 7-7 7zm-1.46-5.47L8.41 11.4l-1.06 1.06 3.18 3.18 6-6-1.06-1.06-4.93 4.95z"/></svg>');
        }
        .monitoring .feature-icon {
            background-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="%236a1b9a"><path d="M19 3H5c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h14c1.1 0 2-.9 2-2V5c0-1.1-.9-2-2-2zm-7 14H5v-2h7v2zm7-4H5v-2h14v2zm0-4H5V7h14v2z"/></svg>');
        }
        .emergency .feature-icon {
            background-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="%236a1b9a"><path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm1 15h-2v-2h2v2zm0-4h-2V7h2v6z"/></svg>');
        }
        .value-cards {
            display: flex;
            justify-content: space-between;
            margin-bottom: 20px;
        }
        .value-card {
            flex: 1;
            text-align: center;
            padding: 20px;
            border-radius: 5px;
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
        }
        .value-icon {
            height: 60px;
            width: 60px;
            margin: 0 auto 15px;
            background-size: contain;
            background-repeat: no-repeat;
            background-position: center;
        }
        .value-title {
            font-weight: bold;
            margin-bottom: 5px;
        }
        .value-desc {
            font-size: 0.9em;
            color: #555;
        }
        .system-components {
            display: flex;
            justify-content: space-between;
            margin-bottom: 20px;
        }
        .component-item {
            flex: 1;
            text-align: center;
            padding: 20px;
            border-radius: 5px;
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
        }
        .component-icon {
            height: 60px;
            width: 60px;
            margin: 0 auto 15px;
            background-size: contain;
            background-repeat: no-repeat;
            background-position: center;
        }
        .component-title {
            font-weight: bold;
            margin-bottom: 5px;
        }
        .component-desc {
            font-size: 0.9em;
            color: #555;
        }
        .scenario-container {
            display: flex;
            justify-content: space-between;
            margin-bottom: 20px;
        }
        .scenario-card {
            flex: 1;
            text-align: center;
            padding: 20px;
            border-radius: 5px;
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
        }
        .scenario-icon {
            font-size: 4em;
            margin-bottom: 10px;
            color: #1976d2;
        }
        .scenario-title {
            font-weight: bold;
            margin-bottom: 5px;
        }
        .scenario-desc {
            font-size: 0.9em;
            color: #555;
        }
        .tech-features-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(240px, 1fr));
            gap: 20px;
        }
        .tech-feature-card {
            background-color: #f5f5f5;
            border-radius: 8px;
            padding: 20px;
            box-shadow: 0 3px 6px rgba(0,0,0,0.1);
            transition: transform 0.3s ease, box-shadow 0.3s ease;
        }
        .tech-feature-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 6px 12px rgba(0,0,0,0.15);
        }
        .feature-icon {
            height: 60px;
            width: 60px;
            margin: 0 auto 15px;
            background-size: contain;
            background-repeat: no-repeat;
            background-position: center;
        }
        .feature-title {
            font-weight: bold;
            font-size: 16px;
            color: #6a1b9a;
            margin-bottom: 10px;
            text-align: center;
        }
        .feature-desc {
            font-size: 14px;
            color: #555;
            text-align: center;
        }
        .data-flow-container {
            display: flex;
            flex-wrap: wrap;
            gap: 20px;
            margin: 20px 0;
        }
        .data-flow-step {
            flex: 1;
            min-width: 200px;
            background-color: #e3f2fd;
            border-radius: 5px;
            padding: 15px;
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
            transition: transform 0.3s ease, box-shadow 0.3s ease;
        }
        .data-flow-step:hover {
            transform: translateY(-5px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.2);
        }
        .flow-number {
            font-size: 1.5em;
            font-weight: bold;
            color: #1976d2;
            margin-bottom: 10px;
        }
        .flow-content {
            font-size: 1em;
        }
        .flow-icon {
            font-size: 2em;
            margin-bottom: 5px;
            height: 60px;
            width: 60px;
            margin: 0 auto 10px;
            background-size: contain;
            background-repeat: no-repeat;
            background-position: center;
        }
        .flow-title {
            font-weight: bold;
            margin-bottom: 5px;
            color: #1565c0;
        }
        .flow-desc {
            font-size: 0.9em;
            color: #555;
        }
        .bluetooth-secure {
            background-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="%231976d2"><path d="M17.71 7.71L12 2h-1v7.59L6.41 5 5 6.41 10.59 12 5 17.59 6.41 19 11 14.41V22h1l5.71-5.71-4.3-4.29 4.3-4.29zM13 5.83l1.88 1.88L13 9.59V5.83zm1.88 10.46L13 18.17v-3.76l1.88 1.88z"/></svg>');
        }
        .secure-arch {
            background-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="%231976d2"><path d="M12 1L3 5v6c0 5.55 3.84 10.74 9 12 5.16-1.26 9-6.45 9-12V5l-9-4zm0 10.99h7c-.53 4.12-3.28 7.79-7 8.94V12H5V6.3l7-3.11v8.8z"/></svg>');
        }
        .key-exchange {
            background-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="%231976d2"><path d="M12.65 10C11.83 7.67 9.61 6 7 6c-3.31 0-6 2.69-6 6s2.69 6 6 6c2.61 0 4.83-1.67 5.65-4H17v4h4v-4h2v-4H12.65zM7 14c-1.1 0-2-.9-2-2s.9-2 2-2 2 .9 2 2-.9 2-2 2z"/></svg>');
        }
        .passive-control {
            background-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="%231976d2"><path d="M18.92 6.01C18.72 5.42 18.16 5 17.5 5h-11c-.66 0-1.21.42-1.42 1.01L3 12v8c0 .55.45 1 1 1h1c.55 0 1-.45 1-1v-1h12v1c0 .55.45 1 1 1h1c.55 0 1-.45 1-1v-8l-2.08-5.99zM6.5 16c-.83 0-1.5-.67-1.5-1.5S5.67 13 6.5 13s1.5.67 1.5 1.5S7.33 16 6.5 16zm11 0c-.83 0-1.5-.67-1.5-1.5s.67-1.5 1.5-1.5 1.5.67 1.5 1.5-.67 1.5-1.5 1.5zM5 11l1.5-4.5h11L19 11H5z"/></svg>');
        }
        .low-power {
            background-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="%231976d2"><path d="M15.67 4H14V2h-4v2H8.33C7.6 4 7 4.6 7 5.33v15.33C7 21.4 7.6 22 8.33 22h7.33c.74 0 1.34-.6 1.34-1.33V5.33C17 4.6 16.4 4 15.67 4zM13 18h-2v-2h2v2zm0-4h-2V9h2v5z"/></svg>');
        }
        .offline {
            background-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="%231976d2"><path d="M24 19V9c0-.55-.45-1-1-1h-6c-.55 0-1 .45-1 1v3.61l2 2V10h4v7h-1.61l2.93 2.93c.39-.13.68-.49.68-.93zM21 6c.55 0 1-.45 1-1s-.45-1-1-1H7.39l2 2H21zm-11.21 6.07L7.43 7.1 5.1 7.43l6.18 6.18 4.1-4.11-1.17-1.17-2.42 2.42-1.9-1.9zM1.41 1.31L0 2.72l2.3 2.3C.95 6.12 0 7.97 0 10v9c0 1.1.9 2 2 2h16.92l1.65 1.65 1.41-1.41L1.41 1.31zM2 19V9.98c0-1.41.6-2.68 1.56-3.57L8.4 11.2 4.33 15H6l3-3 1.98 1.98L2 19z"/></svg>');
        }
        .cloud-interaction {
            background-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="%231976d2"><path d="M19.35 10.04C18.67 6.59 15.64 4 12 4 9.11 4 6.6 5.64 5.35 8.04 2.34 8.36 0 10.91 0 14c0 3.31 2.69 6 6 6h13c2.76 0 5-2.24 5-5 0-2.64-2.05-4.78-4.65-4.96zM10 17l-3.5-3.5 1.41-1.41L10 14.17 15.18 9l1.41 1.41L10 17z"/></svg>');
        }
        .key-management {
            background-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="%231976d2"><path d="M7 14c-1.66 0-3 1.34-3 3 0 1.31-1.16 2-2 2 .92 1.22 2.49 2 4 2 2.21 0 4-1.79 4-4 0-1.66-1.34-3-3-3zm13.71-9.37l-1.34-1.34c-.39-.39-1.02-.39-1.41 0L9 12.25 11.75 15l8.96-8.96c.39-.39.39-1.02 0-1.41z"/></svg>');
        }
        .key-verification {
            background-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="%231976d2"><path d="M22 5.18L10.59 16.6l-4.24-4.24 1.41-1.41 2.83 2.83 10-10L22 5.18zM19.79 10.22C19.92 10.79 20 11.39 20 12c0 4.42-3.58 8-8 8s-8-3.58-8-8c0-4.42 3.58-8 8-8 1.58 0 3.04.46 4.28 1.25l1.44-1.44C16.1 2.67 14.13 2 12 2 6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10c0-1.19-.22-2.33-.6-3.39l-1.61 1.61z"/></svg>');
        }
        .vehicle-control {
            background-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="%231976d2"><path d="M18.92 6.01C18.72 5.42 18.16 5 17.5 5h-11c-.66 0-1.21.42-1.42 1.01L3 12v8c0 .55.45 1 1 1h1c.55 0 1-.45 1-1v-1h12v1c0 .55.45 1 1 1h1c.55 0 1-.45 1-1v-8l-2.08-5.99zM6.5 16c-.83 0-1.5-.67-1.5-1.5S5.67 13 6.5 13s1.5.67 1.5 1.5S7.33 16 6.5 16zm11 0c-.83 0-1.5-.67-1.5-1.5s.67-1.5 1.5-1.5 1.5.67 1.5 1.5-.67 1.5-1.5 1.5zM5 11l1.5-4.5h11L19 11H5z"/></svg>');
        }
        .exception-handling {
            background-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="%231976d2"><path d="M14.59 8L12 10.59 9.41 8 8 9.41 10.59 12 8 14.59 9.41 16 12 13.41 14.59 16 16 14.59 13.41 12 16 9.41 14.59 8zM12 2C6.47 2 2 6.47 2 12s4.47 10 10 10 10-4.47 10-10S17.53 2 12 2zm0 18c-4.41 0-8-3.59-8-8s3.59-8 8-8 8 3.59 8 8-3.59 8-8 8z"/></svg>');
        }
        .hardware {
            background-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="%236a1b9a"><path d="M20 18c1.1 0 1.99-.9 1.99-2L22 6c0-1.1-.9-2-2-2H4c-1.1 0-2 .9-2 2v10c0 1.1.9 2 2 2H0v2h24v-2h-4zM4 6h16v10H4V6z"/></svg>');
        }
        .communication {
            background-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="%236a1b9a"><path d="M7.58 4.08L6.15 2.65C3.75 4.48 2.17 7.3 2.03 10.5h2c.15-2.65 1.51-4.97 3.55-6.42zm12.39 6.42h2c-.15-3.2-1.73-6.02-4.12-7.85l-1.42 1.43c2.02 1.45 3.39 3.77 3.54 6.42zM18 11c0-3.07-1.64-5.64-4.5-6.32V4c0-.83-.67-1.5-1.5-1.5s-1.5.67-1.5 1.5v.68C7.63 5.36 6 7.92 6 11v5l-2 2v1h16v-1l-2-2v-5zm-6 11c.14 0 .27-.01.4-.04.65-.14 1.18-.58 1.44-1.18.1-.24.15-.5.15-.78h-4c.01 1.1.9 2 2.01 2z"/></svg>');
        }
        .time-sync {
            background-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="%236a1b9a"><path d="M11.99 2C6.47 2 2 6.48 2 12s4.47 10 9.99 10C17.52 22 22 17.52 22 12S17.52 2 11.99 2zM12 20c-4.42 0-8-3.58-8-8s3.58-8 8-8 8 3.58 8 8-3.58 8-8 8zm.5-13H11v6l5.25 3.15.75-1.23-4.5-2.67z"/></svg>');
        }
        .relay-protection {
            background-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="%236a1b9a"><path d="M12 1L3 5v6c0 5.55 3.84 10.74 9 12 5.16-1.26 9-6.45 9-12V5l-9-4zm0 10.99h7c-.53 4.12-3.28 7.79-7 8.94V12H5V6.3l7-3.11v8.8z"/></svg>');
        }
        .mutual-auth {
            background-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="%236a1b9a"><path d="M18 8h-1V6c0-2.76-2.24-5-5-5S7 3.24 7 6v2H6c-1.1 0-2 .9-2 2v10c0 1.1.9 2 2 2h12c1.1 0 2-.9 2-2V10c0-1.1-.9-2-2-2zm-6 9c-1.1 0-2-.9-2-2s.9-2 2-2 2 .9 2 2-.9 2-2 2zm3.1-9H8.9V6c0-1.71 1.39-3.1 3.1-3.1 1.71 0 3.1 1.39 3.1 3.1v2z"/></svg>');
        }
        .key-lifecycle {
            background-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="%236a1b9a"><path d="M12 6v3l4-4-4-4v3c-4.42 0-8 3.58-8 8 0 1.57.46 3.03 1.24 4.26L6.7 14.8c-.45-.83-.7-1.79-.7-2.8 0-3.31 2.69-6 6-6zm6.76 1.74L17.3 9.2c.44.84.7 1.79.7 2.8 0 3.31-2.69 6-6 6v-3l-4 4 4 4v-3c4.42 0 8-3.58 8-8 0-1.57-.46-3.03-1.24-4.26z"/></svg>');
        }
        .security-system-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(240px, 1fr));
            gap: 20px;
            margin: 20px 0;
        }
        .security-card {
            background-color: #f5f5f5;
            border-radius: 8px;
            padding: 20px;
            box-shadow: 0 3px 6px rgba(0,0,0,0.1);
            transition: transform 0.3s ease, box-shadow 0.3s ease;
        }
        .security-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 6px 12px rgba(0,0,0,0.15);
        }
        .security-icon {
            height: 60px;
            width: 60px;
            margin: 0 auto 15px;
            background-size: contain;
            background-repeat: no-repeat;
            background-position: center;
        }
        .security-title {
            font-weight: bold;
            font-size: 16px;
            color: #6a1b9a;
            margin-bottom: 10px;
            text-align: center;
        }
        .security-desc {
            font-size: 14px;
            color: #555;
            text-align: center;
        }
        /* 项目概述部分样式 */
        .background-visual {
            margin: 20px 0;
            display: flex;
            flex-direction: column;
            align-items: center;
        }
        .pain-points-container {
            width: 100%;
            background-color: #f5f5f5;
            border-radius: 8px;
            padding: 20px;
            box-shadow: 0 3px 6px rgba(0,0,0,0.1);
        }
        .pain-points-container h4 {
            text-align: center;
            margin-bottom: 20px;
            color: #1565c0;
        }
        .pain-points-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
        }
        .pain-point-card {
            background-color: white;
            border-radius: 8px;
            padding: 15px;
            text-align: center;
            box-shadow: 0 2px 5px rgba(0,0,0,0.05);
            transition: transform 0.3s ease, box-shadow 0.3s ease;
        }
        .pain-point-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }
        .pain-icon {
            height: 60px;
            width: 60px;
            margin: 0 auto 15px;
            background-size: contain;
            background-repeat: no-repeat;
            background-position: center;
        }
        .pain-title {
            font-weight: bold;
            font-size: 16px;
            color: #e53935;
            margin-bottom: 10px;
        }
        .pain-desc {
            font-size: 14px;
            color: #555;
        }
        .lost {
            background-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="%23e53935"><path d="M15.5 14h-.79l-.28-.27C15.41 12.59 16 11.11 16 9.5 16 5.91 13.09 3 9.5 3S3 5.91 3 9.5 5.91 16 9.5 16c1.61 0 3.09-.59 4.23-1.57l.27.28v.79l5 4.99L20.49 19l-4.99-5zm-6 0C7.01 14 5 11.99 5 9.5S7.01 5 9.5 5 14 7.01 14 9.5 11.99 14 9.5 14z"/></svg>');
        }
        .inconvenient {
            background-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="%23e53935"><path d="M11 15h2v2h-2zm0-8h2v6h-2zm.99-5C6.47 2 2 6.48 2 12s4.47 10 9.99 10C17.52 22 22 17.52 22 12S17.52 2 11.99 2zM12 20c-4.42 0-8-3.58-8-8s3.58-8 8-8 8 3.58 8 8-3.58 8-8 8z"/></svg>');
        }
        .no-sharing {
            background-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="%23e53935"><path d="M12 4.5C7 4.5 2.73 7.61 1 12c1.73 4.39 6 7.5 11 7.5s9.27-3.11 11-7.5c-1.73-4.39-6-7.5-11-7.5zM12 17c-2.76 0-5-2.24-5-5s2.24-5 5-5 5 2.24 5 5-2.24 5-5 5zm0-8c-1.66 0-3 1.34-3 3s1.34 3 3 3 3-1.34 3-3-1.34-3-3-3z"/><path d="M0 0h24v24H0z" fill="none"/><path d="M3.27 5L2 6.27l2.44 2.44C1.21 11.06 0 12 0 12s4 8 12 8 12-8 12-8-1.21-.94-4.44-3.29L21.73 5 20.46 3.73l-16 16L3.27 5zm13.34 7.9l-1.43 1.43c-.79.79-2.05.8-2.85.01l-.01-.01c-.79-.79-.79-2.07 0-2.86.79-.79 2.08-.79 2.86 0l1.43-1.43c-1.57-1.57-4.15-1.57-5.71 0-1.57 1.57-1.57 4.14 0 5.71 1.57 1.57 4.14 1.57 5.71 0l1.43-1.43-1.43-1.42z" transform="rotate(45, 12, 12)"/></svg>');
        }
        .no-control {
            background-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="%23e53935"><path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm0 18c-4.42 0-8-3.58-8-8 0-1.85.63-3.55 1.69-4.9L16.9 18.31C15.55 19.37 13.85 20 12 20zm6.31-3.1L7.1 5.69C8.45 4.63 10.15 4 12 4c4.42 0 8 3.58 8 8 0 1.85-.63 3.55-1.69 4.9z"/></svg>');
        }
        .solution-arrow {
            display: flex;
            flex-direction: column;
            align-items: center;
            margin: 20px 0;
        }
        .arrow-icon {
            height: 60px;
            width: 60px;
            background-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="%234caf50"><path d="M20 12l-1.41-1.41L13 16.17V4h-2v12.17l-5.58-5.59L4 12l8 8 8-8z"/></svg>');
            background-size: contain;
            background-repeat: no-repeat;
            background-position: center;
        }
        .solution-text {
            font-weight: bold;
            font-size: 18px;
            color: #4caf50;
            margin-top: 10px;
        }
        .project-definition {
            display: flex;
            align-items: center;
            background-color: #e8f5e9;
            border-radius: 8px;
            padding: 20px;
            margin: 20px 0;
            box-shadow: 0 3px 6px rgba(0,0,0,0.1);
        }
        .definition-icon {
            height: 80px;
            width: 80px;
            margin-right: 20px;
            background-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="%234caf50"><path d="M12 11.55C9.64 9.35 6.48 8 3 8v11c3.48 0 6.64 1.35 9 3.55 2.36-2.19 5.52-3.55 9-3.55V8c-3.48 0-6.64 1.35-9 3.55zM12 8c1.66 0 3-1.34 3-3s-1.34-3-3-3-3 1.34-3 3 1.34 3 3 3z"/></svg>');
            background-size: contain;
            background-repeat: no-repeat;
            background-position: center;
        }
        .definition-content {
            flex: 1;
        }
        .definition-content h4 {
            color: #2e7d32;
            margin-top: 0;
            margin-bottom: 10px;
        }
        .definition-content p {
            margin: 0;
            color: #333;
        }
        .core-values-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(240px, 1fr));
            gap: 20px;
            margin: 20px 0;
        }
        .value-card {
            background-color: #f5f5f5;
            border-radius: 8px;
            padding: 20px;
            text-align: center;
            box-shadow: 0 3px 6px rgba(0,0,0,0.1);
            transition: transform 0.3s ease, box-shadow 0.3s ease;
        }
        .value-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }
        .value-icon {
            height: 60px;
            width: 60px;
            margin: 0 auto 15px;
            background-size: contain;
            background-repeat: no-repeat;
            background-position: center;
        }
        .value-title {
            font-weight: bold;
            font-size: 16px;
            color: #1976d2;
            margin-bottom: 10px;
        }
        .value-desc {
            font-size: 14px;
            color: #555;
        }
        .access {
            background-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="%231976d2"><path d="M12 17c1.1 0 2-.9 2-2s-.9-2-2-2-2 .9-2 2 .9 2 2 2zm6-9h-1V6c0-2.76-2.24-5-5-5S7 3.24 7 6v2H6c-1.1 0-2 .9-2 2v10c0 1.1.9 2 2 2h12c1.1 0 2-.9 2-2V10c0-1.1-.9-2-2-2zM8.9 6c0-1.71 1.39-3.1 3.1-3.1s3.1 1.39 3.1 3.1v2H8.9V6zM18 20H6V10h12v10z"/></svg>');
        }
        .cost {
            background-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="%231976d2"><path d="M11.8 10.9c-2.27-.59-3-1.2-3-2.15 0-1.09 1.01-1.85 2.7-1.85 1.78 0 2.44.85 2.5 2.1h2.21c-.07-1.72-1.12-3.3-3.21-3.81V3h-3v2.16c-1.94.42-3.5 1.68-3.5 3.61 0 2.31 1.91 3.46 4.7 4.13 2.5.6 3 1.48 3 2.41 0 .69-.49 1.79-2.7 1.79-2.06 0-2.87-.92-2.98-2.1h-2.2c.12 2.19 1.76 3.42 3.68 3.83V21h3v-2.15c1.95-.37 3.5-1.5 3.5-3.55 0-2.84-2.43-3.81-4.7-4.4z"/></svg>');
        }
        .permission {
            background-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="%231976d2"><path d="M12 1L3 5v6c0 5.55 3.84 10.74 9 12 5.16-1.26 9-6.45 9-12V5l-9-4zm0 10.99h7c-.53 4.12-3.28 7.79-7 8.94V12H5V6.3l7-3.11v8.8z"/></svg>');
        }
        .interop {
            background-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="%231976d2"><path d="M4 6h18V4H4c-1.1 0-2 .9-2 2v11H0v3h14v-3H4V6zm19 2h-6c-.55 0-1 .45-1 1v10c0 .55.45 1 1 1h6c.55 0 1-.45 1-1V9c0-.55-.45-1-1-1zm-1 9h-4v-7h4v7z"/></svg>');
        }
        .system-components {
            display: flex;
            flex-wrap: wrap;
            justify-content: space-between;
            margin: 20px 0;
        }
        .component-card {
            width: 30%;
            min-width: 250px;
            background-color: #e3f2fd;
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 20px;
            text-align: center;
            box-shadow: 0 3px 6px rgba(0,0,0,0.1);
            transition: transform 0.3s ease, box-shadow 0.3s ease;
        }
        .component-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }
        .component-icon {
            height: 60px;
            width: 60px;
            margin: 0 auto 15px;
            background-size: contain;
            background-repeat: no-repeat;
            background-position: center;
        }
        .component-title {
            font-weight: bold;
            font-size: 16px;
            color: #1976d2;
            margin-bottom: 10px;
        }
        .component-desc {
            font-size: 14px;
            color: #555;
        }
        .mobile-app {
            background-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="%231976d2"><path d="M17 1.01L7 1c-1.1 0-2 .9-2 2v18c0 1.1.9 2 2 2h10c1.1 0 2-.9 2-2V3c0-1.1-.9-1.99-2-1.99zM17 19H7V5h10v14z"/></svg>');
        }
        .cloud-sdk {
            background-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="%231976d2"><path d="M19.35 10.04C18.67 6.59 15.64 4 12 4 9.11 4 6.6 5.64 5.35 8.04 2.34 8.36 0 10.91 0 14c0 3.31 2.69 6 6 6h13c2.76 0 5-2.24 5-5 0-2.64-2.05-4.78-4.65-4.96zM19 18H6c-2.21 0-4-1.79-4-4s1.79-4 4-4h.71C7.37 7.69 9.48 6 12 6c3.04 0 5.5 2.46 5.5 5.5v.5H19c1.66 0 3 1.34 3 3s-1.34 3-3 3z"/></svg>');
        }
        .vehicle-hw {
            background-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="%231976d2"><path d="M18.92 6.01C18.72 5.42 18.16 5 17.5 5h-11c-.66 0-1.21.42-1.42 1.01L3 12v8c0 .55.45 1 1 1h1c.55 0 1-.45 1-1v-1h12v1c0 .55.45 1 1 1h1c.55 0 1-.45 1-1v-8l-2.08-5.99zM6.5 16c-.83 0-1.5-.67-1.5-1.5S5.67 13 6.5 13s1.5.67 1.5 1.5S7.33 16 6.5 16zm11 0c-.83 0-1.5-.67-1.5-1.5s.67-1.5 1.5-1.5 1.5.67 1.5 1.5-.67 1.5-1.5 1.5zM5 11l1.5-4.5h11L19 11H5z"/></svg>');
        }
        .use-cases-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(240px, 1fr));
            gap: 20px;
            margin: 20px 0;
        }
        .use-case-card {
            background-color: #f5f5f5;
            border-radius: 8px;
            padding: 20px;
            text-align: center;
            box-shadow: 0 3px 6px rgba(0,0,0,0.1);
            transition: transform 0.3s ease, box-shadow 0.3s ease;
        }
        .use-case-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }
        .use-case-icon {
            height: 60px;
            width: 60px;
            margin: 0 auto 15px;
            background-size: contain;
            background-repeat: no-repeat;
            background-position: center;
        }
        .use-case-title {
            font-weight: bold;
            font-size: 16px;
            color: #1976d2;
            margin-bottom: 10px;
        }
        .use-case-desc {
            font-size: 14px;
            color: #555;
        }
        .passive {
            background-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="%231976d2"><path d="M18.92 6.01C18.72 5.42 18.16 5 17.5 5h-11c-.66 0-1.21.42-1.42 1.01L3 12v8c0 .55.45 1 1 1h1c.55 0 1-.45 1-1v-1h12v1c0 .55.45 1 1 1h1c.55 0 1-.45 1-1v-8l-2.08-5.99zM6.5 16c-.83 0-1.5-.67-1.5-1.5S5.67 13 6.5 13s1.5.67 1.5 1.5S7.33 16 6.5 16zm11 0c-.83 0-1.5-.67-1.5-1.5s.67-1.5 1.5-1.5 1.5.67 1.5 1.5-.67 1.5-1.5 1.5zM5 11l1.5-4.5h11L19 11H5z"/></svg>');
        }
        .bluetooth {
            background-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="%231976d2"><path d="M17.71 7.71L12 2h-1v7.59L6.41 5 5 6.41 10.59 12 5 17.59 6.41 19 11 14.41V22h1l5.71-5.71-4.3-4.29 4.3-4.29zM13 5.83l1.88 1.88L13 9.59V5.83zm1.88 10.46L13 18.17v-3.76l1.88 1.88z"/></svg>');
        }
        .share {
            background-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="%231976d2"><path d="M18 16.08c-.76 0-1.44.3-1.96.77L8.91 12.7c.05-.23.09-.46.09-.7s-.04-.47-.09-.7l7.05-4.11c.54.5 1.25.81 2.04.81 1.66 0 3-1.34 3-3s-1.34-3-3-3-3 1.34-3 3c0 .24.04.47.09.7L8.04 9.81C7.5 9.31 6.79 9 6 9c-1.66 0-3 1.34-3 3s1.34 3 3 3c.79 0 1.5-.31 2.04-.81l7.12 4.16c-.05.21-.08.43-.08.65 0 1.61 1.31 2.92 2.92 2.92 1.61 0 2.92-1.31 2.92-2.92s-1.31-2.92-2.92-2.92z"/></svg>');
        }
        .remote {
            background-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="%231976d2"><path d="M12 1L3 5v6c0 5.55 3.84 10.74 9 12 5.16-1.26 9-6.45 9-12V5l-9-4zm0 10.99h7c-.53 4.12-3.28 7.79-7 8.94V12H5V6.3l7-3.11v8.8z"/></svg>');
        }
        .features-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(240px, 1fr));
            gap: 20px;
        }
        .module-container {
            display: flex;
            flex-wrap: wrap;
            gap: 20px;
            margin: 20px 0;
        }
        .module-device-icon {
            height: 60px;
            width: 60px;
            margin: 0 auto 15px;
            background-size: contain;
            background-repeat: no-repeat;
            background-position: center;
        }
        .module-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(240px, 1fr));
            gap: 20px;
        }
        .module-card {
            background-color: #f5f5f5;
            border-radius: 8px;
            padding: 20px;
            box-shadow: 0 3px 6px rgba(0,0,0,0.1);
            transition: transform 0.3s ease, box-shadow 0.3s ease;
        }
        .module-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 6px 12px rgba(0,0,0,0.15);
        }
        .module-icon {
            height: 60px;
            width: 60px;
            margin: 0 auto 15px;
            background-size: contain;
            background-repeat: no-repeat;
            background-position: center;
        }
        .module-title {
            font-weight: bold;
            font-size: 16px;
            color: #6a1b9a;
            margin-bottom: 10px;
            text-align: center;
        }
        .module-desc {
            font-size: 14px;
            color: #555;
            text-align: center;
        }
        .mobile {
            background-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="%231976d2"><path d="M17 1.01L7 1c-1.1 0-2 .9-2 2v18c0 1.1.9 2 2 2h10c1.1 0 2-.9 2-2V3c0-1.1-.9-1.99-2-1.99zM17 19H7V5h10v14z"/></svg>');
        }
        .vehicle {
            background-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="%231976d2"><path d="M18.92 6.01C18.72 5.42 18.16 5 17.5 5h-11c-.66 0-1.21.42-1.42 1.01L3 12v8c0 .55.45 1 1 1h1c.55 0 1-.45 1-1v-1h12v1c0 .55.45 1 1 1h1c.55 0 1-.45 1-1v-8l-2.08-5.99zM6.5 16c-.83 0-1.5-.67-1.5-1.5S5.67 13 6.5 13s1.5.67 1.5 1.5S7.33 16 6.5 16zm11 0c-.83 0-1.5-.67-1.5-1.5s.67-1.5 1.5-1.5 1.5.67 1.5 1.5-.67 1.5-1.5 1.5zM5 11l1.5-4.5h11L19 11H5z"/></svg>');
        }
        .cloud {
            background-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="%231976d2"><path d="M19.35 10.04C18.67 6.59 15.64 4 12 4 9.11 4 6.6 5.64 5.35 8.04 2.34 8.36 0 10.91 0 14c0 3.31 2.69 6 6 6h13c2.76 0 5-2.24 5-5 0-2.64-2.05-4.78-4.65-4.96zM19 18H6c-2.21 0-4-1.79-4-4s1.79-4 4-4h.71C7.37 7.69 9.48 6 12 6c3.04 0 5.5 2.46 5.5 5.5v.5H19c1.66 0 3 1.34 3 3s-1.34 3-3 3z"/></svg>');
        }
        .key-management {
            background-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="%236a1b9a"><path d="M12.65 10C11.83 7.67 9.61 6 7 6c-3.31 0-6 2.69-6 6s2.69 6 6 6c2.61 0 4.83-1.67 5.65-4H17v4h4v-4h2v-4H12.65zM7 14c-1.1 0-2-.9-2-2s.9-2 2-2 2 .9 2 2-.9 2-2 2z"/></svg>');
        }
        .vehicle-control {
            background-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="%236a1b9a"><path d="M18.92 6.01C18.72 5.42 18.16 5 17.5 5h-11c-.66 0-1.21.42-1.42 1.01L3 12v8c0 .55.45 1 1 1h1c.55 0 1-.45 1-1v-1h12v1c0 .55.45 1 1 1h1c.55 0 1-.45 1-1v-8l-2.08-5.99zM6.5 16c-.83 0-1.5-.67-1.5-1.5S5.67 13 6.5 13s1.5.67 1.5 1.5S7.33 16 6.5 16zm11 0c-.83 0-1.5-.67-1.5-1.5s.67-1.5 1.5-1.5 1.5.67 1.5 1.5-.67 1.5-1.5 1.5zM5 11l1.5-4.5h11L19 11H5z"/></svg>');
        }
        .bluetooth {
            background-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="%236a1b9a"><path d="M17.71 7.71L12 2h-1v7.59L6.41 5 5 6.41 10.59 12 5 17.59 6.41 19 11 14.41V22h1l5.71-5.71-4.3-4.29 4.3-4.29zM13 5.83l1.88 1.88L13 9.59V5.83zm1.88 10.46L13 18.17v-3.76l1.88 1.88z"/></svg>');
        }
        .secure-storage {
            background-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="%236a1b9a"><path d="M18 8h-1V6c0-2.76-2.24-5-5-5S7 3.24 7 6v2H6c-1.1 0-2 .9-2 2v10c0 1.1.9 2 2 2h12c1.1 0 2-.9 2-2V10c0-1.1-.9-2-2-2zm-6 9c-1.1 0-2-.9-2-2s.9-2 2-2 2 .9 2 2-.9 2-2 2zm3.1-9H8.9V6c0-1.71 1.39-3.1 3.1-3.1 1.71 0 3.1 1.39 3.1 3.1v2z"/></svg>');
        }
        .time-sync {
            background-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="%236a1b9a"><path d="M11.99 2C6.47 2 2 6.48 2 12s4.47 10 9.99 10C17.52 22 22 17.52 22 12S17.52 2 11.99 2zM12 20c-4.42 0-8-3.58-8-8s3.58-8 8-8 8 3.58 8 8-3.58 8-8 8zm.5-13H11v6l5.25 3.15.75-1.23-4.5-2.67z"/></svg>');
        }
        .key-exchange {
            background-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="%236a1b9a"><path d="M9.01 14H2v2h7.01v3L13 15l-3.99-4v3zm5.98-1v-3H22V8h-7.01V5L11 9l3.99 4z"/></svg>');
        }
        .key-verification {
            background-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="%236a1b9a"><path d="M12 1L3 5v6c0 5.55 3.84 10.74 9 12 5.16-1.26 9-6.45 9-12V5l-9-4zm-2 16l-4-4 1.41-1.41L10 14.17l6.59-6.59L18 9l-8 8z"/></svg>');
        }
        .command-execution {
            background-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="%236a1b9a"><path d="M19 3H5c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h14c1.1 0 2-.9 2-2V5c0-1.1-.9-2-2-2zm-5 14h-2V9h-2V7h4v10z"/></svg>');
        }
        .tbox-communication {
            background-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="%236a1b9a"><path d="M20.2 5.9l.8-.8C19.6 3.7 17.8 3 16 3s-3.6.7-5 2.1l.8.8C13 4.8 14.5 4.2 16 4.2s3 .6 4.2 1.7zm-.9.8c-.9-.9-2.1-1.4-3.3-1.4s-2.4.5-3.3 1.4l.8.8c.7-.7 1.6-1 2.5-1 .9 0 1.8.3 2.5 1l.8-.8zM19 13h-2V9h-2v4H5c-1.1 0-2 .9-2 2v4c0 1.1.9 2 2 2h14c1.1 0 2-.9 2-2v-4c0-1.1-.9-2-2-2zM8 18H6v-2h2v2zm3.5 0h-2v-2h2v2zm3.5 0h-2v-2h2v2z"/></svg>');
        }
        .key-lifecycle {
            background-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="%236a1b9a"><path d="M12 6v3l4-4-4-4v3c-4.42 0-8 3.58-8 8 0 1.57.46 3.03 1.24 4.26L6.7 14.8c-.45-.83-.7-1.79-.7-2.8 0-3.31 2.69-6 6-6zm6.76 1.74L17.3 9.2c.44.84.7 1.79.7 2.8 0 3.31-2.69 6-6 6v-3l-4 4 4 4v-3c4.42 0 8-3.58 8-8 0-1.57-.46-3.03-1.24-4.26z"/></svg>');
        }
        .vin-service {
            background-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="%236a1b9a"><path d="M18.92 5.01C18.72 4.42 18.16 4 17.5 4h-11c-.66 0-1.21.42-1.42 1.01L3 11v8c0 .55.45 1 1 1h1c.55 0 1-.45 1-1v-1h12v1c0 .55.45 1 1 1h1c.55 0 1-.45 1-1v-8l-2.08-5.99zM6.85 6h10.29l1.08 3.11H5.77L6.85 6zM19 17H5v-5h14v5z"/></svg>');
        }
        .auth-center {
            background-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="%236a1b9a"><path d="M12 1L3 5v6c0 5.55 3.84 10.74 9 12 5.16-1.26 9-6.45 9-12V5l-9-4zm0 10.99h7c-.53 4.12-3.28 7.79-7 8.94V12H5V6.3l7-3.11v8.8z"/></svg>');
        }
        .key-management-system {
            background-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="%236a1b9a"><path d="M7 14c-1.66 0-3 1.34-3 3 0 1.31-1.16 2-2 2 .92 1.22 2.49 2 4 2 2.21 0 4-1.79 4-4 0-1.66-1.34-3-3-3zm13.71-9.37l-1.34-1.34c-.39-.39-1.02-.39-1.41 0L9 12.25 11.75 15l8.96-8.96c.39-.39.39-1.02 0-1.41z"/></svg>');
        }
        .time-server {
            background-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="%236a1b9a"><path d="M22 11h-4.17l3.24-3.24-1.41-1.42L15 11h-2V9l4.66-4.66-1.42-1.41L13 6.17V2h-2v4.17L7.76 2.93 6.34 4.34 11 9v2H9L4.34 6.34 2.93 7.76 6.17 11H2v2h4.17l-3.24 3.24 1.41 1.42L9 13h2v2l-4.66 4.66 1.42 1.41L11 17.83V22h2v-4.17l3.24 3.24 1.42-1.41L13 15v-2h2l4.66 4.66 1.41-1.42L17.83 13H22z"/></svg>');
        }
        .api-layer {
            background-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="%236a1b9a"><path d="M20 4H4c-1.1 0-1.99.9-1.99 2L2 18c0 1.1.9 2 2 2h16c1.1 0 2-.9 2-2V6c0-1.1-.9-2-2-2zm-5 14H4v-4h11v4zm0-5H4V9h11v4zm5 5h-4V9h4v9z"/></svg>');
        }
        .compact-modules {
            display: flex;
            flex-wrap: wrap;
            gap: 20px;
            margin-bottom: 30px;
        }
        .module-platform {
            width: 100%;
            margin-bottom: 10px;
            display: flex;
            align-items: center;
        }
        .platform-icon {
            width: 40px;
            height: 40px;
            margin-right: 10px;
            background-size: contain;
            background-repeat: no-repeat;
            background-position: center;
        }
        .platform-title {
            font-size: 20px;
            font-weight: bold;
            color: #333;
        }
        .compact-grid {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            gap: 15px;
            width: 100%;
        }
        .compact-card {
            background-color: #f5f5f5;
            border-radius: 8px;
            padding: 15px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            display: flex;
            align-items: center;
            transition: transform 0.2s ease, box-shadow 0.2s ease;
        }
        .compact-card:hover {
            transform: translateY(-3px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.15);
        }
        .compact-icon {
            width: 36px;
            height: 36px;
            min-width: 36px;
            margin-right: 10px;
            background-size: contain;
            background-repeat: no-repeat;
            background-position: center;
        }
        .compact-content {
            display: flex;
            flex-direction: column;
        }
        .compact-title {
            font-weight: bold;
            font-size: 15px;
            color: #1976d2;
            margin-bottom: 5px;
        }
        .compact-desc {
            font-size: 13px;
            color: #555;
            margin: 0;
        }
    </style>
</head>
<body>
    <!-- 第1页：封面 -->
    <header>
        <h1>数字钥匙系统技术架构</h1>
        <p>完整的汽车数字钥匙解决方案</p>
        <p>2023年</p>
        <div class="logo-placeholder">[公司Logo预留位置]</div>
    </header>

    <!-- 第2页：目录 -->
    <nav>
        <h3>目录</h3>
        <ul>
            <li><a href="#overview">1. 系统概述</a></li>
            <li><a href="#architecture">2. 系统架构</a></li>
            <li><a href="#components">3. 核心组件</a></li>
            <li><a href="#process">4. 关键流程</a></li>
            <li><a href="#security">5. 安全策略设计</a></li>
            <li><a href="#challenges">6. 技术难点与解决方案</a></li>
            <li><a href="#integration">7. 平台交互与集成</a></li>
        </ul>
    </nav>

    <!-- 第3页：项目概述 -->
    <section id="overview">
        <h2>1. 项目概述</h2>
        
        <h3>1.1 背景介绍</h3>
        <div class="background-visual">
            <div class="pain-points-container">
                <h4>传统钥匙痛点</h4>
                <div class="pain-points-grid">
                    <div class="pain-point-card">
                        <div class="pain-icon lost"></div>
                        <div class="pain-title">易丢失</div>
                        <div class="pain-desc">物理钥匙容易丢失，造成安全隐患</div>
                    </div>
                    <div class="pain-point-card">
                        <div class="pain-icon inconvenient"></div>
                        <div class="pain-title">不便携带</div>
                        <div class="pain-desc">体积较大，携带不便</div>
                    </div>
                    <div class="pain-point-card">
                        <div class="pain-icon no-sharing"></div>
                        <div class="pain-title">无法远程共享</div>
                        <div class="pain-desc">需要物理接触才能共享使用权</div>
                    </div>
                    <div class="pain-point-card">
                        <div class="pain-icon no-control"></div>
                        <div class="pain-title">无法远程控制</div>
                        <div class="pain-desc">不支持远程操作车辆功能</div>
                    </div>
                </div>
            </div>
            <div class="solution-arrow">
                <div class="arrow-icon"></div>
                <div class="solution-text">数字钥匙解决方案</div>
            </div>
        </div>
        
        <h3>1.2 项目定义</h3>
        <div class="project-definition">
            <div class="definition-icon"></div>
            <div class="definition-content">
                <h4>数字钥匙系统</h4>
                <p>一套完整的汽车数字钥匙解决方案，旨在取代传统的物理钥匙，提供更安全、便捷的车辆访问和控制机制。</p>
            </div>
        </div>
        
        <h3>1.3 核心价值</h3>
        <div class="module-grid">
            <div class="module-card">
                <div class="module-icon key-verification"></div>
                <h4 class="module-title">便捷访问控制</h4>
                <p class="module-desc">提供便捷的车辆访问控制，同时保障安全性，支持多种认证方式和控制模式。</p>
            </div>
            <div class="module-card">
                <div class="module-icon key-management-system"></div>
                <h4 class="module-title">降低管理成本</h4>
                <p class="module-desc">减少物理钥匙的制作、分发和管理成本，实现数字化管理和自动化运维。</p>
            </div>
            <div class="module-card">
                <div class="module-icon auth-center"></div>
                <h4 class="module-title">灵活权限控制</h4>
                <p class="module-desc">实现灵活的权限控制与实时监控，支持细粒度的授权管理和权限分级。</p>
            </div>
            <div class="module-card">
                <div class="module-icon api-layer"></div>
                <h4 class="module-title">多设备互操作</h4>
                <p class="module-desc">支持多种设备与场景的互操作性，提供标准化接口和协议，便于集成和扩展。</p>
            </div>
        </div>
        
        <h3>1.4 系统组成</h3>
        <div class="module-container">
            <div class="module-card">
                <div class="module-device-icon mobile"></div>
                <h4 class="module-title">手机APP及SDK</h4>
                <p class="module-desc">用户交互界面和开发工具包，提供便捷的车辆控制和钥匙管理功能，支持Android和iOS平台。</p>
            </div>
            <div class="module-card">
                <div class="module-device-icon cloud"></div>
                <h4 class="module-title">云平台SDK</h4>
                <p class="module-desc">云服务接口和开发工具，提供钥匙生命周期管理、认证授权、安全通信等核心服务。</p>
            </div>
            <div class="module-card">
                <div class="module-device-icon vehicle"></div>
                <h4 class="module-title">车端软硬件</h4>
                <p class="module-desc">车辆控制系统和通信模块，包括蓝牙通信、安全存储、指令执行等功能组件。</p>
            </div>
        </div>
        
        <h3>1.5 典型应用场景</h3>
        <div class="module-grid">
            <div class="module-card">
                <div class="module-icon vehicle-control"></div>
                <h4 class="module-title">无感控车</h4>
                <p class="module-desc">接近车辆自动开锁，离开自动上锁，提供便捷的用户体验，无需手动操作手机。</p>
            </div>
            <div class="module-card">
                <div class="module-icon bluetooth"></div>
                <h4 class="module-title">蓝牙控车</h4>
                <p class="module-desc">通过手机蓝牙直接控制车辆，实现开关锁、启动等功能，无需网络连接。</p>
            </div>
            <div class="module-card">
                <div class="module-icon key-exchange"></div>
                <h4 class="module-title">远程共享钥匙</h4>
                <p class="module-desc">远程授权他人使用车辆，可设置使用时间、权限范围等，灵活管理车辆使用权。</p>
            </div>
            <div class="module-card">
                <div class="module-icon command-execution"></div>
                <h4 class="module-title">远程控制车辆</h4>
                <p class="module-desc">远程启动、开关锁等操作，通过网络随时随地控制车辆，提供便捷的用户体验。</p>
            </div>
        </div>
    </section>

    <!-- 第4页：系统架构总览 -->
    <section id="architecture">
        <h2>2. 系统架构总览</h2>
        
        <div class="architecture-diagram">
            <h4>系统整体架构图</h4>
            <div style="background: white; padding: 20px; border-radius: 10px; box-shadow: 0 2px 10px rgba(0,0,0,0.1);">
                <h3 style="text-align: center; color: #333;">数字钥匙系统架构</h3>
                <p style="text-align: center; color: #666; margin-bottom: 30px;">本架构基于车端、云端及移动端三层设计，实现安全、便捷的数字车钥匙服务</p>
                
                <!-- 钥匙云平台 -->
                <div style="border: 2px dashed #6750A4; border-radius: 10px; padding: 15px; margin-bottom: 25px; background: #e3f2fd;">
                    <h4 style="color: #6750A4; margin-top: 0;">钥匙云平台</h4>
                    <div style="display: flex; flex-wrap: wrap; gap: 10px; justify-content: space-between;">
                        <div style="background: #a6eba6; padding: 10px; border-radius: 5px; width: 22%; text-align: center; border: 1px solid #007700;">
                            钥匙生命周期管理
                        </div>
                        <div style="background: #a6eba6; padding: 10px; border-radius: 5px; width: 22%; text-align: center; border: 1px solid #007700;">
                            车辆关联服务
                        </div>
                        <div style="background: #a6eba6; padding: 10px; border-radius: 5px; width: 22%; text-align: center; border: 1px solid #007700;">
                            安全认证中心
                        </div>
                        <div style="background: #a6eba6; padding: 10px; border-radius: 5px; width: 22%; text-align: center; border: 1px solid #007700;">
                            异常监控与处理
                        </div>
                        <div style="background: #a6eba6; padding: 10px; border-radius: 5px; width: 22%; text-align: center; border: 1px solid #007700;">
                            密钥管理系统
                        </div>
                        <div style="background: #a6eba6; padding: 10px; border-radius: 5px; width: 22%; text-align: center; border: 1px solid #007700;">
                            统一接口服务
                        </div>
                        <div style="background: #a6eba6; padding: 10px; border-radius: 5px; width: 22%; text-align: center; border: 1px solid #007700;">
                            时间服务器
                        </div>
                        <div style="background: #a6eba6; padding: 10px; border-radius: 5px; width: 22%; text-align: center; border: 1px solid #007700;">
                            安全通信通道
                        </div>
                    </div>
                    
                    <div style="text-align: right; margin-top: 10px;">
                        <div style="display: inline-block; background: #b3d9ff; padding: 10px; border-radius: 5px; text-align: center; border: 1px solid #333; margin-left: auto;">
                            外部平台<br>(TSP/OEM)
                            <span style="display: inline-block; margin-left: 10px; color: #1976d2;">←</span>
                        </div>
                    </div>
                </div>
                
                <div style="display: flex; gap: 25px; margin-bottom: 25px;">
                    <!-- 数据存储 -->
                    <div style="border: 2px dashed #6750A4; border-radius: 10px; padding: 15px; flex: 1; background: #fff3e0;">
                        <h4 style="color: #6750A4; margin-top: 0;">数据存储</h4>
                        <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 10px;">
                            <div style="background: #ffe0b3; padding: 10px; border-radius: 5px; text-align: center; border: 1px solid #996600;">
                                钥匙数据
                            </div>
                            <div style="background: #ffe0b3; padding: 10px; border-radius: 5px; text-align: center; border: 1px solid #996600;">
                                密钥材料
                            </div>
                            <div style="background: #ffe0b3; padding: 10px; border-radius: 5px; text-align: center; border: 1px solid #996600;">
                                通信记录
                            </div>
                            <div style="background: #ffe0b3; padding: 10px; border-radius: 5px; text-align: center; border: 1px solid #996600;">
                                操作日志
                            </div>
                        </div>
                    </div>
                    
                    <!-- 安全基础设施 -->
                    <div style="border: 2px dashed #6750A4; border-radius: 10px; padding: 15px; flex: 1; background: #fff3e0;">
                        <h4 style="color: #6750A4; margin-top: 0;">安全基础设施</h4>
                        <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 10px;">
                            <div style="background: #ffe0b3; padding: 10px; border-radius: 5px; text-align: center; border: 1px solid #996600;">
                                密码机<br>(HSM)
                            </div>
                            <div style="background: #ffe0b3; padding: 10px; border-radius: 5px; text-align: center; border: 1px solid #996600;">
                                证书系统<br>(PKI/CA)
                            </div>
                            <div style="background: #ffe0b3; padding: 10px; border-radius: 5px; text-align: center; border: 1px solid #996600;">
                                密钥备份系统
                            </div>
                            <div style="background: #ffe0b3; padding: 10px; border-radius: 5px; text-align: center; border: 1px solid #996600;">
                                安全监控
                            </div>
                            <div style="background: #ffe0b3; padding: 10px; border-radius: 5px; text-align: center; border: 1px solid #996600; grid-column: span 2;">
                                异常处理机制
                            </div>
                        </div>
                    </div>
                </div>
                
                <div style="display: flex; gap: 25px;">
                    <!-- 移动智能终端 -->
                    <div style="border: 2px dashed #6750A4; border-radius: 10px; padding: 15px; flex: 1; background: #e8f5e9;">
                        <h4 style="color: #6750A4; margin-top: 0;">移动智能终端</h4>
                        <div style="text-align: center; margin-bottom: 15px;">
                            <div style="background: #b3d9ff; width: 60px; height: 100px; margin: 0 auto; border-radius: 10px; border: 1px solid #333; position: relative;">
                                <div style="position: absolute; top: 10px; left: 50%; transform: translateX(-50%); width: 8px; height: 8px; background: #333; border-radius: 50%;"></div>
                            </div>
                        </div>
                        <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 10px;">
                            <div style="background: #a6eba6; padding: 10px; border-radius: 5px; text-align: center; border: 1px solid #007700;">
                                钥匙管理模块
                            </div>
                            <div style="background: #a6eba6; padding: 10px; border-radius: 5px; text-align: center; border: 1px solid #007700;">
                                车辆控制模块
                            </div>
                            <div style="background: #a6eba6; padding: 10px; border-radius: 5px; text-align: center; border: 1px solid #007700;">
                                蓝牙通信模块
                            </div>
                            <div style="background: #a6eba6; padding: 10px; border-radius: 5px; text-align: center; border: 1px solid #007700;">
                                安全存储模块
                            </div>
                            <div style="background: #a6eba6; padding: 10px; border-radius: 5px; text-align: center; border: 1px solid #007700;">
                                时间同步模块
                            </div>
                            <div style="background: #a6eba6; padding: 10px; border-radius: 5px; text-align: center; border: 1px solid #007700;">
                                暗号交换模块
                            </div>
                            <div style="background: #a6eba6; padding: 10px; border-radius: 5px; text-align: center; border: 1px solid #007700;">
                                异常处理模块
                            </div>
                            <div style="background: #a6eba6; padding: 10px; border-radius: 5px; text-align: center; border: 1px solid #007700;">
                                智能场景管理模块
                            </div>
                        </div>
                    </div>
                    
                    <!-- 车辆终端 -->
                    <div style="border: 2px dashed #6750A4; border-radius: 10px; padding: 15px; flex: 1; background: #e8f5e9;">
                        <h4 style="color: #6750A4; margin-top: 0;">车辆终端</h4>
                        <div style="text-align: center; margin-bottom: 15px;">
                            <div style="width: 120px; height: 60px; margin: 0 auto; position: relative;">
                                <div style="position: absolute; top: 15px; left: 0; right: 0; height: 30px; background: #b3d9ff; border-radius: 30px 30px 0 0; border: 1px solid #333;"></div>
                                <div style="position: absolute; bottom: 0; left: 10px; right: 10px; height: 15px; background: #b3d9ff; border: 1px solid #333; border-radius: 0 0 5px 5px;"></div>
                                <div style="position: absolute; bottom: 0; left: 20px; width: 20px; height: 20px; border-radius: 50%; border: 1px solid #333; background: white;"></div>
                                <div style="position: absolute; bottom: 0; right: 20px; width: 20px; height: 20px; border-radius: 50%; border: 1px solid #333; background: white;"></div>
                            </div>
                        </div>
                        <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 10px;">
                            <div style="background: #a6eba6; padding: 10px; border-radius: 5px; text-align: center; border: 1px solid #007700;">
                                蓝牙通信模块
                            </div>
                            <div style="background: #a6eba6; padding: 10px; border-radius: 5px; text-align: center; border: 1px solid #007700;">
                                钥匙验证模块
                            </div>
                            <div style="background: #a6eba6; padding: 10px; border-radius: 5px; text-align: center; border: 1px solid #007700;">
                                远程通信模块
                            </div>
                            <div style="background: #a6eba6; padding: 10px; border-radius: 5px; text-align: center; border: 1px solid #007700;">
                                指令执行模块
                            </div>
                            <div style="background: #a6eba6; padding: 10px; border-radius: 5px; text-align: center; border: 1px solid #007700;">
                                安全存储模块
                            </div>
                            <div style="background: #a6eba6; padding: 10px; border-radius: 5px; text-align: center; border: 1px solid #007700;">
                                时间同步模块
                            </div>
                            <div style="background: #a6eba6; padding: 10px; border-radius: 5px; text-align: center; border: 1px solid #007700;">
                                暗号交换模块
                            </div>
                            <div style="background: #a6eba6; padding: 10px; border-radius: 5px; text-align: center; border: 1px solid #007700;">
                                用户行为分析模块
                            </div>
                            <div style="background: #a6eba6; padding: 10px; border-radius: 5px; text-align: center; border: 1px solid #007700; grid-column: span 2;">
                                异常处理模块
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- 图例 -->
                <div style="margin-top: 25px; display: flex; flex-wrap: wrap; gap: 15px;">
                    <div style="display: flex; align-items: center;">
                        <div style="width: 20px; height: 20px; background: #a6eba6; border: 1px solid #007700; margin-right: 5px;"></div>
                        <span>核心功能模块</span>
                    </div>
                    <div style="display: flex; align-items: center;">
                        <div style="width: 20px; height: 20px; background: #ffe0b3; border: 1px solid #996600; margin-right: 5px;"></div>
                        <span>存储与安全组件</span>
                    </div>
                    <div style="display: flex; align-items: center;">
                        <div style="width: 40px; height: 20px; border: 2px dashed #6750A4; margin-right: 5px;"></div>
                        <span>系统边界</span>
                    </div>
                    <div style="display: flex; align-items: center;">
                        <div style="color: #1976d2; margin-right: 5px;">→</div>
                        <span>数据流向</span>
                    </div>
                </div>
                
                <p style="text-align: center; margin-top: 20px; font-style: italic; color: #666;">
                    数字钥匙系统架构图 - 展示了移动端、云端与车辆端三大系统的核心组件及其交互关系
                </p>
            </div>
        </div>

        <h3>2.1 系统概述</h3>
        <p>数字车钥匙系统是一种结合移动互联网、安全芯片与车载通信技术的现代化车辆访问控制解决方案，系统由三大核心部分构成：</p>

        <div class="component-container">
            <div class="component">
                <h4>移动智能终端（移动端）</h4>
                <p>作为用户交互的主要入口，实现钥匙管理与车辆控制功能：</p>
                <ul>
                    <li><strong>钥匙管理模块</strong>：管理数字钥匙的添加、删除和共享功能</li>
                    <li><strong>车辆控制模块</strong>：实现开锁、关锁、启动等车辆控制功能</li>
                    <li><strong>蓝牙通信模块</strong>：负责与车端建立蓝牙安全连接</li>
                    <li><strong>安全存储模块</strong>：在TEE/SE安全环境中存储密钥材料和凭证</li>
                    <li><strong>时间同步模块</strong>：与云端同步精确时间，为安全操作提供可信时间戳</li>
                    <li><strong>暗号交换模块</strong>：负责与车端的密钥协商和会话密钥更新</li>
                    <li><strong>异常处理模块</strong>：处理连接异常、认证失败等情况</li>
                </ul>
            </div>
            
            <div class="component">
                <h4>云端服务</h4>
                <p>作为系统的控制中心，负责钥匙全生命周期管理与权限验证：</p>
                <ul>
                    <li><strong>钥匙生命周期管理</strong>：负责钥匙的创建、授权和撤销</li>
                    <li><strong>车辆关联服务</strong>：管理车辆信息和VIN码关联</li>
                    <li><strong>安全认证中心</strong>：提供身份验证和授权服务</li>
                    <li><strong>密钥管理系统</strong>：管理系统根密钥和密钥派生流程</li>
                    <li><strong>统一接口服务</strong>：提供标准化的API接口</li>
                    <li><strong>时间服务器</strong>：为整个系统提供精确时间基准</li>
                    <li><strong>异常监控与处理</strong>：监控异常行为并采取应对措施</li>
                </ul>
            </div>
            
            <div class="component">
                <h4>车辆终端</h4>
                <p>安装在车辆上的钥匙识别及执行系统：</p>
                <ul>
                    <li><strong>蓝牙通信模块</strong>：与手机建立安全的蓝牙连接</li>
                    <li><strong>钥匙验证模块</strong>：验证数字钥匙的有效性和权限</li>
                    <li><strong>远程通信模块</strong>：通过4G/5G与云端通信</li>
                    <li><strong>指令执行模块</strong>：执行验证通过的控制指令</li>
                    <li><strong>安全存储模块</strong>：在安全环境中存储密钥和配置</li>
                    <li><strong>时间同步模块</strong>：与云端同步标准时间</li>
                    <li><strong>暗号交换模块</strong>：负责安全通道建立和密钥协商</li>
                    <li><strong>用户行为分析模块</strong>：实现无感控车的核心算法，分析距离和用户行为</li>
                    <li><strong>异常处理模块</strong>：处理异常情况并实施安全措施</li>
                </ul>
            </div>
        </div>

        <h3>2.2 关键技术特点</h3>
        <div class="tech-features-grid">
            <div class="tech-feature-card">
                <div class="feature-icon bluetooth-secure"></div>
                <div class="feature-title">蓝牙安全通信</div>
                <div class="feature-desc">基于BLE技术的安全通信机制，支持无感连接和主动控制</div>
            </div>
            <div class="tech-feature-card">
                <div class="feature-icon secure-arch"></div>
                <div class="feature-title">安全架构</div>
                <div class="feature-desc">基于硬件安全芯片和可信执行环境(TEE)建立的多层次安全体系</div>
            </div>
            <div class="tech-feature-card">
                <div class="feature-icon key-exchange"></div>
                <div class="feature-title">暗号交换</div>
                <div class="feature-desc">采用高强度密钥协商算法，确保通信安全</div>
            </div>
            <div class="tech-feature-card">
                <div class="feature-icon passive-control"></div>
                <div class="feature-title">无感控车</div>
                <div class="feature-desc">基于精确距离计算的自动控车功能，优化用户体验</div>
            </div>
            <div class="tech-feature-card">
                <div class="feature-icon low-power"></div>
                <div class="feature-title">低功耗设计</div>
                <div class="feature-desc">针对无感场景的优化电量管理策略</div>
            </div>
            <div class="tech-feature-card">
                <div class="feature-icon offline"></div>
                <div class="feature-title">离线可用</div>
                <div class="feature-desc">支持在无网络环境下使用钥匙功能</div>
            </div>
        </div>
        
        <h3>2.3 数据流与通信</h3>
        <p>系统采用分层通信架构，确保数据交换的安全性与效率：</p>
        <div class="data-flow-container">
            <div class="data-flow-step">
                <div class="flow-number">1</div>
                <div class="flow-content">
                    <div class="flow-title">云端交互</div>
                    <div class="flow-desc">钥匙云平台与TSP/OEM平台之间通过安全API实现数据交换，同步车辆信息与用户权限</div>
                </div>
                <div class="flow-icon cloud-interaction"></div>
            </div>
            <div class="data-flow-step">
                <div class="flow-number">2</div>
                <div class="flow-content">
                    <div class="flow-title">钥匙管理流</div>
                    <div class="flow-desc">移动终端通过互联网与云端服务交互，完成钥匙申请、激活与管理</div>
                </div>
                <div class="flow-icon key-management"></div>
            </div>
            <div class="data-flow-step">
                <div class="flow-number">3</div>
                <div class="flow-content">
                    <div class="flow-title">钥匙验证流</div>
                    <div class="flow-desc">通过蓝牙技术实现主动控制和无感模式，基于距离和用户行为自动执行操作</div>
                </div>
                <div class="flow-icon key-verification"></div>
            </div>
            <div class="data-flow-step">
                <div class="flow-number">4</div>
                <div class="flow-content">
                    <div class="flow-title">车辆控制流</div>
                    <div class="flow-desc">验证通过后，车辆终端执行对应的控制指令（开锁、关锁、启动等）</div>
                </div>
                <div class="flow-icon vehicle-control"></div>
            </div>
            <div class="data-flow-step">
                <div class="flow-number">5</div>
                <div class="flow-content">
                    <div class="flow-title">异常处理流</div>
                    <div class="flow-desc">当出现通信中断、验证失败等情况时，系统自动执行预设的安全策略</div>
                </div>
                <div class="flow-icon exception-handling"></div>
            </div>
        </div>
        
        <h3>2.4 安全保障体系</h3>
        <p>系统采用多层次安全架构，确保数字钥匙的可靠性与安全性：</p>
        <div class="security-system-grid">
            <div class="security-card">
                <div class="security-icon hardware"></div>
                <div class="security-title">硬件安全</div>
                <div class="security-desc">云端HSM、移动端TEE/SE、车辆端安全芯片构成的硬件安全链</div>
            </div>
            <div class="security-card">
                <div class="security-icon communication"></div>
                <div class="security-title">通信安全</div>
                <div class="security-desc">TLS加密传输、会话密钥、安全信道</div>
            </div>
            <div class="security-card">
                <div class="security-icon time-sync"></div>
                <div class="security-title">时间同步机制</div>
                <div class="security-desc">精确时间同步确保操作的时效性，防止重放攻击</div>
            </div>
            <div class="security-card">
                <div class="security-icon relay-protection"></div>
                <div class="security-title">防中继保护</div>
                <div class="security-desc">距离计算和用户行为分析阻止中继攻击</div>
            </div>
            <div class="security-card">
                <div class="security-icon mutual-auth"></div>
                <div class="security-title">双向认证</div>
                <div class="security-desc">确保手机和车辆双方身份的真实性</div>
            </div>
            <div class="security-card">
                <div class="security-icon key-lifecycle"></div>
                <div class="security-title">密钥生命周期管理</div>
                <div class="security-desc">定期更新会话密钥，撤销失效密钥</div>
            </div>
        </div>
    </section>

    <!-- 第5-7页：核心模块功能 -->
    <section id="core-modules">
        <h2>3. 核心模块功能</h2>
        
        <style>
            .compact-modules {
                display: flex;
                flex-wrap: wrap;
                gap: 20px;
                margin-bottom: 30px;
            }
            .module-platform {
                width: 100%;
                margin-bottom: 10px;
                display: flex;
                align-items: center;
            }
            .platform-icon {
                width: 40px;
                height: 40px;
                margin-right: 10px;
                background-size: contain;
                background-repeat: no-repeat;
                background-position: center;
            }
            .platform-title {
                font-size: 20px;
                font-weight: bold;
                color: #333;
            }
            .compact-grid {
                display: grid;
                grid-template-columns: repeat(3, 1fr);
                gap: 15px;
                width: 100%;
            }
            .compact-card {
                background-color: #f5f5f5;
                border-radius: 8px;
                padding: 15px;
                box-shadow: 0 2px 4px rgba(0,0,0,0.1);
                display: flex;
                align-items: center;
                transition: transform 0.2s ease, box-shadow 0.2s ease;
            }
            .compact-card:hover {
                transform: translateY(-3px);
                box-shadow: 0 4px 8px rgba(0,0,0,0.15);
            }
            .compact-icon {
                width: 36px;
                height: 36px;
                min-width: 36px;
                margin-right: 10px;
                background-size: contain;
                background-repeat: no-repeat;
                background-position: center;
            }
            .compact-content {
                display: flex;
                flex-direction: column;
            }
            .compact-title {
                font-weight: bold;
                font-size: 15px;
                color: #1976d2;
                margin-bottom: 5px;
            }
            .compact-desc {
                font-size: 13px;
                color: #555;
                margin: 0;
            }
        </style>

        <!-- 手机端 -->
        <div class="compact-modules">
            <div class="module-platform">
                <div class="platform-icon mobile"></div>
                <h3 class="platform-title">3.1 手机端</h3>
            </div>
            <div class="compact-grid">
                <div class="compact-card">
                    <div class="compact-icon key-management"></div>
                    <div class="compact-content">
                        <div class="compact-title">钥匙管理模块</div>
                        <div class="compact-desc">添加、删除、共享数字钥匙，管理钥匙权限和有效期</div>
                    </div>
                </div>
                <div class="compact-card">
                    <div class="compact-icon vehicle-control"></div>
                    <div class="compact-content">
                        <div class="compact-title">车辆控制模块</div>
                        <div class="compact-desc">开锁、关锁、启动等功能，支持近场和远程控制</div>
                    </div>
                </div>
                <div class="compact-card">
                    <div class="compact-icon bluetooth"></div>
                    <div class="compact-content">
                        <div class="compact-title">蓝牙通信模块</div>
                        <div class="compact-desc">与车端进行近场通信，支持BLE协议与安全连接</div>
                    </div>
                </div>
                <div class="compact-card">
                    <div class="compact-icon secure-storage"></div>
                    <div class="compact-content">
                        <div class="compact-title">安全存储模块</div>
                        <div class="compact-desc">利用TEE/SE安全区存储数字钥匙和密钥材料</div>
                    </div>
                </div>
                <div class="compact-card">
                    <div class="compact-icon time-sync"></div>
                    <div class="compact-content">
                        <div class="compact-title">时间同步模块</div>
                        <div class="compact-desc">确保认证时间准确性，防止重放攻击</div>
                    </div>
                </div>
                <div class="compact-card">
                    <div class="compact-icon key-exchange"></div>
                    <div class="compact-content">
                        <div class="compact-title">暗号交换模块</div>
                        <div class="compact-desc">确保配对设备间安全通信与密钥协商</div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 车端 -->
        <div class="compact-modules">
            <div class="module-platform">
                <div class="platform-icon vehicle"></div>
                <h3 class="platform-title">3.2 车端</h3>
            </div>
            <div class="compact-grid">
                <div class="compact-card">
                    <div class="compact-icon bluetooth"></div>
                    <div class="compact-content">
                        <div class="compact-title">BLE通信模块</div>
                        <div class="compact-desc">与手机进行蓝牙通信，支持低功耗模式</div>
                    </div>
                </div>
                <div class="compact-card">
                    <div class="compact-icon key-verification"></div>
                    <div class="compact-content">
                        <div class="compact-title">钥匙验证模块</div>
                        <div class="compact-desc">验证数字钥匙的有效性和权限，执行安全认证</div>
                    </div>
                </div>
                <div class="compact-card">
                    <div class="compact-icon command-execution"></div>
                    <div class="compact-content">
                        <div class="compact-title">指令执行模块</div>
                        <div class="compact-desc">执行控制指令，与车辆CAN总线交互</div>
                    </div>
                </div>
                <div class="compact-card">
                    <div class="compact-icon secure-storage"></div>
                    <div class="compact-content">
                        <div class="compact-title">安全存储模块</div>
                        <div class="compact-desc">存储密钥材料和配置，使用安全芯片保护</div>
                    </div>
                </div>
                <div class="compact-card">
                    <div class="compact-icon tbox-communication"></div>
                    <div class="compact-content">
                        <div class="compact-title">T-Box通信模块</div>
                        <div class="compact-desc">与TSP平台通信，接收远程控制指令</div>
                    </div>
                </div>
                <div class="compact-card">
                    <div class="compact-icon time-sync"></div>
                    <div class="compact-content">
                        <div class="compact-title">时间同步模块</div>
                        <div class="compact-desc">确保系统时间同步，防止重放攻击</div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 钥匙云平台 -->
        <div class="compact-modules">
            <div class="module-platform">
                <div class="platform-icon cloud"></div>
                <h3 class="platform-title">3.3 钥匙云平台</h3>
            </div>
            <div class="compact-grid">
                <div class="compact-card">
                    <div class="compact-icon key-lifecycle"></div>
                    <div class="compact-content">
                        <div class="compact-title">钥匙生命周期管理</div>
                        <div class="compact-desc">负责钥匙的创建、授权、撤销和更新管理</div>
                    </div>
                </div>
                <div class="compact-card">
                    <div class="compact-icon vin-service"></div>
                    <div class="compact-content">
                        <div class="compact-title">车辆信息关联</div>
                        <div class="compact-desc">管理车辆信息和VIN码关联，验证车辆身份</div>
                    </div>
                </div>
                <div class="compact-card">
                    <div class="compact-icon api-layer"></div>
                    <div class="compact-content">
                        <div class="compact-title">API接口服务</div>
                        <div class="compact-desc">提供标准化API接口，支持与TSP/OEM平台集成</div>
                    </div>
                </div>
                <div class="compact-card">
                    <div class="compact-icon auth-center"></div>
                    <div class="compact-content">
                        <div class="compact-title">安全认证中心</div>
                        <div class="compact-desc">提供身份验证和授权服务，确保系统安全</div>
                    </div>
                </div>
                <div class="compact-card">
                    <div class="compact-icon key-management-system"></div>
                    <div class="compact-content">
                        <div class="compact-title">密钥管理系统</div>
                        <div class="compact-desc">管理系统根密钥和密钥派生流程</div>
                    </div>
                </div>
                <div class="compact-card">
                    <div class="compact-icon time-server"></div>
                    <div class="compact-content">
                        <div class="compact-title">时间服务器</div>
                        <div class="compact-desc">为整个系统提供精确时间基准</div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- 第10-12页：安全策略设计 -->
    <section id="security">
        <h2>5. 安全策略设计</h2>
        
        <h3>5.1 通信与认证</h3>
        <div class="architecture-diagram">
            <h4>通信安全层次架构</h4>
            <svg width="100%" height="500" viewBox="0 0 1000 500" xmlns="http://www.w3.org/2000/svg">
                <style>
                    .title { font-family: "Microsoft YaHei", Arial, sans-serif; font-weight: bold; }
                    .security-layer { fill: #e3f2fd; stroke: #1976d2; stroke-width: 2; }
                    .layer-title { font-family: "Microsoft YaHei", Arial, sans-serif; fill: #0d47a1; font-size: 16px; font-weight: bold; }
                    .component { fill: #bbdefb; stroke: #1976d2; stroke-width: 1; }
                    .component-text { font-family: "Microsoft YaHei", Arial, sans-serif; fill: #333; font-size: 14px; }
                    .arrow { stroke: #1976d2; stroke-width: 1.5; marker-end: url(#security-arrow); }
                    .desc-text { font-family: "Microsoft YaHei", Arial, sans-serif; fill: #333; font-size: 13px; }
                </style>

                <!-- 箭头定义 -->
                <defs>
                    <marker id="security-arrow" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
                        <polygon points="0 0, 10 3.5, 0 7" fill="#1976d2" />
                    </marker>
                </defs>

                <!-- 标题 -->
                <text x="50" y="40" font-size="18" class="title">数字钥匙系统安全层次架构</text>

                <!-- 应用层安全 -->
                <rect x="150" y="80" width="700" height="100" rx="10" class="security-layer" />
                <text x="180" y="110" class="layer-title">应用层安全</text>

                <rect x="200" y="130" width="120" height="35" rx="5" class="component" />
                <text x="260" y="153" text-anchor="middle" class="component-text">用户身份认证</text>
                
                <rect x="340" y="130" width="120" height="35" rx="5" class="component" />
                <text x="400" y="153" text-anchor="middle" class="component-text">权限分级控制</text>
                
                <rect x="480" y="130" width="120" height="35" rx="5" class="component" />
                <text x="540" y="153" text-anchor="middle" class="component-text">操作审计日志</text>
                
                <rect x="620" y="130" width="120" height="35" rx="5" class="component" />
                <text x="680" y="153" text-anchor="middle" class="component-text">异常行为分析</text>

                <text x="880" y="120" class="desc-text">确保用户身份真实性</text>
                <text x="880" y="140" class="desc-text">根据权限控制操作</text>
                <text x="880" y="160" class="desc-text">记录所有关键操作</text>

                <!-- 通信层安全 -->
                <rect x="150" y="190" width="700" height="100" rx="10" class="security-layer" />
                <text x="180" y="220" class="layer-title">通信层安全</text>

                <rect x="200" y="240" width="120" height="35" rx="5" class="component" />
                <text x="260" y="263" text-anchor="middle" class="component-text">TLS加密传输</text>
                
                <rect x="340" y="240" width="120" height="35" rx="5" class="component" />
                <text x="400" y="263" text-anchor="middle" class="component-text">安全认证通道</text>
                
                <rect x="480" y="240" width="120" height="35" rx="5" class="component" />
                <text x="540" y="263" text-anchor="middle" class="component-text">防重放保护</text>
                
                <rect x="620" y="240" width="120" height="35" rx="5" class="component" />
                <text x="680" y="263" text-anchor="middle" class="component-text">防中继攻击</text>

                <text x="880" y="230" class="desc-text">网络通信加密保护</text>
                <text x="880" y="250" class="desc-text">蓝牙连接安全保障</text>
                <text x="880" y="270" class="desc-text">抵御攻击和篡改</text>

                <!-- 平台层安全 -->
                <rect x="150" y="300" width="700" height="100" rx="10" class="security-layer" />
                <text x="180" y="330" class="layer-title">平台层安全</text>

                <rect x="200" y="350" width="120" height="35" rx="5" class="component" />
                <text x="260" y="373" text-anchor="middle" class="component-text">PKI证书体系</text>
                
                <rect x="340" y="350" width="120" height="35" rx="5" class="component" />
                <text x="400" y="373" text-anchor="middle" class="component-text">时间同步机制</text>
                
                <rect x="480" y="350" width="120" height="35" rx="5" class="component" />
                <text x="540" y="373" text-anchor="middle" class="component-text">HSM硬件保障</text>
                
                <rect x="620" y="350" width="120" height="35" rx="5" class="component" />
                <text x="680" y="373" text-anchor="middle" class="component-text">安全监控系统</text>

                <text x="880" y="340" class="desc-text">基础安全设施保障</text>
                <text x="880" y="360" class="desc-text">密钥安全管理机制</text>
                <text x="880" y="380" class="desc-text">系统安全状态监控</text>

                <!-- 硬件层安全 -->
                <rect x="150" y="410" width="700" height="70" rx="10" class="security-layer" />
                <text x="180" y="440" class="layer-title">硬件层安全</text>

                <rect x="200" y="430" width="150" height="35" rx="5" class="component" />
                <text x="275" y="453" text-anchor="middle" class="component-text">云端HSM安全模块</text>

                <rect x="380" y="430" width="150" height="35" rx="5" class="component" />
                <text x="455" y="453" text-anchor="middle" class="component-text">手机TEE/SE安全区</text>

                <rect x="560" y="430" width="150" height="35" rx="5" class="component" />
                <text x="635" y="453" text-anchor="middle" class="component-text">车端安全芯片</text>

                <text x="880" y="450" class="desc-text">物理安全基础保障</text>
            </svg>
            <p class="diagram-note">通信安全层次架构 - 展示了数字钥匙系统从硬件到应用的多层安全保障机制</p>
        </div>
        
        <h4>通信安全：</h4>
        <ul>
            <li><strong>网络通信</strong>：TLS 1.3或更高版本</li>
            <li><strong>蓝牙通信</strong>：蓝牙4.2及以上版本</li>
            <li><strong>安全通道</strong>：基于会话密钥的加密通信隧道</li>
            <li><strong>数据保护</strong>：传输数据加密与完整性校验</li>
        </ul>
        
        <h4>认证与授权：</h4>
        <ul>
            <li><strong>PKI证书认证</strong>：基于公钥基础设施的身份认证</li>
            <li><strong>API Key + 签名认证</strong>：接口调用安全保障</li>
            <li><strong>OAuth 2.0认证</strong>：标准化的授权框架</li>
            <li><strong>设备身份认证</strong>：确保设备真实性</li>
            <li><strong>权限分级管理</strong>：细粒度的权限控制</li>
        </ul>
        
        <h3>5.2 数据与密钥</h3>
        <div class="architecture-diagram">
            <h4>密钥管理体系</h4>
            <svg width="100%" height="550" viewBox="0 0 1000 550" xmlns="http://www.w3.org/2000/svg">
                <style>
                    .title { font-family: "Microsoft YaHei", Arial, sans-serif; font-weight: bold; }
                    .key-level { fill: #e8f5e9; stroke: #4caf50; stroke-width: 2; }
                    .level-title { font-family: "Microsoft YaHei", Arial, sans-serif; fill: #2e7d32; font-size: 16px; font-weight: bold; }
                    .key-component { fill: #c8e6c9; stroke: #4caf50; stroke-width: 1; }
                    .key-text { font-family: "Microsoft YaHei", Arial, sans-serif; fill: #333; font-size: 14px; }
                    .key-flow { stroke: #4caf50; stroke-width: 1.5; marker-end: url(#key-arrow); }
                    .key-desc { font-family: "Microsoft YaHei", Arial, sans-serif; fill: #333; font-size: 13px; }
                </style>

                <!-- 箭头定义 -->
                <defs>
                    <marker id="key-arrow" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
                        <polygon points="0 0, 10 3.5, 0 7" fill="#4caf50" />
                    </marker>
                </defs>

                <!-- 标题 -->
                <text x="50" y="40" font-size="18" class="title">数字钥匙系统密钥层次结构</text>

                <!-- 根密钥层 -->
                <rect x="200" y="80" width="600" height="100" rx="10" class="key-level" />
                <text x="240" y="110" class="level-title">根密钥层</text>

                <rect x="300" y="130" width="180" height="35" rx="5" class="key-component" />
                <text x="390" y="153" text-anchor="middle" class="key-text">云端主根密钥(MRK)</text>

                <rect x="520" y="130" width="180" height="35" rx="5" class="key-component" />
                <text x="610" y="153" text-anchor="middle" class="key-text">车端根密钥(VRK)</text>

                <text x="820" y="110" class="key-desc">最高安全等级</text>
                <text x="820" y="130" class="key-desc">硬件密码机保护</text>
                <text x="820" y="150" class="key-desc">多重授权访问</text>
                <text x="820" y="170" class="key-desc">长期有效(多年)</text>

                <!-- 中间密钥层 -->
                <rect x="200" y="190" width="600" height="150" rx="10" class="key-level" />
                <text x="240" y="220" class="level-title">中间密钥层</text>

                <rect x="300" y="230" width="180" height="35" rx="5" class="key-component" />
                <text x="390" y="253" text-anchor="middle" class="key-text">用户身份密钥(UIK)</text>

                <rect x="520" y="230" width="180" height="35" rx="5" class="key-component" />
                <text x="610" y="253" text-anchor="middle" class="key-text">车辆身份密钥(VIK)</text>

                <rect x="300" y="290" width="180" height="35" rx="5" class="key-component" />
                <text x="390" y="313" text-anchor="middle" class="key-text">钥匙派生密钥(KDK)</text>

                <rect x="520" y="290" width="180" height="35" rx="5" class="key-component" />
                <text x="610" y="313" text-anchor="middle" class="key-text">配对令牌密钥(PTK)</text>

                <text x="820" y="220" class="key-desc">中等安全等级</text>
                <text x="820" y="240" class="key-desc">安全存储区保护</text>
                <text x="820" y="260" class="key-desc">专用密钥操作API</text>
                <text x="820" y="280" class="key-desc">中期有效(月/年)</text>
                <text x="820" y="300" class="key-desc">可定期更新</text>

                <!-- 应用密钥层 -->
                <rect x="200" y="350" width="600" height="180" rx="10" class="key-level" />
                <text x="240" y="380" class="level-title">应用密钥层</text>

                <rect x="240" y="400" width="150" height="35" rx="5" class="key-component" />
                <text x="315" y="423" text-anchor="middle" class="key-text">虚拟密钥(VK)</text>

                <rect x="420" y="400" width="150" height="35" rx="5" class="key-component" />
                <text x="495" y="423" text-anchor="middle" class="key-text">会话密钥(SK)</text>

                <rect x="600" y="400" width="150" height="35" rx="5" class="key-component" />
                <text x="675" y="423" text-anchor="middle" class="key-text">暗号交换密钥(EK)</text>

                <rect x="240" y="460" width="150" height="35" rx="5" class="key-component" />
                <text x="315" y="483" text-anchor="middle" class="key-text">通信加密密钥(CEK)</text>

                <rect x="420" y="460" width="150" height="35" rx="5" class="key-component" />
                <text x="495" y="483" text-anchor="middle" class="key-text">数据签名密钥(DSK)</text>

                <rect x="600" y="460" width="150" height="35" rx="5" class="key-component" />
                <text x="675" y="483" text-anchor="middle" class="key-text">临时挑战密钥(TCK)</text>

                <text x="820" y="380" class="key-desc">操作安全等级</text>
                <text x="820" y="400" class="key-desc">安全环境内使用</text>
                <text x="820" y="420" class="key-desc">频繁更新(小时/天)</text>
                <text x="820" y="440" class="key-desc">通信会话独立密钥</text>
                <text x="820" y="460" class="key-desc">单一用途专用密钥</text>
                <text x="820" y="480" class="key-desc">临时有效,用后销毁</text>

                <!-- 密钥派生关系 -->
                <line x1="390" y1="165" x2="390" y2="230" class="key-flow" />
                <line x1="610" y1="165" x2="610" y2="230" class="key-flow" />
                <line x1="390" y1="265" x2="390" y2="290" class="key-flow" />
                <line x1="610" y1="265" x2="610" y2="290" class="key-flow" />

                <line x1="300" y1="325" x2="240" y2="400" class="key-flow" />
                <line x1="390" y1="325" x2="420" y2="400" class="key-flow" />
                <line x1="510" y1="325" x2="600" y2="400" class="key-flow" />
                
                <line x1="390" y1="325" x2="280" y2="460" class="key-flow" />
                <line x1="390" y1="325" x2="420" y2="460" class="key-flow" />
                <line x1="610" y1="325" x2="675" y2="460" class="key-flow" />
            </svg>
            <p class="diagram-note">数字钥匙系统密钥层次结构 - 展示了从根密钥到应用密钥的派生关系和安全等级</p>
        </div>
        
        <h4>数据安全：</h4>
        <ul>
            <li><strong>敏感数据加密与签名</strong>：保护数据机密性和完整性</li>
            <li><strong>数据完整性验证</strong>：防止数据被篡改</li>
            <li><strong>个人信息脱敏</strong>：保护用户隐私</li>
            <li><strong>数据分级存储</strong>：不同敏感级别的数据采用不同安全等级存储</li>
        </ul>
        
        <h4>密钥管理：</h4>
        <ul>
            <li><strong>分层密钥架构</strong>：根密钥、中间密钥、应用密钥</li>
            <li><strong>密钥生成与安全存储</strong>：使用安全随机数生成器，存储于安全硬件环境</li>
            <li><strong>根密钥管理流程</strong>：严格的访问控制和多人授权</li>
            <li><strong>定期更新与备份机制</strong>：降低密钥泄露风险</li>
            <li><strong>密钥撤销方案</strong>：应对密钥泄露情况</li>
        </ul>
        
        <h3>5.3 防护机制</h3>
        <div class="architecture-diagram">
            <h4>安全防护机制</h4>
            <svg width="100%" height="450" viewBox="0 0 1000 450" xmlns="http://www.w3.org/2000/svg">
                <style>
                    .title { font-family: "Microsoft YaHei", Arial, sans-serif; font-weight: bold; }
                    .defense-group { fill: #f3e5f5; stroke: #9c27b0; stroke-width: 2; }
                    .group-title { font-family: "Microsoft YaHei", Arial, sans-serif; fill: #6a1b9a; font-size: 16px; font-weight: bold; }
                    .defense-item { fill: #e1bee7; stroke: #9c27b0; stroke-width: 1; }
                    .item-text { font-family: "Microsoft YaHei", Arial, sans-serif; fill: #333; font-size: 14px; }
                    .desc-text { font-family: "Microsoft YaHei", Arial, sans-serif; fill: #333; font-size: 13px; }
                </style>

                <!-- 标题 -->
                <text x="50" y="40" font-size="18" class="title">数字钥匙系统防护机制</text>

                <!-- 时间安全 -->
                <rect x="100" y="70" width="800" height="100" rx="10" class="defense-group" />
                <text x="130" y="100" class="group-title">时间安全机制</text>

                <rect x="150" y="120" width="170" height="35" rx="5" class="defense-item" />
                <text x="235" y="143" text-anchor="middle" class="item-text">NTP/PTP时间同步</text>

                <rect x="340" y="120" width="170" height="35" rx="5" class="defense-item" />
                <text x="425" y="143" text-anchor="middle" class="item-text">时间戳验证</text>

                <rect x="530" y="120" width="170" height="35" rx="5" class="defense-item" />
                <text x="615" y="143" text-anchor="middle" class="item-text">离线时间漂移补偿</text>

                <rect x="720" y="120" width="160" height="35" rx="5" class="defense-item" />
                <text x="800" y="143" text-anchor="middle" class="item-text">可信时间源验证</text>

                <!-- 攻击防御 -->
                <rect x="100" y="180" width="800" height="100" rx="10" class="defense-group" />
                <text x="130" y="210" class="group-title">攻击防御机制</text>

                <rect x="150" y="230" width="170" height="35" rx="5" class="defense-item" />
                <text x="235" y="253" text-anchor="middle" class="item-text">防重放攻击</text>

                <rect x="340" y="230" width="170" height="35" rx="5" class="defense-item" />
                <text x="425" y="253" text-anchor="middle" class="item-text">防中继攻击</text>

                <rect x="530" y="230" width="170" height="35" rx="5" class="defense-item" />
                <text x="615" y="253" text-anchor="middle" class="item-text">防中间人攻击</text>

                <rect x="720" y="230" width="160" height="35" rx="5" class="defense-item" />
                <text x="800" y="253" text-anchor="middle" class="item-text">防DoS攻击</text>

                <!-- 异常处理 -->
                <rect x="100" y="290" width="800" height="150" rx="10" class="defense-group" />
                <text x="130" y="320" class="group-title">异常检测与响应</text>

                <rect x="150" y="340" width="170" height="35" rx="5" class="defense-item" />
                <text x="235" y="363" text-anchor="middle" class="item-text">通信异常检测</text>

                <rect x="340" y="340" width="170" height="35" rx="5" class="defense-item" />
                <text x="425" y="363" text-anchor="middle" class="item-text">操作异常分析</text>

                <rect x="530" y="340" width="170" height="35" rx="5" class="defense-item" />
                <text x="615" y="363" text-anchor="middle" class="item-text">自动响应机制</text>

                <rect x="720" y="340" width="160" height="35" rx="5" class="defense-item" />
                <text x="800" y="363" text-anchor="middle" class="item-text">安全事件日志</text>

                <rect x="150" y="390" width="170" height="35" rx="5" class="defense-item" />
                <text x="235" y="413" text-anchor="middle" class="item-text">指数退避重连</text>

                <rect x="340" y="390" width="170" height="35" rx="5" class="defense-item" />
                <text x="425" y="413" text-anchor="middle" class="item-text">应急恢复机制</text>

                <rect x="530" y="390" width="170" height="35" rx="5" class="defense-item" />
                <text x="615" y="413" text-anchor="middle" class="item-text">安全预警通知</text>

                <rect x="720" y="390" width="160" height="35" rx="5" class="defense-item" />
                <text x="800" y="413" text-anchor="middle" class="item-text">远程紧急干预</text>
            </svg>
            <p class="diagram-note">数字钥匙系统防护机制 - 展示了系统针对各类安全威胁的防御措施</p>
        </div>
        
        <h4>时间同步机制：</h4>
        <ul>
            <li><strong>NTP同步与时间戳验证</strong>：确保系统时间准确，为安全操作提供可信时间基准</li>
            <li><strong>容错机制与离线处理</strong>：应对网络不稳定情况，保证系统可靠运行</li>
            <li><strong>时间源验证</strong>：防止伪造时间源，确保时间数据可信</li>
        </ul>
        
        <h4>安全防护：</h4>
        <ul>
            <li><strong>防重放攻击措施</strong>：使用时间戳、随机数和序列号确保每次通信的唯一性</li>
            <li><strong>防中继攻击技术</strong>：距离测量和严格时间限制，防止信号被转发</li>
            <li><strong>安全存储与运算</strong>：利用TEE/SE等安全环境保护密钥和敏感操作</li>
            <li><strong>异常检测与响应</strong>：实时监控系统行为，及时发现并处理安全威胁</li>
        </ul>

        <div class="note">
            <p><strong>安全最佳实践</strong>：数字钥匙系统安全设计遵循"深度防御"原则，构建多层次防护体系。即使某一层被突破，其他层次仍能提供有效保护，确保系统整体安全性。</p>
        </div>
    </section>

    <!-- 第13-15页：技术难点与解决方案 -->
    <section id="challenges">
        <h2>6. 技术难点与解决方案</h2>
        
        <h3>6.1 安全性问题与解决方案</h3>
        <div class="module-grid">
            <div class="module-card">
                <div class="module-icon secure-storage"></div>
                <h4 class="module-title">密钥安全存储</h4>
                <p class="module-desc">采用TEE/SE安全环境存储密钥材料，利用硬件安全芯片保护根密钥，实现多层次密钥保护机制。</p>
            </div>
            <div class="module-card">
                <div class="module-icon key-verification"></div>
                <h4 class="module-title">防重放攻击</h4>
                <p class="module-desc">使用时间戳、随机挑战和序列号机制，确保每次通信的唯一性，防止通信数据被重放。</p>
            </div>
            <div class="module-card">
                <div class="module-icon auth-center"></div>
                <h4 class="module-title">身份真实性验证</h4>
                <p class="module-desc">实现双向身份认证，采用多因素认证机制，确保用户和车辆身份的真实性和可靠性。</p>
            </div>
        </div>
    </section>

    <!-- 第16-18页：平台交互与集成 -->
    <section id="integration">
        <h2>7. 平台交互与集成</h2>
        
        <h3>7.1 与手机APP的交互</h3>
        <div class="module-grid">
            <div class="module-card">
                <div class="module-icon key-management"></div>
                <h4 class="module-title">钥匙管理流程</h4>
                <p class="module-desc">用户通过APP添加、删除和共享数字钥匙，管理钥匙权限和有效期，实现便捷的钥匙生命周期管理。</p>
            </div>
            <div class="module-card">
                <div class="module-icon vehicle-control"></div>
                <h4 class="module-title">车辆控制指令</h4>
                <p class="module-desc">APP发送开锁、关锁、启动等控制指令，接收车辆状态反馈，提供直观的用户交互界面。</p>
            </div>
            <div class="module-card">
                <div class="module-icon auth-center"></div>
                <h4 class="module-title">安全认证机制</h4>
                <p class="module-desc">实现用户身份验证和权限控制，确保只有授权用户才能执行相应操作，保障系统安全。</p>
            </div>
        </div>
        
        <h3>7.2 与云平台的交互</h3>
        <div class="module-grid">
            <div class="module-card">
                <div class="module-icon key-lifecycle"></div>
                <h4 class="module-title">钥匙生命周期管理</h4>
                <p class="module-desc">云平台负责钥匙的创建、授权、撤销和更新，管理钥匙的完整生命周期，确保系统安全可靠。</p>
            </div>
            <div class="module-card">
                <div class="module-icon vin-service"></div>
                <h4 class="module-title">车辆信息关联</h4>
                <p class="module-desc">管理车辆信息和VIN码关联，验证车辆身份，确保钥匙与正确的车辆绑定，防止误操作。</p>
            </div>
            <div class="module-card">
                <div class="module-icon api-layer"></div>
                <h4 class="module-title">API接口服务</h4>
                <p class="module-desc">提供标准化的API接口，支持与TSP/OEM平台的集成，实现数据交换和服务对接。</p>
            </div>
        </div>
        
        <h3>7.3 与车端的交互</h3>
        <div class="module-grid">
            <div class="module-card">
                <div class="module-icon bluetooth"></div>
                <h4 class="module-title">蓝牙安全通信</h4>
                <p class="module-desc">建立安全的蓝牙连接，实现手机与车辆之间的近场通信，支持BLE协议和安全加密机制。</p>
            </div>
            <div class="module-card">
                <div class="module-icon command-execution"></div>
                <h4 class="module-title">指令验证与执行</h4>
                <p class="module-desc">验证数字钥匙的有效性和权限，执行控制指令，与车辆CAN总线交互，实现车辆控制功能。</p>
            </div>
            <div class="module-card">
                <div class="module-icon secure-storage"></div>
                <h4 class="module-title">安全存储访问</h4>
                <p class="module-desc">安全访问和操作车端存储模块，保护密钥材料和配置信息，确保数据安全和系统稳定。</p>
            </div>
        </div>
    </section>


    <footer>
        <p>数字钥匙系统技术架构 | 版本：1.0.0 | 最后更新：2023年12月25日</p>
        <p>Copyright © 2023 数字钥匙系统团队. All Rights Reserved.</p>
    </footer>
</body>
</html>

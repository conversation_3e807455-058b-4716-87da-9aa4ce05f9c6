package com.ruoyi.dk.service.impl;

import com.ruoyi.common.core.constant.CacheConstants;
import com.ruoyi.common.core.exception.ServiceException;
import com.ruoyi.common.core.utils.DateUtils;
import com.ruoyi.dk.domain.DigitalKeyShareDetail;
import com.ruoyi.dk.mapper.DigitalKeyShareDetailMapper;
import com.ruoyi.dk.service.IDigitalKeyShareDetailService;
import com.ruoyi.system.service.ISysConfigService;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Calendar;
import java.util.Date;
import java.util.List;

/**
 * 车辆数字钥匙分享详情Service业务层处理
 * 
 * <AUTHOR>
 * @date 2024-08-12
 */
@Service
public class DigitalKeyShareDetailServiceImpl implements IDigitalKeyShareDetailService 
{
    @Autowired
    private DigitalKeyShareDetailMapper digitalKeyShareDetailMapper;

    @Autowired
    private ISysConfigService configService;

    /**
     * 查询车辆数字钥匙分享详情
     * 
     * @param detailId 车辆数字钥匙分享详情主键
     * @return 车辆数字钥匙分享详情
     */
    @Override
    public DigitalKeyShareDetail selectDigitalKeyShareDetailByDetailId(Long detailId)
    {
        return digitalKeyShareDetailMapper.selectDigitalKeyShareDetailByDetailId(detailId);
    }

    /**
     * 查询车辆数字钥匙分享详情列表
     * 
     * @param digitalKeyShareDetail 车辆数字钥匙分享详情
     * @return 车辆数字钥匙分享详情
     */
    @Override
    public List<DigitalKeyShareDetail> selectDigitalKeyShareDetailList(DigitalKeyShareDetail digitalKeyShareDetail)
    {
        // 从数据库中获取分享详情列表
        List<DigitalKeyShareDetail> shareDetails = digitalKeyShareDetailMapper.selectDigitalKeyShareDetailList(digitalKeyShareDetail);
        
        // 获取当前时间
        Date currentDate = DateUtils.getNowDate();

        // 遍历分享详情列表，判断每个分享是否过期
        for (DigitalKeyShareDetail detail : shareDetails) {
            // 判断分享结束时间是否小于当前时间
            if (detail.getShareEndTime() != null && detail.getShareEndTime().before(currentDate)) {
                detail.setIsExpired(true); // 如果结束时间早于当前时间，设置为过期
            } else {
                detail.setIsExpired(false); // 如果结束时间晚于或等于当前时间，设置为未过期
            }
        }
        
        // 返回处理后的分享详情列表
        return shareDetails;
    }

    /**
     * 新增车辆数字钥匙分享详情
     * 
     * @param digitalKeyShareDetail 车辆数字钥匙分享详情
     * @return 结果
     */
    @Override
    public int insertDigitalKeyShareDetail(DigitalKeyShareDetail digitalKeyShareDetail)
    {
        if (ObjectUtils.isNotEmpty(digitalKeyShareDetail)) {
            Date currentDate = DateUtils.getNowDate();

            DigitalKeyShareDetail query = new DigitalKeyShareDetail();
            query.setSharerId(digitalKeyShareDetail.getSharerId());
            query.setShareVehicleVin(digitalKeyShareDetail.getShareVehicleVin());
            query.setCurrentTime(currentDate);

            // 查询当前被分享车辆的所有有效分享信息
            List<DigitalKeyShareDetail> shareDetails = selectDigitalKeyShareDetailList(query);

            if (ObjectUtils.isNotEmpty(shareDetails)) {
                // 获取「车辆数字钥匙最大分享次数」
                String maxSharingQuantityStr = configService.selectConfigByKey(CacheConstants.DK_SHARING_QUANTITY);
                if (StringUtils.isNotBlank(maxSharingQuantityStr)) {
                    int maxSharingQuantity = 0;
                    try {
                        maxSharingQuantity = Integer.parseInt(maxSharingQuantityStr);
                    } catch (Exception e) {
                        throw new ServiceException("车辆数字钥匙最大分享次数配置错误！");
                    }

                    if (shareDetails.size() >= maxSharingQuantity) {
                        throw new ServiceException("当前数字钥匙的分享次已超限！");
                    }
                }
            }

            // 获取「车辆数字钥匙分享有效时间」单位：分钟
            String sharingEffectiveTimeStr = configService.selectConfigByKey(CacheConstants.DK_SHARING_EFFECTIVE_TIME);

            // 「车辆数字钥匙分享有效时间」默认为一天，即1560分钟
            int sharingEffectiveTime = 1560;
            if (StringUtils.isNotBlank(sharingEffectiveTimeStr)) {
                try {
                    sharingEffectiveTime = Integer.parseInt(sharingEffectiveTimeStr);
                } catch (Exception e) {
                    throw new ServiceException("当前数字钥匙分享的有效时间配置错误！");
                }
            }

            digitalKeyShareDetail.setShareStartTime(currentDate);

            // 使用 Calendar 类
            Calendar calendar = Calendar.getInstance();
            calendar.setTime(currentDate);
            // 增加「参数配置的分享有效时间」分钟数
            calendar.add(Calendar.MINUTE, sharingEffectiveTime);
            // 获取增加一个月后的日期
            Date newDate = calendar.getTime();

            digitalKeyShareDetail.setShareEndTime(newDate);
        }
        return digitalKeyShareDetailMapper.insertDigitalKeyShareDetail(digitalKeyShareDetail);
    }

    /**
     * 修改车辆数字钥匙分享详情
     * 
     * @param digitalKeyShareDetail 车辆数字钥匙分享详情
     * @return 结果
     */
    @Override
    public int updateDigitalKeyShareDetail(DigitalKeyShareDetail digitalKeyShareDetail)
    {
        return digitalKeyShareDetailMapper.updateDigitalKeyShareDetail(digitalKeyShareDetail);
    }

    /**
     * 批量删除车辆数字钥匙分享详情
     * 
     * @param sharerIds 需要删除的车辆数字钥匙分享详情主键
     * @return 结果
     */
    @Override
    public int deleteDigitalKeyShareDetailByDetailIds(Long[] sharerIds)
    {
        return digitalKeyShareDetailMapper.deleteDigitalKeyShareDetailByDetailIds(sharerIds);
    }

    /**
     * 删除车辆数字钥匙分享详情信息
     * 
     * @param sharerId 车辆数字钥匙分享详情主键
     * @return 结果
     */
    @Override
    public int deleteDigitalKeyShareDetailByDetailId(Long sharerId)
    {
        return digitalKeyShareDetailMapper.deleteDigitalKeyShareDetailByDetailId(sharerId);
    }
}

<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>数字钥匙三端架构</title>
    <style>
        body {
            font-family: 'Arial', sans-serif;
            margin: 0;
            padding: 0;
            background-color: #f5f5f5;
            color: #333;
        }
        .slide {
            width: 90%;
            max-width: 1200px;
            margin: 20px auto;
            background: white;
            padding: 40px;
            box-shadow: 0 4px 8px rgba(0,0,0,0.1);
            border-radius: 8px;
        }
        h1 {
            color: #2c3e50;
            text-align: center;
            font-size: 2.5em;
            margin-bottom: 30px;
        }
        h2 {
            color: #3498db;
            border-bottom: 2px solid #3498db;
            padding-bottom: 10px;
            margin-top: 30px;
        }
        .architecture-container {
            display: flex;
            justify-content: center;
            margin: 40px 0;
        }
        .architecture-diagram {
            position: relative;
            width: 100%;
            height: 600px;
        }
        .component {
            position: absolute;
            width: 300px;
            background-color: #ecf0f1;
            border: 2px solid #3498db;
            border-radius: 8px;
            padding: 15px;
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
        }
        .cloud-platform {
            top: 50px;
            left: 50%;
            transform: translateX(-50%);
            background-color: #d6eaf8;
            border-color: #3498db;
            z-index: 1;
        }
        .mobile-app {
            bottom: 100px;
            left: 180px;
            background-color: #d5f5e3;
            border-color: #27ae60;
            z-index: 2;
        }
        .vehicle-end {
            bottom: 100px;
            right: 180px;
            background-color: #fdebd0;
            border-color: #f39c12;
            z-index: 2;
        }
        .connection-line {
            position: absolute;
            background-color: #95a5a6;
            z-index: 0;
        }
        .vertical-line {
            width: 4px;
            left: 50%;
            transform: translateX(-50%);
            top: 160px;
            height: 180px;
        }
        .horizontal-line {
            height: 4px;
            top: 340px;
            left: 330px;
            right: 330px;
        }
        .component-title {
            text-align: center;
            font-weight: bold;
            font-size: 1.2em;
            margin-bottom: 10px;
            color: #2c3e50;
        }
        .component-list {
            font-size: 0.9em;
            line-height: 1.4;
        }
        .interaction-flow {
            margin-top: 40px;
            background-color: #f8f9fa;
            padding: 20px;
            border-radius: 8px;
            border-left: 5px solid #3498db;
        }
        .flow-step {
            margin-bottom: 15px;
            display: flex;
            align-items: flex-start;
        }
        .step-number {
            background-color: #3498db;
            color: white;
            width: 30px;
            height: 30px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 50%;
            margin-right: 15px;
            flex-shrink: 0;
        }
        .arrow-container {
            position: absolute;
            text-align: center;
            font-weight: bold;
            color: #7f8c8d;
        }
        .arrow-cloud-mobile {
            left: 400px;
            top: 255px;
        }
        .arrow-cloud-vehicle {
            right: 400px;
            top: 255px;
        }
        .arrow-mobile-vehicle {
            top: 460px;
            left: 50%;
            transform: translateX(-50%);
        }
        .legend {
            margin-top: 20px;
            display: flex;
            justify-content: center;
            gap: 20px;
        }
        .legend-item {
            display: flex;
            align-items: center;
            margin-right: 15px;
        }
        .legend-color {
            width: 20px;
            height: 20px;
            margin-right: 8px;
            border-radius: 4px;
        }
    </style>
</head>
<body>
    <div class="slide">
        <h1>数字钥匙系统三端架构</h1>
        
        <div class="architecture-container">
            <div class="architecture-diagram">
                <!-- 连接线 -->
                <div class="connection-line vertical-line"></div>
                <div class="connection-line horizontal-line"></div>
                
                <!-- 云平台组件 -->
                <div class="component cloud-platform">
                    <div class="component-title">钥匙云平台</div>
                    <div class="component-list">
                        • 钥匙生命周期管理<br>
                        • 车辆关联服务<br>
                        • 安全认证中心<br>
                        • 密钥管理系统<br>
                        • 统一接口服务<br>
                        • 安全通信通道<br>
                        • 异常监控与处理<br>
                        • 时间服务器
                    </div>
                </div>
                
                <!-- 手机端组件 -->
                <div class="component mobile-app">
                    <div class="component-title">手机端</div>
                    <div class="component-list">
                        • 钥匙管理模块<br>
                        • 车辆控制模块<br>
                        • 安全存储模块<br>
                        • 蓝牙通信模块<br>
                        • 时间同步模块<br>
                        • 暗号交换模块<br>
                        • 智能场景管理模块<br>
                        • 异常处理模块<br>
                        • 标定模块
                    </div>
                </div>
                
                <!-- 车端组件 -->
                <div class="component vehicle-end">
                    <div class="component-title">车端</div>
                    <div class="component-list">
                        • 蓝牙通信模块<br>
                        • 钥匙验证模块<br>
                        • 指令执行模块<br>
                        • 安全存储模块<br>
                        • 远程通信模块<br>
                        • 时间同步模块<br>
                        • 暗号交换模块<br>
                        • 用户行为分析模块<br>
                        • 异常处理模块<br>
                        • 标定配置模块
                    </div>
                </div>
                
                <!-- 箭头和说明 -->
                <div class="arrow-container arrow-cloud-mobile">
                    ↓ 钥匙分发、授权<br>
                    ↑ 状态同步、认证
                </div>
                
                <div class="arrow-container arrow-cloud-vehicle">
                    ↓ 配置下发、更新<br>
                    ↑ 状态上报、日志
                </div>
                
                <div class="arrow-container arrow-mobile-vehicle">
                    ← 控制指令、认证 →<br>
                    ← 状态反馈、蓝牙连接 →
                </div>
            </div>
        </div>
        
        <div class="legend">
            <div class="legend-item">
                <div class="legend-color" style="background-color: #d6eaf8; border: 1px solid #3498db;"></div>
                <div>云平台</div>
            </div>
            <div class="legend-item">
                <div class="legend-color" style="background-color: #d5f5e3; border: 1px solid #27ae60;"></div>
                <div>手机端</div>
            </div>
            <div class="legend-item">
                <div class="legend-color" style="background-color: #fdebd0; border: 1px solid #f39c12;"></div>
                <div>车端</div>
            </div>
        </div>
        
        <h2>数字钥匙交互流程</h2>
        
        <div class="interaction-flow">
            <div class="flow-step">
                <div class="step-number">1</div>
                <div>
                    <strong>钥匙创建与授权</strong>: 云平台生成数字钥匙，设定权限与有效期，通过安全通道下发至用户手机端
                </div>
            </div>
            
            <div class="flow-step">
                <div class="step-number">2</div>
                <div>
                    <strong>时间同步</strong>: 三端与云平台时间服务器保持同步，确保时间一致性，防止重放攻击
                </div>
            </div>
            
            <div class="flow-step">
                <div class="step-number">3</div>
                <div>
                    <strong>蓝牙发现与连接</strong>: 车端发送加密广播信号，手机端接收并识别车辆，主动发起连接请求
                </div>
            </div>
            
            <div class="flow-step">
                <div class="step-number">4</div>
                <div>
                    <strong>双向认证</strong>: 手机端与车端交换并验证身份凭证，确保双方身份可信
                </div>
            </div>
            
            <div class="flow-step">
                <div class="step-number">5</div>
                <div>
                    <strong>暗号交换</strong>: 手机端与车端协商生成临时会话密钥和通信暗号，建立安全通信通道
                </div>
            </div>
            
            <div class="flow-step">
                <div class="step-number">6</div>
                <div>
                    <strong>距离感知</strong>: 基于RSSI信号强度，车端计算与手机的实际距离，支持无感进入和距离感知操作
                </div>
            </div>
            
            <div class="flow-step">
                <div class="step-number">7</div>
                <div>
                    <strong>控制指令执行</strong>: 手机端发送控制指令（开锁、启动等），车端验证权限后执行并反馈结果
                </div>
            </div>
            
            <div class="flow-step">
                <div class="step-number">8</div>
                <div>
                    <strong>状态同步</strong>: 车端将操作状态同步至云平台，手机端可查看最新车辆状态
                </div>
            </div>
            
            <div class="flow-step">
                <div class="step-number">9</div>
                <div>
                    <strong>异常处理</strong>: 三端各自监控异常行为，执行安全措施，确保系统安全可靠
                </div>
            </div>
        </div>
    </div>
</body>
</html>

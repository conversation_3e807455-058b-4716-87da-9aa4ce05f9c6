@startuml
skinparam monochrome true

participant "车辆蓝牙模块" as CarBT
participant "移动终端" as Mobile

== 蓝牙配对流程 ==

CarBT -> Mobile : 发送蓝牙广播（含车辆识别信息）
activate Mobile
Mobile -> CarBT : 发起蓝牙连接请求
CarBT -> CarBT : 建立蓝牙连接通道
Mobile -> Mobile : 建立蓝牙连接通道
CarBT -> Mobile : 发起蓝牙安全请求
Mobile -> Mobile : 发送蓝牙配对请求
activate CarBT
CarBT -> CarBT : 输入或生成配对码
Mobile -> Mobile : 输入或生成配对码
CarBT -> CarBT : 校验配对码
Mobile -> Mobile : 校验配对码
CarBT -> Mobile : 配对成功，绑定设备信息
Mobile -> CarBT : 配对成功，绑定设备信息
deactivate CarBT
deactivate Mobile

@enduml
<?xml version="1.0" encoding="UTF-8"?>
<mxfile host="app.diagrams.net" agent="Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********" version="26.1.0">
  <diagram name="第 1 页" id="sT5CrcH_zo9G9MpETPcf">
    <mxGraphModel dx="2634" dy="1109" grid="1" gridSize="10" guides="1" tooltips="1" connect="1" arrows="1" fold="1" page="1" pageScale="1" pageWidth="827" pageHeight="1169" math="0" shadow="0">
      <root>
        <mxCell id="0" />
        <mxCell id="1" parent="0" />
        <mxCell id="10" value="数字钥匙系统安全架构图" style="text;html=1;strokeColor=none;fillColor=none;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=28;fontStyle=1;fontColor=#333333;fontFamily=Helvetica;" vertex="1" parent="1">
          <mxGeometry x="213.5" y="40" width="400" height="40" as="geometry" />
        </mxCell>
        
        <!-- 云平台层 -->
        <mxCell id="11" value="云平台层" style="swimlane;whiteSpace=wrap;html=1;fillColor=#dae8fc;strokeColor=#6c8ebf;rounded=1;shadow=1;glass=0;swimlaneFillColor=#EAF2FD;fontFamily=Helvetica;fontSize=14;fontStyle=1;" vertex="1" parent="1">
          <mxGeometry x="120" y="120" width="600" height="160" as="geometry" />
        </mxCell>
        <mxCell id="12" value="钥匙生命周期管理" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#d5e8d4;strokeColor=#82b366;shadow=1;glass=0;sketch=0;gradientColor=#97d077;fontFamily=Helvetica;fontSize=12;" vertex="1" parent="11">
          <mxGeometry x="40" y="40" width="160" height="40" as="geometry" />
        </mxCell>
        <mxCell id="13" value="安全认证中心" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#d5e8d4;strokeColor=#82b366;shadow=1;glass=0;sketch=0;gradientColor=#97d077;fontFamily=Helvetica;fontSize=12;" vertex="1" parent="11">
          <mxGeometry x="220" y="40" width="160" height="40" as="geometry" />
        </mxCell>
        <mxCell id="14" value="密钥管理系统" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#d5e8d4;strokeColor=#82b366;shadow=1;glass=0;sketch=0;gradientColor=#97d077;fontFamily=Helvetica;fontSize=12;" vertex="1" parent="11">
          <mxGeometry x="400" y="40" width="160" height="40" as="geometry" />
        </mxCell>
        <mxCell id="15" value="时间服务器" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#d5e8d4;strokeColor=#82b366;shadow=1;glass=0;sketch=0;gradientColor=#97d077;fontFamily=Helvetica;fontSize=12;" vertex="1" parent="11">
          <mxGeometry x="40" y="100" width="160" height="40" as="geometry" />
        </mxCell>
        <mxCell id="16" value="异常监控与处理" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#d5e8d4;strokeColor=#82b366;shadow=1;glass=0;sketch=0;gradientColor=#97d077;fontFamily=Helvetica;fontSize=12;" vertex="1" parent="11">
          <mxGeometry x="220" y="100" width="160" height="40" as="geometry" />
        </mxCell>
        <mxCell id="17" value="统一接口服务" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#d5e8d4;strokeColor=#82b366;shadow=1;glass=0;sketch=0;gradientColor=#97d077;fontFamily=Helvetica;fontSize=12;" vertex="1" parent="11">
          <mxGeometry x="400" y="100" width="160" height="40" as="geometry" />
        </mxCell>
        
        <!-- 云平台内部连接 -->
        <mxCell id="101" value="" style="endArrow=classic;startArrow=classic;html=1;rounded=1;exitX=1;exitY=0.5;exitDx=0;exitDy=0;entryX=0;entryY=0.5;entryDx=0;entryDy=0;strokeWidth=1.5;strokeColor=#6c8ebf;dashed=0;shadow=0;edgeStyle=orthogonalEdgeStyle;" edge="1" parent="11" source="12" target="13">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="210" y="60" as="sourcePoint" />
            <mxPoint x="260" y="10" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="102" value="" style="endArrow=classic;startArrow=classic;html=1;rounded=1;exitX=1;exitY=0.5;exitDx=0;exitDy=0;entryX=0;entryY=0.5;entryDx=0;entryDy=0;strokeWidth=1.5;strokeColor=#6c8ebf;dashed=0;shadow=0;edgeStyle=orthogonalEdgeStyle;" edge="1" parent="11" source="13" target="14">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="210" y="60" as="sourcePoint" />
            <mxPoint x="260" y="10" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="103" value="" style="endArrow=classic;startArrow=classic;html=1;rounded=1;exitX=0.5;exitY=1;exitDx=0;exitDy=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;strokeWidth=1.5;strokeColor=#6c8ebf;dashed=0;shadow=0;" edge="1" parent="11" source="12" target="15">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="210" y="60" as="sourcePoint" />
            <mxPoint x="260" y="10" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="104" value="" style="endArrow=classic;startArrow=classic;html=1;rounded=1;exitX=0.5;exitY=1;exitDx=0;exitDy=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;strokeWidth=1.5;strokeColor=#6c8ebf;dashed=0;shadow=0;" edge="1" parent="11" source="13" target="16">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="210" y="60" as="sourcePoint" />
            <mxPoint x="260" y="10" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="105" value="" style="endArrow=classic;startArrow=classic;html=1;rounded=1;exitX=0.5;exitY=1;exitDx=0;exitDy=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;strokeWidth=1.5;strokeColor=#6c8ebf;dashed=0;shadow=0;" edge="1" parent="11" source="14" target="17">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="210" y="60" as="sourcePoint" />
            <mxPoint x="260" y="10" as="targetPoint" />
          </mxGeometry>
        </mxCell>

        <!-- 手机端层 -->
        <mxCell id="18" value="手机端层" style="swimlane;whiteSpace=wrap;html=1;fillColor=#fff2cc;strokeColor=#d6b656;rounded=1;shadow=1;glass=0;swimlaneFillColor=#FFF8E6;fontFamily=Helvetica;fontSize=14;fontStyle=1;" vertex="1" parent="1">
          <mxGeometry x="120" y="320" width="600" height="160" as="geometry" />
        </mxCell>
        <mxCell id="19" value="钥匙管理模块" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#ffe6cc;strokeColor=#d79b00;shadow=1;glass=0;sketch=0;gradientColor=#ffd966;fontFamily=Helvetica;fontSize=12;" vertex="1" parent="18">
          <mxGeometry x="40" y="40" width="160" height="40" as="geometry" />
        </mxCell>
        <mxCell id="20" value="安全存储模块" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#ffe6cc;strokeColor=#d79b00;shadow=1;glass=0;sketch=0;gradientColor=#ffd966;fontFamily=Helvetica;fontSize=12;" vertex="1" parent="18">
          <mxGeometry x="220" y="40" width="160" height="40" as="geometry" />
        </mxCell>
        <mxCell id="21" value="蓝牙通信模块" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#ffe6cc;strokeColor=#d79b00;shadow=1;glass=0;sketch=0;gradientColor=#ffd966;fontFamily=Helvetica;fontSize=12;" vertex="1" parent="18">
          <mxGeometry x="400" y="40" width="160" height="40" as="geometry" />
        </mxCell>
        <mxCell id="22" value="暗号交换模块" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#ffe6cc;strokeColor=#d79b00;shadow=1;glass=0;sketch=0;gradientColor=#ffd966;fontFamily=Helvetica;fontSize=12;" vertex="1" parent="18">
          <mxGeometry x="40" y="100" width="160" height="40" as="geometry" />
        </mxCell>
        <mxCell id="23" value="智能场景管理" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#ffe6cc;strokeColor=#d79b00;shadow=1;glass=0;sketch=0;gradientColor=#ffd966;fontFamily=Helvetica;fontSize=12;" vertex="1" parent="18">
          <mxGeometry x="220" y="100" width="160" height="40" as="geometry" />
        </mxCell>
        <mxCell id="24" value="异常处理模块" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#ffe6cc;strokeColor=#d79b00;shadow=1;glass=0;sketch=0;gradientColor=#ffd966;fontFamily=Helvetica;fontSize=12;" vertex="1" parent="18">
          <mxGeometry x="400" y="100" width="160" height="40" as="geometry" />
        </mxCell>
        
        <!-- 手机端内部连接 -->
        <mxCell id="106" value="" style="endArrow=classic;html=1;rounded=1;exitX=1;exitY=0.5;exitDx=0;exitDy=0;entryX=0;entryY=0.5;entryDx=0;entryDy=0;strokeWidth=1.5;strokeColor=#d6b656;dashed=0;shadow=0;edgeStyle=orthogonalEdgeStyle;" edge="1" parent="18" source="19" target="20">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="210" y="60" as="sourcePoint" />
            <mxPoint x="260" y="10" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="107" value="" style="endArrow=classic;html=1;rounded=1;exitX=1;exitY=0.5;exitDx=0;exitDy=0;entryX=0;entryY=0.5;entryDx=0;entryDy=0;strokeWidth=1.5;strokeColor=#d6b656;dashed=0;shadow=0;edgeStyle=orthogonalEdgeStyle;" edge="1" parent="18" source="20" target="21">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="210" y="60" as="sourcePoint" />
            <mxPoint x="260" y="10" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="108" value="" style="endArrow=classic;html=1;rounded=1;exitX=0.5;exitY=1;exitDx=0;exitDy=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;strokeWidth=1.5;strokeColor=#d6b656;dashed=0;shadow=0;" edge="1" parent="18" source="19" target="22">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="210" y="60" as="sourcePoint" />
            <mxPoint x="260" y="10" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="109" value="" style="endArrow=classic;html=1;rounded=1;exitX=0.5;exitY=1;exitDx=0;exitDy=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;strokeWidth=1.5;strokeColor=#d6b656;dashed=0;shadow=0;" edge="1" parent="18" source="20" target="23">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="210" y="60" as="sourcePoint" />
            <mxPoint x="260" y="10" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="110" value="" style="endArrow=classic;html=1;rounded=1;exitX=0.5;exitY=1;exitDx=0;exitDy=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;strokeWidth=1.5;strokeColor=#d6b656;dashed=0;shadow=0;" edge="1" parent="18" source="21" target="24">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="210" y="60" as="sourcePoint" />
            <mxPoint x="260" y="10" as="targetPoint" />
          </mxGeometry>
        </mxCell>

        <!-- 车端层 -->
        <mxCell id="25" value="车端层" style="swimlane;whiteSpace=wrap;html=1;fillColor=#e1d5e7;strokeColor=#9673a6;rounded=1;shadow=1;glass=0;swimlaneFillColor=#F5ECF9;fontFamily=Helvetica;fontSize=14;fontStyle=1;" vertex="1" parent="1">
          <mxGeometry x="120" y="520" width="600" height="160" as="geometry" />
        </mxCell>
        <mxCell id="26" value="钥匙验证模块" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f8cecc;strokeColor=#b85450;shadow=1;glass=0;sketch=0;gradientColor=#ea6b66;fontFamily=Helvetica;fontSize=12;" vertex="1" parent="25">
          <mxGeometry x="40" y="40" width="160" height="40" as="geometry" />
        </mxCell>
        <mxCell id="27" value="安全存储模块" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f8cecc;strokeColor=#b85450;shadow=1;glass=0;sketch=0;gradientColor=#ea6b66;fontFamily=Helvetica;fontSize=12;" vertex="1" parent="25">
          <mxGeometry x="220" y="40" width="160" height="40" as="geometry" />
        </mxCell>
        <mxCell id="28" value="蓝牙通信模块" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f8cecc;strokeColor=#b85450;shadow=1;glass=0;sketch=0;gradientColor=#ea6b66;fontFamily=Helvetica;fontSize=12;" vertex="1" parent="25">
          <mxGeometry x="400" y="40" width="160" height="40" as="geometry" />
        </mxCell>
        <mxCell id="29" value="暗号交换模块" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f8cecc;strokeColor=#b85450;shadow=1;glass=0;sketch=0;gradientColor=#ea6b66;fontFamily=Helvetica;fontSize=12;" vertex="1" parent="25">
          <mxGeometry x="40" y="100" width="160" height="40" as="geometry" />
        </mxCell>
        <mxCell id="30" value="用户行为分析" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f8cecc;strokeColor=#b85450;shadow=1;glass=0;sketch=0;gradientColor=#ea6b66;fontFamily=Helvetica;fontSize=12;" vertex="1" parent="25">
          <mxGeometry x="220" y="100" width="160" height="40" as="geometry" />
        </mxCell>
        <mxCell id="31" value="异常处理模块" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f8cecc;strokeColor=#b85450;shadow=1;glass=0;sketch=0;gradientColor=#ea6b66;fontFamily=Helvetica;fontSize=12;" vertex="1" parent="25">
          <mxGeometry x="400" y="100" width="160" height="40" as="geometry" />
        </mxCell>

        <!-- 车端内部连接线 -->
        <mxCell id="111" value="" style="endArrow=classic;html=1;rounded=1;exitX=1;exitY=0.5;exitDx=0;exitDy=0;entryX=0;entryY=0.5;entryDx=0;entryDy=0;strokeWidth=1.5;strokeColor=#9673a6;dashed=0;shadow=0;edgeStyle=orthogonalEdgeStyle;" edge="1" parent="25" source="26" target="27">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="210" y="60" as="sourcePoint" />
            <mxPoint x="260" y="10" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="112" value="" style="endArrow=classic;html=1;rounded=1;exitX=1;exitY=0.5;exitDx=0;exitDy=0;entryX=0;entryY=0.5;entryDx=0;entryDy=0;strokeWidth=1.5;strokeColor=#9673a6;dashed=0;shadow=0;edgeStyle=orthogonalEdgeStyle;" edge="1" parent="25" source="27" target="28">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="210" y="60" as="sourcePoint" />
            <mxPoint x="260" y="10" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="113" value="" style="endArrow=classic;html=1;rounded=1;exitX=0.5;exitY=1;exitDx=0;exitDy=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;strokeWidth=1.5;strokeColor=#9673a6;dashed=0;shadow=0;" edge="1" parent="25" source="26" target="29">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="210" y="60" as="sourcePoint" />
            <mxPoint x="260" y="10" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="114" value="" style="endArrow=classic;html=1;rounded=1;exitX=0.5;exitY=1;exitDx=0;exitDy=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;strokeWidth=1.5;strokeColor=#9673a6;dashed=0;shadow=0;" edge="1" parent="25" source="27" target="30">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="210" y="60" as="sourcePoint" />
            <mxPoint x="260" y="10" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="115" value="" style="endArrow=classic;html=1;rounded=1;exitX=0.5;exitY=1;exitDx=0;exitDy=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;strokeWidth=1.5;strokeColor=#9673a6;dashed=0;shadow=0;" edge="1" parent="25" source="28" target="31">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="210" y="60" as="sourcePoint" />
            <mxPoint x="260" y="10" as="targetPoint" />
          </mxGeometry>
        </mxCell>

        <!-- 安全层 -->
        <mxCell id="32" value="安全层" style="swimlane;whiteSpace=wrap;html=1;fillColor=#f8cecc;strokeColor=#b85450;rounded=1;shadow=1;glass=0;swimlaneFillColor=#FEF2F1;fontFamily=Helvetica;fontSize=14;fontStyle=1;" vertex="1" parent="1">
          <mxGeometry x="120" y="720" width="600" height="120" as="geometry" />
        </mxCell>
        <mxCell id="33" value="端到端加密" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#e1d5e7;strokeColor=#9673a6;shadow=1;glass=0;sketch=0;gradientColor=#d5739d;fontFamily=Helvetica;fontSize=12;" vertex="1" parent="32">
          <mxGeometry x="40" y="40" width="160" height="40" as="geometry" />
        </mxCell>
        <mxCell id="34" value="安全芯片" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#e1d5e7;strokeColor=#9673a6;shadow=1;glass=0;sketch=0;gradientColor=#d5739d;fontFamily=Helvetica;fontSize=12;" vertex="1" parent="32">
          <mxGeometry x="220" y="40" width="160" height="40" as="geometry" />
        </mxCell>
        <mxCell id="35" value="生物识别" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#e1d5e7;strokeColor=#9673a6;shadow=1;glass=0;sketch=0;gradientColor=#d5739d;fontFamily=Helvetica;fontSize=12;" vertex="1" parent="32">
          <mxGeometry x="400" y="40" width="160" height="40" as="geometry" />
        </mxCell>

        <!-- 安全层内部连接 -->
        <mxCell id="116" value="" style="endArrow=classic;startArrow=classic;html=1;rounded=1;exitX=1;exitY=0.5;exitDx=0;exitDy=0;entryX=0;entryY=0.5;entryDx=0;entryDy=0;strokeWidth=1.5;strokeColor=#b85450;dashed=0;shadow=0;edgeStyle=orthogonalEdgeStyle;" edge="1" parent="32" source="33" target="34">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="200" y="80" as="sourcePoint" />
            <mxPoint x="250" y="30" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="117" value="" style="endArrow=classic;startArrow=classic;html=1;rounded=1;exitX=1;exitY=0.5;exitDx=0;exitDy=0;entryX=0;entryY=0.5;entryDx=0;entryDy=0;strokeWidth=1.5;strokeColor=#b85450;dashed=0;shadow=0;edgeStyle=orthogonalEdgeStyle;" edge="1" parent="32" source="34" target="35">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="200" y="80" as="sourcePoint" />
            <mxPoint x="250" y="30" as="targetPoint" />
          </mxGeometry>
        </mxCell>

        <!-- 连接线 -->
        <mxCell id="36" value="" style="endArrow=classic;startArrow=classic;html=1;rounded=1;exitX=0.5;exitY=0;exitDx=0;exitDy=0;entryX=0.5;entryY=1;entryDx=0;entryDy=0;strokeWidth=2;strokeColor=#007FFF;dashed=0;shadow=1;edgeStyle=orthogonalEdgeStyle;" edge="1" parent="1" source="18" target="11">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="390" y="590" as="sourcePoint" />
            <mxPoint x="440" y="540" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="118" value="&lt;div style=&quot;text-align: left; font-size: 11px;&quot;&gt;&lt;span style=&quot;background-color: white;&quot;&gt;&lt;font style=&quot;font-size: 11px;&quot;&gt;1. 钥匙分发&lt;/font&gt;&lt;/span&gt;&lt;/div&gt;&lt;div style=&quot;text-align: left; font-size: 11px;&quot;&gt;&lt;span style=&quot;background-color: white;&quot;&gt;&lt;font style=&quot;font-size: 11px;&quot;&gt;2. 权限管理&lt;/font&gt;&lt;/span&gt;&lt;/div&gt;&lt;div style=&quot;text-align: left; font-size: 11px;&quot;&gt;&lt;span style=&quot;background-color: white;&quot;&gt;&lt;font style=&quot;font-size: 11px;&quot;&gt;3. 状态同步&lt;/font&gt;&lt;/span&gt;&lt;/div&gt;&lt;div style=&quot;text-align: left; font-size: 11px;&quot;&gt;&lt;span style=&quot;background-color: white;&quot;&gt;&lt;font style=&quot;font-size: 11px;&quot;&gt;4. 异常处理&lt;/font&gt;&lt;/span&gt;&lt;/div&gt;" style="rounded=1;arcSize=10;whiteSpace=wrap;html=1;align=center;fillColor=#FFFFFF;strokeColor=#007FFF;fontColor=#333333;fontSize=11;shadow=1;glass=0;strokeWidth=1;" vertex="1" parent="1">
          <mxGeometry x="440" y="260" width="80" height="60" as="geometry" />
        </mxCell>
        <mxCell id="37" value="" style="endArrow=classic;startArrow=classic;html=1;rounded=1;exitX=0.5;exitY=0;exitDx=0;exitDy=0;entryX=0.5;entryY=1;entryDx=0;entryDy=0;strokeWidth=2;strokeColor=#FF8000;dashed=0;shadow=1;edgeStyle=orthogonalEdgeStyle;" edge="1" parent="1" source="25" target="18">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="390" y="590" as="sourcePoint" />
            <mxPoint x="440" y="540" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="119" value="&lt;div style=&quot;text-align: left; font-size: 11px;&quot;&gt;&lt;span style=&quot;background-color: white;&quot;&gt;&lt;font style=&quot;font-size: 11px;&quot;&gt;1. 钥匙验证&lt;/font&gt;&lt;/span&gt;&lt;/div&gt;&lt;div style=&quot;text-align: left; font-size: 11px;&quot;&gt;&lt;span style=&quot;background-color: white;&quot;&gt;&lt;font style=&quot;font-size: 11px;&quot;&gt;2. 蓝牙配对&lt;/font&gt;&lt;/span&gt;&lt;/div&gt;&lt;div style=&quot;text-align: left; font-size: 11px;&quot;&gt;&lt;span style=&quot;background-color: white;&quot;&gt;&lt;font style=&quot;font-size: 11px;&quot;&gt;3. 暗号交换&lt;/font&gt;&lt;/span&gt;&lt;/div&gt;&lt;div style=&quot;text-align: left; font-size: 11px;&quot;&gt;&lt;span style=&quot;background-color: white;&quot;&gt;&lt;font style=&quot;font-size: 11px;&quot;&gt;4. 开锁指令&lt;/font&gt;&lt;/span&gt;&lt;/div&gt;" style="rounded=1;arcSize=10;whiteSpace=wrap;html=1;align=center;fillColor=#FFFFFF;strokeColor=#FF8000;fontColor=#333333;fontSize=11;shadow=1;glass=0;strokeWidth=1;" vertex="1" parent="1">
          <mxGeometry x="440" y="460" width="80" height="60" as="geometry" />
        </mxCell>
        <mxCell id="38" value="" style="endArrow=classic;startArrow=classic;html=1;rounded=1;exitX=0.5;exitY=0;exitDx=0;exitDy=0;entryX=0.5;entryY=1;entryDx=0;entryDy=0;strokeWidth=2;strokeColor=#CC0066;dashed=0;shadow=1;edgeStyle=orthogonalEdgeStyle;" edge="1" parent="1" source="32" target="25">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="390" y="590" as="sourcePoint" />
            <mxPoint x="440" y="540" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="120" value="&lt;div style=&quot;text-align: left; font-size: 11px;&quot;&gt;&lt;span style=&quot;background-color: white;&quot;&gt;&lt;font style=&quot;font-size: 11px;&quot;&gt;1. 安全认证&lt;/font&gt;&lt;/span&gt;&lt;/div&gt;&lt;div style=&quot;text-align: left; font-size: 11px;&quot;&gt;&lt;span style=&quot;background-color: white;&quot;&gt;&lt;font style=&quot;font-size: 11px;&quot;&gt;2. 加密通信&lt;/font&gt;&lt;/span&gt;&lt;/div&gt;&lt;div style=&quot;text-align: left; font-size: 11px;&quot;&gt;&lt;span style=&quot;background-color: white;&quot;&gt;&lt;font style=&quot;font-size: 11px;&quot;&gt;3. 防重放攻击&lt;/font&gt;&lt;/span&gt;&lt;/div&gt;&lt;div style=&quot;text-align: left; font-size: 11px;&quot;&gt;&lt;span style=&quot;background-color: white;&quot;&gt;&lt;font style=&quot;font-size: 11px;&quot;&gt;4. 完整性校验&lt;/font&gt;&lt;/span&gt;&lt;/div&gt;" style="rounded=1;arcSize=10;whiteSpace=wrap;html=1;align=center;fillColor=#FFFFFF;strokeColor=#CC0066;fontColor=#333333;fontSize=11;shadow=1;glass=0;strokeWidth=1;" vertex="1" parent="1">
          <mxGeometry x="440" y="660" width="80" height="60" as="geometry" />
        </mxCell>

        <!-- 添加业务场景说明 -->
        <mxCell id="39" value="业务场景" style="swimlane;whiteSpace=wrap;html=1;fillColor=#f5f5f5;strokeColor=#666666;rounded=1;shadow=1;glass=0;fontColor=#333333;fontFamily=Helvetica;fontSize=14;fontStyle=1;" vertex="1" parent="1">
          <mxGeometry x="120" y="880" width="600" height="100" as="geometry" />
        </mxCell>
        <mxCell id="121" value="&lt;div style=&quot;text-align: center; font-size: 12px;&quot;&gt;&lt;b&gt;钥匙分发与激活&lt;/b&gt;&lt;/div&gt;" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#dae8fc;strokeColor=#6c8ebf;shadow=1;glass=0;sketch=0;gradientColor=#7ea6e0;fontFamily=Helvetica;fontSize=12;align=center;" vertex="1" parent="39">
          <mxGeometry x="40" y="40" width="120" height="40" as="geometry" />
        </mxCell>
        <mxCell id="122" value="&lt;div style=&quot;text-align: center; font-size: 12px;&quot;&gt;&lt;b&gt;钥匙分发与激活&lt;/b&gt;&lt;/div&gt;" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#dae8fc;strokeColor=#6c8ebf;shadow=1;glass=0;sketch=0;gradientColor=#7ea6e0;fontFamily=Helvetica;fontSize=12;align=center;" vertex="1" parent="39">
          <mxGeometry x="40" y="40" width="120" height="40" as="geometry" />
        </mxCell>
        <mxCell id="123" value="&lt;div style=&quot;text-align: center; font-size: 12px;&quot;&gt;&lt;b&gt;钥匙分发与激活&lt;/b&gt;&lt;/div&gt;" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#dae8fc;strokeColor=#6c8ebf;shadow=1;glass=0;sketch=0;gradientColor=#7ea6e0;fontFamily=Helvetica;fontSize=12;align=center;" vertex="1" parent="39">
          <mxGeometry x="40" y="40" width="120" height="40" as="geometry" />
        </mxCell>
        <mxCell id="124" value="&lt;div style=&quot;text-align: center; font-size: 12px;&quot;&gt;&lt;b&gt;钥匙分发与激活&lt;/b&gt;&lt;/div&gt;" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#dae8fc;strokeColor=#6c8ebf;shadow=1;glass=0;sketch=0;gradientColor=#7ea6e0;fontFamily=Helvetica;fontSize=12;align=center;" vertex="1" parent="39">
          <mxGeometry x="40" y="40" width="120" height="40" as="geometry" />
        </mxCell>

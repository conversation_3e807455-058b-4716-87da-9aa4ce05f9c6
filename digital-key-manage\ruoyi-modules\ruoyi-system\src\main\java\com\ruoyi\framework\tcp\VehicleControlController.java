package com.ruoyi.framework.tcp;

import com.ruoyi.common.core.web.controller.BaseController;
import com.ruoyi.common.core.web.domain.AjaxResult;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.Map;

/**
 * 4G控车API接口
 * 提供RESTful API接口用于车辆控制和状态查询
 * 
 * <AUTHOR>
 */
@RestController
@RequestMapping("/api/vehicle")
public class VehicleControlController extends BaseController {
    
    private static final Logger LOGGER = LoggerFactory.getLogger(VehicleControlController.class);
    
    @Autowired
    private TcpControlService tcpControlService;
    
    /**
     * 车辆解锁
     */
    @PostMapping("/unlock")
    public AjaxResult unlock(@RequestParam(value = "tboxId", required = false) String tboxId) {
        try {
            LOGGER.info("🔓 收到车辆解锁请求, TBOX ID: {}", tboxId);
            
            if (tboxId != null && !tboxId.trim().isEmpty()) {
                // 向指定TBOX发送解锁指令
                tcpControlService.sendCommandToTbox(tboxId, "unlock");
            } else {
                // 向所有TBOX发送解锁指令
                tcpControlService.sendUnlockCommandToAllTbox();
            }
            
            Map<String, Object> data = new HashMap<>();
            data.put("action", "unlock");
            data.put("tboxId", tboxId);
            data.put("timestamp", System.currentTimeMillis());
            data.put("connectedTboxCount", tcpControlService.getConnectedTboxCount());
            
            return AjaxResult.success("解锁指令发送成功", data);
            
        } catch (Exception e) {
            LOGGER.error("❌ 车辆解锁失败: {}", e.getMessage(), e);
            return AjaxResult.error("车辆解锁失败: " + e.getMessage());
        }
    }
    
    /**
     * 车辆闭锁
     */
    @PostMapping("/lock")
    public AjaxResult lock(@RequestParam(value = "tboxId", required = false) String tboxId) {
        try {
            LOGGER.info("🔒 收到车辆闭锁请求, TBOX ID: {}", tboxId);
            
            if (tboxId != null && !tboxId.trim().isEmpty()) {
                // 向指定TBOX发送闭锁指令
                tcpControlService.sendCommandToTbox(tboxId, "lock");
            } else {
                // 向所有TBOX发送闭锁指令
                tcpControlService.sendLockCommandToAllTbox();
            }
            
            Map<String, Object> data = new HashMap<>();
            data.put("action", "lock");
            data.put("tboxId", tboxId);
            data.put("timestamp", System.currentTimeMillis());
            data.put("connectedTboxCount", tcpControlService.getConnectedTboxCount());
            
            return AjaxResult.success("闭锁指令发送成功", data);
            
        } catch (Exception e) {
            LOGGER.error("❌ 车辆闭锁失败: {}", e.getMessage(), e);
            return AjaxResult.error("车辆闭锁失败: " + e.getMessage());
        }
    }
    
    /**
     * 查询车辆状态
     */
    @GetMapping("/status")
    public AjaxResult getStatus() {
        try {
            LOGGER.info("📊 查询车辆状态");
            
            Map<String, Object> data = new HashMap<>();
            data.put("connectedTboxCount", tcpControlService.getConnectedTboxCount());
            data.put("connectedTboxList", TcpClientHandler.getTboxClients().keySet());
            data.put("timestamp", System.currentTimeMillis());
            data.put("serverStatus", "running");
            
            return AjaxResult.success("查询成功", data);
            
        } catch (Exception e) {
            LOGGER.error("❌ 查询车辆状态失败: {}", e.getMessage(), e);
            return AjaxResult.error("查询车辆状态失败: " + e.getMessage());
        }
    }
    
    /**
     * 通用控车接口
     */
    @PostMapping("/control")
    public AjaxResult control(@RequestBody Map<String, Object> request) {
        try {
            String action = (String) request.get("action");
            String tboxId = (String) request.get("tboxId");
            
            LOGGER.info("🚗 收到控车请求: action={}, tboxId={}", action, tboxId);
            
            if (action == null || action.trim().isEmpty()) {
                return AjaxResult.error("控车动作不能为空");
            }
            
            if (tboxId != null && !tboxId.trim().isEmpty()) {
                // 向指定TBOX发送指令
                tcpControlService.sendCommandToTbox(tboxId, action);
            } else {
                // 向所有TBOX发送指令
                if ("unlock".equalsIgnoreCase(action)) {
                    tcpControlService.sendUnlockCommandToAllTbox();
                } else if ("lock".equalsIgnoreCase(action)) {
                    tcpControlService.sendLockCommandToAllTbox();
                } else {
                    return AjaxResult.error("不支持的控车动作: " + action);
                }
            }
            
            Map<String, Object> data = new HashMap<>();
            data.put("action", action);
            data.put("tboxId", tboxId);
            data.put("timestamp", System.currentTimeMillis());
            data.put("connectedTboxCount", tcpControlService.getConnectedTboxCount());
            
            return AjaxResult.success("控车指令发送成功", data);
            
        } catch (Exception e) {
            LOGGER.error("❌ 控车操作失败: {}", e.getMessage(), e);
            return AjaxResult.error("控车操作失败: " + e.getMessage());
        }
    }
    
    /**
     * 获取连接的TBOX列表
     */
    @GetMapping("/tbox/list")
    public AjaxResult getTboxList() {
        try {
            LOGGER.info("📋 查询TBOX连接列表");
            
            Map<String, Object> data = new HashMap<>();
            data.put("tboxList", TcpClientHandler.getTboxClients().keySet());
            data.put("count", tcpControlService.getConnectedTboxCount());
            data.put("timestamp", System.currentTimeMillis());
            
            return AjaxResult.success("查询成功", data);
            
        } catch (Exception e) {
            LOGGER.error("❌ 查询TBOX列表失败: {}", e.getMessage(), e);
            return AjaxResult.error("查询TBOX列表失败: " + e.getMessage());
        }
    }
    
    /**
     * 健康检查接口
     */
    @GetMapping("/health")
    public AjaxResult health() {
        try {
            Map<String, Object> data = new HashMap<>();
            data.put("status", "healthy");
            data.put("tcpServerRunning", true);
            data.put("connectedTboxCount", tcpControlService.getConnectedTboxCount());
            data.put("timestamp", System.currentTimeMillis());
            
            return AjaxResult.success("服务健康", data);
            
        } catch (Exception e) {
            LOGGER.error("❌ 健康检查失败: {}", e.getMessage(), e);
            return AjaxResult.error("服务异常: " + e.getMessage());
        }
    }
}

<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.dk.mapper.DkKeyAssignmentMapper">
    
    <resultMap type="DkKeyAssignment" id="DkKeyAssignmentResult">
        <result property="assignmentId"    column="assignment_id"    />
        <result property="userId"    column="user_id"    />
        <result property="userName"    column="user_name"    />
        <result property="vehicleId"    column="vehicle_id"    />
        <result property="vinCode"    column="vin_code"    />
        <result property="vehicleBrand"    column="vehicle_brand"    />
        <result property="vehicleModel"    column="vehicle_model"    />
        <result property="status"    column="status"    />
        <result property="assignmentTime"    column="assignment_time"    />
        <result property="revokeTime"    column="revoke_time"    />
        <result property="operatorId"    column="operator_id"    />
        <result property="operatorName"    column="operator_name"    />
        <result property="remark"    column="remark"    />
        <result property="validStartTime"    column="valid_start_time"    />
        <result property="validEndTime"    column="valid_end_time"    />
        <result property="permissionType"    column="permission_type"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>

    <sql id="selectDkKeyAssignmentVo">
        select assignment_id, user_id, user_name, vehicle_id, vin_code, vehicle_brand, vehicle_model, status, assignment_time, revoke_time, operator_id, operator_name, remark, valid_start_time, valid_end_time, permission_type, create_by, create_time, update_by, update_time from dk_key_assignment
    </sql>

    <select id="selectDkKeyAssignmentList" parameterType="DkKeyAssignment" resultMap="DkKeyAssignmentResult">
        <include refid="selectDkKeyAssignmentVo"/>
        <where>
            <if test="userId != null "> and user_id = #{userId}</if>
            <if test="userName != null  and userName != ''"> and user_name like concat('%', #{userName}, '%')</if>
            <if test="vehicleId != null "> and vehicle_id = #{vehicleId}</if>
            <if test="vehicleBrand != null  and vehicleBrand != ''"> and vehicle_brand like concat('%', #{vehicleBrand}, '%')</if>
            <if test="vehicleModel != null  and vehicleModel != ''"> and vehicle_model like concat('%', #{vehicleModel}, '%')</if>
            <if test="status != null  and status != ''"> and status = #{status}</if>
            <if test="assignmentTime != null "> and assignment_time = #{assignmentTime}</if>
            <if test="validEndTime != null "> and valid_end_time = #{validEndTime}</if>
        </where>
        order by create_time desc
    </select>
    
    <select id="selectDkKeyAssignmentByAssignmentId" parameterType="Long" resultMap="DkKeyAssignmentResult">
        <include refid="selectDkKeyAssignmentVo"/>
        where assignment_id = #{assignmentId}
    </select>
    
    <select id="selectDkKeyAssignmentByUserId" parameterType="Long" resultMap="DkKeyAssignmentResult">
        <include refid="selectDkKeyAssignmentVo"/>
        where user_id = #{userId}
        order by create_time desc
    </select>
    
    <select id="selectDkKeyAssignmentByVehicleId" parameterType="Long" resultMap="DkKeyAssignmentResult">
        <include refid="selectDkKeyAssignmentVo"/>
        where vehicle_id = #{vehicleId}
        order by create_time desc
    </select>
    
    <select id="selectPendingAssignments" resultType="java.util.Map">
        select
            ka.assignment_id,
            ka.user_name,
            ka.vehicle_brand,
            ka.vehicle_model,
            ka.valid_end_time,
            ka.create_time,
            ka.remark
        from dk_key_assignment ka
        where ka.status = '0'
        order by ka.create_time desc
    </select>
    
    <select id="countAssignmentsByStatus" parameterType="String" resultType="int">
        select count(*) from dk_key_assignment where status = #{status}
    </select>

    <insert id="insertDkKeyAssignment" parameterType="DkKeyAssignment" useGeneratedKeys="true" keyProperty="assignmentId">
        insert into dk_key_assignment
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="userId != null">user_id,</if>
            <if test="userName != null">user_name,</if>
            <if test="vehicleId != null">vehicle_id,</if>
            <if test="vinCode != null">vin_code,</if>
            <if test="vehicleBrand != null">vehicle_brand,</if>
            <if test="vehicleModel != null">vehicle_model,</if>
            <if test="status != null">status,</if>
            <if test="assignmentTime != null">assignment_time,</if>
            <if test="revokeTime != null">revoke_time,</if>
            <if test="operatorId != null">operator_id,</if>
            <if test="operatorName != null">operator_name,</if>
            <if test="validStartTime != null">valid_start_time,</if>
            <if test="validEndTime != null">valid_end_time,</if>
            <if test="permissionType != null">permission_type,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="remark != null">remark,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="userId != null">#{userId},</if>
            <if test="userName != null">#{userName},</if>
            <if test="vehicleId != null">#{vehicleId},</if>
            <if test="vinCode != null">#{vinCode},</if>
            <if test="vehicleBrand != null">#{vehicleBrand},</if>
            <if test="vehicleModel != null">#{vehicleModel},</if>
            <if test="status != null">#{status},</if>
            <if test="assignmentTime != null">#{assignmentTime},</if>
            <if test="revokeTime != null">#{revokeTime},</if>
            <if test="operatorId != null">#{operatorId},</if>
            <if test="operatorName != null">#{operatorName},</if>
            <if test="validStartTime != null">#{validStartTime},</if>
            <if test="validEndTime != null">#{validEndTime},</if>
            <if test="permissionType != null">#{permissionType},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="remark != null">#{remark},</if>
         </trim>
    </insert>

    <update id="updateDkKeyAssignment" parameterType="DkKeyAssignment">
        update dk_key_assignment
        <trim prefix="SET" suffixOverrides=",">
            <if test="userId != null">user_id = #{userId},</if>
            <if test="userName != null">user_name = #{userName},</if>
            <if test="vehicleId != null">vehicle_id = #{vehicleId},</if>
            <if test="vinCode != null">vin_code = #{vinCode},</if>
            <if test="vehicleBrand != null">vehicle_brand = #{vehicleBrand},</if>
            <if test="vehicleModel != null">vehicle_model = #{vehicleModel},</if>
            <if test="status != null">status = #{status},</if>
            <if test="assignmentTime != null">assignment_time = #{assignmentTime},</if>
            <if test="revokeTime != null">revoke_time = #{revokeTime},</if>
            <if test="operatorId != null">operator_id = #{operatorId},</if>
            <if test="operatorName != null">operator_name = #{operatorName},</if>
            <if test="validStartTime != null">valid_start_time = #{validStartTime},</if>
            <if test="validEndTime != null">valid_end_time = #{validEndTime},</if>
            <if test="permissionType != null">permission_type = #{permissionType},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="remark != null">remark = #{remark},</if>
        </trim>
        where assignment_id = #{assignmentId}
    </update>
    
    <update id="updateAssignmentStatus">
        update dk_key_assignment 
        set status = #{status}, remark = #{remark}, update_time = now()
        where assignment_id = #{assignmentId}
    </update>

    <delete id="deleteDkKeyAssignmentByAssignmentId" parameterType="Long">
        delete from dk_key_assignment where assignment_id = #{assignmentId}
    </delete>

    <delete id="deleteDkKeyAssignmentByAssignmentIds" parameterType="String">
        delete from dk_key_assignment where assignment_id in 
        <foreach item="assignmentId" collection="array" open="(" separator="," close=")">
            #{assignmentId}
        </foreach>
    </delete>
</mapper>

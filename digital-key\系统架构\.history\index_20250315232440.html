<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>数字钥匙系统技术方案 - 目录</title>
    <style>
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            line-height: 1.6;
            color: #333;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f9f9f9;
        }
        h1 {
            text-align: center;
            padding: 20px 0;
            background-color: #0066cc;
            color: white;
            border-radius: 8px;
            margin-top: 0;
        }
        .toc {
            background-color: white;
            border-radius: 10px;
            padding: 20px;
            box-shadow: 0 4px 12px rgba(0,0,0,0.1);
            margin: 30px 0;
        }
        .toc-title {
            font-size: 24px;
            color: #0066cc;
            margin-bottom: 20px;
            border-bottom: 2px solid #e0e0e0;
            padding-bottom: 10px;
        }
        .toc-list {
            list-style-type: none;
            padding: 0;
        }
        .toc-item {
            margin: 15px 0;
            padding: 15px;
            background-color: #f5f9ff;
            border-radius: 8px;
            transition: transform 0.3s, box-shadow 0.3s;
        }
        .toc-item:hover {
            transform: translateY(-3px);
            box-shadow: 0 6px 12px rgba(0,0,0,0.1);
            background-color: #e6f0ff;
        }
        .toc-link {
            display: flex;
            align-items: center;
            text-decoration: none;
            color: #0066cc;
            font-weight: bold;
        }
        .toc-icon {
            font-size: 24px;
            margin-right: 15px;
        }
        .toc-desc {
            margin-top: 10px;
            color: #666;
            font-size: 14px;
        }
        .footer {
            text-align: center;
            margin-top: 30px;
            color: #666;
            font-size: 14px;
        }
    </style>
</head>
<body>
    <h1>数字钥匙系统技术方案</h1>
    
    <div class="toc">
        <div class="toc-title">目录</div>
        <ul class="toc-list">
            <li class="toc-item">
                <a href="00-头部样式.html" class="toc-link">
                    <span class="toc-icon">🎨</span>
                    <span>头部样式</span>
                </a>
                <div class="toc-desc">包含HTML头部和CSS样式定义</div>
            </li>
            <li class="toc-item">
                <a href="01-系统整体架构.html" class="toc-link">
                    <span class="toc-icon">🏗️</span>
                    <span>系统整体架构</span>
                </a>
                <div class="toc-desc">展示系统的整体架构设计和分层结构</div>
            </li>
            <li class="toc-item">
                <a href="02-数字钥匙系统整体架构.html" class="toc-link">
                    <span class="toc-icon">🔑</span>
                    <span>数字钥匙系统整体架构</span>
                </a>
                <div class="toc-desc">详细介绍数字钥匙系统的架构和组件关系</div>
            </li>
            <li class="toc-item">
                <a href="03-核心组件功能概述.html" class="toc-link">
                    <span class="toc-icon">⚙️</span>
                    <span>核心组件功能概述</span>
                </a>
                <div class="toc-desc">介绍系统核心组件的主要功能和特点</div>
            </li>
            <li class="toc-item">
                <a href="04-系统架构概述.html" class="toc-link">
                    <span class="toc-icon">📊</span>
                    <span>系统架构概述</span>
                </a>
                <div class="toc-desc">详细说明系统各个模块的功能和连接关系</div>
            </li>
            <li class="toc-item">
                <a href="05-脚本功能.html" class="toc-link">
                    <span class="toc-icon">📜</span>
                    <span>脚本功能</span>
                </a>
                <div class="toc-desc">包含系统交互和动态效果的JavaScript脚本</div>
            </li>
        </ul>
    </div>
    
    <div class="footer">
        <p>数字钥匙系统技术方案 &copy; 2025</p>
    </div>
</body>
</html> 
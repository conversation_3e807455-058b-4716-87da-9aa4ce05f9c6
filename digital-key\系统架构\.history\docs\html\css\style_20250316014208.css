/* 全局样式 */
:root {
  --primary-color: #3d5afe;
  --primary-dark: #0031ca;
  --primary-light: #8187ff;
  --secondary-color: #00bcd4;
  --secondary-light: #62efff;
  --secondary-dark: #008ba3;
  --dark-text: #263238;
  --light-text: #ffffff;
  --gray-100: #f5f5f5;
  --gray-200: #eeeeee;
  --gray-300: #e0e0e0;
  --gray-400: #bdbdbd;
  --gray-500: #9e9e9e;
  --gray-600: #757575;
  --gray-700: #616161;
  --gray-800: #424242;
  --gray-900: #212121;
  --success: #4caf50;
  --warning: #ff9800;
  --danger: #f44336;
  --info: #2196f3;
  --transition-standard: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  --shadow-small: 0 2px 5px rgba(0, 0, 0, 0.1);
  --shadow-medium: 0 4px 10px rgba(0, 0, 0, 0.12);
  --shadow-large: 0 8px 30px rgba(0, 0, 0, 0.15);
  --border-radius-sm: 4px;
  --border-radius-md: 8px;
  --border-radius-lg: 16px;
  --content-max-width: 1400px;
  --content-padding: 40px;
  --section-spacing: 100px;
  --element-spacing: 30px;
}

/* 适应屏幕大小的变量 */
@media (max-width: 1024px) {
  :root {
    --content-padding: 30px;
    --section-spacing: 80px;
    --element-spacing: 25px;
  }
}

@media (max-width: 768px) {
  :root {
    --content-padding: 20px;
    --section-spacing: 60px;
    --element-spacing: 20px;
  }
}

/* 基础设置 */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

html {
  scroll-behavior: smooth;
  scroll-padding-top: 80px;
}

body {
  font-family: 'Microsoft YaHei', 'Segoe UI', -apple-system, BlinkMacSystemFont, 'Helvetica Neue', Arial, sans-serif;
  color: var(--dark-text);
  background-color: var(--gray-100);
  line-height: 1.6;
  letter-spacing: 0.01em;
  overflow-x: hidden;
  font-size: 16px;
}

h1, h2, h3, h4, h5, h6 {
  font-weight: 700;
  line-height: 1.3;
  margin-bottom: 0.5em;
  color: var(--dark-text);
}

p {
  margin-bottom: 1rem;
}

a {
  text-decoration: none;
  color: var(--primary-color);
  transition: var(--transition-standard);
}

a:hover {
  color: var(--primary-dark);
}

ul, ol {
  padding-left: 1.5rem;
  margin-bottom: 1rem;
}

img {
  max-width: 100%;
  height: auto;
  display: block;
}

/* 容器 */
.container {
  max-width: var(--content-max-width);
  margin: 0 auto;
  padding: 0 var(--content-padding);
}

/* 顶部导航栏 */
.top-nav {
  position: fixed;
  top: 0;
  width: 100%;
  background-color: var(--gray-900);
  color: var(--light-text);
  box-shadow: var(--shadow-medium);
  z-index: 1000;
  height: 70px;
  display: flex;
  align-items: center;
}

.top-nav-inner {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
  max-width: var(--content-max-width);
  margin: 0 auto;
  padding: 0 var(--content-padding);
}

.logo-title {
  display: flex;
  align-items: center;
}

.logo-img {
  height: 40px;
  margin-right: 15px;
}

.logo-title h1 {
  font-size: 1.5rem;
  color: var(--light-text);
  margin: 0;
  white-space: nowrap;
}

.nav-links {
  display: flex;
  gap: 20px;
}

.nav-links a {
  color: var(--light-text);
  font-weight: 500;
  padding: 8px 15px;
  border-radius: var(--border-radius-sm);
  position: relative;
  display: flex;
  align-items: center;
  gap: 8px;
}

.nav-links a:hover {
  background-color: rgba(255, 255, 255, 0.1);
  color: var(--light-text);
}

.nav-links a i {
  font-size: 1.2em;
}

.nav-links a::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 15px;
  right: 15px;
  height: 3px;
  background-color: var(--primary-light);
  transform: scaleX(0);
  transform-origin: center;
  transition: var(--transition-standard);
}

.nav-links a:hover::after,
.nav-links a.active::after {
  transform: scaleX(1);
}

/* 移动菜单按钮 */
.mobile-menu-btn {
  display: none;
  background: none;
  border: none;
  color: var(--light-text);
  font-size: 1.5rem;
  cursor: pointer;
}

/* 主内容区域 */
.main-content {
  padding-top: 90px; /* 导航条高度加间距 */
  min-height: 100vh;
}

/* 主要区块样式 */
.section {
  margin-bottom: var(--section-spacing);
  background-color: white;
  border-radius: var(--border-radius-lg);
  box-shadow: var(--shadow-medium);
  overflow: hidden;
  position: relative;
}

.section-header {
  background-color: var(--primary-color);
  color: var(--light-text);
  padding: 25px 35px;
  position: relative;
}

.section-header::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(to right, var(--primary-light), var(--secondary-light));
}

.section-header h2 {
  margin: 0;
  font-size: 1.8rem;
  color: var(--light-text);
  display: flex;
  align-items: center;
  gap: 15px;
}

.section-header h2 i {
  font-size: 1.3em;
  background-color: rgba(255, 255, 255, 0.2);
  width: 45px;
  height: 45px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
}

.section-content {
  padding: 35px;
}

/* 架构图样式 */
.architecture-diagram {
  display: flex;
  justify-content: center;
  gap: 40px;
  flex-wrap: wrap;
  margin: 40px 0;
}

.component {
  background-color: var(--gray-100);
  border-radius: var(--border-radius-md);
  padding: 30px;
  width: 300px;
  text-align: center;
  position: relative;
  transition: var(--transition-standard);
  box-shadow: var(--shadow-small);
  border: 1px solid var(--gray-300);
}

.component:hover {
  transform: translateY(-5px);
  box-shadow: var(--shadow-medium);
}

.component i {
  font-size: 3rem;
  margin-bottom: 15px;
  color: var(--primary-color);
  transition: var(--transition-standard);
}

.component:hover i {
  transform: scale(1.1);
}

.component h3 {
  font-size: 1.4rem;
  margin-bottom: 15px;
  position: relative;
  display: inline-block;
}

.component h3::after {
  content: '';
  display: block;
  width: 50px;
  height: 3px;
  background-color: var(--secondary-color);
  margin: 10px auto 0;
  transition: var(--transition-standard);
}

.component:hover h3::after {
  width: 100%;
}

.component p {
  color: var(--gray-700);
  margin-bottom: 0;
}

/* 特定组件颜色 */
.mobile i {
  color: var(--primary-color);
}

.car i {
  color: var(--secondary-dark);
}

.cloud i {
  color: var(--info);
}

/* 功能列表样式 */
.feature-list {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 25px;
  margin: 30px 0;
}

.feature-item {
  background-color: var(--gray-100);
  border-radius: var(--border-radius-md);
  padding: 25px;
  box-shadow: var(--shadow-small);
  transition: var(--transition-standard);
  border-left: 4px solid var(--primary-color);
}

.feature-item:hover {
  transform: translateY(-3px);
  box-shadow: var(--shadow-medium);
}

.feature-item h3 {
  font-size: 1.2rem;
  margin-bottom: 15px;
  color: var(--primary-dark);
  display: flex;
  align-items: center;
  gap: 10px;
}

.feature-item h3 i {
  color: var(--primary-color);
  font-size: 1.2em;
}

.feature-item:nth-child(3n+1) {
  border-left-color: var(--primary-color);
}

.feature-item:nth-child(3n+2) {
  border-left-color: var(--secondary-color);
}

.feature-item:nth-child(3n+3) {
  border-left-color: var(--info);
}

.feature-item ul, 
.feature-item ol {
  margin-bottom: 0;
}

.feature-item li {
  margin-bottom: 8px;
}

/* 自定义列表样式 */
ul.custom-list {
  list-style: none;
  padding-left: 0;
}

ul.custom-list li {
  position: relative;
  padding-left: 25px;
  margin-bottom: 10px;
}

ul.custom-list li:before {
  content: '\f00c';
  font-family: 'Font Awesome 5 Free';
  font-weight: 900;
  position: absolute;
  left: 0;
  color: var(--success);
}

ol.custom-list {
  list-style: none;
  counter-reset: custom-counter;
  padding-left: 0;
}

ol.custom-list li {
  position: relative;
  padding-left: 35px;
  margin-bottom: 15px;
  counter-increment: custom-counter;
}

ol.custom-list li:before {
  content: counter(custom-counter);
  position: absolute;
  left: 0;
  background-color: var(--primary-color);
  color: white;
  width: 25px;
  height: 25px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 0.8rem;
  font-weight: bold;
}

/* 流程图样式 */
.process-diagram {
  margin: 30px 0;
  background-color: var(--gray-100);
  padding: 30px;
  border-radius: var(--border-radius-md);
  box-shadow: var(--shadow-small);
  text-align: center;
}

.process-diagram img {
  max-width: 100%;
  transition: var(--transition-standard);
  border-radius: var(--border-radius-sm);
  box-shadow: var(--shadow-small);
}

.process-diagram img:hover {
  box-shadow: var(--shadow-medium);
}

.process-diagram figcaption {
  color: var(--gray-700);
  font-style: italic;
  margin-top: 15px;
  text-align: center;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
}

/* 标签样式 */
.tabs {
  margin: 30px 0;
}

.tab-nav {
  display: flex;
  overflow-x: auto;
  border-bottom: 2px solid var(--gray-300);
  margin-bottom: 30px;
  gap: 2px;
}

.tab-btn {
  padding: 12px 25px;
  background: none;
  border: none;
  color: var(--gray-700);
  font-weight: 500;
  font-size: 1rem;
  cursor: pointer;
  transition: var(--transition-standard);
  position: relative;
  white-space: nowrap;
  display: flex;
  align-items: center;
  gap: 8px;
}

.tab-btn i {
  font-size: 1.2em;
}

.tab-btn::after {
  content: '';
  position: absolute;
  bottom: -2px;
  left: 0;
  right: 0;
  height: 3px;
  background-color: var(--primary-color);
  transform: scaleX(0);
  transition: var(--transition-standard);
}

.tab-btn:hover {
  color: var(--primary-color);
}

.tab-btn.active {
  color: var(--primary-color);
}

.tab-btn.active::after {
  transform: scaleX(1);
}

.tab-content {
  display: none;
  animation: fadeIn 0.5s ease forwards;
}

.tab-content.active {
  display: block;
}

/* 卡片样式 */
.cards {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 30px;
  margin: 30px 0;
}

.card {
  background-color: white;
  border-radius: var(--border-radius-md);
  overflow: hidden;
  box-shadow: var(--shadow-small);
  transition: var(--transition-standard);
  border: 1px solid var(--gray-200);
  display: flex;
  flex-direction: column;
}

.card:hover {
  transform: translateY(-5px);
  box-shadow: var(--shadow-medium);
}

.card-header {
  background-color: var(--primary-color);
  color: var(--light-text);
  padding: 20px 25px;
}

.card-header h3 {
  margin: 0;
  font-size: 1.3rem;
  color: var(--light-text);
  display: flex;
  align-items: center;
  gap: 10px;
}

.card-header h3 i {
  font-size: 1.2em;
}

.card-body {
  padding: 25px;
  flex-grow: 1;
  display: flex;
  flex-direction: column;
}

.card-body p:first-child {
  margin-top: 0;
}

.card-body p:last-child {
  margin-bottom: 0;
}

/* 流程详情部分 */
.process-details {
  background-color: var(--gray-100);
  border-radius: var(--border-radius-md);
  padding: 30px;
  margin: 30px 0;
  box-shadow: var(--shadow-small);
  border-left: 4px solid var(--secondary-color);
}

.process-details h3 {
  font-size: 1.3rem;
  color: var(--secondary-dark);
  margin: 25px 0 15px;
  display: flex;
  align-items: center;
  gap: 10px;
}

.process-details h3:first-child {
  margin-top: 0;
}

.process-details h3 i {
  color: var(--secondary-color);
}

/* 页脚样式 */
.footer {
  background-color: var(--gray-900);
  color: var(--gray-400);
  padding: 60px 0 0;
  margin-top: 80px;
}

.footer-content {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 40px;
  padding-bottom: 40px;
}

.footer-section h3 {
  color: var(--light-text);
  font-size: 1.3rem;
  margin-bottom: 20px;
  position: relative;
  display: inline-block;
}

.footer-section h3::after {
  content: '';
  position: absolute;
  left: 0;
  bottom: -10px;
  width: 40px;
  height: 3px;
  background-color: var(--primary-light);
}

.footer-section p, 
.footer-section a {
  margin-bottom: 12px;
  color: var(--gray-400);
  display: flex;
  align-items: center;
  gap: 10px;
}

.footer-section a:hover {
  color: var(--light-text);
}

.footer-bottom {
  background-color: rgba(0, 0, 0, 0.2);
  text-align: center;
  padding: 20px 0;
  color: var(--gray-500);
  font-size: 0.9rem;
}

/* 按钮样式 */
.btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  background-color: var(--primary-color);
  color: white;
  padding: 10px 20px;
  border-radius: var(--border-radius-sm);
  font-weight: 500;
  cursor: pointer;
  transition: var(--transition-standard);
  border: none;
  text-align: center;
  margin-top: 15px;
  box-shadow: var(--shadow-small);
}

.btn:hover {
  background-color: var(--primary-dark);
  transform: translateY(-2px);
  box-shadow: var(--shadow-medium);
  color: white;
}

.btn-secondary {
  background-color: var(--secondary-color);
}

.btn-secondary:hover {
  background-color: var(--secondary-dark);
}

/* 动画效果 */
.fade-in {
  opacity: 0;
  transform: translateY(20px);
  transition: opacity 0.6s ease, transform 0.6s ease;
}

.fade-in.visible {
  opacity: 1;
  transform: translateY(0);
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* 响应式设计 */
@media (max-width: 1100px) {
  .feature-list,
  .cards {
    grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
  }
}

@media (max-width: 960px) {
  .logo-title h1 {
    font-size: 1.2rem;
  }
  
  .nav-links a {
    padding: 6px 12px;
    font-size: 0.9rem;
  }
}

@media (max-width: 768px) {
  .nav-links {
    display: none;
  }
  
  .mobile-menu-btn {
    display: block;
  }
  
  .top-nav {
    height: 60px;
  }
  
  .main-content {
    padding-top: 80px;
  }
  
  .section-header h2 {
    font-size: 1.5rem;
  }
  
  .section-header {
    padding: 20px 25px;
  }
  
  .section-content {
    padding: 25px;
  }
  
  .architecture-diagram {
    flex-direction: column;
    align-items: center;
    gap: 25px;
  }
  
  .component {
    width: 100%;
    max-width: 320px;
  }
  
  .feature-list,
  .cards {
    grid-template-columns: 1fr;
  }
  
  .tab-nav {
    flex-wrap: nowrap;
    overflow-x: auto;
    justify-content: flex-start;
  }
  
  .process-diagram {
    padding: 20px;
  }
}

/* 移动菜单 */
.mobile-menu {
  position: fixed;
  top: 60px;
  left: 0;
  right: 0;
  background-color: var(--gray-900);
  padding: 15px;
  z-index: 999;
  display: none;
  flex-direction: column;
  gap: 10px;
  box-shadow: var(--shadow-medium);
}

.mobile-menu a {
  color: var(--light-text);
  padding: 12px 15px;
  border-radius: var(--border-radius-sm);
  display: flex;
  align-items: center;
  gap: 12px;
}

.mobile-menu a:hover {
  background-color: rgba(255, 255, 255, 0.1);
}

.mobile-menu.active {
  display: flex;
} 